﻿<h5>COMMAND</h5>
<section id="command.copy" class="my-5">
	<h5>command.copy, command.copy_to_clipboard</h5>
	<p>copy to parameter clipboard.</p>
	<p>Syntax</p>
	<code>command.copy('string copy to clipboard')</code>
</section>

<section id="command.sleep" class="my-5">
	<h5>command.sleep(milliseconds)</h5>
	<p>Suspends the execution of the current thread until the time-out interval elapses.</p>
	<p>Syntax</p>
	<code>command.sleep(1000)</code>
</section>

<section id="command.random" class="my-5">
	<h5>command.random</h5>
	<p>Suspends the execution of the current thread until the time-out interval elapses.</p>
	<p>Syntax</p>
	<code>command.random(min value, max value)</code>
</section>

<section id="list" class="my-5">
<pre><code>command.cascade_windows
command.copy_to_folder
command.customize_this_folder
command.find
command.folder_options
command.invert_selection
command.minimize_all_windows
command.move_to_folder
command.redo
command.refresh
command.restart_explorer
command.restore_all_windows
command.run
command.search
command.select_all
command.select_none
command.show_windows_side_by_side
command.show_windows_stacked
command.switcher
command.toggle_desktop
command.toggleext
command.togglehidden
command.undo
</code></pre>
</section>