
// docs: `...`


/*
    :: system shortcuts

    :: usage: `cmd='@URI_SETTINGS_ABOUT'`
*/

// --- system
$URI_SETTINGS                     = 'ms-settings:'
$URI_SETTINGS_ABOUT               = 'ms-settings:about'
$URI_SETTINGS_CLIPBOARD           = 'ms-settings:clipboard'
$URI_SETTINGS_DISPLAY             = 'ms-settings:display'
$URI_SETTINGS_DISPLAY_ADVANCED    = 'ms-settings:display-advanced'
$URI_SETTINGS_DISPLAY_GRAPHICS    = 'ms-settings:display-advancedgraphics'
$URI_SETTINGS_DISPLAY_NIGHTLIGHT  = 'ms-settings:nightlight'
$URI_SETTINGS_DISPLAY_ORIENTATION = 'ms-settings:screenrotation'
$URI_SETTINGS_DISPLAY_WIRELESS    = 'ms-settings-connectabledevices:devicediscovery'
$URI_SETTINGS_ENCRYPTION          = 'ms-settings:deviceencryption'
$URI_SETTINGS_FOCUS               = 'ms-settings:quiethours'
$URI_SETTINGS_FOCUS_DISPLAY       = 'ms-settings:quietmomentspresentation'
$URI_SETTINGS_FOCUS_GAMING        = 'ms-settings:quietmomentsgame'
$URI_SETTINGS_FOCUS_HOURS         = 'ms-settings:quietmomentsscheduled'
$URI_SETTINGS_MULTITASKING        = 'ms-settings:multitasking'
$URI_SETTINGS_NOTIFICATIONS       = 'ms-settings:notifications'
$URI_SETTINGS_POWER_BATTERY       = 'ms-settings:batterysaver'
$URI_SETTINGS_POWER_BATTERY_SAVER = 'ms-settings:batterysaver-settings'
$URI_SETTINGS_POWER_BATTERY_USAGE = 'ms-settings:batterysaver-usagedetails'
$URI_SETTINGS_POWER_SLEEP         = 'ms-settings:powersleep'
$URI_SETTINGS_PROJECTING          = 'ms-settings:project'
$URI_SETTINGS_REMOTEDESKTOP       = 'ms-settings:remotedesktop'
$URI_SETTINGS_SHARING             = 'ms-settings:crossdevice'
$URI_SETTINGS_SOUND               = 'ms-settings:sound'
$URI_SETTINGS_SOUND_DEVICES       = 'ms-settings:sound-devices'
$URI_SETTINGS_SOUND_VOLUME        = 'ms-settings:apps-volume'
$URI_SETTINGS_STORAGE             = 'ms-settings:storagesense'
$URI_SETTINGS_STORAGE_LOCATIONS   = 'ms-settings:savelocations'
$URI_SETTINGS_STORAGE_SENSE       = 'ms-settings:storagepolicies'

// --- devices
$URI_SETTINGS_DEVICES_AUTOPLAY  = 'ms-settings:autoplay'
$URI_SETTINGS_DEVICES_BLUETOOTH = 'ms-settings:bluetooth'
$URI_SETTINGS_DEVICES_KEYBOARD  = 'ms-settings:devicestyping-hwkbtextsuggestions'
$URI_SETTINGS_DEVICES_MOUSE     = 'ms-settings:mousetouchpad'
$URI_SETTINGS_DEVICES_PEN       = 'ms-settings:pen'
$URI_SETTINGS_DEVICES_PRINTERS  = 'ms-settings:printers'
$URI_SETTINGS_DEVICES_TOUCHPAD  = 'ms-settings:devices-touchpad'
$URI_SETTINGS_DEVICES_TYPING    = 'ms-settings:typing'
$URI_SETTINGS_DEVICES_USB       = 'ms-settings:usb'
$URI_SETTINGS_DEVICES_WHEEL     = 'ms-settings:wheel'

// --- phone
$URI_SETTINGS_PHONE_ADD        = 'ms-settings:mobile-devices-addphone'
$URI_SETTINGS_PHONE_SETTINGS   = 'ms-settings:mobile-devices'
$URI_SETTINGS_PHONE_YOUR_PHONE = 'ms-settings:mobile-devices-addphone-direct'

// --- network & internet
$URI_SETTINGS_NETWORK              = 'ms-settings:network'
$URI_SETTINGS_NETWORK_AIRPLANE     = 'ms-settings:network-airplanemode'
$URI_SETTINGS_NETWORK_AVAILABLE    = 'ms-availablenetworks:'
$URI_SETTINGS_NETWORK_CELLULAR     = 'ms-settings:network-cellular'
$URI_SETTINGS_NETWORK_DATA_USAGE   = 'ms-settings:datausage'
$URI_SETTINGS_NETWORK_DIALUP       = 'ms-settings:network-dialup'
$URI_SETTINGS_NETWORK_ETHERNET     = 'ms-settings:network-ethernet'
$URI_SETTINGS_NETWORK_HOTSPOT      = 'ms-settings:network-mobilehotspot'
$URI_SETTINGS_NETWORK_NFC          = 'ms-settings:nfctransactions'
$URI_SETTINGS_NETWORK_PROXY        = 'ms-settings:network-proxy'
$URI_SETTINGS_NETWORK_STATUS       = 'ms-settings:network-status'
$URI_SETTINGS_NETWORK_VPN          = 'ms-settings:network-vpn'
$URI_SETTINGS_NETWORK_WIFI         = 'ms-settings:network-wifi'
$URI_SETTINGS_NETWORK_WIFI_CALLING = 'ms-settings:network-wificalling'
$URI_SETTINGS_NETWORK_WIFI_MANAGE  = 'ms-settings:network-wifisettings'

// --- personalization
$URI_SETTINGS_PERSONALIZATION               = 'ms-settings:personalization'
$URI_SETTINGS_PERSONALIZATION_BACKGROUND    = 'ms-settings:personalization-background'
$URI_SETTINGS_PERSONALIZATION_COLORS        = 'ms-settings:personalization-colors'
$URI_SETTINGS_PERSONALIZATION_FONTS         = 'ms-settings:fonts'
$URI_SETTINGS_PERSONALIZATION_LOCK_SCREEN   = 'ms-settings:lockscreen'
$URI_SETTINGS_PERSONALIZATION_START         = 'ms-settings:personalization-start'
$URI_SETTINGS_PERSONALIZATION_START_FOLDERS = 'ms-settings:personalization-start-places'
$URI_SETTINGS_PERSONALIZATION_TASKBAR       = 'ms-settings:taskbar'
$URI_SETTINGS_PERSONALIZATION_THEMES        = 'ms-settings:themes'

// --- apps
$URI_SETTINGS_APPS               = 'ms-settings:appsfeatures'
$URI_SETTINGS_APPS_DEFAULT       = 'ms-settings:defaultapps'
$URI_SETTINGS_APPS_MAPS_DOWNLOAD = 'ms-settings:maps-downloadmaps'
$URI_SETTINGS_APPS_OFFLINE_MAPS  = 'ms-settings:maps'
$URI_SETTINGS_APPS_OPTIONAL      = 'ms-settings:optionalfeatures'
$URI_SETTINGS_APPS_STARTUP       = 'ms-settings:startupapps'
$URI_SETTINGS_APPS_VIDEO         = 'ms-settings:videoplayback'
$URI_SETTINGS_APPS_WEBSITES      = 'ms-settings:appsforwebsites'

// --- accounts
$URI_SETTINGS_ACCOUNTS_EMAIL              = 'ms-settings:emailandaccounts'
$URI_SETTINGS_ACCOUNTS_FAMILY             = 'ms-settings:otherusers'
$URI_SETTINGS_ACCOUNTS_INFO               = 'ms-settings:yourinfo'
$URI_SETTINGS_ACCOUNTS_KIOSK              = 'ms-settings:assignedaccess'
$URI_SETTINGS_ACCOUNTS_SIGNIN             = 'ms-settings:signinoptions'
$URI_SETTINGS_ACCOUNTS_SIGNIN_DYNAMICLOCK = 'ms-settings:signinoptions-dynamiclock'
$URI_SETTINGS_ACCOUNTS_SIGNIN_FACE        = 'ms-settings:signinoptions-launchfaceenrollment'
$URI_SETTINGS_ACCOUNTS_SIGNIN_FINGERPRINT = 'ms-settings:signinoptions-launchfingerprintenrollment'
$URI_SETTINGS_ACCOUNTS_SIGNIN_SECURITYKEY = 'ms-settings:signinoptions-launchsecuritykeyenrollment'
$URI_SETTINGS_ACCOUNTS_SYNC               = 'ms-settings:sync'
$URI_SETTINGS_ACCOUNTS_WORKPLACE          = 'ms-settings:workplace'

// --- time & language
$URI_SETTINGS_TIME_DATE     = 'ms-settings:dateandtime'
$URI_SETTINGS_TIME_LANGUAGE = 'ms-settings:regionlanguage'
$URI_SETTINGS_TIME_REGION   = 'ms-settings:regionformatting'
$URI_SETTINGS_TIME_SPEECH   = 'ms-settings:speech'



// --- gaming
$URI_SETTINGS_GAMING_BAR          = 'ms-settings:gaming-gamebar'
$URI_SETTINGS_GAMING_BROADCASTING = 'ms-settings:gaming-broadcasting'
$URI_SETTINGS_GAMING_CAPTURES     = 'ms-settings:gaming-gamedvr'
$URI_SETTINGS_GAMING_MODE         = 'ms-settings:gaming-gamemode'
$URI_SETTINGS_GAMING_XBOX         = 'ms-settings:gaming-xboxnetworking'

// --- ease of access
$URI_SETTINGS_EASE_AUDIO        = 'ms-settings:easeofaccess-audio'
$URI_SETTINGS_EASE_CAPTIONS     = 'ms-settings:easeofaccess-closedcaptioning'
$URI_SETTINGS_EASE_COLORFILTERS = 'ms-settings:easeofaccess-colorfilter'
$URI_SETTINGS_EASE_CURSOR       = 'ms-settings:easeofaccess-cursor'
$URI_SETTINGS_EASE_DISPLAY      = 'ms-settings:easeofaccess-display'
$URI_SETTINGS_EASE_EYECONTROL   = 'ms-settings:easeofaccess-eyecontrol'
$URI_SETTINGS_EASE_HIGHCONTRAST = 'ms-settings:easeofaccess-highcontrast'
$URI_SETTINGS_EASE_KEYBOARD     = 'ms-settings:easeofaccess-keyboard'
$URI_SETTINGS_EASE_MAGNIFIER    = 'ms-settings:easeofaccess-magnifier'
$URI_SETTINGS_EASE_MOUSE        = 'ms-settings:easeofaccess-cursorandpointersize'
$URI_SETTINGS_EASE_MOUSE        = 'ms-settings:easeofaccess-mouse'
$URI_SETTINGS_EASE_NARRATOR     = 'ms-settings:easeofaccess-narrator'
$URI_SETTINGS_EASE_SPEECH       = 'ms-settings:easeofaccess-speechrecognition'

// --- search
$URI_SETTINGS_SEARCH_MORE        = 'ms-settings:search-moredetails'
$URI_SETTINGS_SEARCH_PERMISSIONS = 'ms-settings:search-permissions'
$URI_SETTINGS_SEARCH_WINDOWS     = 'ms-settings:cortana-windowssearch'

// --- cortana
$URI_SETTINGS_CORTANA             = 'ms-settings:cortana'
$URI_SETTINGS_CORTANA_MORE        = 'ms-settings:cortana-moredetails'
$URI_SETTINGS_CORTANA_PERMISSIONS = 'ms-settings:cortana-permissions'
$URI_SETTINGS_CORTANA_TALK        = 'ms-settings:cortana-talktocortana'

// --- privacy
$URI_SETTINGS_PRIVACY                 = 'ms-settings:privacy'
$URI_SETTINGS_PRIVACY_ACCOUNTINFO     = 'ms-settings:privacy-accountinfo'
$URI_SETTINGS_PRIVACY_ACTIVITY        = 'ms-settings:privacy-activityhistory'
$URI_SETTINGS_PRIVACY_APPDIAGNOSTICS  = 'ms-settings:privacy-appdiagnostics'
$URI_SETTINGS_PRIVACY_AUTODOWNLOADS   = 'ms-settings:privacy-automaticfiledownloads'
$URI_SETTINGS_PRIVACY_BACKGROUND_APPS = 'ms-settings:privacy-backgroundapps'
$URI_SETTINGS_PRIVACY_CALENDAR        = 'ms-settings:privacy-calendar'
$URI_SETTINGS_PRIVACY_CALL_HISTORY    = 'ms-settings:privacy-callhistory'
$URI_SETTINGS_PRIVACY_CAMERA          = 'ms-settings:privacy-webcam'
$URI_SETTINGS_PRIVACY_CONTACTS        = 'ms-settings:privacy-contacts'
$URI_SETTINGS_PRIVACY_DIAGNOSTICS     = 'ms-settings:privacy-feedback'
$URI_SETTINGS_PRIVACY_DOCUMENTS       = 'ms-settings:privacy-documents'
$URI_SETTINGS_PRIVACY_EMAIL           = 'ms-settings:privacy-email'
$URI_SETTINGS_PRIVACY_FILESYSTEM      = 'ms-settings:privacy-broadfilesystemaccess'
$URI_SETTINGS_PRIVACY_INKING          = 'ms-settings:privacy-speechtyping'
$URI_SETTINGS_PRIVACY_LOCATION        = 'ms-settings:privacy-location'
$URI_SETTINGS_PRIVACY_MESSAGING       = 'ms-settings:privacy-messaging'
$URI_SETTINGS_PRIVACY_MICROPHONE      = 'ms-settings:privacy-microphone'
$URI_SETTINGS_PRIVACY_NOTIFICATIONS   = 'ms-settings:privacy-notifications'
$URI_SETTINGS_PRIVACY_OTHER_DEVICES   = 'ms-settings:privacy-customdevices'
$URI_SETTINGS_PRIVACY_PICTURES        = 'ms-settings:privacy-pictures'
$URI_SETTINGS_PRIVACY_RADIOS          = 'ms-settings:privacy-radios'
$URI_SETTINGS_PRIVACY_SPEECH          = 'ms-settings:privacy-speech'
$URI_SETTINGS_PRIVACY_TASKS           = 'ms-settings:privacy-tasks'
$URI_SETTINGS_PRIVACY_VIDEOS          = 'ms-settings:privacy-documents'
$URI_SETTINGS_PRIVACY_VOICE           = 'ms-settings:privacy-voiceactivation'

// --- update & security
$URI_SETTINGS_UPDATE                 = 'ms-settings:windowsupdate'
$URI_SETTINGS_UPDATE_ACTIVATION      = 'ms-settings:activation'
$URI_SETTINGS_UPDATE_ADVANCEDOPTIONS = 'ms-settings:windowsupdate-options'
$URI_SETTINGS_UPDATE_BACKUP          = 'ms-settings:backup'
$URI_SETTINGS_UPDATE_DELIVERY        = 'ms-settings:delivery-optimization'
$URI_SETTINGS_UPDATE_DEVELOPERS      = 'ms-settings:developers'
$URI_SETTINGS_UPDATE_FINDMYDEVICE    = 'ms-settings:findmydevice'
$URI_SETTINGS_UPDATE_HISTORY         = 'ms-settings:windowsupdate-history'
$URI_SETTINGS_UPDATE_INSIDER         = 'ms-settings:windowsinsider'
$URI_SETTINGS_UPDATE_OPTIONAL        = 'ms-settings:windowsupdate-optionalupdates'
$URI_SETTINGS_UPDATE_RECOVERY        = 'ms-settings:recovery'
$URI_SETTINGS_UPDATE_RESTART         = 'ms-settings:windowsupdate-restartoptions'
$URI_SETTINGS_UPDATE_SECURITY        = 'ms-settings:windowsdefender'
$URI_SETTINGS_UPDATE_SECURITY_OPEN   = 'windowsdefender:'
$URI_SETTINGS_UPDATE_TROUBLESHOOT    = 'ms-settings:troubleshoot'

// --- mixed reality
$URI_SETTINGS_MR             = 'ms-settings:holographic'
$URI_SETTINGS_MR_AUDIO       = 'ms-settings:holographic-audio'
$URI_SETTINGS_MR_ENVIRONMENT = 'ms-settings:privacy-holographic-environment'
$URI_SETTINGS_MR_HEADSET     = 'ms-settings:holographic-headset'
$URI_SETTINGS_MR_UNINSTALL   = 'ms-settings:holographic-management'

// --- surface hub
$URI_SETTINGS_SH_ACCOUNTS          = 'ms-settings:surfacehub-accounts'
$URI_SETTINGS_SH_SESSION_CLEANUP   = 'ms-settings:surfacehub-sessioncleanup'
$URI_SETTINGS_SH_TEAM_CONFERENCING = 'ms-settings:surfacehub-calling'
$URI_SETTINGS_SH_TEAM_DEVICE       = 'ms-settings:surfacehub-devicemanagenent'
$URI_SETTINGS_SH_WELCOME           = 'ms-settings:surfacehub-welcome'

// =============================================================================
// -> system management consoles
$exe_comp_mgmt    = '"@sys.dir\System32\compmgmt.msc"'
$exe_dev_mgmt     = '"@sys.dir\System32\devmgmt.msc"'
$exe_disk_mgmt    = '"@sys.dir\System32\diskmgmt.msc"'
$exe_event_vwr    = '"@sys.dir\System32\eventvwr.msc"'
$exe_perf_mon     = '"@sys.dir\System32\perfmon.msc"'
$exe_print_mgmt   = '"@sys.dir\System32\printmanagement.msc"'
$exe_services     = '"@sys.dir\System32\services.msc"'
$exe_shared_fldrs = '"@sys.dir\System32\fsmgmt.msc"'
$exe_task_schd    = '"@sys.dir\System32\taskschd.msc"'
$exe_user_mgmt    = '"@sys.dir\System32\lusrmgr.msc"'

// -> control panel utilities
$exe_display      = '"@sys.dir\System32\desk.cpl"'
$exe_firewall     = '"@sys.dir\System32\firewall.cpl"'
$exe_net_adapters = '"@sys.dir\System32\ncpa.cpl"'
$exe_power_opts   = '"@sys.dir\System32\powercfg.cpl"'
$exe_programs     = '"@sys.dir\System32\appwiz.cpl"'
$exe_region       = '"@sys.dir\System32\intl.cpl"'
$exe_sound        = '"@sys.dir\System32\mmsys.cpl"'
$exe_sys_props    = '"@sys.dir\System32\sysdm.cpl"'

// -> common windows applications
$exe_calc         = '"@sys.dir\System32\calc.exe"'
$exe_charmap      = '"@sys.dir\System32\charmap.exe"'
$exe_cmd          = '"@sys.dir\System32\cmd.exe"'
$exe_control      = '"@sys.dir\System32\control.exe"'
$exe_ise          = '"@sys.dir\System32\WindowsPowerShell\v1.0\powershell_ise.exe"'
$exe_magnify      = '"@sys.dir\System32\magnify.exe"'
$exe_mspaint      = '"mspaint.exe"'
$exe_notepad      = '"@sys.dir\System32\notepad.exe"'
$exe_osk          = '"@sys.dir\System32\osk.exe"'
$exe_powershell   = '"@sys.dir\System32\WindowsPowerShell\v1.0\powershell.exe"'
$exe_regedit      = '"@sys.dir\regedit.exe"'
$exe_res_mon      = '"@sys.dir\System32\resmon.exe"'
$exe_task_mgr     = '"@sys.dir\System32\taskmgr.exe"'

// --- language bar options
$exe_languagebar = 'rundll32.exe Shell32.dll,Control_RunDLL input.dll,,{C07337D3-DB2C-4D0B-9A93-B722A6C106E2}'


// //
// // ===========================================================================
// // Windows 10 RUN commands (Win+R)
// // ---------------------------------------------------------------------------
// "Open Documents Folder"                           'documents'
// "Open Videos folder"                              'videos'
// "Open Downloads Folder"                           'downloads'
// "Open Favorites Folder"                           'favorites'
// "Open Recent Folder"                              'recent'
// "Open Pictures Folder"                            'pictures'
// "Adding a new Device"                             'devicepairingwizard'
// "About Windows dialog"                            'winver'
// "Add Hardware Wizard"                             'hdwwiz'
// "Advanced User Accounts"                          'netplwiz'
// "Authorization Manager"                           'azman.msc'
// "Backup and Restore"                              'sdclt'
// "Bluetooth File Transfer"                         'fsquirt'
// "Calculator"                                      'calc'
// "Certificates"                                    'certmgr.msc'
// "Change Computer Performance Settings"            'systempropertiesperformance'
// "Change Data Execution Prevention Settings"       'systempropertiesdataexecutionprevention'
// "Change Data Execution Prevention Settings"       'printui'
// "Character Map"                                   'charmap'
// "ClearType Tuner"                                 'cttune'
// "Color Management"                                'colorcpl'
// "Command Prompt"                                  'cmd'
// "Component Services"                              'comexp.msc'
// "Component Services"                              'dcomcnfg'
// "Computer Management"                             'compmgmt.msc'
// "Computer Management"                             'compmgmtlauncher'
// "Connect to a Projector"                          'displayswitch'
// "Control Panel"                                   'control'
// "Create A Shared Folder Wizard"                   'shrpubw'
// "Create a System Repair Disc"                     'recdisc'
// "Data Execution Prevention"                       'systempropertiesdataexecutionprevention'
// "Date and Time"                                   'timedate.cpl'
// "Default Location"                                'locationnotifications'
// "Device Manager"                                  'devmgmt.msc'
// "Device Manager"                                  'hdwwiz.cpl'
// "Device Pairing Wizard"                           'devicepairingwizard'
// "Diagnostics Troubleshooting Wizard"              'msdt'
// "Digitizer Calibration Tool"                      'tabcal'
// "DirectX Diagnostic Tool"                         'dxdiag'
// "Disk Cleanup"                                    'cleanmgr'
// "Disk Defragmenter"                               'dfrgui'
// "Disk Management"                                 'diskmgmt.msc'
// "Display"                                         'dpiscaling'
// "Display Color Calibration"                       'dccw'
// "Display Switch"                                  'displayswitch'
// "DPAPI Key Migration Wizard"                      'dpapimig'
// "Driver Verifier Manager"                         'verifier'
// "Ease of Access Center"                           'utilman'
// "EFS Wizard"                                      'rekeywiz'
// "Event Viewer"                                    'eventvwr.msc'
// "Fax Cover Page Editor"                           'fxscover'
// "File Signature Verification"                     'sigverif'
// "Font Viewer"                                     'fontview'
// "Game Controllers"                                'joy.cpl'
// "IExpress Wizard"                                 'iexpress'
// "Internet Explorer"                               'iexplore'
// "Internet Options"                                'inetcpl.cpl'
// "iSCSI Initiator Configuration Tool"              'iscsicpl'
// "Language Pack Installer"                         'lpksetup'
// "Local Group Policy Editor"                       'gpedit.msc'
// "Local Security Policy"                           'secpol.msc'
// "Local Users and Groups"                          'lusrmgr.msc'
// "Location Activity"                               'locationnotifications'
// "Magnifier"                                       'magnify'
// "Malicious Software Removal Tool"                 'mrt'
// "Manage Your File Encryption Certificates"        'rekeywiz'
// "Microsoft Management Console"                    'mmc'
// "Microsoft Support Diagnostic Tool"               'msdt'
// "Mouse"                                           'main.cpl'
// "NAP Client Configuration"                        'napclcfg.msc'
// "Narrator"                                        'narrator'
// "Network Connections"                             'ncpa.cpl'
// "New Scan Wizard"                                 'wiaacmgr'
// "Notepad"                                         'notepad'
// "ODBC Data Source Administrator"                  'odbcad32'
// "ODBC Driver Configuration"                       'odbcconf'
// "On-Screen Keyboard"                              'osk'
// "Paint"                                           'mspaint'
// "Pen and Touch"                                   'tabletpc.cpl'
// "People Near Me"                                  'collab.cpl'
// "Performance Monitor"                             'perfmon.msc'
// "Performance Options"                             'systempropertiesperformance'
// "Phone and Modem"                                 'telephon.cpl'
// "Phone Dialer"                                    'dialer'
// "Power Options"                                   'powercfg.cpl'
// "Presentation Settings"                           'presentationsettings'
// "Print Management"                                'printmanagement.msc'
// "Printer Migration"                               'printbrmui'
// "Printer User Interface"                          'printui'
// "Private Character Editor"                        'eudcedit'
// "Problem Steps Recorder"                          'psr'
// "Programs and Features"                           'appwiz.cpl'
// "Protected Content Migration"                     'dpapimig'
// "Region and Language"                             'intl.cpl'
// "Registry Editor"                                 'regedit'
// "Registry Editor 32"                              'regedt32'
// "Remote Access Phonebook"                         'rasphone'
// "Remote Desktop Connection"                       'mstsc'
// "Resource Monitor"                                'resmon'
// "Resultant Set of Policy"                         'rsop.msc'
// "SAM Lock Tool"                                   'syskey'
// "Screen Resolution"                               'desk.cpl'
// "Securing the Windows Account Database"           'syskey'
// "Services"                                        'services.msc'
// "Set Program Access and Computer Defaults"        'computerdefaults'
// "Share Creation Wizard"                           'shrpubw'
// "Shared Folders"                                  'fsmgmt.msc'
// "Signout"                                         'logoff'
// "Snipping Tool"                                   'snippingtool'
// "Sound"                                           'mmsys.cpl'
// "Sound recorder"                                  'soundrecorder'
// "SQL Server Client Network Utility"               'cliconfg'
// "Sticky Notes"                                    'stikynot'
// "Stored User Names and Passwords"                 'credwiz'
// "Sync Center"                                     'mobsync'
// "System Configuration"                            'msconfig'
// "System Configuration Editor"                     'sysedit'
// "System Information"                              'msinfo32'
// "System Properties"                               'sysdm.cpl'
// "System Properties (Advanced Tab)"                'systempropertiesadvanced'
// "System Properties (Computer Name Tab)"           'systempropertiescomputername'
// "System Properties (Hardware Tab)"                'systempropertieshardware'
// "System Properties (Remote Tab)"                  'systempropertiesremote'
// "System Properties (System Protection Tab)"       'systempropertiesprotection'
// "System Restore"                                  'rstrui'
// "Task Manager"                                    'taskmgr'
// "Task Scheduler"                                  'taskschd.msc'
// "Trusted Platform Module (TPM) Management"        'tpm.msc'
// "Turn Windows features on or off"                 'optionalfeatures'
// "User Account Control Settings"                   'useraccountcontrolsettings'
// "Utility Manager"                                 'utilman'
// "Volume Mixer"                                    'sndvol'
// "Windows Action Center"                           'wscui.cpl'
// "Windows Activation Client"                       'slui'
// "Windows Anytime Upgrade Results"                 'windowsanytimeupgraderesults'
// "Windows Disc Image Burning Tool"                 'isoburn'
// "Windows Explorer"                                'explorer'
// "Windows Fax and Scan"                            'wfs'
// "Windows Firewall"                                'firewall.cpl'
// "Windows Firewall with Advanced Security"         'wf.msc'
// "Windows Journal"                                 'journal'
// "Windows Media Player"                            'wmplayer'
// "Windows Memory Diagnostic Scheduler"             'mdsched'
// "Windows Mobility Center"                         'mblctr'
// "Windows Picture Acquisition Wizard"              'wiaacmgr'
// "Windows PowerShell"                              'powershell'
// "Windows PowerShell ISE"                          'powershell_ise'
// "Windows Remote Assistance"                       'msra'
// "Windows Repair Disc"                             'recdisc'
// "Windows Script Host"                             'wscript'
// "Windows Update"                                  'wuapp'
// "Windows Update Standalone Installer"             'wusa'
// "Versione Windows"                                'winver'
// "WMI Management"                                  'wmimgmt.msc'
// "WordPad"                                         'write'
// "XPS Viewer"                                      'xpsrchvw'