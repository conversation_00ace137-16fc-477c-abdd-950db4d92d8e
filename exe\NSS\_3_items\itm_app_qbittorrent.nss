
//
$APP_USER_QBITTORRENT_DIR = '@user.desktop\my\flow\home\__GOTO__\Apps\app_qbittorrent\exe\app'
$APP_USER_QBITTORRENT_EXE = '@APP_USER_QBITTORRENT_DIR\qbittorrent.exe'
$APP_USER_QBITTORRENT_TIP = "..."+str.trimstart('@APP_USER_QBITTORRENT_EXE','@app.dir')

// Context: Explorer
item(
    title  = ":  &qBittorrent"
    keys   = "exe"
    type   = 'File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    args   = '"@sel.dir"'
    //
    image  = APP_USER_QBITTORRENT_EXE
    tip    = [APP_USER_QBITTORRENT_TIP,TIP3,0.8]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_USER_QBITTORRENT_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_QBITTORRENT_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_QBITTORRENT_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_QBITTORRENT_DIR')),
    }
)
// Context: Taskbar
item(
    title  = ":  &qBittorrent"
    keys   = "exe"
    type   = 'Taskbar'
    args   = ''
    //
    image  = APP_USER_QBITTORRENT_EXE
    tip    = [APP_USER_QBITTORRENT_TIP,TIP3,0.8]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_USER_QBITTORRENT_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_QBITTORRENT_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_QBITTORRENT_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_QBITTORRENT_DIR')),
    }
)
