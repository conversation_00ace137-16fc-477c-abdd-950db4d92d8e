menu(type="taskbar" vis=key.shift() or key.lbutton() pos=0 title=app.name image=\uE249)
{
	item(title="config" image=\uE10A cmd='"@app.cfg"')
	item(title="manager" image=\uE0F3 admin cmd='"@app.exe"')
	item(title="directory" image=\uE0E8 cmd='"@app.dir"')
	item(title="version\t"+@app.ver vis=label col=1)
	item(title="docs" image=\uE1C4 cmd='https://nilesoft.org/docs')
	item(title="donate" image=\uE1A7 cmd='https://nilesoft.org/donate')
}
menu(where=@(this.count == 0) type='taskbar' image=icon.settings expanded=true)
{
	menu(title="Apps" image=\uE254)
	{
		item(title='Paint' image=\uE116 cmd='mspaint')
		item(title='Edge' image cmd='@sys.prog32\Microsoft\Edge\Application\msedge.exe')
		item(title='Calculator' image=\ue1e7 cmd='calc.exe')
		item(title=str.res('regedit.exe,-16') image cmd='regedit.exe')
	}
	menu(title=title.windows image=\uE1FB)
	{
		item(title=title.cascade_windows cmd=command.cascade_windows)
		item(title=title.Show_windows_stacked cmd=command.Show_windows_stacked)
		item(title=title.Show_windows_side_by_side cmd=command.Show_windows_side_by_side)
		sep
		item(title=title.minimize_all_windows cmd=command.minimize_all_windows)
		item(title=title.restore_all_windows cmd=command.restore_all_windows)
	}
	item(
		title=title.taskbar_Settings
		sep=both
		image=inherit
		cmd='ms-settings:taskbar'
	)
	item(
		vis=key.shift()
		image=icon.refresh
		title='Restart Explorer'
		cmd=command.restart_explorer
	)
}

modify(
	find='Apps and Features'
	type='Taskbar'
	image=image.fluent(\uE71D,13)
)

modify(
	find='Power Options'
	type='Taskbar'
	image=image.fluent(\uE945,13)
)

modify(
	find='Event Viewer'
	type='Taskbar'
	image=image.fluent(\uE81C,13)
)

modify(
	find='System'
	type='Taskbar'
	image=image.fluent(\uE770,13)
)

modify(
	find='Network Connections'
	type='Taskbar'
	image=icon.add_a_network_location
)

modify(
	find='Computer Management'
	type='Taskbar'
	image=icon.pc
)

modify(
	find='Windows Terminal'
	type='Taskbar'
	image=icon.run_with_powershell
)

modify(
	find='Run'
	type='Taskbar'
	image=icon.run_as_administrator
)

modify(
	find='Shut Down or Sign Out'
	type='Taskbar'
	image=image.glyph(\uE12F)
)

modify(
	find='Clear recent items list'
	type='*'
	image=image.fluent(\uE8D8,13)
)

modify(
	find='Show on Desktop'
	type='*'
	image=icon.desktop
)

modify(
	find='Display as a menu'
	type='*'
	image=image.fluent(\uE8BC,13)
)

modify(
	find='Remove from this list'
	type='*'
	image=image.fluent(\uE711,13)
)

modify(
	find='Change icon'
	type='*'
	image=icon.customize_this_folder
)

modify(
	find='Uninstall'
	type='*'
	image=image.fluent(\uE894,13)
)

