﻿<h4>Expressions</h4>
<br>
<p><b>Shell</b> provides a variety of statements and expressions. Most of these will be familiar to developers who have programmed in Java script, C, C++, C#.</p>
<p>Expressions gives you all the power of <b>Shell</b>, but is using a simplified syntax that's easier to learn if you're a beginner, and makes you more productive if you're an expert.</p>
<p>To use expressions, you write them by using proper syntax. Syntax is the set of rules by which the words and symbols in an expression are correctly combined. Initially, expressions in <b>Shell</b> are a little bit hard to read. But with a good understanding of expression syntax and a little practice, it becomes much easier.</p>
<ul>
	<li>Expressions is non-case sensitive</li>
	<li>Expressions (variables and functions) start with @</li>
	<li>blocks are enclosed in @( ... )</li>
	<li>Strings are enclosed with quotation marks or single quotation marks</li>
</ul>

