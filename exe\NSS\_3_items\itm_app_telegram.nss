
//
$APP_USER_TELEGRAM_DIR = '@user.desktop\my\flow\home\__GOTO__\Apps\app_telegram\exe'
$APP_USER_TELEGRAM_EXE = '@APP_USER_TELEGRAM_DIR\Telegram.exe'
$APP_USER_TELEGRAM_TIP = "..."+str.trimstart('@APP_USER_TELEGRAM_EXE','@app.dir')

// context: directory
item(
    title  = ":  &Telegram"
    keys   = "exe"
    type   = 'Desktop|Dir|Drive|Back.Dir|Back.Drive'
    args   = '"@sel.dir"'
    //
    image  = APP_USER_TELEGRAM_EXE
    tip    = [APP_USER_TELEGRAM_TIP,TIP3,0.8]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_USER_TELEGRAM_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_TELEGRAM_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_TELEGRAM_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_TELEGRAM_DIR')),
    }
)
// Context: Taskbar
item(
    title  = ":  &Telegram"
    keys   = "exe"
    type   = 'Taskbar'
    args   = ''
    //
    image  = APP_USER_TELEGRAM_EXE
    tip    = [APP_USER_TELEGRAM_TIP,TIP3,0.8]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_USER_TELEGRAM_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_TELEGRAM_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_TELEGRAM_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_TELEGRAM_DIR')),
    }
)
