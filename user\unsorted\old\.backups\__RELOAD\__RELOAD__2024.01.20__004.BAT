@ECHO OFF
SETLOCAL ENABLEDELAYEDEXPANSION

:: =============================================================================
:: Shell Script with Backup and Initialization Logic
:: =============================================================================

CALL :InitBatchScript
CALL :SetScriptOptions
CALL :BackupShellLog
:: CALL :ReloadShell
EXIT /B 0

:: =============================================================================
:: Initialize Batch Script
:: =============================================================================
:InitBatchScript
    IF EXIST "%~1" (CD /D "%~1") ELSE (CD /D "%~dp0")
    SET "INIT_StartPath=%CD%"
    SET "INIT_BatFullPath=%~dp0%~nx0"
    SET "INIT_BatDirectory=%~dp0"
    SET "INIT_BatFileName=%~nx0"
    SET "INIT_BaseName=%~n0"
GOTO :EOF

:: =============================================================================
:: Set Script Options
:: =============================================================================
:SetScriptOptions
    SET "DEBUG=true"
    SET "CMD_SetEchoOff=true"
    SET "OPT_ShowInfo=true"
    SET "OPT_ShowCommandInfo=true"
    SET "SET_ReexecuteOnKeyPress=true"
    SET "SET_ContinuousExecution=true"
    SET "SET_AutoCloseAfterDelay=true"
    SET "SET_ContinuousExecutionDelay=5"
    SET "SET_AutoCloseAfterDelayDelay=10"
GOTO :EOF

:: =============================================================================
:: Subroutines
:: =============================================================================

:BackupShellLog
    :: Initialize
    CALL :fnSetDebugState BackupShellLog


:BackupShellLog
    SET "sourceFile=shell.log"
    SET "targetDir=%CD%\logs"
    IF NOT EXIST "%targetDir%\" MKDIR "%targetDir%"


    ECHO Starting to process log files...
    SET "maxNum=0"
    FOR /F "tokens=*" %%F IN ('dir /B /A-D /O-N "%targetDir%\shell.*.log"') DO (
        SET "processFile=true"
        SET "fileName=%%~nF"
        SET "fileNum=!fileName:shell.=!"

        ECHO Checking file %%F
        IF "!fileNum!"=="" SET "processFile=false"
        IF "!processFile!"=="true" (
            SET /A fileNum=1!fileNum! - 1000000
            ECHO Found file %%F with number !fileNum!
            IF !fileNum! GTR !maxNum! SET "maxNum=!fileNum!"
        ) ELSE (
            ECHO Skipping invalid file: %%F
        )
    )

    ECHO Highest number found: !maxNum!
    SET /A newNum=!maxNum! + 1
    ECHO New number before padding: !newNum!
    CALL :PadNumber !newNum! newPaddedNum
    SET "newFileName=shell.!newPaddedNum!.log"

    ECHO New file name: !newFileName!

    IF "!newFileName!"=="shell..log" (
        ECHO Invalid file name generated. Aborting copy.
        GOTO :EOF
    )

    COPY "%sourceFile%" "%targetDir%\!newFileName!"
    GOTO :EOF

:: Function to pad a number.
:PadNumber
    SET "num=%~1"
    SET "paddedNum=000000%num%"
    SET "paddedNum=!paddedNum:~-6!"
    SET "%~2=!paddedNum!"
    GOTO :EOF



:ReloadShell
    CLS
    shell -register -restart
    EXPLORER %INIT_StartPath%
GOTO :EOF



:: =============================================================================
:: General Functions -> Batch-Script Functions
:: =============================================================================
::
:fnCmdSetBatchPaths
    CALL :fnSetDebugState fnCmdSetBatchPaths
    IF EXIST "%~1" (CD /D "%~1") ELSE (CD /D "%~dp0")
    SET "INIT_BaseName=%~n0"
    SET "INIT_StartPath=%CD%"
    SET "INIT_BatFileName=%~nx0"
    SET "INIT_BatFullPath=%~dp0%~nx0"
    SET "INIT_PyFileName=%~n0.py"
    SET "INIT_PyFilePath=%~dp0%~n0.py"
    SET "INIT_WindowTitle=%INIT_BaseName%"
GOTO :EOF
::
:fnCmdSetEcho
    CALL :fnSetDebugState fnCmdSetEcho
    IF "%1" == "true" (@ECHO OFF) ELSE (@ECHO ON)
GOTO :EOF
::
:fnCmdExitGracefully
    CALL :fnSetDebugState fnCmdExitGracefully
    CALL :fnCmdEchoLineBreaks 3
    ECHO Window will automatically close in %1 seconds ...
    PING 127.0.0.1 -n %1 > NUL & EXIT
GOTO :EOF
::
:fnCmdEchoLineBreaks
    CALL :fnSetDebugState fnCmdEchoLineBreaks
    SET "LineBreaks=%~1"
    for /L %%i in (1,1,%LineBreaks%) do ECHO(
GOTO :EOF
::
:fnCmdEchoLongLine
    CALL :fnSetDebugState fnCmdEchoLongLine
    SET "LineLength=%~1"
    SET "Char=%~2"
    if "!LineLength!"=="" SET "LineLength=80"
    if "!Char!"=="" SET "Char=-"
    if "!Char!"=="0" SET "Char=_"
    if "!Char!"=="1" SET "Char=-"
    if "!Char!"=="2" SET "Char=="
    SET "Line="
    for /L %%i in (1,1,%LineLength%) do SET "Line=!Line!!Char!"
    ECHO !Line!
    if "!Char:~0,1!"=="_" ECHO.
GOTO :EOF
::
:fnSetDebugState
    IF "%DEBUG%" == "true" (
        ECHO ^=^=^> %1
        PAUSE > NUL
    )
GOTO :EOF
