
// static items
// static.nss



// modify(find='NordVPN' pos=pos.middle)


// modify(type='recyclebin' where=window.is_desktop and this.id==id.empty_recycle_bin pos=1 sep)
// modify(type='back' find=['shortcut', '/new'] vis=vis.remove)
// modify(find='open with visual studio' pos=1 menu='develop/editors')

// // 7-Zip
modify(find='7-Zip' pos=pos.middle)
// modify(find='WinRAR' pos=pos.middle)

// // Windows Explorer - Rarely used modifys
modify(where=this.id(id.copy_as_path) sep=sep.top menu='Windows Explorer')
modify(where=this.id(id.print) sep=sep.top menu='Windows Explorer')
modify(where=this.id(id.rotate_left) sep=sep.top menu='Windows Explorer')
modify(where=this.id(id.rotate_right) menu='Windows Explorer')
modify(where=this.id(id.troubleshoot_compatibility) sep=sep.top menu='Windows Explorer')
modify(where=this.id(id.extract_all) sep=sep.top menu='Windows Explorer')
modify(where=this.id(id.open_in_new_tab) sep=sep.top menu='Windows Explorer')
modify(where=this.id(id.open_in_new_process) menu='Windows Explorer')
modify(where=this.id(id.open_in_new_window) menu='Windows Explorer')
modify(where=this.id(id.pin_to_start) sep=sep.top menu='Windows Explorer')
modify(where=this.id(id.unpin_from_start) sep=sep.top menu='Windows Explorer')
modify(where=this.id(id.pin_to_quick_access) menu='Windows Explorer')
modify(where=this.id(id.unpin_from_quick_access) menu='Windows Explorer')
modify(where=this.id(id.pin_to_taskbar) menu='Windows Explorer')
modify(where=this.id(id.unpin_from_taskbar) menu='Windows Explorer')
modify(find='add to favorites' sep=sep.top menu='Windows Explorer')
modify(where=this.id(id.customize_this_folder) type='dir.back|drive.back' menu='Windows Explorer')
modify(where=this.id(id.give_access_to) sep=sep.top menu='Windows Explorer')
modify(where=this.id(id.include_in_library) menu='Windows Explorer')
modify(where=this.id(id.cast_to_device) menu='Windows Explorer')
modify(where=this.id(id.restore_previous_versions) menu='Windows Explorer')

modify(where=this.id(id.show_touch_keyboard_button) menu='Windows Explorer')


// // PowerToys
// modify(find='powerrename' menu='PowerToys')
// modify(find='resize pictures' menu='PowerToys')
// modify(find='s using this file?' menu='PowerToys')

// // Sharing services
// modify(find='send with nearby share' pos=pos.top menu='Share')
// modify(where=this.id(id.share) sep=sep.top pos=pos.top menu='Share')
// modify(where=this.id(id.send_to) pos=pos.top menu='Share')

// // OneDrive
// modify(find='always keep on this device' menu='OneDrive')
// modify(find='free up space' sep=sep.bottom menu='OneDrive')
// modify(find='view online' menu='OneDrive')
// modify(find='Share' menu='OneDrive')
// modify(find='Copy link' menu='OneDrive')
// modify(find='manage access' sep=sep.bottom menu='OneDrive')
// modify(find='Version History' menu='OneDrive')
// modify(find='Move to OneDrive' menu='OneDrive')

// // VLC
// modify(find='vlc media player' pos=pos.top menu='VLC Media Player')

// // Shell
// modify(find='open in terminal*' title='Terminal' menu='Terminal')
// modify(find='open powershell window here' title='Windows PowerShell' menu='Terminal')
// modify(find='open linux shell here' title='Linux shell' menu='Terminal')
// modify(find='open git bash here' title='Git Bash' menu='Terminal')
// modify(find='WSL' menu='Terminal')

// //Move and organize
// //modify(mode=mode.multiple find='scan with' menu=title.more_options)
// // modify(mode=mode.multiple
// //   where=this.id(id.send_to,id.share,id.create_shortcut,id.set_as_desktop_background,id.rotate_left,
// //                   id.rotate_right, id.map_network_drive,id.disconnect_network_drive,id.format, id.eject,
// //                   id.give_access_to,id.include_in_library,id.print)
// //   pos=1 menu=title.more_options)



// shell
// {
// 	// static items

// 	// Delete items by identifiers
// 	item(mode=mode.multiple
// 		where=this.id(id.restore_previous_versions,id.cast_to_device)
// 		vis=vis.remove)

// 	item(type='recyclebin' where=window.is_desktop and this.id==id.empty_recycle_bin pos=1 sep)
// 	// item(type='back' find=['shortcut', '/new'] vis=vis.remove)
// 	// item(find='unpin' pos=pos.bottom menu="Pin//Unpin")
// 	// item(find='pin' pos=pos.top menu="Pin//Unpin")
// 	// item(where=this.id==id.copy_as_path menu='file manage')
// 	// item(type='dir.back|drive.back' where=this.id==id.customize_this_folder pos=1 sep='top' menu='file manage')
// 	// item(find='open in terminal*' pos=pos.bottom sep menu='Terminal')
// 	// item(find='open with visual studio' pos=1 menu='develop/editors')
// 	// //Move and organize
// 	// //item(mode=mode.multiple find='scan with' menu=title.more_options)
// 	// item(mode=mode.multiple
// 	// 	where=this.id(id.send_to,id.share,id.create_shortcut,id.set_as_desktop_background,id.rotate_left,
// 	// 					id.rotate_right, id.map_network_drive,id.disconnect_network_drive,id.format, id.eject,
// 	// 					id.give_access_to,id.include_in_library,id.print)
// 	// 	pos=1 menu=title.more_options)
// }