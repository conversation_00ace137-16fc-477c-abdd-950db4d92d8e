
//
$PY_CLOSEDUPLICATEWINDOWS_DIR = '@user.desktop\my\flow\home\__GOTO__\Scripts\Python\Py_Cli_Utils\py__CloseDuplicateWindows'
$PY_CLOSEDUPLICATEWINDOWS_EXE = '@PY_CLOSEDUPLICATEWINDOWS_DIR\venv\Scripts\python.exe'
$PY_CLOSEDUPLICATEWINDOWS_APP = '@PY_CLOSEDUPLICATEWINDOWS_DIR\main.py'
//

// Context: Explorer
$PY_CLOSEDUPLICATEWINDOWS_ARGS = '--prompt'
item(
    title="&CloseDuplicateWindows"
    keys="py"
    type='File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    //
    image=[E17C,GREY] image-sel=[E17C,PURPLE]
    tip=['"@PY_CLOSEDUPLICATEWINDOWS_APP" @PY_CLOSEDUPLICATEWINDOWS_ARGS',TIP3,0.75]
    //
    admin=keys.rbutton()
    cmd=if(KEYS_EXE_OPEN_EXE,('"@PY_CLOSEDUPLICATEWINDOWS_EXE"'))
    args='"@PY_CLOSEDUPLICATEWINDOWS_APP" @PY_CLOSEDUPLICATEWINDOWS_ARGS'
    //
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@PY_CLOSEDUPLICATEWINDOWS_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@PY_CLOSEDUPLICATEWINDOWS_APP')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@PY_CLOSEDUPLICATEWINDOWS_DIR')),
    }
)
// Context: Taskbar
$PY_CLOSEDUPLICATEWINDOWS_TASKBAR = '--prompt'
item(
    title="&CloseDuplicateWindows"
    keys="py"
    type='Titlebar|Taskbar'
    //
    image=[E17C,GREY]
    image-sel=[E17C,PURPLE]
    //
    admin=keys.rbutton()
    cmd=if(KEYS_EXE_OPEN_EXE,('"@PY_CLOSEDUPLICATEWINDOWS_EXE"'))
    args='"@PY_CLOSEDUPLICATEWINDOWS_APP" @PY_CLOSEDUPLICATEWINDOWS_TASKBAR'
    tip=['"@PY_CLOSEDUPLICATEWINDOWS_APP" @PY_CLOSEDUPLICATEWINDOWS_TASKBAR',TIP3,0.75]
    //
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@PY_CLOSEDUPLICATEWINDOWS_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@PY_CLOSEDUPLICATEWINDOWS_APP')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@PY_CLOSEDUPLICATEWINDOWS_DIR')),
    }
)
