
//
$APP_LIBREOFFICE_NSS = '@app.dir\NSS\_3_items\user_apps'
$APP_LIBREOFFICE_SRC = '@user.desktop\my\flow\home\__GOTO__\Apps\app_libreoffice'
//
$APP_USER_LIBREOFFICE_DIR = '@user.desktop\my\flow\home\__GOTO__\Apps\app_libreoffice\exe'
$APP_USER_LIBREOFFICE_EXE = '@APP_USER_LIBREOFFICE_DIR\LibreOfficePortable.exe'
$APP_USER_LIBREOFFICE_TIP = "..."+str.trimstart('@APP_USER_LIBREOFFICE_EXE','@app.dir')
//
$APP_USER_LIBREOFFICE_BASE_EXE = '@APP_USER_LIBREOFFICE_DIR\LibreOfficeBasePortable.exe'
$APP_USER_LIBREOFFICE_CALC_EXE = '@APP_USER_LIBREOFFICE_DIR\LibreOfficeCalcPortable.exe'
$APP_USER_LIBREOFFICE_DRAW_EXE = '@APP_USER_LIBREOFFICE_DIR\LibreOfficeDrawPortable.exe'
$APP_USER_LIBREOFFICE_IMPRESS_EXE = '@APP_USER_LIBREOFFICE_DIR\LibreOfficeImpressPortable.exe'
$APP_USER_LIBREOFFICE_MATH_EXE = '@APP_USER_LIBREOFFICE_DIR\LibreOfficeMathPortable.exe'
$APP_USER_LIBREOFFICE_WRITER_EXE = '@APP_USER_LIBREOFFICE_DIR\LibreOfficeWriterPortable.exe'
//

// Context: File
item(
    title  = ":  &LibreOffice"
    keys   = "exe"
    type   = 'File'
    args   = '"@sel.file"'
    where  = str.equals(sel.file.ext,[".odb", ".ods", ".odg", ".odp", ".odf", ".odt"])
    //
    image  = APP_USER_LIBREOFFICE_EXE
    tip    = [APP_USER_LIBREOFFICE_TIP,TIP3,1.0]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_USER_LIBREOFFICE_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_LIBREOFFICE_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_LIBREOFFICE_EXE')),
        cmd=if(KEYS_EXE_GOTO_NSS,('@APP_LIBREOFFICE_NSS')),
        cmd=if(KEYS_EXE_GOTO_SRC,('@APP_LIBREOFFICE_SRC')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_LIBREOFFICE_DIR')),
    }
)
// Context: Directory
item(
    title  = ":  &LibreOffice"
    keys   = "exe"
    type   = 'Desktop|Dir|Drive|Back.Dir|Back.Drive'
    args   = '"@sel.dir"'
    //
    image  = APP_USER_LIBREOFFICE_EXE
    tip    = [APP_USER_LIBREOFFICE_TIP,TIP3,1.0]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_USER_LIBREOFFICE_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_LIBREOFFICE_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_LIBREOFFICE_EXE')),
        cmd=if(KEYS_EXE_GOTO_NSS,('@APP_LIBREOFFICE_NSS')),
        cmd=if(KEYS_EXE_GOTO_SRC,('@APP_LIBREOFFICE_SRC')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_LIBREOFFICE_DIR')),
    }
)
// Context: Taskbar
item(
    title  = ":  &LibreOffice"
    keys   = "exe"
    type   = 'Taskbar'
    args   = ''
    //
    image  = APP_USER_LIBREOFFICE_EXE
    tip    = [APP_USER_LIBREOFFICE_TIP,TIP3,1.0]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_USER_LIBREOFFICE_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_LIBREOFFICE_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_LIBREOFFICE_EXE')),
        cmd=if(KEYS_EXE_GOTO_NSS,('@APP_LIBREOFFICE_NSS')),
        cmd=if(KEYS_EXE_GOTO_SRC,('@APP_LIBREOFFICE_SRC')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_LIBREOFFICE_DIR')),
    }
)

