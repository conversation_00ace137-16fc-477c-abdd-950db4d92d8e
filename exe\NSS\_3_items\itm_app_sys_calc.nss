
//
$APP_SYS_CALC_DIR = '@sys.dir\System32'
$APP_SYS_CALC_EXE = '@APP_SYS_CALC_DIR\calc.exe'
$APP_SYS_CALC_TIP = '@APP_SYS_CALC_EXE'

// Context: Explorer
item(
    title  = ":  &Calc"
    keys   = "exe"
    type   = 'File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    //
    image  = [E1E7,LOWKEY] image-sel = [E1E7,HOVER]
    tip    = [APP_SYS_CALC_TIP,TIP3,0.5]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_SYS_CALC_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_SYS_CALC_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_SYS_CALC_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_SYS_CALC_DIR')),
    }
)
// Context: Taskbar
item(
    title  = ":  &Calc"
    keys   = "exe"
    type   = 'Taskbar'
    //
    image  = [E1E7,LOWKEY] image-sel = [E1E7,HOVER]
    tip    = [APP_SYS_CALC_TIP,TIP3,0.5]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_SYS_CALC_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_SYS_CALC_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_SYS_CALC_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_SYS_CALC_DIR')),
    }
)

