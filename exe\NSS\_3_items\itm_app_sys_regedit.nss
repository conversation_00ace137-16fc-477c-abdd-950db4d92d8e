
//
$APP_SYS_REGEDIT_DIR = '@sys.dir'
$APP_SYS_REGEDIT_EXE = '@APP_SYS_REGEDIT_DIR\regedit.exe'
$APP_SYS_REGEDIT_TIP = '@APP_SYS_REGEDIT_EXE'

// Context: Explorer
item(
    title  = ":  &Regedit"
    keys   = "exe"
    type   = 'File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    //
    image  = APP_SYS_REGEDIT_EXE
    // image  = [E18C,LOWKEY] image-sel = [E18C,HOVER]
    tip    = [APP_SYS_REGEDIT_TIP,TIP3,0.5]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_SYS_REGEDIT_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_SYS_REGEDIT_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_SYS_REGEDIT_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_SYS_REGEDIT_DIR')),
    }
)
// Context: Taskbar
item(
    title  = ":  &Regedit"
    keys   = "exe"
    type   = 'Taskbar'
    //
    image  = APP_SYS_REGEDIT_EXE
    // image  = [E18C,LOWKEY] image-sel = [E18C,HOVER]
    tip    = [APP_SYS_REGEDIT_TIP,TIP3,0.5]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_SYS_REGEDIT_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_SYS_REGEDIT_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_SYS_REGEDIT_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_SYS_REGEDIT_DIR')),
    }
)