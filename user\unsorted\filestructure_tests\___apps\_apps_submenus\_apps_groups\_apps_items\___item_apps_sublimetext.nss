
/* item: singles/apps/sublimetext */

//
$APP_SUBLIMETEXT_DIR = '@app.dir\PORTAL\APPS\exe_sublimetext\exe'
$APP_SUBLIMETEXT_EXE = '@APP_SUBLIMETEXT_DIR\sublime_text.exe'
$APP_SUBLIMETEXT_TIP = "..."+str.trimstart('@APP_SUBLIMETEXT_EXE','@app.dir')

// -> context: file
item(
    title  = ":  &Sublime Text"
    keys   = "exe"
    type   = 'File'
    args   = '"@sel.file"'
    where  = !str.equals(sel.file.ext,[".7z",".zip",".rar",".apk",".bin",".dll",".dmg",".exe",".sys"])
    //
    image  = APP_SUBLIMETEXT_EXE
    tip    = [APP_SUBLIMETEXT_TIP,TIP3,1.0]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_SUBLIMETEXT_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_SUBLIMETEXT_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_SUBLIMETEXT_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_SUBLIMETEXT_DIR')),
    }
)
// context: directory
item(
    title  = ":  &Sublime Text"
    keys   = "exe"
    type   = 'Desktop|Dir|Drive|Back.Dir|Back.Drive'
    args   = '"@sel.dir"'
    //
    image  = APP_SUBLIMETEXT_EXE
    tip    = [APP_SUBLIMETEXT_TIP,TIP3,1.0]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_SUBLIMETEXT_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_SUBLIMETEXT_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_SUBLIMETEXT_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_SUBLIMETEXT_DIR')),
    }
)
// Context: Taskbar
item(
    title  = ":  &Sublime Text"
    keys   = "exe"
    type   = 'Taskbar'
    args   = ''
    //
    image  = APP_SUBLIMETEXT_EXE
    tip    = [APP_SUBLIMETEXT_TIP,TIP3,1.0]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_SUBLIMETEXT_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_SUBLIMETEXT_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_SUBLIMETEXT_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_SUBLIMETEXT_DIR')),
    }
)


// // THIS DOESN'T WORK, CONTEXT SHOULD BE ADDED IN REGISTRY INSTEAD
// // -> process -> everything64.exe
// item(type='*'
//     where='@if_everything64'
//     //
//     title=":  &Sublime Text"
//     keys=""
//     tip=[APP_SUBLIMETEXT_EXE,TIP3,1.0]
//     image=APP_SUBLIMETEXT_EXE
//     window='Hidden'
//     //
//     admin=keys.rbutton()
//     cmd=if(KEYS_EXE_OPEN_EXE,('"@APP_SUBLIMETEXT_EXE"'))
//     args='@SUBLIME_FILE'
//     //
//     commands{
//         cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_SUBLIMETEXT_DIR')),
//         cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_SUBLIMETEXT_EXE')),
//         cmd=if(KEYS_EXE_OPEN_DIR,('@APP_SUBLIMETEXT_DIR')),
//     }
// )


