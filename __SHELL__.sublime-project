{
    "folders": [
        {
            "name": "__SHELL__/exe",
            "path": "./exe",
            "folder_exclude_patterns": ["*.egg-info", ".backups", ".DS_Store", ".git", ".hg", ".idea", ".svn", ".vscode", "__pycache__", "build", "dist", "env", "logs", "node_modules", "venv", ],
            "file_exclude_patterns": ["*.log", "*.pyc", "*.pyo", "*.sublime-workspace", "*.swp", "*.tmp", ".DS_Store", ]
        },
        {
            "name": "__SHELL__/py",
            "path": ".",
            "folder_exclude_patterns": ["exe", "user", "versions", "*.egg-info", ".backups", ".DS_Store", ".git", ".hg", ".idea", ".svn", ".vscode", "__pycache__", "build", "dist", "env", "logs", "node_modules", "venv", ],
            "file_exclude_patterns": ["*.log", "*.pyc", "*.pyo", "*.sublime-workspace", "*.swp", "*.tmp", ".DS_Store", ]
        }
    ],
    "settings": {
        "tab_size": 4,
        "default_line_ending": "unix",
        "translate_tabs_to_spaces": true,
        "ensure_newline_at_eof_on_save": true,
        "trim_trailing_white_space_on_save": true,
        "python_interpreter": "$project_path\\venv\\Scripts\\python",
        "python_formatter": "black",
        "python_linter": "flake8",
        "python_format_on_save": false
    },
    "build_systems": [
        {
            "name": "__SHELL__.sublime-project",
            "cmd": [
                "$project_path\\venv\\Scripts\\python",
                "-u",
                "${file}"
            ],
            "file_regex": "^[ ]*File \"(...*?)\", line ([0-9]*)",
            "selector": "source.python",
            "shell": true,
            "working_dir": "${project_path}"
        }
    ]
}
