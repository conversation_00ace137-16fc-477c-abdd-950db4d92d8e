
// $CMD_GIT_ADD_ALL = '/C (CD /D "@sel.dir") && (git add *)'
// $CMD_GIT_ADD_ALL_V = '/K (CD /D "@sel.dir") && (git add *) && PAUSE'
// $CMD_GIT_ADD_SELECTION = '/C (CD /D "@sel.dir") && (git add @BATCH_SEL_AS_STRING) && (@BATCH_EXIT_WITH_MSG)'
// $CMD_GIT_ADD_SELECTION_V = '/K (CD /D "@sel.dir") && (git add @BATCH_SEL_AS_STRING) && (@BATCH_EXIT_WITH_MSG) && PAUSE'

//
$PY_WINDOWUTILS_DIR = '@user.desktop\my\flow\home\__GOTO__\Scripts\Python\Py_Cli_Utils\py__WindowUtils'
$PY_WINDOWUTILS_EXE = '@PY_WINDOWUTILS_DIR\venv\Scripts\python.exe'
$PY_WINDOWUTILS_APP = '@PY_WINDOWUTILS_DIR\main.py'
//

// Context: Explorer
$PY_WINDOWUTILS_EXPLORER = '--close-duplicate-windows --prompt'
item(
    title="&WindowUtils"
    keys="py"
    type='File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    //
    image=[E17C,GREY] image-sel=[E17C,PURPLE]
    tip=['"@PY_WINDOWUTILS_APP" @PY_WINDOWUTILS_EXPLORER',TIP3,0.75]
    //
    admin=keys.rbutton()
    cmd=if(KEYS_EXE_OPEN_EXE,('"@PY_WINDOWUTILS_EXE"'))
    args='"@PY_WINDOWUTILS_APP" @PY_WINDOWUTILS_EXPLORER'
    //
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@PY_WINDOWUTILS_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@PY_WINDOWUTILS_APP')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@PY_WINDOWUTILS_DIR')),
    }
)
// Context: Taskbar
$PY_WINDOWUTILS_TASKBAR = '--close-duplicate-windows --prompt'
item(
    title="&WindowUtils"
    keys="py"
    type='Taskbar'
    //
    image=[E17C,GREY]
    image-sel=[E17C,PURPLE]
    //
    admin=keys.rbutton()
    cmd=if(KEYS_EXE_OPEN_EXE,('"@PY_WINDOWUTILS_EXE"'))
    args='"@PY_WINDOWUTILS_APP" @PY_WINDOWUTILS_TASKBAR'
    tip=['"@PY_WINDOWUTILS_APP" @PY_WINDOWUTILS_TASKBAR',TIP3,0.75]
    //
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@PY_WINDOWUTILS_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@PY_WINDOWUTILS_APP')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@PY_WINDOWUTILS_DIR')),
    }
)
