
menu(type='~taskbar' title=title.terminal image=[\uE26E, #ff66e3])
{

    $tip_run_admin=["Press SHIFT key to run as administrator", tip.warning, 1.0]
    $has_admin=key.shift() or key.rbutton()

    item(title=title.command_prompt tip=tip_run_admin admin=has_admin image cmd='cmd.exe'args='/K TITLE ^< @sel.path.name ^/ ^> &ver& PUSHD "@sel.dir"')

    item(title=title.windows_powershell admin=has_admin tip=tip_run_admin image cmd='powershell.exe'args='-noexit -command Set-Location -Path "@sel.dir\."')

    item(where=package.exists("WindowsTerminal") title=title.Windows_Terminal tip=tip_run_admin admin=has_admin image='@package.path("WindowsTerminal")\WindowsTerminal.exe'cmd='wt.exe'arg='-d "@sel.path\."')
}
