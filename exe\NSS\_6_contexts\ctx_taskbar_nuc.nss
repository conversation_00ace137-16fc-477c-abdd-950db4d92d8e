
/* taskbar */
menu(type='Taskbar' expanded=true) {

    // [ ]
    menu(vis='@KEYS_MNU_VISIBILITY_0_DEFAULT' expanded=true) {
        import '@app.dir/NSS/_5_menus/mnu_apps_microsoft.nss'
        separator()
    }
    // [ ]
    menu(vis='@KEYS_MNU_VISIBILITY_0_DEFAULT' expanded=true) {
        import '@app.dir/NSS/_5_menus/mnu_user_jorn_apps.nss'
        import '@app.dir/NSS/_5_menus/mnu_user_nuc_urls.nss'
        separator()
    }

    import '@app.dir/NSS/_3_items/itm_app_sys_osk.nss'
    separator()

    // // [shift]
    // menu(vis='@KEYS_MNU_VISIBILITY_1_SHIFT' expanded=true) {
    //     import '@app.dir/NSS/_5_menus/wip_menus/grp_UserLibrary.nss'
    //     import '@app.dir/NSS/_5_menus/wip_menus/mnu_recentpaths.nss'
    //     separator()
    // }


    // // [ctrl]
    // menu(vis='@KEYS_MNU_VISIBILITY_1_CTRL' expanded=true) {

    //     item(title="User"  image=[E0E2,GREY] vis='Static' sep='Both')
    //     import '@app.dir/NSS/_3_items/itm_app_audacity.nss'
    //     import '@app.dir/NSS/_3_items/itm_app_bambustudio.nss'
    //     import '@app.dir/NSS/_3_items/itm_app_beyondcompare.nss'
    //     import '@app.dir/NSS/_3_items/itm_app_filezilla.nss'
    //     import '@app.dir/NSS/_3_items/itm_app_irfanview.nss'
    //     import '@app.dir/NSS/_3_items/itm_app_kdenlive.nss'
    //     import '@app.dir/NSS/_3_items/itm_app_libreoffice.nss'
    //     import '@app.dir/NSS/_3_items/itm_app_notepad++.nss'
    //     import '@app.dir/NSS/_3_items/itm_app_pdf24.nss'
    //     import '@app.dir/NSS/_3_items/itm_app_powertoys.nss'
    //     import '@app.dir/NSS/_3_items/itm_app_rufus.nss'
    //     import '@app.dir/NSS/_3_items/itm_app_rustdesk.nss'
    //     import '@app.dir/NSS/_3_items/itm_app_sharex.nss'
    //     import '@app.dir/NSS/_3_items/itm_app_telegram.nss'
    //     import '@app.dir/NSS/_3_items/itm_app_winmerge.nss'

    //     item(title="System"  image=[E09E,GREY] vis='Static' sep='Both')
    //     import '@app.dir/NSS/_3_items/itm_app_sys_calc.nss'
    //     import '@app.dir/NSS/_3_items/itm_app_sys_magnify.nss'
    //     import '@app.dir/NSS/_3_items/itm_app_sys_mspaint.nss'
    //     import '@app.dir/NSS/_3_items/itm_app_sys_notepad.nss'
    //     import '@app.dir/NSS/_3_items/itm_app_sys_osk.nss'
    //     import '@app.dir/NSS/_3_items/itm_app_sys_powershell.nss'
    //     import '@app.dir/NSS/_3_items/itm_app_sys_powershellise.nss'
    //     import '@app.dir/NSS/_3_items/itm_app_sys_regedit.nss'
    //     separator()

    // }

    // // [ ]
    // menu(vis='@KEYS_MNU_VISIBILITY_0_DEFAULT' expanded=true) {
    //     item(title="System"  image=[E09E,GREY] vis='Static' sep='Both')
    //     import '@app.dir/NSS/_3_items/itm_app_sys_calc.nss'
    //     // import '@app.dir/NSS/_3_items/itm_app_sys_magnify.nss'
    //     // import '@app.dir/NSS/_3_items/itm_app_sys_mspaint.nss'
    //     // import '@app.dir/NSS/_3_items/itm_app_sys_notepad.nss'
    //     import '@app.dir/NSS/_3_items/itm_app_sys_osk.nss'
    //     // import '@app.dir/NSS/_3_items/itm_app_sys_powershell.nss'
    //     // import '@app.dir/NSS/_3_items/itm_app_sys_powershellise.nss'
    //     // import '@app.dir/NSS/_3_items/itm_app_sys_regedit.nss'
    //     separator()
    // }

    // [ ]
    menu(vis='@KEYS_MNU_VISIBILITY_0_DEFAULT' expanded=true) {
        import '@app.dir/NSS/_3_items/itm_app_vlc.nss'
        // import '@app.dir/NSS/_3_items/itm_app_3dsmax.nss'
        // import '@app.dir/NSS/_3_items/itm_app_blender.nss'
        // import '@app.dir/NSS/_3_items/itm_app_claude.nss'
        // import '@app.dir/NSS/_3_items/itm_app_comfyui.nss'
        // import '@app.dir/NSS/_3_items/itm_app_cursor.nss'
        // import '@app.dir/NSS/_3_items/itm_app_mypyinterface.nss'
        // import '@app.dir/NSS/_3_items/itm_app_sourcetree.nss'
        // import '@app.dir/NSS/_3_items/itm_app_sublimetext.nss'
        // import '@app.dir/NSS/_3_items/itm_app_yvonne.nss'
        import '@app.dir/NSS/_3_items/itm_app_sys_cmd.nss'
        import '@app.dir/NSS/_3_items/itm_app_bulkrenameutility.nss'
        import '@app.dir/NSS/_3_items/itm_app_everything.nss'
        import '@app.dir/NSS/_3_items/itm_app_qbittorrent.nss'
        separator()
    }

    // [ ]
    menu(vis='@KEYS_MNU_VISIBILITY_0_DEFAULT' expanded=true) {
        import '@app.dir/NSS/_5_menus/mnu_user_jorn_dirs.nss'
        // import '@app.dir/NSS/__5_menus/mnu_user_jorn_dirs_portal.nss'
        // import '@app.dir/NSS/__5_menus/mnu_user_jorn_dirs_sync.nss'
        // import '@app.dir/NSS/__5_menus/mnu_user_jorn_dirs_nas.nss'
        separator()
    }

    // [ ]
    menu(vis='@KEYS_MNU_VISIBILITY_0_DEFAULT' expanded=true) {
        import '@app.dir/NSS/_5_menus/mnu_user_jorn_dirs_common.nss'
        import '@app.dir/NSS/_5_menus/mnu_scratchpad.nss'
        separator()
    }

    // none OR ctrl+shift
    menu(vis='@KEYS_MNU_VISIBILITY_3_NONE_OR_CTRL_SHIFT' expanded=true) {
        import '@app.dir/NSS/_5_menus/mnu_user_jorn_scripts.nss'
        separator()
    }

    // none
    menu(vis='@KEYS_MNU_VISIBILITY_0_DEFAULT' expanded=true) {
        import '@app.dir/NSS/_3_items/itm_dir_jorn_scratch.nss'
        separator()
    }


    // [shift] || [ctrl+shift]
    menu(vis='@KEYS_MNU_VISIBILITY_3_SHIFT_OR_CTRL_SHIFT' expanded=true) {
        import '@app.dir/NSS/_5_menus/mnu_user_apps_shell.nss'
        separator()
        import '@app.dir/NSS/_5_menus/mnu_sys_debug_commands.nss'
        separator()
        import '@app.dir/NSS/_5_menus/wip_menus/mnu_Processes.nss'
        separator()
    }

    // [*]
    menu(vis='@KEYS_MNU_VISIBILITY_0_ALWAYS' expanded=true) {
        import '@app.dir/NSS/_3_items/itm_dir_sys_thispc.nss'
        import '@app.dir/NSS/_3_items/itm_dir_sys_desktop.nss'
        // import '@app.dir/NSS/_3_items/itm_action_sys_showdesktop.nss'
        import '@app.dir/NSS/_3_items/itm_app_sys_taskmanager.nss'
    }

}
