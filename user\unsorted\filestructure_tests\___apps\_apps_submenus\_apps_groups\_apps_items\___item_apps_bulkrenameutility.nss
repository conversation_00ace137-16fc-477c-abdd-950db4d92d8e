
/* item: singles/apps/bulkrenameutility */

//
$APP_BULKRENAMEUTILITY_DIR = '@app.dir\PORTAL\APPS\app_bulkrenameutility\exe\64-bit'
$APP_BULKRENAMEUTILITY_EXE = '@APP_BULKRENAMEUTILITY_DIR\Bulk Rename Utility.exe'
$APP_BULKRENAMEUTILITY_TIP = "..."+str.trimstart('@APP_BULKRENAMEUTILITY_EXE','@app.dir')

// Context: Explorer
item(
    title  = ":  &Bulk-Rename-Utility"
    keys   = "exe"
    type   = 'File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    args   = '"@sel.dir"'
    //
    image  = APP_BULKRENAMEUTILITY_EXE
    tip    = [APP_BULKRENAMEUTILITY_TIP,TIP3,1.0]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_BULKRENAMEUTILITY_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_BULKRENAMEUTILITY_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_BULKRENAMEUTILITY_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_BULKRENAMEUTILITY_DIR')),
    }
)
// Context: Taskbar
item(
    title  = ":  &Bulk-Rename-Utility"
    keys   = "exe"
    type   = 'Taskbar'
    args   = '@user.desktop'
    //
    image  = APP_BULKRENAMEUTILITY_EXE
    tip    = [APP_BULKRENAMEUTILITY_TIP,TIP3,1.0]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_BULKRENAMEUTILITY_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_BULKRENAMEUTILITY_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_BULKRENAMEUTILITY_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_BULKRENAMEUTILITY_DIR')),
    }
)
