// ----------------------------------------------------------------------------
// COLORS
// ----------------------------------------------------------------------------
$COLOR_SUBTLE = #4E4259
$COLOR_GREY   = #717482
$COLOR_WHITE  = #FFFFFF
$COLOR_BLUE   = #34B6FF
$COLOR_GREEN  = #39C65A
$COLOR_ORANGE = #FF904D
$COLOR_PURPLE = #A457FF
$COLOR_RED    = #FF1C1A

// ----------------------------------------------------------------------------
// MENU: NEW
// ----------------------------------------------------------------------------
// Parent Menu
$M_NEW_TITLE = "&New"
$M_NEW_ICON  = ["\uE11B", COLOR_WHITE]
$M_NEW_FILE_ICON  = ["\uE109", COLOR_WHITE]
//
// Icons
$ICO_APPLICATIONS_CMD            = "C:/Windows/System32/cmd.exe"

// Create Menu
menu(type='Desktop|Back.Dir' title=M_NEW_TITLE image=M_NEW_ICON) {
          item(title="*" keys="_______" cmd=io.file.create("_______", sys.datetime("y.m.d__H.M__\n")) image=["\uE109", COLOR_WHITE])
          separator
          item(title="Time_" keys=sys.datetime('"H.M__"') cmd=io.file.create(sys.datetime("H.M__")) image=["\uE109", COLOR_WHITE])
          item(title="Date_" keys=sys.datetime('"y.m.d__"') cmd=io.file.create(sys.datetime("y.m.d__")) image=["\uE109", COLOR_WHITE])
          item(title="DateTime_" keys=sys.datetime('"y.m.d__H.M__"') cmd=io.file.create(sys.datetime("y.m.d__H.M__")) image=["\uE109", COLOR_WHITE])

          separator
          item(title="Time" keys="_______" cmd=io.dir.create("_______") image=icon.new)
          separator
          item(title="Time_" keys=sys.datetime('"H.M__"') cmd=io.dir.create(sys.datetime("H.M__")) image=icon.new_folder)
          item(title="Date_" keys=sys.datetime('"y.m.d__"') cmd=io.dir.create(sys.datetime("y.m.d__")) image=icon.new_folder)
          item(title="DateTime_" keys=sys.datetime('"y.m.d__H.M__"') cmd=io.dir.create(sys.datetime("y.m.d__H.M__")) image=icon.new_folder)

          separator

          // item(title='JSON' cmd=io.file.create('@(dt).json', '[]'))
          // item(title="*" keys="_______" cmd=io.file.rename(io.file.create('_______', '_______')) image=icon.new_folder)
          // item(title="Time_" keys=sys.datetime('"H.M__"') cmd=io.dir.create(sys.datetime("H.M__")) image=icon.new_folder)
          // item(title="Date_" keys=sys.datetime('"y.m.d__"') cmd=io.dir.create(sys.datetime("y.m.d__")) image=icon.new_folder)
          // item(title="DateTime_" keys=sys.datetime('"y.m.d__H.M__"') cmd=io.dir.create(sys.datetime("y.m.d__H.M__")) image=icon.new_folder)
          // separator
          // item(title='GUID' keys=sys.datetime(str.guid(2)) cmd=io.dir.create(str.guid(2)))
     // }
// menu(type='back' mode="none" title="-> Custom Menus" pos="auto" image=icon.new_folder) {
//     item(title='New Folder (Current Date)' cmd=io.dir.create(sys.datetime("y.m.d")))
//     item(title='New Folder (Current Date)' cmd=io.dir.create(sys.datetime("y.m.d")))
// }

// menu(title='New File' image=icon.new_file menu=id.new.name pos=1 sep=sep.after)
// {
//     $dt = sys.datetime("ymdHMSs")
//     item(title='TXT' cmd=io.file.create('@(dt).txt', 'Hello World!'))
//     item(title='JSON' cmd=io.file.create('@(dt).json', '{}'))
//     item(title='HTML' cmd=io.file.create('@(dt).html', "<html>\n\t<head>\n\t</head>\n\t<body>Hello World!\n\t</body>\n</html>"))
// }

     // menu(title='New File' image=icon.new_file)
     // {
     //      var { dt = sys.datetime("y.m.d Kl.H.M")}
     //      item(title='TXT' cmd=io.file.create('@(dt).txt', ''))
     //      item(title='BAT' cmd=io.file.create('@(dt).bat', ''))
     //      separator
     //      item(title='PY' cmd=io.file.create('@(dt).py', ''))
     //      item(title='MS' cmd=io.file.create('@(dt).ms', ''))
     //      separator
     //      item(title='XML' cmd=io.file.create('@(dt).xml', '<root>Hello World!</root>'))
     //      item(title='JSON' cmd=io.file.create('@(dt).json', '[]'))
     //      item(title='HTML' cmd=io.file.create('@(dt).html', "<html>\n\t<head>\n\t</head>\n\t<body>Hello World!\n\t</body>\n</html>"))
     // }
    // separator
    // item(title = "PowerShell"
    //      image = ICO_APPLICATIONS_POWERSHELL
    //      cmd   = 'powershell.exe'
    // )
    // item(title = "PowerShell ISE"
    //      image = ICO_APPLICATIONS_POWERSHELL_ISE
    //      cmd   = 'powershell_ise.exe'
    // )
    // item(title = "Registry Editor"
    //      image = ICO_APPLICATIONS_REGEDIT
    //      cmd   = 'regedit.exe'
    // )
    // separator
    // item(title = "Paint"
    //      where = sys.ver.major >= 10
    //      image = ICO_APPLICATIONS_MSPAINT_11
    //      cmd   = 'mspaint'
    // )

    // item(title = "Notepad"
    //      image = ICO_APPLICATIONS_NOTEPAD
    //      cmd   = 'notepad.exe'
    // )
}


