
//
$PY_URLGENERATOR_DIR = '@user.desktop\my\flow\home\__GOTO__\Scripts\Python\Py_Cli_Utils\py__UrlGenerator'
$PY_URLGENERATOR_EXE = '@PY_URLGENERATOR_DIR\venv\Scripts\python.exe'
$PY_URLGENERATOR_APP = '@PY_URLGENERATOR_DIR\main.py'
//

// Context: Explorer
$PY_URLGENERATOR_EXPLORER = '-op "@sel.dir" --prompt'
item(
    title="&UrlGenerator"
    keys="py"
    type='File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    //
    image=[E17C,GREY] image-sel=[E17C,PURPLE]
    tip=['"@PY_URLGENERATOR_APP" @PY_URLGENERATOR_EXPLORER',TIP3,0.75]
    //
    admin=keys.rbutton()
    cmd=if(KEYS_EXE_OPEN_EXE,('"@PY_URLGENERATOR_EXE"'))
    args='"@PY_URLGENERATOR_APP" @PY_URLGENERATOR_EXPLORER'
    //
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@PY_URLGENERATOR_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@PY_URLGENERATOR_APP')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@PY_URLGENERATOR_DIR')),
    }
)
// Context: Taskbar
$PY_URLGENERATOR_TASKBAR = '-op "@user.desktop" --prompt'
item(
    title="&UrlGenerator"
    keys="py"
    type='Taskbar'
    //
    image=[E17C,GREY]
    image-sel=[E17C,PURPLE]
    //
    admin=keys.rbutton()
    cmd=if(KEYS_EXE_OPEN_EXE,('"@PY_URLGENERATOR_EXE"'))
    args='"@PY_URLGENERATOR_APP" @PY_URLGENERATOR_TASKBAR'
    tip=['"@PY_URLGENERATOR_APP" @PY_URLGENERATOR_TASKBAR',TIP3,0.75]
    //
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@PY_URLGENERATOR_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@PY_URLGENERATOR_APP')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@PY_URLGENERATOR_DIR')),
    }
)
