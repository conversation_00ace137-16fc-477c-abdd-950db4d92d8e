
/* item: singles/apps/notepad++ */

//
$APP_NOTEPADPLUSPLUS_DIR = '@app.dir\PORTAL\APPS\app_notepad++\exe'
$APP_NOTEPADPLUSPLUS_EXE = '@APP_NOTEPADPLUSPLUS_DIR\notepad++.exe'
$APP_NOTEPADPLUSPLUS_TIP = "..."+str.trimstart('@APP_NOTEPADPLUSPLUS_EXE','@app.dir')

// -> context: file
item(
    title  = ":  &Notepad++"
    keys   = "exe"
    type   = 'File'
    args   = '"@sel.file"'
    //
    image  = APP_NOTEPADPLUSPLUS_EXE
    tip    = [APP_NOTEPADPLUSPLUS_TIP,TIP3,1.0]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_NOTEPADPLUSPLUS_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_NOTEPADPLUSPLUS_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_NOTEPADPLUSPLUS_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_NOTEPADPLUSPLUS_DIR')),
    }
)
// context: directory
item(
    title  = ":  &Notepad++"
    keys   = "exe"
    type   = 'Desktop|Dir|Drive|Back.Dir|Back.Drive'
    args   = '"@sel.dir"'
    //
    image  = APP_NOTEPADPLUSPLUS_EXE
    tip    = [APP_NOTEPADPLUSPLUS_TIP,TIP3,1.0]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_NOTEPADPLUSPLUS_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_NOTEPADPLUSPLUS_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_NOTEPADPLUSPLUS_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_NOTEPADPLUSPLUS_DIR')),
    }
)
// Context: Taskbar
item(
    title  = ":  &Notepad++"
    keys   = "exe"
    type   = 'Taskbar'
    args   = ''
    //
    image  = APP_NOTEPADPLUSPLUS_EXE
    tip    = [APP_NOTEPADPLUSPLUS_TIP,TIP3,1.0]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_NOTEPADPLUSPLUS_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_NOTEPADPLUSPLUS_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_NOTEPADPLUSPLUS_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_NOTEPADPLUSPLUS_DIR')),
    }
)
