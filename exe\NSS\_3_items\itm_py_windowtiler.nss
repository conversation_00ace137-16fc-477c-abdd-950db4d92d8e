
//
$PY_WINDOWTILER_DIR = '@user.desktop\my\flow\home\__GOTO__\Scripts\Python\Py_Cli_Utils\py__WindowTiler'
$PY_WINDOWTILER_EXE = '@PY_WINDOWTILER_DIR\venv\Scripts\python.exe'
$PY_WINDOWTILER_APP = '@PY_WINDOWTILER_DIR\src\window_tiler.py'
//

// Context: Explorer
$PY_WINDOWTILER_EXPLORER = '-op "@sel.dir" --prompt'
item(
    title="&WindowTiler"
    keys="py"
    type='File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    //
    image=[E17C,GREY] image-sel=[E17C,PURPLE]
    tip=['"@PY_WINDOWTILER_APP" @PY_WINDOWTILER_EXPLORER',TIP3,0.75]
    //
    admin=keys.rbutton()
    cmd=if(KEYS_EXE_OPEN_EXE,('"@PY_WINDOWTILER_EXE"'))
    args='"@PY_WINDOWTILER_APP" @PY_WINDOWTILER_EXPLORER'
    //
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@PY_WINDOWTILER_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@PY_WINDOWTILER_APP')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@PY_WINDOWTILER_DIR')),
    }
)
// Context: Taskbar
$PY_WINDOWTILER_TASKBAR = '-op "@user.desktop" --prompt'
item(
    title="&WindowTiler"
    keys="py"
    type='Taskbar'
    //
    image=[E17C,GREY]
    image-sel=[E17C,PURPLE]
    //
    admin=keys.rbutton()
    cmd=if(KEYS_EXE_OPEN_EXE,('"@PY_WINDOWTILER_EXE"'))
    args='"@PY_WINDOWTILER_APP" @PY_WINDOWTILER_TASKBAR'
    tip=['"@PY_WINDOWTILER_APP" @PY_WINDOWTILER_TASKBAR',TIP3,0.75]
    //
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@PY_WINDOWTILER_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@PY_WINDOWTILER_APP')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@PY_WINDOWTILER_DIR')),
    }
)

