// win11
item(mode="single" type='file' find=".exe|.msi|.bat|.VBS|.ps1" title='Firewall Block' image=\uE13A
    admin cmd='powershell.exe'
    args='New-NetFirewallRule -DisplayName “Block @sel.name“ -Program “@sel“ -Direction Outbound -Action Block
    New-NetFirewallRule -DisplayName “Block @sel.name“ -Program “@sel“ -Direction Inbound -Action Block'
)

item(mode="single" type='file' find=".exe|.msi|.bat|.VBS|.ps1" title='Firewall Allow' image=\uE13B
    admin cmd='powershell.exe'
    args='New-NetFirewallRule -DisplayName “Allow @sel.name“ -Program “@sel“ -Direction Outbound -Action Allow
    New-NetFirewallRule -DisplayName “Allow @sel.name“ -Program “@sel“ -Direction Inbound -Action Allow'
)


// win10
item(mode="single" type='file' find=".exe|.msi|.bat|.VBS|.ps1" title='Firewall Block' image=\uE13A
  cmd='powershell.exe'
  args='
    New-NetFirewallRule -DisplayName “Block @sel.name“ -Program “@sel“
    -Direction Outbound -Action Block -Profile Domain, Private
    New-NetFirewallRule -DisplayName “Block @sel.name“ -Program “@sel“
    -Direction Inbound -Action Block -Profile Domain, Private'
admin)

item(mode="single" type='file' find=".exe|.msi|.bat|.VBS|.ps1" title='Firewall Allow' image=\uE13B
  cmd='powershell.exe'
  args='
    New-NetFirewallRule -DisplayName “Allow @sel.name“ -Program “@sel“
    -Direction Outbound -Action Allow -Profile Domain, Private
    New-NetFirewallRule -DisplayName “Allow @sel.name“ -Program “@sel“
    -Direction Inbound -Action Allow -Profile Domain, Private'
admin)