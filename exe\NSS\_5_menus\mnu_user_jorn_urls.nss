
//
// menu(title="&URLs" type='Taskbar|Desktop' image=[E25E,SOFT] image-sel=[E25E,HOVER] sep='None') {
// menu(title='Goto : &Urls@"\t"Jorn' type='Taskbar|Desktop' image=[E25E,SOFT] image-sel=[E25E,HOVER] sep='Bottom') {
// menu(title='Goto : &Urls@"\t"Jorn' type='Taskbar|Desktop' image=[E09B,SOFT] image-sel=[E09B,HOVER] sep='Bottom') {
menu(title='&URLs@"\t"' type='Taskbar|Desktop' image=[E09B,WHITE1] image-sel=[E09B,HOVER] sep='Bottom') {

    // GPTs
    // =======================================================
    item(column vis='Static' image=[E00A,DARK])
    item(title="Gpts" keys="" image=[E25E,PURPLE] tip=['@KEYS_URL_TIP',TIP1,0.0] sep='Both' )

    //
    $URL_CHATGPT_4O1PRO = ['ChatGPT: gpt-o1-pro@"\t"OpenAI',"https://chatgpt.com/?model=o1-pro"]
    item(
        title="&"+'@URL_CHATGPT_4O1PRO[0]'
        image=[E09B,GREEN2]
        image-sel=[E09B,PURPLE]
        tip=['@URL_CHATGPT_4O1PRO[1]',TIP3,0.0]
        commands{
            cmd=if('@KEYS_URL_COPY',clipboard.set('@URL_CHATGPT_4O1PRO[1]')),
            cmd=if('@KEYS_URL_OPEN',('@URL_CHATGPT_4O1PRO[1]')),
        }
    )
    $URL_CHATGPT_4O1 = ['ChatGPT: gpt-o1@"\t"OpenAI',"https://chatgpt.com/?model=o1"]
    item(
        title="&"+'@URL_CHATGPT_4O1[0]'
        image=[E09B,GREEN2]
        image-sel=[E09B,PURPLE]
        tip=['@URL_CHATGPT_4O1[1]',TIP3,0.0]
        commands{
            cmd=if('@KEYS_URL_COPY',clipboard.set('@URL_CHATGPT_4O1[1]')),
            cmd=if('@KEYS_URL_OPEN',('@URL_CHATGPT_4O1[1]')),
        }
    )
    $URL_CHATGPT_4O1P = ['ChatGPT: gpt-o1-preview@"\t"OpenAI',"https://chatgpt.com/?model=o1-preview"]
    item(
        title="&"+'@URL_CHATGPT_4O1P[0]'
        image=[E09B,GREEN2]
        image-sel=[E09B,PURPLE]
        tip=['@URL_CHATGPT_4O1P[1]',TIP3,0.0]
        commands{
            cmd=if('@KEYS_URL_COPY',clipboard.set('@URL_CHATGPT_4O1P[1]')),
            cmd=if('@KEYS_URL_OPEN',('@URL_CHATGPT_4O1P[1]')),
        }
    )
    $URL_CHATGPT_4O1M = ['ChatGPT: gpt-o1-mini@"\t"OpenAI',"https://chatgpt.com/?model=o1-mini"]
    item(
        title="&"+'@URL_CHATGPT_4O1M[0]'
        image=[E09B,GREEN2]
        image-sel=[E09B,PURPLE]
        tip=['@URL_CHATGPT_4O1M[1]',TIP3,0.0]
        commands{
            cmd=if('@KEYS_URL_COPY',clipboard.set('@URL_CHATGPT_4O1M[1]')),
            cmd=if('@KEYS_URL_OPEN',('@URL_CHATGPT_4O1M[1]')),
        }
    )
    $URL_CHATGPT_4O = ['ChatGPT: gpt-4o@"\t"OpenAI',"https://chatgpt.com/?model=gpt-4o"]
    item(
        title="&"+'@URL_CHATGPT_4O[0]'
        image=[E09B,GREEN2]
        image-sel=[E09B,PURPLE]
        tip=['@URL_CHATGPT_4O[1]',TIP3,0.0]
        commands{
            cmd=if('@KEYS_URL_COPY',clipboard.set('@URL_CHATGPT_4O[1]')),
            cmd=if('@KEYS_URL_OPEN',('@URL_CHATGPT_4O[1]')),
        }
    )
    separator()
    // ---

    //
    $URL_CHATGPT_PY311 = ['ChatGPT: py-3.11.3@"\t"OpenAI',"https://chatgpt.com/g/g-XWPgfkWdF-py-3-11-3-on-windows10-11"]
    item(
        title="&"+'@URL_CHATGPT_PY311[0]'
        image=[E09B,GREEN]
        image-sel=[E09B,PURPLE]
        tip=['@URL_CHATGPT_PY311[1]',TIP3,0.0]
        commands{
            cmd=if('@KEYS_URL_COPY',clipboard.set('@URL_CHATGPT_PY311[1]')),
            cmd=if('@KEYS_URL_OPEN',('@URL_CHATGPT_PY311[1]')),
        }
    )
    $URL_CHATGPT_ARTSNOB = ['ChatGPT: art-snob@"\t"OpenAI',"https://chatgpt.com/g/g-plIy0RAhw-art-snob"]
    item(
        title="&"+'@URL_CHATGPT_ARTSNOB[0]'
        image=[E09B,GREEN]
        image-sel=[E09B,PURPLE]
        tip=['@URL_CHATGPT_ARTSNOB[1]',TIP3,0.0]
        commands{
            cmd=if('@KEYS_URL_COPY',clipboard.set('@URL_CHATGPT_ARTSNOB[1]')),
            cmd=if('@KEYS_URL_OPEN',('@URL_CHATGPT_ARTSNOB[1]')),
        }
    )
    $URL_FLUX1AI = ['ChatGPT: FLUX Image Prompt Enhancer@"\t"OpenAI',"https://chatgpt.com/g/g-cNUJKxr8Y-flux-image-prompt-enhancer"]
    item(
        title="&"+'@URL_FLUX1AI[0]'
        image=[E09B,GREEN]
        image-sel=[E09B,PURPLE]
        tip=['@URL_FLUX1AI[1]',TIP3,0.0]
        commands{
            cmd=if('@KEYS_URL_COPY',clipboard.set('@URL_FLUX1AI[1]')),
            cmd=if('@KEYS_URL_OPEN',('@URL_FLUX1AI[1]')),
        }
    )
    separator()
    // ---

    //
    $URL_CLAUDE = ['Claude@"\t"Anthropic',"https://claude.ai/"]
    item(
        title="&"+'@URL_CLAUDE[0]'
        image=[E09B,BLUE]
        image-sel=[E09B,PURPLE]
        tip=['@URL_CLAUDE[1]',TIP3,0.0]
        commands{
            cmd=if('@KEYS_URL_COPY',clipboard.set('@URL_CLAUDE[1]')),
            cmd=if('@KEYS_URL_OPEN',('@URL_CLAUDE[1]')),
        }
    )
    separator()
    // ---

    //
    $URL_PERPLEXITY = ['Perplexity@"\t"',"https://www.perplexity.ai/"]
    item(
        title="&"+'@URL_PERPLEXITY[0]'
        image=[E09B,BLUE2]
        image-sel=[E09B,PURPLE]
        tip=['@URL_PERPLEXITY[1]',TIP3,0.0]
        commands{
            cmd=if('@KEYS_URL_COPY',clipboard.set('@URL_PERPLEXITY[1]')),
            cmd=if('@KEYS_URL_OPEN',('@URL_PERPLEXITY[1]')),
        }
    )
    separator()
    // ---

    //
    $URL_GEMINI = ['Gemini@"\t"Google',"https://gemini.google.com/app"]
    item(
        title="&"+'@URL_GEMINI[0]'
        image=[E09B,GREY]
        image-sel=[E09B,PURPLE]
        tip=['@URL_GEMINI[1]',TIP3,0.0]
        commands{
            cmd=if('@KEYS_URL_COPY',clipboard.set('@URL_GEMINI[1]')),
            cmd=if('@KEYS_URL_OPEN',('@URL_GEMINI[1]')),
        }
    )
    separator()
    // ---

    //
    $URL_DREAMSTUDIO = ['Dreamstudio@"\t"',"https://beta.dreamstudio.ai/generate"]
    item(
        title="&"+'@URL_DREAMSTUDIO[0]'
        image=[E09B,RED_SOFT]
        image-sel=[E09B,PURPLE]
        tip=['@URL_DREAMSTUDIO[1]',TIP3,0.0]
        commands{
            cmd=if('@KEYS_URL_COPY',clipboard.set('@URL_DREAMSTUDIO[1]')),
            cmd=if('@KEYS_URL_OPEN',('@URL_DREAMSTUDIO[1]')),
        }
    )
    separator()
    // ---

    // Websites
    // =======================================================
    item(column vis='Static' image=[E00A,DARK])
    item(title="Websites" keys="" image=[E25E,PURPLE] tip=['@KEYS_URL_TIP',TIP1,0.0] sep='Both' )

    //
    $URL_IPTORRENTS = ['IPTorrents@"\t"',"https://iptorrents.me/t"]
    item(
        title="&"+'@URL_IPTORRENTS[0]'
        image=[E09B,GREEN2]
        image-sel=[E09B,PURPLE]
        tip=['@URL_IPTORRENTS[1]',TIP3,0.0]
        commands{
            cmd=if('@KEYS_URL_COPY',clipboard.set('@URL_IPTORRENTS[1]')),
            cmd=if('@KEYS_URL_OPEN',('@URL_IPTORRENTS[1]')),
        }
    )
    $URL_IMDB = ['IMDb@"\t"',"https://imdb.com/what-to-watch/?ref_=nv_watch"]
    item(
        title="&"+'@URL_IMDB[0]'
        image=[E09B,GREEN2]
        image-sel=[E09B,PURPLE]
        tip=['@URL_IMDB[1]',TIP3,0.0]
        commands{
            cmd=if('@KEYS_URL_COPY',clipboard.set('@URL_IMDB[1]')),
            cmd=if('@KEYS_URL_OPEN',('@URL_IMDB[1]')),
        }
    )
    $URL_NETFLIX = ['Netflix@"\t"',"https://netflix.com/"]
    item(
        title="&"+'@URL_NETFLIX[0]'
        image=[E09B,GREEN2]
        image-sel=[E09B,PURPLE]
        tip=['@URL_NETFLIX[1]',TIP3,0.0]
        commands{
            cmd=if('@KEYS_URL_COPY',clipboard.set('@URL_NETFLIX[1]')),
            cmd=if('@KEYS_URL_OPEN',('@URL_NETFLIX[1]')),
        }
    )
    $URL_SIDEREEL = ['SideReel@"\t"',"https://sidereel.com/"]
    item(
        title="&"+'@URL_SIDEREEL[0]'
        image=[E09B,GREEN2]
        image-sel=[E09B,PURPLE]
        tip=['@URL_SIDEREEL[1]',TIP3,0.0]
        commands{
            cmd=if('@KEYS_URL_COPY',clipboard.set('@URL_SIDEREEL[1]')),
            cmd=if('@KEYS_URL_OPEN',('@URL_SIDEREEL[1]')),
        }
    )
    $URL_YOUTUBE = ['YouTube@"\t"',"https://youtube.com/"]
    item(
        title="&"+'@URL_YOUTUBE[0]'
        image=[E09B,GREEN2]
        image-sel=[E09B,PURPLE]
        tip=['@URL_YOUTUBE[1]',TIP3,0.0]
        commands{
            cmd=if('@KEYS_URL_COPY',clipboard.set('@URL_YOUTUBE[1]')),
            cmd=if('@KEYS_URL_OPEN',('@URL_YOUTUBE[1]')),
        }
    )
    separator()
    // ---

}