


// ->
$DIR_APPS_SYSINTERNAL = '_groups\___user\apps\itm_procexp.nss'
$DIR_APPS_SYSINTERNAL = '@app.dir\PORTAL\APPS\grp_sysinternalssuite'
//
$DIR_AUTORUNS = '@DIR_APPS_SYSINTERNAL\app_autoruns\exe'
$EXE_AUTORUNS = '@DIR_APPS_SYSINTERNAL\app_autoruns\exe\Autoruns64.exe'
//
$DIR_PROCEXP = '@DIR_APPS_SYSINTERNAL\app_procexp\exe'
$EXE_PROCEXP = '@DIR_APPS_SYSINTERNAL\app_procexp\exe\procexp64.exe'
//
$DIR_TCPVIEW = '@DIR_APPS_SYSINTERNAL\app_tcpview\exe'
$EXE_TCPVIEW = '@DIR_APPS_SYSINTERNAL\app_tcpview\exe\tcpview64.exe'

// =============================================================================
// -> SECTION-HEADER
item(title="Sysinternals"
    keys=""
    image=[E253,DARK]
    tip=['@KEYS_EXE_TIP'+"\n"+'@DIR_APPS_SYSINTERNAL',TIP3,0.0]
    commands{
        cmd=if(KEYS_DIR_COPY,clipboard.set('@DIR_APPS_SYSINTERNAL')),
        cmd=if(KEYS_DIR_OPEN,('@DIR_APPS_SYSINTERNAL')),
    }
    sep='Both'
)
// =============================================================================


// -> AUTORUNS
item(title="&Autoruns64"
    keys="exe"
    type='Taskbar|File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    image='@EXE_AUTORUNS'
    tip=['@EXE_AUTORUNS',TIP3,0.0]
    admin=keys.rbutton()
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@DIR_AUTORUNS')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@EXE_AUTORUNS')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@DIR_AUTORUNS')),
        cmd=if(KEYS_EXE_OPEN_EXE,('@EXE_AUTORUNS')),
    }
    window='Visible'
    sep='None'
)

// -> PROCESS EXPLORER
item(title="&Procexp64"
    keys="exe"
    type='Taskbar|File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    image='@EXE_PROCEXP'
    tip=['@EXE_PROCEXP',TIP3,0.0]
    admin=keys.rbutton()
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@DIR_PROCEXP')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@EXE_PROCEXP')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@DIR_PROCEXP')),
        cmd=if(KEYS_EXE_OPEN_EXE,('@EXE_PROCEXP')),
    }
    window='Visible'
    sep='None'
)


// -> PROCESS EXPLORER
item(title="&Tcpview64"
    keys="exe"
    type='Taskbar|File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    image='@EXE_TCPVIEW'
    tip=['@EXE_TCPVIEW',TIP3,0.0]
    admin=keys.rbutton()
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@DIR_TCPVIEW')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@EXE_TCPVIEW')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@DIR_TCPVIEW')),
        cmd=if(KEYS_EXE_OPEN_EXE,('@EXE_TCPVIEW')),
    }
    window='Visible'
    sep='None'
)


