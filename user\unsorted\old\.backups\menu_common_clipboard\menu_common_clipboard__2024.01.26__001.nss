// ----------------------------------------------------------------------------
// COLORS
// ----------------------------------------------------------------------------
$clr_subtle = #4e4259
$clr_grey   = #717482
$clr_white  = #ffffff
$clr_blue   = #34b6ff
$clr_green  = #39c65a
$clr_orange = #ff904d
$clr_purple = #a457ff
$clr_red    = #ff1c1a

// ----------------------------------------------------------------------------
// MENU: COMMON DIRECTORIES
// ----------------------------------------------------------------------------
// Menu
$m_common_str_title = "&Clipboard"
$m_common_str_mode  = "none"
$m_common_str_icon  = ["\uE11B", clr_blue]
//
// Items
$i_common_str_title_filepath    = "Copy Filepath"            + "\t 1"
$i_common_str_title_location    = "Copy Path"                + "\t 2"
$i_common_str_title_filename    = "Copy Filename"            + "\t 3"
$i_common_str_title_basename    = "Copy Name"                + "\t 4"
$i_common_str_title_extension   = "Copy File Extension"      + "\t 5"
$i_common_str_title_content     = "Copy Content"             + "\t 6"
$i_common_str_title_dircontents = "Copy Directory Contents"  + "\t 7"

//
$i_common_str_icon_filepath    = ["\uE10E", clr_green]
$i_common_str_icon_location    = ["\uE10E", clr_green]
$i_common_str_icon_filename    = ["\uE10E", clr_blue]
$i_common_str_icon_basename    = ["\uE10E", clr_blue]
$i_common_str_icon_extension   = ["\uE10E", clr_orange]
$i_common_str_icon_content     = ["\uE1A3", clr_purple]
$i_common_str_icon_dircontents = ["\uE1A3", clr_purple]
//
$i_cmds_copy_filepath    = ['powershell', '-Command @sel("\\\"",",") | % { [System.IO.Path]::GetFullPath($_) } | Set-Clipboard']
$i_cmds_copy_location    = ['powershell', '-Command @sel("\\\"",",") | % { [System.IO.Path]::GetDirectoryName($_) } | Set-Clipboard']
$i_cmds_copy_filename    = ['powershell', '-Command @sel("\\\"",",") | % { [System.IO.Path]::GetFileName($_) } | Set-Clipboard']
$i_cmds_copy_basename    = ['powershell', '-Command @sel("\\\"",",") | % { [System.IO.Path]::GetFileNameWithoutExtension($_) } | Set-Clipboard']
$i_cmds_copy_extension   = ['powershell', '-Command @sel("\\\"",",") | % { [System.IO.Path]::GetExtension($_) } | Set-Clipboard']
$i_cmds_copy_content     = ['powershell', '-Command @sel("\\\"",",") | % { Get-Content $_ -Raw } | Set-Clipboard']
$i_cmds_copy_dircontents = ['powershell', '-Command @sel("\\\"",",") | % { Get-ChildItem $_ -Recurse | Select-Object -ExpandProperty FullName } | Set-Clipboard']
//
// Create Menu
// menu(type='~taskbar|file|dir' mode="none" title=m_common_str_title mode=m_common_str_mode image=m_common_str_icon) {
menu(mode='multiple' type='file|dir' title=m_common_str_title image=m_common_str_icon) {
    item(type='file' title=i_common_str_title_filepath cmd=i_cmds_copy_filepath[0] args=i_cmds_copy_filepath[1] window=hidden image=i_common_str_icon_filepath)
    item(type='file|dir' title=i_common_str_title_location cmd=i_cmds_copy_location[0] args=i_cmds_copy_location[1] window=hidden image=i_common_str_icon_location)
    separator
    item(type='file' title=i_common_str_title_filename cmd=i_cmds_copy_filename[0] args=i_cmds_copy_filename[1] window=hidden image=i_common_str_icon_filename)
    item(type='file|dir' title=i_common_str_title_basename cmd=i_cmds_copy_basename[0] args=i_cmds_copy_basename[1] window=hidden image=i_common_str_icon_basename)
    separator
    item(type='file' title=i_common_str_title_extension cmd=i_cmds_copy_extension[0] args=i_cmds_copy_extension[1] window=hidden image=i_common_str_icon_extension)
    separator
    item(type='file' title=i_common_str_title_content  cmd=i_cmds_copy_content[0]  args=i_cmds_copy_content[1]  window=hidden image=i_common_str_icon_content)

    item(type='dir|back.dir' title=i_common_str_title_dircontents cmd=i_cmds_copy_dircontents[0] args=i_cmds_copy_dircontents[1] window=hidden image=i_common_str_icon_dircontents)
}