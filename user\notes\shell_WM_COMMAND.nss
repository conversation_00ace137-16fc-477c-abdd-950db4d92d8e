// # =============================================================================
// # 11.04.2024 - Kl.13:43:
// https://www.autohotkey.com/boards/viewtopic.php?t=27824
/*
	==================================================

	from AutoHotkey-master\Source\resources\resource.h (AutoHotkey v1.0.48.05 'AutoHotkey Basic' source code):
	GitHub - AutoHotkey/AutoHotkey: AutoHotkey is a powerful and easy to use scripting language for desktop automation on Windows.
	https://github.com/AutoHotkey/AutoHotkey

	// Since WM_COMMAND IDs must be shared among all menus and controls, they are carefully conserved,
	// especially since there are only 65,535 possible IDs. In addition, they are assigned to ranges
	// to minimize the need that they will need to be changed in the future (changing the ID of a main
	// menu item, tray menu item, or a user-defined menu item [by way of increasing MAX_CONTROLS_PER_GUI]
	// is bad because some scripts might be using PostMessage/SendMessage to automate AutoHotkey itself).
	// For this reason, the following ranges are reserved:
	// 0: unused (possibly special in some contexts)
	// 1: IDOK
	// 2: IDCANCEL
	// 3 to 1002: GUI window control IDs (these IDs must be unique only within their parent, not across all GUI windows)
	// 1003 to 65299: User Defined Menu IDs
	// 65300 to 65399: Standard tray menu items.
	// 65400 to 65534: main menu items (might be best to leave 65535 unused in case it ever has special meaning)

	IDs 65300-65307 and IDs 65400-65413:

	ID_TRAY_OPEN := 65300
	ID_FILE_RELOADSCRIPT := 65400 ;ID_TRAY_RELOADSCRIPT := 65303
	ID_FILE_EDITSCRIPT := 65401 ;ID_TRAY_EDITSCRIPT := 65304
	ID_FILE_WINDOWSPY := 65402 ;ID_TRAY_WINDOWSPY := 65302
	ID_FILE_PAUSE := 65403 ;ID_TRAY_PAUSE := 65306
	ID_FILE_SUSPEND := 65404 ;ID_TRAY_SUSPEND := 65305
	ID_FILE_EXIT := 65405 ;ID_TRAY_EXIT := 65307
	ID_VIEW_LINES := 65406
	ID_VIEW_VARIABLES := 65407
	ID_VIEW_HOTKEYS := 65408
	ID_VIEW_KEYHISTORY := 65409
	ID_VIEW_REFRESH := 65410
	ID_HELP_USERMANUAL := 65411 ;ID_TRAY_HELP := 65301
	ID_HELP_WEBSITE := 65412

	==================================================

	From:
	Autohotkey-scripts-.ahk/AHKControl.ahk at master · Drugoy/Autohotkey-scripts-.ahk · GitHub
	https://github.com/Drugoy/Autohotkey-sc ... ontrol.ahk

	formerly at: autohotkey.net/~Lexikos/AHKControl/AHKControl.ahk

	Cmd_Open = 65300
	;-
	Cmd_Reload = 65400
	Cmd_Edit = 65401
	Cmd_Pause = 65403
	Cmd_Suspend = 65404
	;-
	Cmd_ViewLines = 65406
	Cmd_ViewVariables = 65407
	Cmd_ViewHotkeys = 65408
	Cmd_ViewKeyHistory = 65409
	;-
	Cmd_Exit = 65405

	missing from AHKControl.ahk list:
	ID_FILE_WINDOWSPY := 65402 ;ID_TRAY_WINDOWSPY := 65302
	ID_VIEW_REFRESH := 65410
	ID_HELP_USERMANUAL := 65411 ;ID_TRAY_HELP := 65301
	ID_HELP_WEBSITE := 65412

	==================================================

	Note: files inside source code zips/exes seem to differ:
	GitHub - AutoHotkey/AutoHotkey: AutoHotkey is a powerful and easy to use scripting language for desktop automation on Windows.
	https://github.com/AutoHotkey/AutoHotkey
	Index of /download/1.0
	https://autohotkey.com/download/1.0/

	Note: the following opens the script with notepad.exe by default:
	ID_FILE_EDITSCRIPT := 65401 ;ID_TRAY_EDITSCRIPT := 65304
	this can be changed at:
	HKEY_CLASSES_ROOT\AutoHotkeyScript\Shell\Edit\Command

	Note: the following toggle rather than set pause/suspend modes:
	ID_FILE_PAUSE := 65403 ;ID_TRAY_PAUSE := 65306
	ID_FILE_SUSPEND := 65404 ;ID_TRAY_SUSPEND := 65305

	==================================================
*/


// # =============================================================================
// # 11.04.2024 - Kl.00:49:
// https://wiki.winehq.org/List_Of_Windows_Messages
// https://www.autoitscript.com/autoit3/docs/appendix/WinMsgCodes.htm

// # =============================================================================
// # 10.04.2024 - Kl.19:52: WM_COMMAND
// item(title="61744" cmd=window.command(61744))
// cmd=window.command(28691)  item(title='File Properties')
//
// https://github.com/Open-Shell/Open-Shell-Menu/blob/master/Src/ClassicExplorer/ExplorerBand.cpp


// Context: Tree View
//
item(cmd=window.command(41025) title='41025: Tree View: Cut')
item(cmd=window.command(41026) title='41026: Tree View: Copy')
item(cmd=window.command(41027) title='41027: Tree View: Paste')
item(cmd=window.command(40995) title='40995: Tree View: Delete')

// Context: Shell List View
//
item(cmd=window.command(28696) title='28696: Shell List View: Cut')
item(cmd=window.command(28697) title='28697: Shell List View: Copy')
item(cmd=window.command(28698) title='28698: Shell List View: Paste')
item(cmd=window.command(28689) title='28689: Shell List View: Delete')
item(cmd=window.command(28691) title='28691: Shell List View: Properties')
item(cmd=window.command(28702) title='28702: Shell List View: Copy To')
item(cmd=window.command(28703) title='28703: Shell List View: Move To')
//
item(cmd=window.command(31492)  title='31492: Sort By Name')
item(cmd=window.command(31493)  title='31493: Sort By Date')
item(cmd=window.command(31494)  title='31494: Sort By Type')
item(cmd=window.command(31495)  title='31495: Sort By Size')
//



// Context: Shell
//
item(cmd=window.command(28699) title='28699: Undo')
item(cmd=window.command(28704) title='28704: Redo')
item(cmd=window.command(28700) title='28700: Paste Shortcut')
//
item(cmd=window.command(41089) title='41089: Map Drive')
item(cmd=window.command(41090) title='41090: Disconnect')
item(cmd=window.command(28722) title='28722: Customize Folder')
item(cmd=window.command(41251) title='41251: Folder Options')
//
item(cmd=window.command(28748) title='28748: View: Tiles')
item(cmd=window.command(28747) title='28747: View: Details')
item(cmd=window.command(28753) title='28753: View: List')
item(cmd=window.command(28754) title='28754: View: Content')
item(cmd=window.command(28752) title='28752: View: Icons (Small)')
item(cmd=window.command(28750) title='28750: View: Icons (Medium)')
item(cmd=window.command(28751) title='28751: View: Icons (Large)')
item(cmd=window.command(28749) title='28749: View: Icons (Extra Large)')


// Context: Taskbar
//
item(cmd=window.command(401)  title='401: Taskbar: DisplayRunDialog')
item(cmd=window.command(402)  title='402: Taskbar: DisplayLogoffDialog')
item(cmd=window.command(407)  title='407: Taskbar: ShowDesktop')
item(cmd=window.command(408)  title='408: Taskbar: ShowDateTimeDialog')
item(cmd=window.command(413)  title='413: Taskbar: ShowTaskbarPrps')
item(cmd=window.command(415)  title='415: Taskbar: Minimize All')
item(cmd=window.command(416)  title='416: Taskbar: Maximize All')
item(cmd=window.command(419)  title='419: Taskbar: ShowDesktop2')
item(cmd=window.command(420)  title='420: Taskbar: ShowTaskMngr')
item(cmd=window.command(421)  title='421: Taskbar: TaskBrCustomizeNtfy')
item(cmd=window.command(424)  title='424: Taskbar: LockTaskbar')
item(cmd=window.command(503)  title='503: Taskbar: HelpAndSuppCenter')
item(cmd=window.command(506)  title='506: Taskbar: TurnOffCompDialog')


// Unknown
// //
// item(cmd=window.command(16)  title='16: Close')
// item(cmd=window.command(28705)  title='28705: Select All')
// item(cmd=window.command(28706)  title='28706: Invert Selection')
// item(cmd=window.command(28730)  title='28730: Refresh')
// item(cmd=window.command(28753)  title='28753: Open in New Window')
// item(cmd=window.command(28754)  title='28754: Open in New Tab')
// item(cmd=window.command(41058)  title='41058: Rename')
// item(cmd=window.command(4259497121)  title='4259497121: Folder')
// //
// item(cmd=window.command(305)  title='305: DisplayStartupMenu')
// item(cmd=window.command(403)  title='403: ArrangeCascade')
// item(cmd=window.command(404)  title='404: ArrangeTileHrz')
// item(cmd=window.command(405)  title='405: ArrangeTileVrt')
// item(cmd=window.command(41093)  title='41093: FindFilesDialog')
// item(cmd=window.command(41094)  title='41094: FindComputers')
// item(cmd=window.command(505)  title='505: ControlPanel')
// item(cmd=window.command(510)  title='510: PrintersAndFaxesDialog')


