
//
$PY_DIRTREEGENERATOR_DIR = '@user.desktop\my\flow\home\__GOTO__\Scripts\Python\Py_Cli_Utils\py__DirTreeGenerator'
$PY_DIRTREEGENERATOR_EXE = '@PY_DIRTREEGENERATOR_DIR\venv\Scripts\python.exe'
$PY_DIRTREEGENERATOR_APP = '@PY_DIRTREEGENERATOR_DIR\main.py'
//

// Context: Explorer
$PY_DIRTREEGENERATOR_EXPLORER = '-i "@sel.dir" -op "@sel.dir" -of "@sel.dir.name".dirtree.md --prompt'
item(
    title="&DirTreeGenerator"
    keys="py"
    type='File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    //
    image=[E17C,GREY] image-sel=[E17C,PURPLE]
    tip=['"@PY_DIRTREEGENERATOR_APP" @PY_DIRTREEGENERATOR_EXPLORER',TIP3,0.75]
    //
    admin=keys.rbutton()
    cmd=if(KEYS_EXE_OPEN_EXE,('"@PY_DIRTREEGENERATOR_EXE"'))
    args='"@PY_DIRTREEGENERATOR_APP" @PY_DIRTREEGENERATOR_EXPLORER'
    //
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@PY_DIRTREEGENERATOR_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@PY_DIRTREEGENERATOR_APP')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@PY_DIRTREEGENERATOR_DIR')),
    }
)
// Context: Taskbar
$PY_DIRTREEGENERATOR_TASKBAR = '-i "@user.desktop" -op "@user.desktop" -of "output.md" --prompt'
item(
    title="&DirTreeGenerator"
    keys="py"
    type='Taskbar'
    //
    image=[E17C,GREY]
    image-sel=[E17C,PURPLE]
    //
    admin=keys.rbutton()
    cmd=if(KEYS_EXE_OPEN_EXE,('"@PY_DIRTREEGENERATOR_EXE"'))
    args='"@PY_DIRTREEGENERATOR_APP" @PY_DIRTREEGENERATOR_TASKBAR'
    tip=['"@PY_DIRTREEGENERATOR_APP" @PY_DIRTREEGENERATOR_TASKBAR',TIP3,0.75]
    //
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@PY_DIRTREEGENERATOR_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@PY_DIRTREEGENERATOR_APP')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@PY_DIRTREEGENERATOR_DIR')),
    }
)
