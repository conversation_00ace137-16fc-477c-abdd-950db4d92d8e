
/* cmd-ps='@variable_name' */

// -> open powershell prompt in dirs
$ps1_terminal_dsk = '-noexit -command Set-Location -Path "@user.desktop\."'
$ps1_terminal_cwd = '-noexit -command Set-Location -Path "@sel.dir\."'
$ps1_terminal_app = '-noexit -command Set-Location -Path "@app.dir\."'




// OLD
// $ps1_fnGetOneDrive = 'Get-Childitem -Path Env: | where Name -match Onedrive'
// $ps1_fnGetOneDrive = '(Get-ItemProperty -Path "HKCU:\Software\Microsoft\OneDrive" -Name "UserFolder").UserFolder'
// $ps1_fnGetOneDrive  = '-Command @sel("\\\"",",") | % { [System.IO.Path]::GetFullPath($env:onedrive) } | Set-Clipboard'
