// =============================================================================
// [18:04] -> 14.06.2024: scrapped

/*
//
item(title="&git_add_all"
    keys="*"
    type='Desktop|Dir|Drive|Back.Dir|Back.Drive|File'
    image='@exe_cmd'
    commands{
        // cmd-line='/c dir "@sel.path" & (git add "@sel")',
        // cmd-line=for(i=0, i< sel.count) {cmd-line='/c dir "@sel.path" & (git add "@i")'}
        cmd=for(i=0, i<sel.count) { path.lnk.create(user.desktop+'\'+path.title(sel.get(i))+if(!keys.shift(), ' - Shortcut')+'.lnk', sel.get(i)) } & command.refresh)
        // cmd-line='/K (CD /D "@sel.dir") & (git add "@sel.dir")',
        // cmd=if((keys.shift() AND keys.lbutton()),('"@exe_cmd"')),
        // cmd=if((keys.shift() AND keys.rbutton()),('"@exe_cmd"')),
        // cmd=if(keys.shift(), clipboard.set(path.full('@DIR_git_add_all'))),
    }

    tip=['"@exe_cmd"','@TIP3',0.75]
    window='Visible'
    admin=(keys.shift() AND (keys.lbutton() OR keys.lbutton()))
)

// cmd-line=for(i=0, i< sel.count) {cmd-line='/c dir "@sel.path" & (git add "@i")'}
// cmd-line=for(i=0, i< sel.count) {cmd-line='/c dir "@sel.path" & (git add "sel.get(i)")'}
// cmd=for(i=0, i<sel.count) { path.lnk.create(user.desktop+'\'+path.title(sel.get(i))+if(!keys.shift(), ' - Shortcut')+'.lnk', sel.get(i)) } & command.refresh)
*/

// {
    // cmd=if(keys.rbutton(), clipboard.set(path.full('@DIR_DROPBOX'))),
    // cmd=if(window.is_explorer,command.navigate('@DIR_DROPBOX')),
    // cmd=if(!window.is_explorer,('"@DIR_DROPBOX"')),  })

// item(title="Copy-Location"   cmd-ps=ps1_get_location  type='File'                 where=sel.count==1 window='Hidden' sep='None')
// item(title="Copy-Location"   cmd-ps=ps1_get_location  type='Desktop|Dir|Drive'    where=sel.count==1 window='Hidden' sep='None')
// //
// item(title="Copy-Filepath"   cmd-ps=ps1_get_full_path  type='File'                where=sel.count==1 window='Hidden' sep='None')
// item(title="Copy-Filepaths"  cmd-ps=ps1_get_full_path  type='File'                where=sel.count>1  window='Hidden' sep='None')
// item(title="Copy-Path"       cmd-ps=ps1_get_full_path  type='Back.Dir|Back.Drive' where=sel.count==1 window='Hidden' sep='None')
// item(title="Copy-Path"       cmd-ps=ps1_get_full_path  type='Desktop|Dir|Drive'   where=sel.count==1 window='Hidden' sep='None')
// item(title="Copy-Paths"      cmd-ps=ps1_get_full_path  type='Desktop|Dir|Drive'   where=sel.count>1  window='Hidden' sep='None')
// //
// separator()



// # =============================================================================
// # 16.04.2024 - Kl.09:15:
// -> application-specific: everything
//
modify(type='*' where=process.name=="Everything64" && str.equals(this.name, ["Open"]
        image=[E122, WHITE] title="Goto" keys="Everything"
    )
)
modify(type='*' where=process.name=="Everything64" && str.equals(this.name, ["Open Path"]
        image=[E16A, WHITE]
    )
)
modify(type='*' where=process.name=="Everything64" && str.equals(this.name, ["Open With"]
        image=[E16A, WHITE] pos=indexof('Open Path', 1) sep='Both'
    )
)
//
modify(type='*' where=process.name=="Everything64" && str.equals(this.name, ["Copy Name"]
        image=[E10E, BLUE]
    )
)
modify(type='*' where=process.name=="Everything64" && str.equals(this.name, ["Copy Full Path"]
        image=[E114, BLUE]
    )
)
//
modify(type='*' where=process.name=="Everything64" && str.equals(this.name, ["Explore in Everything"]
         vis='Hidden'
    )
)
modify(type='*' where=process.name=="Everything64" && str.equals(this.name, ["Edit in Notepad"]
         vis='Hidden'
    )
)
modify(type='*' where=process.name=="Everything64" && str.equals(this.name, ["Set Run Count"]
         image=[E167, ORANGE] sep='Both'
    )
)

// modify(type='*' image=exe_winmerge where=str.equals(this.name, [
//             "Compare",
//             "Select Middle",
//             "Re-select Left"
//         ]
//     )
// )

// # =============================================================================
// # 15.04.2024 - Kl.23:36:
modify(type='*' in='/New' vis='Hidden'where=!str.equals(this.name, ["Folder", "Shortcut", "Text Document", "AutoHotkey"]))
// ->
modify(
    type='*'
    image=[E122, WHITE]
    where=process.name=="Everything64" & str.equals(this.name, [
            "Open",
            "Open Path",
            "Open With",
            "Copy Name",
            "Copy Full Path",
            "Open Path",
            "Set Run Count"
        ]
    )
)

// # =============================================================================
// DIDN'T WORK:
// # 15.04.2024 - Kl.22:22: multiple in single command
remove(type='*' in='/' where=str.contains(this.name, [
            "Add to VLC Media Player",
            "Dropbox",
            "Enqueue in Winamp",
            "News and interests",
            "NordVPN",
            "Onedrive",
            "Open archive",
            "Send a copy",
            "Sync or Backup",
            "Windows Ink Workspa"
        ]
    )
)
// singleline approach
remove(type='*' in='/' find='Add to VLC Media Player|Dropbox|Enqueue in Winamp|News and interests|NordVPN|Onedrive|Open archive|Send a copy|Sync or Backup|Windows Ink Workspa')
// WORKS:
// # =============================================================================
// # 15.04.2024 - Kl.22:22: modify multiple in single command
modify(type='*' in='/New' image=[E167, ORANGE]
    where=!str.equals(this.name, [
            "Folder",
            "Shortcut",
            "Text Document",
            "AutoHotkey"
        ]
    )
    vis='Hidden'
)



// # =============================================================================
// # 15.04.2024 - Kl.09:54:
// moudey — 09/27/2023 9:14 PM
// Before updating please make a backup copy of the configuration files. Then uninstall the previous version.
// moudey
//  pinned
// a message
//  to this channel. See all
// pinned messages
// .
//  — 09/27/2023 9:14 PM
// MuAlH — 09/27/2023 9:36 PM
// For some reason right click in desktop only works if I shift click
// Deleted the previous app before installing aswell
// Jerry Master Fixer — 09/27/2023 9:37 PM
// add priority=1 in settings to fix it i believe if its not already there
// if it hasn't changed since debug46
// MuAlH — 09/27/2023 11:23 PM
// I will try that


// # =============================================================================
// # 15.04.2024 - Kl.09:35:
// https://superuser.com/questions/277805/how-do-i-set-the-desktop-background-on-windows-from-a-script/1519604#1519604
/*
Rubic — 03/02/2024 10:23 PM
the code works, thank you very much

still struggling to do it in one line command

param ([string]$Image = "C:\Users\<USER>\AppData\Local\Packages\MicrosoftWindows.Client.CBS_cw5n1h2txyewy\LocalCache\Microsoft\IrisService\13536188103926244110\133538372655850262.jpg")

Add-Type -TypeDefinition @"
using System; using System.Runtime.InteropServices; public class Params { [DllImport("User32.dll", CharSet = CharSet.Unicode)] public static extern int SystemParametersInfo (Int32 uAction, Int32 uParam, String lpvParam, Int32 fuWinIni); }
"@

$SPI_SETDESKWALLPAPER = 0x0014; $UpdateIniFile = 0x01; $SendChangeEvent = 0x02; $fWinIni = $UpdateIniFile -bor $SendChangeEvent; [Params]::SystemParametersInfo($SPI_SETDESKWALLPAPER, 0, $Image, $fWinIni)


Add-Type -TypeDefinition @"..."@ - I have a problem with this line
*/


// # =============================================================================
// # 12.04.2024 - Kl.18:11:
modify(type='*' in='/'
    where=regex.match(this.name, [
        ".*",
        "*.",
        ".*Enqueue in Winamp.*",
        ".*News and interests.*",
        ".*NordVPN.*",
        ".*Onedrive.*",
        ".*Open archive.*",
        ".*Send a copy.*",
        ".*Sync or Backup.*",
        ".*Windows Ink Workspace.*"
        ]
    )
    vis=0
)


modify(mode=mode.multiple
    where=str.equals(this.name, [
        "Add to VLC Media Player",
        "Dropbox",
        "Enqueue in Winamp",
        "News and interests",
        "NordVPN",
        "Onedrive",
        "Open archive",
        "Send a copy",
        "Sync or Backup",
        "Windows Ink Workspace"
        ]
    )
    vis=0)





// C:\Users\<USER>\Desktop\PRJ\GIT\JHP\WORKFLOW\UTILS\NilesoftShell-moudey\official_docs\configuration

// =============================================================================
// $dir_onedrive = '[System.IO.Path]::GetFullPath($env:onedrive)'
// =============================================================================

/* root menu - context: explorer  */
menu(type='Desktop|File|Dir|Drive|Back.Dir|Back.Drive' expanded=true) {
// menu(type='*' expanded=true) {
    // update icons
    //
    // modify(image=[E0E7, RED]  type='*' sep='None' pos=-1 where=this.id==id.new)
    // modify(image=[E094, SOFT] where=this.id==id.refresh)
    // modify(image=[E061, WHITE] where=this.id==id.sort_by)
    // modify(image=[E150, WHITE] pos=10 where=this.id==id.view)
    // item(title="py_execute" image=[E157, RED] cmd-line='python "C:/Users/<USER>/Desktop/PRJ/GIT/JHP/WORKFLOW/UTILS/NilesoftShell-moudey/portable/custom/py_utils/generate_dirtree.py" "@sel.dir"')
    // item(title="py_execute" image=[E157, RED] Window='Hidden' cmd='cmd.exe' args='/K python "C:/Users/<USER>/Desktop/PRJ/GIT/JHP/WORKFLOW/UTILS/NilesoftShell-moudey/portable/custom/py_utils/generate_dirtree.py" "@sel.dir"')
    // item(title=title.command_prompt tip=tip_run_admin admin=has_admin image cmd='cmd.exe'args='/K TITLE ^< @sel.path.name ^/ ^> &ver& PUSHD "@sel.dir"')
    // item(title="py_execute" image=[E157, RED] cmd-line='python "@dir_py_utils/generate_dirtree.py" "@sel.dir"')
     // arg='-search "dm:last24hours" -filter " ┌─ (Mix) Sorted: Modified" -explore "@sel.dir"')
    modify(image=[E0FE, LOWKEY] type='*' sep='Top'    pos=3 where=this.id==id.run_as_administrator)
    modify(image=[E173, LOWKEY] type='*' sep='None'   pos=3 where=this.id==id.open_with)
    modify(image=[E173, LOWKEY] type='*' sep='None'   pos=3 where=this.id==id.open)
    //
    modify(image=[E1D2, WHITE]  type='*' sep='Top'    pos='Bottom'  where=this.id==id.create_shortcuts_here)
    modify(image=[E0CA, WHITE] type='*' sep='None'   pos='Bottom'  where=this.id==id.move_here)
    modify(image=[E089, WHITE]   type='*' sep='Bottom' pos='Bottom'  where=this.id==id.copy_here)
    //
    separator
    modify(image=[E0B8, WHITE]    type='*' sep='None'   pos=150  where=this.id==id.cut)
    modify(image=[E089, WHITE]   type='*' sep='None'   pos=150  where=this.id==id.copy)
    modify(image=[E1A6, WHITE]  type='*' sep='None'   pos=150  where=this.id==id.create_shortcut)
    modify(image=[E061, LOWKEY] type='*' sep='None'   pos=150  where=this.id==id.paste)
    modify(image=[E1DE, WHITE] type='*' sep='None'   pos=150 where=this.id==id.delete)
    //
    separator
    modify(image=[E0C9, YELLOW] type='*' sep='Both'   pos=-1 where=this.id==id.pin_to_quick_access)
    modify(image=[E0C9, YELLOW] type='*' sep='Both'   pos=-1 where=this.id==id.pin_to_start)
    modify(image=[E0C9, YELLOW] type='*' sep='Both'   pos=-1 where=this.id==id.pin_to_taskbar)
    separator
    // modify(image=[E15D, LOWKEY]   type='*' sep='None' pos=-1 where=this.id==id.properties)
    //
}
// =============================================================================
// item(title = "hello_world" cmd = msg('@app.dir\custom\_5_create_menus\menu_New.nss'))
// menu(title="&ORG" type='~Taskbar|~Desktop' mode='multiple' pos=-1 sep='Both' image=[E1B8, SOFT]) {}
// item(title='version' cmd-line='/k dotnet --info')
modify(mode=mode.multiple where=this.name=="ORG" image=[E1D7, LOWKEY])
modify(mode=mode.multiple where=this.name=="ORG" image=[E1D7, LOWKEY])
// modify(where=str.equals(this.title, ["ORG", "&ORG"]) pos="bottom" image=[E1D7, LOWKEY])
modify(find="ORG" pos=-1)
// // modify(mode= find='ORG' image=[E1D7, LOWKEY])
// // modify(where=this.title=="ORG" image=[E1D7, LOWKEY]) // drac 02.01.2024
// modify(type='*' in='/'   find='ORG' image=[E1D7, LOWKEY])
// modify(type='*' in='/'   where=this.title=="ORG" image=[E1D7, LOWKEY])
// modify(type='*' in='/'   where=this.name=="ORG" image=[E1D7, LOWKEY])
// modify(type='*' in='/'   find='&ORG' image=[E1D7, LOWKEY])
// modify(type='*' in='/'   where=this.title=="&ORG" image=[E1D7, LOWKEY])
// modify(type='*' in='/'   where=this.name=="&ORG" image=[E1D7, LOWKEY])
// modify(type='*' image=[E1D7, LOWKEY] find="ORG")
// // DEVELOP
// menu(mode="multiple" title='&Develop' image=[\uE22B, #ff66e3])
// {
// 	menu(mode="single" title='editors' image=\uE17A)
// 	{
// 		item(title='Visual Studio Code' image=[\uE272, #22A7F2] cmd='code' args='"@sel.path"')
// 		separator
// 		item(type='file' mode="single" title='Windows notepad' image cmd='@sys.bin\notepad.exe' args='"@sel.path"')
// 	}
// 	menu(mode="multiple" title='dotnet' image=\uE143)
// 	{
// 		item(title='run' cmd-line='/K dotnet run' image=\uE149)
// 		item(title='watch' cmd-line='/K dotnet watch')
// 		item(title='clean' image=\uE0CE cmd-line='/K dotnet clean')
// 		separator
// 		item(title='build debug' cmd-line='/K dotnet build')
// 		item(title='build release' cmd-line='/K dotnet build -c release /p:DebugType=None')
// 		menu(mode="multiple" sep="both" title='publish' image=\ue11f)
// 		{
// 			$publish='dotnet publish -r win-x64 -c release --output publish /*/p:CopyOutputSymbolsToPublishDirectory=false*/'
// 			item(title='publish sinale file' sep="after" cmd-line='/K @publish --no-self-contained /p:PublishSingleFile=true')
// 			item(title='framework-dependent deployment' cmd-line='/K @publish')
// 			item(title='framework-dependent executable' cmd-line='/K @publish --self-contained false')
// 			item(title='self-contained deployment' cmd-line='/K @publish --self-contained true')
// 			item(title='single-file' cmd-line='/K @publish /p:PublishSingleFile=true /p:PublishTrimmed=false')
// 			item(title='single-file-trimmed' cmd-line='/K @publish /p:PublishSingleFile=true /p:PublishTrimmed=true')
// 		}
// 		item(title='ef migrations add InitialCreate' cmd-line='/K dotnet ef migrations add InitialCreate')
// 		item(title='ef database update' cmd-line='/K dotnet ef database update')
// 		separator
// 		item(title='help' image=\uE136 cmd-line='/k dotnet -h')
// 		item(title='version' cmd-line='/k dotnet --info')
// 	}
// }


// =============================================================================
// 10.04.2024 - Kl.11:16: grp_processes.nss
// ----------------------------------------------------------------------------
// COLORS
$clr_sel    = #FFD420
$clr_subtle = #4E4259
$clr_grey   = #717482
$clr_blue   = #34B6FF
$clr_green  = #39C65A
$clr_orange = #FF904D
$clr_red    = #FF1C1A
$clr_purple = #A457FF
$clr_white  = #FFFFFF
//
// COMMANDS
// Parent Menu
$M_PROCESSES_TITLE = "&Processes"
// $M_PROCESSES_ICON  = ["\uE11B", clr_blue]
$M_PROCESSES_ICON  = ["\uE0D5", clr_white]
//
// Icons
$ICO_PROCESSES_1 = "\uE10E"
$ICO_PROCESSES_2 = "\uE1A3"
//
// Commands / Args
$PS1_PROCESS_CLOSE_SUBLIME = ['powershell', '-Command (Get-Process -ErrorAction SilentlyContinue sublime_text) | Stop-Process -Force']
$PS1_PROCESS_CLOSE_EVERYTHING = ['powershell', '-Command (Get-Process -ErrorAction SilentlyContinue Everything64) | Stop-Process -Force']
//
$PS1_PROCESS_RESTART_SUBLIME = ['powershell', '-Command $processes=(Get-Process -ErrorAction SilentlyContinue sublime_text); if ($processes -and $processes.Count -gt 0) { $path = $processes[0].Path; $processes | Stop-Process -Force; Start-Sleep -Milliseconds 500; if($path) { Start-Process -FilePath $path } }']
$PS1_PROCESS_RESTART_EVERYTHING = ['powershell', '-Command $processes=(Get-Process -ErrorAction SilentlyContinue Everything64); if ($processes -and $processes.Count -gt 0) { $path = $processes[0].Path; $processes | Stop-Process -Force; Start-Sleep -Milliseconds 500; if($path) { Start-Process -FilePath $path } }']
// $PS1_PROCESS_RESTART_EVERYTHING = ['powershell', '-Command $path=(Get-Process -ErrorAction SilentlyContinue Everything64).Path; Get-Process -ErrorAction SilentlyContinue Everything64 | Stop-Process -Force;  Start-Sleep -Milliseconds 1500; if($path) { Start-Process -FilePath $path }']

// -Command $processes=(Get-Process -ErrorAction SilentlyContinue sublime_text); if ($processes -and $processes.Count -gt 0) { $path = $processes[0].Path; $processes | Stop-Process -Force; Start-Sleep -Milliseconds 500; if($path) { Start-Process -FilePath $path } }
// ----------------------------------------------------------------------------
// COMMANDS
// ----------------------------------------------------------------------------
// $ps1_terminate_sublime = '-Command (Get-Process -ErrorAction SilentlyContinue sublime_text) | Stop-Process -Force'
// $ps1_restart_sublime  = '-Command (Get-Process -ErrorAction SilentlyContinue Everything64) | Stop-Process -Force'

// $ps1_copy_filename  = '-Command @sel("\\\"",",") | % { [System.IO.Path]::GetFileName($_) } | Set-Clipboard'
// $ps1_copy_basename  = '-Command @sel("\\\"",",") | % { [System.IO.Path]::GetFileNameWithoutExtension($_) } | Set-Clipboard'
// $ps1_copy_content   = '-Command @sel("\\\"",",") | % { Get-Content $_ -Raw } | Set-Clipboard'
// $ps1_copy_contents  = '-Command Set-Clipboard -Path (@sel("\\\"",",") | % { Get-ChildItem $_ -Force | % { $_.FullName } })'

// ----------------------------------------------------------------------------
// CREATE
// ----------------------------------------------------------------------------

// $PS1_PROCESS_RESTART_SUBLIME = ['powershell', '-Command $path = (Get-Process sublime_text -ErrorAction SilentlyContinue).Path; Get-Process sublime_text -ErrorAction SilentlyContinue | Stop-Process -Force; Start-Sleep -Seconds 2; if($path) { Start-Process -FilePath $path }']
// powershell -Command "$path = (Get-Process sublime_text -ErrorAction SilentlyContinue).Path; Get-Process sublime_text -ErrorAction SilentlyContinue | Stop-Process -Force; Start-Sleep -Milliseconds 500; if($path) { Start-Process -FilePath $path }"

// powershell -Command "Get-Process sublime_text -ErrorAction SilentlyContinue | Stop-Process -Force; Start-Sleep -Seconds 2; Start-Process -FilePath 'C:\Path\To\Sublime Text\sublime_text.exe'"
// powershell -Command "{ $processPath = (Get-Process sublime_text -ErrorAction SilentlyContinue).Path; Stop-Process -Name sublime_text -Force -ErrorAction SilentlyContinue; Start-Process $processPath }"
// powershell -Command "$path = (Get-Process sublime_text -ErrorAction SilentlyContinue).Path; Get-Process sublime_text -ErrorAction SilentlyContinue | Stop-Process -Force; Start-Sleep -Seconds 2; if($path) { Start-Process -FilePath $path }"
// powershell -Command "$path=(Get-Process -ErrorAction SilentlyContinue Everything64).Path; Get-Process -ErrorAction SilentlyContinue Everything64 | Stop-Process -Force;  Start-Sleep -Milliseconds 1500; if($path) { Start-Process -FilePath $path }"

// $PS1_COPY_FULLPATH    = ['powershell', '-Command @sel("\\\"",",") | % { [System.IO.Path]::GetFullPath($_) } | Set-Clipboard']
// $PS1_COPY_LOCATION    = ['powershell', '-Command @sel("\\\"",",") | % { [System.IO.Path]::GetDirectoryName($_) } | Set-Clipboard']
// $PS1_COPY_FILENAME    = ['powershell', '-Command @sel("\\\"",",") | % { [System.IO.Path]::GetFileName($_) } | Set-Clipboard']
// $PS1_COPY_BASENAME    = ['powershell', '-Command @sel("\\\"",",") | % { [System.IO.Path]::GetFileNameWithoutExtension($_) } | Set-Clipboard']
// $PS1_COPY_EXTENSION   = ['powershell', '-Command @sel("\\\"",",") | % { [System.IO.Path]::GetExtension($_) } | Set-Clipboard']
// $PS1_COPY_FILECONTENT = ['powershell', '-Command @sel("\\\"",",") | % { Get-Content $_ -Raw } | Set-Clipboard']
// $PS1_COPY_DIRCONTENT  = ['powershell', '-Command @sel("\\\"",",") | % { Get-ChildItem $_ -Recurse | Select-Object -ExpandProperty FullName } | Set-Clipboard']
//
// Regex Criterias
// $REGEX_TEXTFILES = "\\.(url|bat|cmd|cpp|csv|git|gitignore|htm|html|ini|js|json|log|mcr|md|ms|py|scene|sql|txt|xls|xlsx|xml|yml|nss|sublime\\w*)$"
//
// Create Menu
menu(type='Taskbar' title=M_PROCESSES_TITLE image=M_PROCESSES_ICON sep='Both')
{

    item(title = "Sublime Text"
         keys = "CLOSE"
        window = 'Hidden'
        image  = ["\uE1D6", clr_red]
        cmd    = $PS1_PROCESS_CLOSE_SUBLIME[0]
        args   = $PS1_PROCESS_CLOSE_SUBLIME[1]
    )
    item(title = "Sublime Text"
         keys = "RESTART"
        window = 'Hidden'
        image  = ["\uE1D6", clr_red]
        cmd    = $PS1_PROCESS_RESTART_SUBLIME[0]
        args   = $PS1_PROCESS_RESTART_SUBLIME[1]
    )
    separator
    item(title = "Everything"
         keys = "CLOSE"
        window = 'Hidden'
        image  = ["\uE1D6", clr_red]
        cmd    = $PS1_PROCESS_CLOSE_EVERYTHING[0]
        args   = $PS1_PROCESS_CLOSE_EVERYTHING[1]
    )
    item(title = "Everything"
         keys = "RESTART"
        window = 'Hidden'
        image  = ["\uE1D6", clr_red]
        cmd    = $PS1_PROCESS_RESTART_EVERYTHING[0]
        args   = $PS1_PROCESS_RESTART_EVERYTHING[1]
    )
    // item(title = "Copy Path"
    //     type   = 'File|Dir'
    //     window = 'Hidden'
    //     image  = [ICO_PROCESSES_1, clr_green]
    //     cmd    = PS1_COPY_FULLPATH[0]
    //     args   = PS1_COPY_FULLPATH[1]
    // )
    // item(title = "Copy Location"
    //     type   = 'File|Dir'
    //     window = 'Hidden'
    //     image  = [ICO_PROCESSES_1, clr_green]
    //     cmd    = PS1_COPY_LOCATION[0]
    //     args   = PS1_COPY_LOCATION[1]
    // )
    // separator
    // item(title = "Copy Name"
    //     type   = 'File'
    //     window = 'Hidden'
    //     image  = [ICO_PROCESSES_1, clr_blue]
    //     cmd    = PS1_COPY_FILENAME[0]
    //     args   = PS1_COPY_FILENAME[1]
    // )
    // item(title = "Copy Name"
    //     type   = 'dir'
    //     window = 'Hidden'
    //     image  = [ICO_PROCESSES_1, clr_blue]
    //     cmd    = PS1_COPY_BASENAME[0]
    //     args   = PS1_COPY_BASENAME[1]
    // )
    // separator
    // item(title = "Copy Extension"
    //     type   = 'File'
    //     window = 'Hidden'
    //     image  = [ICO_PROCESSES_1, clr_orange]
    //     cmd    = PS1_COPY_EXTENSION[0]
    //     args   = PS1_COPY_EXTENSION[1]
    // )
    // separator
    // item(title = "Copy Content"
    //     type   = 'File'
    //     window = 'Hidden'
    //     where  = regex.match(sel.file.ext, REGEX_TEXTFILES)
    //     image  = [ICO_PROCESSES_2, clr_purple]
    //     cmd    = PS1_COPY_FILECONTENT[0]
    //     args   = PS1_COPY_FILECONTENT[1]
    // )
    // item(title = "Copy Directory Contents"
    //     type   = 'Dir|Back.Dir'
    //     window = 'Hidden'
    //     image  = [ICO_PROCESSES_2, clr_purple]
    //     cmd    = PS1_COPY_DIRCONTENT[0]
    //     args   = PS1_COPY_DIRCONTENT[1]
    // )
}
// =============================================================================
// 10.04.2024 - Kl.11:35: grp_selections.nss
// ----------------------------------------------------------------------------
$ps1_select_pyfiles  = '-Command Get-ChildItem -Filter "*.py" | Select-Object -ExpandProperty FullName'
// ----------------------------------------------------------------------------
menu(title="&Selections" mode='None' image=[\uE07B, clr_white] image-sel=[\uE07B, clr_sel]) {
     item(title="Everything" image=icon.select_all cmd=command.select_all )
     item(title="Copy Filepath" keys=1  cmd-ps=ps1_select_pyfiles window=hidden)
     item(title="Invert" image=icon.invert_selection cmd=command.invert_selection)
     item(title="None" image=icon.select_none cmd=command.select_none)

     // item(mode="single" type="file" title="Change extension" image=\uE0B5 cmd=if(input("Change extension", "Type extension"),
     //      io.rename(sel.path, path.join(sel.dir, sel.file.title + "." + input.result))))
}

// explorer /select, $(Get-ChildItem "%USERPROFILE%" | ForEach-Object { $_.FullName })



// =============================================================================
// 10.04.2024 - Kl.11:35: grp_urls.nss
menu(title="&URLs" type='Taskbar|Desktop|Back.Dir' image=[\uE09B, clr_green] image-sel=[\uE09B, clr_sel]) {
// menu(title="&Directories" type='Taskbar|Desktop|Back.Dir' image=[\uE0E6, clr_green] image-sel=[\uE0E6, clr_sel]) {
    item(title="donate" image=\uE1A7 cmd='https://nilesoft.org/donate')


    // item(title="USERPROFILE"  image=[\uE09B, clr_grey]  image-sel=[\uE09B, clr_sel] cmd=(user.directory) )
    // item(title="APPDATA"      image=[\uE09E, clr_grey]  image-sel=[\uE09E, clr_sel] cmd=(user.appdata) )
    // item(title="Recycle Bin"  image=[\uE0B4, clr_grey]  image-sel=[\uE0B4, clr_sel] cmd='SHELL:::{645FF040-5081-101B-9F08-00AA002F954E}' )
    // separator

    // item(title="This PC"      image=[\uE1D9, clr_green]  image-sel=[\uE1D9, clr_sel] cmd='file:\\' )
    // item(title="Desktop"      image=[\uE1A0, clr_green]  image-sel=[\uE1A0, clr_sel] cmd=(user.desktop) )
    // item(title="Downloads"    image=[\uE154, clr_green]  image-sel=[\uE154, clr_sel] cmd=(user.downloads) )
    // separator

    // item(title="Sublime Text" image=[\uE124, clr_blue]  image-sel=[\uE124, clr_sel] cmd=(user.appdata+'/Sublime Text') )
    // item(title="Everything"   image=[\uE124, clr_blue]  image-sel=[\uE124, clr_sel] cmd=(user.appdata+'/Everything') )
    // separator

    // item(title="PRJ/GIT/JHP"  image=[\uE22C, clr_white]  image-sel=[\uE22C, clr_sel] cmd=(user.desktop+'/PRJ/GIT/JHP') )
}