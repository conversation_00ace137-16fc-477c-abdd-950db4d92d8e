// TODO:
// - Extract to and open '\filename_d\'

// menu(mode="multiple" title="Shell Extended" image=\uE249) {
//     item(where=sel.workdir==user.downloads title='Categorizer' type='dir.back' cmd='Categorizer.py' args='--directory @sel.workdir')
// }



// ----------------------------------------------------------------------------
// APPLICATION ICONS
// ----------------------------------------------------------------------------
// -> Default
// modify(image=["\uE10C", clr_blue] where=this.id==id.run_as_administrator)
// modify(image=image.fluent("\uE8E5", icn_size_mid, clr_blue) where=this.id==id.open)
// modify(image=image.fluent("\uE7AC", icn_size_mid, clr_blue) where=this.id==id.open_with)
// modify(image=image.fluent("\uE8A0", icn_size_mid, clr_blue) where=this.id==id.open_in_new_tab)
// modify(image=image.fluent("\uE838", icn_size_mid, clr_blue) where=this.id==id.open_in_new_window)
// modify(image=image.fluent("\uE81D", icn_size_mid, clr_blue) where=this.id==id.open_file_location)
// modify(image=image.fluent("\uE81D", icn_size_mid, clr_blue) where=this.id==id.open_folder_location)
// -> Custom
modify(image=($AppPath_WinMerge)     where=regex.match(this.name, ".*Compare.*"))
modify(image=($AppPath_SublimeText)  where=regex.match(this.name, ".*Sublime.*"))
modify(image=($AppPath_Everything)   where=regex.match(this.name, ".*SearchEverything.*"))
modify(image=($AppPath_NotepadPlus)  where=regex.match(this.name, ".*Notepad\\+\\+.*"))


// ----------------------------------------------------------------------------
// HIDE SPECIFIC MENUITEMS
// ----------------------------------------------------------------------------
remove(where=this.id==id.copy_path)
remove(where=this.id==id.copy_as_path)
remove(where=this.id==id.content)

// ----------------------------------------------------------------------------
// SUBMENU: OPEN
// ----------------------------------------------------------------------------

// // -> Creation
// menu(type='~taskbar' title=OPEN_MenuName pos=indexof('ORG', 1) mode=OPEN_MenuMode image=OPEN_MenuIcon) {}
// modify(where=this.id==id.run_as_administrator image=image.fluent("\uE77B", icn_size_mid, clr_blue) menu=OPEN_MenuName)
// modify(where=this.id==id.open image=image.fluent("\uE8E5", icn_size_mid, clr_blue) menu=OPEN_MenuName)
// modify(where=this.id==id.open_with image=image.fluent("\uE7AC", icn_size_mid, clr_blue) menu=OPEN_MenuName)
// modify(where=this.id==id.open_in_new_tab image=image.fluent("\uE8A0", icn_size_mid, clr_blue) menu=OPEN_MenuName)
// modify(where=this.id==id.open_in_new_window image=image.fluent("\uE838", icn_size_mid, clr_blue) menu=OPEN_MenuName)
// modify(where=this.id==id.open_file_location image=image.fluent("\uE81D", icn_size_mid, clr_blue) menu=OPEN_MenuName)
// modify(where=this.id==id.open_folder_location image=image.fluent("\uE81D", icn_size_mid, clr_blue) menu=OPEN_MenuName)

// // ----------------------------------------------------------------------------
// // COMMAND ARGUMENTS
// // ----------------------------------------------------------------------------




// // Misc
// menu(type='~taskbar' title="Misc" pos=indexof('ORG', 4) sep="after" image=image.fluent("\uE712", icn_size_mid, clr_grey)) {}
// modify(where=this.id==id.print image=image.fluent("\uE749", icn_size_mid, clr_grey) menu="Misc")
// modify(where=this.id==id.create_shortcut image=image.fluent("\uE71B", icn_size_mid, clr_grey) menu="Misc")
// modify(where=this.id==id.merge image=image.fluent("\uE8AB", icn_size_mid, clr_grey) menu="Misc")
// modify(where=this.id==id.preview image=image.fluent("\uE8FF", icn_size_mid, clr_grey) menu="Misc")
// modify(where=this.id==id.pin_current_folder_to_quick_access image=image.fluent("\uE718", icn_size_mid, clr_grey) menu="Misc")
// modify(where=this.id==id.pin_to_quick_access image=image.fluent("\uE718", icn_size_mid, clr_grey) menu="Misc")
// modify(where=this.id==id.pin_to_start image=image.fluent("\uE718", icn_size_mid, clr_grey) menu="Misc")
// modify(where=this.id==id.pin_to_taskbar image=image.fluent("\uE718", icn_size_mid, clr_grey) menu="Misc")
// modify(where=this.id==id.unpin_from_quick_access image=image.fluent("\uE718", icn_size_mid, clr_grey) menu="Misc")
// modify(where=this.id==id.unpin_from_start image=image.fluent("\uE718", icn_size_mid, clr_grey) menu="Misc")
// modify(where=this.id==id.unpin_from_taskbar image=image.fluent("\uE718", icn_size_mid, clr_grey) menu="Misc")
// modify(where=this.id==id.turn_on_bitlocker image=image.fluent("\uE785", icn_size_mid, clr_grey) menu="Misc")
// modify(where=this.id==id.format image=image.fluent("\uE8CE", icn_size_mid, clr_grey) menu="Misc")
// modify(where=this.id==id.expand image=image.fluent("\uE976", icn_size_mid, clr_grey) menu="Misc")
// // ------------------------------
// // Root
// modify(where=this.id==id.copy pos=5 image=image.fluent("\uE8C8", icn_size_max, clr_blue))
// // modify(where=this.id==id.cut pos=6 image=image.fluent("\uE8C6", icn_size_max, clr_red))
// modify(where=this.id==id.cut pos=6 image=image.fluent("\uE8C6", icn_size_max, clr_purple))
// modify(where=this.id==id.paste pos=7 image=image.fluent("\uE77F", icn_size_max, clr_green))

// // Properties
// modify(where=this.id==id.properties sep="before" pos="end" image=image.fluent("\uE7BA", icn_size_max, #717482))


// $reg_hidden = 'HKCU\SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer\Advanced'
// $is_hidden = reg.get(reg_hidden,'Hidden')==2
// item(
//  type="desktop|back"
//  image=["\uE7B3" ,"Segoe Fluent Icons"]
//  title='Show hidden files'
//  menu='view'
//  checked=!is_hidden tip='Show or hide the files and folders that are marked as hidden.'
//  commands {
//      cmd=reg.set(reg_hidden, 'Hidden', @if(is_hidden,1,2), reg.dword),
//      cmd=command.refresh,
//      cmd='ie4uinit' args='-show'
//  }
// )


// #f76e6e
// // Run as administrator
// modify(
//  where=this.id==id.run_as_administrator
//  pos=indexof('ORG', 1)
//  image=image.fluent("\uE77B", icn_size_mid, #f1a374)
//  title="Run as administrator \t –"
// )
// // Edit
// modify(
//  where=this.id==id.edit
//  pos=indexof('ORG', 2)
//  image=image.fluent("\uE8E5", icn_size_mid, #f1a374)
//  title="Edit \t –"
// )
// // Open
// modify(
//  where=this.id==id.open
//  pos=indexof('ORG', 3)
//  image=image.fluent("\uE8E5", icn_size_mid, #22A7F2)
//  title="Open \t –"
// )
// modify(
//  where=this.id==id.open_with
//  pos=indexof('ORG', 4)
//  image=image.fluent("\uE7AC", icn_size_mid, #22A7F2)
//  title="Open With"
// )
// modify(
//  where=this.id==id.open_in_new_tab
//  pos=indexof('ORG', 5)
//  image=[icon.open_in_new_tab, #22A7F2]
//  title="Open in new tab \t –"
// )
// modify(
//  where=this.id==id.open_in_new_window
//  pos=indexof('ORG', 6)
//  image=[icon.open_in_new_window, #22A7F2]
//  title="Open in new window \t –"
// )
// modify(type='file' where=this.id==id.open_with pos=indexof('ORG', 2) image=image.fluent(\uE7AC, 12) title="Open With" separator="none")
// separator pos=indexof('ORG', 3)



        // item(image=icon.glyph(\uE22A,default_icon_size) tip=['E22A git'] cmd=command.copy('\uE22A'))
        // item(image=icon.glyph(\uE22B,default_icon_size) tip=['E22B Discord'] cmd=command.copy('\uE22B'))
        // item(image=icon.glyph(\uE22C,default_icon_size) tip=['E22C GitHub'] cmd=command.copy('\uE22C'))

// ----------------------------------------------------------------------------
// IMPORT: Initialize images/icons
// ----------------------------------------------------------------------------
// import 'imports/custom/menus/menu_taskbar.nss'

// menu(type='desktop|taskbar' title='Favorites' image=#00ff00)
// {
//     menu(title='Applications' image=#ff0000)
//     {
//         item(title='Command prompt' image cmd='cmd.exe')
//         item(title='PowerShell' image cmd='powershell.exe')
//         item(title='Registry editor' image cmd='regedit.exe')
//         separator
//         item(title='Paint' image cmd='mspaint.exe')
//         item(title='Notepad' image cmd='notepad.exe')
//     }
// }

// image.fluent(\uE770,12)
// - "user.home"
// - "user.appdata"
// - "user.contacts"
// - "user.desktop"
// - "user.directory (user.dir)"
// - "user.documents"
// - "user.documentslibrary"
// - "user.downloads"
// - "user.favorites"
// - "user.libraries"
// - "user.localappdata"
// - "user.music"
// - "user.personal"
// - "user.pictures"
// - "user.profile"
// - "user.quicklaunch"
// - "user.sendto"
// - "user.startmenu"
// - "user.temp"
// - "user.templates"
// - "user.videos"





// // WORKS: Move NordVPN to Submenu
// // menu(mode='multiple' type="*" separator="both" title="-> Applications" pos="middle" image=sublime_logo) {
// menu(title="&TEST" mode='multiple' type='*' pos=0  image=[\uE25E, clr_purple] image-sel=[\uE25E, clr_sel] sep='Both') {
//     menu(mode="multiple" type="*" separator="both" title="NordVPN" image=icon.copy_path) {}
//     menu(mode="multiple" type="*" separator="both" title="GitKraken" image=icon.copy_path) {}
//     menu(mode="multiple" type="*" separator="both" title="ShareX" image=icon.copy_path) {}
//     menu(mode="multiple" type="*" separator="both" title="Microsoft" image=icon.copy_path) {}
//     menu(mode="multiple" type="*" separator="both" title="VLC" image=icon.copy_path) {}
//     menu(mode="multiple" type="*" separator="both" title="Dropbox" image=icon.copy_path) {}
//     menu(mode="multiple" type="*" separator="both" title="Sandbox" image=icon.copy_path) {}
//     menu(mode="multiple" type="*" separator="both" title="Everything" image=icon.copy_path) {}
//     menu(mode="multiple" type="*" separator="both" title="Git" image=icon.copy_path) {}
//     menu(mode="multiple" type="*" separator="both" title="Google Drive" image=icon.copy_path) {}
//     menu(mode="multiple" type="*" separator="both" title="Visual Studio" image=icon.copy_path) {}
// }