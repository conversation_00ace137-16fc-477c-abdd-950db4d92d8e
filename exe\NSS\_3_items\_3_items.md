# Project Files Documentation for `_3_items`

### File Structure

```
├── itm_action_sys_copypath.nss
├── itm_action_sys_createtxt.nss
├── itm_action_sys_restartexplorer.nss
├── itm_action_sys_showdesktop.nss
├── itm_app_3dsmax.nss
├── itm_app_audacity.nss
├── itm_app_bambustudio.nss
├── itm_app_beyondcompare.nss
├── itm_app_blender.nss
├── itm_app_bulkrenameutility.nss
├── itm_app_claude.nss
├── itm_app_comfyui.nss
├── itm_app_cursor.nss
├── itm_app_everything.nss
├── itm_app_filezilla.nss
├── itm_app_firefox.nss
├── itm_app_gitrestoremtime.nss
├── itm_app_gpt4all.nss
├── itm_app_gsmartcontrol.nss
├── itm_app_irfanview.nss
├── itm_app_kdenlive.nss
├── itm_app_libreoffice.nss
├── itm_app_losslesscut.nss
├── itm_app_mypyinterface.nss
├── itm_app_nirsoft_regfromapp.nss
├── itm_app_nirsoft_registrychangesview.nss
├── itm_app_nirsoft_regscanner.nss
├── itm_app_nirsoft_winexplorer.nss
├── itm_app_notepad++.nss
├── itm_app_pdf24.nss
├── itm_app_photoshop.nss
├── itm_app_powertoys.nss
├── itm_app_qbittorrent.nss
├── itm_app_rufus.nss
├── itm_app_rustdesk.nss
├── itm_app_sharex.nss
├── itm_app_simplewall.nss
├── itm_app_sourcetree.nss
├── itm_app_sublimetext.nss
├── itm_app_sys_calc.nss
├── itm_app_sys_cmd.nss
├── itm_app_sys_magnify.nss
├── itm_app_sys_mspaint.nss
├── itm_app_sys_notepad.nss
├── itm_app_sys_osk.nss
├── itm_app_sys_powershell.nss
├── itm_app_sys_powershellise.nss
├── itm_app_sys_regedit.nss
├── itm_app_sys_taskmanager.nss
├── itm_app_sysinternals_autoruns.nss
├── itm_app_sysinternals_diskmon.nss
├── itm_app_sysinternals_procexp.nss
├── itm_app_sysinternals_tcpview.nss
├── itm_app_telegram.nss
├── itm_app_vlc.nss
├── itm_app_winmerge.nss
├── itm_app_yvonne.nss
├── itm_bat_pyvenvterminal.nss
├── itm_bat_pyvenvwriterequirements.nss
├── itm_dir_jorn_nss.nss
├── itm_dir_jorn_scratch.nss
├── itm_dir_jorn_share.nss
├── itm_dir_sys_appdata.nss
├── itm_dir_sys_desktop.nss
├── itm_dir_sys_documents.nss
├── itm_dir_sys_downloads.nss
├── itm_dir_sys_programfiles.nss
├── itm_dir_sys_recent.nss
├── itm_dir_sys_recyclebin.nss
├── itm_dir_sys_startup.nss
├── itm_dir_sys_thispc.nss
├── itm_dir_sys_userprofile.nss
├── itm_dir_sys_userprofile_ssh.nss
├── itm_py_audioandvideocombiner.nss
├── itm_py_bookmarkfolderizer.nss
├── itm_py_closeduplicatewindows.nss
├── itm_py_environmentvariablesmanager.nss
├── itm_py_gitfilterrepo.nss
├── itm_py_gitsizeanalyzer.nss
├── itm_py_markdowngenerator.nss
├── itm_py_projectgenerator.nss
├── itm_py_renamewitheditor.nss
├── itm_py_sanitizefilenames.nss
├── itm_py_speechtotext.nss
├── itm_py_urlgenerator.nss
├── itm_py_videocompressor.nss
├── itm_py_videosplitter.nss
├── itm_py_win4ro1.nss
├── itm_py_windowutils.nss
├── itm_py_youtubedownloader.nss
├── itm_py_youtubeplaylistmanager.nss
├── itm_reg_setkeyboardrepeatrate.nss
└── itm_reg_settaskbarleft.nss
```
### 1. `itm_action_sys_copypath.nss`

#### `itm_action_sys_copypath.nss`

```java

//
$PS1_COPYPATH = '-Command @sel("\\\"",",") | % {[System.IO.Path]::GetFullPath($_)} | Set-Clipboard'

//
item(
    title    = "&Copy Path"
    keys     = ""
    type   = 'Desktop|Dir|Drive|Back.Dir|Back.Drive|File'
    where  = sel.count==1
    cmd-ps = '@PS1_COPYPATH'
    //
    image    = [E0B5,GREEN] image-sel=[E0B5,HOVER]
    // tip      = ["@PS1_COPYPATH",TIP3,0.75]
    window   = 'Hidden'
)

```
### 2. `itm_action_sys_createtxt.nss`

#### `itm_action_sys_createtxt.nss`

```java

$STR_TIME = sys.datetime("H.M")
$STR_DATE = sys.datetime("y.m.d")
$STR_DATETIME = sys.datetime("y.m.d__H.M")

$STR_SEPARATOR = "============================================================================="
$STR_LINEBREAK = "\n"

$STR_CONTENT_PY = '# @STR_SEPARATOR'+STR_LINEBREAK+'# [@STR_TIME] - @STR_DATE'+STR_LINEBREAK

item(
    title="&txt"
    keys="*.*"
    type  = 'Desktop|Back.Dir|Back.Drive|File|Dir'
    image = [E230,WHITE] image-sel=[E230,HOVER]
    cmd=io.file.create('@(STR_DATETIME).txt','@STR_CONTENT_PY',1)
)

```
### 3. `itm_action_sys_restartexplorer.nss`

#### `itm_action_sys_restartexplorer.nss`

```java

//
$CMD_RESTARTEXPLORER = '/c taskkill /F /IM explorer.exe & start explorer.exe'

//
item(
    title    = "&Restart Explorer"
    keys     = ""
    type     = '*'
    cmd-line = '@CMD_RESTARTEXPLORER'
    //
    image    = [E12B,GREY] image-sel=[E12B,HOVER]
    tip      = ['@CMD_RESTARTEXPLORER',TIP3,0.75]
    window   = 'Hidden'
    //
    admin    = (keys.rbutton() OR keys.lbutton())
)

```
### 4. `itm_action_sys_showdesktop.nss`

#### `itm_action_sys_showdesktop.nss`

```java

//
item(
    title    = "&Show Desktop"
    keys     = ""
    type     = '*'
    cmd      = '@command.toggle_desktop'
    //
    image    = [E1A0,BLUE3] image-sel=[E1A0,HOVER]
    tip      = ["@command.toggle_desktop",TIP3,0.75]
    window   = 'Hidden'
    //
)

```
### 5. `itm_app_3dsmax.nss`

#### `itm_app_3dsmax.nss`

```java

//
$APP_3DSMAX_DIR = '@sys.prog\Autodesk\3ds Max 2025'
$APP_3DSMAX_EXE = '@APP_3DSMAX_DIR\3dsmax.exe'
$APP_3DSMAX_TIP = "..."+str.trimstart('@APP_3DSMAX_EXE','@app.dir')
//
$APP_3DSMAX_DIR_CFG = '@user.localappdata\Autodesk\3dsMax\2025 - 64bit'
$APP_3DSMAX_DIR_NSS = '@app.dir\NSS\_3_items\user_apps'
$APP_3DSMAX_DIR_SRC = '@user.desktop\my\flow\home\__GOTO__\Apps\app_3dsmax'

// Context: File
item(
    title  = ":  &3dsMax"
    keys   = "exe"
    type   = 'File'
    args   = '"@sel.file"'
    where  = str.equals(sel.file.ext,[".max"])
    //
    image  = APP_3DSMAX_EXE
    tip    = [APP_3DSMAX_TIP,TIP3,1.0]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_3DSMAX_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_3DSMAX_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_3DSMAX_EXE')),
        cmd=if(KEYS_EXE_GOTO_CFG,('@APP_3DSMAX_DIR_CFG')),
        cmd=if(KEYS_EXE_GOTO_NSS,('@APP_3DSMAX_DIR_NSS')),
        cmd=if(KEYS_EXE_GOTO_SRC,('@APP_3DSMAX_DIR_SRC')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_3DSMAX_DIR')),
    }
)
// Context: Directory
item(
    title  = ":  &3dsMax"
    keys   = "exe"
    type   = 'Desktop|Dir|Drive|Back.Dir|Back.Drive'
    args   = '--local="@sel.dir"'
    //
    image  = APP_3DSMAX_EXE
    tip    = [APP_3DSMAX_TIP,TIP3,1.0]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_3DSMAX_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_3DSMAX_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_3DSMAX_EXE')),
        cmd=if(KEYS_EXE_GOTO_CFG,('@APP_3DSMAX_DIR_CFG')),
        cmd=if(KEYS_EXE_GOTO_NSS,('@APP_3DSMAX_DIR_NSS')),
        cmd=if(KEYS_EXE_GOTO_SRC,('@APP_3DSMAX_DIR_SRC')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_3DSMAX_DIR')),
    }
)
// Context: Taskbar
item(
    title  = ":  &3dsMax"
    keys   = "exe"
    type   = 'Taskbar'
    args   = ''
    //
    image  = APP_3DSMAX_EXE
    tip    = [APP_3DSMAX_TIP,TIP3,1.0]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_3DSMAX_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_3DSMAX_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_3DSMAX_EXE')),
        cmd=if(KEYS_EXE_GOTO_CFG,('@APP_3DSMAX_DIR_CFG')),
        cmd=if(KEYS_EXE_GOTO_NSS,('@APP_3DSMAX_DIR_NSS')),
        cmd=if(KEYS_EXE_GOTO_SRC,('@APP_3DSMAX_DIR_SRC')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_3DSMAX_DIR')),
    }
)

// //
// $APP_USER_3DSMAX_DIR = '@user.desktop\my\flow\home\__GOTO__\Apps\app_3dsmax'
// $APP_USER_3DSMAX_EXE = '@APP_USER_3DSMAX_DIR\Bulk Rename Utility.exe'
// $APP_USER_3DSMAX_TIP = "..."+str.trimstart('@APP_USER_3DSMAX_EXE','@app.dir')

// // Context: Explorer
// item(
//     title  = ":  &3dsMax"
//     keys   = "exe"
//     type   = 'File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
//     args   = '"@sel.dir"'
//     //
//     image  = APP_USER_3DSMAX_EXE
//     tip    = [APP_USER_3DSMAX_TIP,TIP3,0.8]
//     //
//     admin  = keys.rbutton()
//     cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_USER_3DSMAX_EXE"'))
//     commands{
//         cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_3DSMAX_DIR')),
//         cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_3DSMAX_EXE')),
//         cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_3DSMAX_DIR')),
//     }
// )
// // Context: Taskbar
// item(
//     title  = ":  &3dsMax"
//     keys   = "exe"
//     type   = 'Taskbar'
//     args   = '@user.desktop'
//     //
//     image  = APP_USER_3DSMAX_EXE
//     tip    = [APP_USER_3DSMAX_TIP,TIP3,0.8]
//     //
//     admin  = keys.rbutton()
//     cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_USER_3DSMAX_EXE"'))
//     commands{
//         cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_3DSMAX_DIR')),
//         cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_3DSMAX_EXE')),
//         cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_3DSMAX_DIR')),
//     }
// )
```
### 6. `itm_app_audacity.nss`

#### `itm_app_audacity.nss`

```java

//
$APP_AUDACITY_DIR = '@user.desktop\my\flow\home\__GOTO__\Apps\app_audacity\exe\audacity-win-3.7.1-64bit'
$APP_AUDACITY_EXE = '@APP_AUDACITY_DIR\Audacity.exe'
$APP_AUDACITY_TIP = "..."+str.trimstart('@APP_AUDACITY_EXE','@app.dir')
//
$APP_AUDACITY_DIR_CFG = '@user.appdata\audacity'
$APP_AUDACITY_DIR_NSS = '@app.dir\NSS\_3_items\user_apps'
$APP_AUDACITY_DIR_SRC = '@user.desktop\my\flow\home\__GOTO__\Apps\app_audacity'

// Context: File
item(
    title  = ":  &Audacity"
    keys   = "exe"
    type   = 'File'
    args   = '"@sel.file"'
    where  = str.equals(sel.file.ext,[".aac",".flac",".m4a",".mp3",".ogg",".wav",".wma"])
    //
    image  = APP_AUDACITY_EXE
    tip    = [APP_AUDACITY_TIP,TIP3,1.0]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_AUDACITY_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_AUDACITY_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_AUDACITY_EXE')),
        cmd=if(KEYS_EXE_GOTO_CFG,('@APP_AUDACITY_DIR_CFG')),
        cmd=if(KEYS_EXE_GOTO_NSS,('@APP_AUDACITY_DIR_NSS')),
        cmd=if(KEYS_EXE_GOTO_SRC,('@APP_AUDACITY_DIR_SRC')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_AUDACITY_DIR')),
    }
)
// Context: Directory
item(
    title  = ":  &Audacity"
    keys   = "exe"
    type   = 'Desktop|Dir|Drive|Back.Dir|Back.Drive'
    args   = '--local="@sel.dir"'
    //
    image  = APP_AUDACITY_EXE
    tip    = [APP_AUDACITY_TIP,TIP3,1.0]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_AUDACITY_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_AUDACITY_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_AUDACITY_EXE')),
        cmd=if(KEYS_EXE_GOTO_CFG,('@APP_AUDACITY_DIR_CFG')),
        cmd=if(KEYS_EXE_GOTO_NSS,('@APP_AUDACITY_DIR_NSS')),
        cmd=if(KEYS_EXE_GOTO_SRC,('@APP_AUDACITY_DIR_SRC')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_AUDACITY_DIR')),
    }
)
// Context: Taskbar
item(
    title  = ":  &Audacity"
    keys   = "exe"
    type   = 'Taskbar'
    args   = ''
    //
    image  = APP_AUDACITY_EXE
    tip    = [APP_AUDACITY_TIP,TIP3,1.0]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_AUDACITY_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_AUDACITY_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_AUDACITY_EXE')),
        cmd=if(KEYS_EXE_GOTO_CFG,('@APP_AUDACITY_DIR_CFG')),
        cmd=if(KEYS_EXE_GOTO_NSS,('@APP_AUDACITY_DIR_NSS')),
        cmd=if(KEYS_EXE_GOTO_SRC,('@APP_AUDACITY_DIR_SRC')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_AUDACITY_DIR')),
    }
)


```
### 7. `itm_app_bambustudio.nss`

#### `itm_app_bambustudio.nss`

```java

//
$APP_BAMBUSTUDIO_DIR = '@sys.prog\Bambu Studio'
$APP_BAMBUSTUDIO_EXE = '@APP_BAMBUSTUDIO_DIR\bambu-studio.exe'
$APP_BAMBUSTUDIO_TIP = "..."+str.trimstart('@APP_BAMBUSTUDIO_EXE','@app.dir')
//
$APP_BAMBUSTUDIO_NSS = '@app.dir\NSS\_3_items\user_apps'
$APP_BAMBUSTUDIO_SRC = '@user.desktop\my\flow\home\__GOTO__\Apps\app_bambustudio'


// Context: File
item(
    title  = ":  &Bambu Studio"
    keys   = "exe"
    type   = 'File'
    args   = '"@sel.file"'
    where  = str.equals(sel.file.ext,[".max"])
    //
    image  = APP_BAMBUSTUDIO_EXE
    tip    = [APP_BAMBUSTUDIO_TIP,TIP3,1.0]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_BAMBUSTUDIO_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_BAMBUSTUDIO_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_BAMBUSTUDIO_EXE')),
        cmd=if(KEYS_EXE_GOTO_NSS,('@APP_BAMBUSTUDIO_NSS')),
        cmd=if(KEYS_EXE_GOTO_SRC,('@APP_BAMBUSTUDIO_SRC')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_BAMBUSTUDIO_DIR')),
    }
)
// Context: Directory
item(
    title  = ":  &Bambu Studio"
    keys   = "exe"
    type   = 'Desktop|Dir|Drive|Back.Dir|Back.Drive'
    args   = '--local="@sel.dir"'
    //
    image  = APP_BAMBUSTUDIO_EXE
    tip    = [APP_BAMBUSTUDIO_TIP,TIP3,1.0]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_BAMBUSTUDIO_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_BAMBUSTUDIO_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_BAMBUSTUDIO_EXE')),
        cmd=if(KEYS_EXE_GOTO_NSS,('@APP_BAMBUSTUDIO_NSS')),
        cmd=if(KEYS_EXE_GOTO_SRC,('@APP_BAMBUSTUDIO_SRC')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_BAMBUSTUDIO_DIR')),
    }
)
// Context: Taskbar
item(
    title  = ":  &Bambu Studio"
    keys   = "exe"
    type   = 'Taskbar'
    args   = ''
    //
    image  = APP_BAMBUSTUDIO_EXE
    tip    = [APP_BAMBUSTUDIO_TIP,TIP3,1.0]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_BAMBUSTUDIO_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_BAMBUSTUDIO_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_BAMBUSTUDIO_EXE')),
        cmd=if(KEYS_EXE_GOTO_NSS,('@APP_BAMBUSTUDIO_NSS')),
        cmd=if(KEYS_EXE_GOTO_SRC,('@APP_BAMBUSTUDIO_SRC')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_BAMBUSTUDIO_DIR')),
    }
)


```
### 8. `itm_app_beyondcompare.nss`

#### `itm_app_beyondcompare.nss`

```java

//
$APP_USER_BEYONDCOMPARE_DIR = '@user.desktop\my\flow\home\__GOTO__\Apps\app_beyondcompare\exe'
$APP_USER_BEYONDCOMPARE_EXE = '@APP_USER_BEYONDCOMPARE_DIR\BCompare.exe'
$APP_USER_BEYONDCOMPARE_TIP = "..."+str.trimstart('@APP_USER_BEYONDCOMPARE_EXE','@app.dir')

// context: directory
item(
    title  = ":  &BeyondCompare"
    keys   = "exe"
    type   = 'File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    // find   = '.ts|.mp4|.mkv|.webm|.mov|.wmv|.avi|.flv|.mpg'
    // args   = '"@sel.file"'
    //
    image  = APP_USER_BEYONDCOMPARE_EXE
    tip    = [APP_USER_BEYONDCOMPARE_TIP,TIP3,0.8]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_USER_BEYONDCOMPARE_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_BEYONDCOMPARE_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_BEYONDCOMPARE_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_BEYONDCOMPARE_DIR')),
    }
)
// Context: Taskbar
item(
    title  = ":  &BeyondCompare"
    keys   = "exe"
    type   = 'Taskbar'
    args   = ''
    //
    image  = APP_USER_BEYONDCOMPARE_EXE
    tip    = [APP_USER_BEYONDCOMPARE_TIP,TIP3,0.8]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_USER_BEYONDCOMPARE_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_BEYONDCOMPARE_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_BEYONDCOMPARE_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_BEYONDCOMPARE_DIR')),
    }
)

```
### 9. `itm_app_blender.nss`

#### `itm_app_blender.nss`

```java

//
$APP_BLENDER_NSS = '@app.dir\NSS\_3_items\user_apps'
$APP_BLENDER_SRC = '@user.desktop\my\flow\home\__GOTO__\Apps\app_blender'
//
$APP_USER_BLENDER_DIR = '@user.desktop\my\flow\home\__GOTO__\Apps\app_blender\exe\blender-4.1.1-windows-x64'
$APP_USER_BLENDER_EXE = '@APP_USER_BLENDER_DIR\blender.exe'
$APP_USER_BLENDER_TIP = "..."+str.trimstart('@APP_USER_BLENDER_EXE','@app.dir')


// Context: File
item(
    title  = ":  &Blender"
    keys   = "exe"
    type   = 'File'
    args   = '"@sel.file"'
    where  = str.equals(sel.file.ext,[".blend", ".blend1"])
    //
    image  = APP_USER_BLENDER_EXE
    tip    = [APP_USER_BLENDER_TIP,TIP3,1.0]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_USER_BLENDER_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_BLENDER_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_BLENDER_EXE')),
        cmd=if(KEYS_EXE_GOTO_NSS,('@APP_BLENDER_NSS')),
        cmd=if(KEYS_EXE_GOTO_SRC,('@APP_BLENDER_SRC')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_BLENDER_DIR')),
    }
)
// Context: Directory
item(
    title  = ":  &Blender"
    keys   = "exe"
    type   = 'Desktop|Dir|Drive|Back.Dir|Back.Drive'
    args   = '--local="@sel.dir"'
    //
    image  = APP_USER_BLENDER_EXE
    tip    = [APP_USER_BLENDER_TIP,TIP3,1.0]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_USER_BLENDER_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_BLENDER_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_BLENDER_EXE')),
        cmd=if(KEYS_EXE_GOTO_NSS,('@APP_BLENDER_NSS')),
        cmd=if(KEYS_EXE_GOTO_SRC,('@APP_BLENDER_SRC')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_BLENDER_DIR')),
    }
)
// Context: Taskbar
item(
    title  = ":  &Blender"
    keys   = "exe"
    type   = 'Taskbar'
    args   = ''
    //
    image  = APP_USER_BLENDER_EXE
    tip    = [APP_USER_BLENDER_TIP,TIP3,1.0]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_USER_BLENDER_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_BLENDER_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_BLENDER_EXE')),
        cmd=if(KEYS_EXE_GOTO_NSS,('@APP_BLENDER_NSS')),
        cmd=if(KEYS_EXE_GOTO_SRC,('@APP_BLENDER_SRC')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_BLENDER_DIR')),
    }
)


```
### 10. `itm_app_bulkrenameutility.nss`

#### `itm_app_bulkrenameutility.nss`

```java

//
$APP_USER_BULKRENAMEUTILITY_DIR = '@user.desktop\my\flow\home\__GOTO__\Apps'
$APP_USER_BULKRENAMEUTILITY_DIR = '@user.desktop\my\flow\home\__GOTO__\Apps\app_bulkrenameutility\exe\64-bit'
$APP_USER_BULKRENAMEUTILITY_EXE = '@APP_USER_BULKRENAMEUTILITY_DIR\Bulk Rename Utility.exe'
$APP_USER_BULKRENAMEUTILITY_TIP = "..."+str.trimstart('@APP_USER_BULKRENAMEUTILITY_EXE','@app.dir')

// Context: Explorer
item(
    title  = ":  &Bulk-Rename-Utility"
    keys   = "exe"
    type   = 'File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    args   = '"@sel.dir"'
    //
    image  = APP_USER_BULKRENAMEUTILITY_EXE
    tip    = [APP_USER_BULKRENAMEUTILITY_TIP,TIP3,0.8]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_USER_BULKRENAMEUTILITY_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_BULKRENAMEUTILITY_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_BULKRENAMEUTILITY_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_BULKRENAMEUTILITY_DIR')),
    }
)
// Context: Taskbar
item(
    title  = ":  &Bulk-Rename-Utility"
    keys   = "exe"
    type   = 'Taskbar'
    args   = '@user.desktop'
    //
    image  = APP_USER_BULKRENAMEUTILITY_EXE
    tip    = [APP_USER_BULKRENAMEUTILITY_TIP,TIP3,0.8]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_USER_BULKRENAMEUTILITY_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_BULKRENAMEUTILITY_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_BULKRENAMEUTILITY_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_BULKRENAMEUTILITY_DIR')),
    }
)

```
### 11. `itm_app_claude.nss`

#### `itm_app_claude.nss`

```java

//
$APP_CLAUDE_DIR = '@user.localappdata\AnthropicClaude'
$APP_CLAUDE_EXE = '@APP_CLAUDE_DIR\claude.exe'
$APP_CLAUDE_TIP = "..."+str.trimstart('@APP_CLAUDE_EXE','@app.dir')
//
$APP_CLAUDE_DIR_CFG = '@user.appdata\Claude'
$APP_CLAUDE_DIR_NSS = '@app.dir\NSS\_3_items\user_apps'
$APP_CLAUDE_DIR_SRC = '@user.desktop\my\flow\home\__GOTO__\Apps\app_claude'


// Context: File
item(
    title  = ":  &Claude"
    keys   = "exe"
    type   = 'File'
    args   = '"@sel.file"'
    // where  = str.equals(sel.file.ext,[".max"])
    //
    image  = APP_CLAUDE_EXE
    tip    = [APP_CLAUDE_TIP,TIP3,1.0]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_CLAUDE_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_CLAUDE_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_CLAUDE_EXE')),
        cmd=if(KEYS_EXE_GOTO_CFG,('@APP_CLAUDE_DIR_CFG')),
        cmd=if(KEYS_EXE_GOTO_NSS,('@APP_CLAUDE_DIR_NSS')),
        cmd=if(KEYS_EXE_GOTO_SRC,('@APP_CLAUDE_DIR_SRC')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_CLAUDE_DIR')),
    }
)
// Context: Directory
item(
    title  = ":  &Claude"
    keys   = "exe"
    type   = 'Desktop|Dir|Drive|Back.Dir|Back.Drive'
    args   = '--local="@sel.dir"'
    //
    image  = APP_CLAUDE_EXE
    tip    = [APP_CLAUDE_TIP,TIP3,1.0]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_CLAUDE_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_CLAUDE_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_CLAUDE_EXE')),
        cmd=if(KEYS_EXE_GOTO_CFG,('@APP_CLAUDE_DIR_CFG')),
        cmd=if(KEYS_EXE_GOTO_NSS,('@APP_CLAUDE_DIR_NSS')),
        cmd=if(KEYS_EXE_GOTO_SRC,('@APP_CLAUDE_DIR_SRC')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_CLAUDE_DIR')),
    }
)
// Context: Taskbar
item(
    title  = ":  &Claude"
    keys   = "exe"
    type   = 'Taskbar'
    args   = ''
    //
    image  = APP_CLAUDE_EXE
    tip    = [APP_CLAUDE_TIP,TIP3,1.0]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_CLAUDE_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_CLAUDE_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_CLAUDE_EXE')),
        cmd=if(KEYS_EXE_GOTO_CFG,('@APP_CLAUDE_DIR_CFG')),
        cmd=if(KEYS_EXE_GOTO_NSS,('@APP_CLAUDE_DIR_NSS')),
        cmd=if(KEYS_EXE_GOTO_SRC,('@APP_CLAUDE_DIR_SRC')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_CLAUDE_DIR')),
    }
)

```
### 12. `itm_app_comfyui.nss`

#### `itm_app_comfyui.nss`

```java

// Strings
$TIME_NOW = '@sys.datetime("H.M")'

// Static: Paths and commands
$APP_COMFYUI_DIR = '@user.desktop\my\flow\home\__GOTO__\Apps\app_comfyui'
$APP_COMFYUI_CMD = '/K (CD /D "@APP_COMFYUI_DIR") & (TITLE ^[ComfyUI:@TIME_NOW^]) && (ECHO -^| ComfyUI: @TIME_NOW)'
$APP_COMFYUI_EXE = '@APP_COMFYUI_CMD && "@APP_COMFYUI_DIR\exe\python_embeded\python.exe"'


// APP_COMFYUI_GPU_TASKBAR
item(
    title=":  &ComfyUI: GPU"
    keys="py"
    type='Taskbar'
    //
    image=[E17C,GREY]
    image-sel=[E17C,PURPLE]
    //
    admin=keys.rbutton()
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_COMFYUI_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_COMFYUI_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_COMFYUI_DIR')),
        cmd-line=if(KEYS_EXE_OPEN_EXE,('@APP_COMFYUI_EXE "@APP_COMFYUI_DIR\exe\ComfyUI\main.py" --windows-standalone-build')),
    }
)
// APP_COMFYUI_CPU_TASKBAR
item(
    title=":  &ComfyUI: CPU"
    keys="py"
    type='Taskbar'
    //
    image=[E17C,GREY]
    image-sel=[E17C,PURPLE]
    //
    admin=keys.rbutton()
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_COMFYUI_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_COMFYUI_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_COMFYUI_DIR')),
        cmd-line=if(KEYS_EXE_OPEN_EXE,('@APP_COMFYUI_EXE "@APP_COMFYUI_DIR\exe\ComfyUI\main.py" --cpu --windows-standalone-build')),
    }
)

```
### 13. `itm_app_cursor.nss`

#### `itm_app_cursor.nss`

```java

//
$APP_CURSOR_DIR = '@user.desktop\my\flow\home\__GOTO__\Apps\app_cursor\exe'
$APP_CURSOR_EXE = '@APP_CURSOR_DIR\cursor.exe'
$APP_CURSOR_TIP = "..."+str.trimstart('@APP_CURSOR_EXE','@app.dir')

//
$APP_CURSOR_DIR_CFG = '@user.desktop\my\flow\home\__GOTO__\Apps\app_cursor\data'
$APP_CURSOR_DIR_NSS = '@app.dir\NSS\_3_items\user_apps'
$APP_CURSOR_DIR_SRC = '@user.desktop\my\flow\home\__GOTO__\Apps\app_cursor'
$APP_CURSOR_DIR_USR = '@user.desktop\my\flow\home\__GOTO__\Apps\app_cursor\user'

// Context: Taskbar
item(
    title  = ":  &Cursor"
    keys   = "exe"
    type   = 'Taskbar'
    args   = ''
    //
    image  = APP_CURSOR_EXE
    tip    = [APP_CURSOR_TIP,TIP3,1.0]
    //
    admin  = KEYS_EXE_ADMIN // [rightclick] -[ctrl, alt, shift]
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_CURSOR_EXE"')) // -[ctrl, alt, shift]
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_CURSOR_DIR')), // [ctrl + leftclick] -[alt]
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_CURSOR_EXE')), // [ctrl + rightclick] -[alt]
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_CURSOR_DIR')), // [shift + leftclick] -[ctrl, alt]
        //
        cmd=if(KEYS_EXE_GOTO_CFG,('@APP_CURSOR_DIR_CFG')), // [alt + leftclick] -[ctrl, shift]
        cmd=if(KEYS_EXE_GOTO_NSS,('@APP_CURSOR_DIR_NSS')), // [alt + shift + leftclick] -[ctrl]
        cmd=if(KEYS_EXE_GOTO_SRC,('@APP_CURSOR_DIR_SRC')), // [alt + rightclick] -[ctrl, shift]
    }
)

```
### 14. `itm_app_everything.nss`

#### `itm_app_everything.nss`

```java

//
$APP_USER_EVERYTHING_DIR = '@user.desktop\my\flow\home\__GOTO__\Apps\app_everything\exe'
$APP_USER_EVERYTHING_EXE = '@APP_USER_EVERYTHING_DIR\Everything64.exe'
$APP_USER_EVERYTHING_TIP = "..."+str.trimstart('@APP_USER_EVERYTHING_EXE','@app.dir')

//
// $SEARCH_ARGS_GRPEXT = "grp-unrelevant: "

// Context: Explorer
item(
    title  = ":  &Everything"
    keys   = "exe"
    type   = 'File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    args   = '-search "!ext:lnk " -filter " ├─ Modified: Any Time (filtered)" -explore "@sel.dir"'
    //
    image  = APP_USER_EVERYTHING_EXE
    tip    = [APP_USER_EVERYTHING_TIP,TIP3,0.8]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_USER_EVERYTHING_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_EVERYTHING_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_EVERYTHING_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_EVERYTHING_DIR')),
    }
)
// Context: Taskbar
item(
    title  = ":  &Everything"
    keys   = "exe"
    type   = 'Taskbar'
    args   = '-search "!ext:lnk " -filter " ├─ Modified: Any Time (filtered)"'
    //
    image  = APP_USER_EVERYTHING_EXE
    tip    = [APP_USER_EVERYTHING_TIP,TIP3,0.8]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_USER_EVERYTHING_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_EVERYTHING_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_EVERYTHING_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_EVERYTHING_DIR')),
    }
)


```
### 15. `itm_app_filezilla.nss`

#### `itm_app_filezilla.nss`

```java

//
$APP_USER_FILEZILLA_DIR = '@user.desktop\my\flow\home\__GOTO__\Apps\app_filezilla\exe'
$APP_USER_FILEZILLA_EXE = '@APP_USER_FILEZILLA_DIR\filezilla.exe'
$APP_USER_FILEZILLA_TIP = "..."+str.trimstart('@APP_USER_FILEZILLA_EXE','@app.dir')

// context: directory
item(
    title  = ":  &FileZilla"
    keys   = "exe"
    type   = 'Desktop|Dir|Drive|Back.Dir|Back.Drive'
    args   = '--local="@sel.dir"'
    //
    image  = APP_USER_FILEZILLA_EXE
    tip    = [APP_USER_FILEZILLA_TIP,TIP3,0.8]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_USER_FILEZILLA_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_FILEZILLA_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_FILEZILLA_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_FILEZILLA_DIR')),
    }
)
// Context: Taskbar
item(
    title  = ":  &FileZilla"
    keys   = "exe"
    type   = 'Taskbar'
    args   = ''
    //
    image  = APP_USER_FILEZILLA_EXE
    tip    = [APP_USER_FILEZILLA_TIP,TIP3,0.8]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_USER_FILEZILLA_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_FILEZILLA_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_FILEZILLA_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_FILEZILLA_DIR')),
    }
)

```
### 16. `itm_app_firefox.nss`

#### `itm_app_firefox.nss`

```java

//
$APP_FIREFOX_DIR = '@sys.prog\Firefox Developer Edition'
$APP_FIREFOX_EXE = '@APP_FIREFOX_DIR\firefox.exe'
$APP_FIREFOX_TIP = "..."+str.trimstart('@APP_FIREFOX_EXE','@app.dir')
//
$APP_FIREFOX_DIR_CFG = '@user.appdata\Mozilla'
$APP_FIREFOX_DIR_NSS = '@app.dir\NSS\_3_items\user_apps'
$APP_FIREFOX_DIR_SRC = '@user.desktop\my\flow\home\__GOTO__\Apps\app_firefox'

// Context: Taskbar
item(
    title  = ":  &Firefox"
    keys   = "exe"
    type   = 'Taskbar'
    args   = ''
    //
    image  = APP_FIREFOX_EXE
    tip    = [APP_FIREFOX_TIP,TIP3,1.0]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_FIREFOX_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_FIREFOX_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_FIREFOX_EXE')),
        cmd=if(KEYS_EXE_GOTO_CFG,('@APP_FIREFOX_DIR_CFG')),
        cmd=if(KEYS_EXE_GOTO_NSS,('@APP_FIREFOX_DIR_NSS')),
        cmd=if(KEYS_EXE_GOTO_SRC,('@APP_FIREFOX_DIR_SRC')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_FIREFOX_DIR')),
    }
)


```
### 17. `itm_app_gitrestoremtime.nss`

#### `itm_app_gitrestoremtime.nss`

```java

//
$APP_USER_GITRESTOREMTIME_DIR = '@user.desktop\my\flow\home\__GOTO__\Apps\grp_gittools\app_gitrestoremtime\exe'
$APP_USER_GITRESTOREMTIME_EXE = '@APP_USER_GITRESTOREMTIME_DIR\git-restore-mtime.exe'
$APP_USER_GITRESTOREMTIME_TIP = "..."+str.trimstart('@APP_USER_GITRESTOREMTIME_EXE','@app.dir')

//
$BATCH_SEL_AS_STRING = for(i=0, i< sel.count, '"@sel[i]" ')
$BATCH_FINISHED_MSG1 = "TITLE "+"["+sys.datetime("H.M")+"]"+" Finished ... Exiting"
$BATCH_FINISHED_MSG2 = "ECHO. & ECHO. & ECHO Finished && ECHO ---------"
$BATCH_FINISHED_MSG3 = "ECHO Window will close in 10 seconds ... & PING 127.0.0.1 -n 10 > NUL & EXIT"
$BATCH_EXIT_WITH_MSG = '@BATCH_FINISHED_MSG1 && @BATCH_FINISHED_MSG2 && @BATCH_FINISHED_MSG3'

// $BATCH_EXIT_WITH_MESSAGE = "ECHO. & ECHO Finished && ECHO --------- & ECHO. & ECHO. & ECHO Window will close in 10 seconds ...& TITLE Finished & PING 127.0.0.1 -n 10 > NUL & EXIT"
// $BATCH_EXIT_WITH_MESSAGE = "ECHO. & ECHO FINISHED & ECHO. & ECHO Window will close in 10 seconds ...& PING 127.0.0.1 -n 10 > NUL & EXIT"
//
$APP_USER_GITRESTOREMTIME_SELECTION     = '/C (CD /D "@sel.parent") && (@APP_USER_GITRESTOREMTIME_EXE @BATCH_SEL_AS_STRING)'
$APP_USER_GITRESTOREMTIME_SELECTION_V   = '/K (CD /D "@sel.parent") && (@APP_USER_GITRESTOREMTIME_EXE @BATCH_SEL_AS_STRING) && @BATCH_EXIT_WITH_MSG'
$APP_USER_GITRESTOREMTIME_SELECTION_F   = '/C (CD /D "@sel.parent") && (@APP_USER_GITRESTOREMTIME_EXE @BATCH_SEL_AS_STRING) --force'
$APP_USER_GITRESTOREMTIME_SELECTION_F_V = '/K (CD /D "@sel.parent") && (@APP_USER_GITRESTOREMTIME_EXE @BATCH_SEL_AS_STRING) --force && @BATCH_EXIT_WITH_MSG'

// -> gitrestoremtime selection
item(title="&gitrestoremtime: (selection)"
    keys="..."
    image=[E26E,PURPLE]
    image-sel=[E26E,GREEN]
    type='File|Desktop|Dir|Drive'
    where=sel.count>=1
    tip=['@APP_USER_GITRESTOREMTIME_SELECTION_V','@tip.info',0.75]
    admin=(keys.shift() AND (keys.lbutton() OR keys.rbutton()))
    commands{
        cmd-line='@APP_USER_GITRESTOREMTIME_SELECTION_V',
    }
    window='Hidden'
    vis=KEYS_MNU_VISIBILITY_0_DEFAULT
)
// -> gitrestoremtime *
item(title="&gitrestoremtime: *"
    keys="..."
    image=[E26E,PURPLE]
    image-sel=[E26E,GREEN]
    type='Back.Dir|Back.Drive'
    where=sel.count>=1
    tip=['@APP_USER_GITRESTOREMTIME_SELECTION_V','@tip.info',0.75]
    admin=(keys.shift() AND (keys.lbutton() OR keys.rbutton()))
    commands{
        cmd-line='@APP_USER_GITRESTOREMTIME_SELECTION_V',
    }
    window='Hidden'
    vis=KEYS_MNU_VISIBILITY_0_DEFAULT
)

// -> gitrestoremtime selection --force
item(title="&gitrestoremtime: (selection) --force"
    keys="..."
    image=[E26E,ORANGE]
    image-sel=[E26E,RED]
    type='File|Desktop|Dir|Drive'
    where=sel.count>=1
    tip=['@APP_USER_GITRESTOREMTIME_SELECTION_F_V','@tip.info',0.75]
    admin=(keys.shift() AND (keys.lbutton() OR keys.rbutton()))
    commands{
        cmd-line='@APP_USER_GITRESTOREMTIME_SELECTION_F_V',
    }
    window='Hidden'
    vis=KEYS_MNU_VISIBILITY_1_SHIFT
)
// -> gitrestoremtime * --force
item(title="&gitrestoremtime: * --force"
    keys="..."
    image=[E26E,ORANGE]
    image-sel=[E26E,RED]
    type='Back.Dir|Back.Drive'
    where=sel.count>=1
    tip=['@APP_USER_GITRESTOREMTIME_SELECTION_F_V','@tip.info',0.75]
    admin=(keys.shift() AND (keys.lbutton() OR keys.rbutton()))
    commands{
        cmd-line='@APP_USER_GITRESTOREMTIME_SELECTION_F_V',
    }
    window='Hidden'
    vis=KEYS_MNU_VISIBILITY_1_SHIFT
)


```
### 18. `itm_app_gpt4all.nss`

#### `itm_app_gpt4all.nss`

```java

//
$APP_GPT4ALL_DIR = '@user.desktop\my\flow\home\__GOTO__\Apps\app_gpt4all\exe\bin'
$APP_GPT4ALL_EXE = '@APP_GPT4ALL_DIR\chat.exe'
$APP_GPT4ALL_TIP = "..."+str.trimstart('@APP_GPT4ALL_EXE','@app.dir')
//

$APP_GPT4ALL_DIR_CFG = '@user.appdata\nomic.ai'
$APP_GPT4ALL_DIR_NSS = '@app.dir\NSS\_3_items\user_apps'
$APP_GPT4ALL_DIR_SRC = '@user.desktop\my\flow\home\__GOTO__\Apps\app_gpt4all'


// Context: Taskbar
item(
    title  = ":  &GPT4ALL"
    keys   = "exe"
    type   = 'Taskbar'
    args   = ''
    //
    image  = APP_GPT4ALL_EXE
    tip    = [APP_GPT4ALL_TIP,TIP3,1.0]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_GPT4ALL_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_GPT4ALL_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_GPT4ALL_EXE')),
        cmd=if(KEYS_EXE_GOTO_CFG,('@APP_GPT4ALL_DIR_CFG')),
        cmd=if(KEYS_EXE_GOTO_NSS,('@APP_GPT4ALL_DIR_NSS')),
        cmd=if(KEYS_EXE_GOTO_SRC,('@APP_GPT4ALL_DIR_SRC')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_GPT4ALL_DIR')),
    }
)


```
### 19. `itm_app_gsmartcontrol.nss`

#### `itm_app_gsmartcontrol.nss`

```java

// //
// $APP_USER_GSMARTCONTROL_DIR = '@user.desktop\my\flow\home\__GOTO__\Apps\grp_misc\app_gsmartcontrol\exe'
// $APP_USER_GSMARTCONTROL_EXE = '@APP_USER_GSMARTCONTROL_DIR\gsmartcontrol.exe'
// $APP_USER_GSMARTCONTROL_TIP = "..."+str.trimstart('@APP_USER_GSMARTCONTROL_EXE','@app.dir')

// //
// item(
//     title=":  &Gsmartcontrol"
//     keys="exe"
//     type='Taskbar|File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
//     //
//     image=APP_USER_GSMARTCONTROL_EXE
//     tip=[APP_USER_GSMARTCONTROL_TIP,TIP3,0.5]
//     //
//     admin=keys.rbutton()
//     cmd=if(KEYS_EXE_OPEN_EXE,(APP_USER_GSMARTCONTROL_EXE))
//     commands{
//         cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_GSMARTCONTROL_DIR')),
//         cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_GSMARTCONTROL_EXE')),
//         cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_GSMARTCONTROL_DIR')),
//     }
// )



//
$APP_USER_GSMARTCONTROL_DIR = '@user.desktop\my\flow\home\__GOTO__\Apps\grp_misc\app_gsmartcontrol\exe'
$APP_USER_GSMARTCONTROL_EXE = '@APP_USER_GSMARTCONTROL_DIR\gsmartcontrol.exe'
$APP_USER_GSMARTCONTROL_TIP = "..."+str.trimstart('@APP_USER_GSMARTCONTROL_EXE','@app.dir')

// // Context: Explorer
item(
    title  = ":  &Gsmartcontrol"
    keys   = "exe"
    type   = 'Taskbar|File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    args   = '/C (CD /D "@APP_USER_GSMARTCONTROL_DIR") & (start @APP_USER_GSMARTCONTROL_EXE)'
    //
    image  = APP_USER_GSMARTCONTROL_EXE
    tip    = [APP_USER_GSMARTCONTROL_TIP,TIP3,0.5]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('cmd.exe'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_GSMARTCONTROL_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_GSMARTCONTROL_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_GSMARTCONTROL_DIR')),
    }
)
```
### 20. `itm_app_irfanview.nss`

#### `itm_app_irfanview.nss`

```java

//
$APP_USER_IRFANVIEW_DIR = '@user.desktop\my\flow\home\__GOTO__\Apps\app_irfanview\exe'
$APP_USER_IRFANVIEW_EXE = '@APP_USER_IRFANVIEW_DIR\i_view64.exe'
$APP_USER_IRFANVIEW_TIP = "..."+str.trimstart('@APP_USER_IRFANVIEW_EXE','@app.dir')

// context: directory
item(
    title  = ":  &Irfanview"
    keys   = "exe"
    type   = 'Desktop|Dir|Drive|Back.Dir|Back.Drive'
    args   = '/slideshow="@sel.dir"'
    //
    image  = APP_USER_IRFANVIEW_EXE
    tip    = [APP_USER_IRFANVIEW_TIP,TIP3,0.8]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_USER_IRFANVIEW_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_IRFANVIEW_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_IRFANVIEW_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_IRFANVIEW_DIR')),
    }
)
// Context: Taskbar
item(
    title  = ":  &Irfanview"
    keys   = "exe"
    type   = 'Taskbar'
    args   = ''
    //
    image  = APP_USER_IRFANVIEW_EXE
    tip    = [APP_USER_IRFANVIEW_TIP,TIP3,0.8]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_USER_IRFANVIEW_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_IRFANVIEW_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_IRFANVIEW_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_IRFANVIEW_DIR')),
    }
)

```
### 21. `itm_app_kdenlive.nss`

#### `itm_app_kdenlive.nss`

```java

//
$APP_USER_KDENLIVE_DIR = '@user.desktop\my\flow\home\__GOTO__\Apps\app_kdenlive\exe\bin'
$APP_USER_KDENLIVE_EXE = '@APP_USER_KDENLIVE_DIR\kdenlive.exe'
$APP_USER_KDENLIVE_TIP = "..."+str.trimstart('@APP_USER_KDENLIVE_EXE','@app.dir')

// context: directory
item(
    title  = ":  &kdenlive"
    keys   = "exe"
    type   = 'File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    find   = '.ts|.mp4|.mkv|.webm|.mov|.wmv|.avi|.flv|.mpg'
    args   = '"@sel.file"'
    //
    image  = APP_USER_KDENLIVE_EXE
    tip    = [APP_USER_KDENLIVE_TIP,TIP3,0.8]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_USER_KDENLIVE_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_KDENLIVE_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_KDENLIVE_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_KDENLIVE_DIR')),
    }
)
// Context: Taskbar
item(
    title  = ":  &kdenlive"
    keys   = "exe"
    type   = 'Taskbar'
    args   = ''
    //
    image  = APP_USER_KDENLIVE_EXE
    tip    = [APP_USER_KDENLIVE_TIP,TIP3,0.8]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_USER_KDENLIVE_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_KDENLIVE_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_KDENLIVE_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_KDENLIVE_DIR')),
    }
)

```
### 22. `itm_app_libreoffice.nss`

#### `itm_app_libreoffice.nss`

```java

//
$APP_LIBREOFFICE_NSS = '@app.dir\NSS\_3_items\user_apps'
$APP_LIBREOFFICE_SRC = '@user.desktop\my\flow\home\__GOTO__\Apps\app_libreoffice'
//
$APP_USER_LIBREOFFICE_DIR = '@user.desktop\my\flow\home\__GOTO__\Apps\app_libreoffice\exe'
$APP_USER_LIBREOFFICE_EXE = '@APP_USER_LIBREOFFICE_DIR\LibreOfficePortable.exe'
$APP_USER_LIBREOFFICE_TIP = "..."+str.trimstart('@APP_USER_LIBREOFFICE_EXE','@app.dir')
//
$APP_USER_LIBREOFFICE_BASE_EXE = '@APP_USER_LIBREOFFICE_DIR\LibreOfficeBasePortable.exe'
$APP_USER_LIBREOFFICE_CALC_EXE = '@APP_USER_LIBREOFFICE_DIR\LibreOfficeCalcPortable.exe'
$APP_USER_LIBREOFFICE_DRAW_EXE = '@APP_USER_LIBREOFFICE_DIR\LibreOfficeDrawPortable.exe'
$APP_USER_LIBREOFFICE_IMPRESS_EXE = '@APP_USER_LIBREOFFICE_DIR\LibreOfficeImpressPortable.exe'
$APP_USER_LIBREOFFICE_MATH_EXE = '@APP_USER_LIBREOFFICE_DIR\LibreOfficeMathPortable.exe'
$APP_USER_LIBREOFFICE_WRITER_EXE = '@APP_USER_LIBREOFFICE_DIR\LibreOfficeWriterPortable.exe'
//

// Context: File
item(
    title  = ":  &LibreOffice"
    keys   = "exe"
    type   = 'File'
    args   = '"@sel.file"'
    where  = str.equals(sel.file.ext,[".odb", ".ods", ".odg", ".odp", ".odf", ".odt"])
    //
    image  = APP_USER_LIBREOFFICE_EXE
    tip    = [APP_USER_LIBREOFFICE_TIP,TIP3,1.0]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_USER_LIBREOFFICE_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_LIBREOFFICE_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_LIBREOFFICE_EXE')),
        cmd=if(KEYS_EXE_GOTO_NSS,('@APP_LIBREOFFICE_NSS')),
        cmd=if(KEYS_EXE_GOTO_SRC,('@APP_LIBREOFFICE_SRC')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_LIBREOFFICE_DIR')),
    }
)
// Context: Directory
item(
    title  = ":  &LibreOffice"
    keys   = "exe"
    type   = 'Desktop|Dir|Drive|Back.Dir|Back.Drive'
    args   = '"@sel.dir"'
    //
    image  = APP_USER_LIBREOFFICE_EXE
    tip    = [APP_USER_LIBREOFFICE_TIP,TIP3,1.0]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_USER_LIBREOFFICE_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_LIBREOFFICE_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_LIBREOFFICE_EXE')),
        cmd=if(KEYS_EXE_GOTO_NSS,('@APP_LIBREOFFICE_NSS')),
        cmd=if(KEYS_EXE_GOTO_SRC,('@APP_LIBREOFFICE_SRC')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_LIBREOFFICE_DIR')),
    }
)
// Context: Taskbar
item(
    title  = ":  &LibreOffice"
    keys   = "exe"
    type   = 'Taskbar'
    args   = ''
    //
    image  = APP_USER_LIBREOFFICE_EXE
    tip    = [APP_USER_LIBREOFFICE_TIP,TIP3,1.0]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_USER_LIBREOFFICE_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_LIBREOFFICE_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_LIBREOFFICE_EXE')),
        cmd=if(KEYS_EXE_GOTO_NSS,('@APP_LIBREOFFICE_NSS')),
        cmd=if(KEYS_EXE_GOTO_SRC,('@APP_LIBREOFFICE_SRC')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_LIBREOFFICE_DIR')),
    }
)


```
### 23. `itm_app_losslesscut.nss`

#### `itm_app_losslesscut.nss`

```java

//
$APP_USER_LOSSLESSCUT_DIR = '@user.desktop\my\flow\home\__GOTO__\Apps\app_losslesscut\exe'
$APP_USER_LOSSLESSCUT_EXE = '@APP_USER_LOSSLESSCUT_DIR\LosslessCut.exe'
$APP_USER_LOSSLESSCUT_TIP = "..."+str.trimstart('@APP_USER_LOSSLESSCUT_EXE','@app.dir')

// context: directory
item(
    title  = ":  &LosslessCut"
    keys   = "exe"
    type   = 'File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    find   = '.ts|.mp4|.mkv|.webm|.mov|.wmv|.avi|.flv|.mpg'
    args   = '"@sel.file"'
    //
    image  = APP_USER_LOSSLESSCUT_EXE
    tip    = [APP_USER_LOSSLESSCUT_TIP,TIP3,0.8]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_USER_LOSSLESSCUT_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_LOSSLESSCUT_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_LOSSLESSCUT_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_LOSSLESSCUT_DIR')),
    }
)
// Context: Taskbar
item(
    title  = ":  &LosslessCut"
    keys   = "exe"
    type   = 'Taskbar'
    args   = ''
    //
    image  = APP_USER_LOSSLESSCUT_EXE
    tip    = [APP_USER_LOSSLESSCUT_TIP,TIP3,0.8]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_USER_LOSSLESSCUT_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_LOSSLESSCUT_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_LOSSLESSCUT_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_LOSSLESSCUT_DIR')),
    }
)

```
### 24. `itm_app_mypyinterface.nss`

#### `itm_app_mypyinterface.nss`

```java

//
$APP_MYPYINTERFACE_DIR = '@user.desktop\my\flow\home\__GOTO__\Apps\app_mypyinterface\exe'
$APP_MYPYINTERFACE_EXE = '@APP_MYPYINTERFACE_DIR\cursor.exe'
$APP_MYPYINTERFACE_TIP = "..."+str.trimstart('@APP_MYPYINTERFACE_EXE','@app.dir')

//
$APP_MYPYINTERFACE_DIR_CFG = '@user.desktop\my\flow\home\__GOTO__\Apps\app_mypyinterface\data'
$APP_MYPYINTERFACE_DIR_NSS = '@app.dir\NSS\_3_items\user_apps'
$APP_MYPYINTERFACE_DIR_SRC = '@user.desktop\my\flow\home\__GOTO__\Apps\app_mypyinterface'
$APP_MYPYINTERFACE_DIR_USR = '@user.desktop\my\flow\home\__GOTO__\Apps\app_mypyinterface\user'

// Context: Taskbar
item(
    title  = ":  &MyPyInterface"
    keys   = "exe"
    type   = 'Taskbar'
    args   = ''
    //
    image  = APP_MYPYINTERFACE_EXE
    tip    = [APP_MYPYINTERFACE_TIP,TIP3,1.0]
    //
    admin  = KEYS_EXE_ADMIN // [rightclick] -[ctrl, alt, shift]
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_MYPYINTERFACE_EXE"')) // -[ctrl, alt, shift]
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_MYPYINTERFACE_DIR')), // [ctrl + leftclick] -[alt]
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_MYPYINTERFACE_EXE')), // [ctrl + rightclick] -[alt]
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_MYPYINTERFACE_DIR')), // [shift + leftclick] -[ctrl, alt]
        //
        cmd=if(KEYS_EXE_GOTO_CFG,('@APP_MYPYINTERFACE_DIR_CFG')), // [alt + leftclick] -[ctrl, shift]
        cmd=if(KEYS_EXE_GOTO_NSS,('@APP_MYPYINTERFACE_DIR_NSS')), // [alt + shift + leftclick] -[ctrl]
        cmd=if(KEYS_EXE_GOTO_SRC,('@APP_MYPYINTERFACE_DIR_SRC')), // [alt + rightclick] -[ctrl, shift]
    }
)
```
### 25. `itm_app_nirsoft_regfromapp.nss`

#### `itm_app_nirsoft_regfromapp.nss`

```java

//
$APP_USER_REGFROMAPP_USER_DIR = '@user.desktop\my\flow\home\__GOTO__\Apps\grp_nirsoft\app_regfromapp\exe'
$APP_USER_REGFROMAPP_USER_EXE = '@APP_USER_REGFROMAPP_USER_DIR\RegFromApp.exe'
$APP_USER_REGFROMAPP_USER_TIP = "..."+str.trimstart('@APP_USER_REGFROMAPP_USER_EXE','@app.dir')

//
item(title="&RegFromApp"
    keys="exe"
    type='Taskbar|File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    //
    image=APP_USER_REGFROMAPP_USER_EXE
    tip=[APP_USER_REGFROMAPP_USER_TIP,TIP3,0.5]
    //
    admin=keys.rbutton()
    cmd=if(KEYS_EXE_OPEN_EXE,(APP_USER_REGFROMAPP_USER_EXE))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set(APP_USER_REGFROMAPP_USER_DIR)),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set(APP_USER_REGFROMAPP_USER_EXE)),
        cmd=if(KEYS_EXE_OPEN_DIR,(APP_USER_REGFROMAPP_USER_DIR)),
    }
)
```
### 26. `itm_app_nirsoft_registrychangesview.nss`

#### `itm_app_nirsoft_registrychangesview.nss`

```java

//
$APP_USER_REGISTRYCHANGESVIEW_DIR = '@user.desktop\my\flow\home\__GOTO__\Apps\grp_nirsoft\app_registrychangesview\exe'
$APP_USER_REGISTRYCHANGESVIEW_EXE = '@APP_USER_REGISTRYCHANGESVIEW_DIR\RegistryChangesView.exe'
$APP_USER_REGISTRYCHANGESVIEW_TIP = "..."+str.trimstart('@APP_USER_REGISTRYCHANGESVIEW_EXE','@app.dir')

//
item(title="&RegistryChangesView"
    keys="exe"
    type='Taskbar|File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    //
    image=APP_USER_REGISTRYCHANGESVIEW_EXE
    tip=[APP_USER_REGISTRYCHANGESVIEW_TIP,TIP3,0.5]
    //
    admin=keys.rbutton()
    cmd=if(KEYS_EXE_OPEN_EXE,(APP_USER_REGISTRYCHANGESVIEW_EXE))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set(APP_USER_REGISTRYCHANGESVIEW_DIR)),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set(APP_USER_REGISTRYCHANGESVIEW_EXE)),
        cmd=if(KEYS_EXE_OPEN_DIR,(APP_USER_REGISTRYCHANGESVIEW_DIR)),
    }
)


```
### 27. `itm_app_nirsoft_regscanner.nss`

#### `itm_app_nirsoft_regscanner.nss`

```java

//
$APP_USER_REGSCANNER_DIR = '@user.desktop\my\flow\home\__GOTO__\Apps\grp_nirsoft\app_regscanner\exe'
$APP_USER_REGSCANNER_EXE = '@APP_USER_REGSCANNER_DIR\RegScanner.exe'
$APP_USER_REGSCANNER_TIP = "..."+str.trimstart('@APP_USER_REGSCANNER_EXE','@app.dir')

// -> RegScanner
item(title="&RegScanner"
    keys="exe"
    type='Taskbar|File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    //
    image=APP_USER_REGSCANNER_EXE
    tip=[APP_USER_REGSCANNER_TIP,TIP3,0.5]
    //
    admin=keys.rbutton()
    cmd=if(KEYS_EXE_OPEN_EXE,(APP_USER_REGSCANNER_EXE))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set(APP_USER_REGSCANNER_DIR)),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set(APP_USER_REGSCANNER_EXE)),
        cmd=if(KEYS_EXE_OPEN_DIR,(APP_USER_REGSCANNER_DIR)),
    }
)


```
### 28. `itm_app_nirsoft_winexplorer.nss`

#### `itm_app_nirsoft_winexplorer.nss`

```java

//
$APP_USER_WINEXPLORER_DIR = '@user.desktop\my\flow\home\__GOTO__\Apps\grp_nirsoft\app_winexplorer\exe'
$APP_USER_WINEXPLORER_EXE = '@APP_USER_WINEXPLORER_DIR\winexp.exe'
$APP_USER_WINEXPLORER_TIP = "..."+str.trimstart('@APP_USER_WINEXPLORER_EXE','@app.dir')

// -> WinExplorer
item(
    title="&WinExplorer"
    keys="exe"
    type='Taskbar|File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    //
    image=APP_USER_WINEXPLORER_EXE
    tip=[APP_USER_WINEXPLORER_TIP,TIP3,0.5]
    //
    admin=keys.rbutton()
    cmd=if(KEYS_EXE_OPEN_EXE,(APP_USER_WINEXPLORER_EXE))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set(APP_USER_WINEXPLORER_DIR)),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set(APP_USER_WINEXPLORER_EXE)),
        cmd=if(KEYS_EXE_OPEN_DIR,(APP_USER_WINEXPLORER_DIR)),
    }
)

```
### 29. `itm_app_notepad++.nss`

#### `itm_app_notepad++.nss`

```java

//
$APP_USER_NOTEPADPLUSPLUS_DIR = '@user.desktop\my\flow\home\__GOTO__\Apps\app_notepad++\exe'
$APP_USER_NOTEPADPLUSPLUS_EXE = '@APP_USER_NOTEPADPLUSPLUS_DIR\notepad++.exe'
$APP_USER_NOTEPADPLUSPLUS_TIP = "..."+str.trimstart('@APP_USER_NOTEPADPLUSPLUS_EXE','@app.dir')

// -> context: file
item(
    title  = ":  &Notepad++"
    keys   = "exe"
    type   = 'File'
    args   = '"@sel.file"'
    where  = !str.equals(sel.file.ext,[".7z",".zip",".rar",".apk",".bin",".dll",".dmg",".exe",".sys"])
    //
    image  = APP_USER_NOTEPADPLUSPLUS_EXE
    tip    = [APP_USER_NOTEPADPLUSPLUS_TIP,TIP3,0.8]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_USER_NOTEPADPLUSPLUS_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_NOTEPADPLUSPLUS_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_NOTEPADPLUSPLUS_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_NOTEPADPLUSPLUS_DIR')),
    }
)
// context: directory
item(
    title  = ":  &Notepad++"
    keys   = "exe"
    type   = 'Desktop|Dir|Drive|Back.Dir|Back.Drive'
    args   = '"@sel.dir"'
    //
    image  = APP_USER_NOTEPADPLUSPLUS_EXE
    tip    = [APP_USER_NOTEPADPLUSPLUS_TIP,TIP3,0.8]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_USER_NOTEPADPLUSPLUS_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_NOTEPADPLUSPLUS_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_NOTEPADPLUSPLUS_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_NOTEPADPLUSPLUS_DIR')),
    }
)
// Context: Taskbar
item(
    title  = ":  &Notepad++"
    keys   = "exe"
    type   = 'Taskbar'
    args   = ''
    //
    image  = APP_USER_NOTEPADPLUSPLUS_EXE
    tip    = [APP_USER_NOTEPADPLUSPLUS_TIP,TIP3,0.8]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_USER_NOTEPADPLUSPLUS_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_NOTEPADPLUSPLUS_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_NOTEPADPLUSPLUS_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_NOTEPADPLUSPLUS_DIR')),
    }
)

```
### 30. `itm_app_pdf24.nss`

#### `itm_app_pdf24.nss`

```java


//
$APP_PDF24_DIR = '@sys.prog\PDF24'
// $APP_PDF24_EXE = '@APP_PDF24_DIR\pdf24.exe'
$APP_PDF24_EXE = '@APP_PDF24_DIR\pdf24-Toolbox.exe'
$APP_PDF24_TIP = "..."+str.trimstart('@APP_PDF24_EXE','@app.dir')
//
$APP_PDF24_NSS = '@app.dir\NSS\_3_items\user_apps'
$APP_PDF24_SRC = '@user.desktop\my\flow\home\__GOTO__\Apps\app_pdf24'


// Context: File
item(
    title  = ":  &Pdf24"
    keys   = "exe"
    type   = 'File'
    args   = '"@sel.file"'
    where  = str.equals(sel.file.ext,[".pdf"])
    //
    image  = APP_PDF24_EXE
    tip    = [APP_PDF24_TIP,TIP3,1.0]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_PDF24_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_PDF24_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_PDF24_EXE')),
        cmd=if(KEYS_EXE_GOTO_NSS,('@APP_PDF24_NSS')),
        cmd=if(KEYS_EXE_GOTO_SRC,('@APP_PDF24_SRC')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_PDF24_DIR')),
    }
)
// Context: Directory
item(
    title  = ":  &Pdf24"
    keys   = "exe"
    type   = 'Desktop|Dir|Drive|Back.Dir|Back.Drive'
    args   = '--local="@sel.dir"'
    //
    image  = APP_PDF24_EXE
    tip    = [APP_PDF24_TIP,TIP3,1.0]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_PDF24_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_PDF24_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_PDF24_EXE')),
        cmd=if(KEYS_EXE_GOTO_NSS,('@APP_PDF24_NSS')),
        cmd=if(KEYS_EXE_GOTO_SRC,('@APP_PDF24_SRC')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_PDF24_DIR')),
    }
)
// Context: Taskbar
item(
    title  = ":  &Pdf24"
    keys   = "exe"
    type   = 'Taskbar'
    args   = ''
    //
    image  = APP_PDF24_EXE
    tip    = [APP_PDF24_TIP,TIP3,1.0]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_PDF24_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_PDF24_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_PDF24_EXE')),
        cmd=if(KEYS_EXE_GOTO_NSS,('@APP_PDF24_NSS')),
        cmd=if(KEYS_EXE_GOTO_SRC,('@APP_PDF24_SRC')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_PDF24_DIR')),
    }
)


```
### 31. `itm_app_photoshop.nss`

#### `itm_app_photoshop.nss`

```java


//
$APP_PHOTOSHOP_DIR = '@sys.prog\Adobe\Adobe Photoshop 2024'
$APP_PHOTOSHOP_EXE = '@APP_PHOTOSHOP_DIR\Photoshop.exe'
$APP_PHOTOSHOP_TIP = "..."+str.trimstart('@APP_PHOTOSHOP_EXE','@app.dir')

// -> context: file
item(
    title  = "&Photoshop"
    keys   = "exe"
    type   = 'File'
    args   = '"@sel.file"'
    where  = str.equals(sel.file.ext,[".ai", ".apng", ".avif", ".bmp", ".dwf", ".dwg", ".dxf", ".eps", ".exr", ".gif", ".ico", ".iges", ".igs", ".jfi", ".jfif", ".jif", ".jp2", ".jpe", ".jpeg", ".jpf", ".jpg", ".jpm", ".jpx", ".jxr", ".k25", ".mj2", ".nrw", ".pct", ".pdf", ".pic", ".pict", ".png", ".psd", ".raw", ".svg", ".tga", ".tif", ".tiff", ".wdp", ".webp", ".wrl", ".xcf"])
    //
    image  = APP_PHOTOSHOP_EXE
    tip    = [APP_PHOTOSHOP_TIP,TIP3,1.0]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_PHOTOSHOP_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_PHOTOSHOP_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_PHOTOSHOP_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_PHOTOSHOP_DIR')),
    }
)

// context: directory
item(
    title  = ":  &Photoshop"
    keys   = "exe"
    type   = 'Desktop|Dir|Drive|Back.Dir|Back.Drive'
    args   = '--local="@sel.dir"'
    //
    image  = APP_PHOTOSHOP_EXE
    tip    = [APP_PHOTOSHOP_TIP,TIP3,1.0]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_PHOTOSHOP_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_PHOTOSHOP_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_PHOTOSHOP_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_PHOTOSHOP_DIR')),
    }
)
// context: taskbar
item(
    title  = ":  &Photoshop"
    keys   = "exe"
    type   = 'Taskbar'
    args   = ''
    //
    image  = APP_PHOTOSHOP_EXE
    tip    = [APP_PHOTOSHOP_TIP,TIP3,1.0]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_PHOTOSHOP_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_PHOTOSHOP_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_PHOTOSHOP_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_PHOTOSHOP_DIR')),
    }
)





```
### 32. `itm_app_powertoys.nss`

#### `itm_app_powertoys.nss`

```java

//
$APP_POWERTOYS_DIR = '@user.localappdata\PowerToys'
$APP_POWERTOYS_EXE = '@APP_POWERTOYS_DIR\PowerToys.exe'
$APP_POWERTOYS_TIP = "..."+str.trimstart('@APP_POWERTOYS_EXE','@app.dir')
//
$APP_POWERTOYS_DIR_CFG = '@user.localappdata\Microsoft\PowerToys'
$APP_POWERTOYS_DIR_NSS = '@app.dir\NSS\_3_items\user_apps'
$APP_POWERTOYS_DIR_SRC = '@user.desktop\my\flow\home\__GOTO__\Apps\app_powertoys'


// Context: Taskbar
item(
    title  = ":  &PowerToys"
    keys   = "exe"
    type   = 'Taskbar'
    args   = ''
    //
    image  = APP_POWERTOYS_EXE
    tip    = [APP_POWERTOYS_TIP,TIP3,1.0]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_POWERTOYS_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_POWERTOYS_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_POWERTOYS_EXE')),
        cmd=if(KEYS_EXE_GOTO_CFG,('@APP_POWERTOYS_DIR_CFG')),
        cmd=if(KEYS_EXE_GOTO_NSS,('@APP_POWERTOYS_DIR_NSS')),
        cmd=if(KEYS_EXE_GOTO_SRC,('@APP_POWERTOYS_DIR_SRC')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_POWERTOYS_DIR')),
    }
)

```
### 33. `itm_app_qbittorrent.nss`

#### `itm_app_qbittorrent.nss`

```java

//
$APP_USER_QBITTORRENT_DIR = '@user.desktop\my\flow\home\__GOTO__\Apps\app_qbittorrent\exe\app'
$APP_USER_QBITTORRENT_EXE = '@APP_USER_QBITTORRENT_DIR\qbittorrent.exe'
$APP_USER_QBITTORRENT_TIP = "..."+str.trimstart('@APP_USER_QBITTORRENT_EXE','@app.dir')

// Context: Explorer
item(
    title  = ":  &qBittorrent"
    keys   = "exe"
    type   = 'File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    args   = '"@sel.dir"'
    //
    image  = APP_USER_QBITTORRENT_EXE
    tip    = [APP_USER_QBITTORRENT_TIP,TIP3,0.8]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_USER_QBITTORRENT_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_QBITTORRENT_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_QBITTORRENT_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_QBITTORRENT_DIR')),
    }
)
// Context: Taskbar
item(
    title  = ":  &qBittorrent"
    keys   = "exe"
    type   = 'Taskbar'
    args   = ''
    //
    image  = APP_USER_QBITTORRENT_EXE
    tip    = [APP_USER_QBITTORRENT_TIP,TIP3,0.8]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_USER_QBITTORRENT_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_QBITTORRENT_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_QBITTORRENT_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_QBITTORRENT_DIR')),
    }
)

```
### 34. `itm_app_rufus.nss`

#### `itm_app_rufus.nss`

```java

//
$APP_RUFUS_DIR = '@user.desktop\my\flow\home\__GOTO__\Apps\app_rufus\exe'
$APP_RUFUS_EXE = '@APP_RUFUS_DIR\rufus-4.6p.exe'
$APP_RUFUS_TIP = "..."+str.trimstart('@APP_RUFUS_EXE','@app.dir')
//
$APP_RUFUS_DIR_CFG = APP_RUFUS_DIR
$APP_RUFUS_DIR_NSS = '@app.dir\NSS\_3_items\user_apps\app_rufus'
$APP_RUFUS_DIR_SRC = '@user.desktop\my\flow\home\__GOTO__\Apps\app_rufus'
$APP_RUFUS_DIR_USR = '@user.desktop\my\flow\home\__GOTO__\Apps\app_rufus\user'

// Context: Taskbar
item(
    title  = ":  &Rufus"
    keys   = "exe"
    type   = 'Taskbar'
    args   = ''
    //
    image  = APP_RUFUS_EXE
    tip    = [APP_RUFUS_TIP,TIP3,1.0]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_RUFUS_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_RUFUS_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_RUFUS_EXE')),
        cmd=if(KEYS_EXE_GOTO_CFG,('@APP_RUFUS_DIR_CFG')),
        cmd=if(KEYS_EXE_GOTO_NSS,('@APP_RUFUS_DIR_NSS')),
        cmd=if(KEYS_EXE_GOTO_SRC,('@APP_RUFUS_DIR_SRC')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_RUFUS_DIR')),
    }
)

```
### 35. `itm_app_rustdesk.nss`

#### `itm_app_rustdesk.nss`

```java

//
$APP_USER_RUSTDESK_DIR = '@user.desktop\my\flow\home\__GOTO__\Apps\app_rustdesk\exe'
$APP_USER_RUSTDESK_EXE = '@APP_USER_RUSTDESK_DIR\RustDesk.exe'
$APP_USER_RUSTDESK_TIP = "..."+str.trimstart('@APP_USER_RUSTDESK_EXE','@app.dir')

// context: directory
item(
    title  = ":  &RustDesk"
    keys   = "exe"
    type   = 'File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    // find   = '.ts|.mp4|.mkv|.webm|.mov|.wmv|.avi|.flv|.mpg'
    // args   = '"@sel.file"'
    //
    image  = APP_USER_RUSTDESK_EXE
    tip    = [APP_USER_RUSTDESK_TIP,TIP3,0.8]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_USER_RUSTDESK_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_RUSTDESK_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_RUSTDESK_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_RUSTDESK_DIR')),
    }
)
// Context: Taskbar
item(
    title  = ":  &RustDesk"
    keys   = "exe"
    type   = 'Taskbar'
    args   = ''
    //
    image  = APP_USER_RUSTDESK_EXE
    tip    = [APP_USER_RUSTDESK_TIP,TIP3,0.8]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_USER_RUSTDESK_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_RUSTDESK_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_RUSTDESK_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_RUSTDESK_DIR')),
    }
)

```
### 36. `itm_app_sharex.nss`

#### `itm_app_sharex.nss`

```java

//
$APP_USER_SHAREX_DIR = '@user.desktop\my\flow\home\__GOTO__\Apps\app_sharex\exe'
$APP_USER_SHAREX_EXE = '@APP_USER_SHAREX_DIR\ShareX.exe'
$APP_USER_SHAREX_TIP = "..."+str.trimstart('@APP_USER_SHAREX_EXE','@app.dir')

// context: directory
item(
    title  = ":  &ShareX"
    keys   = "exe"
    type   = 'File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    // find   = '.ts|.mp4|.mkv|.webm|.mov|.wmv|.avi|.flv|.mpg'
    // args   = '"@sel.file"'
    //
    image  = APP_USER_SHAREX_EXE
    tip    = [APP_USER_SHAREX_TIP,TIP3,0.8]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_USER_SHAREX_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_SHAREX_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_SHAREX_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_SHAREX_DIR')),
    }
)
// Context: Taskbar
item(
    title  = ":  &ShareX"
    keys   = "exe"
    type   = 'Taskbar'
    args   = ''
    //
    image  = APP_USER_SHAREX_EXE
    tip    = [APP_USER_SHAREX_TIP,TIP3,0.8]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_USER_SHAREX_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_SHAREX_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_SHAREX_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_SHAREX_DIR')),
    }
)

```
### 37. `itm_app_simplewall.nss`

#### `itm_app_simplewall.nss`

```java

//
$APP_USER_SIMPLEWALL_DIR = '@user.desktop\my\flow\home\__GOTO__\Apps\app_simplewall\exe'
$APP_USER_SIMPLEWALL_EXE = '@APP_USER_SIMPLEWALL_DIR\simplewall.exe'
$APP_USER_SIMPLEWALL_TIP = "..."+str.trimstart('@APP_USER_SIMPLEWALL_EXE','@app.dir')

// context: directory
item(
    title  = ":  &Simplewall"
    keys   = "exe"
    type   = 'Desktop|Dir|Drive|Back.Dir|Back.Drive'
    args   = '--local="@sel.dir"'
    //
    image  = APP_USER_SIMPLEWALL_EXE
    tip    = [APP_USER_SIMPLEWALL_TIP,TIP3,0.8]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_USER_SIMPLEWALL_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_SIMPLEWALL_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_SIMPLEWALL_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_SIMPLEWALL_DIR')),
    }
)
// Context: Taskbar
item(
    title  = ":  &Simplewall"
    keys   = "exe"
    type   = 'Taskbar'
    args   = ''
    //
    image  = APP_USER_SIMPLEWALL_EXE
    tip    = [APP_USER_SIMPLEWALL_TIP,TIP3,0.8]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_USER_SIMPLEWALL_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_SIMPLEWALL_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_SIMPLEWALL_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_SIMPLEWALL_DIR')),
    }
)

```
### 38. `itm_app_sourcetree.nss`

#### `itm_app_sourcetree.nss`

```java

//
$APP_USER_SOURCETREE_DIR = '@user.localappdata\SourceTree'
$APP_USER_SOURCETREE_EXE = '@APP_USER_SOURCETREE_DIR\SourceTree.exe'
$APP_USER_SOURCETREE_TIP = "..."+str.trimstart('@APP_USER_SOURCETREE_EXE','@app.dir')

// Context: Explorer
item(
    title  = ":  &SourceTree"
    keys   = "exe"
    type   = 'File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    args   = '"@sel.dir"'
    //
    image  = APP_USER_SOURCETREE_EXE
    tip    = [APP_USER_SOURCETREE_TIP,TIP3,0.8]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_USER_SOURCETREE_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_SOURCETREE_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_SOURCETREE_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_SOURCETREE_DIR')),
    }
)
// Context: Taskbar
item(
    title  = ":  &SourceTree"
    keys   = "exe"
    type   = 'Taskbar'
    args   = ''
    //
    image  = APP_USER_SOURCETREE_EXE
    tip    = [APP_USER_SOURCETREE_TIP,TIP3,0.8]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_USER_SOURCETREE_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_SOURCETREE_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_SOURCETREE_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_SOURCETREE_DIR')),
    }
)

```
### 39. `itm_app_sublimetext.nss`

#### `itm_app_sublimetext.nss`

```java

//
$APP_SUBLIMETEXT_NSS = '@app.dir\NSS\_3_items\user_apps'
$APP_SUBLIMETEXT_SRC = '@user.desktop\my\flow\home\__GOTO__\Apps\app_sublimetext'
//
$APP_USER_SUBLIMETEXT_DIR = '@user.desktop\my\flow\home\__GOTO__\Apps\app_sublimetext\exe'
$APP_USER_SUBLIMETEXT_EXE = '@APP_USER_SUBLIMETEXT_DIR\sublime_text.exe'
$APP_USER_SUBLIMETEXT_TIP = "..."+str.trimstart('@APP_USER_SUBLIMETEXT_EXE','@app.dir')

// -> context: file
item(
    title  = ":  &Sublime Text"
    keys   = "exe"
    type   = 'File'
    args   = '"@sel.file"'
    where  = !str.equals(sel.file.ext,[".7z",".zip",".rar",".apk",".bin",".dll",".dmg",".exe",".sys"])
    //
    image  = APP_USER_SUBLIMETEXT_EXE
    tip    = [APP_USER_SUBLIMETEXT_TIP,TIP3,0.8]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_USER_SUBLIMETEXT_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_SUBLIMETEXT_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_SUBLIMETEXT_EXE')),
        cmd=if(KEYS_EXE_GOTO_NSS,('@APP_SUBLIMETEXT_NSS')),
        cmd=if(KEYS_EXE_GOTO_SRC,('@APP_SUBLIMETEXT_SRC')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_SUBLIMETEXT_DIR')),
    }
)
// context: directory
item(
    title  = ":  &Sublime Text"
    keys   = "exe"
    type   = 'Desktop|Dir|Drive|Back.Dir|Back.Drive'
    args   = '"@sel.dir"'
    //
    image  = APP_USER_SUBLIMETEXT_EXE
    tip    = [APP_USER_SUBLIMETEXT_TIP,TIP3,0.8]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_USER_SUBLIMETEXT_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_SUBLIMETEXT_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_SUBLIMETEXT_EXE')),
        cmd=if(KEYS_EXE_GOTO_NSS,('@APP_SUBLIMETEXT_NSS')),
        cmd=if(KEYS_EXE_GOTO_SRC,('@APP_SUBLIMETEXT_SRC')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_SUBLIMETEXT_DIR')),
    }
)
// Context: Taskbar
item(
    title  = ":  &Sublime Text"
    keys   = "exe"
    type   = 'Taskbar'
    args   = ''
    //
    image  = APP_USER_SUBLIMETEXT_EXE
    tip    = [APP_USER_SUBLIMETEXT_TIP,TIP3,0.8]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_USER_SUBLIMETEXT_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_SUBLIMETEXT_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_SUBLIMETEXT_EXE')),
        cmd=if(KEYS_EXE_GOTO_NSS,('@APP_SUBLIMETEXT_NSS')),
        cmd=if(KEYS_EXE_GOTO_SRC,('@APP_SUBLIMETEXT_SRC')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_SUBLIMETEXT_DIR')),
    }
)


// // THIS DOESN'T WORK, CONTEXT SHOULD BE ADDED IN REGISTRY INSTEAD
// // -> process -> everything64.exe
// item(type='*'
//     where=process.name=="Everything64"
//     //
//     title=":  &Sublime Text"
//     keys=""
//     tip=[APP_USER_SUBLIMETEXT_EXE,TIP3,0.8]
//     image=APP_USER_SUBLIMETEXT_EXE
//     window='Hidden'
//     //
//     admin=keys.rbutton()
//     cmd=if(KEYS_EXE_OPEN_EXE,('"@APP_USER_SUBLIMETEXT_EXE"'))
//     args='@SUBLIME_FILE'
//     //
//     commands{
//         cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_SUBLIMETEXT_DIR')),
//         cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_SUBLIMETEXT_EXE')),
//         cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_SUBLIMETEXT_DIR')),
//     }
// )



```
### 40. `itm_app_sys_calc.nss`

#### `itm_app_sys_calc.nss`

```java

//
$APP_SYS_CALC_DIR = '@sys.dir\System32'
$APP_SYS_CALC_EXE = '@APP_SYS_CALC_DIR\calc.exe'
$APP_SYS_CALC_TIP = '@APP_SYS_CALC_EXE'

// Context: Explorer
item(
    title  = ":  &Calc"
    keys   = "exe"
    type   = 'File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    //
    image  = [E1E7,LOWKEY] image-sel = [E1E7,HOVER]
    tip    = [APP_SYS_CALC_TIP,TIP3,0.5]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_SYS_CALC_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_SYS_CALC_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_SYS_CALC_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_SYS_CALC_DIR')),
    }
)
// Context: Taskbar
item(
    title  = ":  &Calc"
    keys   = "exe"
    type   = 'Taskbar'
    //
    image  = [E1E7,LOWKEY] image-sel = [E1E7,HOVER]
    tip    = [APP_SYS_CALC_TIP,TIP3,0.5]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_SYS_CALC_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_SYS_CALC_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_SYS_CALC_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_SYS_CALC_DIR')),
    }
)


```
### 41. `itm_app_sys_cmd.nss`

#### `itm_app_sys_cmd.nss`

```java

//
$APP_SYS_CMD_DIR = '@sys.dir\System32'
$APP_SYS_CMD_EXE = '@APP_SYS_CMD_DIR\cmd.exe'
$APP_SYS_CMD_TIP = '@APP_SYS_CMD_EXE'

// Context: Explorer
item(
    title  = ":  &Cmd"
    keys   = "exe"
    type   = 'File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    args   = '/K (CD /D "@sel.dir") & (TITLE ^(cmd-prompt:@sys.datetime("H.M")^))'
    //
    image  = APP_SYS_CMD_EXE
    tip    = [APP_SYS_CMD_TIP,TIP3,0.5]
    //
    admin  = KEYS_EXE_ADMIN
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_SYS_CMD_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_SYS_CMD_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_SYS_CMD_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_SYS_CMD_DIR')),
    }
)
// Context: Taskbar
item(
    title  = ":  &Cmd"
    keys   = "exe"
    type   = 'Taskbar'
    args   = '/K (CD /D "@user.desktop") & (TITLE ^(cmd-prompt:@sys.datetime("H.M")^))'
    //
    image  = APP_SYS_CMD_EXE
    tip    = [APP_SYS_CMD_TIP,TIP3,0.5]
    //
    admin  = KEYS_EXE_ADMIN
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_SYS_CMD_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_SYS_CMD_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_SYS_CMD_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_SYS_CMD_DIR')),
    }
)
```
### 42. `itm_app_sys_magnify.nss`

#### `itm_app_sys_magnify.nss`

```java

//
$APP_SYS_MAGNIFY_DIR = '@sys.dir\System32'
$APP_SYS_MAGNIFY_EXE = '@APP_SYS_MAGNIFY_DIR\magnify.exe'
$APP_SYS_MAGNIFY_TIP = '@APP_SYS_MAGNIFY_EXE'

// Context: Explorer
item(
    title  = ":  &Magnify"
    keys   = "exe"
    type   = 'File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    //
    image  = APP_SYS_MAGNIFY_EXE
    tip    = [APP_SYS_MAGNIFY_TIP,TIP3,0.5]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_SYS_MAGNIFY_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_SYS_MAGNIFY_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_SYS_MAGNIFY_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_SYS_MAGNIFY_DIR')),
    }
)
// Context: Taskbar
item(
    title  = ":  &Magnify"
    keys   = "exe"
    type   = 'Taskbar'
    //
    image  = APP_SYS_MAGNIFY_EXE
    tip    = [APP_SYS_MAGNIFY_TIP,TIP3,0.5]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_SYS_MAGNIFY_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_SYS_MAGNIFY_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_SYS_MAGNIFY_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_SYS_MAGNIFY_DIR')),
    }
)
```
### 43. `itm_app_sys_mspaint.nss`

#### `itm_app_sys_mspaint.nss`

```java

//
$APP_SYS_MSPAINT_DIR = '@sys.dir\System32'
$APP_SYS_MSPAINT_EXE = 'mspaint.exe'
$APP_SYS_MSPAINT_TIP = '@APP_SYS_MSPAINT_EXE'

// Context: Explorer
item(
    title  = ":  &Mspaint"
    keys   = "exe"
    type   = 'File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    //
    image  = [E116,LOWKEY] image-sel = [E116,HOVER]
    tip    = [APP_SYS_MSPAINT_TIP,TIP3,0.5]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('mspaint.exe'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_SYS_MSPAINT_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_SYS_MSPAINT_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_SYS_MSPAINT_DIR')),
    }
)
// Context: Taskbar
item(
    title  = ":  &Mspaint"
    keys   = "exe"
    type   = 'Taskbar'
    //
    image  = [E116,LOWKEY] image-sel = [E116,HOVER]
    tip    = [APP_SYS_MSPAINT_TIP,TIP3,0.5]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('mspaint.exe'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_SYS_MSPAINT_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_SYS_MSPAINT_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_SYS_MSPAINT_DIR')),
    }
)
```
### 44. `itm_app_sys_notepad.nss`

#### `itm_app_sys_notepad.nss`

```java

//
$APP_SYS_NOTEPAD_DIR = '@sys.dir\System32'
$APP_SYS_NOTEPAD_EXE = '@APP_SYS_NOTEPAD_DIR\notepad.exe'
$APP_SYS_NOTEPAD_TIP = '@APP_SYS_NOTEPAD_EXE'

// Context: Explorer
item(
    title  = ":  &Notepad"
    keys   = "exe"
    type   = 'File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    //
    image  = APP_SYS_NOTEPAD_EXE
    tip    = [APP_SYS_NOTEPAD_TIP,TIP3,0.5]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_SYS_NOTEPAD_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_SYS_NOTEPAD_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_SYS_NOTEPAD_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_SYS_NOTEPAD_DIR')),
    }
)
// Context: Taskbar
item(
    title  = ":  &Notepad"
    keys   = "exe"
    type   = 'Taskbar'
    //
    image  = APP_SYS_NOTEPAD_EXE
    tip    = [APP_SYS_NOTEPAD_TIP,TIP3,0.5]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_SYS_NOTEPAD_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_SYS_NOTEPAD_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_SYS_NOTEPAD_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_SYS_NOTEPAD_DIR')),
    }
)
```
### 45. `itm_app_sys_osk.nss`

#### `itm_app_sys_osk.nss`

```java

//
$APP_SYS_OSK_DIR = '@sys.dir\System32'
$APP_SYS_OSK_EXE = '@APP_SYS_OSK_DIR\osk.exe'
$APP_SYS_OSK_TIP = '@APP_SYS_OSK_EXE'

// Context: Explorer
item(
    title  = ":  &OnScreen Keyboard"
    keys   = "exe"
    type   = 'File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    //
    image  = APP_SYS_OSK_EXE
    // image  = [E18C,LOWKEY] image-sel = [E18C,HOVER]
    tip    = [APP_SYS_OSK_TIP,TIP3,0.5]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_SYS_OSK_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_SYS_OSK_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_SYS_OSK_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_SYS_OSK_DIR')),
    }
)
// Context: Taskbar
item(
    title  = ":  &OnScreen Keyboard"
    keys   = "exe"
    type   = 'Taskbar'
    //
    image  = APP_SYS_OSK_EXE
    // image  = [E18C,LOWKEY] image-sel = [E18C,HOVER]
    tip    = [APP_SYS_OSK_TIP,TIP3,0.5]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_SYS_OSK_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_SYS_OSK_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_SYS_OSK_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_SYS_OSK_DIR')),
    }
)
```
### 46. `itm_app_sys_powershell.nss`

#### `itm_app_sys_powershell.nss`

```java

//
$APP_SYS_POWERSHELL_DIR = '@sys.dir\System32\WindowsPowerShell\v1.0'
$APP_SYS_POWERSHELL_EXE = '@APP_SYS_POWERSHELL_DIR\powershell.exe'
$APP_SYS_POWERSHELL_TIP = '@APP_SYS_POWERSHELL_EXE'

// Context: Explorer
item(
    title  = ":  &PowerShell"
    keys   = "exe"
    type   = 'File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    args   = '-NoExit -Command (Set-Location -Path "@sel.dir"); & { $Host.UI.RawUI.WindowTitle = \"powershell-prompt:@sys.datetime("H.M")\"}'
    //
    image  = APP_SYS_POWERSHELL_EXE
    tip    = [APP_SYS_POWERSHELL_TIP,TIP3,0.5]
    //
    admin  = keys.rbutton()
    cmd = if(KEYS_EXE_OPEN_EXE,('"@APP_SYS_POWERSHELL_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_SYS_POWERSHELL_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_SYS_POWERSHELL_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_SYS_POWERSHELL_DIR')),
    }
)
// Context: Taskbar
item(
    title  = ":  &PowerShell"
    keys   = "exe"
    type   = 'Taskbar'
    args   = '-NoExit -Command (Set-Location -Path "@user.desktop"); & { $Host.UI.RawUI.WindowTitle = \"powershell-prompt:@sys.datetime("H.M")\"}'
    //
    image  = APP_SYS_POWERSHELL_EXE
    tip    = [APP_SYS_POWERSHELL_TIP,TIP3,0.5]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_SYS_POWERSHELL_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_SYS_POWERSHELL_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_SYS_POWERSHELL_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_SYS_POWERSHELL_DIR')),
    }
)
```
### 47. `itm_app_sys_powershellise.nss`

#### `itm_app_sys_powershellise.nss`

```java

//
$APP_SYS_POWERSHELLISE_DIR = '@sys.dir\System32\WindowsPowerShell\v1.0'
$APP_SYS_POWERSHELLISE_EXE = '@APP_SYS_POWERSHELLISE_DIR\powershell_ise.exe'
$APP_SYS_POWERSHELLISE_TIP = '@APP_SYS_POWERSHELLISE_EXE'

// // Context: Explorer
item(
    title  = ":  &PowerShell ISE"
    keys   = "exe"
    type   = 'File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    args   = '/C (CD /D "@sel.dir") & (start @APP_SYS_POWERSHELLISE_EXE)'
    //
    image  = APP_SYS_POWERSHELLISE_EXE
    tip    = [APP_SYS_POWERSHELLISE_TIP,TIP3,0.5]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('cmd.exe'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_SYS_POWERSHELLISE_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_SYS_POWERSHELLISE_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_SYS_POWERSHELLISE_DIR')),
    }
)
// Context: Taskbar
item(
    title  = ":  &PowerShell ISE"
    keys   = "exe"
    type   = 'Taskbar'
    args   = '/C (CD /D "@user.desktop") & (start @APP_SYS_POWERSHELLISE_EXE)'
    //
    image  = APP_SYS_POWERSHELLISE_EXE
    tip    = [APP_SYS_POWERSHELLISE_TIP,TIP3,0.5]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('cmd.exe'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_SYS_POWERSHELLISE_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_SYS_POWERSHELLISE_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_SYS_POWERSHELLISE_DIR')),
    }
)

```
### 48. `itm_app_sys_regedit.nss`

#### `itm_app_sys_regedit.nss`

```java

//
$APP_SYS_REGEDIT_DIR = '@sys.dir'
$APP_SYS_REGEDIT_EXE = '@APP_SYS_REGEDIT_DIR\regedit.exe'
$APP_SYS_REGEDIT_TIP = '@APP_SYS_REGEDIT_EXE'

// Context: Explorer
item(
    title  = ":  &Regedit"
    keys   = "exe"
    type   = 'File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    //
    image  = APP_SYS_REGEDIT_EXE
    // image  = [E18C,LOWKEY] image-sel = [E18C,HOVER]
    tip    = [APP_SYS_REGEDIT_TIP,TIP3,0.5]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_SYS_REGEDIT_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_SYS_REGEDIT_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_SYS_REGEDIT_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_SYS_REGEDIT_DIR')),
    }
)
// Context: Taskbar
item(
    title  = ":  &Regedit"
    keys   = "exe"
    type   = 'Taskbar'
    //
    image  = APP_SYS_REGEDIT_EXE
    // image  = [E18C,LOWKEY] image-sel = [E18C,HOVER]
    tip    = [APP_SYS_REGEDIT_TIP,TIP3,0.5]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_SYS_REGEDIT_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_SYS_REGEDIT_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_SYS_REGEDIT_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_SYS_REGEDIT_DIR')),
    }
)
```
### 49. `itm_app_sys_taskmanager.nss`

#### `itm_app_sys_taskmanager.nss`

```java

//
$APP_SYS_TASKMGR_DIR = '@sys.dir\System32'
$APP_SYS_TASKMGR_EXE = '@APP_SYS_TASKMGR_DIR\taskmgr.exe'
$APP_SYS_TASKMGR_TIP = '@APP_SYS_TASKMGR_EXE'

// Context: Explorer
item(
    title  = "&Task Manager"
    // keys   = "exe"
    type   = 'File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    //
    // image  = APP_SYS_TASKMGR_EXE
    image  = [E0A4,RED] image-sel = [E0A4,HOVER]
    tip    = [APP_SYS_TASKMGR_TIP,TIP3,0.5]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_SYS_TASKMGR_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_SYS_TASKMGR_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_SYS_TASKMGR_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_SYS_TASKMGR_DIR')),
    }
)
// Context: Taskbar
item(
    title  = "&Task Manager"
    // keys   = "exe"
    type   = 'Taskbar'
    //
    // image  = APP_SYS_TASKMGR_EXE
    image  = [E0A4,RED] image-sel = [E0A4,HOVER]
    tip    = [APP_SYS_TASKMGR_TIP,TIP3,0.5]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_SYS_TASKMGR_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_SYS_TASKMGR_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_SYS_TASKMGR_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_SYS_TASKMGR_DIR')),
    }
)

```
### 50. `itm_app_sysinternals_autoruns.nss`

#### `itm_app_sysinternals_autoruns.nss`

```java

//
$APP_USER_AUTORUNS_DIR = '@user.desktop\my\flow\home\__GOTO__\Apps\grp_sysinternalssuite\app_autoruns\exe'
$APP_USER_AUTORUNS_EXE = '@APP_USER_AUTORUNS_DIR\Autoruns64.exe'
$APP_USER_AUTORUNS_TIP = "..."+str.trimstart('@APP_USER_AUTORUNS_EXE','@app.dir')

//
item(
    title=":  &Autoruns"
    keys="exe"
    type='Taskbar|File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    //
    image=APP_USER_AUTORUNS_EXE
    tip=[APP_USER_AUTORUNS_TIP,TIP3,0.5]
    //
    admin=keys.rbutton()
    cmd=if(KEYS_EXE_OPEN_EXE,(APP_USER_AUTORUNS_EXE))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_AUTORUNS_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_AUTORUNS_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_AUTORUNS_DIR')),
    }
)

```
### 51. `itm_app_sysinternals_diskmon.nss`

#### `itm_app_sysinternals_diskmon.nss`

```java

//
$APP_USER_DISKMON_DIR = '@user.desktop\my\flow\home\__GOTO__\Apps\grp_sysinternalssuite\app_diskmon\exe'
$APP_USER_DISKMON_EXE = '@APP_USER_DISKMON_DIR\Diskmon64.exe'
$APP_USER_DISKMON_TIP = "..."+str.trimstart('@APP_USER_DISKMON_EXE','@app.dir')

// -> Diskmon
item(
    title=":  &Diskmon"
    keys="exe"
    type='Taskbar|File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    //
    image=APP_USER_DISKMON_EXE
    tip=[APP_USER_DISKMON_TIP,TIP3,0.5]
    //
    admin=(keys.rbutton() OR keys.lbutton())
    cmd=if(KEYS_EXE_OPEN_EXE,(APP_USER_DISKMON_EXE))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_DISKMON_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_DISKMON_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_DISKMON_DIR')),
    }
)
```
### 52. `itm_app_sysinternals_procexp.nss`

#### `itm_app_sysinternals_procexp.nss`

```java

//
$APP_USER_PROCEXP_DIR = '@user.desktop\my\flow\home\__GOTO__\Apps\grp_sysinternalssuite\app_procexp\exe'
$APP_USER_PROCEXP_EXE = '@APP_USER_PROCEXP_DIR\procexp64.exe'
$APP_USER_PROCEXP_TIP = "..."+str.trimstart('@APP_USER_PROCEXP_EXE','@app.dir')

// -> Diskmon
item(
    title=":  &Procexp"
    keys="exe"
    type='Taskbar|File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    //
    image=APP_USER_PROCEXP_EXE
    tip=[APP_USER_PROCEXP_TIP,TIP3,0.5]
    //
    admin=keys.rbutton()
    cmd=if(KEYS_EXE_OPEN_EXE,(APP_USER_PROCEXP_EXE))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_PROCEXP_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_PROCEXP_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_PROCEXP_DIR')),
    }
)

```
### 53. `itm_app_sysinternals_tcpview.nss`

#### `itm_app_sysinternals_tcpview.nss`

```java

//
$APP_USER_TCPVIEW_DIR = '@user.desktop\my\flow\home\__GOTO__\Apps\grp_sysinternalssuite\app_tcpview\exe'
$APP_USER_TCPVIEW_EXE = '@APP_USER_TCPVIEW_DIR\tcpview64.exe'
$APP_USER_TCPVIEW_TIP = "..."+str.trimstart('@APP_USER_TCPVIEW_EXE','@app.dir')

//
item(
    title=":  &Tcpview"
    keys="exe"
    type='Taskbar|File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    //
    image=APP_USER_TCPVIEW_EXE
    tip=[APP_USER_TCPVIEW_TIP,TIP3,0.5]
    //
    admin=keys.rbutton()
    cmd=if(KEYS_EXE_OPEN_EXE,(APP_USER_TCPVIEW_EXE))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_TCPVIEW_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_TCPVIEW_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_TCPVIEW_DIR')),
    }
)

```
### 54. `itm_app_telegram.nss`

#### `itm_app_telegram.nss`

```java

//
$APP_USER_TELEGRAM_DIR = '@user.desktop\my\flow\home\__GOTO__\Apps\app_telegram\exe'
$APP_USER_TELEGRAM_EXE = '@APP_USER_TELEGRAM_DIR\Telegram.exe'
$APP_USER_TELEGRAM_TIP = "..."+str.trimstart('@APP_USER_TELEGRAM_EXE','@app.dir')

// context: directory
item(
    title  = ":  &Telegram"
    keys   = "exe"
    type   = 'Desktop|Dir|Drive|Back.Dir|Back.Drive'
    args   = '"@sel.dir"'
    //
    image  = APP_USER_TELEGRAM_EXE
    tip    = [APP_USER_TELEGRAM_TIP,TIP3,0.8]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_USER_TELEGRAM_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_TELEGRAM_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_TELEGRAM_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_TELEGRAM_DIR')),
    }
)
// Context: Taskbar
item(
    title  = ":  &Telegram"
    keys   = "exe"
    type   = 'Taskbar'
    args   = ''
    //
    image  = APP_USER_TELEGRAM_EXE
    tip    = [APP_USER_TELEGRAM_TIP,TIP3,0.8]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_USER_TELEGRAM_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_TELEGRAM_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_TELEGRAM_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_TELEGRAM_DIR')),
    }
)

```
### 55. `itm_app_vlc.nss`

#### `itm_app_vlc.nss`

```java

//
$APP_USER_VLC_DIR = '@user.desktop\my\flow\home\__GOTO__\Apps\app_vlc\exe'
$APP_USER_VLC_EXE = '@APP_USER_VLC_DIR\vlc.exe'
$APP_USER_VLC_TIP = "..."+str.trimstart('@APP_USER_VLC_EXE','@app.dir')

// context: file
item(
    title  = ":  &VLC"
    keys   = "exe"
    type   = 'File'
    args   = '"@sel.file"'
    where  = str.equals(sel.file.ext,[".aac",".flac",".m4a",".mp3",".ogg",".wav",".wma",".mov",".mkv",".mp4"])
    //
    image  = APP_USER_VLC_EXE
    tip    = [APP_USER_VLC_TIP,TIP3,0.8]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_USER_VLC_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_VLC_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_VLC_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_VLC_DIR')),
    }
)
// Context: Taskbar
item(
    title  = ":  &VLC"
    keys   = "exe"
    type   = 'Taskbar'
    args   = ''
    //
    image  = APP_USER_VLC_EXE
    tip    = [APP_USER_VLC_TIP,TIP3,0.8]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_USER_VLC_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_VLC_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_VLC_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_VLC_DIR')),
    }
)

```
### 56. `itm_app_winmerge.nss`

#### `itm_app_winmerge.nss`

```java

//
$APP_USER_WINMERGE_DIR = '@user.desktop\my\flow\home\__GOTO__\Apps\app_winmerge\exe'
$APP_USER_WINMERGE_EXE = '@APP_USER_WINMERGE_DIR\WinMergeU.exe'
$APP_USER_WINMERGE_TIP = "..."+str.trimstart('@APP_USER_WINMERGE_EXE','@app.dir')

// Context: Taskbar
item(
    title  = ":  &Winmerge"
    keys   = "exe"
    type   = 'Taskbar'
    args   = '@user.desktop'
    //
    image  = APP_USER_WINMERGE_EXE
    tip    = [APP_USER_WINMERGE_TIP,TIP3,0.8]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_USER_WINMERGE_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_WINMERGE_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_WINMERGE_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_WINMERGE_DIR')),
    }
)

```
### 57. `itm_app_yvonne.nss`

#### `itm_app_yvonne.nss`

```java

//
$APP_YVONNE_DIR = '@user.desktop\my\flow\home\__GOTO__\Apps\app_yvonne'
$APP_YVONNE_EXE = '@APP_YVONNE_DIR\cursor.exe'
$APP_YVONNE_TIP = "..."+str.trimstart('@APP_YVONNE_EXE','@app.dir')

//
$APP_YVONNE_DIR_CFG = '@user.desktop\my\flow\home\__GOTO__\Apps\app_yvonne\data'
$APP_YVONNE_DIR_NSS = '@app.dir\NSS\_3_items\user_apps'
$APP_YVONNE_DIR_SRC = '@user.desktop\my\flow\home\__GOTO__\Apps\app_yvonne'
$APP_YVONNE_DIR_USR = '@user.desktop\my\flow\home\__GOTO__\Apps\app_yvonne\user'

// Context: Taskbar
item(
    title  = ":  &Yvonne"
    keys   = "exe"
    type   = 'Taskbar'
    args   = ''
    //
    image  = APP_YVONNE_EXE
    tip    = [APP_YVONNE_TIP,TIP3,1.0]
    //
    admin  = KEYS_EXE_ADMIN // [rightclick] -[ctrl, alt, shift]
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_YVONNE_EXE"')) // -[ctrl, alt, shift]
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_YVONNE_DIR')), // [ctrl + leftclick] -[alt]
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_YVONNE_EXE')), // [ctrl + rightclick] -[alt]
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_YVONNE_DIR')), // [shift + leftclick] -[ctrl, alt]
        //
        cmd=if(KEYS_EXE_GOTO_CFG,('@APP_YVONNE_DIR_CFG')), // [alt + leftclick] -[ctrl, shift]
        cmd=if(KEYS_EXE_GOTO_NSS,('@APP_YVONNE_DIR_NSS')), // [alt + shift + leftclick] -[ctrl]
        cmd=if(KEYS_EXE_GOTO_SRC,('@APP_YVONNE_DIR_SRC')), // [alt + rightclick] -[ctrl, shift]
    }
)
```
### 58. `itm_bat_pyvenvterminal.nss`

#### `itm_bat_pyvenvterminal.nss`

```java

//
$PY_VENV_TERMINAL_BAT = path.full('@app.dir\LIB\bat\py_venv_terminal.bat')
item(
    title=path.name(PY_VENV_TERMINAL_BAT)
    keys="bat"
    type='File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    //
    image=[E17C,GREY] image-sel=[E17C,PURPLE]
    tip=[PY_VENV_TERMINAL_BAT,TIP5,0.5]
    //
    // admin=keys.rbutton()
    cmd='"@PY_VENV_TERMINAL_BAT"'
    args='"@sel.dir"'
)


```
### 59. `itm_bat_pyvenvwriterequirements.nss`

#### `itm_bat_pyvenvwriterequirements.nss`

```java

//
$PY_VENV_WRITE_REQUIREMENTS_BAT = path.full('@app.dir\LIB\bat\py_venv_write_requirements.bat')
item(
    title=path.name(PY_VENV_WRITE_REQUIREMENTS_BAT)
    keys="bat"
    type='File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    //
    image=[E17C,GREY] image-sel=[E17C,PURPLE]
    tip=[PY_VENV_WRITE_REQUIREMENTS_BAT,TIP5,0.5]
    //
    admin=keys.rbutton()
    cmd='"@PY_VENV_WRITE_REQUIREMENTS_BAT"'
    args='"@sel.dir"'
)


```
### 60. `itm_dir_jorn_nss.nss`

#### `itm_dir_jorn_nss.nss`

```java

//

//
item(
    title="&Nss"
    keys="/"
    type='Taskbar|File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    //
    // image=[E0E7,WHITE] image-sel=[E0E8,ORANGE]
    // image=[E0E7,WHITE] image-sel=[E0E8,ORANGE]
    // image=["\uE1EA",WHITE] image-sel=["\uE1EA",ORANGE]
    // image=[E0CE,WHITE] image-sel=[E0E8,ORANGE]
    image=[E0E0,WHITE1] image-sel=[E0E0,GREEN]
    tip=['@app.dir\NSS',TIP1,0.5]
    //
    commands{
        cmd=if(KEYS_DIR_COPY,clipboard.set('@app.dir\NSS')),
        cmd=if(KEYS_DIR_GOTO,command.navigate('@app.dir\NSS')),
        cmd=if(KEYS_DIR_OPEN,('@app.dir\NSS')),
    }
)
```
### 61. `itm_dir_jorn_scratch.nss`

#### `itm_dir_jorn_scratch.nss`

```java

//
$DIR_SCRATCH = '@user.desktop\SCRATCH'
$TIP_SCRATCH = "..."+str.trimstart('@DIR_SCRATCH','@user.directory')

//
item(
    title="&Scratch"
    keys="/"
    type='Taskbar|File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    //
    // image=[E0E7,WHITE] image-sel=[E0E8,ORANGE]
    // image=[E0E7,WHITE] image-sel=[E0E8,ORANGE]
    // image=["\uE1EA",WHITE] image-sel=["\uE1EA",ORANGE]
    // image=[E0CE,WHITE] image-sel=[E0E8,ORANGE]
    image=[E0E0,ORANGE2] image-sel=[E0E0,ORANGE]
    tip=[TIP_SCRATCH,TIP1,0.5]
    //
    commands{
        cmd=if(KEYS_DIR_COPY,clipboard.set('@DIR_SCRATCH')),
        cmd=if(KEYS_DIR_GOTO,command.navigate('@DIR_SCRATCH')),
        cmd=if(KEYS_DIR_OPEN,('@DIR_SCRATCH')),
    }
)
```
### 62. `itm_dir_jorn_share.nss`

#### `itm_dir_jorn_share.nss`

```java

//
$DIR_SHARE = '@user.desktop\SHARE'
$TIP_SHARE = "..."+str.trimstart('@DIR_SHARE','@user.directory')

//
item(
    title="&Share"
    keys="/"
    type='Taskbar|File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    //
    // image=[E0E7,WHITE] image-sel=[E0E8,ORANGE]
    // image=[E0E7,WHITE] image-sel=[E0E8,ORANGE]
    // image=["\uE1EA",WHITE] image-sel=["\uE1EA",ORANGE]
    // image=[E0CE,WHITE] image-sel=[E0E8,ORANGE]
    image=[E0E0,WHITE1] image-sel=[E0E0,ORANGE]
    tip=[TIP_SHARE,TIP1,0.5]
    //
    commands{
        cmd=if(KEYS_DIR_COPY,clipboard.set('@DIR_SHARE')),
        cmd=if(KEYS_DIR_GOTO,command.navigate('@DIR_SHARE')),
        cmd=if(KEYS_DIR_OPEN,('@DIR_SHARE')),
    }
)
```
### 63. `itm_dir_sys_appdata.nss`

#### `itm_dir_sys_appdata.nss`

```java

//
$DIR_SYS_APPDATA = '@user.appdata'

//
item(
    title="&APPDATA"
    keys="/"
    type='Taskbar|File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    //
    image=[E09E,GREY] image-sel=[E09E,GREEN]
    tip=[DIR_SYS_APPDATA,TIP1,0.5]
    //
    commands{
        cmd=if(KEYS_DIR_COPY,clipboard.set('@DIR_SYS_APPDATA')),
        cmd=if(KEYS_DIR_GOTO,command.navigate('@DIR_SYS_APPDATA')),
        cmd=if(KEYS_DIR_OPEN,('@DIR_SYS_APPDATA')),
    }
)


```
### 64. `itm_dir_sys_desktop.nss`

#### `itm_dir_sys_desktop.nss`

```java

//
$DIR_SYS_DESKTOP = '@user.desktop'

//
item(
    title="&Desktop"
    keys="/"
    type='Taskbar|File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    //
    image=[E1A0,GREY] image-sel=[E1A0,GREEN]
    tip=[DIR_SYS_DESKTOP,TIP1,0.5]
    //
    commands{
        cmd=if(KEYS_DIR_COPY,clipboard.set('@DIR_SYS_DESKTOP')),
        cmd=if(KEYS_DIR_GOTO,command.navigate('@DIR_SYS_DESKTOP')),
        cmd=if(KEYS_DIR_OPEN,('@DIR_SYS_DESKTOP')),
    }
)
```
### 65. `itm_dir_sys_documents.nss`

#### `itm_dir_sys_documents.nss`

```java

//
$DIR_SYS_DOCUMENTS = '@user.documents'

//
item(
    title="&Documents"
    keys="/"
    type='Taskbar|File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    //
    image=[E0E7,GREY] image-sel=[E0E7,GREEN]
    tip=[DIR_SYS_DOCUMENTS,TIP1,0.5]
    //
    commands{
        cmd=if(KEYS_DIR_COPY,clipboard.set('@DIR_SYS_DOCUMENTS')),
        cmd=if(KEYS_DIR_GOTO,command.navigate('@DIR_SYS_DOCUMENTS')),
        cmd=if(KEYS_DIR_OPEN,('@DIR_SYS_DOCUMENTS')),
    }
)
```
### 66. `itm_dir_sys_downloads.nss`

#### `itm_dir_sys_downloads.nss`

```java

//
$DIR_SYS_DOWNLOADS = '@user.downloads'

//
item(
    title="&Documents"
    keys="/"
    type='Taskbar|File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    //
    image=[E0BD,GREY] image-sel=[E0BD,GREEN]
    tip=[DIR_SYS_DOWNLOADS,TIP1,0.5]
    //
    commands{
        cmd=if(KEYS_DIR_COPY,clipboard.set('@DIR_SYS_DOWNLOADS')),
        cmd=if(KEYS_DIR_GOTO,command.navigate('@DIR_SYS_DOWNLOADS')),
        cmd=if(KEYS_DIR_OPEN,('@DIR_SYS_DOWNLOADS')),
    }
)
```
### 67. `itm_dir_sys_programfiles.nss`

#### `itm_dir_sys_programfiles.nss`

```java

//
$DIR_SYS_PROGRAMFILES = '@sys.prog'

//
item(
    title="&PROGRAMFILES"
    keys="/"
    type='Taskbar|File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    //
    image=[E09F,GREY] image-sel=[E09F,GREEN]
    tip=[DIR_SYS_PROGRAMFILES,TIP1,0.5]
    //
    commands{
        cmd=if(KEYS_DIR_COPY,clipboard.set('@DIR_SYS_PROGRAMFILES')),
        cmd=if(KEYS_DIR_GOTO,command.navigate('@DIR_SYS_PROGRAMFILES')),
        cmd=if(KEYS_DIR_OPEN,('@DIR_SYS_PROGRAMFILES')),
    }
)
```
### 68. `itm_dir_sys_recent.nss`

#### `itm_dir_sys_recent.nss`

```java

//
$DIR_SYS_RECENT = '@user.appdata\Microsoft\Windows\Recent'

//
item(
    title="&Recent"
    keys="/"
    type='Taskbar|File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    //
    image=[E12A,GREY] image-sel=[E12A,GREEN]
    tip=[DIR_SYS_RECENT,TIP1,0.5]
    //
    commands{
        cmd=if(KEYS_DIR_COPY,clipboard.set('@DIR_SYS_RECENT')),
        cmd=if(KEYS_DIR_GOTO,command.navigate('@DIR_SYS_RECENT')),
        cmd=if(KEYS_DIR_OPEN,('@DIR_SYS_RECENT')),
    }
)
```
### 69. `itm_dir_sys_recyclebin.nss`

#### `itm_dir_sys_recyclebin.nss`

```java

//
$DIR_SYS_RECYCLEBIN = 'shell:::{645ff040-5081-101b-9f08-00aa002f954e}'

//
item(
    title="&Recycle Bin"
    keys="/"
    type='Taskbar|File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    //
    image=[E0B4,GREY] image-sel=[E0B4,GREEN]
    tip=[DIR_SYS_RECYCLEBIN,TIP1,0.5]
    //
    commands{
        cmd=if(KEYS_DIR_COPY,clipboard.set('@DIR_SYS_RECYCLEBIN')),
        cmd=if(KEYS_DIR_GOTO,command.navigate('@DIR_SYS_RECYCLEBIN')),
        cmd=if(KEYS_DIR_OPEN,('@DIR_SYS_RECYCLEBIN')),
    }
)
```
### 70. `itm_dir_sys_startup.nss`

#### `itm_dir_sys_startup.nss`

```java

//
$DIR_SYS_STARTUP = '@user.startmenu\Programs\Startup'

//
item(
    title="&Startup"
    keys="/"
    type='Taskbar|File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    //
    image=[E092,GREY] image-sel=[E092,GREEN]
    tip=[DIR_SYS_STARTUP,TIP1,0.5]
    //
    commands{
        cmd=if(KEYS_DIR_COPY,clipboard.set('@DIR_SYS_STARTUP')),
        cmd=if(KEYS_DIR_GOTO,command.navigate('@DIR_SYS_STARTUP')),
        cmd=if(KEYS_DIR_OPEN,('@DIR_SYS_STARTUP')),
    }
)
```
### 71. `itm_dir_sys_thispc.nss`

#### `itm_dir_sys_thispc.nss`

```java

//
$DIR_SYS_THISPC = 'file:/'

//
item(
    title="&This PC"
    keys="/"
    type='Taskbar|File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    //
    image=[E1D9,GREY] image-sel=[E1D9,GREEN]
    // image=[E1D9,WHITE1] image-sel=[E1D9,WHITE]
    tip=[DIR_SYS_THISPC,TIP1,0.5]
    //
    commands{
        cmd=if(KEYS_DIR_COPY,clipboard.set('@DIR_SYS_THISPC')),
        cmd=if(KEYS_DIR_GOTO,command.navigate('@DIR_SYS_THISPC')),
        cmd=if(KEYS_DIR_OPEN,('@DIR_SYS_THISPC')),
    }
)
```
### 72. `itm_dir_sys_userprofile.nss`

#### `itm_dir_sys_userprofile.nss`

```java

//
$DIR_SYS_USERPROFILE = '@user.directory'

//
item(
    title="&USERPROFILE"
    keys="/"
    type='Taskbar|File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    //
    image=[E09F,GREY] image-sel=[E09F,GREEN]
    tip=[DIR_SYS_USERPROFILE,TIP1,0.5]
    //
    commands{
        cmd=if(KEYS_DIR_COPY,clipboard.set('@DIR_SYS_USERPROFILE')),
        cmd=if(KEYS_DIR_GOTO,command.navigate('@DIR_SYS_USERPROFILE')),
        cmd=if(KEYS_DIR_OPEN,('@DIR_SYS_USERPROFILE')),
    }
)
```
### 73. `itm_dir_sys_userprofile_ssh.nss`

#### `itm_dir_sys_userprofile_ssh.nss`

```java

//
$DIR_SYS_USERPROFILE_SSH = '@user.directory\.ssh'

//
item(
    title=".&ssh"
    keys="/"
    type='Taskbar|File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    //
    image=[E09F,GREY] image-sel=[E09F,GREEN]
    tip=[DIR_SYS_USERPROFILE_SSH,TIP1,0.5]
    //
    commands{
        cmd=if(KEYS_DIR_COPY,clipboard.set('@DIR_SYS_USERPROFILE_SSH')),
        cmd=if(KEYS_DIR_GOTO,command.navigate('@DIR_SYS_USERPROFILE_SSH')),
        cmd=if(KEYS_DIR_OPEN,('@DIR_SYS_USERPROFILE_SSH')),
    }
)
```
### 74. `itm_py_audioandvideocombiner.nss`

#### `itm_py_audioandvideocombiner.nss`

```java

//
$PY_AUDIOANDVIDEOCOMBINER_DIR = '@user.desktop\my\flow\home\__GOTO__\Scripts\Python\Py_Cli_Utils\py__AudioAndVideoCombiner'
$PY_AUDIOANDVIDEOCOMBINER_EXE = '@PY_AUDIOANDVIDEOCOMBINER_DIR\venv\Scripts\python.exe'
$PY_AUDIOANDVIDEOCOMBINER_APP = '@PY_AUDIOANDVIDEOCOMBINER_DIR\main.py'
//

// Context: Explorer
$PY_AUDIOANDVIDEOCOMBINER_ARGS = 'input_video "@sel.file" --prompt'
item(
    title="&AudioAndVideoCombiner"
    keys="py"
    type='File'
    where=str.equals(sel.file.ext,[".m4a",".webm",".wmv",".wav",".f4v",".mov",".mkv",".mp4"])
    //
    image=[E17C,GREY] image-sel=[E17C,PURPLE]
    tip=['"@PY_AUDIOANDVIDEOCOMBINER_APP" @PY_AUDIOANDVIDEOCOMBINER_ARGS',TIP3,0.75]
    //
    admin=keys.rbutton()
    cmd=if(KEYS_EXE_OPEN_EXE,('"@PY_AUDIOANDVIDEOCOMBINER_EXE"'))
    args='"@PY_AUDIOANDVIDEOCOMBINER_APP" @PY_AUDIOANDVIDEOCOMBINER_ARGS'
    //
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@PY_AUDIOANDVIDEOCOMBINER_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@PY_AUDIOANDVIDEOCOMBINER_APP')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@PY_AUDIOANDVIDEOCOMBINER_DIR')),
    }
)

// Context: Taskbar
$PY_AUDIOANDVIDEOCOMBINER_TASKBAR = '--prompt'
item(
    title="&AudioAndVideoCombiner"
    keys="py"
    type='Taskbar'
    //
    image=[E17C,GREY] image-sel=[E17C,PURPLE]
    tip=['"@PY_AUDIOANDVIDEOCOMBINER_APP" @PY_AUDIOANDVIDEOCOMBINER_TASKBAR',TIP3,0.75]
    //
    admin=keys.rbutton()
    args='"@PY_AUDIOANDVIDEOCOMBINER_APP" @PY_AUDIOANDVIDEOCOMBINER_TASKBAR'
    cmd=if(KEYS_EXE_OPEN_EXE,('"@PY_AUDIOANDVIDEOCOMBINER_EXE"'))
    //
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@PY_AUDIOANDVIDEOCOMBINER_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@PY_AUDIOANDVIDEOCOMBINER_APP')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@PY_AUDIOANDVIDEOCOMBINER_DIR')),
    }
)
```
### 75. `itm_py_bookmarkfolderizer.nss`

#### `itm_py_bookmarkfolderizer.nss`

```java

//
$PY_BOOKMARKFOLDERIZER_DIR = '@user.desktop\my\flow\home\__GOTO__\Scripts\Python\Py_Cli_Utils\py__BookmarkFolderizer'
$PY_BOOKMARKFOLDERIZER_EXE = '@PY_BOOKMARKFOLDERIZER_DIR\venv\Scripts\python.exe'
$PY_BOOKMARKFOLDERIZER_APP = '@PY_BOOKMARKFOLDERIZER_DIR\main.py'
//

// Context: Explorer
$PY_BOOKMARKFOLDERIZER_EXPLORER = '-i "bookmarks.html" -op "@sel.dir" --prompt'
item(
    title="&BookmarkFolderizer"
    keys="py"
    type='File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    //
    image=[E17C,GREY] image-sel=[E17C,PURPLE]
    tip=['"@PY_BOOKMARKFOLDERIZER_APP" @PY_BOOKMARKFOLDERIZER_EXPLORER',TIP3,0.75]
    //
    admin=keys.rbutton()
    cmd=if(KEYS_EXE_OPEN_EXE,('"@PY_BOOKMARKFOLDERIZER_EXE"'))
    args='"@PY_BOOKMARKFOLDERIZER_APP" @PY_BOOKMARKFOLDERIZER_EXPLORER'
    //
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@PY_BOOKMARKFOLDERIZER_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@PY_BOOKMARKFOLDERIZER_APP')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@PY_BOOKMARKFOLDERIZER_DIR')),
    }
)
// Context: Taskbar
$PY_BOOKMARKFOLDERIZER_TASKBAR = '-i "bookmarks.html" -op "@user.desktop" --prompt'
item(
    title="&BookmarkFolderizer"
    keys="py"
    type='Taskbar'
    //
    image=[E17C,GREY]
    image-sel=[E17C,PURPLE]
    //
    admin=keys.rbutton()
    cmd=if(KEYS_EXE_OPEN_EXE,('"@PY_BOOKMARKFOLDERIZER_EXE"'))
    args='"@PY_BOOKMARKFOLDERIZER_APP" @PY_BOOKMARKFOLDERIZER_TASKBAR'
    tip=['"@PY_BOOKMARKFOLDERIZER_APP" @PY_BOOKMARKFOLDERIZER_TASKBAR',TIP3,0.75]
    //
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@PY_BOOKMARKFOLDERIZER_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@PY_BOOKMARKFOLDERIZER_APP')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@PY_BOOKMARKFOLDERIZER_DIR')),
    }
)
```
### 76. `itm_py_closeduplicatewindows.nss`

#### `itm_py_closeduplicatewindows.nss`

```java

//
$PY_CLOSEDUPLICATEWINDOWS_DIR = '@user.desktop\my\flow\home\__GOTO__\Scripts\Python\Py_Cli_Utils\py__CloseDuplicateWindows'
$PY_CLOSEDUPLICATEWINDOWS_EXE = '@PY_CLOSEDUPLICATEWINDOWS_DIR\venv\Scripts\python.exe'
$PY_CLOSEDUPLICATEWINDOWS_APP = '@PY_CLOSEDUPLICATEWINDOWS_DIR\main.py'
//

// Context: Explorer
$PY_CLOSEDUPLICATEWINDOWS_ARGS = '--prompt'
item(
    title="&CloseDuplicateWindows"
    keys="py"
    type='File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    //
    image=[E17C,GREY] image-sel=[E17C,PURPLE]
    tip=['"@PY_CLOSEDUPLICATEWINDOWS_APP" @PY_CLOSEDUPLICATEWINDOWS_ARGS',TIP3,0.75]
    //
    admin=keys.rbutton()
    cmd=if(KEYS_EXE_OPEN_EXE,('"@PY_CLOSEDUPLICATEWINDOWS_EXE"'))
    args='"@PY_CLOSEDUPLICATEWINDOWS_APP" @PY_CLOSEDUPLICATEWINDOWS_ARGS'
    //
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@PY_CLOSEDUPLICATEWINDOWS_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@PY_CLOSEDUPLICATEWINDOWS_APP')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@PY_CLOSEDUPLICATEWINDOWS_DIR')),
    }
)
// Context: Taskbar
$PY_CLOSEDUPLICATEWINDOWS_TASKBAR = '--prompt'
item(
    title="&CloseDuplicateWindows"
    keys="py"
    type='Taskbar'
    //
    image=[E17C,GREY]
    image-sel=[E17C,PURPLE]
    //
    admin=keys.rbutton()
    cmd=if(KEYS_EXE_OPEN_EXE,('"@PY_CLOSEDUPLICATEWINDOWS_EXE"'))
    args='"@PY_CLOSEDUPLICATEWINDOWS_APP" @PY_CLOSEDUPLICATEWINDOWS_TASKBAR'
    tip=['"@PY_CLOSEDUPLICATEWINDOWS_APP" @PY_CLOSEDUPLICATEWINDOWS_TASKBAR',TIP3,0.75]
    //
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@PY_CLOSEDUPLICATEWINDOWS_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@PY_CLOSEDUPLICATEWINDOWS_APP')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@PY_CLOSEDUPLICATEWINDOWS_DIR')),
    }
)

```
### 77. `itm_py_environmentvariablesmanager.nss`

#### `itm_py_environmentvariablesmanager.nss`

```java

//
$PY_ENVIRONMENTVARIABLESMANAGER_DIR = '@user.desktop\my\flow\home\__GOTO__\Scripts\Python\Py_Cli_Utils\py__EnvironmentVariablesManager'
$PY_ENVIRONMENTVARIABLESMANAGER_EXE = '@PY_ENVIRONMENTVARIABLESMANAGER_DIR\venv\Scripts\python.exe'
$PY_ENVIRONMENTVARIABLESMANAGER_APP = '@PY_ENVIRONMENTVARIABLESMANAGER_DIR\main.py'

// Context: Explorer
$PY_ENVIRONMENTVARIABLESMANAGER_EXPLORER = '--prompt'
item(
    title="&EnvironmentVariablesManager"
    keys="py"
    type='File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    //
    image=[E17C,GREY] image-sel=[E17C,PURPLE]
    tip=['"@PY_ENVIRONMENTVARIABLESMANAGER_APP" @PY_ENVIRONMENTVARIABLESMANAGER_EXPLORER',TIP3,0.75]
    //
    admin=keys.rbutton()
    cmd=if(KEYS_EXE_OPEN_EXE,('"@PY_ENVIRONMENTVARIABLESMANAGER_EXE"'))
    args='"@PY_ENVIRONMENTVARIABLESMANAGER_APP" @PY_ENVIRONMENTVARIABLESMANAGER_EXPLORER'
    //
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@PY_ENVIRONMENTVARIABLESMANAGER_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@PY_ENVIRONMENTVARIABLESMANAGER_APP')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@PY_ENVIRONMENTVARIABLESMANAGER_DIR')),
    }
)
// Context: Taskbar
$PY_ENVIRONMENTVARIABLESMANAGER_TASKBAR = '--prompt'
item(
    title="&EnvironmentVariablesManager"
    keys="py"
    type='Taskbar'
    //
    image=[E17C,GREY] image-sel=[E17C,PURPLE]
    tip=['"@PY_ENVIRONMENTVARIABLESMANAGER_APP" @PY_ENVIRONMENTVARIABLESMANAGER_TASKBAR',TIP3,0.75]
    //
    admin=keys.rbutton()
    args='"@PY_ENVIRONMENTVARIABLESMANAGER_APP" @PY_ENVIRONMENTVARIABLESMANAGER_TASKBAR'
    cmd=if(KEYS_EXE_OPEN_EXE,('"@PY_ENVIRONMENTVARIABLESMANAGER_EXE"'))
    //
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@PY_ENVIRONMENTVARIABLESMANAGER_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@PY_ENVIRONMENTVARIABLESMANAGER_APP')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@PY_ENVIRONMENTVARIABLESMANAGER_DIR')),
    }
)
```
### 78. `itm_py_gitfilterrepo.nss`

#### `itm_py_gitfilterrepo.nss`

```java

//
$PY_GITFILTERREPO_DIR = '@user.desktop\my\flow\home\__GOTO__\Scripts\Python\Py_Cli_Utils\py__GitFilterRepo'
$PY_GITFILTERREPO_EXE = '@PY_GITFILTERREPO_DIR\venv\Scripts\python.exe'
$PY_GITFILTERREPO_APP = '@PY_GITFILTERREPO_DIR\main.py'
//

// Context: Explorer
$PY_GITFILTERREPO_EXPLORER = '-i "@sel.dir" --prompt'
item(
    title="&GitFilterRepo"
    keys="py"
    type='File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    //
    image=[E17C,GREY] image-sel=[E17C,PURPLE]
    tip=['"@PY_GITFILTERREPO_APP" @PY_GITFILTERREPO_EXPLORER',TIP3,0.75]
    //
    admin=keys.rbutton()
    cmd=if(KEYS_EXE_OPEN_EXE,('"@PY_GITFILTERREPO_EXE"'))
    args='"@PY_GITFILTERREPO_APP" @PY_GITFILTERREPO_EXPLORER'
    //
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@PY_GITFILTERREPO_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@PY_GITFILTERREPO_APP')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@PY_GITFILTERREPO_DIR')),
    }
)
// Context: Taskbar
$PY_GITFILTERREPO_TASKBAR = '-i "" --prompt'
item(
    title="&GitFilterRepo"
    keys="py"
    type='Taskbar'
    //
    image=[E17C,GREY]
    image-sel=[E17C,PURPLE]
    //
    admin=keys.rbutton()
    cmd=if(KEYS_EXE_OPEN_EXE,('"@PY_GITFILTERREPO_EXE"'))
    args='"@PY_GITFILTERREPO_APP" @PY_GITFILTERREPO_TASKBAR'
    tip=['"@PY_GITFILTERREPO_APP" @PY_GITFILTERREPO_TASKBAR',TIP3,0.75]
    //
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@PY_GITFILTERREPO_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@PY_GITFILTERREPO_APP')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@PY_GITFILTERREPO_DIR')),
    }
)

```
### 79. `itm_py_gitsizeanalyzer.nss`

#### `itm_py_gitsizeanalyzer.nss`

```java

//
$PY_GITSIZEANALYZER_DIR = '@user.desktop\my\flow\home\__GOTO__\Scripts\Python\Py_Cli_Utils\py__GitSizeAnalyzer'
$PY_GITSIZEANALYZER_EXE = '@PY_GITSIZEANALYZER_DIR\venv\Scripts\python.exe'
$PY_GITSIZEANALYZER_APP = '@PY_GITSIZEANALYZER_DIR\main.py'
//

// Context: Explorer
$PY_GITSIZEANALYZER_EXPLORER = '-i "@sel.dir" --prompt'
item(
    title="&GitSizeAnalyzer"
    keys="py"
    type='File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    //
    image=[E17C,GREY] image-sel=[E17C,PURPLE]
    tip=['"@PY_GITSIZEANALYZER_APP" @PY_GITSIZEANALYZER_EXPLORER',TIP3,0.75]
    //
    admin=keys.rbutton()
    cmd=if(KEYS_EXE_OPEN_EXE,('"@PY_GITSIZEANALYZER_EXE"'))
    args='"@PY_GITSIZEANALYZER_APP" @PY_GITSIZEANALYZER_EXPLORER'
    //
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@PY_GITSIZEANALYZER_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@PY_GITSIZEANALYZER_APP')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@PY_GITSIZEANALYZER_DIR')),
    }
)
// Context: Taskbar
$PY_GITSIZEANALYZER_TASKBAR = '-i "" --prompt'
item(
    title="&GitSizeAnalyzer"
    keys="py"
    type='Taskbar'
    //
    image=[E17C,GREY]
    image-sel=[E17C,PURPLE]
    //
    admin=keys.rbutton()
    cmd=if(KEYS_EXE_OPEN_EXE,('"@PY_GITSIZEANALYZER_EXE"'))
    args='"@PY_GITSIZEANALYZER_APP" @PY_GITSIZEANALYZER_TASKBAR'
    tip=['"@PY_GITSIZEANALYZER_APP" @PY_GITSIZEANALYZER_TASKBAR',TIP3,0.75]
    //
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@PY_GITSIZEANALYZER_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@PY_GITSIZEANALYZER_APP')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@PY_GITSIZEANALYZER_DIR')),
    }
)

```
### 80. `itm_py_markdowngenerator.nss`

#### `itm_py_markdowngenerator.nss`

```java

//
$PY_MARKDOWNGENERATOR_DIR = '@user.desktop\my\flow\home\__GOTO__\Scripts\Python\Py_Cli_Utils\py__MarkdownGenerator'
$PY_MARKDOWNGENERATOR_EXE = '@PY_MARKDOWNGENERATOR_DIR\venv\Scripts\python.exe'
$PY_MARKDOWNGENERATOR_APP = '@PY_MARKDOWNGENERATOR_DIR\main.py'
//

// Context: Explorer
$PY_MARKDOWNGENERATOR_EXPLORER = '-i "@sel.dir" -op "@sel.dir" -of "@sel.dir.name" -d 99 -e py --prompt'
item(
    title="&MarkdownGenerator"
    keys="py"
    type='File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    //
    image=[E17C,GREY] image-sel=[E17C,PURPLE]
    tip=['"@PY_MARKDOWNGENERATOR_APP" @PY_MARKDOWNGENERATOR_EXPLORER',TIP3,0.75]
    //
    admin=keys.rbutton()
    cmd=if(KEYS_EXE_OPEN_EXE,('"@PY_MARKDOWNGENERATOR_EXE"'))
    args='"@PY_MARKDOWNGENERATOR_APP" @PY_MARKDOWNGENERATOR_EXPLORER'
    //
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@PY_MARKDOWNGENERATOR_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@PY_MARKDOWNGENERATOR_APP')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@PY_MARKDOWNGENERATOR_DIR')),
    }
)
// Context: Taskbar
$PY_MARKDOWNGENERATOR_TASKBAR = '-i "@user.desktop" -op "@user.desktop" -of "output.md" -d 99 -e py --prompt'
item(
    title="&MarkdownGenerator"
    keys="py"
    type='Taskbar'
    //
    image=[E17C,GREY]
    image-sel=[E17C,PURPLE]
    //
    admin=keys.rbutton()
    cmd=if(KEYS_EXE_OPEN_EXE,('"@PY_MARKDOWNGENERATOR_EXE"'))
    args='"@PY_MARKDOWNGENERATOR_APP" @PY_MARKDOWNGENERATOR_TASKBAR'
    tip=['"@PY_MARKDOWNGENERATOR_APP" @PY_MARKDOWNGENERATOR_TASKBAR',TIP3,0.75]
    //
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@PY_MARKDOWNGENERATOR_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@PY_MARKDOWNGENERATOR_APP')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@PY_MARKDOWNGENERATOR_DIR')),
    }
)

```
### 81. `itm_py_projectgenerator.nss`

#### `itm_py_projectgenerator.nss`

```java

// $CMD_GIT_ADD_ALL = '/C (CD /D "@sel.dir") && (git add *)'
// $CMD_GIT_ADD_ALL_V = '/K (CD /D "@sel.dir") && (git add *) && PAUSE'
// $CMD_GIT_ADD_SELECTION = '/C (CD /D "@sel.dir") && (git add @BATCH_SEL_AS_STRING) && (@BATCH_EXIT_WITH_MSG)'
// $CMD_GIT_ADD_SELECTION_V = '/K (CD /D "@sel.dir") && (git add @BATCH_SEL_AS_STRING) && (@BATCH_EXIT_WITH_MSG) && PAUSE'

//
$PY_PROJECTGENERATOR_DIR = '@user.desktop\my\flow\home\__GOTO__\Scripts\Python\Py_Cli_Utils\py__ProjectGenerator'
$PY_PROJECTGENERATOR_EXE = '@PY_PROJECTGENERATOR_DIR\venv\Scripts\python.exe'
$PY_PROJECTGENERATOR_APP = '@PY_PROJECTGENERATOR_DIR\src\main.py'
//

// Context: Explorer
$PY_PROJECTGENERATOR_EXPLORER = '-pp "@sel.dir" --prompt'
item(
    title="&ProjectGenerator"
    keys="py"
    type='File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    //
    image=[E17C,GREY] image-sel=[E17C,PURPLE]
    tip=['"@PY_PROJECTGENERATOR_APP" @PY_PROJECTGENERATOR_EXPLORER',TIP3,0.75]
    //
    admin=keys.rbutton()
    cmd=if(KEYS_EXE_OPEN_EXE,('"@PY_PROJECTGENERATOR_EXE"'))
    args='"@PY_PROJECTGENERATOR_APP" @PY_PROJECTGENERATOR_EXPLORER'
    //
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@PY_PROJECTGENERATOR_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@PY_PROJECTGENERATOR_APP')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@PY_PROJECTGENERATOR_DIR')),
    }
)
// Context: Taskbar
$PY_PROJECTGENERATOR_TASKBAR = '-pp "@user.desktop" --prompt'
item(
    title="&ProjectGenerator"
    keys="py"
    type='Taskbar'
    //
    image=[E17C,GREY]
    image-sel=[E17C,PURPLE]
    //
    admin=keys.rbutton()
    cmd=if(KEYS_EXE_OPEN_EXE,('"@PY_PROJECTGENERATOR_EXE"'))
    args='"@PY_PROJECTGENERATOR_APP" @PY_PROJECTGENERATOR_TASKBAR'
    tip=['"@PY_PROJECTGENERATOR_APP" @PY_PROJECTGENERATOR_TASKBAR',TIP3,0.75]
    //
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@PY_PROJECTGENERATOR_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@PY_PROJECTGENERATOR_APP')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@PY_PROJECTGENERATOR_DIR')),
    }
)

```
### 82. `itm_py_renamewitheditor.nss`

#### `itm_py_renamewitheditor.nss`

```java

//
$PY_RENAMEWITHEDITOR_DIR = '@user.desktop\my\flow\home\__GOTO__\Scripts\Python\Py_Cli_Utils\py__RenameWithEditor'
$PY_RENAMEWITHEDITOR_EXE = '@PY_RENAMEWITHEDITOR_DIR\venv\Scripts\python.exe'
$PY_RENAMEWITHEDITOR_APP = '@PY_RENAMEWITHEDITOR_DIR\src\main.py'
//
// $CMD_GIT_ADD_SELECTION_F_V = '/K (CD /D "@sel.dir") && (git add @BATCH_SEL_AS_STRING -f) && (@BATCH_EXIT_WITH_MSG) && PAUSE'

// Context: Explorer
$PY_RENAMEWITHEDITOR_EXPLORER = '-d "@sel.dir" --prompt'
item(
    title="&RenameWithEditor"
    keys="py"
    type='File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    //
    image=[E17C,GREY] image-sel=[E17C,PURPLE]
    tip=['"@PY_RENAMEWITHEDITOR_APP" @PY_RENAMEWITHEDITOR_EXPLORER',TIP3,0.75]
    //
    admin=keys.rbutton()
    cmd=if(KEYS_EXE_OPEN_EXE,('"@PY_RENAMEWITHEDITOR_EXE"'))
    args='"@PY_RENAMEWITHEDITOR_APP" @PY_RENAMEWITHEDITOR_EXPLORER'
    //
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@PY_RENAMEWITHEDITOR_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@PY_RENAMEWITHEDITOR_APP')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@PY_RENAMEWITHEDITOR_DIR')),
    }
)
// Context: Taskbar
$PY_RENAMEWITHEDITOR_TASKBAR = '-d "@user.desktop" --prompt'
item(
    title="&RenameWithEditor"
    keys="py"
    type='Taskbar'
    //
    image=[E17C,GREY]
    image-sel=[E17C,PURPLE]
    //
    admin=keys.rbutton()
    cmd=if(KEYS_EXE_OPEN_EXE,('"@PY_RENAMEWITHEDITOR_EXE"'))
    args='"@PY_RENAMEWITHEDITOR_APP" @PY_RENAMEWITHEDITOR_TASKBAR'
    tip=['"@PY_RENAMEWITHEDITOR_APP" @PY_RENAMEWITHEDITOR_TASKBAR',TIP3,0.75]
    //
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@PY_RENAMEWITHEDITOR_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@PY_RENAMEWITHEDITOR_APP')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@PY_RENAMEWITHEDITOR_DIR')),
    }
)

```
### 83. `itm_py_sanitizefilenames.nss`

#### `itm_py_sanitizefilenames.nss`

```java

//
$PY_SANITIZEFILENAMES_DIR = '@user.desktop\my\flow\home\__GOTO__\Scripts\Python\Py_Cli_Utils\py__SanitizeFilenames'
$PY_SANITIZEFILENAMES_EXE = '@PY_SANITIZEFILENAMES_DIR\venv\Scripts\python.exe'
$PY_SANITIZEFILENAMES_APP = '@PY_SANITIZEFILENAMES_DIR\main.py'
//
$BATCH_SEL_AS_STRING = for(i=0, i< sel.count, '"@sel[i]" ')

// Context: Explorer
$PY_SANITIZEFILENAMES_EXPLORER = '-i @BATCH_SEL_AS_STRING --prompt'
item(
    title="&SanitizeFilenames"
    keys="py"
    type='File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    //
    image=[E17C,GREY] image-sel=[E17C,PURPLE]
    tip=['"@PY_SANITIZEFILENAMES_APP" @PY_SANITIZEFILENAMES_EXPLORER',TIP3,0.75]
    //
    admin=keys.rbutton()
    cmd=if(KEYS_EXE_OPEN_EXE,('"@PY_SANITIZEFILENAMES_EXE"'))
    args='"@PY_SANITIZEFILENAMES_APP" @PY_SANITIZEFILENAMES_EXPLORER'
    //
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@PY_SANITIZEFILENAMES_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@PY_SANITIZEFILENAMES_APP')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@PY_SANITIZEFILENAMES_DIR')),
    }
)
// Context: Taskbar
$PY_SANITIZEFILENAMES_TASKBAR = '--prompt'
item(
    title="&SanitizeFilenames"
    keys="py"
    type='Taskbar'
    //
    image=[E17C,GREY] image-sel=[E17C,PURPLE]
    tip=['"@PY_SANITIZEFILENAMES_APP" @PY_SANITIZEFILENAMES_TASKBAR',TIP3,0.75]
    //
    admin=keys.rbutton()
    args='"@PY_SANITIZEFILENAMES_APP" @PY_SANITIZEFILENAMES_TASKBAR'
    cmd=if(KEYS_EXE_OPEN_EXE,('"@PY_SANITIZEFILENAMES_EXE"'))
    //
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@PY_SANITIZEFILENAMES_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@PY_SANITIZEFILENAMES_APP')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@PY_SANITIZEFILENAMES_DIR')),
    }
)
```
### 84. `itm_py_speechtotext.nss`

#### `itm_py_speechtotext.nss`

```java

//
$PY_SPEECHTOTEXT_DIR = '@user.desktop\my\flow\home\__GOTO__\Scripts\Python\Py_Cli_Utils\py__SpeechToText'
$PY_SPEECHTOTEXT_EXE = '@PY_SPEECHTOTEXT_DIR\venv\Scripts\python.exe'
$PY_SPEECHTOTEXT_APP = '@PY_SPEECHTOTEXT_DIR\main.py'
//


// Context: Explorer
$PY_SPEECHTOTEXT_ARGS = '"@sel.file" -o "@sel.dir" --prompt'
item(
    title="&SpeechToText"
    keys="py"
    type='File'
    where=str.equals(sel.file.ext,[".mp3",".m4a",".webm",".wmv",".wav",".f4v",".mov",".mkv",".mp4"])
    //
    image=[E17C,GREY] image-sel=[E17C,PURPLE]
    tip=['"@PY_SPEECHTOTEXT_APP" @PY_SPEECHTOTEXT_ARGS',TIP3,0.75]
    //
    admin=keys.rbutton()
    cmd=if(KEYS_EXE_OPEN_EXE,('"@PY_SPEECHTOTEXT_EXE"'))
    args='"@PY_SPEECHTOTEXT_APP" @PY_SPEECHTOTEXT_ARGS'
    //
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@PY_SPEECHTOTEXT_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@PY_SPEECHTOTEXT_APP')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@PY_SPEECHTOTEXT_DIR')),
    }
)


// // Context: Explorer
// $PY_SPEECHTOTEXT_EXPLORER = 'input_files "@sel.file" --prompt'
// item(
//     title="&SpeechToText"
//     keys="py"
//     type='File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
//     //
//     image=[E17C,GREY] image-sel=[E17C,PURPLE]
//     tip=['"@PY_SPEECHTOTEXT_APP" @PY_SPEECHTOTEXT_EXPLORER',TIP3,0.75]
//     //
//     admin=keys.rbutton()
//     cmd=if(KEYS_EXE_OPEN_EXE,('"@PY_SPEECHTOTEXT_EXE"'))
//     args='"@PY_SPEECHTOTEXT_APP" @PY_SPEECHTOTEXT_EXPLORER'
//     //
//     commands{
//         cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@PY_SPEECHTOTEXT_DIR')),
//         cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@PY_SPEECHTOTEXT_APP')),
//         cmd=if(KEYS_EXE_OPEN_DIR,('@PY_SPEECHTOTEXT_DIR')),
//     }
// )

// Context: Taskbar
$PY_SPEECHTOTEXT_TASKBAR = '--prompt'
item(
    title="&SpeechToText"
    keys="py"
    type='Taskbar'
    //
    image=[E17C,GREY]
    image-sel=[E17C,PURPLE]
    //
    admin=keys.rbutton()
    cmd=if(KEYS_EXE_OPEN_EXE,('"@PY_SPEECHTOTEXT_EXE"'))
    args='"@PY_SPEECHTOTEXT_APP" @PY_SPEECHTOTEXT_TASKBAR'
    tip=['"@PY_SPEECHTOTEXT_APP" @PY_SPEECHTOTEXT_TASKBAR',TIP3,0.75]
    //
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@PY_SPEECHTOTEXT_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@PY_SPEECHTOTEXT_APP')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@PY_SPEECHTOTEXT_DIR')),
    }
)

```
### 85. `itm_py_urlgenerator.nss`

#### `itm_py_urlgenerator.nss`

```java

//
$PY_URLGENERATOR_DIR = '@user.desktop\my\flow\home\__GOTO__\Scripts\Python\Py_Cli_Utils\py__UrlGenerator'
$PY_URLGENERATOR_EXE = '@PY_URLGENERATOR_DIR\venv\Scripts\python.exe'
$PY_URLGENERATOR_APP = '@PY_URLGENERATOR_DIR\main.py'
//

// Context: Explorer
$PY_URLGENERATOR_EXPLORER = '-op "@sel.dir" --prompt'
item(
    title="&UrlGenerator"
    keys="py"
    type='File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    //
    image=[E17C,GREY] image-sel=[E17C,PURPLE]
    tip=['"@PY_URLGENERATOR_APP" @PY_URLGENERATOR_EXPLORER',TIP3,0.75]
    //
    admin=keys.rbutton()
    cmd=if(KEYS_EXE_OPEN_EXE,('"@PY_URLGENERATOR_EXE"'))
    args='"@PY_URLGENERATOR_APP" @PY_URLGENERATOR_EXPLORER'
    //
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@PY_URLGENERATOR_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@PY_URLGENERATOR_APP')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@PY_URLGENERATOR_DIR')),
    }
)
// Context: Taskbar
$PY_URLGENERATOR_TASKBAR = '-op "@user.desktop" --prompt'
item(
    title="&UrlGenerator"
    keys="py"
    type='Taskbar'
    //
    image=[E17C,GREY]
    image-sel=[E17C,PURPLE]
    //
    admin=keys.rbutton()
    cmd=if(KEYS_EXE_OPEN_EXE,('"@PY_URLGENERATOR_EXE"'))
    args='"@PY_URLGENERATOR_APP" @PY_URLGENERATOR_TASKBAR'
    tip=['"@PY_URLGENERATOR_APP" @PY_URLGENERATOR_TASKBAR',TIP3,0.75]
    //
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@PY_URLGENERATOR_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@PY_URLGENERATOR_APP')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@PY_URLGENERATOR_DIR')),
    }
)

```
### 86. `itm_py_videocompressor.nss`

#### `itm_py_videocompressor.nss`

```java

//
$PY_VIDEOCOMPRESSOR_DIR = '@user.desktop\my\flow\home\__GOTO__\Scripts\Python\Py_Cli_Utils\py__VideoCompressor'
$PY_VIDEOCOMPRESSOR_EXE = '@PY_VIDEOCOMPRESSOR_DIR\venv\Scripts\python.exe'
$PY_VIDEOCOMPRESSOR_APP = '@PY_VIDEOCOMPRESSOR_DIR\main.py'
//
$BATCH_SEL_AS_STRING = for(i=0, i< sel.count, '"@sel[i]" ')

// Context: Explorer
$PY_VIDEOCOMPRESSOR_EXPLORER = '-i @BATCH_SEL_AS_STRING --prompt'
item(
    title="&VideoCompressor"
    keys="py"
    type='File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    //
    image=[E17C,GREY] image-sel=[E17C,PURPLE]
    tip=['"@PY_VIDEOCOMPRESSOR_APP" @PY_VIDEOCOMPRESSOR_EXPLORER',TIP3,0.75]
    //
    admin=keys.rbutton()
    cmd=if(KEYS_EXE_OPEN_EXE,('"@PY_VIDEOCOMPRESSOR_EXE"'))
    args='"@PY_VIDEOCOMPRESSOR_APP" @PY_VIDEOCOMPRESSOR_EXPLORER'
    //
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@PY_VIDEOCOMPRESSOR_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@PY_VIDEOCOMPRESSOR_APP')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@PY_VIDEOCOMPRESSOR_DIR')),
    }
)
// Context: Taskbar
$PY_VIDEOCOMPRESSOR_TASKBAR = '--prompt'
item(
    title="&VideoCompressor"
    keys="py"
    type='Taskbar'
    //
    image=[E17C,GREY] image-sel=[E17C,PURPLE]
    tip=['"@PY_VIDEOCOMPRESSOR_APP" @PY_VIDEOCOMPRESSOR_TASKBAR',TIP3,0.75]
    //
    admin=keys.rbutton()
    args='"@PY_VIDEOCOMPRESSOR_APP" @PY_VIDEOCOMPRESSOR_TASKBAR'
    cmd=if(KEYS_EXE_OPEN_EXE,('"@PY_VIDEOCOMPRESSOR_EXE"'))
    //
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@PY_VIDEOCOMPRESSOR_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@PY_VIDEOCOMPRESSOR_APP')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@PY_VIDEOCOMPRESSOR_DIR')),
    }
)
```
### 87. `itm_py_videosplitter.nss`

#### `itm_py_videosplitter.nss`

```java

//
$PY_VIDEOSPLITTER_DIR = '@user.desktop\my\flow\home\__GOTO__\Scripts\Python\Py_Cli_Utils\py__VideoSplitter'
$PY_VIDEOSPLITTER_EXE = '@PY_VIDEOSPLITTER_DIR\venv\Scripts\python.exe'
$PY_VIDEOSPLITTER_APP = '@PY_VIDEOSPLITTER_DIR\src\main.py'
//


// Context: Explorer
$PY_VIDEOSPLITTER_ARGS = '"@sel.file" --prompt'
item(
    title="&VideoSplitter"
    keys="py"
    type='File'
    where=str.equals(sel.file.ext,[".m4a",".webm",".wmv",".wav",".f4v",".mov",".mkv",".mp4"])
    //
    image=[E17C,GREY] image-sel=[E17C,PURPLE]
    tip=['"@PY_VIDEOSPLITTER_APP" @PY_VIDEOSPLITTER_ARGS',TIP3,0.75]
    //
    admin=keys.rbutton()
    cmd=if(KEYS_EXE_OPEN_EXE,('"@PY_VIDEOSPLITTER_EXE"'))
    args='"@PY_VIDEOSPLITTER_APP" @PY_VIDEOSPLITTER_ARGS'
    //
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@PY_VIDEOSPLITTER_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@PY_VIDEOSPLITTER_APP')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@PY_VIDEOSPLITTER_DIR')),
    }
)


// Context: Taskbar
$PY_VIDEOSPLITTER_TASKBAR = ' --prompt'
item(
    title="&VideoSplitter"
    keys="py"
    type='Taskbar'
    //
    image=[E17C,GREY]
    image-sel=[E17C,PURPLE]
    //
    admin=keys.rbutton()
    cmd=if(KEYS_EXE_OPEN_EXE,('"@PY_VIDEOSPLITTER_EXE"'))
    args='"@PY_VIDEOSPLITTER_APP" @PY_VIDEOSPLITTER_TASKBAR'
    tip=['"@PY_VIDEOSPLITTER_APP" @PY_VIDEOSPLITTER_TASKBAR',TIP3,0.75]
    //
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@PY_VIDEOSPLITTER_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@PY_VIDEOSPLITTER_APP')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@PY_VIDEOSPLITTER_DIR')),
    }
)

```
### 88. `itm_py_win4ro1.nss`

#### `itm_py_win4ro1.nss`

```java

//
$APP_UTIL_WIN4RO1_DIR = '@user.desktop\my\flow\home\__GOTO__\Scripts\Python\Py_Ai_Utils\py__GptReasoningChains\win4r.o1'
$APP_UTIL_WIN4RO1_RUN = '@APP_UTIL_WIN4RO1_DIR\run.bat'
$APP_UTIL_WIN4RO1_TIP = "..."+str.trimstart('@APP_UTIL_WIN4RO1_RUN','@app.dir')

// Context: Explorer
item(
    title  = ":  &win4r.o1"
    keys   = "py"
    type   = 'File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    // args   = '"@sel.dir"'
    //
    image  = [E17C,GREY] image-sel=[E17C,PURPLE]
    tip    = [APP_UTIL_WIN4RO1_TIP,TIP3,0.8]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_UTIL_WIN4RO1_RUN"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_UTIL_WIN4RO1_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_UTIL_WIN4RO1_RUN')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_UTIL_WIN4RO1_DIR')),
    }
)
// Context: Taskbar
item(
    title  = ":  &win4r.o1"
    keys   = "py"
    type   = 'Taskbar'
    // args   = ''
    //
    image  = [E17C,GREY] image-sel=[E17C,PURPLE]
    tip    = [APP_UTIL_WIN4RO1_TIP,TIP3,0.8]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_UTIL_WIN4RO1_RUN"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_UTIL_WIN4RO1_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_UTIL_WIN4RO1_RUN')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_UTIL_WIN4RO1_DIR')),
    }
)


```
### 89. `itm_py_windowutils.nss`

#### `itm_py_windowutils.nss`

```java

// $CMD_GIT_ADD_ALL = '/C (CD /D "@sel.dir") && (git add *)'
// $CMD_GIT_ADD_ALL_V = '/K (CD /D "@sel.dir") && (git add *) && PAUSE'
// $CMD_GIT_ADD_SELECTION = '/C (CD /D "@sel.dir") && (git add @BATCH_SEL_AS_STRING) && (@BATCH_EXIT_WITH_MSG)'
// $CMD_GIT_ADD_SELECTION_V = '/K (CD /D "@sel.dir") && (git add @BATCH_SEL_AS_STRING) && (@BATCH_EXIT_WITH_MSG) && PAUSE'

//
$PY_WINDOWUTILS_DIR = '@user.desktop\my\flow\home\__GOTO__\Scripts\Python\Py_Cli_Utils\py__WindowUtils'
$PY_WINDOWUTILS_EXE = '@PY_WINDOWUTILS_DIR\venv\Scripts\python.exe'
$PY_WINDOWUTILS_APP = '@PY_WINDOWUTILS_DIR\main.py'
//

// Context: Explorer
$PY_WINDOWUTILS_EXPLORER = '--close-duplicate-windows --prompt'
item(
    title="&WindowUtils"
    keys="py"
    type='File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    //
    image=[E17C,GREY] image-sel=[E17C,PURPLE]
    tip=['"@PY_WINDOWUTILS_APP" @PY_WINDOWUTILS_EXPLORER',TIP3,0.75]
    //
    admin=keys.rbutton()
    cmd=if(KEYS_EXE_OPEN_EXE,('"@PY_WINDOWUTILS_EXE"'))
    args='"@PY_WINDOWUTILS_APP" @PY_WINDOWUTILS_EXPLORER'
    //
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@PY_WINDOWUTILS_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@PY_WINDOWUTILS_APP')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@PY_WINDOWUTILS_DIR')),
    }
)
// Context: Taskbar
$PY_WINDOWUTILS_TASKBAR = '--close-duplicate-windows --prompt'
item(
    title="&WindowUtils"
    keys="py"
    type='Taskbar'
    //
    image=[E17C,GREY]
    image-sel=[E17C,PURPLE]
    //
    admin=keys.rbutton()
    cmd=if(KEYS_EXE_OPEN_EXE,('"@PY_WINDOWUTILS_EXE"'))
    args='"@PY_WINDOWUTILS_APP" @PY_WINDOWUTILS_TASKBAR'
    tip=['"@PY_WINDOWUTILS_APP" @PY_WINDOWUTILS_TASKBAR',TIP3,0.75]
    //
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@PY_WINDOWUTILS_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@PY_WINDOWUTILS_APP')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@PY_WINDOWUTILS_DIR')),
    }
)

```
### 90. `itm_py_youtubedownloader.nss`

#### `itm_py_youtubedownloader.nss`

```java

//
$PY_YOUTUBEDOWNLOADER_DIR = '@user.desktop\my\flow\home\__GOTO__\Scripts\Python\Py_Cli_Utils\py__YoutubeDownloader'
$PY_YOUTUBEDOWNLOADER_EXE = '@PY_YOUTUBEDOWNLOADER_DIR\venv\Scripts\python.exe'
$PY_YOUTUBEDOWNLOADER_APP = '@PY_YOUTUBEDOWNLOADER_DIR\main.py'
//

// Context: Explorer
$PY_YOUTUBEDOWNLOADER_EXPLORER = '-op "@sel.dir" --prompt'
item(
    title="&YoutubeDownloader"
    keys="py"
    type='File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    //
    image=[E17C,GREY] image-sel=[E17C,PURPLE]
    tip=['"@PY_YOUTUBEDOWNLOADER_APP" @PY_YOUTUBEDOWNLOADER_EXPLORER',TIP3,0.75]
    //
    admin=keys.rbutton()
    cmd=if(KEYS_EXE_OPEN_EXE,('"@PY_YOUTUBEDOWNLOADER_EXE"'))
    args='"@PY_YOUTUBEDOWNLOADER_APP" @PY_YOUTUBEDOWNLOADER_EXPLORER'
    //
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@PY_YOUTUBEDOWNLOADER_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@PY_YOUTUBEDOWNLOADER_APP')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@PY_YOUTUBEDOWNLOADER_DIR')),
    }
)
// Context: Taskbar
$PY_YOUTUBEDOWNLOADER_TASKBAR = '-op "@user.desktop" --prompt'
item(
    title="&YoutubeDownloader"
    keys="py"
    type='Taskbar'
    //
    image=[E17C,GREY]
    image-sel=[E17C,PURPLE]
    //
    admin=keys.rbutton()
    cmd=if(KEYS_EXE_OPEN_EXE,('"@PY_YOUTUBEDOWNLOADER_EXE"'))
    args='"@PY_YOUTUBEDOWNLOADER_APP" @PY_YOUTUBEDOWNLOADER_TASKBAR'
    tip=['"@PY_YOUTUBEDOWNLOADER_APP" @PY_YOUTUBEDOWNLOADER_TASKBAR',TIP3,0.75]
    //
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@PY_YOUTUBEDOWNLOADER_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@PY_YOUTUBEDOWNLOADER_APP')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@PY_YOUTUBEDOWNLOADER_DIR')),
    }
)

```
### 91. `itm_py_youtubeplaylistmanager.nss`

#### `itm_py_youtubeplaylistmanager.nss`

```java

//
$PY_YOUTUBEPLAYLISTMANAGER_DIR = '@user.desktop\my\flow\home\__GOTO__\Scripts\Python\Py_Cli_Utils\py__YoutubePlaylistManager'
$PY_YOUTUBEPLAYLISTMANAGER_EXE = '@PY_YOUTUBEPLAYLISTMANAGER_DIR\venv\Scripts\python.exe'
$PY_YOUTUBEPLAYLISTMANAGER_APP = '@PY_YOUTUBEPLAYLISTMANAGER_DIR\main.py'

// Context: Explorer
$PY_YOUTUBEPLAYLISTMANAGER_EXPLORER = '--prompt'
item(
    title="&YoutubePlaylistManager"
    keys="py"
    type='File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    //
    image=[E17C,GREY] image-sel=[E17C,PURPLE]
    tip=['"@PY_YOUTUBEPLAYLISTMANAGER_APP" @PY_YOUTUBEPLAYLISTMANAGER_EXPLORER',TIP3,0.75]
    //
    admin=keys.rbutton()
    cmd=if(KEYS_EXE_OPEN_EXE,('"@PY_YOUTUBEPLAYLISTMANAGER_EXE"'))
    args='"@PY_YOUTUBEPLAYLISTMANAGER_APP" @PY_YOUTUBEPLAYLISTMANAGER_EXPLORER'
    //
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@PY_YOUTUBEPLAYLISTMANAGER_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@PY_YOUTUBEPLAYLISTMANAGER_APP')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@PY_YOUTUBEPLAYLISTMANAGER_DIR')),
    }
)
// Context: Taskbar
$PY_YOUTUBEPLAYLISTMANAGER_TASKBAR = '--prompt'
item(
    title="&YoutubePlaylistManager"
    keys="py"
    type='Taskbar'
    //
    image=[E17C,GREY] image-sel=[E17C,PURPLE]
    tip=['"@PY_YOUTUBEPLAYLISTMANAGER_APP" @PY_YOUTUBEPLAYLISTMANAGER_TASKBAR',TIP3,0.75]
    //
    admin=keys.rbutton()
    args='"@PY_YOUTUBEPLAYLISTMANAGER_APP" @PY_YOUTUBEPLAYLISTMANAGER_TASKBAR'
    cmd=if(KEYS_EXE_OPEN_EXE,('"@PY_YOUTUBEPLAYLISTMANAGER_EXE"'))
    //
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@PY_YOUTUBEPLAYLISTMANAGER_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@PY_YOUTUBEPLAYLISTMANAGER_APP')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@PY_YOUTUBEPLAYLISTMANAGER_DIR')),
    }
)
```
### 92. `itm_reg_setkeyboardrepeatrate.nss`

#### `itm_reg_setkeyboardrepeatrate.nss`

```java

// -> increase keyboard response speed
item(title="Set &Keyboard Repeat Rate" tip="Warning: This will change the registry" image=[E0FE,LOWKEY] image-sel=[E0FE,RED] admin=1 commands{
        // -> original values
        // cmd=reg.set('HKEY_CURRENT_USER\Control Panel\Accessibility\Keyboard Response', "AutoRepeatDelay", 1000, reg.sz),
        // cmd=reg.set('HKEY_CURRENT_USER\Control Panel\Accessibility\Keyboard Response', "AutoRepeatRate", 500, reg.sz),
        // cmd=reg.set('HKEY_CURRENT_USER\Control Panel\Accessibility\Keyboard Response', "DelayBeforeAcceptance", 1000, reg.sz),
        // cmd=reg.set('HKEY_CURRENT_USER\Control Panel\Accessibility\Keyboard Response', "Flags", 126, reg.sz),

        // -> custom values
        cmd=reg.set('HKEY_CURRENT_USER\Control Panel\Keyboard', "KeyboardDelay", 0, reg.sz),
        cmd=reg.set('HKEY_CURRENT_USER\Control Panel\Keyboard', "KeyboardSpeed", 31, reg.sz),
        cmd=reg.set('HKEY_CURRENT_USER\Control Panel\Accessibility\Keyboard Response', "AutoRepeatDelay", 250, reg.sz),
        cmd=reg.set('HKEY_CURRENT_USER\Control Panel\Accessibility\Keyboard Response', "AutoRepeatRate", 16, reg.sz),
        cmd=reg.set('HKEY_CURRENT_USER\Control Panel\Accessibility\Keyboard Response', "DelayBeforeAcceptance", 0, reg.sz),
        cmd=reg.set('HKEY_CURRENT_USER\Control Panel\Accessibility\Keyboard Response', "Flags", 27, reg.sz),
    }
)
```
### 93. `itm_reg_settaskbarleft.nss`

#### `itm_reg_settaskbarleft.nss`

```java

// ->
item(title="Set &Taskbar to the Left" tip="Warning: This will change the registry" image=[E0FE,LOWKEY] image-sel=[E0FE,RED] admin=1 commands{
        // -> original values
        // cmd=reg.set('HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Explorer\Advanced', "TaskbarAl", 1, reg.dword),

        // -> custom values
        cmd=reg.set('HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Explorer\Advanced', "TaskbarAl", 0, reg.dword),
    }
)
```
