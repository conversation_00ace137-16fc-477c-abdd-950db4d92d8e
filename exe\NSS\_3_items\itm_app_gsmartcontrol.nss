
// //
// $APP_USER_GSMARTCONTROL_DIR = '@user.desktop\my\flow\home\__GOTO__\Apps\grp_misc\app_gsmartcontrol\exe'
// $APP_USER_GSMARTCONTROL_EXE = '@APP_USER_GSMARTCONTROL_DIR\gsmartcontrol.exe'
// $APP_USER_GSMARTCONTROL_TIP = "..."+str.trimstart('@APP_USER_GSMARTCONTROL_EXE','@app.dir')

// //
// item(
//     title=":  &Gsmartcontrol"
//     keys="exe"
//     type='Taskbar|File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
//     //
//     image=APP_USER_GSMARTCONTROL_EXE
//     tip=[APP_USER_GSMARTCONTROL_TIP,TIP3,0.5]
//     //
//     admin=keys.rbutton()
//     cmd=if(KEYS_EXE_OPEN_EXE,(APP_USER_GSMARTCONTROL_EXE))
//     commands{
//         cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_GSMARTCONTROL_DIR')),
//         cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_GSMARTCONTROL_EXE')),
//         cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_GSMARTCONTROL_DIR')),
//     }
// )



//
$APP_USER_GSMARTCONTROL_DIR = '@user.desktop\my\flow\home\__GOTO__\Apps\grp_misc\app_gsmartcontrol\exe'
$APP_USER_GSMARTCONTROL_EXE = '@APP_USER_GSMARTCONTROL_DIR\gsmartcontrol.exe'
$APP_USER_GSMARTCONTROL_TIP = "..."+str.trimstart('@APP_USER_GSMARTCONTROL_EXE','@app.dir')

// // Context: Explorer
item(
    title  = ":  &Gsmartcontrol"
    keys   = "exe"
    type   = 'Taskbar|File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    args   = '/C (CD /D "@APP_USER_GSMARTCONTROL_DIR") & (start @APP_USER_GSMARTCONTROL_EXE)'
    //
    image  = APP_USER_GSMARTCONTROL_EXE
    tip    = [APP_USER_GSMARTCONTROL_TIP,TIP3,0.5]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('cmd.exe'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_GSMARTCONTROL_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_GSMARTCONTROL_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_GSMARTCONTROL_DIR')),
    }
)
