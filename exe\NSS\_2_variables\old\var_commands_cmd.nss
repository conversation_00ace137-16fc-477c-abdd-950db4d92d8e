
/* cmd-line */


/*
    usage:
    :: cmd-line='@cmd_terminal_app'
    :: cmd-line='@cmd_close_chrome'
    :: ...
*/

// -> open prompt in directory
$cmd_terminal_app = '/K (CD /D "@app.dir")      & (TITLE ^(cmd-prompt:@sys.datetime("H.M")^))'
$cmd_terminal_cwd = '/K (CD /D "@sel.dir")      & (TITLE ^(cmd-prompt:@sys.datetime("H.M")^))'
$cmd_terminal_def = '/K (CD /D "@user.desktop") & (TITLE ^(cmd-prompt:@sys.datetime("H.M")^))'

// // -> open ping-prompt (for checking if online)
// $cmd_ping_web           = '/c (TITLE ^(cmd:ping:@sys.datetime("H.M")^)) & ping 195.159.0.100 -t'

// -> close/restart/start
$cmd_close_3dsmax        = '/c taskkill /F /IM 3dsmax.exe'
$cmd_restart_3dsmax      = '/c taskkill /F /IM 3dsmax.exe & "@exe_3dsmax"'
$cmd_start_3dsmax        = '/c start "@exe_3dsmax"'
//
$cmd_close_audacity      = '/c taskkill /F /IM audacity.exe'
$cmd_restart_audacity    = '/c taskkill /F /IM audacity.exe & "@exe_audacity"'
$cmd_start_audacity      = '/c start "@exe_audacity"'
//
$cmd_close_blender       = '/c taskkill /F /IM blender.exe'
$cmd_restart_blender     = '/c taskkill /F /IM blender.exe & "@exe_blender"'
$cmd_start_blender       = '/c start "@exe_blender"'
//
$cmd_close_chrome        = '/c taskkill /F /IM chrome.exe'
$cmd_restart_chrome      = '/c taskkill /F /IM chrome.exe & start chrome.exe'
$cmd_start_chrome        = '/c start chrome.exe'
//
$cmd_close_everything    = '/c taskkill /F /IM everything64.exe'
$cmd_restart_everything  = '/c taskkill /F /IM everything64.exe & "@exe_everything"'
$cmd_start_everything    = '/c start "@exe_everything" @args_everything_search'
//
$cmd_close_explorer      = '/c taskkill /F /IM explorer.exe'
$cmd_restart_explorer    = '/c taskkill /F /IM explorer.exe & start explorer.exe'
$cmd_start_explorer      = '/c start explorer.exe'
//
$cmd_close_illustrator   = '/c taskkill /F /IM illustrator.exe'
$cmd_restart_illustrator = '/c taskkill /F /IM illustrator.exe & start illustrator.exe'
$cmd_start_illustrator   = '/c start illustrator.exe'
//
$cmd_close_photoshop     = '/c taskkill /F /IM photoshop.exe'
$cmd_restart_photoshop   = '/c taskkill /F /IM photoshop.exe & start photoshop.exe'
$cmd_start_photoshop     = '/c start photoshop.exe'
//
$cmd_close_sourcetree    = '/c taskkill /F /IM sourcetree.exe'
$cmd_restart_sourcetree  = '/c taskkill /F /IM sourcetree.exe & "@exe_sourcetree"'
$cmd_start_sourcetree    = '/c start "@exe_sourcetree"'
//
$cmd_close_sublime       = '/c taskkill /F /IM sublime_text.exe'
$cmd_restart_sublime     = '/c taskkill /F /IM sublime_text.exe & "@exe_sublime"'
$cmd_start_sublime       = '/c start "@exe_sublime"'
//
$cmd_close_vlc           = '/c taskkill /F /IM vlc.exe'
$cmd_restart_vlc         = '/c taskkill /F /IM vlc.exe & "@exe_vlc"'
$cmd_start_vlc           = '/c start "@exe_vlc"'
//
$cmd_close_qbittorrent   = '/c taskkill /F /IM qbittorrent.exe /IM qbittorrent-portable.exe'
$cmd_restart_qbittorrent = '/c taskkill /F /IM qbittorrent.exe /IM qbittorrent-portable.exe & "@exe_qbittorrent"'
$cmd_start_qbittorrent   = '/c start "@exe_qbittorrent"'
//
$cmd_close_winmerge      = '/c taskkill /F /IM WinMerge.exe /IM WinMergeU.exe'
$cmd_restart_winmerge    = '/c taskkill /F /IM WinMerge.exe /IM WinMergeU.exe & "@exe_winmerge"'
$cmd_start_winmerge      = '/c start "@exe_winmerge"'