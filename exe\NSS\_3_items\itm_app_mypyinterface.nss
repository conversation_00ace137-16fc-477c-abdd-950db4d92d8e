
//
$APP_MYPYINTERFACE_DIR = '@user.desktop\my\flow\home\__GOTO__\Apps\app_mypyinterface\exe'
$APP_MYPYINTERFACE_EXE = '@APP_MYPYINTERFACE_DIR\cursor.exe'
$APP_MYPYINTERFACE_TIP = "..."+str.trimstart('@APP_MYPYINTERFACE_EXE','@app.dir')

//
$APP_MYPYINTERFACE_DIR_CFG = '@user.desktop\my\flow\home\__GOTO__\Apps\app_mypyinterface\data'
$APP_MYPYINTERFACE_DIR_NSS = '@app.dir\NSS\_3_items\user_apps'
$APP_MYPYINTERFACE_DIR_SRC = '@user.desktop\my\flow\home\__GOTO__\Apps\app_mypyinterface'
$APP_MYPYINTERFACE_DIR_USR = '@user.desktop\my\flow\home\__GOTO__\Apps\app_mypyinterface\user'

// Context: Taskbar
item(
    title  = ":  &MyPyInterface"
    keys   = "exe"
    type   = 'Taskbar'
    args   = ''
    //
    image  = APP_MYPYINTERFACE_EXE
    tip    = [APP_MYPYINTERFACE_TIP,TIP3,1.0]
    //
    admin  = KEYS_EXE_ADMIN // [rightclick] -[ctrl, alt, shift]
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_MYPYINTERFACE_EXE"')) // -[ctrl, alt, shift]
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_MYPYINTERFACE_DIR')), // [ctrl + leftclick] -[alt]
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_MYPYINTERFACE_EXE')), // [ctrl + rightclick] -[alt]
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_MYPYINTERFACE_DIR')), // [shift + leftclick] -[ctrl, alt]
        //
        cmd=if(KEYS_EXE_GOTO_CFG,('@APP_MYPYINTERFACE_DIR_CFG')), // [alt + leftclick] -[ctrl, shift]
        cmd=if(KEYS_EXE_GOTO_NSS,('@APP_MYPYINTERFACE_DIR_NSS')), // [alt + shift + leftclick] -[ctrl]
        cmd=if(KEYS_EXE_GOTO_SRC,('@APP_MYPYINTERFACE_DIR_SRC')), // [alt + rightclick] -[ctrl, shift]
    }
)
