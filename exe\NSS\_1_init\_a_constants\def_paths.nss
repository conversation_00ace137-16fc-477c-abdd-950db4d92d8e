
// docs: `https://nilesoft.org/docs/configuration/properties#tip`


/*
    :: system paths

    :: usage: `cmd=command.navigate('@DIR_SYS_DESKTOP')`
*/

// --- directories
$DIR_SYS_SHELL           = '@app.dir'
$DIR_SYS_PROGRAMDATA     = '@sys.programdata'
$DIR_SYS_PROGRAMFILES    = '@sys.prog'
$DIR_SYS_PROGRAMFILESX86 = '@sys.prog32'
$DIR_SYS_SYSTEM32        = '@sys.bin'
$DIR_SYS_SYSTEMROOT      = '@sys.root'
$DIR_SYS_SYSWOW64        = '@sys.wow'
$DIR_SYS_TEMP            = '@sys.temp'
$DIR_SYS_USERS           = '@sys.users'
$DIR_SYS_WINDOWS         = '@sys.dir'
$DIR_SYS_FONTS           = '@sys.dir/Fonts'
$DIR_SYS_APPDATA         = '@user.appdata'
$DIR_SYS_DESKTOP         = '@user.desktop'
$DIR_SYS_DOCUMENTS       = '@user.documents'
$DIR_SYS_DOWNLOADS       = '@user.downloads'
$DIR_SYS_LOCALAPPDATA    = '@user.localappdata'
$DIR_SYS_RECENT          = '@user.appdata/Microsoft/Windows/Recent'
$DIR_SYS_STARTMENU       = '@user.startmenu'
$DIR_SYS_STARTUP         = '@user.startmenu/Programs/Startup'
$DIR_SYS_USERPROFILE     = '@user.directory'
$DIR_SYS_THISPC          = 'file:/'
$DIR_SYS_APPLICATIONS    = 'shell:appsfolder'
$DIR_SYS_CONTROLPANEL    = 'shell:::{5399e694-6ce5-4d6c-8fce-1d8870fdcba0}'
$DIR_SYS_NETWORKADAPTERS = 'shell:::{7007acc7-3202-11d1-aad2-00805fc1270e}'
$DIR_SYS_RECYCLEBIN      = 'shell:::{645ff040-5081-101b-9f08-00aa002f954e}'
