
//
$PY_SPEECHTOTEXT_DIR = '@user.desktop\my\flow\home\__GOTO__\Scripts\Python\Py_Cli_Utils\py__SpeechToText'
$PY_SPEECHTOTEXT_EXE = '@PY_SPEECHTOTEXT_DIR\venv\Scripts\python.exe'
$PY_SPEECHTOTEXT_APP = '@PY_SPEECHTOTEXT_DIR\src\main.py'
//


// Context: Explorer
$PY_SPEECHTOTEXT_ARGS = '"@sel.file" -o "@sel.dir" --prompt'
item(
    title="&SpeechToText"
    keys="py"
    type='File'
    where=str.equals(sel.file.ext,[".mp3",".m4a",".webm",".wmv",".wav",".f4v",".mov",".mkv",".mp4"])
    //
    image=[E17C,GREY] image-sel=[E17C,PURPLE]
    tip=['"@PY_SPEECHTOTEXT_APP" @PY_SPEECHTOTEXT_ARGS',TIP3,0.75]
    //
    admin=keys.rbutton()
    cmd=if(KEYS_EXE_OPEN_EXE,('"@PY_SPEECHTOTEXT_EXE"'))
    args='"@PY_SPEECHTOTEXT_APP" @PY_SPEECHTOTEXT_ARGS'
    //
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@PY_SPEECHTOTEXT_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@PY_SPEECHTOTEXT_APP')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@PY_SPEECHTOTEXT_DIR')),
    }
)


// // Context: Explorer
// $PY_SPEECHTOTEXT_EXPLORER = 'input_files "@sel.file" --prompt'
// item(
//     title="&SpeechToText"
//     keys="py"
//     type='File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
//     //
//     image=[E17C,GREY] image-sel=[E17C,PURPLE]
//     tip=['"@PY_SPEECHTOTEXT_APP" @PY_SPEECHTOTEXT_EXPLORER',TIP3,0.75]
//     //
//     admin=keys.rbutton()
//     cmd=if(KEYS_EXE_OPEN_EXE,('"@PY_SPEECHTOTEXT_EXE"'))
//     args='"@PY_SPEECHTOTEXT_APP" @PY_SPEECHTOTEXT_EXPLORER'
//     //
//     commands{
//         cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@PY_SPEECHTOTEXT_DIR')),
//         cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@PY_SPEECHTOTEXT_APP')),
//         cmd=if(KEYS_EXE_OPEN_DIR,('@PY_SPEECHTOTEXT_DIR')),
//     }
// )

// Context: Taskbar
$PY_SPEECHTOTEXT_TASKBAR = '--prompt'
item(
    title="&SpeechToText"
    keys="py"
    type='Taskbar'
    //
    image=[E17C,GREY]
    image-sel=[E17C,PURPLE]
    //
    admin=keys.rbutton()
    cmd=if(KEYS_EXE_OPEN_EXE,('"@PY_SPEECHTOTEXT_EXE"'))
    args='"@PY_SPEECHTOTEXT_APP" @PY_SPEECHTOTEXT_TASKBAR'
    tip=['"@PY_SPEECHTOTEXT_APP" @PY_SPEECHTOTEXT_TASKBAR',TIP3,0.75]
    //
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@PY_SPEECHTOTEXT_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@PY_SPEECHTOTEXT_APP')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@PY_SPEECHTOTEXT_DIR')),
    }
)
