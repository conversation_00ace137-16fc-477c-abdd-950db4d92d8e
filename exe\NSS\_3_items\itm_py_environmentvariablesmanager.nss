
//
$PY_<PERSON>NVIRONMENTVARIABLESMANAGER_DIR = '@user.desktop\my\flow\home\__GOTO__\Scripts\Python\Py_Cli_Utils\py__EnvironmentVariablesManager'
$PY_ENVIRONMENTVARIABLESMANAGER_EXE = '@PY_ENVIRONMENTVA<PERSON>ABLESMANAGER_DIR\venv\Scripts\python.exe'
$PY_ENVIRONMENTVARIABLESMANAGER_APP = '@PY_ENVIRONMENTVARIABLESMANAGER_DIR\main.py'

// Context: Explorer
$PY_ENVIRONMENTVARIABLESMANAGER_EXPLORER = '--prompt'
item(
    title="&EnvironmentVariablesManager"
    keys="py"
    type='File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    //
    image=[E17C,GREY] image-sel=[E17C,PURPLE]
    tip=['"@PY_ENVIRONMENTVARIABLESMANAGER_APP" @PY_ENVIRONMENTVARIABLESMANAGER_EXPLORER',TIP3,0.75]
    //
    admin=keys.rbutton()
    cmd=if(KEYS_EXE_OPEN_EXE,('"@PY_ENVIRONMENTVARIABLESMANAGER_EXE"'))
    args='"@PY_ENVIRONMENTVARIABLESMANAGER_APP" @PY_ENVIRONMENTVARIABLESMANAGER_EXPLORER'
    //
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@PY_ENVIRONMENTVARIABLESMANAGER_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@PY_ENVIRONMENTVARIABLESMANAGER_APP')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@PY_ENVIRONMENTVARIABLESMANAGER_DIR')),
    }
)
// Context: Taskbar
$PY_ENVIRONMENTVARIABLESMANAGER_TASKBAR = '--prompt'
item(
    title="&EnvironmentVariablesManager"
    keys="py"
    type='Taskbar'
    //
    image=[E17C,GREY] image-sel=[E17C,PURPLE]
    tip=['"@PY_ENVIRONMENTVARIABLESMANAGER_APP" @PY_ENVIRONMENTVARIABLESMANAGER_TASKBAR',TIP3,0.75]
    //
    admin=keys.rbutton()
    args='"@PY_ENVIRONMENTVARIABLESMANAGER_APP" @PY_ENVIRONMENTVARIABLESMANAGER_TASKBAR'
    cmd=if(KEYS_EXE_OPEN_EXE,('"@PY_ENVIRONMENTVARIABLESMANAGER_EXE"'))
    //
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@PY_ENVIRONMENTVARIABLESMANAGER_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@PY_ENVIRONMENTVARIABLESMANAGER_APP')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@PY_ENVIRONMENTVARIABLESMANAGER_DIR')),
    }
)
