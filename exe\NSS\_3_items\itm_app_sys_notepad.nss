
//
$APP_SYS_NOTEPAD_DIR = '@sys.dir\System32'
$APP_SYS_NOTEPAD_EXE = '@APP_SYS_NOTEPAD_DIR\notepad.exe'
$APP_SYS_NOTEPAD_TIP = '@APP_SYS_NOTEPAD_EXE'

// Context: Explorer
item(
    title  = ":  &Notepad"
    keys   = "exe"
    type   = 'File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    //
    image  = APP_SYS_NOTEPAD_EXE
    tip    = [APP_SYS_NOTEPAD_TIP,TIP3,0.5]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_SYS_NOTEPAD_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_SYS_NOTEPAD_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_SYS_NOTEPAD_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_SYS_NOTEPAD_DIR')),
    }
)
// Context: Taskbar
item(
    title  = ":  &Notepad"
    keys   = "exe"
    type   = 'Taskbar'
    //
    image  = APP_SYS_NOTEPAD_EXE
    tip    = [APP_SYS_NOTEPAD_TIP,TIP3,0.5]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_SYS_NOTEPAD_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_SYS_NOTEPAD_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_SYS_NOTEPAD_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_SYS_NOTEPAD_DIR')),
    }
)