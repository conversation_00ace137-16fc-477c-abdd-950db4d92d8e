
/* menu */

// -> apps
menu(title="&Apps" type='Taskbar|Desktop|Dir|File|Drive|Back.Dir|Back.Drive' image=[E25E,SOFT] image-sel=[E25E,HOVER] sep='None') {

    item(title="System"  image=[E09E,GREY] vis='Static' sep='Both' col)
    import '@app.dir/NSS/_3_items/itm_app_sys_calc.nss'
    import '@app.dir/NSS/_3_items/itm_app_sys_cmd.nss'
    import '@app.dir/NSS/_3_items/itm_app_sys_magnify.nss'
    import '@app.dir/NSS/_3_items/itm_app_sys_mspaint.nss'
    import '@app.dir/NSS/_3_items/itm_app_sys_notepad.nss'
    import '@app.dir/NSS/_3_items/itm_app_sys_osk.nss'
    import '@app.dir/NSS/_3_items/itm_app_sys_powershell.nss'
    import '@app.dir/NSS/_3_items/itm_app_sys_powershellise.nss'
    import '@app.dir/NSS/_3_items/itm_app_sys_regedit.nss'
    separator()

    item(title="Nirsoft"  image=[E253,GREY] vis='Static' sep='Both' /*col*/)
    import '@app.dir/NSS/_3_items/itm_app_nirsoft_regfromapp.nss'
    import '@app.dir/NSS/_3_items/itm_app_nirsoft_registrychangesview.nss'
    import '@app.dir/NSS/_3_items/itm_app_nirsoft_regscanner.nss'
    import '@app.dir/NSS/_3_items/itm_app_nirsoft_winexplorer.nss'
    separator()

    item(title="SysInternals"  image=[E253,GREY] vis='Static' sep='Both' /*col*/)
    import '@app.dir/NSS/_3_items/itm_app_sysinternals_autoruns.nss'
    import '@app.dir/NSS/_3_items/itm_app_sysinternals_diskmon.nss'
    import '@app.dir/NSS/_3_items/itm_app_sysinternals_procexp.nss'
    import '@app.dir/NSS/_3_items/itm_app_sysinternals_procmon.nss'
    import '@app.dir/NSS/_3_items/itm_app_sysinternals_tcpview.nss'
    separator()

    item(title="Misc"  image=[E253,GREY] vis='Static' sep='Both' col)
    import '@app.dir/NSS/_3_items/itm_app_audacity.nss'
    import '@app.dir/NSS/_3_items/itm_app_bambustudio.nss'
    import '@app.dir/NSS/_3_items/itm_app_bulkrenameutility.nss'
    import '@app.dir/NSS/_3_items/itm_app_davinciresolve.nss'
    import '@app.dir/NSS/_3_items/itm_app_filezilla.nss'
    import '@app.dir/NSS/_3_items/itm_app_gsmartcontrol.nss'
    import '@app.dir/NSS/_3_items/itm_app_irfanview.nss'
    import '@app.dir/NSS/_3_items/itm_app_kdenlive.nss'
    import '@app.dir/NSS/_3_items/itm_app_libreoffice.nss'
    import '@app.dir/NSS/_3_items/itm_app_notepad++.nss'
    import '@app.dir/NSS/_3_items/itm_app_pdf24.nss'
    import '@app.dir/NSS/_3_items/itm_app_powertoys.nss'
    import '@app.dir/NSS/_3_items/itm_app_qbittorrent.nss'
    import '@app.dir/NSS/_3_items/itm_app_rufus.nss'
    import '@app.dir/NSS/_3_items/itm_app_rustdesk.nss'
    import '@app.dir/NSS/_3_items/itm_app_sharex.nss'
    import '@app.dir/NSS/_3_items/itm_app_sonicvisualiser.nss'
    import '@app.dir/NSS/_3_items/itm_app_vlc.nss'
    import '@app.dir/NSS/_3_items/itm_app_winmerge.nss'
    separator()

}
