
/* cmd='python.exe' args='@py_visualize_directory_tree_open' */

// -> util: visualize directory (dirtree)
$py_visualize_dir_file = '"@dir_py/utils/explorer/visualize_directory_tree.py" "@sel.dir"'
$py_visualize_dir_open = '"@dir_py/utils/explorer/visualize_directory_tree.py" "@sel.path" --open-file'
$py_visualize_dir_view = '"@dir_py/utils/explorer/visualize_directory_tree.py" "@sel.path" --open-file --remove-file'

// -> close duplicate explorer windows
$py_close_duplicate_explorer_windows = '"@dir_py/utils/explorer/close_duplicate_explorer_windows.py"'

