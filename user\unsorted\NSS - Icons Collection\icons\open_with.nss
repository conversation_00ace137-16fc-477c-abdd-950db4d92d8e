@open_with='<svg fill="none" viewBox="0 0 16 16">
<g clip-path="url(#clip0)">
<path fill="@color3" d="M4 1.5H2a.5.5 0 00-.5.5v2a.5.5 0 00.5.5h2a.5.5 0 00.5-.5V2a.5.5 0 00-.5-.5zM4 6.5H2a.5.5 0 00-.5.5v2a.5.5 0 00.5.5h2a.5.5 0 00.5-.5V7a.5.5 0 00-.5-.5zM4 11.5H2a.5.5 0 00-.5.5v2a.5.5 0 00.5.5h2a.5.5 0 00.5-.5v-2a.5.5 0 00-.5-.5z"/>
<path fill="@image.color1" d="M1 2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 01-1 1H2a1 1 0 01-1-1V2zm3 0H2v2h2V2zM1 7a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 01-1 1H2a1 1 0 01-1-1V7zm3 0H2v2h2V7zm-3 5a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 01-1 1H2a1 1 0 01-1-1v-2zm3 0H2v2h2v-2zM6.5 2a.5.5 0 000 1h8a.5.5 0 000-1h-8zM6.5 7a.5.5 0 000 1h.43c.28-.37.61-.70.98-1H6.5z"/>
<path fill="@image.color2" d="M7 11.5a4.5 4.5 0 119 0 4.5 4.5 0 01-9 0z"/>
<path fill="@if(theme.islight,'#fff','#000')" d="M13 12.5a.5.5 0 101 0v-3a.5.5 0 00-.5-.5h-3a.5.5 0 100 1h1.73l-2.61 2.61a.54.54 0 10.76.76L13 10.76V12.5z"/>
</g>
@clipPath
</svg>'
