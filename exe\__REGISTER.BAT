:: =============================================================================
:: cmd: initialize
:: =============================================================================
@ECHO OFF
SETLOCAL ENABLEDELAYEDEXPANSION
IF EXIST "%~1" (CD /D "%~1") ELSE (CD /D "%~dp0")
SET "__init_path__=%CD%"


:: =============================================================================
:: shell: locate
:: =============================================================================
SET "__shell_dir__=__SHELL__\exe"
SET "__shell_exe__=%__shell_dir__%\shell.exe"
SET "__shell_log__=%__shell_dir__%\shell.log"
:LocateShell
    IF EXIST "%CD%\%__shell_exe__%" (GOTO RegisterShell)
    SET "tmp=%CD%" & CD .. & IF "%CD%"=="%tmp%" (
        ECHO Not found: %__shell_exe__%
        CD /D "%__init_path__%"
        PAUSE>NUL & EXIT /B
    )
GOTO LocateShell


:: =============================================================================
:: shell: register
:: =============================================================================
:: shell -[options]
::   -register (-r)   <- Registering.
::   -unregister (-u) <- Unregistering.
::   -restart (-re)   <- Restart Windows Explorer.
::   -treat           <- Disable Windows 11 context menu.
::   -silent (-s)     <- Prevents displaying messages.
::   -?               <- Display this help message.
:RegisterShell
    ECHO shell.log: "%CD%\%__shell_log__%"
    ECHO shell.exe: "%CD%\%__shell_exe__%" -register -restart
    ECHO.
    TYPE NUL > "%CD%\%__shell_log__%"
    "%CD%\%__shell_exe__%" -register -restart
    explorer %__init_path__%


:: =============================================================================
:: cmd: exit
:: =============================================================================
ECHO.
ECHO.
ECHO Breaks/Exits in 10 seconds ...
PING 127.0.0.1 -n 10 > NUL
EXIT /B
