<h5>REGEX</h5>
<p>regex functions</p>
<br>
<section id="regex.match" class="my-5">
	<h5>regex.match</h5>
	<p>Returns true if a match exists, false otherwise.</p>
	<p>Syntax</p>
	<pre><code>regex.match(str, pattern)</code></pre>
</section>
<section id="regex.matches" class="my-5">
	<h5>regex.matches</h5>
	<p>Returns an array of strings</p>
	<p>Syntax</p>
	<pre><code>regex.matches(str, pattern)</code></pre>pre>
</section>
<section id="regex.replace" class="my-5">
	<h5>regex.replace</h5>
	<p>Syntax</p>
	<pre><code>regex.replace(str, pattern, "new str")</code></pre>
</section>
