
//
$PY_GIT<PERSON>ZEANALYZER_DIR = '@user.desktop\my\flow\home\__GOTO__\Scripts\Python\Py_Cli_Utils\py__GitSizeAnalyzer'
$PY_GITSIZEANALYZER_EXE = '@PY_GITSIZEANALYZER_DIR\venv\Scripts\python.exe'
$PY_GITSIZEANALYZER_APP = '@PY_GITSIZEANALYZER_DIR\main.py'
//

// Context: Explorer
$PY_GITSIZEANALYZER_EXPLORER = '-i "@sel.dir" --prompt'
item(
    title="&GitSizeAnalyzer"
    keys="py"
    type='File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    //
    image=[E17C,GREY] image-sel=[E17C,PURPLE]
    tip=['"@PY_GITSIZEANALYZER_APP" @PY_GITSIZEANALYZER_EXPLORER',TIP3,0.75]
    //
    admin=keys.rbutton()
    cmd=if(KEYS_EXE_OPEN_EXE,('"@PY_GITSIZEANALYZER_EXE"'))
    args='"@PY_GITSIZEANALYZER_APP" @PY_GITSIZEANALYZER_EXPLORER'
    //
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@PY_GITSIZEANALYZER_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@PY_GITSIZEANALYZER_APP')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@PY_GITSIZEANALYZER_DIR')),
    }
)
// Context: Taskbar
$PY_GITSIZEANALYZER_TASKBAR = '-i "" --prompt'
item(
    title="&GitSizeAnalyzer"
    keys="py"
    type='Taskbar'
    //
    image=[E17C,GREY]
    image-sel=[E17C,PURPLE]
    //
    admin=keys.rbutton()
    cmd=if(KEYS_EXE_OPEN_EXE,('"@PY_GITSIZEANALYZER_EXE"'))
    args='"@PY_GITSIZEANALYZER_APP" @PY_GITSIZEANALYZER_TASKBAR'
    tip=['"@PY_GITSIZEANALYZER_APP" @PY_GITSIZEANALYZER_TASKBAR',TIP3,0.75]
    //
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@PY_GITSIZEANALYZER_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@PY_GITSIZEANALYZER_APP')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@PY_GITSIZEANALYZER_DIR')),
    }
)
