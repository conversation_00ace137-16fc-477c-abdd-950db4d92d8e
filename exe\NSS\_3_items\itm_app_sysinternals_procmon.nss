
//
$APP_USER_PROCMON_DIR = '@user.desktop\my\flow\home\__GOTO__\Apps\grp_sysinternalssuite\app_procmon\exe'
$APP_USER_PROCMON_EXE = '@APP_USER_PROCMON_DIR\procmon64.exe'
$APP_USER_PROCMON_TIP = "..."+str.trimstart('@APP_USER_PROCMON_EXE','@app.dir')

// -> Diskmon
item(
    title=":  &Procmon"
    keys="exe"
    type='Taskbar|File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    //
    image=APP_USER_PROCMON_EXE
    tip=[APP_USER_PROCMON_TIP,TIP3,0.5]
    //
    admin=keys.rbutton()
    cmd=if(KEYS_EXE_OPEN_EXE,(APP_USER_PROCMON_EXE))
    args='/AcceptEula'
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_PROCMON_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_PROCMON_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_PROCMON_DIR')),
    }
)
