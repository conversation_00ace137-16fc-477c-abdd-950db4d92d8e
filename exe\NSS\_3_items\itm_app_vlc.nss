
//
$APP_USER_VLC_DIR = '@user.desktop\my\flow\home\__GOTO__\Apps\app_vlc\exe'
$APP_USER_VLC_EXE = '@APP_USER_VLC_DIR\vlc.exe'
$APP_USER_VLC_TIP = "..."+str.trimstart('@APP_USER_VLC_EXE','@app.dir')

// context: file
item(
    title  = ":  &VLC"
    keys   = "exe"
    type   = 'File'
    args   = '"@sel.file"'
    where  = str.equals(sel.file.ext,[".aac",".flac",".m4a",".mp3",".ogg",".wav",".wma",".mov",".mkv",".mp4"])
    //
    image  = APP_USER_VLC_EXE
    tip    = [APP_USER_VLC_TIP,TIP3,0.8]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_USER_VLC_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_VLC_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_VLC_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_VLC_DIR')),
    }
)
// context: directory
item(
    title  = ":  &VLC"
    keys   = "exe"
    type   = 'Desktop|Dir|Drive|Back.Dir|Back.Drive'
    args   = '"@sel.dir"'
    //
    image  = APP_USER_VLC_EXE
    tip    = [APP_USER_VLC_TIP,TIP3,0.8]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_USER_VLC_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_VLC_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_VLC_EXE')),
        cmd=if(KEYS_EXE_GOTO_NSS,('@APP_SUBLIMETEXT_NSS')),
        cmd=if(KEYS_EXE_GOTO_SRC,('@APP_SUBLIMETEXT_SRC')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_VLC_DIR')),
    }
)
// Context: Taskbar
item(
    title  = ":  &VLC"
    keys   = "exe"
    type   = 'Taskbar'
    args   = ''
    //
    image  = APP_USER_VLC_EXE
    tip    = [APP_USER_VLC_TIP,TIP3,0.8]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_USER_VLC_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_VLC_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_VLC_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_VLC_DIR')),
    }
)
