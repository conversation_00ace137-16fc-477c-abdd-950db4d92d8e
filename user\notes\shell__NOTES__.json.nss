// =============================================================================
//
// Windows paths
- "sys.appdata"
- "sys.bin"
- "sys.bin32"
- "sys.bin64"
- "sys.directory (sys.dir)"
- "sys.path"
- "sys.prog"
- "sys.prog32"
- "sys.programdata"
- "sys.root"
- "sys.temp"
- "sys.templates"
- "sys.users"
- "sys.wow"
- "sys.is_primary_monitor"
// =============================================================================
//
// sys.datetime
- "sys.datetime"
- "sys.datetime.date"
- "sys.datetime.date_day"
- "sys.datetime.date_dayofweek"
- "sys.datetime.date_month"
- "sys.datetime.date_y"
- "sys.datetime.date_year"
- "sys.datetime.date_yy"
- "sys.datetime.short"
- "sys.datetime.time"
- "sys.datetime.time_hour"
- "sys.datetime.time_milliseconds"
- "sys.datetime.time_min"
- "sys.datetime.time_minute"
- "sys.datetime.time_ms"
- "sys.datetime.time_pm"
- "sys.datetime.time_second"
// =============================================================================
//
// Functions that are used with the current item in the context menu
this.type     // Returns the type of the current item [item = 0, menu = 1, separator = 2]
this.checked  // Returns true if the current item is checked
this.pos      // Returns the position of the current item in the context menu
this.disabled // Returns true if the current item is disabled
this.sys      // Returns true if the current item is a system item
this.title    // Returns the title of the current item
this.id       // Returns the ID of the current item
this.count    // Returns the number of items in the context menu
this.length   // Returns the length of the current item's title
// =============================================================================
//
// Functions to return the path of user directories
- "user.home"
- "user.appdata"
- "user.contacts"
- "user.desktop"
- "user.directory (user.dir)"
- "user.documents"
- "user.documentslibrary"
- "user.downloads"
- "user.favorites"
- "user.libraries"
- "user.localappdata"
- "user.music"
- "user.personal"
- "user.pictures"
- "user.profile"
- "user.quicklaunch"
- "user.sendto"
- "user.startmenu"
- "user.temp"
- "user.templates"
- "user.videos"
// =============================================================================
//
// https://nilesoft.org/docs/functions/window
// Syntax
"window.is_taskbar"   // Returns true if the window handle is for the Taskbar window
"window.is_desktop"   // Returns true if the window handle is for the Desktop window
"window.is_explorer"  // Returns true if the window handle is for the Explorer window
"window.is_tree"      // Returns true if the window handle is for the Side window
"window.is_edit"      // Returns true if the window handle is for the Edit menu
"window.is_start"     // Returns true if the window handle is for the Win+X menu
//
"window.send(name, msg, wparam, lparam)"  // Search for the window by name and send the specified message
"window.post(name, msg, wparam, lparam)"  // Search for the window by name and send the specified message without waiting
//
"window.send(null, msg, wparam, lparam)"  // Send the specified message to current window.
"window.post(null, msg, wparam, lparam)"  // Send the specified message without waiting to current window.
//
"window.command(command)"       // Send WM_COMMAND to current window.
"window.command(command,name)"  // Search for the window by name and send the command to it.
//
"window.handle"
"window.name"
"window.title"
"window.owner"
"window.parent"
"window.parent.handle"
"window.parent.name"
"window.is_contextmenuhandler"


// =============================================================================
//
// https://nilesoft.org/docs/functions/command
- "command.cascade_windows"
- "command.copy_to_folder"
- "command.customize_this_folder"
- "command.find"
- "command.folder_options"
- "command.invert_selection"
- "command.minimize_all_windows"
- "command.move_to_folder"
- "command.redo"
- "command.refresh"
- "command.restart_explorer"
- "command.restore_all_windows"
- "command.run"
- "command.search"
- "command.select_all"
- "command.select_none"
- "command.show_windows_side_by_side"
- "command.show_windows_stacked"
- "command.switcher"
- "command.toggle_desktop"
- "command.toggleext"
- "command.togglehidden"
- "command.undo"





// Dynamic vs Static
// =============================================================================
// - These terms are not very self-explanatory. Even after understanding the
//   concept, I find the terms still meaningless or rather a bit confusion.
//   static seems to refer to the "existing" items, as in "already built",
//   hence "static". Whereas dynamic might refer to the action of
//   "creating/building". However, the static section is about changing items
//   (hence also dynamic) while the dynamic section is not necessarily as
//   dynamic as it might seem.
// -----------------------------------------------------------------------------
// set | settings
// static | update
// {
//     item | change
// }
// dynamic | new | insert




// ID'S
// =============================================================================


modify(
    where=!this.id(
        // File and Folder Operations:
        id.copy, id.copy_as_path, id.copy_here, id.copy_path, id.copy_to,
        id.copy_to_folder, id.cut, id.delete, id.move_here, id.move_to,
        id.move_to_folder, id.new, id.new_folder, id.new_item, id.open,
        id.open_file_location, id.open_folder_location, id.open_with,
        id.paste, id.paste_shortcut, id.rename,
        id.restore_previous_versions, id.send_to, id.extract_all,
        id.extract_to, id.create_shortcut, id.create_shortcuts_here,
        id.compressed,
        // View and Layout:
        id.extra_large_icons, id.large_icons, id.medium_icons, id.small_icons,
        id.list, id.details, id.tiles, id.content, id.sort_by,
        id.group_by, id.arrange_by, id.align_icons_to_grid,
        id.auto_arrange_icons, id.show_desktop_icons,
        id.show_file_extensions, id.show_hidden_files, id.show_libraries,
        id.show_network, id.show_touch_keyboard_button,
        id.show_touchpad_button, id.show_cortana_button,
        id.show_people_on_the_taskbar, id.show_task_view_button,
        id.customize_notification_icons, id.customize_this_folder,
        // System and Settings:
        id.settings, id.control_panel, id.device_manager, id.display_settings,
        id.file_explorer, id.folder_options, id.power_options,
        id.task_manager, id.taskbar_settings, id.adjust_date_time,
        id.options, id.personalize, id.desktop,
        // Network and Sharing:
        id.cast_to_device, id.disconnect, id.disconnect_network_drive,
        id.map_as_drive, id.map_network_drive, id.give_access_to,
        id.share, id.share_with, id.make_available_offline,
        id.make_available_online,
        // Utilities and Tools:
        id.command_prompt, id.open_command_prompt,
        id.open_command_window_here, id.open_powershell_window_here,
        id.open_windows_powershell, id.run, id.run_as_administrator,
        id.run_as_another_user, id.troubleshoot_compatibility, id.search,
        id.refresh, id.cleanup,
        // Access and Security:
        id.install, id.turn_off_bitlocker, id.turn_on_bitlocker, id.manage,
        // Media and Devices:
        id.autoplay, id.eject, id.erase_this_disc, id.play, id.print, id.mount,
        // Window Management:
        id.cascade_windows, id.show_windows_side_by_side,
        id.show_windows_stacked, id.lock_all_taskbars, id.lock_the_taskbar,
        // Special Features:
        id.cortana, id.news_and_interests, id.store,
        // Miscellaneous:
        id.add_a_network_location, id.cancel, id.collapse,
        id.collapse_all_groups, id.collapse_group, id.configure, id.edit,
        id.empty_recycle_bin, id.exit_explorer, id.expand,
        id.expand_all_groups, id.expand_group, id.format,
        id.include_in_library, id.insert_unicode_control_character,
        id.merge, id.more_options, id.open_as_portable, id.open_autoplay,
        id.open_in_new_process, id.open_in_new_tab, id.open_in_new_window,
        id.open_new_tab, id.open_new_window,
        id.pin_current_folder_to_quick_access, id.pin_to_quick_access,
        id.pin_to_start, id.pin_to_taskbar, id.preview, id.properties,
        id.reconversion, id.redo, id.remove_from_quick_access,
        id.remove_properties, id.restore, id.restore_default_libraries,
        id.rotate_left, id.rotate_right, id.select_all,
        id.set_as_desktop_background, id.set_as_desktop_wallpaper,
        id.next_desktop_background, id.shield, id.show_pen_button,
        id.show_the_desktop, id.show_this_pc, id.undo,
        id.unpin_from_quick_access, id.unpin_from_start,
        id.unpin_from_taskbar, id.view
    )
    menu="Apps"
)

// ICONS (some of these also works as commands)
// =============================================================================
[
    "add_a_network_location", "adjust_date_time", "align_icons_to_grid", "arrange_by",
    "auto_arrange_icons", "autoplay", "cancel", "cascade_windows", "cast_to_device",
    "cleanup", "collapse", "collapse_all_groups", "collapse_group", "command_prompt",
    "compressed", "configure", "content", "control_panel", "copy", "copy_as_path",
    "copy_here", "copy_path", "copy_to", "copy_to_folder", "cortana",
    "create_shortcut", "create_shortcuts", "create_shortcuts_here",
    "customize_notification_icons", "customize_this_folder", "cut", "delete",
    "desktop", "details", "device_manager", "disconnect", "disconnect_network_drive",
    "display_settings", "edit", "eject", "empty_recycle_bin", "erase_this_disc",
    "exit_explorer", "expand", "expand_all_groups", "expand_group",
    "expand_to_current_folder", "extra_large_icons", "extract_all", "extract_to",
    "file_explorer", "folder_options", "format", "give_access_to", "group_by",
    "include_in_library", "insert_unicode_control_character", "install",
    "large_icons", "list", "lock_all_taskbars", "lock_the_taskbar",
    "make_available_offline", "make_available_online", "manage", "map_as_drive",
    "map_network_drive", "medium_icons", "merge", "more_options", "mount",
    "move_here", "move_to", "move_to_folder", "new", "new_folder", "new_item",
    "news_and_interests", "next_desktop_background", "open", "open_as_portable",
    "open_autoplay", "open_command_prompt", "open_command_window_here",
    "open_file_location", "open_folder_location", "open_in_new_process",
    "open_in_new_tab", "open_in_new_window", "open_new_tab", "open_new_window",
    "open_powershell_window_here", "open_windows_powershell", "open_with", "options",
    "paste", "paste_shortcut", "personalize", "pin_current_folder_to_quick_access",
    "pin_to_quick_access", "pin_to_start", "pin_to_taskbar", "play",
    "play_with_windows_media_player", "power_options", "preview", "print",
    "properties", "reconversion", "redo", "refresh", "remove_from_quick_access",
    "remove_properties", "rename", "restore", "restore_default_libraries",
    "restore_previous_versions", "rotate_left", "rotate_right", "run",
    "run_as_administrator", "run_as_another_user", "search", "select_all", "send_to",
    "set_as_desktop_background", "set_as_desktop_wallpaper", "settings", "share",
    "share_with", "shield", "show_all_folders", "show_cortana_button",
    "show_desktop_icons", "show_file_extensions", "show_hidden_files",
    "show_libraries", "show_network", "show_pen_button",
    "show_people_on_the_taskbar", "show_task_view_button", "show_the_desktop",
    "show_this_pc", "show_touch_keyboard_button", "show_touchpad_button",
    "show_windows_side_by_side", "show_windows_stacked", "small_icons", "sort_by",
    "store", "task_manager", "taskbar_settings", "tiles",
    "troubleshoot_compatibility", "turn_off_bitlocker", "turn_on_bitlocker", "undo",
    "unpin_from_quick_access", "unpin_from_start", "unpin_from_taskbar", "view",
]


// PROPERTIES
// =============================================================================
{ id: 'admin',    details: 'Execute the command with administrative permissions.' },
{ id: 'arg',      details: 'The command line parameters to pass to the command property of a menuitem.' },
{ id: 'args',     details: 'The command line parameters to pass to the command property of a menuitem.' },
{ id: 'checked',  details: 'Type of select option.' },
{ id: 'cmd',      details: 'The command associated with the menuitem. Occurs when the menuitem is clicked or selected using a shortcut key or access key defined for the menuitem.' },
{ id: 'col',      details: 'Create a new column.' },
{ id: 'default',  details: 'Specifies that the item is the default. A menu can contain only one default menuitem, which is displayed in bold.' },
{ id: 'dir',      details: 'Specifies the working directory to execute the command in.' },
{ id: 'expanded', details: 'Move all immediate menuitems to the parent menu.' },
{ id: 'find',     details: 'For static items (required) Apply the current item\'s process instructions to any existing menuitem if their title property matches the pattern of the current item\'s find property.  For dynamic items (optional) Display the current menuitem if the pattern of its find property matches the path name or path extension of the selected files.' },
{ id: 'icon',     details: 'The icon that appears in a menuitem. This property can be assigned as image files, resource icons, glyph or color. With one of the following parameters' },
{ id: 'image',    details: 'The icon that appears in a menuitem. This property can be assigned as image files, resource icons, glyph or color. With one of the following parameters' },
{ id: 'invoke',   details: 'Set execution type' },
{ id: 'keys',     details: 'Show keyboard shortcuts' },
{ id: 'menu',     details: 'Move current menuitem to another menu.' },
{ id: 'mode',     details: 'Display menuitem by type of selection.' },
{ id: 'parent',   details: 'Move current menuitem to another menu.' },
{ id: 'pos',      details: 'The position at which a menuitem should be inserted into the menu.' },
{ id: 'sep',      details: 'Add a separator to the menuitem.' },
{ id: 'tip',      details: 'Show a tooltip for the current menuor item.' },
{ id: 'title',    details: 'Sets the caption of the menuitem.' },
{ id: 'type',     details: 'Specifies the types of objects for which the menuitem will be displayed. Separate multiple types with the pipe character (|), in which case the menuitem is displayed if any of the given types is matched. To exclude a given type, prefix its value with the tilde character (~).' },
{ id: 'verb',     details: 'Specifies the default operation for the selected file' },
{ id: 'vis',      details: 'Sets the visibility of a menuitem.' },
{ id: 'wait',     details: 'Wait for the command to complete.' },
{ id: 'where',    details: 'Process given menuitem if true is returned. Allows the evaluation of arbitrary expressions.' },
{ id: 'window',   details: 'Controls how the window of the executed command is to be shown.' },


// CONTEXT-SPECIFIC MENUS
// =============================================================================
// -> Modes: Display menuitem by type of selection.
item(title='Subscene' image=[\uE270, #22A7F2] pos="middle" cmd='chrome' args='"https://subscene.com/subtitles/searchbytitle"')

{ id: 'none',         details: 'Display menuitem when there is no selection' },
//
{ id: 'single',       details: 'Display menuitem when there is a single object selected' },
{ id: 'multi_unique', details: 'Display menuitem when multiple objects of the same type are selected' },
{ id: 'multi_single', details: 'Display menuitem when multiple files with a single file extension are selected' },
{ id: 'multiple',     details: 'Display any type of selection, unless there is none' },
// -> Examples:
// Displayed regardless of context
menu(type='~Taskbar' mode="none" title="Test -> Mode: none" image=icon.pin) {
    item(title='Test -> Mode: none' cmd=msg('Hello @user.name'))
}
// Displayed when a single type is selected (e.g. a single file/folder)
menu(type='~Taskbar' mode="single" title="Test -> Mode: single" image=icon.pin) {
    item(title='Test -> Mode: single' cmd=msg('Hello @user.name'))
}
// Displayed when multiple similar types is selected (e.g. multiple files)
menu(type='~Taskbar' mode="multi_unique" title="Test -> Mode: multi_unique" image=icon.pin) {
    item(title='Test -> Mode: multi_unique' cmd=msg('Hello @user.name'))
}
// Displayed when multiple identical types is selected (e.g. multiple .txt files)
menu(type='~Taskbar' mode="multi_single" title="Test -> Mode: multi_single" image=icon.pin) {
    item(title='Test -> Mode: multi_single' cmd=msg('Hello @user.name'))
}
// Displayed if any of the given types is matched.
menu(type='~Taskbar' mode="multiple" title="Test -> Mode: multiple" image=icon.pin) {
    item(title='Test -> Mode: multiple' cmd=msg('Hello @user.name'))
}
// =============================================================================
// -> Types:
// - Specifies the types of objects for which the menuitem will be displayed.
//   Separate multiple types with the pipe character (|), in which case the
//   menuitem is displayed if any of the given types is matched. To exclude a
//   given type, prefix its value with the tilde character (~).
{ id: '*',          details: 'if any type is selected' },
{ id: 'File',       details: 'if files are selected' },
{ id: 'Directory',  details: 'if directories are selected' },
{ id: 'Dir',        details: 'if directories are selected' },
{ id: 'Drive',      details: 'if drives are selected' },
{ id: 'Usb',        details: 'if USB flash-drives are selected' },
{ id: 'Dvd',        details: 'if DVD-ROM drives are selected' },
{ id: 'Fixed',      details: 'if fixed drives are selected. Such drives have a fixed media (e.g. a hard drive)' },
{ id: 'Vhd',        details: 'if Virtual Hard Disks are selected' },
{ id: 'Removable',  details: 'if the selected drives have removable media (e.g. a thumb drive).' },
{ id: 'Remote',     details: 'if the selected remote (network) drives are selected.' },
{ id: 'Back',       details: 'if the background of all types are selected.' },
{ id: 'Desktop',    details: 'if the Desktop is selected.' },
{ id: 'Namespace',  details: 'if Namespaces are selected. Can be virtual objects such as My Network Places and Recycle Bin.' },
{ id: 'Computer',   details: 'if My Computer is selected' },
{ id: 'Recyclebin', details: 'if the Recycle bin is selected.' },
{ id: 'Taskbar',    details: 'if the Taskbar is selected.' },
// -> Examples:
// Display menuitem when file(s) are selected
menu(type='File' mode="none" title="Test -> Type: file" image=icon.pin) {
    item(title='Test -> Type: file' cmd=msg('Hello @user.name'))
}
// Display menuitem when folder(s) are selected
menu(type='Dir' mode="none" title="Test -> Type: dir" image=icon.pin) {
    item(title='Test -> Type: dir' cmd=msg('Hello @user.name'))
}
// Display menuitem when anything except file(s) is selected
menu(type='~File' mode="none" title="Test -> Type: ~File" image=icon.pin) {
    item(title='Test -> Type: ~File' cmd=msg('Hello @user.name'))
}

// =============================================================================
// MENUITEM PROPERTIES
// - This set of properties describe the appearance and location of a given
//   menuitem. For modify-items, this is the target menuitem. For dynamic
//   entries, this is the newly created menuitem.
// -----------------------------------------------------------------------------
// -> Title: Sets the caption of the menuitem.
{ id: 'title', details: 'Sets the caption of the menuitem.' },
# =============================================================================
# -> Visibility: Specify how a menu/item is displayed and interacted with.
{ id: 'true',    details: 'Enables the menuitem.' },
{ id: 'false',   details: 'Hides the menuitem.' },
{ id: 'hidden',  details: 'Hides the menuitem.' },
{ id: 'disable', details: 'Disables the menuitem.' },
{ id: 'static',  details: 'Displays menuitem as label, with or without an image.' },
{ id: 'label',   details: 'Displays menuitem as label without an image.' },
# -> Examples:
"""
// Hides the menuitem
menu(type='~Taskbar' mode="none" vis="hidden" title="Test -> Visibility: hidden" image=icon.pin) {
    item(title='Test -> Visibility: hidden' cmd=msg('Hello @user.name'))
}
// Disables the menuitem
menu(type='~Taskbar' mode="none" vis="disable" title="Test -> Visibility: disable" image=icon.pin) {
    item(title='Test -> Visibility: disable' cmd=msg('Hello @user.name'))
}
// Displays menuitem as label, with or without an image
menu(type='~Taskbar' mode="none" vis="static" title="Test -> Visibility: static" image=icon.pin) {
    item(title='Test -> Visibility: static' cmd=msg('Hello @user.name'))
}
// Displays menuitem as label without an image
menu(type='~Taskbar' mode="none" vis="label" title="Test -> Visibility: label" image=icon.pin) {
    item(title='Test -> Visibility: label' cmd=msg('Hello @user.name'))
}
"""
// -----------------------------------------------------------------------------
// -> Separator: Add a separator to the menuitem.
{ id: 'none',   details: 'Not adding a separator with the menuitem.' },
{ id: 'before', details: 'Add a separator before the menuitem.' },
{ id: 'after',  details: 'Add a separator after the menuitem.' },
{ id: 'both',   details: 'Add a separator before and after the menuitem.' },
// -> Examples:
menu(type='~Taskbar' mode="none" sep="both" title="Test -> Type: ~Taskbar" image=icon.pin) {
    item(title='Test -> Type: ~Taskbar' cmd=msg('Hello @user.name'))
}
// -----------------------------------------------------------------------------
// -> Position: Specify where to position the menu/item.
{ id: 'auto',   details: 'Positions the menuitem chronologically.' },
{ id: 'middle', details: 'Positions the menuitem in the middle of the contextmenu.' },
{ id: 'top',    details: 'Positions the menuitem at the top of the contextmenu.' },
{ id: 'bottom', details: 'Positions the menuitem at the bottom of the contextmenu.' },
// auto
menu(type='~Taskbar' mode="none" vis="disable" pos="auto" title="Test -> Position: auto" image=icon.pin) {
    item(title='Test -> Position: auto' cmd=msg('Hello @user.name'))
}
// middle
menu(type='~Taskbar' mode="none" vis="disable" pos="middle" title="Test -> Position: middle" image=icon.pin) {
    item(title='Test -> Position: middle' cmd=msg('Hello @user.name'))
}
// top
menu(type='~Taskbar' mode="none" vis="disable" pos="top" title="Test -> Position: top" image=icon.pin) {
    item(title='Test -> Position: top' cmd=msg('Hello @user.name'))
}
// bottom
menu(type='~Taskbar' mode="none" vis="disable" pos="bottom" title="Test -> Position: bottom" image=icon.pin) {
    item(title='Test -> Position: bottom' cmd=msg('Hello @user.name'))
}
// =============================================================================

{ id: 'admin',    details: 'Execute the command with administrative permissions.' },
{ id: 'arg',      details: 'The command line parameters to pass to the command property of a menuitem.' },
{ id: 'args',     details: 'The command line parameters to pass to the command property of a menuitem.' },
{ id: 'checked',  details: 'Type of select option.' },
{ id: 'cmd',      details: 'The command associated with the menuitem. Occurs when the menuitem is clicked or selected using a shortcut key or access key defined for the menuitem.' },
{ id: 'col',      details: 'Create a new column.' },
{ id: 'default',  details: 'Specifies that the item is the default. A menu can contain only one default menuitem, which is displayed in bold.' },
{ id: 'dir',      details: 'Specifies the working directory to execute the command in.' },
{ id: 'expanded', details: 'Move all immediate menuitems to the parent menu.' },
{ id: 'find',     details: 'For static items (required) Apply the current items process instructions to any existing menuitem if their title property matches the pattern of the current items find property. For dynamic items (optional) Display the current menuitem if the pattern of its find property matches the path name or path extension of the selected files. ' },
{ id: 'icon',     details: 'The icon that appears in a menuitem. This property can be assigned as image files, resource icons, glyph or color. With one of the following parameters' },
{ id: 'image',    details: 'The icon that appears in a menuitem. This property can be assigned as image files, resource icons, glyph or color. With one of the following parameters' },
{ id: 'invoke',   details: 'Set execution type' },
{ id: 'keys',     details: 'Show keyboard shortcuts' },
{ id: 'menu',     details: 'Move current menuitem to another menu.' },
{ id: 'mode',     details: 'Display menuitem by type of selection.' },
{ id: 'parent',   details: 'Move current menuitem to another menu.' },
{ id: 'pos',      details: 'The position at which a menuitem should be inserted into the menu.' },
{ id: 'sep',      details: 'Add a separator to the menuitem.' },
{ id: 'tip',      details: 'Show a tooltip for the current menuor item.' },
{ id: 'title',    details: 'Sets the caption of the menuitem.' },
{ id: 'type',     details: 'Specifies the types of objects for which the menuitem will be displayed. Separate multiple types with the pipe character (|), in which case the menuitem is displayed if any of the given types is matched. To exclude a given type, prefix its value with the tilde character (~).' },
{ id: 'verb',     details: 'Specifies the default operation for the selected file' },
{ id: 'vis',      details: 'Sets the visibility of a menuitem.' },
{ id: 'wait',     details: 'Wait for the command to complete.' },
{ id: 'where',    details: 'Process given menuitem if true is returned. Allows the evaluation of arbitrary expressions.' },
{ id: 'window',   details: 'Controls how the window of the executed command is to be shown.' },


// GENERAL NOTES
// =============================================================================
// - To find the Shell program folder, use shift+right-click on the Taskbar:
//   - Shift + Rightclick Taskbar

// =============================================================================
// Glyph icons: https://nilesoft.org/gallery/glyphs


