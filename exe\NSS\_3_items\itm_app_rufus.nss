
//
$APP_RUFUS_DIR = '@user.desktop\my\flow\home\__GOTO__\Apps\app_rufus\exe'
$APP_RUFUS_EXE = '@APP_RUFUS_DIR\rufus-4.6p.exe'
$APP_RUFUS_TIP = "..."+str.trimstart('@APP_RUFUS_EXE','@app.dir')
//
$APP_RUFUS_DIR_CFG = APP_RUFUS_DIR
$APP_RUFUS_DIR_NSS = '@app.dir\NSS\_3_items\user_apps\app_rufus'
$APP_RUFUS_DIR_SRC = '@user.desktop\my\flow\home\__GOTO__\Apps\app_rufus'
$APP_RUFUS_DIR_USR = '@user.desktop\my\flow\home\__GOTO__\Apps\app_rufus\user'

// Context: Taskbar
item(
    title  = ":  &<PERSON>"
    keys   = "exe"
    type   = 'Taskbar'
    args   = ''
    //
    image  = APP_RUFUS_EXE
    tip    = [APP_RUFUS_TIP,TIP3,1.0]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_RUFUS_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_RUFUS_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_RUFUS_EXE')),
        cmd=if(KEYS_EXE_GOTO_CFG,('@APP_RUFUS_DIR_CFG')),
        cmd=if(KEYS_EXE_GOTO_NSS,('@APP_RUFUS_DIR_NSS')),
        cmd=if(KEYS_EXE_GOTO_SRC,('@APP_RUFUS_DIR_SRC')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_RUFUS_DIR')),
    }
)
