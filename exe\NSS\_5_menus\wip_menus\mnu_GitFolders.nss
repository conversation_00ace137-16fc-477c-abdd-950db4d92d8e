// -> application library
menu(title="&Git Folders" type='Taskbar|Desktop|Drive|Back.Dir|Back.Drive' image=[E0C8,SOFT] image-sel=[E124,HOVER] sep='None') {

    //
    // item(column vis='Static' image=[E00A,DARK])
    // item(title="WORKFLOW" image=[E0E0,GREY] vis='Static' sep='both' )

    //
    item(title="&PRJ/GIT/JHP" image=[E22C,WHITE]  image-sel=[E22C,HOVER] cmd='@dir_prj'   tip=path.full('@dir_prj'))
    //

    // //
    // item(title="App Executable" image=[E14D,GREY] vis='Static' sep='Both' col)
    // //
    // item(title="&3dsMax"              image='@exe_3dsmax'         tip='@exe_3dsmax'         cmd='"@exe_3dsmax"'         admin=(keys.rbutton() OR keys.lbutton()))
    // item(title="&Adobe-Illustrator"   image='@exe_illustrator'    tip='@exe_illustrator'    cmd='"@exe_illustrator"'    admin=(keys.rbutton() OR keys.lbutton()))
    // item(title="&Adobe-Photoshop"     image='@exe_photoshop'      tip='@exe_photoshop'      cmd='"@exe_photoshop"'      admin=(keys.rbutton() OR keys.lbutton()))
    // item(title="&Audacity"            image='@exe_audacity'       tip='@exe_audacity'       cmd='"@exe_audacity"'       admin=(keys.rbutton() OR keys.lbutton()))
    // item(title="&Blender"             image='@exe_blender'        tip='@exe_blender'        cmd='"@exe_blender"'        admin=(keys.rbutton() OR keys.lbutton()))
    // item(title="&Bulk-Rename-Utility" image='@exe_bulkrename'     tip='@exe_bulkrename'     cmd='"@exe_bulkrename"'     admin=(keys.rbutton() OR keys.lbutton()))
    // item(title="&Chrome"              image='@exe_chrome'         tip='@exe_chrome'         cmd='"@exe_chrome"'         admin=(keys.rbutton() OR keys.lbutton()))
    // item(title="&Discord"             image='@exe_discord'        tip='@exe_discord'        cmd='"@exe_discord"'        admin=(keys.rbutton() OR keys.lbutton()))
    // item(title="&Dremel-3D-Slicer"    image='@exe_dremel3dslicer' tip='@exe_dremel3dslicer' cmd='"@exe_dremel3dslicer"' admin=(keys.rbutton() OR keys.lbutton()))
    // item(title="&Everything"          image='@exe_everything'     tip='@exe_everything'     cmd='"@exe_everything"'     admin=(keys.rbutton() OR keys.lbutton()))
    // item(title="&FileZilla"           image='@exe_filezilla'      tip='@exe_filezilla'      cmd='"@exe_filezilla"'      admin=(keys.rbutton() OR keys.lbutton()))
    // item(title="&Greenshot"           image='@app_greenshot'      tip='@app_greenshot'      cmd='"@app_greenshot"'      admin=(keys.rbutton() OR keys.lbutton()))
    // item(title="&Notepad++"           image='@exe_notepad_plus'   tip='@exe_notepad_plus'   cmd='"@exe_notepad_plus"'   admin=(keys.rbutton() OR keys.lbutton()))
    // item(title="&Python"              image='@exe_python'         tip='@exe_python'         cmd='"@exe_python"'         admin=(keys.rbutton() OR keys.lbutton()))
    // item(title="&qBittorrent"         image='@exe_qbittorrent'    tip='@exe_qbittorrent'    cmd='"@exe_qbittorrent"'    admin=(keys.rbutton() OR keys.lbutton()))
    // item(title="&Sourcetree"          image='@exe_sourcetree'     tip='@exe_sourcetree'     cmd='"@exe_sourcetree"'     admin=(keys.rbutton() OR keys.lbutton()))
    // item(title="&Sublime-Text"        image='@exe_sublime'        tip='@exe_sublime'        cmd='"@exe_sublime"'        admin=(keys.rbutton() OR keys.lbutton()))
    // item(title="&VLC"                 image='@exe_vlc'            tip='@exe_vlc'            cmd='"@exe_vlc"'            admin=(keys.rbutton() OR keys.lbutton()))
    // item(title="&VSCodium"            image='@exe_vscodium'       tip='@exe_vscodium'       cmd='"@exe_vscodium"'       admin=(keys.rbutton() OR keys.lbutton()))
    // item(title="&WinaeroTweaker"      image='@exe_winaerotweaker' tip='@exe_winaerotweaker' cmd='"@exe_winaerotweaker"' admin=(keys.rbutton() OR keys.lbutton()))
    // item(title="&WinMerge"            image='@exe_winmerge'       tip='@exe_winmerge'       cmd='"@exe_winmerge"'       admin=(keys.rbutton() OR keys.lbutton()))

    // //
    // item(title="App Folders" image=[E0E8,GREY] vis='Static' sep='Both' col)
    // //
    // item(title="&3dsMax"              image=[E0E8,SOFT] image-sel=[E0E8,HOVER] tip=path.full('@dir_3dsmax')         commands{cmd=if(keys.rbutton(), clipboard.set(path.full('@dir_3dsmax'))),         cmd=('"@dir_3dsmax"')})
    // item(title="&Adobe-Illustrator"   image=[E0E8,SOFT] image-sel=[E0E8,HOVER] tip=path.full('@dir_illustrator')    commands{cmd=if(keys.rbutton(), clipboard.set(path.full('@dir_illustrator'))),    cmd=('"@dir_illustrator"')})
    // item(title="&Adobe-Photoshop"     image=[E0E8,SOFT] image-sel=[E0E8,HOVER] tip=path.full('@dir_photoshop')      commands{cmd=if(keys.rbutton(), clipboard.set(path.full('@dir_photoshop'))),      cmd=('"@dir_photoshop"')})
    // item(title="&Audacity"            image=[E0E8,SOFT] image-sel=[E0E8,HOVER] tip=path.full('@dir_audacity')       commands{cmd=if(keys.rbutton(), clipboard.set(path.full('@dir_audacity'))),       cmd=('"@dir_audacity"')})
    // item(title="&Blender"             image=[E0E8,SOFT] image-sel=[E0E8,HOVER] tip=path.full('@dir_blender')        commands{cmd=if(keys.rbutton(), clipboard.set(path.full('@dir_blender'))),        cmd=('"@dir_blender"')})
    // item(title="&Bulk-Rename-Utility" image=[E0E8,SOFT] image-sel=[E0E8,HOVER] tip=path.full('@dir_bulkrename')     commands{cmd=if(keys.rbutton(), clipboard.set(path.full('@dir_bulkrename'))),     cmd=('"@dir_bulkrename"')})
    // item(title="&Chrome"              image=[E0E8,SOFT] image-sel=[E0E8,HOVER] tip=path.full('@dir_chrome')         commands{cmd=if(keys.rbutton(), clipboard.set(path.full('@dir_chrome'))),         cmd=('"@dir_chrome"')})
    // item(title="&Discord"             image=[E0E8,SOFT] image-sel=[E0E8,HOVER] tip=path.full('@dir_discord')        commands{cmd=if(keys.rbutton(), clipboard.set(path.full('@dir_discord'))),        cmd=('"@dir_discord"')})
    // item(title="&Dremel-3D-Slicer"    image=[E0E8,SOFT] image-sel=[E0E8,HOVER] tip=path.full('@dir_dremel3dslicer') commands{cmd=if(keys.rbutton(), clipboard.set(path.full('@dir_dremel3dslicer'))), cmd=('"@dir_dremel3dslicer"')})
    // item(title="&Everything"          image=[E0E8,SOFT] image-sel=[E0E8,HOVER] tip=path.full('@dir_everything')     commands{cmd=if(keys.rbutton(), clipboard.set(path.full('@dir_everything'))),     cmd=('"@dir_everything"')})
    // item(title="&FileZilla"           image=[E0E8,SOFT] image-sel=[E0E8,HOVER] tip=path.full('@dir_filezilla')      commands{cmd=if(keys.rbutton(), clipboard.set(path.full('@dir_filezilla'))),      cmd=('"@dir_filezilla"')})
    // item(title="&Greenshot"           image=[E0E8,SOFT] image-sel=[E0E8,HOVER] tip=path.full('@dir_greenshot')      commands{cmd=if(keys.rbutton(), clipboard.set(path.full('@dir_greenshot'))),      cmd=('"@dir_greenshot"')})
    // item(title="&Notepad++"           image=[E0E8,SOFT] image-sel=[E0E8,HOVER] tip=path.full('@dir_notepad_plus')   commands{cmd=if(keys.rbutton(), clipboard.set(path.full('@dir_notepad_plus'))),   cmd=('"@dir_notepad_plus"')})
    // item(title="&Python"              image=[E0E8,SOFT] image-sel=[E0E8,HOVER] tip=path.full('@dir_python')         commands{cmd=if(keys.rbutton(), clipboard.set(path.full('@dir_python'))),         cmd=('"@dir_python"')})
    // item(title="&qBittorrent"         image=[E0E8,SOFT] image-sel=[E0E8,HOVER] tip=path.full('@dir_qbittorrent')    commands{cmd=if(keys.rbutton(), clipboard.set(path.full('@dir_qbittorrent'))),    cmd=('"@dir_qbittorrent"')})
    // item(title="&Sourcetree"          image=[E0E8,SOFT] image-sel=[E0E8,HOVER] tip=path.full('@dir_sourcetree')     commands{cmd=if(keys.rbutton(), clipboard.set(path.full('@dir_sourcetree'))),     cmd=('"@dir_sourcetree"')})
    // item(title="&Sublime-Text"        image=[E0E8,SOFT] image-sel=[E0E8,HOVER] tip=path.full('@dir_sublime')        commands{cmd=if(keys.rbutton(), clipboard.set(path.full('@dir_sublime'))),        cmd=('"@dir_sublime"')})
    // item(title="&VLC"                 image=[E0E8,SOFT] image-sel=[E0E8,HOVER] tip=path.full('@dir_vlc')            commands{cmd=if(keys.rbutton(), clipboard.set(path.full('@dir_vlc'))),            cmd=('"@dir_vlc"')})
    // item(title="&VSCodium"            image=[E0E8,SOFT] image-sel=[E0E8,HOVER] tip=path.full('@dir_vscodium')       commands{cmd=if(keys.rbutton(), clipboard.set(path.full('@dir_vscodium'))),       cmd=('"@dir_vscodium"')})
    // item(title="&WinaeroTweaker"      image=[E0E8,SOFT] image-sel=[E0E8,HOVER] tip=path.full('@dir_winaerotweaker') commands{cmd=if(keys.rbutton(), clipboard.set(path.full('@dir_winaerotweaker'))), cmd=('"@dir_winaerotweaker"')})
    // item(title="&WinMerge"            image=[E0E8,SOFT] image-sel=[E0E8,HOVER] tip=path.full('@dir_winmerge')       commands{cmd=if(keys.rbutton(), clipboard.set(path.full('@dir_winmerge'))),       cmd=('"@dir_winmerge"')})
}
