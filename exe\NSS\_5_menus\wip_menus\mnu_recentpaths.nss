
menu(title='Recent@"\t"Address Bar'  where=reg.exists('HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\TypedPaths', 'url1') image=\uE14A) {
    $path01=reg.get('HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\TypedPaths', 'url1')
    item(title=if(len(path.location.name(path01))==1, path01, `...\` + path.location.name(path01) + `\` + path.title(path01)) where=path.exists(path01) image=\uE1F4 tip=path01
        cmd=if(window.name=='CabinetWClass' and !keys.shift(), command.navigate(path01), path01))
    $path02=reg.get('HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\TypedPaths', 'url2')
    item(title=if(len(path.location.name(path02))==1, path02, `...\` + path.location.name(path02) + `\` + path.title(path02)) where=path.exists(path02) image=\uE1F4 tip=path02
        cmd=if(window.name=='CabinetWClass' and !keys.shift(), command.navigate(path02), path02))
    $path03=reg.get('HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\TypedPaths', 'url3')
    item(title=if(len(path.location.name(path03))==1, path03, `...\` + path.location.name(path03) + `\` + path.title(path03)) where=path.exists(path03) image=\uE1F4 tip=path03
        cmd=if(window.name=='CabinetWClass' and !keys.shift(), command.navigate(path03), path03))
    $path04=reg.get('HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\TypedPaths', 'url4')
    item(title=if(len(path.location.name(path04))==1, path04, `...\` + path.location.name(path04) + `\` + path.title(path04)) where=path.exists(path04) image=\uE1F4 tip=path04
        cmd=if(window.name=='CabinetWClass' and !keys.shift(), command.navigate(path04), path04))
    $path05=reg.get('HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\TypedPaths', 'url5')
    item(title=if(len(path.location.name(path05))==1, path05, `...\` + path.location.name(path05) + `\` + path.title(path05)) where=path.exists(path05) image=\uE1F4 tip=path05
        cmd=if(window.name=='CabinetWClass' and !keys.shift(), command.navigate(path05), path05))
    $path06=reg.get('HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\TypedPaths', 'url6')
    item(title=if(len(path.location.name(path06))==1, path06, `...\` + path.location.name(path06) + `\` + path.title(path06)) where=path.exists(path06) image=\uE1F4 tip=path06
        cmd=if(window.name=='CabinetWClass' and !keys.shift(), command.navigate(path06), path06))
    $path07=reg.get('HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\TypedPaths', 'url7')
    item(title=if(len(path.location.name(path07))==1, path07, `...\` + path.location.name(path07) + `\` + path.title(path07)) where=path.exists(path07) image=\uE1F4 tip=path07
        cmd=if(window.name=='CabinetWClass' and !keys.shift(), command.navigate(path07), path07))
    $path08=reg.get('HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\TypedPaths', 'url8')
    item(title=if(len(path.location.name(path08))==1, path08, `...\` + path.location.name(path08) + `\` + path.title(path08)) where=path.exists(path08) image=\uE1F4 tip=path08
        cmd=if(window.name=='CabinetWClass' and !keys.shift(), command.navigate(path08), path08))
    $path09=reg.get('HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\TypedPaths', 'url9')
    item(title=if(len(path.location.name(path09))==1, path09, `...\` + path.location.name(path09) + `\` + path.title(path09)) where=path.exists(path09) image=\uE1F4 tip=path09
        cmd=if(window.name=='CabinetWClass' and !keys.shift(), command.navigate(path09), path09))
    $path10=reg.get('HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\TypedPaths', 'url10')
    item(title=if(len(path.location.name(path10))==1, path10, `...\` + path.location.name(path10) + `\` + path.title(path10)) where=path.exists(path10) image=\uE1F4 tip=path10
        cmd=if(window.name=='CabinetWClass' and !keys.shift(), command.navigate(path10), path10))
    $path11=reg.get('HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\TypedPaths', 'url11')
    item(title=if(len(path.location.name(path11))==1, path11, `...\` + path.location.name(path11) + `\` + path.title(path11)) where=path.exists(path11) image=\uE1F4 tip=path11
        cmd=if(window.name=='CabinetWClass' and !keys.shift(), command.navigate(path11), path11))
    $path12=reg.get('HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\TypedPaths', 'url12')
    item(title=if(len(path.location.name(path12))==1, path12, `...\` + path.location.name(path12) + `\` + path.title(path12)) where=path.exists(path12) image=\uE1F4 tip=path12
        cmd=if(window.name=='CabinetWClass' and !keys.shift(), command.navigate(path12), path12))
    $path13=reg.get('HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\TypedPaths', 'url13')
    item(title=if(len(path.location.name(path13))==1, path13, `...\` + path.location.name(path13) + `\` + path.title(path13)) where=path.exists(path13) image=\uE1F4 tip=path13
        cmd=if(window.name=='CabinetWClass' and !keys.shift(), command.navigate(path13), path13))
    $path14=reg.get('HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\TypedPaths', 'url14')
    item(title=if(len(path.location.name(path14))==1, path14, `...\` + path.location.name(path14) + `\` + path.title(path14)) where=path.exists(path14) image=\uE1F4 tip=path14
        cmd=if(window.name=='CabinetWClass' and !keys.shift(), command.navigate(path14), path14))
    $path15=reg.get('HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\TypedPaths', 'url15')
    item(title=if(len(path.location.name(path15))==1, path15, `...\` + path.location.name(path15) + `\` + path.title(path15)) where=path.exists(path15) image=\uE1F4 tip=path15
        cmd=if(window.name=='CabinetWClass' and !keys.shift(), command.navigate(path15), path15))
    $path16=reg.get('HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\TypedPaths', 'url16')
    item(title=if(len(path.location.name(path16))==1, path16, `...\` + path.location.name(path16) + `\` + path.title(path16)) where=path.exists(path16) image=\uE1F4 tip=path16
        cmd=if(window.name=='CabinetWClass' and !keys.shift(), command.navigate(path16), path16))
    $path17=reg.get('HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\TypedPaths', 'url17')
    item(title=if(len(path.location.name(path17))==1, path17, `...\` + path.location.name(path17) + `\` + path.title(path17)) where=path.exists(path17) image=\uE1F4 tip=path17
        cmd=if(window.name=='CabinetWClass' and !keys.shift(), command.navigate(path17), path17))
    $path18=reg.get('HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\TypedPaths', 'url18')
    item(title=if(len(path.location.name(path18))==1, path18, `...\` + path.location.name(path18) + `\` + path.title(path18)) where=path.exists(path18) image=\uE1F4 tip=path18
        cmd=if(window.name=='CabinetWClass' and !keys.shift(), command.navigate(path18), path18))
    $path19=reg.get('HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\TypedPaths', 'url19')
    item(title=if(len(path.location.name(path19))==1, path19, `...\` + path.location.name(path19) + `\` + path.title(path19)) where=path.exists(path19) image=\uE1F4 tip=path19
        cmd=if(window.name=='CabinetWClass' and !keys.shift(), command.navigate(path19), path19))
    $path20=reg.get('HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\TypedPaths', 'url20')
    item(title=if(len(path.location.name(path20))==1, path20, `...\` + path.location.name(path20) + `\` + path.title(path20)) where=path.exists(path20) image=\uE1F4 tip=path20
        cmd=if(window.name=='CabinetWClass' and !keys.shift(), command.navigate(path20), path20))
    $path21=reg.get('HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\TypedPaths', 'url21')
    item(title=if(len(path.location.name(path21))==1, path21, `...\` + path.location.name(path21) + `\` + path.title(path21)) where=path.exists(path21) image=\uE1F4 tip=path21
        cmd=if(window.name=='CabinetWClass' and !keys.shift(), command.navigate(path21), path21))
    $path22=reg.get('HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\TypedPaths', 'url22')
    item(title=if(len(path.location.name(path22))==1, path22, `...\` + path.location.name(path22) + `\` + path.title(path22)) where=path.exists(path22) image=\uE1F4 tip=path22
        cmd=if(window.name=='CabinetWClass' and !keys.shift(), command.navigate(path22), path22))
    $path23=reg.get('HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\TypedPaths', 'url23')
    item(title=if(len(path.location.name(path23))==1, path23, `...\` + path.location.name(path23) + `\` + path.title(path23)) where=path.exists(path23) image=\uE1F4 tip=path23
        cmd=if(window.name=='CabinetWClass' and !keys.shift(), command.navigate(path23), path23))
    $path24=reg.get('HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\TypedPaths', 'url24')
    item(title=if(len(path.location.name(path24))==1, path24, `...\` + path.location.name(path24) + `\` + path.title(path24)) where=path.exists(path24) image=\uE1F4 tip=path24
        cmd=if(window.name=='CabinetWClass' and !keys.shift(), command.navigate(path24), path24))
    $path25=reg.get('HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\TypedPaths', 'url25')
    item(title=if(len(path.location.name(path25))==1, path25, `...\` + path.location.name(path25) + `\` + path.title(path25)) where=path.exists(path25) image=\uE1F4 tip=path25
        cmd=if(window.name=='CabinetWClass' and !keys.shift(), command.navigate(path25), path25))
    $path26=reg.get('HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\TypedPaths', 'url26')
    item(title=if(len(path.location.name(path26))==1, path26, `...\` + path.location.name(path26) + `\` + path.title(path26)) where=path.exists(path26) image=\uE1F4 tip=path26
        cmd=if(window.name=='CabinetWClass' and !keys.shift(), command.navigate(path26), path26)) }
