
//
$APP_USER_MPVPLAYER_DIR = '@user.desktop\my\flow\home\__GOTO__\Apps\app_mpvplayer\exe'
$APP_USER_MPVPLAYER_EXE = '@APP_USER_MPVPLAYER_DIR\mpv.exe'
$APP_USER_MPVPLAYER_TIP = "..."+str.trimstart('@APP_USER_MPVPLAYER_EXE','@app.dir')

// context: file
item(
    title  = ":  &MPV Player"
    keys   = "exe"
    type   = 'File'
    args   = '"@sel.file"'
    where  = str.equals(sel.file.ext,[".aac",".flac",".m4a",".mp3",".ogg",".wav",".wma",".mov",".mkv",".mp4"])
    //
    image  = APP_USER_MPVPLAYER_EXE
    tip    = [APP_USER_MPVPLAYER_TIP,TIP3,0.8]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_USER_MPVPLAYER_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_MPVPLAYER_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_MPVPLAYER_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_MPVPLAYER_DIR')),
    }
)
// Context: Taskbar
item(
    title  = ":  &MPV Player"
    keys   = "exe"
    type   = 'Taskbar'
    args   = ''
    //
    image  = APP_USER_MPVPLAYER_EXE
    tip    = [APP_USER_MPVPLAYER_TIP,TIP3,0.8]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_USER_MPVPLAYER_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_MPVPLAYER_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_MPVPLAYER_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_MPVPLAYER_DIR')),
    }
)
