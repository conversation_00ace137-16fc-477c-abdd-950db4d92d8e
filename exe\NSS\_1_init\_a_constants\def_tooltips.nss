
// docs: `https://nilesoft.org/docs/configuration/properties#tip`


/*
    :: tooltip colors
*/
$TIP1_COLOR = [#1B3A40, #fff] // tip.primary
$TIP2_COLOR = [#1A377D, #fff] // tip.info
$TIP3_COLOR = [#37275C, #fff] // tip.success
$TIP4_COLOR = [#434328, #fff] // tip.warning
$TIP5_COLOR = [#42140B, #fff] // tip.danger

/*
    :: agnostic tooltip style aliases

    :: usage: `tip=[app.dir,TIP1,0.1]`
*/
$TIP0 = ""
$TIP1 = tip.primary
$TIP2 = tip.info
$TIP3 = tip.success
$TIP4 = tip.warning
$TIP5 = tip.danger


/*
    :: predefined tooltip messages

    :: usage: `tip=TIP_ADMIN`
*/
$TIP_ADMIN = ["Right-click to run as &Admin",TIP5,0.0]
