
//
$APP_USER_WINMERGE_DIR = '@user.desktop\my\flow\home\__GOTO__\Apps\app_winmerge\exe'
$APP_USER_WINMERGE_EXE = '@APP_USER_WINMERGE_DIR\WinMergeU.exe'
$APP_USER_WINMERGE_TIP = "..."+str.trimstart('@APP_USER_WINMERGE_EXE','@app.dir')

// Context: Taskbar
item(
    title  = ":  &Winmerge"
    keys   = "exe"
    type   = 'Taskbar'
    args   = '@user.desktop'
    //
    image  = APP_USER_WINMERGE_EXE
    tip    = [APP_USER_WINMERGE_TIP,TIP3,0.8]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_USER_WINMERGE_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_WINMERGE_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_WINMERGE_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_WINMERGE_DIR')),
    }
)
