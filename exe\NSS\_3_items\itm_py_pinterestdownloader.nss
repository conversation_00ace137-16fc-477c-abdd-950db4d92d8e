
//
$PY_PINTERESTDOWNLOADER_DIR = '@user.desktop\my\flow\home\__GOTO__\Scripts\Python\Py_Cli_Utils\py__PinterestDownloader'
$PY_PINTERESTDOWNLOADER_EXE = '@PY_PINTERESTDOWNLOADER_DIR\venv\Scripts\python.exe'
$PY_PINTERESTDOWNLOADER_APP = '@PY_PINTERESTDOWNLOADER_DIR\src\main.py'
//

// Context: Explorer
$PY_PINTERESTDOWNLOADER_EXPLORER = '-op "@sel.dir" --prompt'
item(
    title="&PinterestDownloader"
    keys="py"
    type='File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    //
    image=[E17C,GREY] image-sel=[E17C,PURPLE]
    tip=['"@PY_PINTERESTDOWNLOADER_APP" @PY_PINTERESTDOWNLOADER_EXPLORER',TIP3,0.75]
    //
    admin=keys.rbutton()
    cmd=if(KEYS_EXE_OPEN_EXE,('"@PY_PINTERESTDOWNLOADER_EXE"'))
    args='"@PY_PINTERESTDOWNLOADER_APP" @PY_PINTERESTDOWNLOADER_EXPLORER'
    //
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@PY_PINTERESTDOWNLOADER_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@PY_PINTERESTDOWNLOADER_APP')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@PY_PINTERESTDOWNLOADER_DIR')),
    }
)
// Context: Taskbar
$PY_PINTERESTDOWNLOADER_TASKBAR = '-op "@user.desktop" --prompt'
item(
    title="&PinterestDownloader"
    keys="py"
    type='Taskbar'
    //
    image=[E17C,GREY]
    image-sel=[E17C,PURPLE]
    //
    admin=keys.rbutton()
    cmd=if(KEYS_EXE_OPEN_EXE,('"@PY_PINTERESTDOWNLOADER_EXE"'))
    args='"@PY_PINTERESTDOWNLOADER_APP" @PY_PINTERESTDOWNLOADER_TASKBAR'
    tip=['"@PY_PINTERESTDOWNLOADER_APP" @PY_PINTERESTDOWNLOADER_TASKBAR',TIP3,0.75]
    //
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@PY_PINTERESTDOWNLOADER_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@PY_PINTERESTDOWNLOADER_APP')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@PY_PINTERESTDOWNLOADER_DIR')),
    }
)

