
//
$APP_BLENDER_NSS = '@app.dir\NSS\_3_items\user_apps'
$APP_BLENDER_SRC = '@user.desktop\my\flow\home\__GOTO__\Apps\app_blender'
//
$APP_USER_BLENDER_DIR = '@user.desktop\my\flow\home\__GOTO__\Apps\app_blender\exe\blender-4.1.1-windows-x64'
$APP_USER_BLENDER_EXE = '@APP_USER_BLENDER_DIR\blender.exe'
$APP_USER_BLENDER_TIP = "..."+str.trimstart('@APP_USER_BLENDER_EXE','@app.dir')


// Context: File
item(
    title  = ":  &Blender"
    keys   = "exe"
    type   = 'File'
    args   = '"@sel.file"'
    where  = path.exists(APP_USER_BLENDER_EXE) && str.equals(sel.file.ext,[".blend", ".blend1"])
    //
    image  = APP_USER_BLENDER_EXE
    tip    = [APP_USER_BLENDER_TIP,TIP3,1.0]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_USER_BLENDER_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_BLENDER_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_BLENDER_EXE')),
        cmd=if(KEYS_EXE_GOTO_NSS,('@APP_BLENDER_NSS')),
        cmd=if(KEYS_EXE_GOTO_SRC,('@APP_BLENDER_SRC')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_BLENDER_DIR')),
    }
)
// Context: Directory
item(
    title  = ":  &Blender"
    keys   = "exe"
    type   = 'Desktop|Dir|Drive|Back.Dir|Back.Drive'
    args   = '--local="@sel.dir"'
    where  = path.exists(APP_USER_BLENDER_EXE)
    //
    image  = APP_USER_BLENDER_EXE
    tip    = [APP_USER_BLENDER_TIP,TIP3,1.0]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_USER_BLENDER_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_BLENDER_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_BLENDER_EXE')),
        cmd=if(KEYS_EXE_GOTO_NSS,('@APP_BLENDER_NSS')),
        cmd=if(KEYS_EXE_GOTO_SRC,('@APP_BLENDER_SRC')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_BLENDER_DIR')),
    }
)
// Context: Taskbar
item(
    title  = ":  &Blender"
    keys   = "exe"
    type   = 'Taskbar'
    args   = ''
    where  = path.exists(APP_USER_BLENDER_EXE)
    //
    image  = APP_USER_BLENDER_EXE
    tip    = [APP_USER_BLENDER_TIP,TIP3,1.0]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_USER_BLENDER_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_BLENDER_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_BLENDER_EXE')),
        cmd=if(KEYS_EXE_GOTO_NSS,('@APP_BLENDER_NSS')),
        cmd=if(KEYS_EXE_GOTO_SRC,('@APP_BLENDER_SRC')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_BLENDER_DIR')),
    }
)

