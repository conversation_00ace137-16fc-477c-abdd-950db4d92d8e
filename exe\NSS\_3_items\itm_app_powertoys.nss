
//
$APP_POWERTOYS_DIR = '@user.localappdata\PowerToys'
$APP_POWERTOYS_EXE = '@APP_POWERTOYS_DIR\PowerToys.exe'
$APP_POWERTOYS_TIP = "..."+str.trimstart('@APP_POWERTOYS_EXE','@app.dir')
//
$APP_POWERTOYS_DIR_CFG = '@user.localappdata\Microsoft\PowerToys'
$APP_POWERTOYS_DIR_NSS = '@app.dir\NSS\_3_items\user_apps'
$APP_POWERTOYS_DIR_SRC = '@user.desktop\my\flow\home\__GOTO__\Apps\app_powertoys'


// Context: Taskbar
item(
    title  = ":  &PowerToys"
    keys   = "exe"
    type   = 'Taskbar'
    args   = ''
    //
    image  = APP_POWERTOYS_EXE
    tip    = [APP_POWERTOYS_TIP,TIP3,1.0]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_POWERTOYS_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_POWERTOYS_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_POWERTOYS_EXE')),
        cmd=if(KEYS_EXE_GOTO_CFG,('@APP_POWERTOYS_DIR_CFG')),
        cmd=if(KEYS_EXE_GOTO_NSS,('@APP_POWERTOYS_DIR_NSS')),
        cmd=if(KEYS_EXE_GOTO_SRC,('@APP_POWERTOYS_DIR_SRC')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_POWERTOYS_DIR')),
    }
)
