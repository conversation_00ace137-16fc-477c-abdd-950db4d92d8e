
// docs: `...`


/*
    :: predefined icons

    :: usage: `image=[E23B,#E0D9EA]`
*/
$E153 = "\uE153" // streaming

$E120 = "\uE120" // network/nas
$E121 = "\uE121" // network/nas
$E11F = "\uE11F" // network
$E265 = "\uE265" // device
$E26F = "\uE26F" // commandbrackets
$E0CE = "\uE0CE" // cleanup
$E26D = "\uE26D" // system script
$E276 = "\uE276" // restart shell
$E0FB = "\uE0FB" // home outline
$E0FC = "\uE0FC" // home filled
$E09D = "\uE09D" // cloud userfolders
$E282 = "\uE282" // squared minus
$E0EA = "\uE0EA" // system folder
$E0E3 = "\uE0E3" // user directory
$E286 = "\uE286" // system / nodegroups
$E0FE = "\uE0FE" // admin / user
$E228 = "\uE228" // arrow down / v-shape
$E00A = "\uE00A" // arrow down
$E1A0 = "\uE1A0" // bubble
$E1E7 = "\uE1E7" // calculator outline
$E1D1 = "\uE1D1" // close/cancel
$E23C = "\uE23C" // circle filled
$E12A = "\uE12A" // clock recent
$E09C = "\uE09C" // cloud outline
$E0D1 = "\uE0D1" // computer large
$E1D9 = "\uE1D9" // computer small
$E114 = "\uE114" // copy full path
$E0B2 = "\uE0B2" // copy
$E0A0 = "\uE0A0" // customize folder
$E0B8 = "\uE0B8" // cut
$E0E0 = "\uE0E0" // dir arrow
$E0E2 = "\uE0E2" // dir checkbox
$E0FA = "\uE0FA" // dir gear
$E154 = "\uE154" // dir media
$E0DE = "\uE0DE" // dir music
$E203 = "\uE203" // dir plus
$E09E = "\uE09E" // dir shield
$E0E4 = "\uE0E4" // dir sync
$E0A7 = "\uE0A7" // dir user libraries 1
$E0E1 = "\uE0E1" // dir user libraries 2
$E0FF = "\uE0FF" // dir user libraries 3
$E09F = "\uE09F" // dir user
$E0E8 = "\uE0E8" // directory
$E0BD = "\uE0BD" // downloads
$E17A = "\uE17A" // edit text
$E14D = "\uE14D" // executables
$E12E = "\uE12E" // eye look
$E1A3 = "\uE1A3" // file children
$E109 = "\uE109" // file icon filled
$E108 = "\uE108" // file icon outline
$E275 = "\uE275" // file outline
$E0E6 = "\uE0E6" // folder icon filled
$E0E7 = "\uE0E7" // folder icon outline
$E184 = "\uE184" // generate dirtree
$E22C = "\uE22C" // git logo
$E11E = "\uE11E" // globe
$E123 = "\uE123" // goto filled
$E122 = "\uE122" // goto outline
$E14A = "\uE14A" // goto target active
$E159 = "\uE159" // graph monitor
$E09A = "\uE09A" // group by
$E25E = "\uE25E" // hash
$E1A8 = "\uE1A8" // heart filled
$E15E = "\uE15E" // heart outline
$E1A7 = "\uE1A7" // heart outline
$E18C = "\uE18C" // keyboard
$E158 = "\uE158" // lightbulb filled
$E157 = "\uE157" // lightbulb outline
$E187 = "\uE187" // magnifying glass outline / search
$E1B1 = "\uE1B1" // maximize
$E1D3 = "\uE1D3" // minimize
$E0DC = "\uE0DC" // monitor with crescent
$E0BE = "\uE0BE" // monitor with gear
$E087 = "\uE087" // move
$E0CA = "\uE0CA" // move
$E107 = "\uE107" // multiple users
$E16A = "\uE16A" // open dir
$E1F6 = "\uE1F6" // open in new window
$E160 = "\uE160" // open in new tab
$E0A6 = "\uE0A6" // open with 2
$E089 = "\uE089" // open with
$E201 = "\uE201" // open with
$E173 = "\uE173" // open
$E116 = "\uE116" // paint
$E0AF = "\uE0AF" // paste 1
$E16D = "\uE16D" // paste 2
$E0B1 = "\uE0B1" // paste 3
$E0A1 = "\uE0A1" // pencil edit
$E0C9 = "\uE0C9" // pin icon 1
$E03F = "\uE03F" // pin icon 2
$E1D2 = "\uE1D2" // plus
$E15D = "\uE15D" // properties
$E230 = "\uE230" // python
$E132 = "\uE132" // questionmark outline
$E0B4 = "\uE0B4" // recycle bin
$E1AA = "\uE1AA" // recycle
$E094 = "\uE094" // refresh
$E0B5 = "\uE0B5" // rename / copy path
$E0B7 = "\uE0B7" // rename / copy path
$E12B = "\uE12B" // restart process
$E1AB = "\uE1AB" // restart process
$E172 = "\uE172" // round plus
$E099 = "\uE099" // share / send to
$E17C = "\uE17C" // script
$E0F4 = "\uE0F4" // settings gear filled
$E0F3 = "\uE0F3" // settings gear outline
$E0ED = "\uE0ED" // settings slider horizontal
$E0EE = "\uE0EE" // settings slider vertical
$E10A = "\uE10A" // shell config
$E1C4 = "\uE1C4" // shell docs
$E249 = "\uE249" // shell
$E1B5 = "\uE1B5" // shield filled
$E194 = "\uE194" // shield outline
$E1A6 = "\uE0B3" // shortcut add
$E12F = "\uE12F" // shotdown / close
$E28A = "\uE28A" // signal
$E1B0 = "\uE1B0" // size
$E0A2 = "\uE0A2" // sort 1
$E0D7 = "\uE0D7" // sort 2
$E23B = "\uE23B" // square filled / work
$E11B = "\uE11B" // star filled
$E124 = "\uE124" // star offset
$E0C8 = "\uE0C8" // star outline
$E092 = "\uE092" // startup
$E046 = "\uE046" // target
$E0A4 = "\uE0A4" // task manager
$E0C5 = "\uE0C5" // tasks
$E0D6 = "\uE0D6" // terminal filled ps1
$E0AC = "\uE0AC" // terminal outline cmd
$E26E = "\uE26E" // terminal outline general
$E0AB = "\uE0AB" // terminal outline ps1
$E271 = "\uE271" // terminal
$E061 = "\uE061" // text content
$E19D = "\uE19D" // text lines / list content
$E10E = "\uE10E" // textfile outline
$E163 = "\uE163" // thick minus shape / menu-sel
$E167 = "\uE167" // three horizontal dots
$E1DE = "\uE1DE" // trashbin
$E03A = "\uE03A" // undo 1
$E076 = "\uE076" // undo 2
$E04E = "\uE04E" // undo 3
$E093 = "\uE093" // undo 4
$E09B = "\uE09B" // url
$E1BC = "\uE1BC" // user
$E1D7 = "\uE1D7" // utils
$E253 = "\uE253" // view 1
$E150 = "\uE150" // view 2
$E1B8 = "\uE1B8" // windows logo large
$E1B6 = "\uE1B6" // windows logo normal
$E254 = "\uE254" // windows logo offset
$E142 = "\uE142" // windows logo outline
$E1FB = "\uE1FB" // windows
$E0F8 = "\uE0F8" // wrench outline
$E0AA = "\uE0AA" // zip / archived folder
$E1A4 = "\uE1A4" // zip / archived folder
