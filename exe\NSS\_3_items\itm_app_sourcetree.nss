
//
$APP_USER_SOURCETREE_DIR = '@user.localappdata\SourceTree'
$APP_USER_SOURCETREE_EXE = '@APP_USER_SOURCETREE_DIR\SourceTree.exe'
$APP_USER_SOURCETREE_TIP = "..."+str.trimstart('@APP_USER_SOURCETREE_EXE','@app.dir')

// Context: Explorer
item(
    title  = ":  &SourceTree"
    keys   = "exe"
    type   = 'File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    args   = '"@sel.dir"'
    //
    image  = APP_USER_SOURCETREE_EXE
    tip    = [APP_USER_SOURCETREE_TIP,TIP3,0.8]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_USER_SOURCETREE_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_SOURCETREE_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_SOURCETREE_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_SOURCETREE_DIR')),
    }
)
// Context: Taskbar
item(
    title  = ":  &SourceTree"
    keys   = "exe"
    type   = 'Taskbar'
    args   = ''
    //
    image  = APP_USER_SOURCETREE_EXE
    tip    = [APP_USER_SOURCETREE_TIP,TIP3,0.8]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_USER_SOURCETREE_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_SOURCETREE_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_SOURCETREE_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_SOURCETREE_DIR')),
    }
)
