
//
$APP_USER_REGFROMAPP_USER_DIR = '@user.desktop\my\flow\home\__GOTO__\Apps\grp_nirsoft\app_regfromapp\exe'
$APP_USER_REGFROMAPP_USER_EXE = '@APP_USER_REGFROMAPP_USER_DIR\RegFromApp.exe'
$APP_USER_REGFROMAPP_USER_TIP = "..."+str.trimstart('@APP_USER_REGFROMAPP_USER_EXE','@app.dir')

//
item(title="&RegFromApp"
    keys="exe"
    type='Taskbar|File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    //
    image=APP_USER_REGFROMAPP_USER_EXE
    tip=[APP_USER_REGFROMAPP_USER_TIP,TIP3,0.5]
    //
    admin=keys.rbutton()
    cmd=if(KEYS_EXE_OPEN_EXE,(APP_USER_REGFROMAPP_USER_EXE))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set(APP_USER_REGFROMAPP_USER_DIR)),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set(APP_USER_REGFROMAPP_USER_EXE)),
        cmd=if(KEYS_EXE_OPEN_DIR,(APP_USER_REGFROMAPP_USER_DIR)),
    }
)
