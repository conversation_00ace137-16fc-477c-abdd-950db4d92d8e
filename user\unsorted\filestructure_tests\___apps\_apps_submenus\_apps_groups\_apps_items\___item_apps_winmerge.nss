
/* item: singles/apps/winmerge */

//
$APP_WINMERGE_DIR = '@app.dir\PORTAL\APPS\app_winmerge\exe'
$APP_WINMERGE_EXE = '@APP_WINMERGE_DIR\WinMergeU.exe'
$APP_WINMERGE_TIP = "..."+str.trimstart('@APP_WINMERGE_EXE','@app.dir')

// Context: Taskbar
item(
    title  = ":  &Winmerge"
    keys   = "exe"
    type   = 'Taskbar'
    args   = '@user.desktop'
    //
    image  = APP_WINMERGE_EXE
    tip    = [APP_WINMERGE_TIP,TIP3,1.0]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_WINMERGE_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_WINMERGE_DIR')),
        cmd=if(KEY<PERSON>_EXE_COPY_EXE,clipboard.set('@APP_WINMERGE_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_WINMERGE_DIR')),
    }
)
