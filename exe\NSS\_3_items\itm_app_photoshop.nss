

//
$APP_PHOTOSHOP_DIR = '@sys.prog\Adobe\Adobe Photoshop 2024'
$APP_PHOTOSHOP_EXE = '@APP_PHOTOSHOP_DIR\Photoshop.exe'
$APP_PHOTOSHOP_TIP = "..."+str.trimstart('@APP_PHOTOSHOP_EXE','@app.dir')

// -> context: file
item(
    title  = "&Photoshop"
    keys   = "exe"
    type   = 'File'
    args   = '"@sel.file"'
    where  = str.equals(sel.file.ext,[".ai", ".apng", ".avif", ".bmp", ".dwf", ".dwg", ".dxf", ".eps", ".exr", ".gif", ".ico", ".iges", ".igs", ".jfi", ".jfif", ".jif", ".jp2", ".jpe", ".jpeg", ".jpf", ".jpg", ".jpm", ".jpx", ".jxr", ".k25", ".mj2", ".nrw", ".pct", ".pdf", ".pic", ".pict", ".png", ".psd", ".raw", ".svg", ".tga", ".tif", ".tiff", ".wdp", ".webp", ".wrl", ".xcf"])
    //
    image  = APP_PHOTOSHOP_EXE
    tip    = [APP_PHOTOSHOP_TIP,TIP3,1.0]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_PHOTOSHOP_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_PHOTOSHOP_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_PHOTOSHOP_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_PHOTOSHOP_DIR')),
    }
)

// context: directory
item(
    title  = ":  &Photoshop"
    keys   = "exe"
    type   = 'Desktop|Dir|Drive|Back.Dir|Back.Drive'
    args   = '--local="@sel.dir"'
    //
    image  = APP_PHOTOSHOP_EXE
    tip    = [APP_PHOTOSHOP_TIP,TIP3,1.0]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_PHOTOSHOP_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_PHOTOSHOP_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_PHOTOSHOP_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_PHOTOSHOP_DIR')),
    }
)
// context: taskbar
item(
    title  = ":  &Photoshop"
    keys   = "exe"
    type   = 'Taskbar'
    args   = ''
    //
    image  = APP_PHOTOSHOP_EXE
    tip    = [APP_PHOTOSHOP_TIP,TIP3,1.0]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_PHOTOSHOP_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_PHOTOSHOP_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_PHOTOSHOP_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_PHOTOSHOP_DIR')),
    }
)




