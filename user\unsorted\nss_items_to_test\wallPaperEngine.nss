$wallPaperEnginePath = eval(reg.get('HKCU\Software\WallpaperEngine','installPath'))


menu(type='desktop' image="\uE0A0" title='Wallpaper Engine'){
item(title="Change Wallpaper" image=\uE150 cmd=wallPaperEnginePath)
item(title="Pause" image=\uE294 cmd=wallPaperEnginePath args='-control pause')
item(title="Play" image="\uE148" cmd=wallPaperEnginePath args='-control play')
item(title="Mute" image=[\uE74F, "Segoe MDL2 Assets"] cmd=wallPaperEnginePath args='-control mute')
item(title="Unmute" image=[\uE995, "Segoe MDL2 Assets"] cmd=wallPaperEnginePath args='-control unmute')
}

menu(type="taskbar" image="\uE0A0" title='Wallpaper Engine'){
item(title="Change Wallpaper" image=\uE150 cmd=wallPaperEnginePath)
item(title="Pause" image=\uE294 cmd=wallPaperEnginePath args='-control pause')
item(title="Play" image=\uE148 cmd=wallPaperEnginePath args='-control play')
item(title="Mute" image=[\uE74F, "Segoe MDL2 Assets"] cmd=wallPaperEnginePath args='-control mute')
item(title="Unmute" image=[\uE995, "Segoe MDL2 Assets"] cmd=wallPaperEnginePath args='-control unmute')
}


// var {
// 	wallPaperEnginePath='D:\Program Files (x86)\Steam\steamapps\common\wallpaper_engine\wallpaper64.exe'
// }
// menu(type='desktop' image="\uE0A0" title='Wallpaper Engine') {
// 	item(title='Change Wallpaper' image="\uE150" cmd=wallPaperEnginePath)
// 	item(title='Pause' image="\uE294" cmd=wallPaperEnginePath args='-control pause')
// 	item(title='Play' image="\uE148" cmd=wallPaperEnginePath args='-control play')
// }