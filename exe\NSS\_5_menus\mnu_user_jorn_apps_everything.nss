
//
$APP_USER_EVERYTHING_DIR = '@user.desktop\my\flow\home\__GOTO__\Apps\app_everything\exe'
$APP_USER_EVERYTHING_EXE = '@APP_USER_EVERYTHING_DIR\Everything64.exe'
$APP_USER_EVERYTHING_TIP = "..."+str.trimstart('@APP_USER_EVERYTHING_EXE','@app.dir')

/* menu */

// menu(type='~Taskbar|~Desktop|~Titlebar' mode='multiple' expanded=true) {
//     // item(title="&Explore in Everssything" keys="Dir" image='@APP_USER_EVERYTHING_EXE' cmd='"@APP_USER_EVERYTHING_EXE"' args='-search "EE: EX1: AND EX2: AND EX3: AND EX4:" -filter " ├─ Sorted: Modified" -explore "@sel.dir"'  sep='None')
//     import '@app.dir/NSS/_3_items/itm_app_everything.nss'
// }

// item(title="&Explore in Everything" keys="Dir" image=image.svg('@app.dir/custom/icons/sublime2.svg') tip='@app.dir/custom/icons' image-sel=[E158,GREEN] cmd='"@APP_USER_EVERYTHING_EXE"' args='-search "EE: EX1: AND EX2: AND EX3: AND EX4:" -filter " ├─ Sorted: Modified" -explore "@sel.dir"'  sep='Both')

// item(title="&Explore in Everything" keys="Dir" image='@APP_USER_EVERYTHING_EXE' cmd='"@APP_USER_EVERYTHING_EXE"' args='-search "EE: EX1: AND EX2: AND EX3: AND EX4:" -filter " ├─ Sorted: Modified" -explore "@sel.dir"'  sep='None')

// menu(title="&Everything" type='Desktop|Dir|Drive|Back.Dir|Back.Drive|File' image='@APP_USER_EVERYTHING_EXE' sep='None')

menu(title="App :  &Everything" type='Desktop|Dir|Drive|Back.Dir|Back.Drive|File' image=[E187,WHITE1]  image-sel=[E187,HOVER] sep='None')
{
    item(title="dir: size:>1mb sfda: !path:datafiles|bin" tip="blender_addons" keys="*" image=[E157,RED_SOFT] image-sel=[E158,HOVER] cmd='"@APP_USER_EVERYTHING_EXE"' args='-search "dm:last52weeks dir: size:>1mb sfda: !path:datafiles|bin !path:/.git/objects" -filter " ┌─ (Mix) Sorted: Modified" -explore "@sel.dir"')
    separator()

    item(title="&Modified: 24 hours" keys="*" image=[E157,BLUE] image-sel=[E158,HOVER] cmd='"@APP_USER_EVERYTHING_EXE"' args='-search "dm:last24hours" -filter " ┌─ (Mix) Sorted: Modified" -explore "@sel.dir"')
    item(title="&Modified: 30 days"  keys="*" image=[E157,BLUE] image-sel=[E158,HOVER] cmd='"@APP_USER_EVERYTHING_EXE"' args='-search "dm:last30days" -filter " ┌─ (Mix) Sorted: Modified" -explore "@sel.dir"')
    item(title="&Modified: 52 weeks" keys="*" image=[E157,BLUE] image-sel=[E158,HOVER] cmd='"@APP_USER_EVERYTHING_EXE"' args='-search "dm:last52weeks" -filter " ┌─ (Mix) Sorted: Modified" -explore "@sel.dir"')
    separator()


    item(title="&(Mix) Sorted: Modified" keys="0" image=[E157,BLUE] image-sel=[E158,HOVER] cmd='"@APP_USER_EVERYTHING_EXE"' args='-search "!file:" -filter " ┌─ (Mix) Sorted: Modified" -explore "@sel.dir"')
    item(title="&(Mix) Sorted: Accessed" keys="0" image=[E157,BLUE] image-sel=[E158,HOVER] cmd='"@APP_USER_EVERYTHING_EXE"' args='-search "!file:" -filter " ├─ (Mix) Sorted: Accessed" -explore "@sel.dir"')
    item(title="&(Mix) Sorted: Size"     keys="0" image=[E157,BLUE] image-sel=[E158,HOVER] cmd='"@APP_USER_EVERYTHING_EXE"' args='-search "!file:" -filter " └─ (Mix) Sorted: Size" -explore "@sel.dir"')


    // item(title="&Explore in Everything"       keys="*" image=[E19D,WHITE]  image-sel=[E19D,HOVER] cmd='"@APP_USER_EVERYTHING_EXE"' args='-explore "@sel.dir"')
    // item(title="Everything here"      image=[E19D,WHITE]  image-sel=[E19D,HOVER] cmd='"@APP_USER_EVERYTHING_EXE"' args='-explore "@sel.dir"')
    separator()

    // item(title="*"       keys="Everything: Overview" image=[E19D,WHITE]  image-sel=[E19D,HOVER] cmd='"@APP_USER_EVERYTHING_EXE"' args='-explore "@sel.dir"')
    //
    // item(title="Overview"   keys="*.*" image=[E0E7,GREEN]  image-sel=[E0E7,HOVER] cmd-ps=ps1_get_location  type='File'                where=sel.count==1 window='Hidden' sep='None')
    // item(title="&Overview"       keys="/*" image=[E19D,WHITE]  image-sel=[E19D,HOVER] cmd-line='/K @everything_exe -explore "@sel.dir"')
    // item(title="&Overview"       keys="/*" image=[E19D,WHITE]  image-sel=[E19D,HOVER] cmd='@everything_exe' args='-explore "@sel.dir"')
    // item(title="&Overview"       keys="/*" image=[E19D,WHITE]  image-sel=[E19D,HOVER] command='"@everything_exe"' args='-explore "@sel.dir"')
    // item(title="Directories"   keys="/" image=[E0E7,GREEN]  image-sel=[E0E7,HOVER] cmd-ps=ps1_get_location  type='File'                where=sel.count==1 window='Hidden' sep='None')
    // item(title="Recent"   keys="files" image=[E0E7,GREEN]  image-sel=[E0E7,HOVER] cmd-ps=ps1_get_location  type='File'                where=sel.count==1 window='Hidden' sep='None')
    // item(title="Content"   keys="text" image=[E0E7,GREEN]  image-sel=[E0E7,HOVER] cmd-ps=ps1_get_location  type='File'                where=sel.count==1 window='Hidden' sep='None')
    // item(title="Directories"   keys="/" image=[E0E7,GREEN]  image-sel=[E0E7,HOVER] cmd-ps=ps1_get_location  type='File'                where=sel.count==1 window='Hidden' sep='None')
    // item(title="Directories"   keys="/" image=[E0E7,GREEN]  image-sel=[E0E7,HOVER] cmd-ps=ps1_get_location  type='File'                where=sel.count==1 window='Hidden' sep='None')
    // item(title="Directories"   keys="/" image=[E0E7,GREEN]  image-sel=[E0E7,HOVER] cmd-ps=ps1_get_location  type='File'                where=sel.count==1 window='Hidden' sep='None')

    item(title="&(DSK) Filtered: <dm:last24hours>"     keys="0" image=[E157,BLUE] image-sel=[E158,HOVER] cmd='"@APP_USER_EVERYTHING_EXE"' args='-search "<!ext:url;lnk sort:dm> <dm:last24hours> " -filter " ├─ Defaults: DSK" -explore "@sel.dir"')
    item(title="&(DSK) Filtered: <dm:1999.12.24-dm:2025.12.24>"     keys="0" image=[E157,BLUE] image-sel=[E158,HOVER] cmd='"@APP_USER_EVERYTHING_EXE"' args='-search "<!ext:url;lnk sort:dm> <dm:1999.12.24-dm:2025.12.24> " -filter " ├─ Defaults: DSK" -explore "@sel.dir"')
    separator()

    item(title="&(ringerikelandskap) Filtered: <path:ringerikelandskap>"     keys="0" image=[E157,PINK] image-sel=[E158,HOVER] cmd='"@APP_USER_EVERYTHING_EXE"' args='-search "<sort:dm> nohighlight:<path:ringerikelandskap> " -filter " ├─ Defaults: DSK" -explore "@sel.dir"')
    separator()

    item(title="&(recent) Filtered: <git: child-date-run:week|child-date-modified:week>"     keys="0" image=[E157,WHITE] image-sel=[E158,HOVER] cmd='"@APP_USER_EVERYTHING_EXE"' args='-search "git: child-date-run:week|child-date-modified:week " -filter " ├─ Defaults: DSK" -explore "@sel.dir"')
    separator()

}


