<h4 id="_top">Properties</h4>
<style>
    dt {
        font-weight: 700;
    }

    dd dt {
        font-weight: 400;
    }

    dd {
        margin-bottom: 2em;
    }

    ul.fold, #_index-list {
        padding-left: 0;
    }

    ul.fold, #_index-list li {
        display: inline;
        list-style: none;
    }

    ul.fold > li, #_index-list li {
        display: inline;
    }

    ul.fold > li:after, #_index-list li li:after {
        content: ", ";
    }

    ul.fold > li:last-child:after, #_index-list li li:last-child:after {
        content: "";
    }

</style>
Shell supports the following properties classes:
<ul>
	<li><a href="#_validation-properties">Validation Properties</a></li>
	<li><a href="#_filter-properties">Filter Properties</a></li>
	<li><a href="#_menuitem-properties">Menuitem Properties</a></li>
	<li><a href="#_command-properties">Command Properties</a></li>
</ul>
<p>Please also see the full index of available properties <a href="#_index">below</a>.</p>
<h5 id="_index">Index</h5>
<ul id="_index-list" class="m-0 p-0 mb-4">
	<li>
		<ul>
			<li><a href="#admin">Admin</a></li>
			<li><a href="#arguments">arg</a></li>
			<li><a href="#arguments">args</a></li>
			<li><a href="#arguments">Arguments</a></li>
		</ul>
	</li>
	<li>
		<ul>
			<li><a href="#checked">Checked</a></li>
			<li><a href="#command">cmd</a></li>
			<li><a href="#column">col</a></li>
			<li><a href="#column">Column</a></li>
			<li><a href="#command">Command</a></li>
		</ul>
	</li>
	<li>
		<ul>
			<li><a href="#default">Default</a></li>
			<li><a href="#directory">dir</a></li>
			<li><a href="#directory">Directory</a></li>
		</ul>
	</li>
	<li>
		<ul>
			<li><a href="#expanded">Expanded</a></li>
		</ul>
	</li>
	<li>
		<ul>
			<li><a href="#find">Find</a></li>
		</ul>
	</li>
	<li>
		<ul>
			<li><a href="#image">Icon</a></li>
			<li><a href="#image">Image</a></li>
			<li><a href="#invoke">Invoke</a></li>
		</ul>
	</li>
	<li>
		<ul>
			<li><a href="#keys">Keys</a></li>
		</ul>
	</li>
	<li>
		<ul>
			<li><a href="#mode">Mode</a></li>
			<li><a href="#parent">Menu</a></li>
		</ul>
	</li>
	<li>
		<ul>
			<li><a href="#parent">Parent</a></li>
			<li><a href="#position">pos</a></li>
			<li><a href="#position">Position</a></li>
		</ul>
	</li>
	<li>
		<ul>
			<li><a href="#separator">sep</a></li>
			<li><a href="#separator">Separator</a></li>
		</ul>
	</li>
	<li>
		<ul>
			<li><a href="#tip">Tip</a></li>
			<li><a href="#title">Title</a></li>
			<li><a href="#type">Type</a></li>
		</ul>
	</li>
	<li>
		<ul>
			<li><a href="#verb">Verb</a></li>
			<li><a href="#visibility">vis</a></li>
			<li><a href="#visibility">Visibility</a></li>
		</ul>
	</li>
	<li>
		<ul>
			<li><a href="#wait">Wait</a></li>
			<li><a href="#where">Where</a></li>
			<li><a href="#window">Window</a></li>
		</ul>
	</li>
</ul>

<h4 id="_syntax">Syntax</h4>
<h5 id="_entry-types">Entry types</h5>
<p>In the following tables, the Types column shows to which entry types the
	property applies to.</p>
<p>The following abbreviations are used (if set in bold, then the property is mandatory for the given type):
</p>
<dl>
	<dt>mi</dt>
	<dd><a href="/docs/configuration/modify-items#item">modify item</a>, i.e. the
		item
		entry itself. Is basically required to evaluate if the process instructions are applied to any given target.
	</dd>
	<dt>mt</dt>
	<dd><a href="/docs/configuration/modify-items#target">modify target</a>, i.e.
		the menuitem of the existing menu to which the process instructions are applied
	</dd>
	<dt>nm</dt>
	<dd><a href="/docs/configuration/new-items#menu">new menu type</a></dd>
	<dt>ni</dt>
	<dd><a href="/docs/configuration/new-items#item">new item type</a></dd>
	<dt>ns</dt>
	<dd><a href="/docs/configuration/new-items#separator">new separator type</a>.
	</dd>
</dl>

<!--<h5 id="_property-classes">Property Classes</h5>-->
<h5 id="_validation-properties">Validation Properties</h5>
<p>Determine if a given <a href="/docs/configuration/modify-items">Modify items</a>
	or <a href="/docs/configuration/new-items">New items</a> entry should be
	processed when a context menu is displayed.</p>

<br/>
<ul class="fold">
	<li><a href="#mode">Mode</a></li>
	<li><a href="#type">Type</a></li>
	<li><a href="#where">Where</a></li>
</ul>
<br/>
<br/>
<div id="_validation-syntax" class="table-responsive">
	<h6>Syntax</h6>
	<table class="table">
		<thead>
		<tr>
			<th scope="col">Property</th>
			<th scope="col">Types<sup><a href="#_entry-types">(*)</a></sup></th>
			<th scope="col">Summary</th>
		</tr>
		</thead>
		<tbody>
		<tr id="where">
			<td>Where</td>
			<td>mi, nm, ni, ns</td>
			<td>Process given menuitem if <code>true</code> is returned. Allows the <strong>evaluation of arbitrary
				<a href="/docs/expressions">expressions</a></strong>, e.g. <a href="/docs/functions#if"><code>if()</code></a>.<br/>
				Default = <span class="syntax-keyword">true</span>
			</td>
		</tr>
		<tr id="mode">
			<td>Mode</td>
			<td>mi, nm, ni, ns</td>
			<td>Display menuitem by <strong>type of selection</strong>. The value has one of the following
				parameters
				(of type <a href="/docs/configuration/modify-items">string</a>):
				<table class="table">
					<tr>
						<td class="syntax-keyword">none</td>
						<td>Display menuitem when there is no selection.</td>
					</tr>
					<tr>
						<td class="syntax-keyword">single</td>
						<td>Display menuitem when there is a single object selected.</td>
					</tr>
					<tr>
						<td class="syntax-keyword">multi_unique</td>
						<td>Display menuitem when multiple objects of the same <a href="#type">type</a> are selected.
						</td>
					</tr>
					<tr>
						<td class="syntax-keyword">multi_single</td>
						<td>Display menuitem when multiple files with a single file extension are selected.</td>
					</tr>
					<tr>
						<td class="syntax-keyword">multiple</td>
						<td>Display any type of selection, unless there is none.</td>
					</tr>
				</table>
				Default = <span class="syntax-keyword">single</span>
			</td>
		</tr>

		<tr id="type">
			<td>Type</td>
			<td>mi, nm, ni, ns</td>
			<td>Specifies the <strong>types of objects</strong> for which the menuitem will be displayed.<br/>
				Possible values are shown below. Separate multiple types with the pipe character (<code>|</code>),
				in
				which case the menuitem is displayed if any of the given types is matched.<br/>
				To exclude a given type, prefix its value with the tilde character (<code>~</code>).
				<p class="has-text-danger">Expressions are not supported with this property.</p>
				<table class="table">
					<tr id="type-asterisks">
						<td class="syntax-keyword">*</td>
						<td>Display menuitem when any type is selected.</td>
					</tr>
					<tr id="type-file">
						<td class="syntax-keyword">File</td>
						<td>Display menuitem when files are selected.</td>
					</tr>
					<tr id="type-directory">
						<td class="syntax-keyword">Directory(Dir)</td>
						<td>Display menuitem when directories are selected.</td>
					</tr>
					<tr id="type-drive">
						<td class="syntax-keyword">Drive</td>
						<td>Display menuitem when drives are selected.</td>
					</tr>
					<tr id="type-usb">
						<td class="syntax-keyword">USB</td>
						<td>Display menuitem when USB flash-drives are selected.</td>
					</tr>
					<tr id="type-dvd">
						<td class="syntax-keyword">DVD</td>
						<td>Display menuitem when DVD-ROM drives are selected.</td>
					</tr>
					<tr id="type-fixed">
						<td class="syntax-keyword">Fixed</td>
						<td>Display menuitem when fixed drives are selected. Such drives have a fixed media; for
							example, a hard disk drive or flash drive.
						</td>
					</tr>
					<tr id="type-vhd">
						<td class="syntax-keyword">VHD</td>
						<td>Display menuitem when Virtual Hard Disks are selected.</td>
					</tr>
					<tr id="type-removable">
						<td class="syntax-keyword">Removable</td>
						<td>Display menuitem when the selected drives have removable media; for example, a floppy drive,
							thumb drive, or flash card
							reader.
						</td>
					</tr>
					<tr id="type-remote">
						<td class="syntax-keyword">Remote</td>
						<td>Display menuitem when the selected remote (network) drives are selected.</td>
					</tr>
					<tr id="type-back">
						<td class="syntax-keyword">Back</td>
						<td>Display menuitem when the background of all types are selected (<code>back</code>). Or
							specify one of
							the following more granular types for the background:
							<ul>
								<li>
									<code>back.directory</code>
								</li>
								<li>
									<code>back.drive</code>, including
									<ul>
										<li><code>back.fixed</code></li>
										<li><code>back.usb</code></li>
										<li><code>back.dvd</code></li>
										<li><code>back.vhd</code></li>
										<li><code>back.Removable</code></li>
									</ul>
								</li>
								<li>
									<code>back.namespace</code>, including
									<ul>
										<li><code>back.computer</code></li>
										<li><code>back.recyclebin</code></li>
									</ul>
								</li>
							</ul>
						</td>
					</tr>
					<tr id="type-desktop">
						<td class="syntax-keyword">Desktop</td>
						<td>Display menuitem when the Desktop is selected.</td>
					</tr>
					<tr id="type-namespace">
						<td class="syntax-keyword">Namespace</td>
						<td>Display menuitem when Namespaces are selected. Can be virtual objects such as My Network Places and Recycle Bin.
						</td>
					</tr>
					<tr id="type-computer">
						<td class="syntax-keyword">Computer</td>
						<td>Display menuitem when My Computer is selected.</td>
					</tr>
					<tr id="type-recyclebin">
						<td class="syntax-keyword">Recyclebin</td>
						<td>Display menuitem when the Recycle bin is selected.
						</td>
					</tr>
					<tr id="type-taskbar">
						<td class="syntax-keyword">Taskbar</td>
						<td>Display menuitem when the Taskbar is selected.
						</td>
					</tr>
				</table>
				Default = <span class="syntax-keyword">Accepts all types, except for the Taskbar.</span>
			</td>
		</tr>
		<tbody>
		</tbody>
	</table>
</div>

<h5 id="_filter-properties">Filter Properties</h5>
<p>For <a href="/docs/configuration/modify-items">Modify items</a> entries only,
	filter properties determine if a given menuitem is a valid <a href="/docs/configuration/modify-items#target">target</a> for the <a href="/docs/configuration/modify-items#process-instructions">process instructions</a></p>

<ul class="fold">
	<li><a href="#find">Find</a></li>
</ul>

<div id="_filter-syntax" class="table-responsive">
	<h6>Syntax</h6>
	<table class="table">
		<thead>
		<tr>
			<th scope="col">Property</th>
			<th scope="col">Types<sup><a href="#_entry-types">(*)</a></sup></th>
			<th scope="col">Summary</th>
		</tr>
		</thead>
		<tbody>

		<tr id="find">
			<td>Find</td>
			<td>nm, ni, ns</td>
			<td>
				<dl>
					<dt>For modify items (required)</dt>
					<dd>Apply the current item's process instructions to any existing menuitem if their <a href="#title"><code>title</code></a> property matches the
						pattern of the current item's <code>find</code> property.
					</dd>
					<dt>For dynamic items (optional)</dt>
					<dd><p>Display the current menuitem if the pattern of its <code>find</code> property matches the
						path name or path extension
						of the <strong>selected files</strong>.</p>
						<p>Default = <span class="syntax-keyword">null</span>, which means any string is "matched".
						</p>
					</dd>

					<dt>Syntax</dt>
					<dd>
						<pre><code>find = '%pattern%'
find = '%pattern%|%pattern%[...]'</code></pre>
						<p>where <strong>%pattern%</strong> can be one or
							more
							matching instructions (see Examples below). The
							following characters do have special meaning:</p>
						<ul>
							<li><code>|</code> <strong>Use to separate patterns.</strong> If any one pattern
								matches,
								the property yields
								<span class="syntax-keyword">true</span>.
							</li>
							<li><code>*</code> <strong>Matches any number of characters.</strong> Is used as a
								wildcard
								to
								match only the beginning or the end of the entire string (or word, if used in
								combination with the exclamation mark <code>!</code>).
							</li>
							<li><code>!</code> <strong>Negates the match</strong> of the current pattern, or
								<strong>limits
									the wildcard (<code>*</code>)</strong> to one word only.
							</li>
							<li><code>""</code> the enclosed string is treated as a <strong>word</strong>.
							</li>
						</ul>
						<p>A <strong>word</strong> is a sequence of
							alphanumerical characters that is confined to the left and to the right by either a
							space
							<code> </code>, a non-word character (e.g. <code>/</code> or <code>-</code>), or the
							beginning or the end of the entire string, respectively.</p></dd>
					<dt>Examples</dt>
					<dd>
						<div class="table-container">
							<table class="table">
								<thead>
								<tr>
									<th>Pattern</th>
									<th class="is-two-thirds">Matches any string that ...</th>
									<th>Would match</th>
									<th>Would not match</th>
								</tr>
								</thead>
								<tbody>
								<tr>
									<td><code>'foo'</code></td>
									<td>contains the literal string <code>foo</code> anywhere.</td>
									<td><code>foo</code>, <code>foobar</code>, <code>afoobar</code></td>
									<td><code>fo</code>, <code>f oo</code>, <code>bar</code></td>
								</tr>
								<tr>
									<td><code>'"foo"'</code></td>
									<td>contains the literal string <code>foo</code> as a whole word
										only.
									</td>
									<td><code>foo</code>, <code>foo/bar</code>, <code>some foo bar</code></td>
									<td><code>foobar</code>, <code>foofoo</code>, <code>bar</code></td>
								</tr>
								</tbody>
								<tbody>
								<tr>
									<td><code>'*foo'</code></td>
									<td>ends with the literal string <code>foo</code>.</td>
									<td><code>foo</code>, <code>barfoo</code>, <code>bar/foo</code></td>
									<td><code>foobar</code>, <code>fooo</code>, <code>foo </code></td>
								</tr>
								<tr>
									<td><code>'foo*'</code></td>
									<td>starts with the literal string <code>foo</code>.
									</td>
									<td><code>foo</code>, <code>foobar</code>, <code>foo/bar</code></td>
									<td><code> foobar</code>, <code>fo</code>, <code>yeti</code></td>
								</tr>
								</tbody>
								<tbody>
								<tr>
									<td><code>'!foo'</code></td>
									<td>does not contain the literal string <code>foo</code> anywhere.
									</td>
									<td><code>fobar</code>, <code>fo</code>, <code>kung-fu</code></td>
									<td><code>foo</code>, <code>foobar</code>, <code>barfoo/bar</code></td>
								</tr>
								<tr>
									<td><code>'!"foo"'</code></td>
									<td>does not contain the word
										<code>foo</code></td>
									<td><code>fobar</code>, <code>kung fu bar</code>, <code>foobar</code></td>
									<td><code>foo</code>, <code>kung foo bar</code>, <code>bar/foo/bar</code></td>
								</tr>
								<tr>
									<td><code>'!*foo'</code></td>
									<td>does not contain a word ending on
										<code>foo</code></td>
									<td><code>foobar</code>, <code>fooo-fo</code></td>
									<td><code>foo</code>, <code>foo bar</code>, <code>bar/foo</code></td>
								</tr>
								<tr>
									<td><code>'foo*!'</code></td>
									<td>does not contain a word starting with
										<code>foo</code></td>
									<td><code>myFooBar</code>, <code>barFoo</code></td>
									<td><code>foo</code>, <code>foobar</code>, <code>fo-fooo</code></td>
								</tr>
								</tbody>
								<tbody>
								<tr>
									<td colspan="4">
										<p><br/>For dynamic items the following syntax allows to match against file
											extensions:<br/><br/></p>
									</td>
								</tr>
								</tbody>
								<thead>
								<tr>
									<th>Pattern</th>
									<th>Matches any file extension ...</th>
									<th>Would match</th>
									<th>Would not match</th>
								</tr>
								</thead>
								<tbody>
								<tr>
									<td><code>'.exe'</code></td>
									<td>equal to <code>.exe</code></td>
									<td><code>setup.exe</code>, <code>notepad.exe</code></td>
									<td><code>install.bat</code>, <code>shell.nss</code>, <code>shell.ex_</code>,
										file
										without an extension.
									</td>
								</tr>
								<tr>
									<td><code>'!.exe'</code></td>
									<td>not equal to <code>.exe</code></td>
									<td><code>setup.exe.zip</code>, <code>video.mp4</code>, <code>shell.ex_</code>,
										file
										without an extension.
									</td>
									<td><code>setup.exe</code>, <code>shell.exe</code></td>
								</tr>
								</tbody>
								<tbody>
								<tr>
									<td><code>'.exe|.dll'</code></td>
									<td>equal to either <code>.exe</code> or <code>.dll</code></td>
									<td><code>shell.exe</code>, <code>shell.dll</code>
									</td>
									<td><code>shell.zip</code>, <code>shell.nss</code>, file
										without an extension.
									</td>
								</tr>
								</tbody>
							</table>
						</div>
					</dd>
				</dl>
			</td>
		</tr>

		</tbody>
	</table>
</div>

<h5 id="_menuitem-properties">Menuitem Properties</h5>
<p>This set of properties describe the appearance and location of a given menuitem. For modify-items, this is the target menuitem. For dynamic entries, this is the newly created menuitem.</p>
<dl>
	<dt>Appearance</dt>
	<dd>
		<ul class="fold">
			<li><a href="#checked">Checked</a></li>
			<li><a href="#default">Default</a></li>
			<li><a href="#image">Image</a></li>
			<li><a href="#separator">Separator</a></li>
			<li><a href="#tip">Tip</a></li>
			<li><a href="#title">Title</a></li>
			<li><a href="#visibility">Visibility</a></li>
		</ul>

	</dd>
	<dt>Location</dt>
	<dd>
		<ul class="fold">
			<li><a href="#column">Column</a></li>
			<li><a href="#expanded">Expanded</a></li>
			<li><a href="#keys">Keys</a></li>
			<li><a href="#parent">Menu</a></li>
			<li><a href="#parent">Parent</a></li>
			<li><a href="#position">Position</a></li>
		</ul>

	</dd>
</dl>

<div id="_menuitem-syntax" class="table-responsive">
	<h6>Syntax</h6>
	<table class="table">
		<thead>
		<tr>
			<th scope="col">Property</th>
			<th scope="col">Types<sup><a href="#_entry-types">(*)</a></sup></th>
			<th scope="col">Summary</th>
		</tr>
		</thead>
		<tbody>
		<tr id="title">
			<td>Title</td>
			<td>st, <strong>nm</strong>, <strong>ni</strong>
			</td>
			<td><p>Sets the <strong>caption</strong> of the menuitem.</p>
				<dl>
					<dt>For modify-items (optional)</dt>
					<dd><p>Default = <span class="syntax-keyword">null</span>, which means the title of the target
						is
						not changed.</p></dd>
					<dt>For dynamic items (required)</dt>
					<dd><p class="text-danger">It is mandatory for <a href="/docs/configuration/dynamic#menu">menu</a> and <a href="/docs/configuration/dynamic#menu">item</a> entries, unless a <a href=#image"><code>image</code></a> property is defined.</p>
					</dd>
				</dl>
			</td>
		</tr>
		<tr id="visibility">
			<td>Visibility (vis)</td>
			<td>st, nm, ni, ns</td>
			<td>Sets the <strong>visibility</strong> of a menuitem. Can have one of the following parameters:
				<table class="table">
					<tr id="visibility-hidden">
						<td class="syntax-keyword">Hidden</td>
						<td>Hide the menuitem.</td>
					</tr>
					<tr id="visibility-normal">
						<td class="syntax-keyword">Normal</td>
						<td>Enable the menuitem.</td>
					</tr>
					<tr id="visibility-disable">
						<td class="syntax-keyword">Disable</td>
						<td>Disable the menuitem.</td>
					</tr>
					<tr id="visibility-static">
						<td class="syntax-keyword">Static</td>
						<td>Display menuitem as label, with or without an <a href=#image"><code>image</code></a></td>
					</tr>
					<tr id="visibility-label">
						<td class="syntax-keyword">Label</td>
						<td>Display menuitem as label without an image</td>
					</tr>
				</table>
				<div class="notification is-info mt-5">
					<i class="mr-4">Note:</i> The values Static and Label are not available for modify-items.
				</div>
				Default = <span class="syntax-keyword">Normal</span>
			</td>
		</tr>
		<tr id="separator">
			<td>Separator (sep)</td>
			<td>st, nm, ni</td>
			<td>Add a <strong>separator</strong> to the menuitem:
				<table class="table">
					<tr id="separator-none">
						<td class="syntax-keyword">None</td>
						<td>Not adding a separator with the menuitem.</td>
					</tr>
					<tr id="separator-before">
						<td class="syntax-keyword">Before, Top</td>
						<td>Add a separator before the menuitem.</td>
					</tr>
					<tr id="separator-after">
						<td class="syntax-keyword">After, Bottom</td>
						<td>Add a separator after the menuitem.</td>
					</tr>
					<tr id="separator-both">
						<td class="syntax-keyword">Both</td>
						<td>Add a separator before and after the menuitem.</td>
					</tr>
				</table>
				Default = <span class="syntax-keyword">none</span>
			</td>
		</tr>

		<tr id="position">
			<td>Position (pos)</td>
			<td>st, nm, ni, ns</td>
			<td>The <strong>position</strong> at which a menuitem should be inserted into the <a href="/docs/configuration/dynamic#menu">menu</a>.<br/>
				Position can have one of the following parameters:
				<table class="table">
					<tr id="position-auto">
						<td class="syntax-keyword">Auto</td>
						<td>Insert the menuitem to the current position.</td>
					</tr>
					<tr id="position-middle">
						<td class="syntax-keyword">Middle</td>
						<td>Insert the menuitem to the middle of the <a href="/docs/configuration/dynamic#menu">menu</a>.
						</td>
					</tr>
					<tr id="position-top">
						<td class="syntax-keyword">Top</td>
						<td>Insert the menuitem to the top of the <a href="/docs/configuration/dynamic#menu">menu</a>.
						</td>
					</tr>
					<tr id="position-bottom">
						<td class="syntax-keyword">Bottom</td>
						<td>Insert the menuitem to the bottom of the <a href="/docs/configuration/dynamic#menu">menu</a>.
						</td>
					</tr>
					<tr id="position-integer">
						<td><code>Integer</code></td>
						<td>Insert the menuitem to a specified position.</td>
					</tr>
				</table>
				Default = <span class="syntax-keyword">auto</span>
			</td>
		</tr>

		<tr id="image">
			<td>Image, Icon</td>
			<td>st, nm, ni</td>
			<td>The <strong>icon</strong> that appears in a menuitem. This property can be assigned as image files,
				resource icons, glyph
				or color. With one of the following parameters
				<table class="table">
					<tr id="image-null">
						<td class="syntax-keyword">null</td>
						<td>Show menuitem without icon.</td>
					</tr>
					<tr id="image-inherit">
						<td class="syntax-keyword">Inherit</td>
						<td>@*Inheriting the image from the parent.*@
							Inherits this property from its parent item.
						</td>
					</tr>
					<tr id="image-cmd">
						<td class="syntax-keyword">Cmd</td>
						<td>Assign image from the command property.</td>
					</tr>
					<tr id="image-glyph">
						<td class="syntax-keyword">Glyph</td>
						<td>Assign image as Glyph.</td>
					</tr>
					<tr id="image-color">
						<td class="syntax-keyword">Color</td>
						<td>Assign image as color.</td>
					</tr>
					<tr id="image-path">
						<td class="syntax-keyword">Path</td>
						<td>Assign image from location path or resource icon.</td>
					</tr>
				</table>
				<div class="notification is-info mt-5">
					<i class="mr-4">Note:</i>The value Cmd is not available for modify-items
					targets.
				</div>
				Default = <span class="syntax-keyword">null</span>
			</td>
		</tr>
		<tr id="parent">
			<td>Parent, Menu</td>
			<td>st, nm, ni, ns</td>
			<td><p><strong>Move current menuitem</strong> to another <a href="/docs/configuration/dynamic#menu">menu</a>.</p>
				Default = <span class="syntax-keyword">null</span></td>
		</tr>
		<tr id="checked">
			<td>Checked</td>
			<td>st, ni</td>
			<td><strong>Type of select option</strong>:
				<table class="table">
					<tr id="checked-0">
						<td class="syntax-keyword">0</td>
						<td>Not checked</td>
					</tr>
					<tr id="checked-1">
						<td class="syntax-keyword">1</td>
						<td>Display as check mark.</td>
					</tr>
					<tr id="checked-2">
						<td class="syntax-keyword">2</td>
						<td>Display as radio bullet.</td>
					</tr>
				</table>
				Default = <span class="syntax-keyword">0</span>
			</td>
		</tr>
		<tr id="default">
			<td>Default</td>
			<td>st, ni</td>
			<td>
				<p>Specifies that the <a href="/docs/configuration/dynamic#item">item</a> is the default. A <a href="/docs/configuration/dynamic#menu">menu</a> can contain only one default
					menuitem, which is <strong>displayed in bold</strong>.</p>
				Default = <span class="syntax-keyword">false</span>
			</td>
		</tr>
		<tr id="expanded">
			<td>Expanded</td>
			<td>nm</td>
			<td>
				<p>Move all immediate menuitems to the parent <a href="/docs/configuration/dynamic#menu">menu</a>.</p>
				Default = <span class="syntax-keyword">false</span>
			</td>
		</tr>
		<tr id="column">
			<td>Column(col)</td>
			<td>nm, ni</td>
			<td><p>Create a new <strong>column</strong>.</p>
				Default = <span class="syntax-keyword">true</span>
		</tr>
		<tr id="keys">
			<td>Keys</td>
			<td>st, nm, ni</td>
			<td><p>Show <strong>keyboard shortcuts</strong>.</p>
				Default = <span class="syntax-keyword">null</span></td>
		</tr>
		<tr id="tip">
			<td>Tip</td>
			<td>st, nm, ni</td>
			<td>
				<p>Show a <strong>tooltip</strong> for the current <a href="/docs/configuration/dynamic#menu">menu</a>or <a href="/docs/configuration/dynamic#item">item</a>.</p>
				Default = <span class="syntax-keyword">null</span><br/>
				<strong>Syntax</strong><br/>
				<pre><code class="lang-shell">tip = "Lorem Ipsum is simply dummy text."
tip = ["Lorem Ipsum is simply dummy text.", tip.info]
tip = ["Lorem Ipsum is simply dummy text.", tip.info, 1.2]</code></pre>
			</td>
		</tr>
		</tbody>
	</table>
</div>

<h5 id="_command-properties">Command Properties</h5>
<p>This set of properties describe how a command is executed. Only available for dynamic items.</p>

<ul class="fold">
	<li><a href="#admin">Admin</a></li>
	<li><a href="#arguments">Arguments</a></li>
	<li><a href="#command">Command</a></li>
	<li><a href="#directory">Directory</a></li>
	<li><a href="#invoke">Invoke</a></li>
	<li><a href="#verb">Verb</a></li>
	<li><a href="#wait">Wait</a></li>
	<li><a href="#window">Window</a></li>
</ul>

<div id="_command-syntax" class="table-responsive">
	<h6>Syntax</h6>
	<table class="table">
		<thead>
		<tr>
			<th scope="col">Property</th>
			<th scope="col">Types<sup><a href="#_entry-types">(*)</a></sup></th>
			<th scope="col">Summary</th>
		</tr>
		</thead>
		<tbody>

		<tr id="command">
			<td>Command (cmd)</td>
			<td>ni</td>
			<td><p>The <strong>command</strong> associated with the menuitem. Occurs when the menuitem is clicked or
				selected using a shortcut key or access key defined for the menuitem.</p>
				Default = <span class="syntax-keyword">null</span>
			</td>
		</tr>

		<tr id="arguments">
			<td>Arguments (arg, args)</td>
			<td>ni</td>
			<td><p>The <strong>command line parameters</strong> to pass to the <a href=#command">command</a> property of a menuitem.</p>
				Default = <span class="syntax-keyword">null</span>
			</td>
		</tr>

		<tr id="invoke">
			<td>Invoke</td>
			<td>ni</td>
			<td>Set <strong>execution type</strong>
				<table class="table">
					<tr id="invoke-single">
						<td class="syntax-keyword">0, single</td>
						<td>execute the <a href="#command">command</a> only once in total. The list of selected
							items can be accessed with <a href="/docs/functions/sel#sel"><code>@sel</code></a>
						</td>
					</tr>
					<tr id="invoke-multiple">
						<td class="syntax-keyword">1, multiple</td>
						<td>execute the <a href="#command"><code>command</code></a> once for every single item in the current
							selection. The currently processed item can be accessed with e.g <a href="/docs/functions/sel#sel.path.quote"><code>@sel.path.quote</code></a>
						</td>
					</tr>
				</table>
				Default = <span class="syntax-keyword">0</span>
			</td>
		</tr>

		<tr id="window">
			<td>Window</td>
			<td>ni</td>
			<td>Controls how the <strong>window</strong> of the executed <a href="#command">command</a> is to be shown. Can be one of the following
				parameters:
				<p class="syntax-keyword"><code>Hidden</code>, <code>Show</code>, <code>Visible</code>,
					<code>Minimized</code>, <code>Maximized</code></p>
				Default = <span class="syntax-keyword">show</span>
			</td>
		</tr>

		<tr id="directory">
			<td>Directory (dir)</td>
			<td>ni</td>
			<td><p>Specifies the <strong>working directory</strong> to
				execute
				the <a href="#command">command</a> in.</p>
				Default = <span class="syntax-keyword">null</span>
			</td>
		</tr>

		<tr id="admin">
			<td>Admin</td>
			<td>ni</td>
			<td><p>Execute the <a href="#command">command</a>
				with <strong>administrative permissions</strong>.</p>
				Default = <span class="syntax-keyword">false</span>
			</td>
		</tr>

		<tr id="verb">
			<td>Verb</td>
			<td>ni</td>
			<td>Specifies the <strong>default operation</strong> for the selected file. Value type <a href="/docs/configuration/modify-items">string</a>
				and can have one of the following parameters:<br/>
				<table class="table">
					<tr id="verb-null">
						<td>null</td>
						<td>Specifies that the operation is the default for the selected file type.</td>
					</tr>
					<tr id="verb-open">
						<td>Open</td>
						<td>Opens a file or an application.</td>
					</tr>
					<tr id="verb-openas">
						<td>OpenAs</td>
						<td>Opener dialog when no program is associated to the extension.</td>
					</tr>
					<tr id="verb-runas">
						<td>RunAs</td>
						<td>In Windows 7 and Vista, opens the UAC dialog andin others, open the Run as... Dialog.</td>
					</tr>
					<tr id="verb-edit">
						<td>Edit</td>
						<td>Opens the default text editor for the file.</td>
					</tr>
					<tr id="verb-explore">
						<td>Explore</td>
						<td>Opens the Windows Explorer in the folder specified in Directory.</td>
					</tr>
					<tr id="verb-properties">
						<td>Properties</td>
						<td>Opens the properties window of the file.</td>
					</tr>
					<tr id="verb-print">
						<td>Print</td>
						<td>Start printing the file with the default application.</td>
					</tr>
					<tr id="verb-find">
						<td>Find</td>
						<td>Start a search.</td>
					</tr>
				</table>
				Default = <span class="syntax-keyword">open</span>
			</td>
		</tr>
		<tr id="wait">
			<td>Wait</td>
			<td>ni</td>
			<td>
				<p><strong>Wait</strong> for the <a href="#command">command</a>
					to complete.</p>
				Default = <span class="syntax-keyword">false</span>
			</td>
		</tr>
		</tbody>
	</table>
</div>
