// =============================================================================
// [14:17] -> 01.09.2024: relative directory paths
// example for retrieving path through the parent of app dir
$DIR_GOTO = '@app.dir/../../__GOTO__'




// =============================================================================
// PRI:4 - [11:23] -> 11.06.2024: open multiple files in app
// for(i=0, i< sel.count, '"@sel[i]" ')
menu(where=sel.count>0 mode="multiple" title='Convert' sep="both" image= \uE14A)
{
    menu(title='Audio' image=\uE1F4)
    {
        item(title='Convert (@sel.count) files to OggOpus' image=inherit cmd=
            for(i=0, i< sel.count) {
                if(str.equals(sel[i].file.ext,[".aac",".flac",".m4a",".mp3",".ogg",".wav",".wma"]),
                    $filePath = sel[i].directory
                    $fileName= sel[i].title
                    $newFileName = fileName + '.ogg'
                    $newFilePath = '"@filePath\@newFileName"'

                    msg('ffmpeg -i @sel[i].path.quote -c:a libopus -b:a 510k @newFilePath')
                )
            }
        )
    }
}




// =============================================================================
// [21:40] -> 20.07.2024: context for recycle bin
menu(type='~Taskbar|~Desktop|~Titlebar' mode='multiple' expanded=true) {

    item(
        title = "&Context: Recycle Bin"
        where = sel.raw=='::{645FF040-5081-101B-9F08-00AA002F954E}'
        image = [E10A,SOFT]
        tip   = [sel.raw+"\n"+sel.parent.raw,TIP3,0.8]
    )
}

// =============================================================================
// [21:34] -> 20.07.2024:
settings
{
    priority=1
    exclude.where = !process.is_explorer
    showdelay = 200
    // Options to allow modification of system items
    modify.remove.duplicate=1
    tip.enabled=true
}
import 'imports/theme.nss'
import 'imports/images.nss'
import 'imports/modify.nss'
menu(mode="multiple" title="Pin/Unpin" image=icon.pin) {}
menu(mode="multiple" title=title.more_options image=icon.more_options) {}
menu(mode='multiple' expanded='true' where=not(wnd.is_desktop and sel.type==0)
    and sel.raw!='::{645FF040-5081-101B-9F08-00AA002F954E}'
    and sel.parent.raw!='::{645FF040-5081-101B-9F08-00AA002F954E}')
{
    import 'imports/terminal.nss'
    import 'imports/file-manage.nss'
    import 'imports/develop.nss'
    import 'imports/goto.nss'
}
menu(type='*' where=wnd.is_taskbar expanded='true')
{
    import 'imports/terminal.nss'
    separator()
    import 'imports/taskbar.nss'
}
// =============================================================================
// [11:08] -> 20.07.2024: goto recent locations
// https://nilesoft.org/forum/viewtopic.php?t=31
menu(title='Go To@"\t"Address Bar'  where=reg.exists('HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\TypedPaths', 'url1') image=\uE14A) {
    $path01=reg.get('HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\TypedPaths', 'url1')
    item(title=if(len(path.location.name(path01))==1, path01, `...\` + path.location.name(path01) + `\` + path.title(path01)) where=path.exists(path01) image=\uE1F4 tip=path01
        cmd=if(window.name=='CabinetWClass' and !keys.shift(), command.navigate(path01), path01))
    $path02=reg.get('HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\TypedPaths', 'url2')
    item(title=if(len(path.location.name(path02))==1, path02, `...\` + path.location.name(path02) + `\` + path.title(path02)) where=path.exists(path02) image=\uE1F4 tip=path02
        cmd=if(window.name=='CabinetWClass' and !keys.shift(), command.navigate(path02), path02))
    $path03=reg.get('HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\TypedPaths', 'url3')
    item(title=if(len(path.location.name(path03))==1, path03, `...\` + path.location.name(path03) + `\` + path.title(path03)) where=path.exists(path03) image=\uE1F4 tip=path03
        cmd=if(window.name=='CabinetWClass' and !keys.shift(), command.navigate(path03), path03))
    $path04=reg.get('HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\TypedPaths', 'url4')
    item(title=if(len(path.location.name(path04))==1, path04, `...\` + path.location.name(path04) + `\` + path.title(path04)) where=path.exists(path04) image=\uE1F4 tip=path04
        cmd=if(window.name=='CabinetWClass' and !keys.shift(), command.navigate(path04), path04))
    $path05=reg.get('HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\TypedPaths', 'url5')
    item(title=if(len(path.location.name(path05))==1, path05, `...\` + path.location.name(path05) + `\` + path.title(path05)) where=path.exists(path05) image=\uE1F4 tip=path05
        cmd=if(window.name=='CabinetWClass' and !keys.shift(), command.navigate(path05), path05))
    $path06=reg.get('HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\TypedPaths', 'url6')
    item(title=if(len(path.location.name(path06))==1, path06, `...\` + path.location.name(path06) + `\` + path.title(path06)) where=path.exists(path06) image=\uE1F4 tip=path06
        cmd=if(window.name=='CabinetWClass' and !keys.shift(), command.navigate(path06), path06))
    $path07=reg.get('HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\TypedPaths', 'url7')
    item(title=if(len(path.location.name(path07))==1, path07, `...\` + path.location.name(path07) + `\` + path.title(path07)) where=path.exists(path07) image=\uE1F4 tip=path07
        cmd=if(window.name=='CabinetWClass' and !keys.shift(), command.navigate(path07), path07))
    $path08=reg.get('HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\TypedPaths', 'url8')
    item(title=if(len(path.location.name(path08))==1, path08, `...\` + path.location.name(path08) + `\` + path.title(path08)) where=path.exists(path08) image=\uE1F4 tip=path08
        cmd=if(window.name=='CabinetWClass' and !keys.shift(), command.navigate(path08), path08))
    $path09=reg.get('HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\TypedPaths', 'url9')
    item(title=if(len(path.location.name(path09))==1, path09, `...\` + path.location.name(path09) + `\` + path.title(path09)) where=path.exists(path09) image=\uE1F4 tip=path09
        cmd=if(window.name=='CabinetWClass' and !keys.shift(), command.navigate(path09), path09))
    $path10=reg.get('HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\TypedPaths', 'url10')
    item(title=if(len(path.location.name(path10))==1, path10, `...\` + path.location.name(path10) + `\` + path.title(path10)) where=path.exists(path10) image=\uE1F4 tip=path10
        cmd=if(window.name=='CabinetWClass' and !keys.shift(), command.navigate(path10), path10))
    $path11=reg.get('HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\TypedPaths', 'url11')
    item(title=if(len(path.location.name(path11))==1, path11, `...\` + path.location.name(path11) + `\` + path.title(path11)) where=path.exists(path11) image=\uE1F4 tip=path11
        cmd=if(window.name=='CabinetWClass' and !keys.shift(), command.navigate(path11), path11))
    $path12=reg.get('HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\TypedPaths', 'url12')
    item(title=if(len(path.location.name(path12))==1, path12, `...\` + path.location.name(path12) + `\` + path.title(path12)) where=path.exists(path12) image=\uE1F4 tip=path12
        cmd=if(window.name=='CabinetWClass' and !keys.shift(), command.navigate(path12), path12))
    $path13=reg.get('HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\TypedPaths', 'url13')
    item(title=if(len(path.location.name(path13))==1, path13, `...\` + path.location.name(path13) + `\` + path.title(path13)) where=path.exists(path13) image=\uE1F4 tip=path13
        cmd=if(window.name=='CabinetWClass' and !keys.shift(), command.navigate(path13), path13))
    $path14=reg.get('HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\TypedPaths', 'url14')
    item(title=if(len(path.location.name(path14))==1, path14, `...\` + path.location.name(path14) + `\` + path.title(path14)) where=path.exists(path14) image=\uE1F4 tip=path14
        cmd=if(window.name=='CabinetWClass' and !keys.shift(), command.navigate(path14), path14))
    $path15=reg.get('HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\TypedPaths', 'url15')
    item(title=if(len(path.location.name(path15))==1, path15, `...\` + path.location.name(path15) + `\` + path.title(path15)) where=path.exists(path15) image=\uE1F4 tip=path15
        cmd=if(window.name=='CabinetWClass' and !keys.shift(), command.navigate(path15), path15))
    $path16=reg.get('HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\TypedPaths', 'url16')
    item(title=if(len(path.location.name(path16))==1, path16, `...\` + path.location.name(path16) + `\` + path.title(path16)) where=path.exists(path16) image=\uE1F4 tip=path16
        cmd=if(window.name=='CabinetWClass' and !keys.shift(), command.navigate(path16), path16))
    $path17=reg.get('HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\TypedPaths', 'url17')
    item(title=if(len(path.location.name(path17))==1, path17, `...\` + path.location.name(path17) + `\` + path.title(path17)) where=path.exists(path17) image=\uE1F4 tip=path17
        cmd=if(window.name=='CabinetWClass' and !keys.shift(), command.navigate(path17), path17))
    $path18=reg.get('HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\TypedPaths', 'url18')
    item(title=if(len(path.location.name(path18))==1, path18, `...\` + path.location.name(path18) + `\` + path.title(path18)) where=path.exists(path18) image=\uE1F4 tip=path18
        cmd=if(window.name=='CabinetWClass' and !keys.shift(), command.navigate(path18), path18))
    $path19=reg.get('HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\TypedPaths', 'url19')
    item(title=if(len(path.location.name(path19))==1, path19, `...\` + path.location.name(path19) + `\` + path.title(path19)) where=path.exists(path19) image=\uE1F4 tip=path19
        cmd=if(window.name=='CabinetWClass' and !keys.shift(), command.navigate(path19), path19))
    $path20=reg.get('HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\TypedPaths', 'url20')
    item(title=if(len(path.location.name(path20))==1, path20, `...\` + path.location.name(path20) + `\` + path.title(path20)) where=path.exists(path20) image=\uE1F4 tip=path20
        cmd=if(window.name=='CabinetWClass' and !keys.shift(), command.navigate(path20), path20))
    $path21=reg.get('HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\TypedPaths', 'url21')
    item(title=if(len(path.location.name(path21))==1, path21, `...\` + path.location.name(path21) + `\` + path.title(path21)) where=path.exists(path21) image=\uE1F4 tip=path21
        cmd=if(window.name=='CabinetWClass' and !keys.shift(), command.navigate(path21), path21))
    $path22=reg.get('HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\TypedPaths', 'url22')
    item(title=if(len(path.location.name(path22))==1, path22, `...\` + path.location.name(path22) + `\` + path.title(path22)) where=path.exists(path22) image=\uE1F4 tip=path22
        cmd=if(window.name=='CabinetWClass' and !keys.shift(), command.navigate(path22), path22))
    $path23=reg.get('HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\TypedPaths', 'url23')
    item(title=if(len(path.location.name(path23))==1, path23, `...\` + path.location.name(path23) + `\` + path.title(path23)) where=path.exists(path23) image=\uE1F4 tip=path23
        cmd=if(window.name=='CabinetWClass' and !keys.shift(), command.navigate(path23), path23))
    $path24=reg.get('HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\TypedPaths', 'url24')
    item(title=if(len(path.location.name(path24))==1, path24, `...\` + path.location.name(path24) + `\` + path.title(path24)) where=path.exists(path24) image=\uE1F4 tip=path24
        cmd=if(window.name=='CabinetWClass' and !keys.shift(), command.navigate(path24), path24))
    $path25=reg.get('HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\TypedPaths', 'url25')
    item(title=if(len(path.location.name(path25))==1, path25, `...\` + path.location.name(path25) + `\` + path.title(path25)) where=path.exists(path25) image=\uE1F4 tip=path25
        cmd=if(window.name=='CabinetWClass' and !keys.shift(), command.navigate(path25), path25))
    $path26=reg.get('HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\TypedPaths', 'url26')
    item(title=if(len(path.location.name(path26))==1, path26, `...\` + path.location.name(path26) + `\` + path.title(path26)) where=path.exists(path26) image=\uE1F4 tip=path26
        cmd=if(window.name=='CabinetWClass' and !keys.shift(), command.navigate(path26), path26)) }

// =============================================================================
// [10:55] -> 20.07.2024: using keys for menus
menu(title='Goto : &System@"\t"Common' type='Taskbar|File|Desktop|Dir|Drive|Back.Dir|Back.Drive' image=[E0E8,WHITE1] image-sel=[E22C,WHITE] sep='None') {
    item(column vis='Static' image=[E00A,DARK])
    item(title="System" image=[E0EA,GREEN] vis='Static' sep='Both')
    separator()
}

// =============================================================================
// [10:45] -> 20.07.2024:
menu(title='make variables local' expanded=1) {
    // default order
    $do='0123456789'
    // next item
    $ni = 0
    // item paths
    $ip = ''
    menu(title='Go To@"\t"temporary' image=\uE14A) {
        item(title='Add current path' keys=ni+'/'+len(do) type='drive|dir|back.drive|back.dir|Desktop'
        vis=if(len(do)<=ni or str.contains(str.join(ip,'|')+'|', sel+'|'), 'disable','normal') sep='after' image=icon.new_folder
            cmd={ ip=[  if(ni==0, sel, ip[0]), if(ni==1, sel, ip[1]), if(ni==2, sel, ip[2]), if(ni==3, sel, ip[3]), if(ni==4, sel, ip[4]),
                        if(ni==5, sel, ip[5]), if(ni==6, sel, ip[6]), if(ni==7, sel, ip[7]), if(ni==8, sel, ip[8]), if(ni==9, sel, ip[9])] ni+=1 })
        item(title=ip[toint(do[0])] where=path.exists(ip[toint(do[0])]) image=\uE1F4 commands{
            cmd=if(window.name=='CabinetWClass' and !keys.shift(), command.navigate(this.title), this.title),
            /*no need to change the order*/})
        item(title=ip[toint(do[1])] where=path.exists(ip[toint(do[1])]) image=\uE1F4 commands{
            cmd=if(window.name=='CabinetWClass' and !keys.shift(), command.navigate(this.title), this.title),
            cmd={ do=str.get(do, 1)+str.remove(do, 1, 1) }})
        item(title=ip[toint(do[2])] where=path.exists(ip[toint(do[2])]) image=\uE1F4 commands{
            cmd=if(window.name=='CabinetWClass' and !keys.shift(), command.navigate(this.title), this.title),
            cmd={ do=str.get(do, 2)+str.remove(do, 2, 1) }})
        item(title=ip[toint(do[3])] where=path.exists(ip[toint(do[3])]) image=\uE1F4 commands{
            cmd=if(window.name=='CabinetWClass' and !keys.shift(), command.navigate(this.title), this.title),
            cmd={ do=str.get(do, 3)+str.remove(do, 3, 1) }})
        item(title=ip[toint(do[4])] where=path.exists(ip[toint(do[4])]) image=\uE1F4 commands{
            cmd=if(window.name=='CabinetWClass' and !keys.shift(), command.navigate(this.title), this.title),
            cmd={ do=str.get(do, 4)+str.remove(do, 4, 1) }})
        item(title=ip[toint(do[5])] where=path.exists(ip[toint(do[5])]) image=\uE1F4 commands{
            cmd=if(window.name=='CabinetWClass' and !keys.shift(), command.navigate(this.title), this.title),
            cmd={ do=str.get(do, 5)+str.remove(do, 5, 1) }})
        item(title=ip[toint(do[6])] where=path.exists(ip[toint(do[6])]) image=\uE1F4 commands{
            cmd=if(window.name=='CabinetWClass' and !keys.shift(), command.navigate(this.title), this.title),
            cmd={ do=str.get(do, 6)+str.remove(do, 6, 1) }})
        item(title=ip[toint(do[7])] where=path.exists(ip[toint(do[7])]) image=\uE1F4 commands{
            cmd=if(window.name=='CabinetWClass' and !keys.shift(), command.navigate(this.title), this.title),
            cmd={ do=str.get(do, 7)+str.remove(do, 7, 1) }})
        item(title=ip[toint(do[8])] where=path.exists(ip[toint(do[8])]) image=\uE1F4 commands{
            cmd=if(window.name=='CabinetWClass' and !keys.shift(), command.navigate(this.title), this.title),
            cmd={ do=str.get(do, 8)+str.remove(do, 8, 1) }})
        item(title=ip[toint(do[9])] where=path.exists(ip[toint(do[9])]) image=\uE1F4 commands{
            cmd=if(window.name=='CabinetWClass' and !keys.shift(), command.navigate(this.title), this.title),
            cmd={ do=str.get(do, 9)+str.remove(do, 9, 1) }})
        item(title='Clear' where=ni>0 image=image.glyph(\uE0CE) sep='both' cmd={ do='0123456789' ni=0 ip='' })
        // where=keys.shift()
        item(title='Set NS sys...'  image=image.glyph(\uE0F9) cmd={ do='0123456789' ip=[user.desktop, user.downloads, user.documents, user.pictures] ni=len(ip) })
        item(title='Set NS user...' image=image.glyph(\uE0F9) cmd={ do='01234'      ip=['sys.dir', sys.bin, sys.prog, sys.prog32, sys.users] ni=len(ip) })
        item(title='Set Example...' image=image.glyph(\uE0F9) cmd={ do='0123456789' ip=['C:', user.desktop, '%temp%', sys.prog, sys.bin, '@user.localappdata\Programs\Microsoft VS Code'] ni=len(ip) }) }
}
// =============================================================================
// [15:57] -> 06.07.2024: video_checker_py_ffmpeg.nss
item(
    type='file'
    find='.ts|.mp4|.mkv|.webm|.mov|.wmv|.avi|.flv|.mpg'
    title='Check "@sel.file.name" Integrity'
    image=[\uE230, #ffd343]
    cmd='<script.py_placeholder>'
    args='--paths @sel(true, " ")'
    pos=middle
)
item(
    where=sel.count > 1
    mode='multiple'
    find='.ts|.mp4|.mkv|.webm|.mov|.wmv|.avi|.flv|.mpg|.m2ts|.m4v'
    title='Check (@sel.count) Videos Integrity'
    image=[\uE230, #ffd343]
    cmd='<script.py_placeholder>'
    args='--paths @sel(true, " ")'
    pos=middle
)
// =============================================================================
// [15:53] -> 06.07.2024: based on extension
// 4ianpyn
// OP
//  — 08/31/2023 2:16 PM
// Update(s) were released down below, please use the newer version.
// Verify selected single/multiple video using Python script via FFmpeg binary. Older Python version should works as well.
// Import video_checker_py_ffmpeg.nss or add these lines into your shell.nss dynamic {...}
item(type='file' find='.ts|.mp4|.mkv|.webm|.mov|.wmv|.avi|.flv|.mpg' title='Check "@sel.file.name" Integrity' image=[\uE230, #ffd343] cmd='<script.py_placeholder>' args='--paths @sel(true, " ")' pos=middle)
item(where=sel.count > 1 mode='multiple' find='.ts|.mp4|.mkv|.webm|.mov|.wmv|.avi|.flv|.mpg|.m2ts|.m4v' title='Check (@sel.count) Videos Integrity' image=[\uE230, #ffd343] cmd='<script.py_placeholder>' args='--paths @sel(true, " ")' pos=middle)
// =============================================================================
// REFACTORED
$checkerPySciptPath = '<script.py_placeholder>'
$truncateLength = 15
/* 3: Lorem Ipsum.mkv -> Lor ... mkv */
$allowedFormats_checker = '.ts|.mp4|.mkv|.webm|.mov|.wmv|.avi|.flv|.mpg|.m2ts|.m4v'
item(mode='multiple' find=allowedFormats_checker title=if(
    sel.count > 1,
    'Check (@sel.count) Videos Integrity',
    'Check "@(sel.file.title.length > truncateLength ? str.sub(sel.file.title, 0, truncateLength) : sel.file)@(sel.file.title.length > truncateLength ? " ... " : "")@(sel.file.title.length > truncateLength ? str.replace(sel.file.ext, ".", "") : "")" Integrity'
    )
image=[\uE230, #ffd343] cmd=checkerPySciptPath args='--paths @sel(true, " ")')

// =============================================================================
// [12:12] -> 05.06.2024: unsorted
// \"$fileName - $($fileSizeGB.ToString('N2')) GB\";
// \"$fileName - $($fileSizeMB.ToString('N2')) MB\";
// } else { $fileSizeKB = $fileSizeBytes / 1KB; \"$fileName - $($fileSizeKB.ToString('N2')) KB\"; } }; $list | Out-String | Set-Clipboard;` window=hidden)


// =============================================================================
// [15:40] -> 01.06.2024: using key modifiers
//
/*
    USAGE:
        :: // admin
        :: admin='@EXE_KEYS_ADMIN'

        :: // query
        :: where='@EXE_KEYS_ADMIN'

        :: // directory
        :: item(title="app.dir"
        ::     commands{
        ::         cmd=if('@DIR_KEYS_COPY',clipboard.set('@app.dir')),
        ::         cmd=if('@DIR_KEYS_OPEN',('"@app.dir"')),
        ::     }
        :: )

        :: // executable
        :: item(title="cmd.exe"
        ::     admin=keys.rbutton()
        ::     commands{
        ::        cmd=if('@EXE_KEYS_COPY_DIR',clipboard.set('@sys.dir\System32\')),
        ::        cmd=if('@EXE_KEYS_COPY_EXE',clipboard.set('@sys.dir\System32\cmd.exe')),
        ::        cmd=if('@EXE_KEYS_OPEN_DIR',('"@sys.dir\System32\"')),
        ::        cmd=if('@EXE_KEYS_OPEN_EXE',('"@sys.dir\System32\cmd.exe"')),
        ::     }
        :: )
*/
//
// -> Key-kombinations for directories
$DIR_KEYS_COPY = (key(key.control, key.lbutton) OR key(key.control, key.rbutton))
$DIR_KEYS_OPEN = (!key(key.control) OR key(key.shift, key.lbutton) OR key(key.shift, key.rbutton))
// -> Key-kombinations for executables
$EXE_KEYS_ADMIN    = key.rbutton()
$EXE_KEYS_COPY_DIR = key(key.control, key.lbutton)
$EXE_KEYS_COPY_EXE = key(key.control, key.rbutton)
$EXE_KEYS_OPEN_DIR = (key(key.shift, key.lbutton) OR key(key.shift, key.rbutton))
$EXE_KEYS_OPEN_EXE = (!key(key.shift) AND !key(key.control))
//
// directory
item(title="app.dir"
    keys="dir"
    commands{
        cmd=if('@DIR_KEYS_COPY',clipboard.set('@app.dir')),
        cmd=if('@DIR_KEYS_OPEN',('"@app.dir"')),
    }
)
//
// executable
item(title="cmd.exe"
    keys="exe"
    admin='@EXE_KEYS_ADMIN'
    commands{
        cmd=if('@EXE_KEYS_COPY_DIR',clipboard.set('@sys.dir\System32\')),
        cmd=if('@EXE_KEYS_COPY_EXE',clipboard.set('@sys.dir\System32\cmd.exe')),
        cmd=if('@EXE_KEYS_OPEN_DIR',('"@sys.dir\System32\"')),
        cmd=if('@EXE_KEYS_OPEN_EXE',('"@sys.dir\System32\cmd.exe"')),
    }
)

// =============================================================================
// [12:52] -> 01.06.2024: using key modifiers
$DIR_REGISTRYCHANGESVIEW = '@app.dir\PORTAL\APPS\grp_nirsoft\app_registrychangesview\exe'
$EXE_REGISTRYCHANGESVIEW = '@app.dir\PORTAL\APPS\grp_nirsoft\app_registrychangesview\exe\RegistryChangesView.exe'
item(title="&RegistryChangesView"
    keys="exe"
    type='Taskbar|File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    image='@EXE_REGISTRYCHANGESVIEW'
    commands{
        // Keys: Set Clipboard
        cmd=if(key(key.control, key.lbutton),clipboard.set('@DIR_REGISTRYCHANGESVIEW')),
        cmd=if(key(key.control, key.rbutton),clipboard.set('@EXE_REGISTRYCHANGESVIEW')),
        // Keys: Open Directory
        cmd=if(key(key.shift),('"@DIR_REGISTRYCHANGESVIEW"')),
        // Keys: Execute
        cmd=if(!key(key.shift, key.control),('"@EXE_REGISTRYCHANGESVIEW"')),
    }
    tip=['"@EXE_REGISTRYCHANGESVIEW"',TIP3,0.75]
    window='Visible'
    admin=keys.rbutton()
)


// =============================================================================
// [18:08] -> 31.05.2024: execute command on selected files/dirs
//
$GIT_ADD_ALL = '/K (CD /D "@sel.dir") && (git add *) && pause'
//
$GET_SELECTION_AS_STRING = for(i=0, i< sel.count, '"@sel[i]" ')
$CMD_GIT_ADD_SELECTION = '/C (CD /D "@sel.dir") && (git add @GET_SELECTION_AS_STRING)'
$CMD_GIT_ADD_SELECTION_V = '/K (CD /D "@sel.dir") && (git add @GET_SELECTION_AS_STRING) && PAUSE'
// menu
menu(title="&Git Commands" type='Desktop|Dir|Drive|Back.Dir|Back.Drive|File' image=[E122,BLUE] image-sel=[E123,HOVER] sep='None')
{
    // section-header
    item(title="Git Commands" keys="" image=[E22C,PURPLE] vis='Static' sep='both')

    // items
    item(title="&git add ..."
        keys="[selection]"
        type='Desktop|Dir|Drive|Back.Dir|Back.Drive|File'
        where=sel.count>1
        commands{
            // (git add "@sel[i]")
            // (git add "@sel[i]")
            // (git add "@sel[i]")
            cmd-line='@CMD_GIT_ADD_SELECTION',
        }
        window='Visible'
        //
        // tip=[(for(i=0, i< sel.count, sel[i]+"\n")),TIP3,0.75]
        // tip=['/c dir "@sel.path" && (git add "@cmd_git_add_input")',TIP3,0.75]
        tip=['@CMD_GIT_ADD_SELECTION',TIP3,0.75]
        //
        image=[E122,GREY]
        image-sel=[E123,RED]
        //
        admin=(keys.shift() AND (keys.lbutton() OR keys.lbutton()))
    )
}




// =============================================================================
// [19:31] -> 19.05.2024: json ref
    {
        "type": "item",
        "title": "&Downloads",
        "image": ["E0BD", "GREEN"],
        "image-sel": ["E0BD", "HOVER"],
        "tip": "path.full('@user.downloads')",
        "commands": [
            "cmd=if(keys.rbutton(), clipboard.set(path.full('@user.downloads')))",
            "cmd=if(window.is_explorer, command.navigate('@user.downloads'))",
            "cmd=if(!window.is_explorer, ('\"@user.downloads\"'))"
        ]
    },
// =============================================================================
// [19:00] -> 19.05.2024: prettified syntax
// -
item(vis='Static' image=[E00A,DARK])
// -
item(title="Microsoft" image=[E1B5,GREY] vis='Static' sep='both')
// -
item(
    title="&Downloads"
    image=[E0BD,GREEN]
    image-sel=[E0BD,HOVER]
    tip=path.full('@user.downloads')
    commands{
        cmd=if(keys.rbutton(), clipboard.set(path.full('@user.downloads'))),
        cmd=if(window.is_explorer, command.navigate('@user.downloads')),
        cmd=if(!window.is_explorer, ('"@user.downloads"'))
    }
)

// =============================================================================
// [11:46] -> 19.05.2024: multifunctional items
menu(type='*' mode='multiple' expanded=true) {
item(title="&xxxxxxxxxxxxxxxx" image=[E0E8,SOFT] image-sel=[E0E8,HOVER] tip=path.full('@user.downloads') commands{
    cmd=if(keys.rbutton(), clipboard.set(path.full('@user.downloads'))),
    cmd=if(window.is_explorer,command.navigate('@user.downloads')),
    cmd=if(!window.is_explorer,('"@user.downloads"')),
})}

// =============================================================================
// [21:32] -> 16.05.2024:
// -> multiple commands
menu(expanded=1 type='taskbar' pos=0) {
    item(title="TEST" tip="multiple commands" image=[E0E8,SOFT] commands{
            cmd=if(keys.rbutton(), clipboard.set(path.full('@dir_3dsmax'))),
            cmd=('"@dir_3dsmax"'),
        }
    )
}
// =============================================================================
// [21:11] -> 16.05.2024:
// - startallback.nss
menu(where=@(this.count == 0) type='taskbar' image=icon.settings expanded=true)
// -
menu(type="taskbar" vis=key.shift() or key.lbutton() pos=0 title=app.name image=\uE249)
{
    item(title="config" image=\uE10A cmd='"@app.cfg"')
    item(title="manager" image=\uE0F3 admin cmd='"@app.exe"')
    item(title="directory" image=\uE0E8 cmd='"@app.dir"')
    item(title="version\t"+@app.ver vis=label col=1)
    item(title="docs" image=\uE1C4 cmd='https://nilesoft.org/docs')
    item(title="donate" image=\uE1A7 cmd='https://nilesoft.org/donate')
}
// =============================================================================
// [21:08] -> 16.05.2024:
// - @drac
// - I modified it a bit for myself now though. entries now show up with Shift
//   only, and Lbutton opens and Rbutton copies. Though I was a bit curious
//   about the static/label portion. It only had a check whether
//   winver_type==3 if not, shouldn't it copy in label/static mode or does
//   it? taskbar.nss:
$winver_type=4
menu(expanded=1 type='taskbar' pos=0)
{
    separator
    item(title='SO: '+sys.ver.name        image=icon.svg_info
        vis=If(winver_type==4, 'normal', 'disable',)    tip=["[Mayús]\xE112 + [Clic]\xE110 para mostrar Información del sistema. [Clic]\xE110 para copiar", tip.primary, 1.0]
        cmd=if(keys.shift(), 'msinfo32.exe', clipboard.set('SO: '+sys.ver.name)))
    item(title='Compilación: '+sys.ver    image=icon.svg_info
        vis=If(winver_type==4, 'normal', 'disable',)    tip=["[Mayús]\xE112 + [Clic]\xE110 para mostrar Acerca de Windows. [Clic]\xE110 para copiar", tip.primary, 1.0]
        cmd=if(keys.shift(), 'winver.exe', clipboard.set('Compilación: '+sys.ver)))
    separator
    item(vis=@key.shift() title='Reiniciar Explorador' image=[\uE093,#f00] cmd=command.restart_explorer)
}
// =============================================================================
// [21:04] -> 16.05.2024:
// Comment options
// moudey
// on Mar 12, 2023Mar 12, 2023
// Maintainer
// It works fine with me. I will check this issue.
// Mouse functionality has been added to the command execution event. Where you can run the command as administrator, for example, by right-clicking on the item
item(title="Command Prompt" admin=key.rbutton()  cmd args='/K echo  RB=@key.rbutton(), LB=@key.lbutton()')
// https://nilesoft.org/download/shell/debug.zip


// # =============================================================================


// # =============================================================================
// develop.nss
menu(mode="multiple" title='&Develop' sep=sep.bottom image=\uE26E)
{
    menu(mode="single" title='editors' image=\uE17A)
    {
        item(title='Visual Studio Code' image=[\uE272, #22A7F2] cmd='code' args='"@sel.path"')
        separator
        item(title=@str.res('regedit.exe,-16') image cmd='regedit.exe')
    }

    menu(mode="multiple" title='dotnet' image=\uE143)
    {
        item(title='run' cmd-line='/K dotnet run' image=\uE149)
        item(title='watch' cmd-line='/K dotnet watch')
        item(title='clean' image=\uE0CE cmd-line='/K dotnet clean')
        separator
        item(title='build debug' cmd-line='/K dotnet build')
        item(title='build release' cmd-line='/K dotnet build -c release /p:DebugType=None')

        menu(mode="multiple" sep="both" title='publish' image=\ue11f)
        {
            var { publish='dotnet publish -r win-x64 -c release --output publish /*/p:CopyOutputSymbolsToPublishDirectory=false*/' }
            item(title='publish sinale file' sep="after" cmd-line='/K @publish --no-self-contained /p:PublishSingleFile=true')
            item(title='framework-dependent deployment' cmd-line='/K @publish')
            item(title='framework-dependent executable' cmd-line='/K @publish --self-contained false')
            item(title='self-contained deployment' cmd-line='/K @publish --self-contained true')
            item(title='single-file' cmd-line='/K @publish /p:PublishSingleFile=true /p:PublishTrimmed=false')
            item(title='single-file-trimmed' cmd-line='/K @publish /p:PublishSingleFile=true /p:PublishTrimmed=true')
        }

        item(title='ef migrations add InitialCreate' cmd-line='/K dotnet ef migrations add InitialCreate')
        item(title='ef database update' cmd-line='/K dotnet ef database update')
        separator
        item(title='help' image=\uE136 cmd-line='/k dotnet -h')
        item(title='version' cmd-line='/k dotnet --info')
    }
}
// # =============================================================================
// coding-quick-settings.nss
item(mode="single" type='file' where=sel.file.len != sel.file.title.len title='Copy "'+@sel.file.title+'"' image=[\uE055, #4d8e4a] cmd=command.copy(sel.file.title))
item(mode="single" title='Copy "'+@sel.path+'"' tip=sel.path image=[\uE09B, #4d8e4a] cmd=command.copy(sel.path))
item(mode="single" where=@sel.parent.len>3 title='Copy "'+sel.parent+'"' image=[\uE0E7, #4d8e4a] cmd=@command.copy(sel.parent))
separator

// # =============================================================================
menu(type='dir|back.dir' mode="single" title="File List" image=icon.copy_path)
{
    item(title="Copy to Clipboard" image=inherit cmd args='/c dir "@sel.path" /a:-d /o:n | clip' window=hidden)
    item(title="Copy to Clipboard (Name)" image=inherit cmd args='/c dir "@sel.path" /b /a:-d /o:n | clip' window=hidden)
    item(title="Copy to Clipboard (Name - Size)" image=inherit
        cmd-ps=`$path = '@sel'; $list = Get-ChildItem -Path $path | Where-Object { $_.PSIsContainer -eq $false } | ForEach-Object { $fileName = $_.Name; $fileSizeBytes = $_.Length; if ($fileSizeBytes -ge 1GB) { $fileSizeGB = $fileSizeBytes / 1GB; \"$fileName - $($fileSizeGB.ToString('N2')) GB\"; } elseif ($fileSizeBytes -ge 1MB) { $fileSizeMB = $fileSizeBytes / 1MB; \"$fileName - $($fileSizeMB.ToString('N2')) MB\"; } else { $fileSizeKB = $fileSizeBytes / 1KB; \"$fileName - $($fileSizeKB.ToString('N2')) KB\"; } }; $list | Out-String | Set-Clipboard;` window=hidden)
}



// # =============================================================================
// -> increase keyboard response speed
item(title="Set &Keyboard Repeat Rate" tip="Warning: This will change the registry" image=[E0FE,LOWKEY] image-sel=[E0FE,RED] admin=1 commands{
        // -> original values
        // cmd=reg.set('HKEY_CURRENT_USER\Control Panel\Accessibility\Keyboard Response', "AutoRepeatDelay", 1000, reg.sz),
        // cmd=reg.set('HKEY_CURRENT_USER\Control Panel\Accessibility\Keyboard Response', "AutoRepeatRate", 500, reg.sz),
        // cmd=reg.set('HKEY_CURRENT_USER\Control Panel\Accessibility\Keyboard Response', "DelayBeforeAcceptance", 1000, reg.sz),
        // cmd=reg.set('HKEY_CURRENT_USER\Control Panel\Accessibility\Keyboard Response', "Flags", 126, reg.sz),

        // -> custom values
        cmd=reg.set('HKEY_CURRENT_USER\Control Panel\Keyboard', "KeyboardDelay", 0, reg.sz),
        cmd=reg.set('HKEY_CURRENT_USER\Control Panel\Keyboard', "KeyboardSpeed", 31, reg.sz),
        cmd=reg.set('HKEY_CURRENT_USER\Control Panel\Accessibility\Keyboard Response', "AutoRepeatDelay", 250, reg.sz),
        cmd=reg.set('HKEY_CURRENT_USER\Control Panel\Accessibility\Keyboard Response', "AutoRepeatRate", 16, reg.sz),
        cmd=reg.set('HKEY_CURRENT_USER\Control Panel\Accessibility\Keyboard Response', "DelayBeforeAcceptance", 0, reg.sz),
        cmd=reg.set('HKEY_CURRENT_USER\Control Panel\Accessibility\Keyboard Response', "Flags", 27, reg.sz),
    }
)

// # =============================================================================
// Alfamari — 03/23/2024 4:47 PM
// Is it possible to chain multiple different commands/args together or do I just need to create a script file for that?
// Rubic — 03/23/2024 4:49 PM
// yes, but there are different methods to do that - explain more
// https://github.com/moudey/Shell/issues/193#issuecomment-1462556890
// # =============================================================================
item(title='Estrai3' image=icon.compressed pos=indexof('Apri con',3)
    mode='single' type='file' find='.rar|.zip|.001|.7z|.arj|.bz2|.cab|.gz|.iso|.jar|.lz|.lzh|.tar|.uue|.xz|.z|.zst'
    commands{
        cmd=if(keys.shift(), path.combine(sys.prog, 'WinRar', 'WinRar.exe')) args='x -iext -ver -imon1 @sel(true)' + ' * "@sel.parent\@sel.file.title \"'  wait=1,
        cmd=if(keys.shift(), command.refresh) wait=1,
        cmd=if(keys.shift(), command.navigate('@sel.parent\@sel.file.title')),
        cmd=if(!keys.shift(), path.combine(sys.prog, 'WinRar', 'WinRar.exe')) args='x -iext -ver -imon1 @sel(true)' + ' * "D:\Download\Spacchettati\@sel.file.title \"'  wait=1,
        cmd=if(!keys.shift(), io.move(@sel, 'D:\Download\Rar\@sel.name')) wait=1,
        cmd=if(!keys.shift(), command.navigate('D:\Download\Spacchettati\@sel.file.title')),
    }
)
// # =============================================================================
// Multiple commands is a feature that is not currently documented. It must be viewed so that we have the idea of ​​working with the delay as well
item(title='multi-command' commands {
    cmd-line='/k echo FIRST COMMAND'  invoke=2000,
    cmd-line='/k echo SECOND COMMAND'  invoke=2000,
    cmd='cmd.exe' args { // MULTI-ARGUMENTS
        "/k" "echo THIRD COMMAND"
        "&"
        "echo Welcome " + @user.name
    }
    //...
})

// # =============================================================================
// 27.04.2024 - Kl.05:31: path: note
// -> if i use path.full, i need to use path.combine if i want to join multiple
//    paths. it does not work with '@dir_sync/JORN/_3D/3D Prints' if @dir_sync is
//    initialized with path.full, as an extra space is added to it.
item(title="Full path on tooltip" cmd='@sys.dir/System32/desk.cpl' tip=path.full('@sys.dir/System32/desk.cpl'))

// # =============================================================================
// 26.04.2024 - Kl.21:58: path: 'https://nilesoft.org/docs/functions/path'
item(title="Open directory on hover" cmd='@dir_dropbox' tip=path.dir.box)


// # =============================================================================
// 26.04.2024 - Kl.12:10 - COMBINE PATH
item(title="TEST" tip=path.combine(sys.prog,'7-Zip','7z.exe'))

// # =============================================================================
// # 19.04.2024 - Kl.21:39:
// Rubic — 09/05/2023 8:26 PM
// 'apostrophes' / "quotation marks"

// 'apostrophes' - marks where a value starts and ends
// "quotation marks" - means a string

// when you describe a word it can be as "string" or as 'value', but with the path it is different:
// 'C:\path\file.ext' and "C:\\path\\file.ext"

// # =============================================================================
// # 16.04.2024 - Kl.12:55:
modify(vis='Hidden' type='*' in='/' where=
    // contains
    str.contains(this.name, "NordVPN") ||
    str.contains(this.name, "VLC Media Player") ||
    str.contains(this.name, "Send a copy") ||
    str.contains(this.name, "Onedrive") ||
    str.contains(this.name, "Dropbox") ||
    // exact
    str.equals(this.name, [
            "Add to VLC Media Player",
            "Move to Dropbox",
            "Clipchamp",
            "Back up to Dropbox",
            "Move to Onedrive",
            "Enqueue in Winamp",
            "News and interests",
            "Dropbox",
            "Open archive",
            "Sync or Backup",
            "Open in Terminal",
            "Open Command Prompt Here",
            "Windows Ink Workspace"])
)

// # =============================================================================
// # 15.04.2024 - Kl.23:09:
modify(type='*' in='/New' image=[E167, ORANGE] where=!str.equals(this.name, ["Folder", "Shortcut"]) || str.equals(this.name, ["Text Document"])  vis='Hidden')

// # =============================================================================
// # 15.04.2024 - Kl.22:22: modify multiple in single command
modify(type='*' in='/New' image=[E167, ORANGE]
    where=!str.equals(this.name, [
            "Folder",
            "Shortcut",
            "Text Document",
            "AutoHotkey"
        ]
    )
    vis='Hidden'
)

// #2
modify(type='*' in='/New' vis='Hidden'
    where=!str.equals(this.name, [
            "Folder", "Shortcut", "Text Document", "AutoHotkey"
        ]
    )
)

// # =============================================================================
// # 15.04.2024 - Kl.22:52: working reference
modify(type='*' image=[E122, WHITE]
    where=process.name=="Everything64" & str.equals(this.name, [
            "Open",
            "Open Path",
            "Open With",
            "Copy Name",
            "Copy Full Path",
            "Open Path",
            "Set Run Count"
        ]
    )
)
modify(type='*' image=[E167, ORANGE]
    where=process.name=="Everything64" & str.equals(this.name, [
            "Set Run Count"
        ]
    )
)


// # =============================================================================
// # 15.04.2024 - Kl.09:44:
// moudey
// on Feb 21, 2023Feb 21, 2023
// Maintainer
// You can add these commands directly to Shell instead of adding them to the registry
menu(title='Restart Explorer' image='explorer.exe')
{
    item(title='Restart Explorer Now' cmd args='/c taskkill /f /im explorer.exe & start explorer.exe')
    item(title='Restart Explorer with Pause' cmd args='/c @@echo off & echo. & echo Stopping explorer.exe process . . . & echo. & taskkill /f /im explorer.exe & echo. & echo. & echo Waiting to start explorer.exe process when you are ready . . . & pause && start explorer.exe && exit')
}

menu(title='System File Checker' image='WmiPrvSE.exe')
{
    tem(title='Run System File Checker' admin=true cmd='PowerShell' args="-windowstyle hidden -command \"Start-Process cmd -ArgumentList '/s,/k, sfc /scannow' -Verb runAs\"")
    item(title='System File Checker log' cmd='PowerShell' args='(sls [SR] $env:windir\Logs\CBS\CBS.log -s).Line >"$env:userprofile\Desktop\sfcdetails.txt\"')
}

// # =============================================================================
// # 15.04.2024 - Kl.09:37:
// Blueberry — 02/28/2024 11:58 AM
// the code is kinda messy cuz I wasn't fully understanding the docs and I didn't take time to clean it up fully, but this is where I'd left off
menu(where=sel.count>0 mode="multiple" title='Convert' sep="both" image= \uE14A)
{
    menu(title='Audio' image=\uE1F4)
    {
        item(title='Convert (@sel.count) files to OggOpus' image=inherit cmd=
            for(i=0, i< sel.count) {
                $filePath = sel[i].directory
                $fileName= sel[i].title
                $newFileName = fileName + '.ogg'
                $newFilePath = '"@filePath\@newFileName"'

                msg('ffmpeg -i @sel[i].path.quote -c:a libopus -b:a 510k @newFilePath')
            }
        )
    }
}
// that works, it recursively prints a single theoretically functional ffmpeg command per selected file
// my issue is, I cannot figure out how to get it to actually run that command
// in terms of the regex not working example, I'll give you that code snippet in an example testing context
item(title='Test Regex (remove file extension from full path)' image=inherit cmd=
    for(i=0, i< sel.count) {
        msg(regex.replace(sel[i], '\.[^/.]+$', ''))
    }
)

I did find a workaround, so that specific example isn't the best case to demonstrate but the regex should still work nevertheless

// # =============================================================================
// # 15.04.2024 - Kl.09:36: here are some experimental and undocumented functions
$path1 = 'C:\Te st.exe'
item(title=path.quote_spaces(path1))
item(title=path.remove_extension(path1))
item(title=path.rename_extension(path1, '.dll'))
item(title=path.add_extension('C:\Test', '.ext'))

// # =============================================================================
// # 13.04.2024 - Kl.09:50: RubicBG commented on Jan 19
// https://github.com/moudey/Shell/issues/364#issuecomment-1785006172
$sendto_items=path.files(user.sendto, '*.lnk')
menu(title='Se&nd to' type='file|dir' mode='multiple' where=len(sendto_items)>0 pos=indexof('Send to', 1)  image=icon.send_to){
    item(title='Compressed (zipped) folder' image='@sys.bin\imageres.dll, 165' vis='disable' )
    item(title='Desktop (create shortcut)'  image='@sys.bin\imageres.dll, 105'
        cmd=for(i=0, i<sel.count) { path.lnk.create(user.desktop+'\'+path.title(sel.get(i))+if(!keys.shift(), ' - Shortcut')+'.lnk', sel.get(i)) } & command.refresh)
    item(title='Documents (create copy)'    image='@sys.bin\imageres.dll, 189' where=!str.contains(sel.path, user.documents)
        cmd=for(i=0, i<sel.count) { io.copy(sel.get(i), '@user.documents'+"\\"+path.name(sel.get(i)), 16 | 4) } & command.refresh)
    item(title='Mail recipient'             image='@sys.bin\sendmail.dll'
        //cmd='mailto:support@"@"nilesoft.org?subject=Аttachments are via Nilesoft Shell&attach=file:///D:/TEST.txt&body="Test"')
        cmd='mailto:support@"@"nilesoft.org?subject=Attachments are via Nilesoft Shell&body="Test"&attachment=sel(true)')
    separator()
    item(title=path.title(sendto_items[0])                  where=len(sendto_items)>0 and path.isexe(path.lnk(user.sendto+'\'+sendto_items[0]))
        image=path.lnk(user.sendto+'\'+sendto_items[0])     cmd=user.sendto+'\'+sendto_items[0]  args=sel(true))
    item(title=path.title(sendto_items[1])                  where=len(sendto_items)>1 and path.isexe(path.lnk(user.sendto+'\'+sendto_items[1]))
        image=path.lnk(user.sendto+'\'+sendto_items[1])     cmd=user.sendto+'\'+sendto_items[1]  args=sel(true))
    item(title=path.title(sendto_items[2])                  where=len(sendto_items)>2 and path.isexe(path.lnk(user.sendto+'\'+sendto_items[2]))
        image=path.lnk(user.sendto+'\'+sendto_items[2])     cmd=user.sendto+'\'+sendto_items[1]  args=sel(true))
    item(title=path.title(sendto_items[3])                  where=len(sendto_items)>3 and path.isexe(path.lnk(user.sendto+'\'+sendto_items[3]))
        image=path.lnk(user.sendto+'\'+sendto_items[3])     cmd=user.sendto+'\'+sendto_items[3]  args=sel(true))
    item(title=path.title(sendto_items[4])                  where=len(sendto_items)>4 and path.isexe(path.lnk(user.sendto+'\'+sendto_items[4]))
        image=path.lnk(user.sendto+'\'+sendto_items[4])     cmd=user.sendto+'\'+sendto_items[4]  args=sel(true))
    item(title=path.title(sendto_items[5])                  where=len(sendto_items)>5 and path.isexe(path.lnk(user.sendto+'\'+sendto_items[5]))
        image=path.lnk(user.sendto+'\'+sendto_items[5])     cmd=user.sendto+'\'+sendto_items[5]  args=sel(true))
    item(title=path.title(sendto_items[6])                  where=len(sendto_items)>6 and path.isexe(path.lnk(user.sendto+'\'+sendto_items[6]))
        image=path.lnk(user.sendto+'\'+sendto_items[6])     cmd=user.sendto+'\'+sendto_items[6]  args=sel(true))
    item(title=path.title(sendto_items[7])                  where=len(sendto_items)>7 and path.isexe(path.lnk(user.sendto+'\'+sendto_items[7]))
        image=path.lnk(user.sendto+'\'+sendto_items[7])     cmd=user.sendto+'\'+sendto_items[7]  args=sel(true))
    item(title=path.title(sendto_items[8])                  where=len(sendto_items)>8 and path.isexe(path.lnk(user.sendto+'\'+sendto_items[8]))
        image=path.lnk(user.sendto+'\'+sendto_items[8])     cmd=user.sendto+'\'+sendto_items[8]  args=sel(true))
    item(title=path.title(sendto_items[9])                  where=len(sendto_items)>9 and path.isexe(path.lnk(user.sendto+'\'+sendto_items[9]))
        image=path.lnk(user.sendto+'\'+sendto_items[9])     cmd=user.sendto+'\'+sendto_items[9]  args=sel(true))
    item(title=path.title(sendto_items[10])                 where=len(sendto_items)>10 and path.isexe(path.lnk(user.sendto+'\'+sendto_items[10]))
        image=path.lnk(user.sendto+'\'+sendto_items[10])    cmd=user.sendto+'\'+sendto_items[10] args=sel(true))
    item(title=path.title(sendto_items[11])                 where=len(sendto_items)>11 and path.isexe(path.lnk(user.sendto+'\'+sendto_items[11]))
        image=path.lnk(user.sendto+'\'+sendto_items[11])    cmd=user.sendto+'\'+sendto_items[11] args=sel(true))
    separator()
    item(title=path.title(sendto_items[0])                  where=len(sendto_items)>0 and path.isdirectory(path.lnk(user.sendto+'\'+sendto_items[0]))
        image='explorer.exe' keys='SHIFT move'              tip=path.lnk(user.sendto+'\'+sendto_items[0])
        cmd=for(i=0, i<sel.count) { if(keys.shift(),
            io.move(sel[i], path.lnk(user.sendto+'\'+sendto_items[0]) + '\' + path.name(sel.get(i))),
            io.copy(sel[i], path.lnk(user.sendto+'\'+sendto_items[0]) + '\' + path.name(sel.get(i)))) } & command.refresh() & msg('Jobs done!'))
    item(title=path.title(sendto_items[1])                  where=len(sendto_items)>1 and path.isdirectory(path.lnk(user.sendto+'\'+sendto_items[1]))
        image='explorer.exe' keys='SHIFT move'              tip=path.lnk(user.sendto+'\'+sendto_items[1])
        cmd=for(i=0, i<sel.count) { if(keys.shift(),
            io.move(sel[i], path.lnk(user.sendto+'\'+sendto_items[1]) + '\' + path.name(sel.get(i))),
            io.copy(sel[i], path.lnk(user.sendto+'\'+sendto_items[1]) + '\' + path.name(sel.get(i)))) } & command.refresh() & msg('Jobs done!'))
    item(title=path.title(sendto_items[2])                  where=len(sendto_items)>2 and path.isdirectory(path.lnk(user.sendto+'\'+sendto_items[2]))
        image='explorer.exe' keys='SHIFT move'              tip=path.lnk(user.sendto+'\'+sendto_items[2])
        cmd=for(i=0, i<sel.count) { if(keys.shift(),
            io.move(sel[i], path.lnk(user.sendto+'\'+sendto_items[2]) + '\' + path.name(sel.get(i))),
            io.copy(sel[i], path.lnk(user.sendto+'\'+sendto_items[2]) + '\' + path.name(sel.get(i)))) } & command.refresh() & msg('Jobs done!'))
    item(title=path.title(sendto_items[3])                  where=len(sendto_items)>3 and path.isdirectory(path.lnk(user.sendto+'\'+sendto_items[3]))
        image='explorer.exe' keys='SHIFT move'              tip=path.lnk(user.sendto+'\'+sendto_items[3])
        cmd=for(i=0, i<sel.count) { if(keys.shift(),
            io.move(sel[i], path.lnk(user.sendto+'\'+sendto_items[3]) + '\' + path.name(sel.get(i))),
            io.copy(sel[i], path.lnk(user.sendto+'\'+sendto_items[3]) + '\' + path.name(sel.get(i)))) } & command.refresh() & msg('Jobs done!'))
    item(title=path.title(sendto_items[4])                  where=len(sendto_items)>4 and path.isdirectory(path.lnk(user.sendto+'\'+sendto_items[4]))
        image='explorer.exe' keys='SHIFT move'              tip=path.lnk(user.sendto+'\'+sendto_items[4])
        cmd=for(i=0, i<sel.count) { if(keys.shift(),
            io.move(sel[i], path.lnk(user.sendto+'\'+sendto_items[4]) + '\' + path.name(sel.get(i))),
            io.copy(sel[i], path.lnk(user.sendto+'\'+sendto_items[4]) + '\' + path.name(sel.get(i)))) } & command.refresh() & msg('Jobs done!'))
    item(title=path.title(sendto_items[5])                  where=len(sendto_items)>5 and path.isdirectory(path.lnk(user.sendto+'\'+sendto_items[5]))
        image='explorer.exe' keys='SHIFT move'              tip=path.lnk(user.sendto+'\'+sendto_items[5])
        cmd=for(i=0, i<sel.count) { if(keys.shift(),
            io.move(sel[i], path.lnk(user.sendto+'\'+sendto_items[5]) + '\' + path.name(sel.get(i))),
            io.copy(sel[i], path.lnk(user.sendto+'\'+sendto_items[5]) + '\' + path.name(sel.get(i)))) } & command.refresh() & msg('Jobs done!'))
    item(title=path.title(sendto_items[6])                  where=len(sendto_items)>6 and path.isdirectory(path.lnk(user.sendto+'\'+sendto_items[6]))
        image='explorer.exe' keys='SHIFT move'              tip=path.lnk(user.sendto+'\'+sendto_items[6])
        cmd=for(i=0, i<sel.count) { if(keys.shift(),
            io.move(sel[i], path.lnk(user.sendto+'\'+sendto_items[6]) + '\' + path.name(sel.get(i))),
            io.copy(sel[i], path.lnk(user.sendto+'\'+sendto_items[6]) + '\' + path.name(sel.get(i)))) } & command.refresh() & msg('Jobs done!'))
    item(title=path.title(sendto_items[7])                  where=len(sendto_items)>7 and path.isdirectory(path.lnk(user.sendto+'\'+sendto_items[7]))
        image='explorer.exe' keys='SHIFT move'              tip=path.lnk(user.sendto+'\'+sendto_items[7])
        cmd=for(i=0, i<sel.count) { if(keys.shift(),
            io.move(sel[i], path.lnk(user.sendto+'\'+sendto_items[7]) + '\' + path.name(sel.get(i))),
            io.copy(sel[i], path.lnk(user.sendto+'\'+sendto_items[7]) + '\' + path.name(sel.get(i)))) } & command.refresh() & msg('Jobs done!'))
    item(title=path.title(sendto_items[8])                  where=len(sendto_items)>8 and path.isdirectory(path.lnk(user.sendto+'\'+sendto_items[8]))
        image='explorer.exe' keys='SHIFT move'              tip=path.lnk(user.sendto+'\'+sendto_items[8])
        cmd=for(i=0, i<sel.count) { if(keys.shift(),
            io.move(sel[i], path.lnk(user.sendto+'\'+sendto_items[8]) + '\' + path.name(sel.get(i))),
            io.copy(sel[i], path.lnk(user.sendto+'\'+sendto_items[8]) + '\' + path.name(sel.get(i)))) } & command.refresh() & msg('Jobs done!'))
    item(title=path.title(sendto_items[9])                  where=len(sendto_items)>9 and path.isdirectory(path.lnk(user.sendto+'\'+sendto_items[9]))
        image='explorer.exe' keys='SHIFT move'              tip=path.lnk(user.sendto+'\'+sendto_items[9])
        cmd=for(i=0, i<sel.count) { if(keys.shift(),
            io.move(sel[i], path.lnk(user.sendto+'\'+sendto_items[9]) + '\' + path.name(sel.get(i))),
            io.copy(sel[i], path.lnk(user.sendto+'\'+sendto_items[9]) + '\' + path.name(sel.get(i)))) } & command.refresh() & msg('Jobs done!'))
    item(title=path.title(sendto_items[10])                 where=len(sendto_items)>10 and path.isdirectory(path.lnk(user.sendto+'\'+sendto_items[10]))
        image='explorer.exe' keys='SHIFT move'              tip=path.lnk(user.sendto+'\'+sendto_items[10])
        cmd=for(i=0, i<sel.count) { if(keys.shift(),
            io.move(sel[i], path.lnk(user.sendto+'\'+sendto_items[10]) + '\' + path.name(sel.get(i))),
            io.copy(sel[i], path.lnk(user.sendto+'\'+sendto_items[10]) + '\' + path.name(sel.get(i)))) } & command.refresh() & msg('Jobs done!'))
    item(title=path.title(sendto_items[11])                 where=len(sendto_items)>11 and path.isdirectory(path.lnk(user.sendto+'\'+sendto_items[11]))
        image='explorer.exe' keys='SHIFT move'              tip=path.lnk(user.sendto+'\'+sendto_items[11])
        cmd=for(i=0, i<sel.count) { if(keys.shift(),
            io.move(sel[i], path.lnk(user.sendto+'\'+sendto_items[11]) + '\' + path.name(sel.get(i))),
            io.copy(sel[i], path.lnk(user.sendto+'\'+sendto_items[11]) + '\' + path.name(sel.get(i)))) } & command.refresh() & msg('Jobs done!'))
}


// # =============================================================================
// # 13.04.2024 - Kl.00:02:
// 👾 𝔍𝒵eͥяsͣcͫḣε — 03/07/2024 6:39 AM
// The only thing is I don't know how to add it to the right-click context for the taskbar itself. Only folder menu.
// Unlock/Unlock Taskbar Menu Item
menu(title = 'Taskbar locking' image=\uE023 type='taskbar' position='bottom')
{
  item(title='Unlocked' image=\uE19B cmd=reg.set('HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\Advanced', "TaskbarSizeMove", 1, reg.dword))
  item(title='Locked' image=\uE19A cmd=reg.set('HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\Advanced', "TaskbarSizeMove", 0, reg.dword))
}



// # =============================================================================
// # 12.04.2024 - Kl.23:44:
// Rubic
// OP
//  — 08/21/2023 1:44 PM
// shows the Windows version in the context menu of the taskbar. Three versions:
// as label (file 'winver.label.nss')
// as commands - opens information panels (file 'winver.cmd.nss')
// as commands - copies information to clipboard / with Shift key opens information panels (file 'winver.clip.nss') (version 1.8.37 /debug version 37)
// source of the idea: https://github.com/moudey/Shell/discussions/315#discussioncomment-6771768
$svg_winver= '<svg width="100" height="100" viewBox="-160 0 512 512">
        <path style="fill:@image.color2" d="M20 424.229h20V279.771H20c-11.046 0-20-8.954-20-20V212c0-11.046 8.954-20 20-20h112c11.046 0 20 8.954 20 20v212.229h20c11.046 0 20 8.954 20 20V492c0 11.046-8.954 20-20 20H20c-11.046 0-20-8.954-20-20v-47.771c0-11.046 8.954-20 20-20zM96 0C56.235 0 24 32.235 24 72s32.235 72 72 72 72-32.235 72-72S135.764 0 96 0z"/>
    </svg>'
// minimum requirements: version 1.8.37 (debug version 37) as commands: copy to clipboard / as commands + key SHIFT: open info
menu(expanded=1 type='taskbar' pos=0) {
    separator
    item(title=sys.ver.name image=svg_winver cmd=if(keys.shift(), 'msinfo32.exe', clipboard.set(sys.ver.name)))
    item(title='OS build: '+sys.ver image=svg_winver cmd=if(keys.shift(), 'winver.exe', clipboard.set(sys.ver)))
    separator
}
$svg_winver= '<svg width="100" height="100" viewBox="-160 0 512 512">
        <path style="fill:@image.color2" d="M20 424.229h20V279.771H20c-11.046 0-20-8.954-20-20V212c0-11.046 8.954-20 20-20h112c11.046 0 20 8.954 20 20v212.229h20c11.046 0 20 8.954 20 20V492c0 11.046-8.954 20-20 20H20c-11.046 0-20-8.954-20-20v-47.771c0-11.046 8.954-20 20-20zM96 0C56.235 0 24 32.235 24 72s32.235 72 72 72 72-32.235 72-72S135.764 0 96 0z"/>
    </svg>'
// minimum requirements: version 1.8.1 as commands: open info
menu(expanded=1 type='taskbar' pos=0) {
    separator
    item(title=sys.ver.name image=svg_winver cmd='msinfo32.exe')
    item(title='OS build: '+sys.ver image=svg_winver cmd='winver.exe')
    separator
}
// minimum requirements: version 1.8.1 as label
menu(expanded=1 type='taskbar' pos=0) {
    separator
    item(title=sys.ver.name vis='label')
    item(title='OS build: '+sys.ver vis='label')
    separator
}

// # =============================================================================
// # 12.04.2024 - Kl.15:43:
$dir_all_tasks   = 'shell:::{ed7ba470-8e54-465e-825c-99712043e01c}'

// # =============================================================================
// # 12.04.2024 - Kl.15:22: common directories
item(title="user.&home" cmd='@user.home')
item(title="user.&appdata" cmd='@user.appdata')
item(title="user.&contacts" cmd='@user.contacts')
item(title="user.&desktop" cmd='@user.desktop')
item(title="user.&directory (user.dir)" cmd='@user.directory (user.dir)')
item(title="user.&documents" cmd='@user.documents')
item(title="user.&documentslibrary" cmd='@user.documentslibrary')
item(title="user.&downloads" cmd='@user.downloads')
item(title="user.&favorites" cmd='@user.favorites')
item(title="user.&libraries" cmd='@user.libraries')
item(title="user.&localappdata" cmd='@user.localappdata')
item(title="user.&music" cmd='@user.music')
item(title="user.&personal" cmd='@user.personal')
item(title="user.&pictures" cmd='@user.pictures')
item(title="user.&profile" cmd='@user.profile')
item(title="user.&quicklaunch" cmd='@user.quicklaunch')
item(title="user.&sendto" cmd='@user.sendto')
item(title="user.&startmenu" cmd='@user.startmenu')
item(title="user.startmenu/Programs/Startup" cmd='@user.startmenu/Programs/Startup')
item(title="user.&temp" cmd='@user.temp')
item(title="user.&templates" cmd='@user.templates')
item(title="user.&videos" cmd='@user.videos')

// =============================================================================
// 10.04.2024 - Kl.18:21: find id of menuitems
modify(find='Open with' tip=this.id)
//
modify(type='*' in='/New' where=this.id tip=this.id)


// =============================================================================
// 10.04.2024 - Kl.18:08:
// remove everything except query (in the default "/New" menu)
remove(type='*' in='/New' where=!regex.match(this.name, "^Folder$|^Shortcut$|.*Text*."))

// # =============================================================================
// # 10.04.2024 - Kl.17:24:
// moudey — 07/03/2023 6:23 PM
// A new function "key.send" that enables you to send one or more keys to the current window.
// It allows customizing multiple items to interact with the explorer.
// example:
key.send(key.f5)       // Refresh
key.send(key.ctrl,'n') // Open a New Window with the Same Folder.
key.send(key.ctrl,'t') // Open a new tab and switch to it.
key.send(key.shift,'d') // Close a File Explorer Window.
key.send(key.shift, key.delete) // Delete permanently.

// =============================================================================
// 09.04.2024 - Kl.17:43:
menu(title="&ORG" type='~Taskbar|~Desktop' pos=0 sep='Both' image=[E1B8, SOFT] tip=this.pos) {
    // modify(type='~Taskbar|~Desktop' where=this.id tip=this.pos menu="ORG" )
    modify(type='~Taskbar|~Desktop' where=this.id tip=this.pos cmd=msg('@this.pos') menu="ORG" )
}

// menu(title="&ORG" type='~Taskbar|~Desktop' mode='multiple' /* pos=0 */ sep='Both' image=[E1B8, SOFT] tip=this.pos) {
//     item(title="&Command Prompt" keys=".exe" image=[E0AC, GREY] image-sel=[E0AC, HOVER] tip=this.pos cmd=msg('@this.pos'))
//     // modify(type='~Taskbar|~Desktop' where=this.id tip=this.pos cmd=msg('@this.pos') )
// }



// =============================================================================
// 08.04.2024 - Kl.21:22:
// 👾 𝔍𝒵eͥяsͣcͫḣε
// OP
//  — Today at 9:40 AM
// Create Directory Index (.txt)
item(title='Create Directory Index (.txt)' cmd='powershell' args='-NoExit $sourceDirectory = \"@sel("\"",",")\"; Get-ChildItem -LiteralPath $sourceDirectory -Recurse | Select-Object FullName | Out-File -LiteralPath \"$sourceDirectory\DirectoryIndex.txt\"' admin window=show image=image.glyph(\uE1DF,segoe_icon_size))

// =============================================================================
// 08.04.2024 - Kl.21:23:
// 👾 𝔍𝒵eͥяsͣcͫḣε
// OP
//  — 04/05/2024 6:30 AM
// Powershell Sort Files by Extension to Directories
item(title='Sort Directory by File Extensions' cmd='powershell' args='-NoExit $output = New-Object System.Collections.ArrayList; $sourceDirectory = \"@sel("\"",",")\"; Get-Item -Path $sourceDirectory; \"`n\"; $fileExtensions = (Get-ChildItem -LiteralPath $sourceDirectory -File | Select-Object -ExpandProperty Extension -Unique); foreach ($extension in $fileExtensions) { Write-Host \"File Extension: $extension\"; $folderPath = Join-Path -Path $sourceDirectory -ChildPath $extension.TrimStart(\".\"); Write-Host \"Folder Path: $folderPath\"; $output += \" \" + (New-Item -ItemType Directory -Path $folderPath -ErrorAction SilentlyContinue)}; foreach ($file in (Get-ChildItem -LiteralPath $sourceDirectory -File)) { $extension = $file.Extension.TrimStart(\".\"); $destinationFolder = Join-Path -Path $sourceDirectory -ChildPath $extension; Move-Item -LiteralPath $file.FullName -Destination $destinationFolder -Force }; Write-Host \"Press any key to continue . . .\"; $null = $Host.UI.RawUI.ReadKey(\"NoEcho,IncludeKeyDown\");' admin window=show image=image.glyph(\uE15D,segoe_icon_size))


// =============================================================================
// 08.04.2024 - Kl.21:23:
// MauWinGold
// OP
//  — 04/06/2024 7:54 AM
// Hash files using Powershell
menu(title="Hash" mode="single" type='file' image= \uE10C) {
        $generate_hash= 'Get-FileHash -Path \""@sel.path"\" -Algorithm "@this.title" | ForEach-Object { $_.Hash } | Set-Clipboard | Add-Type -AssemblyName System.Windows.Forms; $global:balmsg = New-Object System.Windows.Forms.NotifyIcon; $path = (Get-Process -id $pid).Path; $balmsg.Icon = [System.Drawing.Icon]::ExtractAssociatedIcon($path); $balmsg.BalloonTipIcon = [System.Windows.Forms.ToolTipIcon]::Info; $balmsg.BalloonTipText = \"Hash generated successfully!\"; $balmsg.BalloonTipTitle = \"Hash Tools\"; $balmsg.Visible = $true; $balmsg.ShowBalloonTip(20000)'
        $compare_hash= 'Add-Type -AssemblyName System.Windows.Forms; $global:balmsg = New-Object System.Windows.Forms.NotifyIcon; $path = (Get-Process -id $pid).Path; $balmsg.Icon = [System.Drawing.Icon]::ExtractAssociatedIcon($path); $balmsg.BalloonTipTitle = \"Hash Tools\"; $balmsg.Visible = $true; if ((Get-FileHash \""@sel.path"\" -Algorithm "@this.title").Hash -eq \""@input.result"\") { $balmsg.BalloonTipIcon = [System.Windows.Forms.ToolTipIcon]::Info; $balmsg.BalloonTipText = \"Input hash is VALID!\"; $balmsg.ShowBalloonTip(20000) } else { $balmsg.BalloonTipIcon = [System.Windows.Forms.ToolTipIcon]::Error; $balmsg.BalloonTipText = \"Hashes do NOT match\"; $balmsg.ShowBalloonTip(20000)}'
        item(title="Generate" image= \uE1E2 sep="after")
            item(title="MD5" cmd='powershell.exe' args='"@generate_hash"' window=hidden)
            item(title="SHA256" cmd='powershell.exe' args='"@generate_hash"' window=hidden)
        item(title="Compare" image= \uE111 sep="both")
            item(mode="single" type="file" title="MD5" cmd=if(input('Compare checksum @this.title', 'Input checksum'), 'powershell.exe') args='"@compare_hash"' window=hidden)
            item(mode="single" type="file" title="SHA256" cmd=if(input('Compare checksum @this.title', 'Input checksum'), 'powershell.exe') args='"@compare_hash"' window=hidden)
    }


// # =============================================================================
// # 30.03.2024 - Kl.23:54:
// Blah Blah Blaze — 03/22/2024 9:59 AM
For anyone having trouble with icons, I think I figured it all out. image.fluent and image.mdl mess up the sizing, but using image.glyph (or the shorthand version with brackets... image=[]) and specifying fluent/mdl as the font argument gives you the correct size. So define these somewhere (I put it in theme.nss):
$fluent = "Segoe Fluent Icons"
$mdl = "Segoe MDL2 Assets"

Then define item images like this:
item(title='Item1' image=[[\uED28],fluent] )
item(title='Item2' image=[[\uE710],[\uF12F,#ffc107],fluent] )

// # =============================================================================
// # 21.03.2024 - Kl.11:57:
item(type="taskbar" title="&Lock all taskbars" cmd=window.command(425))


// # =============================================================================
// # 21.03.2024 - Kl.11:56:
// 4ianpyn — 01/03/2024 12:51 PM
// I have similar idea to utilize Windows Explorer shell to move/copy file(s) to specific location
// I planned to make it using Python script that calls shell move/copy and play with cmd=, args= and shift/ctrl switch
// Here is what I get currently:
import ctypes
import os
from ctypes import wintypes

# https://stackoverflow.com/a/55263840


class _SHFILEOPSTRUCTW(ctypes.Structure):
    _fields_ = [
        ("hwnd", wintypes.HWND),
        ("wFunc", wintypes.UINT),
        ("pFrom", wintypes.LPCWSTR),
        ("pTo", wintypes.LPCWSTR),
        ("fFlags", ctypes.c_uint),
        ("fAnyOperationsAborted", wintypes.BOOL),
        ("hNameMappings", ctypes.c_uint),
        ("lpszProgressTitle", wintypes.LPCWSTR),
    ]


def win_shell_file_handler (src, dst, action):
    """
    :param str src: Source path to copy from. Must exist!
    :param str dst: Destination path to copy to. Will be created on demand.
    :param int action: 1 for move, 2 for copy, 3 for delete
    :return: Success of the operation. False means it was aborted!
    :rtype: bool
    """
    if not os.path.exists(src):
        print('No such source "%s"' % src)
        return False

    src_buffer = ctypes.create_unicode_buffer(src, len(src) + 2)
    dst_buffer = ctypes.create_unicode_buffer(dst, len(dst) + 2)

    fileop = _SHFILEOPSTRUCTW()
    fileop.hwnd = 0

    # Switch on action
    if action == 1:
        fileop.wFunc = 1 # FO_MOVE
    elif action == 2:
        fileop.wFunc = 2 # FO_COPY
    elif action == 3:
        fileop.wFunc = 3 # FO_DELETE
    else:
        raise ValueError("Invalid action")

    fileop.pFrom = wintypes.LPCWSTR(ctypes.addressof(src_buffer))
    fileop.pTo = wintypes.LPCWSTR(ctypes.addressof(dst_buffer))
    fileop.fFlags = 512  # FOF_NOCONFIRMMKDIR
    fileop.fAnyOperationsAborted = 0
    fileop.hNameMappings = 0
    fileop.lpszProgressTitle = None

    result = ctypes.windll.shell32.SHFileOperationW(ctypes.byref(fileop))
    return not result


// # =============================================================================
// # 21.03.2024 - Kl.11:55:
menu(title='Power Schemes #1' image=image.glyph(\uE271, #00FF00)) {
    $ps_default = reg.get('HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\Power\User\PowerSchemes', 'ActivePowerScheme')
    item(title='Balanced'            tip='Automatically balances performance with energy consumption on capable hardware.' sep='after'
    image=image.mdl(\uF5F7)            vis=if(ps_default== '381b4222-f694-41f0-9685-ff5bb260df2e', 'disable') cmd-line='/c powercfg /setactive 381b4222-f694-41f0-9685-ff5bb260df2e' window=hidden)
    item(title='High Performance'    tip='Favors performance, but may use more energy.'
    image=image.mdl(\uF5FB)            vis=if(ps_default== '8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c', 'disable') cmd-line='/c powercfg /setactive 8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c' window=hidden)
    item(title='Power Saver'        tip='Saves energy by reducing your computer performance where possible.'
    image=image.mdl(\uF5F2)            vis=if(ps_default== 'a1841308-3541-4fab-bc81-f71556f20b4a', 'disable') cmd-line='/c powercfg /setactive a1841308-3541-4fab-bc81-f71556f20b4a' window=hidden)
}


// # =============================================================================
// # 21.03.2024 - Kl.00:58:
menu(where=sel.count>0 mode="multiple" title='Convert' sep="both" image= \uE14A)
{
    menu(title='Audio' image=\uE1F4)
    {
        item(title='Convert (@sel.count) files to OggOpus' image=inherit cmd=
            for(i=0, i< sel.count) {
                $filePath = sel[i].directory
                $fileName= sel[i].title
                $newFileName = fileName + '.ogg'
                $newFilePath = '"@filePath\@newFileName"'

                msg('ffmpeg -i @sel[i].path.quote -c:a libopus -b:a 510k @newFilePath')
            }
        )
    }
}


// =============================================================================
// Jerry Master Fixer — 10/07/2023 2:14 PM
// - I've got a problem where a menu item will move in some places but not
//   others:
modify(mode='multiple' type='dir' where=window.name=='Progman'&&this.id==id.open sep=after pos=0)
modify(mode='multiple' type='dir' where=window.name=='CabinetWClass'&&this.id==id.open sep=after pos=0)
modify(mode='multiple' type='dir' where=window.name=='CabinetWClass' find='open in new tab' pos=indexof('open', 1))
modify(mode='multiple' type='dir' where=window.name=='CabinetWClass' find='open in new window' sep=after pos=indexof('open in new window', 1))
modify(type='dir' where=window.name=='Progman' find='7-zip' sep=after pos=indexof('open', 1))
modify(mode='multiple' type='dir' where=window.name=='CabinetWClass' find='7-zip' sep=after pos=indexof('open in new window', 1))
modify(mode='multiple' type='dir' find='cut' pos=indexof('7-zip', 1))
modify(mode='multiple' type='dir' find='copy' sep=after pos=indexof('cut', 1))
modify(mode='multiple' type='dir' find='create shortcut' sep=after pos=indexof('copy', 1))
modify(mode='multiple' type='dir' find='rename' pos=indexof('create shortcut', 1))
modify(mode='multiple' type='dir' find='delete' sep=after pos=indexof('rename', 1))
modify(mode='multiple' type='dir' find='properties' sep=before pos=indexof('delete', 1))
modify(type='dir' find='send to' vis=vis.remove)
//
// Blah Blah Blaze — 10/07/2023 2:35 PM
// - But I found this hack, you can insert all new menus with no items, have
//   your existing items positioned below them, then they'll appear in the
//   right order. I basically did this with every one of my menuitems and it's
//   the only way I've found to get a guaranteed ordering
modify(mode='multiple' type='dir' where=window.name=='Progman'&&this.id==id.open sep=after pos=0)
modify(mode='multiple' type='dir' where=window.name=='CabinetWClass'&&this.id==id.open sep=after pos=0)
modify(mode='multiple' type='dir' where=window.name=='CabinetWClass' find='open in new tab' pos=indexof('open', 1))
modify(mode='multiple' type='dir' where=window.name=='CabinetWClass' find='open in new window' sep=after pos=indexof('open in new window', 1))
modify(type='dir' where=window.name=='Progman' find='7-zip' sep=after pos=indexof('open', 1))
modify(mode='multiple' type='dir' where=window.name=='CabinetWClass' find='7-zip' sep=after pos=indexof('open in new window', 1))
menu(title=id.cut mode='multiple' pos=indexof('7-zip', 1)){}
    modify(mode='multiple' type='dir' where=this.id==id.cut pos=indexof(this.id))
menu(title=id.copy mode='multiple'){}
    modify(mode='multiple' type='dir' where=this.id==id.copy sep=after pos=indexof(this.id))
menu(title=id.create_shortcut mode='multiple'){}
    modify(mode='multiple' type='dir' where=this.id==id.create_shortcut sep=after pos=indexof(this.id))
menu(title=id.rename mode='multiple'){}
    modify(mode='multiple' type='dir' where=this.id==id.rename pos=indexof(this.id))
menu(title=id.delete mode='multiple'){}
    modify(mode='multiple' type='dir' where=this.id==id.delete sep=after pos=indexof(this.id))
menu(title=id.properties mode='multiple'){}
    modify(mode='multiple' type='dir' where=this.id==id.properties sep=before pos=indexof(this.id))
// =============================================================================
// create menu within a menu
// 1
menu(title='Test 1' parent='new') {
  item(vis=0) }
// 2
menu(title='Test 2' menu='new') {
  item(vis=0) }


// =============================================================================
item(title = "hello_world" cmd = msg(sys.dir))
item(title = "hello_world" cmd = msg('@app.dir\custom\_5_create_menus\menu_New.nss'))



// =============================================================================
// Seamless single/multi regex rename using PowerToys (overrides default)
menu(title=id.rename mode='multiple'){}
    modify(where=sel.count==1&&this.id==id.rename pos=indexof(this.id))
    remove(where=sel.count==1 find='PowerRename')
    modify(where=sel.count>1 find='PowerRename' pos=indexof(id.rename) title='Rename' image=icon.rename)
    remove(where=sel.count>1&&this.id==id.rename)


// =============================================================================
// combined glyphs
image.fluent([\uE002, \uE001])
image.fluent([[\uE002],[\uE001]])
image.fluent([[\uE002, #000],[\uE001, #fff]])
image.fluent([[\uE002, #000],[\uE001, #fff]], size)

// =============================================================================
// for loop
for(var=0, var<10)
{
    msg(var)
}

// =============================================================================
// A convenient way to create symlink, hardlink
menu(title="Create mklink" image=icon.show_hidden_files)
    {
        item(
            type='file|dir|back.dir|drive'
            title="Symlink"
            cmd admin
            args=if(
                input("Create a symlink", "Link Folder Path"),
                '/c mklink @if(sel.type==1,null,"/d") "@input.result\@sel.file.name" "@sel.path"'
            )
        )
        item(
            type='file|dir|back.dir|drive'
            title="Hardlink"
            cmd admin
            args=if(
                input("Create a hardlink", "Link Folder Path"),
                '/c mklink /h @if(sel.type==1,null,"/d") "@input.result\@sel.file.name" "@sel.path"'
            )
        )
        item(
            type='dir|back.dir|drive'
            title="Junction"
            cmd admin
            args=if(
                input("Create a symlink", "Link Folder Path"),
                '/c mklink /j @if(sel.type==1,null,"/d") "@input.result\@sel.file.name" "@sel.path"'
            )
        )
    }
// =============================================================================
menu(type='*' where=(sel.count or wnd.is_taskbar or wnd.is_edit) title=title.terminal sep='top' image=icon.run_with_powershell)
{
    var{tip_run_admin=["\xE1A7 Press SHIFT key to run " + this.title + " as administrator", tip.warning, 1.0]}
    item(title=title.command_prompt tip=tip_run_admin admin=key.shift() image cmd='cmd.exe' args='/K TITLE Command Prompt &ver& PUSHD "@sel.dir"')
    item(title=title.windows_powershell admin=@key.shift() tip=tip_run_admin image cmd='powershell.exe' args='-noexit -command Set-Location -Path "@sel.dir\."')
    item(where=sys.ver.major >= 10 title=title.Windows_Terminal tip=tip_run_admin admin=key.shift() image='@package.path("WindowsTerminal")\WindowsTerminal.exe' cmd='wt.exe' arg='-d "@sel.path\."')
}

// =============================================================================
// parent="New" - dukker opp under built-in "New" i explorer
$base_template_folder='E:\Utils\ShellNew\'
menu(title="Kd New" mode="multiple" parent="New" sep=after pos=top) {
  menu(title="XML" mode="multiple") {
    $sub_folder="XML"
    item(title='Blank XML Document' command='robocopy' args='"@base_template_folder@sub_folder" @sel.workdir "Empty XML File.xml"' window=hidden)
    item(title='RimWorld XML Document' command='robocopy' args='"@base_template_folder@sub_folder" @sel.workdir "RimWorld XML File.xml"' window=hidden)
    item(title='RimWorld About.xml' command='robocopy' args='"@base_template_folder@sub_folder" @sel.workdir "About.xml"' window=hidden)
  }
}
// =============================================================================
// mode='Multiple'
// - betyr at den kan fungere i flere/forskjellige kontekster, så hvis man
//   f.eks ønsker å lage en meny som skal deles av både taskbar og explorer,
//   så må denne stå på multiple.
// =============================================================================
item(title='Copy pixels' type='file' find='.jpg|.jpeg|.bmp|.tiff|.webp|.png' mode='single' cmd='pwsh' args='-Command Add-Type -AssemblyName System.Windows.Forms; [Windows.Forms.Clipboard]::SetImage([System.Drawing.Image]::FromFile(@sel.path.quote))' admin window='show' image=image.fluent(\uE8B9,13))
item(title='Paste pixels' type='back.dir|back.drive|desktop' where=clipboard.has_image() cmd='pwsh' args='-Command Add-Type -AssemblyName System.Windows.Forms; if([Windows.Forms.Clipboard]::ContainsImage()){ [Windows.Forms.Clipboard]::GetImage().Save((Join-Path \"@sel.curdir\" \"Pasted image ($(Get-Date -UFormat \"%Y-%m-%d %I;%M;%S %p\")).png\"), [System.Drawing.Imaging.ImageFormat]::Png) }' admin window='show' image=icon.personalize)
// =============================================================================
// Edit existing
item(find=["Folder*", "new"] separator="after" image=\uE0E5)
// Create New
item(title="Folders\t5" image=\uE0E5 parent="New" cmd=io.dir.create("Folder1","Folder2","Folder3", "Folder4", "Folder5") args='"@sel.path"' pos=11 col)

// =============================================================================
menu(title='Test icon in a row')
{
    item(image=\uE249 col=1)
    item(image=\uE243 col=1)
    item(image=\uE242 col=1)
    item(image=\uE24F col=1)
}
// =============================================================================
menu(type='back' title='Apps' image=\ue0a0)
{
    $set_path=user.sendto // or: '@user.desktop\Apps'
    $get_items=path.files(set_path, "*.lnk")
    item(title='Application Shortcuts:' vis=label sep='after')
    item(title=path.title(get_items[0])  where=len(get_items)>0 image=path.lnk(set_path +'\'+get_items[0]) cmd=path.lnk(set_path +'\'+get_items[0]))
    item(title=path.title(get_items[1])  where=len(get_items)>1 image cmd=path.lnk(set_path +'\'+get_items[1]))
    item(title=path.title(get_items[2])  where=len(get_items)>2 )
    item(title=path.title(get_items[3])  where=len(get_items)>3 )
    item(title=path.title(get_items[4])  where=len(get_items)>4 )
    item(title=''                        where=len(get_items)>5 col )
    item(title=path.title(get_items[5])  where=len(get_items)>5 )
    item(title=path.title(get_items[6])  where=len(get_items)>6 )
    item(title=path.title(get_items[7])  where=len(get_items)>7 )
    item(title=path.title(get_items[8])  where=len(get_items)>8 )
    item(title=path.title(get_items[9])  where=len(get_items)>9 )
}
// =============================================================================
// cmd=window.command(iii)
item(type="taskbar" title="&Lock all taskbars" cmd=window.command(425))
// -----------------------------------------------------------------------------
// /official_docs/functions/window.html
window.is_taskbar   // Returns true if the window handle is for the Taskbar window
window.is_desktop   // Returns true if the window handle is for the Desktop window
window.is_explorer  // Returns true if the window handle is for the Explorer window
window.is_tree      // Returns true if the window handle is for the Side window
window.is_edit      // Returns true if the window handle is for the Edit menu
window.is_start     // Returns true if the window handle is for the Win+X menu

window.send(name, msg, wparam, lparam)  // Search for the window by name and send the specified message
window.post(name, msg, wparam, lparam)  // Search for the window by name and send the specified message without waiting

window.send(null, msg, wparam, lparam)  // Send the specified message to current window.
window.post(null, msg, wparam, lparam)  // Send the specified message without waiting to current window.

window.command(command) // Send WM_COMMAND to current window.
window.command(command, name)   // Search for the window by name and send the command to it.

window.handle
window.name
window.title
window.owner
window.parent
window.parent.handle
window.parent.name
window.is_contextmenuhandler

// =============================================================================
//
// Command Prompt
CMD-LINE
CMD-PROMPT
// PowerShell
CMD-POWERSHELL
CMD-PS
// PowerShell 7
CMD-PWSH
// Explorer
CMD-SHELL
CMD-EXPLORER
//

item(title=title.windows_powershell  cmd-ps=`-command Set-Location -Path '@sel.dir\.'`)
// =============================================================================
modify(mode='multiple' type='dir' where=window.name=='Progman'&&this.id==id.open sep=after pos=0)
modify(mode='multiple' type='dir' where=window.name=='CabinetWClass'&&this.id==id.open sep=after pos=0)
modify(mode='multiple' type='dir' where=window.name=='CabinetWClass' find='open in new tab' pos=indexof('open', 1))
modify(mode='multiple' type='dir' where=window.name=='CabinetWClass' find='open in new window' sep=after pos=indexof('open in new window', 1))
modify(type='dir' where=window.name=='Progman' find='7-zip' sep=after pos=indexof('open', 1))
modify(mode='multiple' type='dir' where=window.name=='CabinetWClass' find='7-zip' sep=after pos=indexof('open in new window', 1))
menu(title=id.cut mode='multiple' pos=indexof('7-zip', 1)){}
    modify(mode='multiple' type='dir' where=this.id==id.cut pos=indexof(this.id))
menu(title=id.copy mode='multiple'){}
    modify(mode='multiple' type='dir' where=this.id==id.copy sep=after pos=indexof(this.id))
menu(title=id.create_shortcut mode='multiple'){}
    modify(mode='multiple' type='dir' where=this.id==id.create_shortcut sep=after pos=indexof(this.id))
menu(title=id.rename mode='multiple'){}
    modify(mode='multiple' type='dir' where=this.id==id.rename pos=indexof(this.id))
menu(title=id.delete mode='multiple'){}
    modify(mode='multiple' type='dir' where=this.id==id.delete sep=after pos=indexof(this.id))
menu(title=id.properties mode='multiple'){}
    modify(mode='multiple' type='dir' where=this.id==id.properties sep=before pos=indexof(this.id))
// =============================================================================
item(type="*" title='Window class name [@window.name]' cmd=clipboard.set(window.name))

// =============================================================================
// COLORS
$clr_blue   = #34b6ff
$clr_green  = #39c65a
$clr_purple = #a457ff

// ITEMS
$cmd_copy_filepath  = '-Command @sel("\\\"",",") | % { [System.IO.Path]::GetFullPath($_) } | Set-Clipboard'
$cmd_copy_location  = '-Command @sel("\\\"",",") | % { [System.IO.Path]::GetDirectoryName($_) } | Set-Clipboard'
$cmd_copy_filename  = '-Command @sel("\\\"",",") | % { [System.IO.Path]::GetFileName($_) } | Set-Clipboard'
$cmd_copy_basename  = '-Command @sel("\\\"",",") | % { [System.IO.Path]::GetFileNameWithoutExtension($_) } | Set-Clipboard'
$cmd_copy_content   = '-Command @sel("\\\"",",") | % { Get-Content $_ -Raw } | Set-Clipboard'
$cmd_copy_contents  = '-Command Set-Clipboard -Path (@sel("\\\"",",") | % { Get-ChildItem $_ -Force | % { $_.FullName } })'

// CREATE
menu(title="&Clipboard" mode="multiple" image=[\uE11B, clr_blue] image-sel=[\uE11B, clr_green]) {
    item(title="Copy Filepath" keys=1 cmd-ps=cmd_copy_filepath window=hidden image=[\uE10E, clr_green])
    item(title="Copy Path"     keys=2 cmd-ps=cmd_copy_location window=hidden image=[\uE10E, clr_green])
    separator
    item(title="Copy Filename" keys=3 cmd-ps=cmd_copy_filename window=hidden image=[\uE10E, clr_blue])
    item(title="Copy Name"     keys=4 cmd-ps=cmd_copy_basename window=hidden image=[\uE10E, clr_blue])
    separator
    item(title="Copy Content"  keys=5 cmd-ps=cmd_copy_content  window=hidden image=[\uE1A3, clr_purple] type='file' mode='multi_unique')
    item(title="Copy Contents" keys=6 cmd-ps=cmd_copy_contents window=hidden image=[\uE1A3, clr_purple] type='dir|back.dir|drive|back.drive')
}
// =============================================================================
menu(where=sys.ver.major >= 9 expanded=1 type='taskbar' pos=0) {
    separator
    item(title=sys.ver.name vis='label')
    item(title='OS build: '+sys.ver vis='label')
    separator
}
// =============================================================================
     // open target directory of shortcuts
     item(mode="single"
          type='file'
          separator="before"
          find='.lnk'
          title='open file location'
          cmd='explorer.exe'
          args='/select, @path.lnktarget(sel) '
     )

// =============================================================================
item(mode="multiple" title='test for statement 1' cmd={
    $files =''
    for(i=0, i< sel.count) {
        files += sel[i] + "\n"
    }
    msg(files)
})

item(mode="multiple" title='test for statement 2' cmd=msg(for(i=0, i< sel.count, sel[i]+"\n")))

item(title='test for statement 3' cmd=
    for(i=0, i< 6) {
        if(i==4, break)
        if(i==2, continue)
        msg(i)
        i++
    }
)


// =============================================================================
// To move sub-item to top-level menu use this command
modify(find='size' in='/sort by' vis=disabled image=#f00 menu='/')


// =============================================================================
//  All sub-items move well to the top level when using the "expanded" property.
menu(mode='multiple' expanded=true)
{
    item(title="item1")
    item(title="item2")
}


// =============================================================================
// Copy utils
$segoe_icon_size=13

menu(title='Copy' type='file|dir|back.dir|drive|back.drive' mode='multiple' image=icon.copy){
    item(title='Copy path' cmd='powershell' args='-Command @sel("\\\"",",") | % { [System.IO.Path]::GetFullPath($_) } | Set-Clipboard' window=hidden image=image.fluent(\uE74E,segoe_icon_size))
    item(title='Copy location' cmd='powershell' args='-Command @sel("\\\"",",") | % { [System.IO.Path]::GetDirectoryName($_) } | Set-Clipboard' window=hidden image=image.mdl(\uED43,segoe_icon_size))
    item(title='Copy filename' cmd='powershell' args='-Command @sel("\\\"",",") | % { [System.IO.Path]::GetFileName($_) } | Set-Clipboard' window=hidden image=image.fluent(\uE729,segoe_icon_size))
    item(title='Copy name' cmd='powershell' args='-Command @sel("\\\"",",") | % { [System.IO.Path]::GetFileNameWithoutExtension($_) } | Set-Clipboard' window=hidden image=image.fluent(\uE7C3,segoe_icon_size))
    item(title='Copy contents' type='file' mode='multi_unique' cmd='powershell' args='-Command @sel("\\\"",",") | % { Get-Content $_ -Raw } | Set-Clipboard' window=hidden image=image.fluent(\uE8E4,segoe_icon_size))
    item(title='Copy contents' type='~file|dir|back.dir|drive|back.drive' mode='multiple' cmd='powershell' args='-Command Set-Clipboard -Path (@sel("\\\"",",") | % { Get-ChildItem $_ -Force | % { $_.FullName } })' window=hidden image=image.fluent(\uED28,segoe_icon_size))
}
modify(where=this.id==id.copy menu='Copy' pos=top image=image.fluent(\uE8C8,segoe_icon_size))

// =============================================================================
modify(where=regex.match(this.name, ".*^Pin.*") menu="Apps")

// =============================================================================
modify(type='file' where=this.id==id.open pos=indexof('ORG', 1) image=image.fluent(\uE8E5,12) title="Open \t |" )
modify(type='file' where=this.id==id.open_with pos=indexof('ORG', 2) image=image.fluent(\uE7AC,12) title="Open With" )
// =============================================================================
// ----------------------------------------------------------------------------
// CLEANUP FILE-EXPLORER
// ----------------------------------------------------------------------------
menu(title='ORG' pos='top' sep='bottom' image=[\uE1B8, #000000]) {}
modify(
    where=!this.id(
        // -> Basic Operations
        id.delete, id.edit, id.new, id.new_folder, id.new_item, id.open,
        id.open_with, id.properties, id.rename,
        // -> Clipboard Operations
        id.copy_as_path, id.copy_path, id.copy, id.cut, id.paste,
        id.paste_shortcut,
        // -> Movement and Location
        id.copy_here, id.copy_to, id.copy_to_folder, id.move_here, id.move_to,
        id.move_to_folder, id.open_file_location, id.open_folder_location,
        // -> Advanced File Operations
        id.compressed, id.create_shortcut, id.create_shortcuts_here,
        id.extract_all, id.extract_to, id.restore_previous_versions,
        // -> Icon and Display Settings
        id.extra_large_icons, id.large_icons, id.medium_icons, id.small_icons,
        id.list, id.details, id.tiles, id.content,
        // -> Organization and Sorting
        id.arrange_by, id.group_by, id.sort_by,
        // -> Icon Management
        id.align_icons_to_grid, id.auto_arrange_icons,
        // -> Visibility and Customization
        id.customize_notification_icons, id.customize_this_folder,
        id.show_cortana_button, id.show_desktop_icons, id.show_file_extensions,
        id.show_hidden_files, id.show_libraries, id.show_network,
        id.show_people_on_the_taskbar, id.show_task_view_button,
        id.show_touch_keyboard_button, id.show_touchpad_button,
        // -> System Tools
        id.adjust_date_time, id.control_panel, id.device_manager,
        id.display_settings, id.file_explorer, id.folder_options,
        id.power_options, id.settings, id.task_manager, id.taskbar_settings,
        // -> Personalization
        id.desktop, id.options, id.personalize,
        // -> Network Operations
        id.cast_to_device, id.disconnect, id.disconnect_network_drive,
        id.map_as_drive, id.map_network_drive,
        // -> Sharing and Accessibility
        id.give_access_to, id.make_available_offline, id.make_available_online,
        id.share, id.share_with,
        // -> Command Line Tools
        id.command_prompt, id.open_command_prompt, id.open_command_window_here,
        id.open_powershell_window_here, id.open_windows_powershell,
        // -> System Utilities
        id.cleanup, id.refresh, id.run, id.run_as_administrator,
        id.run_as_another_user, id.search, id.troubleshoot_compatibility,
        // -> Security Tools
        id.install, id.manage, id.turn_off_bitlocker, id.turn_on_bitlocker,
        // -> Device Operations
        id.autoplay, id.eject, id.erase_this_disc, id.mount,
        // -> Media Actions
        id.play, id.print,
        // -> Window Arrangement
        id.cascade_windows, id.show_windows_side_by_side,
        id.show_windows_stacked,
        // -> Taskbar Management
        id.lock_all_taskbars, id.lock_the_taskbar,
        // -> Windows Features
        id.cortana, id.news_and_interests, id.send_to, id.store,
        // -> General
        id.add_a_network_location, id.cancel, id.collapse,
        id.collapse_all_groups, id.collapse_group, id.configure,
        id.empty_recycle_bin, id.exit_explorer, id.expand, id.expand_all_groups,
        id.expand_group, id.format, id.include_in_library,
        id.insert_unicode_control_character, id.merge, id.more_options,
        id.next_desktop_background, id.open_as_portable, id.open_autoplay,
        id.open_in_new_process, id.open_in_new_tab, id.open_in_new_window,
        id.open_new_tab, id.open_new_window,
        id.pin_current_folder_to_quick_access, id.pin_to_quick_access,
        id.pin_to_start, id.pin_to_taskbar, id.preview, id.reconversion,
        id.redo, id.remove_from_quick_access, id.remove_properties, id.restore,
        id.restore_default_libraries, id.rotate_left, id.rotate_right,
        id.select_all, id.set_as_desktop_background,
        id.set_as_desktop_wallpaper, id.shield, id.show_pen_button,
        id.show_the_desktop, id.show_this_pc, id.undo,
        id.unpin_from_quick_access, id.unpin_from_start, id.unpin_from_taskbar,
        id.view
    ) menu='ORG'
)


// =============================================================================
// Blah Blah Blaze — 10/07/2023 2:35 PM
modify(mode='multiple' type='dir' where=window.name=='Progman'&&this.id==id.open sep=after pos=0)
modify(mode='multiple' type='dir' where=window.name=='CabinetWClass'&&this.id==id.open sep=after pos=0)
modify(mode='multiple' type='dir' where=window.name=='CabinetWClass' find='open in new tab' pos=indexof('open', 1))
modify(mode='multiple' type='dir' where=window.name=='CabinetWClass' find='open in new window' sep=after pos=indexof('open in new window', 1))
modify(type='dir' where=window.name=='Progman' find='7-zip' sep=after pos=indexof('open', 1))
modify(mode='multiple' type='dir' where=window.name=='CabinetWClass' find='7-zip' sep=after pos=indexof('open in new window', 1))
menu(title=id.cut mode='multiple' pos=indexof('7-zip', 1)){}
    modify(mode='multiple' type='dir' where=this.id==id.cut pos=indexof(this.id))
menu(title=id.copy mode='multiple'){}
    modify(mode='multiple' type='dir' where=this.id==id.copy sep=after pos=indexof(this.id))
menu(title=id.create_shortcut mode='multiple'){}
    modify(mode='multiple' type='dir' where=this.id==id.create_shortcut sep=after pos=indexof(this.id))
menu(title=id.rename mode='multiple'){}
    modify(mode='multiple' type='dir' where=this.id==id.rename pos=indexof(this.id))
menu(title=id.delete mode='multiple'){}
    modify(mode='multiple' type='dir' where=this.id==id.delete sep=after pos=indexof(this.id))
menu(title=id.properties mode='multiple'){}
    modify(mode='multiple' type='dir' where=this.id==id.properties sep=before pos=indexof(this.id))


// =============================================================================
//
// Use "Segoe Fluent Icons" with "lowercase glyph codes" for bigger badge
(e.g.: image=[[\uf83d, #ffffff],[\uf83f, #ff6a00], "Segoe Fluent Icons"])
// Use "Segoe MDL2 Assets" with "uppercase glyph codes" for smaller badge
(e.g.: image=[[\uF83D, #ffffff],[\uF83F, #ff6a00], "Segoe MDL2 Assets"])

// =============================================================================
//
// Blah Blah Blaze — 09/16/2023 11:44 AM
// - Nicer tooltip styles
$light=#E0DFDF
settings {
    tip {
        enabled=true
        opacity=97
        padding=[14,6,14,6]
        width=600
        radius=1
        time=1.5

        default=[#d4d4d4, #232323]
        info=[#696969, light]
        primary=[#2269cd, light]
        success=[#2eaf2c, light]
        warning=[#ffc107, #343a40]
        danger=[#c33e3e, light]
    }
}
//
// Here's the entire styling:
$dark=#000000
$light=#E0DFDF
$accent=#4CC2FF
theme
{
    name="modern"
    background {
        effect=0
        opacity=99
    }
    border {
        enabled=true
        size=1
        color=[light,10]
        padding=[0,4,0,4]
        radius=2
    }
    shadow {
        enabled=true
        color=[dark,7]
        size=3
        offset=1
    }
    view=view.medium
    separator {
        size=1
        margin=[0,4,0,4]
        color=[light,10]
    }
    item {
        prefix=1
        padding=[12,4,12,4]
        radius=2
        margin=[4,0,4,0]
        back {
            // normal=
            select=[light,20]
            // normal_disabled=
            // select_disabled=
        }
        text {
            // normal=
            // select=
            // normal_disabled=
            // select_disabled=
        }
        border {
            // normal=
            select=[light,40]
            // normal_disabled=
            // select_disabled=
        }
    }
    layout.popup=0
    font {
        size=25
        weight=5
    }
    symbol {
        chevron {
            // normal=
            // select=
            // normal_disabled=
            // select_disabled=
        }
        checkmark {
            // normal=
            // select=
            // normal_disabled=
            // select_disabled=
        }
        bullet {
            // normal=
            // select=
            // normal_disabled=
            // select_disabled=
        }
    }
    image {
        scale=true
        gap=10
        color=[light, accent, dark]
    }
}
settings {
    tip {
        enabled=true
        opacity=97
        padding=[14,6,14,6]
        width=600
        radius=1
        time=1.5
        default=[#d4d4d4, #232323]
        info=[#696969, light]
        primary=[#2269cd, light]
        success=[#2eaf2c, light]
        warning=[#ffc107, #343a40]
        danger=[#c33e3e, light]
    }
}
// =============================================================================
//
// Blah Blah Blaze — 09/16/2023 3:04 PM
// - Allows for various copying operations with single/multiple items of any
//   type. When all selected items are files, "Copy contents" will concatenate
//   the contents of all selected files (separated by line-breaks) and copy it
//   to the clipboard (no guaranteed order of the items).
// - When all select items are folders or drives, "Copy contents" will jointly
//   copy all of their immediate child items to the clipboard as filesystem
//   objects.
$segoe_icon_size=13
menu(title='Copy' type='file|dir|back.dir|drive|back.drive' mode='multiple' image=icon.copy){
    item(title='Copy path' cmd='powershell' args='-Command @sel("\\\"",",") | % { [System.IO.Path]::GetFullPath($_) } | Set-Clipboard' admin window=hidden image=image.fluent(\uE74E,segoe_icon_size))
    item(title='Copy location' cmd='powershell' args='-Command @sel("\\\"",",") | % { [System.IO.Path]::GetDirectoryName($_) } | Set-Clipboard' admin window=hidden image=image.mdl(\uED43,segoe_icon_size))
    item(title='Copy filename' cmd='powershell' args='-Command @sel("\\\"",",") | % { [System.IO.Path]::GetFileName($_) } | Set-Clipboard' admin window=hidden image=image.fluent(\uE729,segoe_icon_size))
    item(title='Copy name' cmd='powershell' args='-Command @sel("\\\"",",") | % { [System.IO.Path]::GetFileNameWithoutExtension($_) } | Set-Clipboard' admin window=hidden image=image.fluent(\uE7C3,segoe_icon_size))
    item(title='Copy contents' type='file' mode='multi_unique' cmd='powershell' args='-Command @sel("\\\"",",") | % { Get-Content $_ -Raw } | Set-Clipboard' admin window=hidden image=image.fluent(\uE8E4,segoe_icon_size))
    item(title='Copy contents' type='~file|dir|back.dir|drive|back.drive' mode='multiple' cmd='powershell' args='-Command Set-Clipboard -Path (@sel("\\\"",",") | % { Get-ChildItem $_ -Force | % { $_.FullName } })' admin window=hidden image=image.fluent(\uED28,segoe_icon_size))
}
modify(where=this.id==id.copy menu='Copy' pos=top image=image.fluent(\uE8C8,segoe_icon_size))

// =============================================================================
//
// Blah Blah Blaze — 09/06/2023 9:21 PM
// - How to change the background color of the tooltip:
$dark=#000000
$light=#E0DFDF
$accent=#4CC2FF
settings {
    tip {
        enabled=true
        opacity=100
        padding=[14,6,14,8]
        width=400
        radius=1
        time=1.5

        default=[#d4d4d4, #232323]
        info=[#737373, light]
        primary=[accent, light]
        success=[#28a745, light]
        warning=[#ffc107, #343a40]
        danger=[#c24e4e, light]
    }
}
// - You can also change the default glyph font family in the theme section.
//   But this isn't too useful because you can't use a default glyph and
//   specify the size. The Segoe Fluent ones are by default HUGE. So I'd just
//   leave it how it is for now and use image.fluent(xxx,12). Or maybe the
//   default works with image.glyph(0xE00B, #0000ff, 10), but at that point
//   you might as well just specify what specific font you're using in the
//   actual call, since changing the default font would make no sense as
//   corresponding hex values between fonts have no relation to each other.
//   And most people wont be sticking to a single font.
theme {
    image {
        glyph="Segoe Fluent Icons"
        scale=true
        gap=10
        color=[light, accent, dark]
    }
}
// =============================================================================
//
// Blah Blah Blaze
// OP
//  — 09/07/2023 3:29 AM
// If you use PowerToys, here's a nice snippet that will disguise the
// PowerRename to look like the standard Windows menu option. With 1 file
// selected, it will use the standard Windows rename. With multiple files
// selected, it'll use the PowerRename menu option:
menu(title=id.rename mode='multiple'){}
    modify(where=sel.count==1&&this.id==id.rename pos=indexof(this.id))
    remove(where=sel.count==1 find='PowerRename')
    modify(where=sel.count>1 find='PowerRename' pos=indexof(id.rename) title='Rename' image=icon.rename)
    remove(where=sel.count>1&&this.id==id.rename)
// Also, if you have the duplicated PowerRename menu option problem, you can fix it by including this:
settings {
    modify {
        remove.duplicate=true
    }
}
// =============================================================================
//
// pavichokche
// on Jun 23, 2023
// I want to hide certain programs' action items in the context menu, but they don't all just have the program's name in their action item title, or it's a generic word. For example, Microsoft Access has a 'Preview' item that appears for some file types, and I never use that software. I just want to hide all items where "MSACCESS.EXE" is the command executable.
// I tried things like this but nothing worked:
// item(where=this.exe = "msaccess.exe" vis="remove")
// Unfortunately the documentation is so limited and lacking of examples to give context to how commands should/could be used, I'm quite lost...
//
// Answered by RubicBG
// on Jun 24, 2023
// this code is simple and it will work:
modify(find='Open As Read-Only|Open in Protected View|"new"|Preview' where=(sel.file.ext=='.accdb' or sel.file.ext=='.maw') vis=remove)
// you can use NirSort ShellMenuView ( http://www.nirsoft.net/utils/shell_menu_view.html ) to see which extensions are associated with which commands.
// PS: what you are actually asking for the moment is not achievable with simple commands. You can do complex logic: search the windows registry for the commands for the corresponding extension, check if the command contains the executable you are looking for ...
// I was able to create something similar with another command where I check 5 paths in the registry to get the same desired result
// =============================================================================
//
// https://nilesoft.org/docs/configuration/properties
// variables
$hello_world = 'Hello World!'
$test_add1 = 5 + 6
item(title = hello_world cmd = msg(hello_world))
menu(title = test_add1)
{
    $test_sub1 = 11 - 5
    item(title = test_sub1)
}
// =============================================================================
//
// Windows paths
- "sys.appdata"
- "sys.bin"
- "sys.bin32"
- "sys.bin64"
- "sys.directory (sys.dir)"
- "sys.path"
- "sys.prog"
- "sys.prog32"
- "sys.programdata"
- "sys.root"
- "sys.temp"
- "sys.templates"
- "sys.users"
- "sys.wow"
- "sys.is_primary_monitor"
// =============================================================================
//
// sys.datetime
- "sys.datetime"
- "sys.datetime.date"
- "sys.datetime.date_day"
- "sys.datetime.date_dayofweek"
- "sys.datetime.date_month"
- "sys.datetime.date_y"
- "sys.datetime.date_year"
- "sys.datetime.date_yy"
- "sys.datetime.short"
- "sys.datetime.time"
- "sys.datetime.time_hour"
- "sys.datetime.time_milliseconds"
- "sys.datetime.time_min"
- "sys.datetime.time_minute"
- "sys.datetime.time_ms"
- "sys.datetime.time_pm"
- "sys.datetime.time_second"
// =============================================================================
//
// Functions that are used with the current item in the context menu
this.type     // Returns the type of the current item [item = 0, menu = 1, separator = 2]
this.checked  // Returns true if the current item is checked
this.pos      // Returns the position of the current item in the context menu
this.disabled // Returns true if the current item is disabled
this.sys      // Returns true if the current item is a system item
this.title    // Returns the title of the current item
this.id       // Returns the ID of the current item
this.count    // Returns the number of items in the context menu
this.length   // Returns the length of the current item's title
// =============================================================================
//
// Functions to return the path of user directories
- "user.home"
- "user.appdata"
- "user.contacts"
- "user.desktop"
- "user.directory (user.dir)"
- "user.documents"
- "user.documentslibrary"
- "user.downloads"
- "user.favorites"
- "user.libraries"
- "user.localappdata"
- "user.music"
- "user.personal"
- "user.pictures"
- "user.profile"
- "user.quicklaunch"
- "user.sendto"
- "user.startmenu"
- "user.temp"
- "user.templates"
- "user.videos"
// =============================================================================
//
// https://nilesoft.org/docs/functions/window
// Syntax
"window.is_taskbar"   // Returns true if the window handle is for the Taskbar window
"window.is_desktop"   // Returns true if the window handle is for the Desktop window
"window.is_explorer"  // Returns true if the window handle is for the Explorer window
"window.is_tree"      // Returns true if the window handle is for the Side window
"window.is_edit"      // Returns true if the window handle is for the Edit menu
"window.is_start"     // Returns true if the window handle is for the Win+X menu
//
"window.send(name, msg, wparam, lparam)"  // Search for the window by name and send the specified message
"window.post(name, msg, wparam, lparam)"  // Search for the window by name and send the specified message without waiting
//
"window.send(null, msg, wparam, lparam)"  // Send the specified message to current window.
"window.post(null, msg, wparam, lparam)"  // Send the specified message without waiting to current window.
//
"window.command(command)"       // Send WM_COMMAND to current window.
"window.command(command,name)"  // Search for the window by name and send the command to it.
//
"window.handle"
"window.name"
"window.title"
"window.owner"
"window.parent"
"window.parent.handle"
"window.parent.name"
"window.is_contextmenuhandler"


// =============================================================================
//
// https://nilesoft.org/docs/functions/command
- "command.cascade_windows"
- "command.copy_to_folder"
- "command.customize_this_folder"
- "command.find"
- "command.folder_options"
- "command.invert_selection"
- "command.minimize_all_windows"
- "command.move_to_folder"
- "command.redo"
- "command.refresh"
- "command.restart_explorer"
- "command.restore_all_windows"
- "command.run"
- "command.search"
- "command.select_all"
- "command.select_none"
- "command.show_windows_side_by_side"
- "command.show_windows_stacked"
- "command.switcher"
- "command.toggle_desktop"
- "command.toggleext"
- "command.togglehidden"
- "command.undo"
// =============================================================================
//
where= (sel.file.ext != '.png'  and sel.file.ext != '.jpg')
where= !(sel.file.ext != '.png' or sel.file.ext != '.jpg')
//
// use svg as icons
theme
{
    image.color=[#0000ff, #000000]
}
// Declaration of an SVG image
@test='<svg fill="none" viewBox="0 0 16 16">
  <circle cx="8" cy="8" r="7" stroke="@image.color1" />
  <circle cx="8" cy="8" r="5.5" stroke="@image.color2" />
</svg>'
item(title="test SVG image" image=icon.test)
// =============================================================================
//
// drac — 12/22/2023 5:26 PM
// SHELL command to run applications knowing their AppID (AUMID:Application User Model ID)
// In general you can run any application if you know its AppID using the following command:
cmd='shell:AppsFolder\AppID'
// To know the AppID of the installed applications you can use from Power Shell the command: Get-StartApps
// It will show a complete list of all applications with their AppID=AUMID:
/*
Name                               AppID
----                               -----
...                ...
Windows PowerShell ISE (x86)    {D65231B0-B2F1-4857-A4CE-A8E7C6EA7D27}\WindowsPowerShell\v1.0\PowerShell_ISE.exe
Editor del Registro             {F38BF404-1D43-42F2-9305-67DE0B28FC23}\regedit.exe
Configuración                   windows.immersivecontrolpanel_cw5n1h2txyewy!microsoft.windows.immersivecontrolpanel
NVIDIA Control Panel            NVIDIACorp.NVIDIAControlPanel_56jybvy8sckqj!NVIDIACorp.NVIDIAControlPanel
Seguridad de Windows            Microsoft.SecHealthUI_8wekyb3d8bbwe!SecHealthUI
Películas y TV                  Microsoft.ZuneVideo_8wekyb3d8bbwe!Microsoft.ZuneVideo
...                ... */
// Example:
NVIDIA CP > cmd='shell:AppsFolder\NVIDIACorp.NVIDIAControlPanel_56jybvy8sckqj!NVIDIACorp.NVIDIAControlPanel'
WhatsApp  > cmd='shell:AppsFolder\5319275A.WhatsAppDesktop_cv1g1gvanyjgm!App'
// =============================================================================
//
// Rubic — 12/24/2023 8:58 PM
$target_path = 'D:\Test'
item(title='Copy Large File@if(sel.count>1,'s')' mode='multiple' type='file' tip='Displays a message about a copied file over 200MB in size'
    cmd=io.directory.create(target_path) & io.copy(sel.file, '@target_path\@sel.file.name') & if(io.file.size(sel.file)>200*1024*1024, msg('@sel.file.name is copied!')) invoke=true)
// Rubic — 12/24/2023 9:11 PM
item(title='Copy File@if(sel.count>1,'s')' mode='multiple' type='file' tip='Мessage at the end that all files have been copied'
    cmd=io.directory.create(target_path) & for(i=0, i<sel.count) { io.copy(sel[i], target_path + '\' + path.name(sel.get(i))) } & msg('Jobs done!') )
// =============================================================================
//
// Rubic — 12/29/2023 10:29 PM
item(title=title.windows_powershell cmd='powershell.exe' args='-noexit -command Set-Location -Path "@sel.dir\."')
// Rubic — 12/29/2023 9:50 PM
cmd='%LocalAppData%\PowerToys\PowerToys.exe' & command.sleep(1000) & key.send(key.alt, key.space)
//
modify(type="recyclebin" where=window.is_desktop and this.id==id.empty_recycle_bin pos=1 sep vis=true)

// =============================================================================
//
// Rubic — 12/29/2023 11:29 PM
item(title='Test1' where=wnd.is_desktop commands{
    cmd=if(!process.is_started('PowerToys.exe'), '%LocalAppData%\PowerToys\PowerToys.exe') wait=0 invoke=if(!process.is_started('PowerToys.exe'), 5000, 0),
    cmd=key.send(key.alt, key.space),
    })
// process.is_started() - this function is not released yet, but once
// released the code will work fine, for now you can use this:
item(title='Test2' where=wnd.is_desktop commands{
    cmd='%LocalAppData%\PowerToys\PowerToys.exe' wait=0 invoke=5000,
    cmd=key.send(key.alt, key.space),
    })
// but it will work only once, when PowerToys is not started yet
// =============================================================================
//
// Mostwest — 01/01/2024 6:30 PM
// i was trying to convert this item for single files to multiple files with the cycle for  (moving files)
item(title='Sposta in TV Streaming' type='file' mode='single'
    cmd=io.move(@sel.file, 'C:\Users\<USER>\Desktop\testmove2\@sel.name'))
// moudey — 01/01/2024 6:36 PM
 item(title='Sposta in TV Streaming' type='file' mode='multiple' invoke=true
    cmd=io.move(sel.path, 'C:\Users\<USER>\Desktop\testmove2\@sel.name'))
// =============================================================================
//
// Mostwest — 01/01/2024 7:14 PM
// Yeah, i was thinkig it too, any chance I can get a message like
// "Moving Done" only once with "invoke=true"? btw with invoke works
// perfectly
item(title='Sposta in TV Streaming' type='file' mode='multiple' cmd={
    $count = 0
    for(i= 0, i < sel.count) {
        count += io.move(sel[i], 'C:\Users\<USER>\Desktop\testmove2\@path.name(sel[i])')
    }
    msg('Moving Done (@count from @sel.count)!')
})
// moudey — 01/01/2024 6:18 PM
item(mode="multiple" title='test for statement 1' cmd={
    $files =''
    for(i=0, i< sel.count) {
        files += sel[i] + "\n"
    }
    msg(files)
})

item(mode="multiple" title='test for statement 2' cmd=msg(for(i=0, i< sel.count, sel[i]+"\n")))

item(title='test for statement 3' cmd=
    for(i=0, i< 6) {
        if(i==4, break)
        if(i==2, continue)
        msg(i)
        i++
    }
)
// =============================================================================
//
modify(where=this.name=="Vaciar Papelera de reciclaje" vis=1 image=#f00) // drac 02.01.2024
// modify(type="recyclebin" where=window.is_desktop and this.id==id.empty_recycle_bin pos=1 sep)
modify(where=this.id==id.empty_recycle_bin vis=1 image=#f00) // Rubic 01.01.2024
// Shell relies on finding element names through pre-definitions in
// system resource files, regardless of the language used. The attached
// image illustrates the concept.
//
// drac — 01/03/2024 10:46 AM
// commands to copy or move to
item(title="Copiar a carpeta" image=icon.copy_to cmd=command.copy_to_folder)
item(title="Mover a carpeta" image=icon.move_to cmd=command.move_to_folder)
// =============================================================================
//
// Diavel — 01/04/2024 6:03 PM
// path: Desktop/Apps
// Extension type: .lnk (shortcuts, essentially)
// Max files to display: hopefully all 55 of them, but even 10 is good
// (I'll try to make multiple sub-menus to extend the functionality,
// hopefully)
// Rubic — 01/04/2024 6:17 PM
menu(type='back' title='Apps' image=\ue0a0)
{
    $set_path=user.sendto // or: '@user.desktop\Apps'
    $get_items=path.files(set_path, "*.lnk")
    item(title='Application Shortcuts:' vis=label sep='after')
    item(title=path.title(get_items[0])  where=len(get_items)>0 image=path.lnk(set_path +'\'+get_items[0]) cmd=path.lnk(set_path +'\'+get_items[0]))
    item(title=path.title(get_items[1])  where=len(get_items)>1 image cmd=path.lnk(set_path +'\'+get_items[1]))
    item(title=path.title(get_items[2])  where=len(get_items)>2 )
    item(title=path.title(get_items[3])  where=len(get_items)>3 )
    item(title=path.title(get_items[4])  where=len(get_items)>4 )
    item(title=''                        where=len(get_items)>5 col )
    item(title=path.title(get_items[5])  where=len(get_items)>5 )
    item(title=path.title(get_items[6])  where=len(get_items)>6 )
    item(title=path.title(get_items[7])  where=len(get_items)>7 )
    item(title=path.title(get_items[8])  where=len(get_items)>8 )
    item(title=path.title(get_items[9])  where=len(get_items)>9 )
}

// =============================================================================
//
// Rubic — 01/04/2024 4:59 PM
// this is experimental and only works for "open" command:
modify(where=this.title=='"Open"' image=image.default)
modify(where=this.id==id.open image=image.default)

// I modified the function to recognize the 'Open' command in other
// languages as well - I only tested it on a .reg file (as a renamed
// command named "open" to "Merge") on win 11:
$reg_ext_main = if(sel.file.ext=='.lnk', io.meta(sel,"System.Link.TargetExtension"), sel.file.ext)
$reg_ext_type = if(reg.exists('HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\FileExts\@reg_ext_main\UserChoice', 'ProgID'), reg.get('HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\FileExts\@reg_ext_main\UserChoice', 'ProgID'), if(reg.exists('HKCR\@reg_ext_main'), reg.get('HKCR\@reg_ext_main'), reg_ext_main))
$reg_key_name = if(str.empty(reg.get('HKCR\@reg_ext_type\shell\open')), '&Open', if(str.empty(str.res(reg.get('HKCR\@reg_ext_type\shell\open'))), reg.get('HKCR\@reg_ext_type\shell\open'), str.res(reg.get('HKCR\@reg_ext_type\shell\open'))))
modify(where=this.title==reg_key_name image=image.default)

// =============================================================================
//
//  "Lock All Taskbars" entry
item(type="taskbar" title="&Lock all taskbars" cmd=window.command(425))
//
// =============================================================================
//
// Rubic — 01/09/2024 6:07 PM
// NS Glyphs, Segoe Fluent Icons and Segoe MDL2 Assets are different
// image systems - codes from one system cannot be combined with another
// there are two ways to use glyphs:
// - with function: image.glyph() image.segoe() image.fluent() with
//   which you can change color and size of single glyph
image.glyph(\uF16B, #00ffff, 25, "Segoe MDL2 Assets")
// as a block of information: [[...],[...],...], where the size does not change (maybe it is a bug):
image=[[\uEA18, #ffff00], 'Segoe MDL2 Assets']
image=[[\uEA18], #ffff00, 'Segoe MDL2 Assets']
// but you can combine glyphs:
image=[[\uE00B, #ffff00], [\uE70F, #ff00ff], 'Segoe MDL2 Assets']
// I prefer to experiment with image.svg() - gives you maximum freedom

// =============================================================================
//
// menuitem on specific extensions
remove(find="Extract All|Open with" type="file" where=sel.file.ext=='.zip'  sel.file.ext=='.rar'  sel.file.ext=='.7z')

// =============================================================================
//
// drac — 01/11/2024 8:13 PM
// I have created a menu option to open .ico files with Pain.NET.
// The following code only allows to open the "Paint.NET" application, but not the selected file.
item(type='file' where=sel.file.ext=='.ico' title='Abrir' pos=top cmd='@sys.prog\paint.net\paintdotnet.exe' image=image.default)
// What arguments should be added to open the previously selected .ico
// file directly? How can I directly call the path to the selected
// .ico file?
//
// moudey — 01/11/2024 8:30 PM
// Try add:
args=@sel(true)

// =============================================================================
// Mostwest — 01/12/2024 3:53 PM
// How do I translate this cli argument into NS?
"command": "cmd /c mediainfo --Inform=\\\"General;%Format%\\\" $path"
// moudey — 01/12/2024 4:59 PM
item(mode="multiple" title='mediainfo'
    cmd-line='/k title Mediainfo & "C:\Program Files (x86)\K-Lite Codec Pack\Tools\mediainfo.exe" --Inform="General;%Format%" @sel(true)')

// =============================================================================
//
// Rubic — Yesterday at 11:58 AM
// https://nilesoft.org/docs/configuration/themes
item
{
    text
    {
        normal = color
        normal.disabled = color
        select = color
        select.disabled = color
    }
    back
    {
        normal = color
        normal.disabled = color
        select = color
        select.disabled = color
    }
}
// =============================================================================
//
// Rubic — 11/28/2023 4:14 PM
// item for vscode in a workspace
item(title='Visual Studio Code' image=[\uE272, #22A7F2] cmd='code' args='"@app.directory"' window=hidden)
vis=key.shift() type='taskbar' pos=0 image=[[\uE249, color.red]]
// =============================================================================
//
// Blah Blah Blaze OP — 09/07/2023 3:29 AM If you use PowerToys, here's a
// nice snippet that will disguise the PowerRename to look like the
// standard Windows menu option. With 1 file selected, it will use the
// standard Windows rename. With multiple files selected, it'll use the
// PowerRename menu option:
menu(title=id.rename mode='multiple'){}
    modify(where=sel.count==1&&this.id==id.rename pos=indexof(this.id))
    remove(where=sel.count==1 find='PowerRename')
    modify(where=sel.count>1 find='PowerRename' pos=indexof(id.rename) title='Rename' image=icon.rename)
    remove(where=sel.count>1&&this.id==id.rename)
// Also, if you have the duplicated PowerRename menu option problem,
// you can fix it by including this:
settings {
    modify {
        remove.duplicate=true
    }
}


// =============================================================================
//
// python
// import os
// import sys
// import argparse
// # Get command line options: depth, folder
// argparser = argparse.ArgumentParser(description="Recursively create nilesoft shell items for a folder", formatter_class=argparse.ArgumentDefaultsHelpFormatter)
// =============================================================================
//
// Rubic
// 08/19/2023 at 22:16:58 GMT+2
// [APP] 7-Zip
// SevenZip.nss
// Context menu with commands to completely replace that of 7-Zip archiver
// - menu is consistent with supported formats (all supported formats are documented in the file; more can be added)
// - with the shift key stops taking file extensions into account
// - with the shift key shows additional commands
// - with the shift key on a command gives an additional execution option
// - the drag drop menu has not been replaced - there is no such
//   functionality yet (the main standard menu of 7-Zip is hidden, not
//   removed - otherwise the drag drop menu will also disappear)
// =============================================================================
//
// MethaZz
// 08/19/2023 at 21:14:02 GMT+2
// Shell x ShareX
// This thread covers a few things to use Shell with ShareX (or other programs). This includes:
// - Adding existing items into a custom group
// - Removing duplicated items (PowerToys)
// - Adding an entry to "Add image effects" (ShareX)
// Adding existing entries into a custom group
// 1. Create a menu entry (*static*)
menu(mode='multiple' type='file' title='ShareX' image='C:\Program Files\ShareX\ShareX.exe') {}
// 2. Move the existing entries in the created menu (*dynamic*)
item(find='Upload with ShareX' menu='ShareX')
item(find='Edit with ShareX' menu='ShareX')
//
// (thanks to
// @Rubic
// )
// Removing duplicated items (PowerToys)
// 1. Paste the following lines (*static*)
item(where=this.pos>10 find='"PowerRename"' vis=vis.remove)
item(where=this.pos>10 find='"Resize pictures"' vis=vis.remove)
//
// However, there still will be duplicated items when selecting more than 1 item. (https://github.com/moudey/Shell/issues/210)
// Adding a entry to "Add image effects" (ShareX)
// 1. Paste the following (*dynamic*)
item(title='Add image effects' image='C:\Program Files\ShareX\ShareX.exe' find='.png|.jpg' command='"C:\Program Files\ShareX\ShareX.exe"' arg='-imageeffects "@sel.path"' menu='ShareX')
// =============================================================================
//
// wbs_perso
// 05/26/2023 at 21:16:38 GMT+2
// Add "Update Badge" on StartMenu options
// cf. https://learn.microsoft.com/en-us/windows/apps/design/style/segoe-fluent-icons-font
// 21:16:14
// Glyphs code ref.
// =============================================================================
//
// FierySpectre
// 05/21/2023 at 17:43:06 GMT+2
// add a directory to PATH
// I spend more time on creating this than I'm ever gonna save by using this so I'll share it here in the hopes someone has any use for it:
item(title = 'Add to PATH' Window=Hidden type='dir|back.dir' admin cmd args='/K @echo powershell.exe [Environment]::SetEnvironmentVariable("\"PATH\"", "\"@sel.path;"\" + Environment
// Can be placed wherever, I have mine in the default template develop submenu
// This was created mostly with try and error, if someone knows a more elegant way to call powershell let me know haha
// =============================================================================
//
// Vinimeu
// 05/20/2023 at 00:05:03 GMT+2
// Change color based on windows theme color
// # ❤️ Change color of something depending on whether Windows is on dark or light mode
// ## Now you can read text on both themes!
@(theme.isdark ? '#000000':'#FFFFFF')
// ### Replace 000000 for the hex you want to show when theme is dark
// ### Replace FFFFFF for the hex you want to show when theme is light
// ❤️ Example so you can read text on both themes
text
{
    normal = @(theme.isdark ? '#ffffff':'#000000')
    // ...
}
// ❤️ For color you gotta use
color = theme.isdark ? [#FF0000, #FFC0C8, #660000] : [#0000FF, #000066, #C0E0FF]
// =============================================================================
//
// unknown
// 05/14/2023 at 23:06:34 GMT+2
// wallPapperEngine Controls
// I cant replace shell.dll
//
// moudey
// 05/14/2023 at 22:59:47 GMT+2
// wallPapperEngine Controls
// use this code with debug version
//
// Store a value in registry that you can refer to
$is_mute =reg.get('hkcu\Software\test',"mute")==1
item(title="test"
image=if(is_mute, \ue002, \ue003))
cmd=if(is_mute, null(reg.set('hkcu\Software\test',"mute", 0))),
null(reg.set('hkcu\Software\test',"mute", 1))+"invoke"))
// =============================================================================
//
// moudey
// 05/14/2023 at 13:40:33 GMT+2
// wallPapperEngine Controls
image=image.svg('svg data')
image=image svgf('path/to/svg')
// =============================================================================
//
// -> unknown
// 05/14/2023 at 07:00:19 GMT+2
// wallPapperEngine Controls
// I wanted to add mute and un mute, but there where no icons for those that I could find.
// suggestion to anyone who works on this: allow custom svgs for icons, and maybe run commands to get text? so I can display what's current playing. idk there is a lot of cool things that could be added and this is just a few that I was thinking would be nice to be added when I made this.
// 06:56:47
// to get your path go here and click the address bar and copy pathand add \wallpaper64.exe at the end.
//
// -> moudey
// 05/14/2023 at 13:38:30 GMT+2
// wallPapperEngine Controls
// image=[\uE992, #0f0, "Segoe MDL2 Assets"]
// =============================================================================
//
// -> unknown
// 05/14/2023 at 06:55:35 GMT+2
// wallPapperEngine Controls
// adds controls for wallpapperengine
// you will need to adjust the var "wallPaperEnginePath" to your correct path.
// wallPaperEngine.nss
var {
    wallPaperEnginePath='D:\Program Files (x86)\Steam\steamapps\common\wallpaper_engine\wallpaper64.exe'
}
menu(type='desktop' image="\uE0A0" title='Wallpaper Engine') {
    item(title='Change Wallpaper' image="\uE150" cmd=wallPaperEnginePath)
    item(title='Pause' image="\uE294" cmd=wallPaperEnginePath args='-control pause')
    item(title='Play' image="\uE148" cmd=wallPaperEnginePath args='-control play')
}
// =============================================================================
//
// -> TroubleChute
// 05/12/2023 at 03:30:45 GMT+2
// Goto example
// - An example from Adds a "Goto" option to your context menu, to
//   easily navigate to different Windows folders such as Program
//   Data, Downloads, AppData, etc, as well as locations in the
//   Control Panel.
js
shell
{
    dynamic
    {
        menu(mode="multiple" title='Goto' sep="both" image= \uE14A)
        {
            menu(title='Folder' image=\uE1F4)
            {
                item(title='Windows' image=inherit cmd=sys.dir)
                item(title='System' image=inherit cmd=sys.bin)
                item(title='Program Files' image=inherit cmd=sys.prog)
                item(title='Program Files x86' image=inherit cmd=sys.prog32)
                item(title='ProgramData' image=inherit cmd=sys.programdata)
                item(title='Applications' image=inherit cmd='shell:appsfolder')
                item(title='Users' image=inherit cmd=sys.users)
                separator
                item(title='Desktop' image=inherit cmd=user.desktop)
                item(title='Downloads' image=inherit cmd=user.downloads)
                item(title='Pictures' image=inherit cmd=user.pictures)
                item(title='Documents' image=inherit cmd=user.documents)
                item(title='Startmenu' image=inherit cmd=user.startmenu)
                item(title='Profile' image=inherit cmd=user.dir)
                item(title='AppData' image=inherit cmd=user.appdata)
                item(title='Temp' image=inherit cmd=user.temp)
            }
            item(title=title.control_panel image=\uE0F3 cmd='shell:::{5399E694-6CE5-4D6C-8FCE-1D8870FDCBA0}')
            item(title='All Control Panel Items' image=\uE0F3 cmd='shell:::{ED7BA470-8E54-465E-825C-99712043E01C}')
            item(title=title.run image=\uE14B cmd='shell:::{2559a1f3-21d7-11d4-bdaf-00c04f60b9f0}')
            menu(title=title.settings sep="before" image=id.settings.icon)
            {
                // <a
                //     style="
                //         text-decoration: none;
                //         color: rgb(0, 168, 252);
                //         cursor: pointer !important;
                //     "
                //     href="https://docs.microsoft.com/en-us/windows/uwp/launch-resume/launch-settings-app"
                //     target="_blank"
                //     alt="link-to"
                //     rel="noreferrer"
                //     title="https://docs.microsoft.com/en-us/windows/uwp/launch-resume/launch-settings-app"
                //     data-reactroot=""
                //     >https://docs.microsoft.com/en-us/windows/uwp/launch-resume/launch-settings-app</a
                // >
                item(title='systeminfo' image=inherit cmd arg='/K systeminfo')
                item(title='search' cmd='search-ms:' image=inherit)
                item(title='settings' image=inherit cmd='ms-settings:')
                item(title='about' image=inherit cmd='ms-settings:about')
                item(title='usb' image=inherit cmd='ms-settings:usb')
                item(title='network-status' image=inherit cmd='ms-settings:network-status')
                item(title='network-ethernet' image=inherit cmd='ms-settings:network-ethernet')
                item(title='personalization-background' image=inherit cmd='ms-settings:personalization-background')
                item(title='personalization-colors' image=inherit cmd='ms-settings:colors')
                item(title='lockscreen' image=\uE0F3 cmd='ms-settings:lockscreen')
                item(title='personalization-start' image=inherit cmd='ms-settings:personalization-start')
                item(title='appsfeatures' image=inherit cmd='ms-settings:appsfeatures')
                item(title='optionalfeatures' image=inherit cmd='ms-settings:optionalfeatures')
                item(title='defaultapps' image=inherit cmd='ms-settings:defaultapps')
                item(title='yourinfo' image=inherit cmd='ms-settings:yourinfo')
                item(title='windowsupdate' image=inherit cmd='ms-settings:windowsupdate')
                item(title='windowsdefender' image=inherit cmd='ms-settings:windowsdefender')
                item(title='network connections' image=inherit cmd='shell:::{7007ACC7-3202-11D1-AAD2-00805FC1270E}')
            }
        }
    }
}
// =============================================================================
//
// TroubleChute
// 05/12/2023 at 03:29:39 GMT+2
// Favorite applications and directories example
// An example from
// Adds quick access to the Command Prompt, PowerShell, Regedit and other programs, as well as the Downloads, Pictures and more through a "Favourites" section of the context menu.
js
shell
{
    dynamic
    {
        menu(type='desktop|taskbar' title='Favorites' image=#00ff00)
        {
            menu(title='Applications' image=#ff0000)
            {
                item(title='Command prompt' image cmd='cmd.exe')
                item(title='PowerShell' image cmd='powershell.exe')
                item(title='Registry editor' image cmd='regedit.exe')
                separator
                item(title='Paint' image cmd='mspaint.exe')
                item(title='Notepad' image cmd='notepad.exe')
            }
            separator
            menu(title='Directories' image=#0000ff)
            {
                item(title='Downloads' cmd=user.downloads)
                item(title='Pictures' cmd=user.pictures)
                item(title='Home' cmd=user.directory)
                separator
                item(title='Windows' cmd=sys.directory)
                item(title='Program files' cmd=sys.prog())
            }
        }
    }
}
// =============================================================================
//
// TroubleChute
// 05/12/2023 at 03:28:39 GMT+2
// Copy Path
// This is an example lifted from
// Adds a "Copy to Clipboard" option that lets you copy a file or folder path, or part of, or multiple!
// js
shell
{
    dynamic
    {
        // type can set with '~taskbar' equals all file types except taskbar.
        menu(type='file|dir|back|root|namespace' mode="multiple" title='copy to clipboard' image=#ff00ff)
        {
            // Appears only when multiple selections.
            item(vis=@(sel.count > 1) title='Copy path (@sel.count) items selected'
                cmd=command.copy(sel(false, "\n")))
            item(mode="single" title=sel.path
                cmd=command.copy(sel.path))
            item(mode="single" type='file|dir|back.dir'
                vis=sel.short.len!=sel.path.len title=sel.short
                cmd=command.copy(sel.short))
            separator
            item(mode="single" vis=@(sel.parent.len>3) title=sel.parent
                cmd=command.copy(sel.parent))
            separator
            item(mode="single" type='file|dir|back.dir' title=sel.file.name
                cmd=command.copy(sel.file.name))
            item(mode="single" type='file' title=sel.file.title
                cmd=command.copy(sel.file.title))
            item(mode="single" type='file' title=sel.file.ext
                cmd=command.copy(sel.file.ext))
        }
    }
}