
//
$APP_SUBLIMETEXT_NSS = '@app.dir\NSS\_3_items\user_apps'
$APP_SUBLIMETEXT_SRC = '@user.desktop\my\flow\home\__GOTO__\Apps\app_sublimetext'
//
$APP_USER_SUBLIMETEXT_DIR = '@user.desktop\my\flow\home\__GOTO__\Apps\app_sublimetext\exe'
$APP_USER_SUBLIMETEXT_EXE = '@APP_USER_SUBLIMETEXT_DIR\sublime_text.exe'
$APP_USER_SUBLIMETEXT_TIP = "..."+str.trimstart('@APP_USER_SUBLIMETEXT_EXE','@app.dir')

// -> context: file
item(
    title  = ":  &Sublime Text"
    keys   = "exe"
    type   = 'File'
    args   = '"@sel.file"'
    where  = !str.equals(sel.file.ext,[".7z",".zip",".rar",".apk",".bin",".dll",".dmg",".exe",".sys"])
    //
    image  = APP_USER_SUBLIMETEXT_EXE
    tip    = [APP_USER_SUBLIMETEXT_TIP,TIP3,0.8]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_USER_SUBLIMETEXT_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_SUBLIMETEXT_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_SUBLIMETEXT_EXE')),
        cmd=if(KEYS_EXE_GOTO_NSS,('@APP_SUBLIMETEXT_NSS')),
        cmd=if(KEYS_EXE_GOTO_SRC,('@APP_SUBLIMETEXT_SRC')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_SUBLIMETEXT_DIR')),
    }
)
// context: directory
item(
    title  = ":  &Sublime Text"
    keys   = "exe"
    type   = 'Desktop|Dir|Drive|Back.Dir|Back.Drive'
    args   = '"@sel.dir"'
    //
    image  = APP_USER_SUBLIMETEXT_EXE
    tip    = [APP_USER_SUBLIMETEXT_TIP,TIP3,0.8]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_USER_SUBLIMETEXT_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_SUBLIMETEXT_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_SUBLIMETEXT_EXE')),
        cmd=if(KEYS_EXE_GOTO_NSS,('@APP_SUBLIMETEXT_NSS')),
        cmd=if(KEYS_EXE_GOTO_SRC,('@APP_SUBLIMETEXT_SRC')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_SUBLIMETEXT_DIR')),
    }
)
// Context: Taskbar
item(
    title  = ":  &Sublime Text"
    keys   = "exe"
    type   = 'Taskbar'
    args   = ''
    //
    image  = APP_USER_SUBLIMETEXT_EXE
    tip    = [APP_USER_SUBLIMETEXT_TIP,TIP3,0.8]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_USER_SUBLIMETEXT_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_SUBLIMETEXT_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_SUBLIMETEXT_EXE')),
        cmd=if(KEYS_EXE_GOTO_NSS,('@APP_SUBLIMETEXT_NSS')),
        cmd=if(KEYS_EXE_GOTO_SRC,('@APP_SUBLIMETEXT_SRC')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_SUBLIMETEXT_DIR')),
    }
)


// // THIS DOESN'T WORK, CONTEXT SHOULD BE ADDED IN REGISTRY INSTEAD
// // -> process -> everything64.exe
// item(type='*'
//     where=process.name=="Everything64"
//     //
//     title=":  &Sublime Text"
//     keys=""
//     tip=[APP_USER_SUBLIMETEXT_EXE,TIP3,0.8]
//     image=APP_USER_SUBLIMETEXT_EXE
//     window='Hidden'
//     //
//     admin=keys.rbutton()
//     cmd=if(KEYS_EXE_OPEN_EXE,('"@APP_USER_SUBLIMETEXT_EXE"'))
//     args='@SUBLIME_FILE'
//     //
//     commands{
//         cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_SUBLIMETEXT_DIR')),
//         cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_SUBLIMETEXT_EXE')),
//         cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_SUBLIMETEXT_DIR')),
//     }
// )


