menu(title="cURL" mode="single" type='file' image=\uE14D) {
	$upload_single_link='https://transfer.sh/@str.replace(sel.file.name, " ", "_")'
	item(title='Upload to Transfer.sh for a day'
		cmd-line='/k curl @if(key.shift(),"-v","") -H "Max-Days: 1" --upload-file @sel(true) @upload_single_link & echo. & pause & exit')
	item(title='Upload to Transfer.sh for 14 days'
		cmd-line='/k curl @if(key.shift(),"-v","") --upload-file @sel(true) @upload_single_link & pause & exit')
	item(title='Upload to Transfer.sh for 1 Download'
		cmd-line='/k curl @if(key.shift(),"-v","") -H "Max-Downloads: 1" --upload-file @sel(true) upload_single_link & pause & exit')
	separator()
	item(title='Upload to Transfer.sh for a day'
		cmd args='/k curl --progress-bar -H "Max-Days: 1" --upload-file @sel(true) @upload_single_link | clip & exit')
	item(title='Upload to Transfer.sh for 14 days'
		cmd args='/k curl --progress-bar --upload-file @sel(true) @upload_single_link | clip & exit')
	item(title='Upload to Transfer.sh for 1 Download'
		cmd args='/k curl --progress-bar -H "Max-Downloads: 1" --upload-file @sel(true) upload_single_link | clip & exit')
	separator()
	item(title='Upload and Virusscan'
		cmd-line='/k curl @if(key.shift(),"-v","") -X PUT --upload-file @sel(true) @upload_single_link/scan & echo. & pause & exit'	)
}