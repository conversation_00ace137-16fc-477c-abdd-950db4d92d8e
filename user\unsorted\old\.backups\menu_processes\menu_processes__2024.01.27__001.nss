// ----------------------------------------------------------------------------
// COLORS
// ----------------------------------------------------------------------------
$COLOR_SUBTLE = #4E4259
$COLOR_GREY   = #717482
$COLOR_WHITE  = #FFFFFF
$COLOR_BLUE   = #34B6FF
$COLOR_GREEN  = #39C65A
$COLOR_ORANGE = #FF904D
$COLOR_PURPLE = #A457FF
$COLOR_RED    = #FF1C1A

// ----------------------------------------------------------------------------
// MENU: CLIPBOARD
// ----------------------------------------------------------------------------
// Parent Menu
$M_PROCESSES_TITLE = "&Processes"
$M_PROCESSES_ICON  = ["\uE11B", COLOR_BLUE]
//
// Icons
$ICO_PROCESSES_1 = "\uE10E"
$ICO_PROCESSES_2 = "\uE1A3"
//
// Commands / Args
$PS1_PROCESS_CLOSE_SUBLIME = ['powershell', '-Command (Get-Process -ErrorAction SilentlyContinue sublime_text) | Stop-Process -Force']
$PS1_PROCESS_CLOSE_EVERYTHING = ['powershell', '-Command (Get-Process -ErrorAction SilentlyContinue Everything64) | Stop-Process -Force']


$PS1_PROCESS_RESTART_SUBLIME = ['powershell', '-Command $path=(Get-Process -ErrorAction SilentlyContinue sublime_text).Path; Get-Process -ErrorAction SilentlyContinue sublime_text | Stop-Process -Force;  Start-Sleep -Milliseconds 500; if($path) { Start-Process -FilePath $path }']
$PS1_PROCESS_RESTART_EVERYTHING = ['powershell', '-Command $path=(Get-Process -ErrorAction SilentlyContinue Everything64).Path; Get-Process -ErrorAction SilentlyContinue Everything64 | Stop-Process -Force;  Start-Sleep -Milliseconds 1500; if($path) { Start-Process -FilePath $path }']




// $PS1_PROCESS_RESTART_SUBLIME = ['powershell', '-Command $path = (Get-Process sublime_text -ErrorAction SilentlyContinue).Path; Get-Process sublime_text -ErrorAction SilentlyContinue | Stop-Process -Force; Start-Sleep -Seconds 2; if($path) { Start-Process -FilePath $path }']
// powershell -Command "$path = (Get-Process sublime_text -ErrorAction SilentlyContinue).Path; Get-Process sublime_text -ErrorAction SilentlyContinue | Stop-Process -Force; Start-Sleep -Milliseconds 500; if($path) { Start-Process -FilePath $path }"

// powershell -Command "Get-Process sublime_text -ErrorAction SilentlyContinue | Stop-Process -Force; Start-Sleep -Seconds 2; Start-Process -FilePath 'C:\Path\To\Sublime Text\sublime_text.exe'"
// powershell -Command "{ $processPath = (Get-Process sublime_text -ErrorAction SilentlyContinue).Path; Stop-Process -Name sublime_text -Force -ErrorAction SilentlyContinue; Start-Process $processPath }"
// powershell -Command "$path = (Get-Process sublime_text -ErrorAction SilentlyContinue).Path; Get-Process sublime_text -ErrorAction SilentlyContinue | Stop-Process -Force; Start-Sleep -Seconds 2; if($path) { Start-Process -FilePath $path }"
// powershell -Command "$path=(Get-Process -ErrorAction SilentlyContinue Everything64).Path; Get-Process -ErrorAction SilentlyContinue Everything64 | Stop-Process -Force;  Start-Sleep -Milliseconds 1500; if($path) { Start-Process -FilePath $path }"

// $PS1_COPY_FULLPATH    = ['powershell', '-Command @sel("\\\"",",") | % { [System.IO.Path]::GetFullPath($_) } | Set-Clipboard']
// $PS1_COPY_LOCATION    = ['powershell', '-Command @sel("\\\"",",") | % { [System.IO.Path]::GetDirectoryName($_) } | Set-Clipboard']
// $PS1_COPY_FILENAME    = ['powershell', '-Command @sel("\\\"",",") | % { [System.IO.Path]::GetFileName($_) } | Set-Clipboard']
// $PS1_COPY_BASENAME    = ['powershell', '-Command @sel("\\\"",",") | % { [System.IO.Path]::GetFileNameWithoutExtension($_) } | Set-Clipboard']
// $PS1_COPY_EXTENSION   = ['powershell', '-Command @sel("\\\"",",") | % { [System.IO.Path]::GetExtension($_) } | Set-Clipboard']
// $PS1_COPY_FILECONTENT = ['powershell', '-Command @sel("\\\"",",") | % { Get-Content $_ -Raw } | Set-Clipboard']
// $PS1_COPY_DIRCONTENT  = ['powershell', '-Command @sel("\\\"",",") | % { Get-ChildItem $_ -Recurse | Select-Object -ExpandProperty FullName } | Set-Clipboard']
//
// Regex Criterias
// $REGEX_TEXTFILES = "\\.(url|bat|cmd|cpp|csv|git|gitignore|htm|html|ini|js|json|log|mcr|md|ms|py|scene|sql|txt|xls|xlsx|xml|yml|nss|sublime\\w*)$"
//
// Create Menu
menu(type='Taskbar' title=M_PROCESSES_TITLE image=M_PROCESSES_ICON)
{

    item(title = "Force Close Sublime Text"
        window = 'Hidden'
        image  = ["\uE1D6", COLOR_RED]
        cmd    = $PS1_PROCESS_CLOSE_SUBLIME[0]
        args   = $PS1_PROCESS_CLOSE_SUBLIME[1]
    )
    item(title = "Force Close Everything"
        window = 'Hidden'
        image  = ["\uE1D6", COLOR_RED]
        cmd    = $PS1_PROCESS_CLOSE_EVERYTHING[0]
        args   = $PS1_PROCESS_CLOSE_EVERYTHING[1]
    )
    separator
    item(title = "Force Restart Sublime Text"
        window = 'Hidden'
        image  = ["\uE1D6", COLOR_RED]
        cmd    = $PS1_PROCESS_RESTART_SUBLIME[0]
        args   = $PS1_PROCESS_RESTART_SUBLIME[1]
    )
    item(title = "Force Restart Everything"
        window = 'Hidden'
        image  = ["\uE1D6", COLOR_RED]
        cmd    = $PS1_PROCESS_RESTART_EVERYTHING[0]
        args   = $PS1_PROCESS_RESTART_EVERYTHING[1]
    )
    // item(title = "Copy Path"
    //     type   = 'File|Dir'
    //     window = 'Hidden'
    //     image  = [ICO_PROCESSES_1, COLOR_GREEN]
    //     cmd    = PS1_COPY_FULLPATH[0]
    //     args   = PS1_COPY_FULLPATH[1]
    // )
    // item(title = "Copy Location"
    //     type   = 'File|Dir'
    //     window = 'Hidden'
    //     image  = [ICO_PROCESSES_1, COLOR_GREEN]
    //     cmd    = PS1_COPY_LOCATION[0]
    //     args   = PS1_COPY_LOCATION[1]
    // )
    // separator
    // item(title = "Copy Name"
    //     type   = 'File'
    //     window = 'Hidden'
    //     image  = [ICO_PROCESSES_1, COLOR_BLUE]
    //     cmd    = PS1_COPY_FILENAME[0]
    //     args   = PS1_COPY_FILENAME[1]
    // )
    // item(title = "Copy Name"
    //     type   = 'dir'
    //     window = 'Hidden'
    //     image  = [ICO_PROCESSES_1, COLOR_BLUE]
    //     cmd    = PS1_COPY_BASENAME[0]
    //     args   = PS1_COPY_BASENAME[1]
    // )
    // separator
    // item(title = "Copy Extension"
    //     type   = 'File'
    //     window = 'Hidden'
    //     image  = [ICO_PROCESSES_1, COLOR_ORANGE]
    //     cmd    = PS1_COPY_EXTENSION[0]
    //     args   = PS1_COPY_EXTENSION[1]
    // )
    // separator
    // item(title = "Copy Content"
    //     type   = 'File'
    //     window = 'Hidden'
    //     where  = regex.match(sel.file.ext, REGEX_TEXTFILES)
    //     image  = [ICO_PROCESSES_2, COLOR_PURPLE]
    //     cmd    = PS1_COPY_FILECONTENT[0]
    //     args   = PS1_COPY_FILECONTENT[1]
    // )
    // item(title = "Copy Directory Contents"
    //     type   = 'Dir|Back.Dir'
    //     window = 'Hidden'
    //     image  = [ICO_PROCESSES_2, COLOR_PURPLE]
    //     cmd    = PS1_COPY_DIRCONTENT[0]
    //     args   = PS1_COPY_DIRCONTENT[1]
    // )
}