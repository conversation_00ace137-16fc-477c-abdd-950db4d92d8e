
//
$APP_YVONNE_DIR = '@user.desktop\my\flow\home\__GOTO__\Apps\app_yvonne'
$APP_YVONNE_EXE = '@APP_YVONNE_DIR\cursor.exe'
$APP_YVONNE_TIP = "..."+str.trimstart('@APP_YVONNE_EXE','@app.dir')

//
$APP_YVONNE_DIR_CFG = '@user.desktop\my\flow\home\__GOTO__\Apps\app_yvonne\data'
$APP_YVONNE_DIR_NSS = '@app.dir\NSS\_3_items\user_apps'
$APP_YVONNE_DIR_SRC = '@user.desktop\my\flow\home\__GOTO__\Apps\app_yvonne'
$APP_YVONNE_DIR_USR = '@user.desktop\my\flow\home\__GOTO__\Apps\app_yvonne\user'

// Context: Taskbar
item(
    title  = ":  &<PERSON>"
    keys   = "exe"
    type   = 'Taskbar'
    args   = ''
    //
    image  = APP_YVONNE_EXE
    tip    = [APP_YVONNE_TIP,TIP3,1.0]
    //
    admin  = KEYS_EXE_ADMIN // [rightclick] -[ctrl, alt, shift]
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_YVONNE_EXE"')) // -[ctrl, alt, shift]
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_YVONNE_DIR')), // [ctrl + leftclick] -[alt]
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_YVONNE_EXE')), // [ctrl + rightclick] -[alt]
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_YVONNE_DIR')), // [shift + leftclick] -[ctrl, alt]
        //
        cmd=if(KEYS_EXE_GOTO_CFG,('@APP_YVONNE_DIR_CFG')), // [alt + leftclick] -[ctrl, shift]
        cmd=if(KEYS_EXE_GOTO_NSS,('@APP_YVONNE_DIR_NSS')), // [alt + shift + leftclick] -[ctrl]
        cmd=if(KEYS_EXE_GOTO_SRC,('@APP_YVONNE_DIR_SRC')), // [alt + rightclick] -[ctrl, shift]
    }
)
