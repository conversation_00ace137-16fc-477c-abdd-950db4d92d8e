@ECHO OFF
SETLOCAL ENABLEDELAYEDEXPANSION

CALL :fnCmdSetBatchPaths
CALL :DownloadAndInstall
EXIT /B 0

:fnCmdSetBatchPaths
    IF EXIST "%~1" (CD /D "%~1") ELSE (CD /D "%~dp0")
    SET "INIT_BaseName=%~n0"
    SET "INIT_StartPath=%CD%"
    SET "INIT_BatFileName=%~nx0"
    SET "INIT_BatFilePath=%~dp0%~nx0"
    SET "INIT_WindowTitle=%INIT_BaseName%"
GOTO :EOF


:DownloadAndInstall
    SET "downloadLink=https://nilesoft.org/download/shell/1.9.10/setup.exe"
    SET "fileName=setup.exe"

    :: Reference: The Setup program accepts optional command line parameters.
    :NUL
        :: -> /HELP, /?                                     | Shows this information.
        :: -> /SP-                                          | Disables the This will install... Do you wish to continue? prompt at the beginning of Setup.
        :: -> /SILENT, /VERYSILENT                          | Instructs Setup to be silent or very silent.
        :: -> /SUPPRESSMSGBOXES                             | Instructs Setup to suppress message boxes.
        :: -> /LOG                                          | Causes Setup to create a log file in the user's TEMP directory.
        :: -> /LOG="filename"                               | Same as /LOG, except it allows you to specify a fixed path/filename to use for the log file.
        :: -> /NOCANCEL                                     | Prevents the user from cancelling during the installation process.
        :: -> /NORESTART                                    | Prevents Setup from restarting the system following a successful installation, or after a Preparing to Install failure that requests a restart.
        :: -> /RESTARTEXITCODE=exit code                    | Specifies a custom exit code that Setup is to return when the system needs to be restarted.
        :: -> /CLOSEAPPLICATIONS                            | Instructs Setup to close applications using files that need to be updated.
        :: -> /NOCLOSEAPPLICATIONS                          | Prevents Setup from closing applications using files that need to be updated.
        :: -> /FORCECLOSEAPPLICATIONS                       | Instructs Setup to force close when closing applications.
        :: -> /FORCENOCLOSEAPPLICATIONS                     | Prevents Setup from force closing when closing applications.
        :: -> /LOGCLOSEAPPLICATIONS                         | Instructs Setup to create extra logging when closing applications for debugging purposes.
        :: -> /RESTARTAPPLICATIONS                          | Instructs Setup to restart applications.
        :: -> /NORESTARTAPPLICATIONS                        | Prevents Setup from restarting applications.
        :: -> /LOADINF="filename"                           | Instructs Setup to load the settings from the specified file after having checked the command line.
        :: -> /SAVEINF="filename"                           | Instructs Setup to save installation settings to the specified file.
        :: -> /LANG=language                                | Specifies the internal name of the language to use.
        :: -> /DIR="x:\dirname"                             | Overrides the default directory name.
        :: -> /GROUP="folder name"                          | Overrides the default folder name.
        :: -> /NOICONS                                      | Instructs Setup to initially check the Don't create a Start Menu folder check box.
        :: -> /TYPE=type name                               | Overrides the default setup type.
        :: -> /COMPONENTS="comma separated component names" | Overrides the default component settings.
        :: -> /TASKS="comma separated task names"           | Specifies a list of tasks that should be initially selected.
        :: -> /MERGETASKS="comma separated task names"      | Like the /TASKS parameter, except the specified tasks will be merged with the set of tasks that would have otherwise been selected by default.
        :: -> /PASSWORD=password                            | Specifies the password to use.

    ECHO Downloading application...
    PowerShell -Command "Invoke-WebRequest -Uri '%downloadLink%' -OutFile '%fileName%'"

    ECHO Installing application...
    start "" "%fileName%"

    EXIT /b %ERRORLEVEL%
GOTO :EOF
