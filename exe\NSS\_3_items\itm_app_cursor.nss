
//
$APP_CURSOR_DIR = '@user.desktop\my\flow\home\__GOTO__\Apps\app_cursor\exe'
$APP_CURSOR_EXE = '@APP_CURSOR_DIR\cursor.exe'
$APP_CURSOR_TIP = "..."+str.trimstart('@APP_CURSOR_EXE','@app.dir')

//
$APP_CURSOR_DIR_CFG = '@user.desktop\my\flow\home\__GOTO__\Apps\app_cursor\data'
$APP_CURSOR_DIR_NSS = '@app.dir\NSS\_3_items\user_apps'
$APP_CURSOR_DIR_SRC = '@user.desktop\my\flow\home\__GOTO__\Apps\app_cursor'
$APP_CURSOR_DIR_USR = '@user.desktop\my\flow\home\__GOTO__\Apps\app_cursor\user'

// Context: Taskbar
item(
    title  = ":  &Cursor"
    keys   = "exe"
    type   = 'Taskbar'
    args   = ''
    //
    image  = APP_CURSOR_EXE
    tip    = [APP_CURSOR_TIP,TIP3,1.0]
    //
    admin  = KEYS_EXE_ADMIN // [rightclick] -[ctrl, alt, shift]
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_CURSOR_EXE"')) // -[ctrl, alt, shift]
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_CURSOR_DIR')), // [ctrl + leftclick] -[alt]
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_CURSOR_EXE')), // [ctrl + rightclick] -[alt]
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_CURSOR_DIR')), // [shift + leftclick] -[ctrl, alt]
        //
        cmd=if(KEYS_EXE_GOTO_CFG,('@APP_CURSOR_DIR_CFG')), // [alt + leftclick] -[ctrl, shift]
        cmd=if(KEYS_EXE_GOTO_NSS,('@APP_CURSOR_DIR_NSS')), // [alt + shift + leftclick] -[ctrl]
        cmd=if(KEYS_EXE_GOTO_SRC,('@APP_CURSOR_DIR_SRC')), // [alt + rightclick] -[ctrl, shift]
    }
)

// context: directory
item(
    title  = ":  &Cursor"
    keys   = "exe"
    type   = 'Desktop|Dir|Drive|Back.Dir|Back.Drive'
    args   = '"@sel.dir"'
    //
    image  = APP_CURSOR_EXE
    tip    = [APP_CURSOR_TIP,TIP3,0.8]
    //
    admin  = KEYS_EXE_ADMIN // [rightclick] -[ctrl, alt, shift]
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_CURSOR_EXE"')) // -[ctrl, alt, shift]
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_CURSOR_DIR')), // [ctrl + leftclick] -[alt]
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_CURSOR_EXE')), // [ctrl + rightclick] -[alt]
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_CURSOR_DIR')), // [shift + leftclick] -[ctrl, alt]
        //
        cmd=if(KEYS_EXE_GOTO_CFG,('@APP_CURSOR_DIR_CFG')), // [alt + leftclick] -[ctrl, shift]
        cmd=if(KEYS_EXE_GOTO_NSS,('@APP_CURSOR_DIR_NSS')), // [alt + shift + leftclick] -[ctrl]
        cmd=if(KEYS_EXE_GOTO_SRC,('@APP_CURSOR_DIR_SRC')), // [alt + rightclick] -[ctrl, shift]
    }
)
