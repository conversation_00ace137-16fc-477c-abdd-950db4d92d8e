
//
$APP_USER_EVERYTHING_DIR = '@user.desktop\my\flow\home\__GOTO__\Apps\app_everything\exe'
$APP_USER_EVERYTHING_EXE = '@APP_USER_EVERYTHING_DIR\Everything64.exe'
$APP_USER_EVERYTHING_TIP = "..."+str.trimstart('@APP_USER_EVERYTHING_EXE','@app.dir')

//
// $SEARCH_ARGS_GRPEXT = "grp-unrelevant: "

// Context: Explorer
item(
    title  = ":  &Everything"
    keys   = "exe"
    type   = 'File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    args   = '-search "c: path:my/flow <!ext:url;lnk; !ua: !ue: !up: !us:> sfdm: " -filter " ├─ Defaults: DSK" -explore "@sel.dir"'
    // args   = '-search "dm:last30days <!ext:url;lnk; !ua: !ue: !up: !us:> sfdm: " -filter " ├─ Defaults: DSK" -explore "@sel.dir"'
    // args   = '-search "<!ext:url;lnk;> <!ua: !ue: !up: !ur: !us:> mytypes: common: sfdm: <dm:2000.01.01-dm:2026.01.01> " -filter " ├─ Defaults: DSK" -explore "@sel.dir"'
    // args   = '-search "<!ext:url;lnk sort:dm> <dm:last24hours> " -filter " ├─ Defaults: DSK" -explore "@sel.dir"'
    //
    image  = APP_USER_EVERYTHING_EXE
    tip    = [APP_USER_EVERYTHING_TIP,TIP3,0.8]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_USER_EVERYTHING_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_EVERYTHING_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_EVERYTHING_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_EVERYTHING_DIR')),
    }
)
// Context: Taskbar
item(
    title  = ":  &Everything"
    keys   = "exe"
    type   = 'Taskbar'
    // args   = '-search "!ext:lnk " -filter " ├─ Modified: Any Time (filtered)"'
    args   = '-search "<dm:last48hours> <!ext:url;lnk; !ua: !ue: !up: !us:> sfdm: file: " -filter " Everything"'
    //
    image  = APP_USER_EVERYTHING_EXE
    tip    = [APP_USER_EVERYTHING_TIP,TIP3,0.8]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_USER_EVERYTHING_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_EVERYTHING_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_EVERYTHING_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_EVERYTHING_DIR')),
    }
)

// // Context: Taskbar
// item(
//     title  = ":  &Everything"
//     keys   = "exe"
//     type   = 'Taskbar'
//     args   = '-search "!ext:lnk " -filter " ├─ Modified: Any Time (filtered)"'
//     //
//     image  = APP_USER_EVERYTHING_EXE
//     tip    = [APP_USER_EVERYTHING_TIP,TIP3,0.8]
//     //
//     admin  = keys.rbutton()
//     cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_USER_EVERYTHING_EXE"'))
//     commands{
//         cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_EVERYTHING_DIR')),
//         cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_EVERYTHING_EXE')),
//         cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_EVERYTHING_DIR')),
//     }
// )
