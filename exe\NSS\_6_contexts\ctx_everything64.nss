
/* everything64 */
menu(type='*' where=process.name=="Everything64" expanded=true) {

    //
    // item(title="&Open x1 Sublime Text" keys="File" type='*' cmd='@exe_sublime' args='@sel.file' image='@exe_sublime' tip=process.name)
    // item(title="&Open x2 Sublime Text" keys="" type='*' cmd='@exe_sublime' args='"@sel.file"' image='@exe_sublime' tip=process.name)

    //
    import '_3_items/itm_app_sublimetext.nss'
    // import '@app.dir\NSS\_2_setup\_3_items\_1_singles\___user\apps\itm_app_everything.nss'
    // separator()

    // //
    // import '@app.dir/NSS/__5_menus/mnu_user_jorn_apps_everything.nss'
    // import '@app.dir/NSS/_5_menus/mnu_sys_actions_clipboard.nss'
    // separator()

    // //
    // import '@app.dir/NSS/_5_menus/mnu_user_jorn_scripts.nss'
    // separator()

    // //
    // import '@app.dir/NSS/__5_menus/mnu_Goto.nss'
    // import '@app.dir/NSS/__5_menus/mnu_sys_actions_create.nss'
    // separator()



    // //
    // import '@app.dir/NSS/__5_menus/mnu_sys_apps_microsoft.nss'
    // // import '@app.dir/NSS/_5_menus/mnu_user_apps_shell.nss'
    // separator()

    // // //
    // // import '@app.dir/NSS/__5_menus/mnu_sys_apps_microsoft.nss'
    // // separator()

    // // //
    // import '@app.dir/NSS/_5_menus/mnu_user_jorn_apps.nss'
    // import '@app.dir/NSS/_5_menus/mnu_user_jorn_urls.nss'
    // import '@app.dir/NSS/__5_menus/mnu_dirs_user_common.nss'
    // import '@app.dir/NSS/_5_menus/mnu_user_projects.nss'
    // separator()

    // //
    // import '@app.dir/NSS/_5_menus/wip_menus/mnu_Processes.nss'
    // import '@app.dir/NSS/_5_menus/mnu_sys_actions_windows.nss'

    // separator()

    // // items
    // import '@app.dir/NSS/_2_setup/_3_items/itm_this_pc.nss'
    // import '@app.dir/NSS/_2_setup/_3_items/app_cmd.nss'
    // separator()

    // //
    // import '@app.dir/NSS/_3_items/itm_action_sys_showdesktop.nss'
    // import '@app.dir/NSS/_3_items/itm_app_sys_taskmanager.nss'
    //
    // item(title="&Command Prompt" keys="<\\>" admin=ifAdmin image=[E17C,GREY] cmd-line=cmdCwd)
    // item(title="&Show Desktop" admin=ifAdmin pos=-1 image=[E1A0,BLUE] cmd=command.toggle_desktop)
}
