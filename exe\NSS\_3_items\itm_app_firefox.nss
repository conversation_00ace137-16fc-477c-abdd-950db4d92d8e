
//
$APP_FIREFOX_DIR = '@sys.prog\Firefox Developer Edition'
$APP_FIREFOX_EXE = '@APP_FIREFOX_DIR\firefox.exe'
$APP_FIREFOX_TIP = "..."+str.trimstart('@APP_FIREFOX_EXE','@app.dir')
//
$APP_FIREFOX_DIR_CFG = '@user.appdata\Mozilla'
$APP_FIREFOX_DIR_NSS = '@app.dir\NSS\_3_items\user_apps'
$APP_FIREFOX_DIR_SRC = '@user.desktop\my\flow\home\__GOTO__\Apps\app_firefox'

// Context: Taskbar
item(
    title  = ":  &Firefox"
    keys   = "exe"
    type   = 'Taskbar'
    args   = ''
    //
    image  = APP_FIREFOX_EXE
    tip    = [APP_FIREFOX_TIP,TIP3,1.0]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_FIREFOX_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_FIREFOX_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_FIREFOX_EXE')),
        cmd=if(KEYS_EXE_GOTO_CFG,('@APP_FIREFOX_DIR_CFG')),
        cmd=if(KEYS_EXE_GOTO_NSS,('@APP_FIREFOX_DIR_NSS')),
        cmd=if(KEYS_EXE_GOTO_SRC,('@APP_FIREFOX_DIR_SRC')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_FIREFOX_DIR')),
    }
)

