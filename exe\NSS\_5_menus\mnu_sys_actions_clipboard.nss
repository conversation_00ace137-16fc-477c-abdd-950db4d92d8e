
/* menu */


// -> todo: copy siblings (e.g. siblings sharing extenstion | siblings modified current day/week/etc, ...)

// -> copy to clipboard
$ps1_get_base_name = '-Command @sel("\\\"",",") | % {[System.IO.Path]::GetFileNameWithoutExtension($_)} | Set-Clipboard'
$ps1_get_children  = '-Command @sel("\\\"",",") | % {Get-ChildItem $_ -Name} | Set-Clipboard'
$ps1_get_content   = '-Command @sel("\\\"",",") | % {Get-Content $_ -Raw} | Set-Clipboard'
$ps1_get_extension = '-Command @sel("\\\"",",") | % {[System.IO.Path]::GetExtension($_)} | Set-Clipboard'
$ps1_get_file_name = '-Command @sel("\\\"",",") | % {[System.IO.Path]::GetFileName($_)} | Set-Clipboard'
$ps1_get_full_path = '-Command @sel("\\\"",",") | % {[System.IO.Path]::GetFullPath($_)} | Set-Clipboard'
$ps1_get_location  = '-Command @sel("\\\"",",") | % {[System.IO.Path]::GetDirectoryName($_)} | Set-Clipboard'

// -> clipboard
menu(title="&Clipboard" type='Desktop|File|Dir|Drive|Back.Dir|Back.Drive' image=[E03F,WHITE1] image-sel=[E03F,GREEN] sep='None')
{
    //
    item(title="Copy-Location"   keys="clipboard" image=[E0E7,GREEN]  image-sel=[E0E7,HOVER] cmd-ps=ps1_get_location  type='File'                 where=sel.count==1 window='Hidden' sep='None')
    item(title="Copy-Location"   keys="clipboard" image=[E0E7,GREEN]  image-sel=[E0E7,HOVER] cmd-ps=ps1_get_location  type='Desktop|Dir|Drive'    where=sel.count==1 window='Hidden' sep='None')
    //
    item(title="Copy-Filepath"   keys="clipboard" image=[E10E,GREEN]  image-sel=[E10E,HOVER] cmd-ps=ps1_get_full_path  type='File'                where=sel.count==1 window='Hidden' sep='None')
    item(title="Copy-Filepaths"  keys="clipboard" image=[E10E,GREEN]  image-sel=[E10E,HOVER] cmd-ps=ps1_get_full_path  type='File'                where=sel.count>1  window='Hidden' sep='None')
    item(title="Copy-Path"       keys="clipboard" image=[E0E7,GREEN]  image-sel=[E0E7,HOVER] cmd-ps=ps1_get_full_path  type='Back.Dir|Back.Drive' where=sel.count==1 window='Hidden' sep='None')
    item(title="Copy-Path"       keys="clipboard" image=[E0E7,GREEN]  image-sel=[E0E7,HOVER] cmd-ps=ps1_get_full_path  type='Desktop|Dir|Drive'   where=sel.count==1 window='Hidden' sep='None')
    item(title="Copy-Paths"      keys="clipboard" image=[E0E7,GREEN]  image-sel=[E0E7,HOVER] cmd-ps=ps1_get_full_path  type='Desktop|Dir|Drive'   where=sel.count>1  window='Hidden' sep='None')
    //
    separator()
    //
    item(title="Copy-Filename"   keys="clipboard" image=[E10E,BLUE]   image-sel=[E10E,HOVER] cmd-ps=ps1_get_file_name  type='File'                where=sel.count==1 window='Hidden' sep='None')
    item(title="Copy-Filenames"  keys="clipboard" image=[E10E,BLUE]   image-sel=[E10E,HOVER] cmd-ps=ps1_get_file_name  type='File'                where=sel.count>1  window='Hidden' sep='None')
    item(title="Copy-Name"       keys="clipboard" image=[E0E7,BLUE]   image-sel=[E0E7,HOVER] cmd-ps=ps1_get_file_name  type='Back.Dir|Back.Drive' where=sel.count==1 window='Hidden' sep='None')
    item(title="Copy-Name"       keys="clipboard" image=[E0E7,BLUE]   image-sel=[E0E7,HOVER] cmd-ps=ps1_get_file_name  type='Desktop|Dir|Drive'   where=sel.count==1 window='Hidden' sep='None')
    item(title="Copy-Names"      keys="clipboard" image=[E0E7,BLUE]   image-sel=[E0E7,HOVER] cmd-ps=ps1_get_file_name  type='Desktop|Dir|Drive'   where=sel.count>1  window='Hidden' sep='None')
    //
    item(title="Copy-Basename"   keys="clipboard" image=[E10E,BLUE]   image-sel=[E10E,HOVER] cmd-ps=ps1_get_base_name  type='File'                where=sel.count==1 window='Hidden' sep='None')
    item(title="Copy-Basenames"  keys="clipboard" image=[E10E,BLUE]   image-sel=[E10E,HOVER] cmd-ps=ps1_get_base_name  type='File'                where=sel.count>1  window='Hidden' sep='None')
    //
    item(title="Copy-Extension"  keys="clipboard" image=[E10E,BLUE]   image-sel=[E10E,HOVER] cmd-ps=ps1_get_extension type='File'                 where=sel.count==1 window='Hidden' sep='None')
    item(title="Copy-Extensions" keys="clipboard" image=[E10E,BLUE]   image-sel=[E10E,HOVER] cmd-ps=ps1_get_extension type='File'                 where=sel.count>1  window='Hidden' sep='None')
    //
    separator()
    //
    item(title="Copy-Content"    keys="clipboard" image=[E10E,YELLOW] image-sel=[E10E,HOVER] cmd-ps=ps1_get_content   type='File'                 where=sel.count==1 window='Hidden' sep='None')
    item(title="Copy-Contents"   keys="clipboard" image=[E10E,YELLOW] image-sel=[E10E,HOVER] cmd-ps=ps1_get_content   type='File'                 where=sel.count>1  window='Hidden' sep='None')
    //
    separator()
    //
    item(title="Copy-Children"   keys="clipboard" image=[E1A3,YELLOW] image-sel=[E1A3,HOVER] cmd-ps=ps1_get_children  type='Back.Dir|Back.Drive'  where=sel.count==1 window='Hidden' sep='None')
    item(title="Copy-Children"   keys="clipboard" image=[E1A3,YELLOW] image-sel=[E1A3,HOVER] cmd-ps=ps1_get_children  type='Desktop|Dir|Drive'    where=sel.count==1 window='Hidden' sep='None')
    item(title="Copy-Childrens"  keys="clipboard" image=[E1A3,YELLOW] image-sel=[E1A3,HOVER] cmd-ps=ps1_get_children  type='Dir'                  where=sel.count>1  window='Hidden' sep='None')
}