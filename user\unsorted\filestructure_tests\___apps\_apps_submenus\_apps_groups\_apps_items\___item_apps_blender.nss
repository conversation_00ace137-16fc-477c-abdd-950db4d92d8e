
/* item: singles/apps/blender */

//
$APP_BLENDER_DIR = '@app.dir\PORTAL\APPS\app_blender\exe\blender-4.1.1-windows-x64'
$APP_BLENDER_EXE = '@APP_BLENDER_DIR\blender.exe'
$APP_BLENDER_TIP = "..."+str.trimstart('@APP_BLENDER_EXE','@app.dir')

// context: directory
item(
    title  = ":  &Blender"
    keys   = "exe"
    type   = 'Desktop|Dir|Drive|Back.Dir|Back.Drive'
    args   = '--local="@sel.dir"'
    //
    image  = APP_BLENDER_EXE
    tip    = [APP_BLENDER_TIP,TIP3,1.0]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_BLENDER_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_BLENDER_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_BLENDER_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_BLENDER_DIR')),
    }
)
// Context: Taskbar
item(
    title  = ":  &Blender"
    keys   = "exe"
    type   = 'Taskbar'
    args   = ''
    //
    image  = APP_BLENDER_EXE
    tip    = [APP_BLENDER_TIP,TIP3,1.0]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_BLENDER_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_BLENDER_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_BLENDER_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_BLENDER_DIR')),
    }
)
