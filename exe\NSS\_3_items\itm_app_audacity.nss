
//
$APP_AUDACITY_DIR = '@user.desktop\my\flow\home\__GOTO__\Apps\app_audacity\exe\audacity-win-3.7.1-64bit'
$APP_AUDACITY_EXE = '@APP_AUDACITY_DIR\Audacity.exe'
$APP_AUDACITY_TIP = "..."+str.trimstart('@APP_AUDACITY_EXE','@app.dir')
//
$APP_AUDACITY_DIR_CFG = '@user.appdata\audacity'
$APP_AUDACITY_DIR_NSS = '@app.dir\NSS\_3_items\user_apps'
$APP_AUDACITY_DIR_SRC = '@user.desktop\my\flow\home\__GOTO__\Apps\app_audacity'

// Context: File
item(
    title  = ":  &Audacity"
    keys   = "exe"
    type   = 'File'
    args   = '"@sel.file"'
    where  = str.equals(sel.file.ext,[".aac",".flac",".m4a",".mp3",".ogg",".wav",".wma"])
    //
    image  = APP_AUDACITY_EXE
    tip    = [APP_AUDACITY_TIP,TIP3,1.0]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_AUDACITY_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_AUDACITY_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_AUDACITY_EXE')),
        cmd=if(KEYS_EXE_GOTO_CFG,('@APP_AUDACITY_DIR_CFG')),
        cmd=if(KEYS_EXE_GOTO_NSS,('@APP_AUDACITY_DIR_NSS')),
        cmd=if(KEYS_EXE_GOTO_SRC,('@APP_AUDACITY_DIR_SRC')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_AUDACITY_DIR')),
    }
)
// Context: Directory
item(
    title  = ":  &Audacity"
    keys   = "exe"
    type   = 'Desktop|Dir|Drive|Back.Dir|Back.Drive'
    args   = '--local="@sel.dir"'
    //
    image  = APP_AUDACITY_EXE
    tip    = [APP_AUDACITY_TIP,TIP3,1.0]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_AUDACITY_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_AUDACITY_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_AUDACITY_EXE')),
        cmd=if(KEYS_EXE_GOTO_CFG,('@APP_AUDACITY_DIR_CFG')),
        cmd=if(KEYS_EXE_GOTO_NSS,('@APP_AUDACITY_DIR_NSS')),
        cmd=if(KEYS_EXE_GOTO_SRC,('@APP_AUDACITY_DIR_SRC')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_AUDACITY_DIR')),
    }
)
// Context: Taskbar
item(
    title  = ":  &Audacity"
    keys   = "exe"
    type   = 'Taskbar'
    args   = ''
    //
    image  = APP_AUDACITY_EXE
    tip    = [APP_AUDACITY_TIP,TIP3,1.0]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_AUDACITY_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_AUDACITY_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_AUDACITY_EXE')),
        cmd=if(KEYS_EXE_GOTO_CFG,('@APP_AUDACITY_DIR_CFG')),
        cmd=if(KEYS_EXE_GOTO_NSS,('@APP_AUDACITY_DIR_NSS')),
        cmd=if(KEYS_EXE_GOTO_SRC,('@APP_AUDACITY_DIR_SRC')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_AUDACITY_DIR')),
    }
)

