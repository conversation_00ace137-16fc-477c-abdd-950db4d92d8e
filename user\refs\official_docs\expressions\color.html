﻿<h4>Color literal</h4>
<br>
<h5>Hexadecimal Colors</h5>
<p>interprets color constants as hexadecimal if they are preceded by <code>#</code> and hexadecimal color is specified with: 
<code>#RRGGBB</code> or <code>#RRGGBBAA</code>, where the RR (red), GG (green) and BB (blue)and AA (alpha) hexadecimal integers specify the components of the color. All values must be between <code>00</code> and <code>FF</code>.<br/>For example, the <code>#0000FF</code> value is rendered as blue, because the blue component is set to its highest value (<code>FF</code>) and the others are set to <code>00</code>.<br/>There are 140 color names are predefined under the <a href="/docs/functions/color">color</a> scope.</p>