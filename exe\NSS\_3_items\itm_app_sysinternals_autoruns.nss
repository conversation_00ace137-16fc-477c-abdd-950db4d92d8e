
//
$APP_USER_AUTORUNS_DIR = '@user.desktop\my\flow\home\__GOTO__\Apps\grp_sysinternalssuite\app_autoruns\exe'
$APP_USER_AUTORUNS_EXE = '@APP_USER_AUTORUNS_DIR\Autoruns64.exe'
$APP_USER_AUTORUNS_TIP = "..."+str.trimstart('@APP_USER_AUTORUNS_EXE','@app.dir')

//
item(
    title=":  &Autoruns"
    keys="exe"
    type='Taskbar|File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    //
    image=APP_USER_AUTORUNS_EXE
    tip=[APP_USER_AUTORUNS_TIP,TIP3,0.5]
    //
    admin=keys.rbutton()
    cmd=if(KEYS_EXE_OPEN_EXE,(APP_USER_AUTORUNS_EXE))
    args=''
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_AUTORUNS_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_AUTORUNS_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_AUTORUNS_DIR')),
    }
)
