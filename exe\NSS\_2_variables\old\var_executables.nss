
/* executables */


/*
    usage:
    :: cmd='@exe_apps_fldr'
    :: cmd='@exe_control_all'
    :: ...
*/


// -> microsoft shell commands
$exe_apps_fldr    = '"shell:appsfolder"'
$exe_control_all  = '"shell:::{5399e694-6ce5-4d6c-8fce-1d8870fdcba0}"'
$exe_net_conns    = '"shell:::{7007acc7-3202-11d1-aad2-00805fc1270e}"'
$exe_run          = '"shell:::{2559a1f3-21d7-11d4-bdaf-00c04f60b9f0}"'


// -> windows settings shortcuts
$exe_defender     = '"ms-settings:windowsdefender"'
$exe_lang_conf    = '"ms-settings:keyboard"'
$exe_sys_conf     = '"ms-settings:System Configuration"'
$exe_taskbar_conf = '"ms-settings:taskbar"'


// -> system management consoles
$exe_comp_mgmt    = '"@sys.dir\System32\compmgmt.msc"'
$exe_dev_mgmt     = '"@sys.dir\System32\devmgmt.msc"'
$exe_disk_mgmt    = '"@sys.dir\System32\diskmgmt.msc"'
$exe_event_vwr    = '"@sys.dir\System32\eventvwr.msc"'
$exe_perf_mon     = '"@sys.dir\System32\perfmon.msc"'
$exe_print_mgmt   = '"@sys.dir\System32\printmanagement.msc"'
$exe_services     = '"@sys.dir\System32\services.msc"'
$exe_shared_fldrs = '"@sys.dir\System32\fsmgmt.msc"'
$exe_task_schd    = '"@sys.dir\System32\taskschd.msc"'
$exe_user_mgmt    = '"@sys.dir\System32\lusrmgr.msc"'


// -> control panel utilities
$exe_display      = '"@sys.dir\System32\desk.cpl"'
$exe_firewall     = '"@sys.dir\System32\firewall.cpl"'
$exe_language     = '"ms-settings:regionlanguage-languageoptions"'
$exe_net_adapters = '"@sys.dir\System32\ncpa.cpl"'
$exe_power_opts   = '"@sys.dir\System32\powercfg.cpl"'
$exe_programs     = '"@sys.dir\System32\appwiz.cpl"'
$exe_region       = '"@sys.dir\System32\intl.cpl"'
$exe_sound        = '"@sys.dir\System32\mmsys.cpl"'
$exe_sys_props    = '"@sys.dir\System32\sysdm.cpl"'


// -> common windows applications
$exe_calc         = '"@sys.dir\System32\calc.exe"'
$exe_charmap      = '"@sys.dir\System32\charmap.exe"'
$exe_cmd          = '"@sys.dir\System32\cmd.exe"'
$exe_control      = '"@sys.dir\System32\control.exe"'
$exe_ise          = '"@sys.dir\System32\WindowsPowerShell\v1.0\powershell_ise.exe"'
$exe_magnify      = '"@sys.dir\System32\magnify.exe"'
$exe_mspaint      = '"mspaint.exe"'
$exe_notepad      = '"@sys.dir\System32\notepad.exe"'
$exe_osk          = '"@sys.dir\System32\osk.exe"'
$exe_powershell   = '"@sys.dir\System32\WindowsPowerShell\v1.0\powershell.exe"'
$exe_regedit      = '"@sys.dir\regedit.exe"'
$exe_res_mon      = '"@sys.dir\System32\resmon.exe"'
$exe_task_mgr     = '"@sys.dir\System32\taskmgr.exe"'


// -> third party
$exe_3dsmax         = '"@sys.prog\Autodesk\3ds Max 2025\3dsmax.exe"'
$exe_chrome         = '"@sys.prog\Google\Chrome\Application\chrome.exe"'
$exe_discord        = '"@DIR_SYS_LOCALAPPDATA\Discord\app-1.0.9147\Discord.exe"'
$exe_dremel3dslicer = '"@sys.prog\Dremel DigiLab 3D Slicer\Dremel3DSlicer.exe"'
$exe_illustrator    = '"@sys.prog\Adobe\Adobe Illustrator 2024\Support Files\Contents\Windows\Illustrator.exe"'
$exe_photoshop      = '"@sys.prog\Adobe\Adobe Photoshop 2024\Photoshop.exe"'
$exe_sourcetree     = '"@DIR_SYS_LOCALAPPDATA\SourceTree\SourceTree.exe"'
$exe_vscode         = '"@sys.prog\Microsoft VS Code\Code.exe"'


// -> third party portable
$exe_audacity            = '"@user.desktop\my\flow\home\__GOTO__\Apps\app_audacity\exe\Audacity.exe"'
$exe_blender             = '"@user.desktop\my\flow\home\__GOTO__\Apps\app_blender\exe\blender-4.1.1-windows-x64\blender.exe"'
// $exe_blender             = '"@user.desktop\my\flow\home\__GOTO__\Apps\app_blender\exe\blender-4.2.0-alpha-windows-x64\blender.exe"'
$exe_bulkrename          = '"@user.desktop\my\flow\home\__GOTO__\Apps\app_bulkrenameutility\exe\64-bit\Bulk Rename Utility.exe"'
$exe_everything          = '"@user.desktop\my\flow\home\__GOTO__\Apps\app_everything\exe\Everything64.exe"'
$exe_filezilla           = '"@user.desktop\my\flow\home\__GOTO__\Apps\app_filezilla\exe\filezilla.exe"'
$app_greenshot           = '"@user.desktop\my\flow\home\__GOTO__\Apps\app_greenshot\app\Greenshot.exe"'
$exe_nilesoftshell       = '"@user.desktop\my\flow\home\__GOTO__\Apps\exe_nilesoftshell\app\shell.exe"'
$exe_notepad_plus        = '"@user.desktop\my\flow\home\__GOTO__\Apps\app_notepad++\exe\notepad++.exe"'
$exe_python              = '"@app.dir\PORTAL\PY\venv\Scripts\python.exe"'
$exe_qbittorrent         = '"@user.desktop\my\flow\home\__GOTO__\Apps\app_qbittorrent\exe\qbittorrent-portable.exe"'
$exe_sublime             = '"@user.desktop\my\flow\home\__GOTO__\Apps\exe_sublimetext\exe\sublime_text.exe"'
$exe_vlc                 = '"@user.desktop\my\flow\home\__GOTO__\Apps\app_vlc\exe\vlc.exe"'
$exe_vscodium            = '"@user.desktop\my\flow\home\__GOTO__\Apps\app_vscodium\exe\VSCodium.exe"'
$exe_winaerotweaker      = '"@user.desktop\my\flow\home\__GOTO__\Apps\app_winaerotweaker\exe\WinaeroTweaker.exe"'
$exe_winmerge            = '"@user.desktop\my\flow\home\__GOTO__\Apps\app_winmerge\exe\WinMergeU.exe"'
// -> nirsoft utils
$exe_registrychangesview = '"@user.desktop\my\flow\home\__GOTO__\Apps\grp_nirsoft\app_registrychangesview\exe\RegistryChangesView.exe"'




// - TODO: URL-FROM-CLIPBOARD (in file explorer, autogenerates filename from url in clipboard).
// - TODO: PY-SCRIPT FOR GENERATING NSS MENUS (e.g. based on AppLibrary)
// - TODO: BACKUP REVISION OF SPECIFIC TEXTFILES




// $exe_bulk_rename  = '"@sys.prog/Bulk Rename Utility/Bulk Rename Utility.exe"'
// $exe_everything   = '"@sys.prog/Everything 1.5a/Everything64.exe"'
// $exe_winmerge     = '"@sys.prog/WinMergeU.exe"'
// $exe_winmerge     = '"@user.profile/Sync/JORN/UTILS/WinMerge/portable/WinMergeU.exe"'
// $exe_credentials  = '""@sys.dir/System32/control.exe" /name Microsoft.CredentialManager"'

// OLD
// $exe_bulkrename  = '"@user.profile/Sync/JORN/UTILS/BulkRenameUtility/64-bit/Bulk Rename Utility.exe"'
