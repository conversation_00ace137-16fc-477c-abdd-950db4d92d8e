
//
$APP_SUBLIMEMERGE_NSS = '@app.dir\NSS\_3_items\user_apps'
$APP_SUBLIMEMERGE_SRC = '@user.desktop\my\flow\home\__GOTO__\Apps\app_sublimemerge'
//
$APP_USER_SUBLIMEMERGE_DIR = '@user.desktop\my\flow\home\__GOTO__\Apps\app_sublimemerge\exe'
$APP_USER_SUBLIMEMERGE_EXE = '@APP_USER_SUBLIMEMERGE_DIR\sublime_merge.exe'
$APP_USER_SUBLIMEMERGE_TIP = "..."+str.trimstart('@APP_USER_SUBLIMEMERGE_EXE','@app.dir')

// -> context: file
item(
    title  = ":  &Sublime Merge"
    keys   = "exe"
    type   = 'File'
    args   = '"@sel.file"'
    where  = !str.equals(sel.file.ext,[".7z",".zip",".rar",".apk",".bin",".dll",".dmg",".exe",".sys"])
    //
    image  = APP_USER_SUBLIMEMERGE_EXE
    tip    = [APP_USER_SUBLIMEMERGE_TIP,TIP3,0.8]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_USER_SUBLIMEMERGE_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_SUBLIMEMERGE_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_SUBLIMEMERGE_EXE')),
        cmd=if(KEYS_EXE_GOTO_NSS,('@APP_SUBLIMEMERGE_NSS')),
        cmd=if(KEYS_EXE_GOTO_SRC,('@APP_SUBLIMEMERGE_SRC')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_SUBLIMEMERGE_DIR')),
    }
)
// context: directory
item(
    title  = ":  &Sublime Merge"
    keys   = "exe"
    type   = 'Desktop|Dir|Drive|Back.Dir|Back.Drive'
    args   = '"@sel.dir"'
    //
    image  = APP_USER_SUBLIMEMERGE_EXE
    tip    = [APP_USER_SUBLIMEMERGE_TIP,TIP3,0.8]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_USER_SUBLIMEMERGE_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_SUBLIMEMERGE_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_SUBLIMEMERGE_EXE')),
        cmd=if(KEYS_EXE_GOTO_NSS,('@APP_SUBLIMEMERGE_NSS')),
        cmd=if(KEYS_EXE_GOTO_SRC,('@APP_SUBLIMEMERGE_SRC')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_SUBLIMEMERGE_DIR')),
    }
)
// Context: Taskbar
item(
    title  = ":  &Sublime Merge"
    keys   = "exe"
    type   = 'Taskbar'
    args   = ''
    //
    image  = APP_USER_SUBLIMEMERGE_EXE
    tip    = [APP_USER_SUBLIMEMERGE_TIP,TIP3,0.8]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_USER_SUBLIMEMERGE_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_SUBLIMEMERGE_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_SUBLIMEMERGE_EXE')),
        cmd=if(KEYS_EXE_GOTO_NSS,('@APP_SUBLIMEMERGE_NSS')),
        cmd=if(KEYS_EXE_GOTO_SRC,('@APP_SUBLIMEMERGE_SRC')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_SUBLIMEMERGE_DIR')),
    }
)
