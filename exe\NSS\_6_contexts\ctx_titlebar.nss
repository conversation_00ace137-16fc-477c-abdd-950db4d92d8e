
/* titlebar */
menu(type='Titlebar' expanded=true) {

    import '@app.dir/NSS/_5_menus/mnu_user_jorn_scripts.nss'
    separator()

    // [*]
    import '@app.dir/NSS/_5_menus/mnu_sys_actions_windows.nss'
    separator()

    // [shift OR ctrl+shift]
    menu(vis='@KEYS_MNU_VISIBILITY_3_SHIFT_OR_CTRL_SHIFT' expanded=true) {
        import '@app.dir/NSS/_5_menus/mnu_user_apps_shell.nss'
        separator()
        import '@app.dir/NSS/_5_menus/mnu_sys_debug_commands.nss'
        separator()
        import '@app.dir/NSS/_5_menus/wip_menus/mnu_Processes.nss'
        separator()
    }
}

/* move default items to bottom */
modify(pos='Bottom' sep='None' type='Titlebar' in='/' where=(
        this.id(
            id.close,
            id.maximize,
            id.minimize,
            id.move,
            id.size)))
