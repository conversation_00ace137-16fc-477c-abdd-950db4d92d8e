
//
$APP_SYS_MSPAINT_DIR = '@sys.dir\System32'
$APP_SYS_MSPAINT_EXE = 'mspaint.exe'
$APP_SYS_MSPAINT_TIP = '@APP_SYS_MSPAINT_EXE'

// Context: Explorer
item(
    title  = ":  &Mspaint"
    keys   = "exe"
    type   = 'File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    //
    image  = [E116,LOWKEY] image-sel = [E116,HOVER]
    tip    = [APP_SYS_MSPAINT_TIP,TIP3,0.5]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('mspaint.exe'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_SYS_MSPAINT_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_SYS_MSPAINT_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_SYS_MSPAINT_DIR')),
    }
)
// Context: Taskbar
item(
    title  = ":  &Mspaint"
    keys   = "exe"
    type   = 'Taskbar'
    //
    image  = [E116,LOWKEY] image-sel = [E116,HOVER]
    tip    = [APP_SYS_MSPAINT_TIP,TIP3,0.5]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('mspaint.exe'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_SYS_MSPAINT_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_SYS_MSPAINT_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_SYS_MSPAINT_DIR')),
    }
)