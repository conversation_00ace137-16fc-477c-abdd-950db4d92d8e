# Dir `bat`

### File Structure

```
├── py_venv_execute.bat
├── py_venv_git_commit_all.bat
├── py_venv_pip_install.bat
├── py_venv_terminal.bat
├── py_venv_upgrade_requirements.bat
└── py_venv_write_requirements.bat
```

---

#### `py_venv_execute.bat`

```batch
    :: =============================================================================
    :: cmd: initialize
    :: =============================================================================
    @ECHO OFF
    SETLOCAL ENABLEEXTENSIONS ENABLEDELAYEDEXPANSION
    IF EXIST "%~1" (CD /D "%~1") ELSE (CD /D "%~dp0")
    SET "__init_path__=%CD%"
    SET "__base_name__=%~n0"
    SET "__venv_name__=venv"
    
    
    :: =============================================================================
    :: venv: locate
    :: =============================================================================
    SET "__venv_identifier__=%__venv_name__%\Scripts\python.exe"
    :LocateVenv
        IF EXIST "%CD%\%__venv_identifier__%" (GOTO ActivateVenv)
        SET "tmp=%CD%" & CD .. & IF "%CD%"=="%tmp%" (
            ECHO venv not found
            CD /D "%__init_path__%"
            PAUSE>NUL & EXIT /B
        )
    GOTO LocateVenv
    
    
    :: =============================================================================
    :: venv: activate
    :: =============================================================================
    :ActivateVenv
        SET "__venv_stem__=%CD%"
        CD /D "%__init_path__%"
        CALL "%__venv_stem__%\%__venv_name__%\Scripts\activate"
        ::
        ECHO __init_path__: %__init_path__%
        ECHO __venv_stem__: %__venv_stem__%
        ECHO.
        GOTO ExecuteCommand
    
    
    :: =============================================================================
    :: file: execute
    :: =============================================================================
    :ExecuteCommand
        SET "__script_name__=%__base_name__%.py"
        IF EXIST "%~2" (SET "__script_name__=%~2")
        python %__script_name__%
        :: python %__base_name__%.py --prompt
    
    
    :: =============================================================================
    :: cmd: re-execute
    :: =============================================================================
    ECHO. & ECHO Press a key to re-execute script ... & PAUSE > NUL & CLS
    GOTO :ActivateVenv
```

---

#### `py_venv_git_commit_all.bat`

```batch
    :: =============================================================================
    :: cmd: initialize
    :: =============================================================================
    @ECHO OFF
    SETLOCAL ENABLEEXTENSIONS ENABLEDELAYEDEXPANSION
    IF EXIST "%~1" (CD /D "%~1") ELSE (CD /D "%~dp0")
    SET "__init_path__=%CD%"
    SET "__base_name__=%~n0"
    SET "__venv_name__=venv"
    
    :: =============================================================================
    :: venv: locate
    :: =============================================================================
    SET "__venv_identifier__=%__venv_name__%\Scripts\python.exe"
    :LocateVenv
        IF EXIST "%CD%\%__venv_identifier__%" (GOTO ActivateVenv)
        SET "tmp=%CD%" & CD .. & IF "%CD%"=="%tmp%" (
            ECHO venv not found
            CD /D "%__init_path__%"
            PAUSE>NUL & EXIT /B
        )
    GOTO LocateVenv
    
    
    :: =============================================================================
    :: venv: activate
    :: =============================================================================
    :ActivateVenv
        SET "__venv_stem__=%CD%"
        CD /D "%__init_path__%"
        CALL "%__venv_stem__%\%__venv_name__%\Scripts\activate"
        ::
        ECHO __init_path__: %__init_path__%
        ECHO __venv_stem__: %__venv_stem__%
        ECHO.
        GOTO ExecuteCommand
    
    
    :: =============================================================================
    :: cmd: terminal
    :: =============================================================================
    :ExecuteCommand
        SET "__venv_activate__=%__venv_stem__%\%__venv_name__%\Scripts\activate"
        CMD /K ""%__venv_activate__%" & TITLE ^(venv^) %__base_name__%"
    
    
    :: =============================================================================
    :: cmd: exit
    :: =============================================================================
    ECHO. & ECHO Window will close in 3 seconds ...& PING 127.0.0.1 -n 3 > NUL
    EXIT /B
```

---

#### `py_venv_pip_install.bat`

```batch
    :: =============================================================================
    :: cmd: initialize
    :: =============================================================================
    @ECHO OFF
    SETLOCAL ENABLEEXTENSIONS ENABLEDELAYEDEXPANSION
    IF EXIST "%~1" (CD /D "%~1") ELSE (CD /D "%~dp0")
    SET "__init_path__=%CD%"
    SET "__base_name__=%~n0"
    SET "__venv_name__=venv"
    
    
    :: =============================================================================
    :: venv: locate
    :: =============================================================================
    SET "__venv_identifier__=%__venv_name__%\Scripts\python.exe"
    :LocateVenv
        IF EXIST "%CD%\%__venv_identifier__%" (GOTO ActivateVenv)
        SET "tmp=%CD%" & CD .. & IF "%CD%"=="%tmp%" (
            ECHO Not found: %__venv_identifier__%
            ECHO make sure you've initialized the venv.
            CD /D "%__init_path__%"
            PAUSE>NUL & EXIT /B
        )
    GOTO LocateVenv
    
    
    :: =============================================================================
    :: venv: activate
    :: =============================================================================
    :ActivateVenv
        SET "__venv_stem__=%CD%"
        CD /D "%__init_path__%"
        CALL "%__venv_stem__%\%__venv_name__%\Scripts\activate"
        ::
        ECHO __init_path__: %__init_path__%
        ECHO __venv_stem__: %__venv_stem__%
        ECHO.
        GOTO ExecuteCommand
    
    
    :: =============================================================================
    :: venv: install/remove package
    :: =============================================================================
    :ExecuteCommand
        "pip" list & ECHO.
        ECHO Enter the package name (optionally followed by ' r' to remove):
        SET /P py_package="Package Name: " & ECHO.
        FOR /F "tokens=1,2" %%A IN ("%py_package%") DO (
            SET "package_name=%%A"
            SET "modifier=%%B"
        )
        SET "pip_command=install %package_name%"
        IF /I "%modifier%"=="r" SET "pip_command=uninstall %package_name% -y"
        ECHO Executing 'pip %pip_command%'...
        "python" -m pip %pip_command%
    
    
    :: =============================================================================
    :: requirements.txt: write
    :: =============================================================================
    SET "requirements_txt=%__venv_stem__%\requirements.txt"
    IF NOT ERRORLEVEL 1 ("pip" freeze > %requirements_txt%) ELSE (
        ECHO Failed to write  %requirements_txt%
    )
    
    
    :: =============================================================================
    :: cmd: re-execute
    :: =============================================================================
    ECHO. & ECHO Press a key to re-execute script ... & PAUSE > NUL & CLS
    GOTO :ActivateVenv
```

---

#### `py_venv_terminal.bat`

```batch
    :: =============================================================================
    :: cmd: initialize
    :: =============================================================================
    @ECHO OFF
    SETLOCAL ENABLEEXTENSIONS ENABLEDELAYEDEXPANSION
    IF EXIST "%~1" (CD /D "%~1") ELSE (CD /D "%~dp0")
    SET "__init_path__=%CD%"
    SET "__base_name__=%~n0"
    SET "__venv_name__=venv"
    
    
    :: =============================================================================
    :: venv: locate
    :: =============================================================================
    SET "__venv_identifier__=%__venv_name__%\Scripts\python.exe"
    :LocateVenv
        IF EXIST "%CD%\%__venv_identifier__%" (GOTO ActivateVenv)
        SET "tmp=%CD%" & CD .. & IF "%CD%"=="%tmp%" (
            ECHO venv not found
            CD /D "%__init_path__%"
            PAUSE>NUL & EXIT /B
        )
    GOTO LocateVenv
    
    
    :: =============================================================================
    :: venv: activate
    :: =============================================================================
    :ActivateVenv
        SET "__venv_stem__=%CD%"
        CD /D "%__init_path__%"
        CALL "%__venv_stem__%\%__venv_name__%\Scripts\activate"
        ::
        ECHO __init_path__: %__init_path__%
        ECHO __venv_stem__: %__venv_stem__%
        ECHO.
        GOTO ExecuteCommand
    
    
    :: =============================================================================
    :: cmd: terminal
    :: =============================================================================
    :ExecuteCommand
        SET "__venv_activate__=%__venv_stem__%\%__venv_name__%\Scripts\activate"
        CMD /K "@ECHO OFF" & "%__venv_activate__%" & TITLE ^(venv^) %__base_name__%"
    
    
    :: =============================================================================
    :: cmd: exit
    :: =============================================================================
    ECHO. & ECHO Window will close in 10 seconds ...& PING 127.0.0.1 -n 10 > NUL
    EXIT /B
```

---

#### `py_venv_upgrade_requirements.bat`

```batch
    :: =============================================================================
    :: cmd: initialize
    :: =============================================================================
    @ECHO OFF
    SETLOCAL ENABLEEXTENSIONS ENABLEDELAYEDEXPANSION
    IF EXIST "%~1" (CD /D "%~1") ELSE (CD /D "%~dp0")
    SET "__init_path__=%CD%"
    SET "__base_name__=%~n0"
    SET "__venv_name__=venv"
    
    
    :: =============================================================================
    :: venv: locate
    :: =============================================================================
    SET "__venv_identifier__=%__venv_name__%\Scripts\python.exe"
    :LocateVenv
        IF EXIST "%CD%\%__venv_identifier__%" (GOTO ActivateVenv)
        SET "tmp=%CD%" & CD .. & IF "%CD%"=="%tmp%" (
            ECHO Not found: %__venv_identifier__%
            ECHO make sure you've initialized the venv.
            CD /D "%__init_path__%"
            PAUSE>NUL & EXIT /B
        )
    GOTO LocateVenv
    
    
    :: =============================================================================
    :: venv: activate
    :: =============================================================================
    :ActivateVenv
        SET "__venv_stem__=%CD%"
        CD /D "%__init_path__%"
        CALL "%__venv_stem__%\%__venv_name__%\Scripts\activate"
        ::
        ECHO __init_path__: %__init_path__%
        ECHO __venv_stem__: %__venv_stem__%
        ECHO.
        GOTO ExecuteCommand
    
    
    :ExecuteCommand
        :: =============================================================================
        :: requirements.txt: upgrade
        :: =============================================================================
        SET requirements_txt="%__venv_stem__%\requirements.txt"
        IF EXIST "%requirements_txt%" (
            ECHO. & ECHO Do you want to upgrade the packages? ^(press 'y' to upgrade...^)
            SET /P upgrade_packages=">> "
            IF "!upgrade_packages!"=="y" (
                "python" -m pip install --upgrade pip
                "python" -m pip install --upgrade -r "%requirements_txt%" -vvv
                IF NOT ERRORLEVEL 1 ("pip" freeze > "%requirements_txt%") ELSE (
                    ECHO Failed to upgrade packages from "%requirements_txt%"
                )
            )
        )
    
        :: =============================================================================
        :: requirements.txt: write
        :: =============================================================================
        SET requirements_txt="%__venv_stem__%\requirements.txt"
        SET requirements_txt="%__venv_stem__%\requirements.txt"
        IF NOT ERRORLEVEL 1 ("pip" freeze > %requirements_txt%) ELSE (
            ECHO wailed to write  %requirements_txt%
        )
    
    
    :: =============================================================================
    :: cmd: exit
    :: =============================================================================
    ECHO. & ECHO Window will close in 10 seconds ...& PING 127.0.0.1 -n 10 > NUL
    EXIT /B
```

---

#### `py_venv_write_requirements.bat`

```batch
    :: =============================================================================
    :: cmd: initialize
    :: =============================================================================
    @ECHO OFF
    SETLOCAL ENABLEEXTENSIONS ENABLEDELAYEDEXPANSION
    IF EXIST "%~1" (CD /D "%~1") ELSE (CD /D "%~dp0")
    SET "__init_path__=%CD%"
    SET "__base_name__=%~n0"
    SET "__venv_name__=venv"
    
    
    :: =============================================================================
    :: venv: locate
    :: =============================================================================
    SET "__venv_identifier__=%__venv_name__%\Scripts\python.exe"
    :LocateVenv
        IF EXIST "%CD%\%__venv_identifier__%" (GOTO ActivateVenv)
        SET "tmp=%CD%" & CD .. & IF "%CD%"=="%tmp%" (
            ECHO Not found: %__venv_identifier__%
            ECHO make sure you've initialized the venv.
            CD /D "%__init_path__%"
            PAUSE>NUL & EXIT /B
        )
    GOTO LocateVenv
    
    
    :: =============================================================================
    :: venv: activate
    :: =============================================================================
    :ActivateVenv
        SET "__venv_stem__=%CD%"
        CD /D "%__init_path__%"
        CALL "%__venv_stem__%\%__venv_name__%\Scripts\activate"
        ::
        ECHO __init_path__: %__init_path__%
        ECHO __venv_stem__: %__venv_stem__%
        ECHO.
        GOTO ExecuteCommand
    
    
    :: =============================================================================
    :: requirements.txt: write
    :: =============================================================================
    :ExecuteCommand
        SET requirements_txt="%__venv_stem__%\requirements.txt"
        IF NOT ERRORLEVEL 1 ("pip" freeze > %requirements_txt%) ELSE (
            ECHO wailed to write  %requirements_txt%
        )
    
    
    :: =============================================================================
    :: cmd: exit
    :: =============================================================================
    ECHO. & ECHO Window will close in 3 seconds ...& PING 127.0.0.1 -n 3 > NUL
    EXIT /B
```

