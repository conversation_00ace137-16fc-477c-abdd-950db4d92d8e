
//
$APP_SYS_OSK_DIR = '@sys.dir\System32'
$APP_SYS_OSK_EXE = '@APP_SYS_OSK_DIR\osk.exe'
$APP_SYS_OSK_TIP = '@APP_SYS_OSK_EXE'

// Context: Explorer
item(
    title  = ":  &OnScreen Keyboard"
    keys   = "exe"
    type   = 'File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    //
    image  = APP_SYS_OSK_EXE
    // image  = [E18C,LOWKEY] image-sel = [E18C,HOVER]
    tip    = [APP_SYS_OSK_TIP,TIP3,0.5]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_SYS_OSK_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_SYS_OSK_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_SYS_OSK_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_SYS_OSK_DIR')),
    }
)
// Context: Taskbar
item(
    title  = ":  &OnScreen Keyboard"
    keys   = "exe"
    type   = 'Taskbar'
    //
    image  = APP_SYS_OSK_EXE
    // image  = [E18C,LOWKEY] image-sel = [E18C,HOVER]
    tip    = [APP_SYS_OSK_TIP,TIP3,0.5]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_SYS_OSK_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_SYS_OSK_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_SYS_OSK_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_SYS_OSK_DIR')),
    }
)