
//
$APP_SYS_TASKMGR_DIR = '@sys.dir\System32'
$APP_SYS_TASKMGR_EXE = '@APP_SYS_TASKMGR_DIR\taskmgr.exe'
$APP_SYS_TASKMGR_TIP = '@APP_SYS_TASKMGR_EXE'

// Context: Explorer
item(
    title  = "&Task Manager"
    // keys   = "exe"
    type   = 'File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    //
    // image  = APP_SYS_TASKMGR_EXE
    image  = [E0A4,RED] image-sel = [E0A4,HOVER]
    tip    = [APP_SYS_TASKMGR_TIP,TIP3,0.5]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_SYS_TASKMGR_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_SYS_TASKMGR_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_SYS_TASKMGR_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_SYS_TASKMGR_DIR')),
    }
)
// Context: Taskbar
item(
    title  = "&Task Manager"
    // keys   = "exe"
    type   = 'Taskbar'
    //
    // image  = APP_SYS_TASKMGR_EXE
    image  = [E0A4,RED] image-sel = [E0A4,HOVER]
    tip    = [APP_SYS_TASKMGR_TIP,TIP3,0.5]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_SYS_TASKMGR_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_SYS_TASKMGR_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_SYS_TASKMGR_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_SYS_TASKMGR_DIR')),
    }
)
