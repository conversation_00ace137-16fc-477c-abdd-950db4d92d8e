﻿<h5>REG</h5>
<p>Registry functions</p>
<br>
<section id="registry-hives" class="my-5">
	<h5>Registry hive enum</h5>
	<p>Syntax</p>
	<pre><code>HKCU
HKCR
HKLM
HKU
HKEY_CLASSES_ROOT
HKEY_CURRENT_USER
HKEY_LOCAL_MACHINE
HKEY_USERS
</code></pre>
</section>
<section id="registry-types" class="my-5">
	<h5>Registry value type Enum</h5>
	<p>Syntax</p>
	<pre><code>reg.none	// No data type
reg.sz		// REG_SZ
reg.expand	// REG_EXPAND_SZ
reg.binary	// REG_BINARY
reg.multi	// REG_MULTI_SZ
reg.dword	// REG_DWORD
reg.qword	// REG_QWORD</code></pre>
</section>

<section id="reg-function" class="my-5">
	<h5>The function of reading from the Registry.</h5>
	<p>Syntax</p>
	<code>reg(reg.lm, '<PERSON><PERSON><PERSON><PERSON><PERSON>\Microsoft\Windows NT\CurrentVersion','ProductName')</code><br><br>
	<code>reg(reg.cr, 'txtfile\DefaultIcon')</code>
</section>

<section id="reg.exists" class="my-5">
	<h5>reg.exists</h5>
	<p>Check that the key or value name exists</p>
	<p>Syntax</p>
	<p>Check that the key exists</p>
	<code>reg.exists('HKCU\Control Panel\Desktop')</code>
	<p>Check that the value name exists</p>
	<code>reg.exists('HKCU\Control Panel\Desktop', "WallPaper")</code>
</section>

<section id="reg.get" class="my-5">
	<h5>reg.get</h5>
	<p>Read data by value name</p>
	<p>Syntax</p>
	<pre><code>reg('HKCU\Control Panel\Desktop', "WallPaper")
reg.get('HKCU\Control Panel\Desktop', "WallPaper")
reg.get('HKCU\Control Panel\Desktop')</code></pre>
</section>

<section id="reg.set" class="my-5">
	<h5>reg.set</h5>
	<p>Allows creating a subkey with the value name and value data</p>
	<p>Syntax</p>
	<p>Create Subkey</p>
	<pre><code>reg.set('HKCU\Software\Nilesoft\Shell')</pre></code>
	<p>Create Subkey with value and set value data type.</p>
	<pre><code>reg.set('HKCU\Software\Nilesoft\Shell', "test-int", 1, reg.dword)
reg.set('HKCU\Software\Nilesoft\Shell', "test-str", 1, reg.sz)
reg.set('HKCU\Software\Nilesoft\Shell', "test-str", 'some string', reg.sz)</code></pre>

	<p>Set value data with auto type detection.</p><pre><code>
reg.set('HKCU\Software\Nilesoft\Shell', 'test-auto-int', 1)
reg.set('HKCU\Software\Nilesoft\Shell', 'test-auto-str', 'some string')</code></pre>
</section>

<section id="reg.delete" class="my-5">
	<h5>reg.delete</h5>
	<p>Allows deleting a subkey or deleting a value</p>
	<p>Syntax</p>
	<p>Delete value name.</p>
	<pre><code>reg.delete('HKCU\Software\Nilesoft\Shell', 'test-auto')</pre></code>
	<p>Delete subkey.</p>
	<pre><code>reg.delete('HKCU\Software\Nilesoft\Shell')</code></pre>
</section>

<section id="reg.keys" class="my-5">
	<h5>reg.keys</h5>
	<p>Returns all subkey names</p>
	<p>Syntax</p>
	<pre><code>reg.keys('HKCU\Software\Nilesoft\Shell')</pre></code>
</section>
<section id="reg.values" class="my-5">
	<h5>reg.values</h5>
	<p>Returns all value names</p>
	<p>Syntax</p>
	<pre><code>reg.values('HKCU\Software\Nilesoft\Shell')</pre></code>
</section>
