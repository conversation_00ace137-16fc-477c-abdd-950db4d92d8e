
/* item: singles/apps/simplewall */

//
$APP_SIMPLEWALL_DIR = '@app.dir\PORTAL\APPS\app_simplewall\exe'
$APP_SIMPLEWALL_EXE = '@APP_SIMPLEWALL_DIR\simplewall.exe'
$APP_SIMPLEWALL_TIP = "..."+str.trimstart('@APP_SIMPLEWALL_EXE','@app.dir')

// context: directory
item(
    title  = ":  &Simplewall"
    keys   = "exe"
    type   = 'Desktop|Dir|Drive|Back.Dir|Back.Drive'
    args   = '--local="@sel.dir"'
    //
    image  = APP_SIMPLEWALL_EXE
    tip    = [APP_SIMPLEWALL_TIP,TIP3,1.0]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_SIMPLEWALL_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_SIMPLEWALL_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_SIMPLEWALL_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_SIMPLEWALL_DIR')),
    }
)
// Context: Taskbar
item(
    title  = ":  &Simplewall"
    keys   = "exe"
    type   = 'Taskbar'
    args   = ''
    //
    image  = APP_SIMPLEWALL_EXE
    tip    = [APP_SIMPLEWALL_TIP,TIP3,1.0]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_SIMPLEWALL_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_SIMPLEWALL_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_SIMPLEWALL_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_SIMPLEWALL_DIR')),
    }
)
