// ----------------------------------------------------------------------------
// COLORS
// ----------------------------------------------------------------------------
$COLOR_SUBTLE = #4E4259
$COLOR_GREY   = #717482
$COLOR_WHITE  = #FFFFFF
$COLOR_BLUE   = #34B6FF
$COLOR_GREEN  = #39C65A
$COLOR_ORANGE = #FF904D
$COLOR_PURPLE = #A457FF
$COLOR_RED    = #FF1C1A

// ----------------------------------------------------------------------------
// MENU: CLIPBOARD
// ----------------------------------------------------------------------------
// Parent Menu
$M_CLIPBOARD_TITLE = "&Clipboard"
$M_CLIPBOARD_ICON  = ["\uE11B", COLOR_BLUE]
//
// Icons
$ICO_CLIPBOARD_1 = "\uE10E"
$ICO_CLIPBOARD_2 = "\uE1A3"
//
// Commands / Args
$PS1_COPY_FULLPATH    = '-Command @sel("\\\"",",") | % { [System.IO.Path]::GetFullPath($_) } | Set-Clipboard'
$PS1_COPY_LOCATION    = '-Command @sel("\\\"",",") | % { [System.IO.Path]::GetDirectoryName($_) } | Set-Clipboard'
$PS1_COPY_FILENAME    = '-Command @sel("\\\"",",") | % { [System.IO.Path]::GetFileName($_) } | Set-Clipboard'
$PS1_COPY_BASENAME    = '-Command @sel("\\\"",",") | % { [System.IO.Path]::GetFileNameWithoutExtension($_) } | Set-Clipboard'
$PS1_COPY_EXTENSION   = '-Command @sel("\\\"",",") | % { [System.IO.Path]::GetExtension($_) } | Set-Clipboard'
$PS1_COPY_FILECONTENT = '-Command @sel("\\\"",",") | % { Get-Content $_ -Raw } | Set-Clipboard'
$PS1_COPY_DIRCONTENTS = '-Command @sel("\\\"",",") | % { Get-ChildItem $_ -Recurse | Select-Object -ExpandProperty FullName } | Set-Clipboard'
//
// Regex Criterias
$REGEX_TEXTFILES = "\\.(url|bat|cmd|cpp|csv|git|gitignore|htm|html|ini|js|json|log|mcr|md|ms|py|scene|sql|txt|xls|xlsx|xml|yml|nss|sublime\\w*)$"
//
// Create Menu
menu(mode='multiple' type='file|dir' title=M_CLIPBOARD_TITLE image=M_CLIPBOARD_ICON)
{
    item(title = "Copy Path"
        type   = 'file|dir'
        window = 'hidden'
        image  = [ICO_CLIPBOARD_1, COLOR_GREEN]
        cmd    = 'powershell'
        args   = PS1_COPY_FULLPATH
    )
    item(title = "Copy Location"
        type   = 'file|dir'
        window = 'hidden'
        image  = [ICO_CLIPBOARD_1, COLOR_GREEN]
        cmd    = 'powershell'
        args   = PS1_COPY_LOCATION
    )
    separator
    item(title = "Copy Name"
        type   = 'file'
        window = 'hidden'
        image  = [ICO_CLIPBOARD_1, COLOR_BLUE]
        cmd    = 'powershell'
        args   = PS1_COPY_FILENAME
    )
    item(title = "Copy Name"
        type   = 'dir'
        window = 'hidden'
        image  = [ICO_CLIPBOARD_1, COLOR_BLUE]
        cmd    = 'powershell'
        args   = PS1_COPY_BASENAME
    )
    separator
    item(title = "Copy Extension"
        type   = 'file'
        window = 'hidden'
        image  = [ICO_CLIPBOARD_1, COLOR_ORANGE]
        cmd    = 'powershell'
        args   = PS1_COPY_EXTENSION
    )
    separator
    item(title = "Copy Content"
        type   = 'file'
        window = 'hidden'
        where  = regex.match(sel.file.ext, REGEX_TEXTFILES)
        image  = [ICO_CLIPBOARD_2, COLOR_PURPLE]
        cmd    = 'powershell'
         args   = PS1_COPY_FILECONTENT
    )
    item(title = "Copy Directory Contents"
        type   = 'dir|back.dir'
        window = 'hidden'
        image  = [ICO_CLIPBOARD_2, COLOR_PURPLE]
        cmd    = 'powershell'
        args   = PS1_COPY_DIRCONTENTS
    )
}