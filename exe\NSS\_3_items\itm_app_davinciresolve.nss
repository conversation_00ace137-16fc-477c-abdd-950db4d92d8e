
//
$APP_USER_DAVINCIRESOLVE_DIR = '@user.desktop\my\flow\home\__GOTO__\Apps\app_davinciresolve\exe'
$APP_USER_DAVINCIRESOLVE_EXE = '@APP_USER_DAVINCIRESOLVE_DIR\Resolve.exe'
$APP_USER_DAVINCIRESOLVE_TIP = "..."+str.trimstart('@APP_USER_DAVINCIRESOLVE_EXE','@app.dir')

// context: directory
item(
    title  = ":  &DaVinci Resolve"
    keys   = "exe"
    type   = 'File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    find   = '.ts|.mp4|.mkv|.webm|.mov|.wmv|.avi|.flv|.mpg'
    args   = '"@sel.file"'
    //
    image  = APP_USER_DAVINCIRESOLVE_EXE
    tip    = [APP_USER_DAVINCIRESOLVE_TIP,TIP3,0.8]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_USER_DAVINCIRESOLVE_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_DAVINCIRESOLVE_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_DAVINCIRESOLVE_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_DAVINCIRESOLVE_DIR')),
    }
)
// Context: Taskbar
item(
    title  = ":  &DaVinci Resolve"
    keys   = "exe"
    type   = 'Taskbar'
    args   = ''
    //
    image  = APP_USER_DAVINCIRESOLVE_EXE
    tip    = [APP_USER_DAVINCIRESOLVE_TIP,TIP3,0.8]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_USER_DAVINCIRESOLVE_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_DAVINCIRESOLVE_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_DAVINCIRESOLVE_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_DAVINCIRESOLVE_DIR')),
    }
)

