// ----------------------------------------------------------------------------
// COLORS
// ----------------------------------------------------------------------------
$clr_sel    = #FFD420
$clr_subtle = #4E4259
$clr_grey   = #717482
$clr_blue   = #34B6FF
$clr_green  = #39C65A
$clr_orange = #FF904D
$clr_red    = #FF1C1A
$clr_purple = #A457FF
$clr_white  = #FFFFFF

// ----------------------------------------------------------------------------
// HIDE
// ----------------------------------------------------------------------------
modify(find='7-Zip' vis=hidden)
remove(clsid='{23170F69-40C1-278A-1000-000100020000}' where=!this.isuwp)

// ----------------------------------------------------------------------------
// VARIABLES
// ----------------------------------------------------------------------------
$path_7zip_cli  = '@sys.prog\7-Zip\7z.exe'
$path_7zip_gui  = '@sys.prog\7-Zip\7zG.exe'
$path_7zip_mngr = '@sys.prog\7-Zip\7zFM.exe'

// ----------------------------------------------------------------------------
// CREATE
// ----------------------------------------------------------------------------
menu(title='&7-Zip' type='File|Dir|Drive|Back' mode='Multiple' image=[\uE0D0, clr_blue] image-sel=[\uE0D0, clr_sel]) {
    //
    $ico_zip_close    = [\uE0CF, clr_green]
    $ico_zip_close_hi = [\uE0CF, clr_sel]
    $ico_zip_open     = [\uE0D0, clr_purple]
    $ico_zip_open_hi  = [\uE0D0, clr_sel]
    $ico_zip_test     = \uE1E8
    $ico_zip_info     = \uE134

    //
    $is_archive  = if(keys.shift(), '', str.replace('."7z|xz|bz2|gz|tar|zip|wim|apfs|ar|arj|cab|chm|cpio|cramfs|dmg|ext|fat|gpt|hfs|ihex|iso|lzh|lzma|mbr|msi|nsis|ntfs|qcow2|rar|rpm|squashfs|udf|uefi|vdi|vhd|vhdx|vmdk|xar|z"', '|', '"|."'))
    $is_checksum = if(keys.shift(), '', str.replace('."sha256|sha512|sha224|sha384|sha1|sha|md5|crc32|crc64|asc|cksum"', '|', '"|."'))
    $sel_air=str.replace('"-air!@sel(false, '" "-air!')#', '" "-air#', '" -an')

    item(title='&Open in 7-Zip...' image=[\uE0D0, clr_orange] image-sel=ico_zip_open_hi type='File|Dir|Drive|Back' mode='single' cmd=path_7zip_mngr args=sel(true))

    menu(title='Extract...' type='File' find=is_archive image=ico_zip_open image-sel=ico_zip_open_hi expanded=1) {
        separator
        // item(title="Extract..." keys="GUI" image=inherit cmd=path_7zip_gui args='x @sel_air -ad -o@if(sel.count>1, '*\', '@sel.path.title\')')
        menu(title='Extract...' where=keys.shift() image=inherit) {
            item(title='Extract files...@"\t"single' mode='single' image=inherit cmd=path_7zip_gui args='x @sel(true) -<EMAIL>\ -ad')
            item(title='Extract to "@sel.title\", delete' mode='single' image=inherit commands {
                    cmd=path_7zip_gui args='x @sel(true) -o*\' wait = 1,
                    cmd=io.delete(sel) wait = 1,
                    cmd=command.refresh
                }
            )
            item(title='Extract to "@sel.title\", ask to delete' mode='single' image=inherit commands {
                    cmd=path_7zip_gui args='x @sel(true) -o*\' wait = 1,
                    cmd=if(msg("Are you sure you want to delete the archive file?","NileSoft Shell", msg.warning | msg.yesno)==msg.idyes, io.delete(sel)) wait = 1,
                    cmd=command.refresh
                }
            )
        }
        item(title='Extract'+if(sel.count==1, ' to "@sel.title\"', ' to separate folders') image=inherit cmd=path_7zip_gui args='x @sel_air -o*\ -spe')
        item(title='Extract Here' image=inherit cmd=path_7zip_gui args='x @sel_air -spe')
    }

    menu(title='Archive...' type='File|Dir|Drive|Back' image=ico_zip_close image-sel=ico_zip_close_hi expanded=1) {
        separator
        item(title="Add to..." keys="GUI"  image=inherit cmd=path_7zip_gui args='a @(sel.title) -ad -sae -- @sel(true)')
        item(title='Add to "@(sel.title).zip"' keys="SHIFT to .7z" image=inherit cmd=path_7zip_gui args='a @(sel.title)@if(!keys.shift(), '.zip -tzip', '.7z -t7z') -sae -- @sel(true)')
        // item(title='Generate a file checksum ' image=inherit cmd=path_7zip_gui args='a @(sel.name).sha256 -thash -sae -- @sel(true)')
    }


    // menu(title='Test...' type='file' image=ico_zip_test expanded=1) {
    //     separator
    //     item(title='Test archive@if(sel.count>1,'s')'  find=is_archive image=inherit cmd=path_7zip_gui args='t @sel_air')
    //     item(title='Test and list...' find=is_archive image=inherit tip='Test the integrity of the archive and list the files afterwards.' cmd-line='/k @path.short(path_7zip_cli) l @sel_air & pause & exit')
    //     item(title='Test Checksum' find=is_checksum image=inherit cmd=path_7zip_gui args='t @sel_air -thash')

    //     menu(title='Checksum' type='file|dir' where=keys.shift() image=inherit) {
    //         item(title='CRC 32'     cmd=path_7zip_gui args='h -scrcCRC32  @sel(true)')
    //         item(title='CRC 64'     cmd=path_7zip_gui args='h -scrcCRC64  @sel(true)')
    //         item(title='SHA 1'      cmd=path_7zip_gui args='h -scrcSHA1   @sel(true)')
    //         item(title='SHA 256'    cmd=path_7zip_gui args='h -scrcSHA256 @sel(true)')
    //         item(title='*'          cmd=path_7zip_gui args='h -scrc*      @sel(true)')
    //     }
    // }

    // menu(title='7-Zip Info' type='file|dir|drive|back' where=key.shift() sep='before' image=ico_zip_info) {
    //     item(title='Homepage...' cmd='https://7-zip.org/')
    //     item(title='Documentation...' cmd=path.combine(sys.prog,'7-Zip','7-zip.chm'))
    //     item(title='Command Line Version User@"'"s Guide...' cmd='https://7-zip.opensource.jp/chm/cmdline/')
    //     item(title='Supported Formats Info...' cmd-line='/k @path.short(path_7zip_cli) i & pause & exit')
    // }
}