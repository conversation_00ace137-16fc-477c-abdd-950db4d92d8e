
// ----------------------------------------------------------------------------
// REMOVE
// ----------------------------------------------------------------------------
// -> Default
remove(where=this.id==id.copy_path)
remove(where=this.id==id.copy_as_path)
remove(where=this.id==id.content)
// -> Custom
remove(where=regex.match(this.name, ".*NordVPN.*"))
remove(where=regex.match(this.name, ".*Onedrive.*"))
remove(where=regex.match(this.name, ".*Dropbox.*"))
remove(where=regex.match(this.name, ".*Sync or Backup.*"))
remove(where=regex.match(this.name, ".*Add to VLC Media Player.*"))
remove(where=regex.match(this.name, ".*Enqueue in Winamp.*"))
remove(where=regex.match(this.name, ".*Send a copy.*"))
remove(where=regex.match(this.name, ".*Open archive.*"))


// ----------------------------------------------------------------------------
// APPLICATION ICONS
// ----------------------------------------------------------------------------
// -> Default
// modify(image=image.fluent("\uE77B", icn_size_mid, clr_blue) where=this.id==id.run_as_administrator)
// modify(image=image.fluent("\uE8E5", icn_size_mid, clr_blue) where=this.id==id.open)
// modify(image=image.fluent("\uE7AC", icn_size_mid, clr_blue) where=this.id==id.open_with)
// modify(image=image.fluent("\uE8A0", icn_size_mid, clr_blue) where=this.id==id.open_in_new_tab)
// modify(image=image.fluent("\uE838", icn_size_mid, clr_blue) where=this.id==id.open_in_new_window)
// modify(image=image.fluent("\uE81D", icn_size_mid, clr_blue) where=this.id==id.open_file_location)
// modify(image=image.fluent("\uE81D", icn_size_mid, clr_blue) where=this.id==id.open_folder_location)
// -> Custom
// modify(image=($AppPath_WinMerge)     where=regex.match(this.name, ".*Compare.*"))
// modify(image=($AppPath_SublimeText)  where=regex.match(this.name, ".*Sublime.*"))
// modify(image=($AppPath_Everything)   where=regex.match(this.name, ".*SearchEverything.*"))
// modify(image=($AppPath_NotepadPlus)  where=regex.match(this.name, ".*Notepad\\+\\+.*"))



// ----------------------------------------------------------------------------
// REMOVE (Unused)
// ----------------------------------------------------------------------------
remove(
    where=this.id(
        id.new_item, id.copy_as_path, id.restore_previous_versions,
        id.show_cortana_button, id.show_people_on_the_taskbar,
        id.cast_to_device, id.disconnect_network_drive, id.give_access_to,
        id.make_available_offline, id.make_available_online, id.autoplay,
        id.cortana, id.news_and_interests, id.include_in_library,
        id.open_as_portable
    )
)


// ----------------------------------------------------------------------------
// HIDE (Rarely Used)
// ----------------------------------------------------------------------------
modify(
    where=this.id(
        id.copy_path, id.extract_all, id.extract_to, id.map_as_drive,
        id.map_network_drive, id.command_prompt, id.open_command_prompt,
        id.open_command_window_here, id.open_powershell_window_here,
        id.open_windows_powershell, id.troubleshoot_compatibility,
        id.copy_to_folder, id.move_to_folder, id.rotate_left, id.rotate_right
    ) vis="hidden"
)


// ----------------------------------------------------------------------------
// MOVE THE NON-DEFAULT MENUITEMS INTO A NEW SUBMENU
// ----------------------------------------------------------------------------
// menu(type='~taskbar' title='ORG' pos='top' sep='bottom' image=[\uE1B8, #000000]) {}
menu(title='&ORG' pos='top' sep='bottom' image=["\uE1B8", #4e4259]) {}
modify(
    where=!this.id(
        // excluded: id.group_by, id.customize_this_folder,
        // -> Basic Operations
        id.delete, id.edit, id.new, id.new_folder, id.new_item, id.open,
        id.open_with, id.properties, id.rename,

        // -> Clipboard Operations
        id.copy_as_path, id.copy_path, id.copy, id.cut, id.paste,
        id.paste_shortcut,

        // -> Movement and Location
        id.copy_here, id.copy_to, id.move_here, id.move_to,
        id.open_file_location, id.open_folder_location,

        // -> Advanced File Operations
        id.compressed, id.create_shortcut, id.create_shortcuts_here,
        id.extract_all, id.extract_to, id.restore_previous_versions,

        // -> Icon and Display Settings
        id.extra_large_icons, id.large_icons, id.medium_icons, id.small_icons,
        id.list, id.details, id.tiles, id.content,

        // -> Organization and Sorting
        id.arrange_by, id.sort_by,

        // -> Icon Management
        id.align_icons_to_grid, id.auto_arrange_icons,

        // -> Visibility and Customization
        id.customize_notification_icons,
        id.show_cortana_button, id.show_desktop_icons, id.show_file_extensions,
        id.show_hidden_files, id.show_libraries, id.show_network,
        id.show_people_on_the_taskbar, id.show_task_view_button,
        id.show_touch_keyboard_button, id.show_touchpad_button,

        // -> System Tools
        id.adjust_date_time, id.control_panel, id.device_manager,
        id.display_settings, id.file_explorer, id.folder_options,
        id.power_options, id.settings, id.task_manager, id.taskbar_settings,

        // -> Personalization
        id.desktop, id.options, id.personalize,

        // -> Network Operations
        id.cast_to_device, id.disconnect, id.disconnect_network_drive,
        id.map_as_drive, id.map_network_drive,

        // -> Sharing and Accessibility
        id.give_access_to, id.make_available_offline, id.make_available_online,
        id.share, id.share_with,

        // -> Command Line Tools
        id.command_prompt, id.open_command_prompt, id.open_command_window_here,
        id.open_powershell_window_here, id.open_windows_powershell,

        // -> System Utilities
        id.cleanup, id.refresh, id.run, id.run_as_administrator,
        id.run_as_another_user, id.search, id.troubleshoot_compatibility,

        // -> Security Tools
        id.install, id.manage, id.turn_off_bitlocker, id.turn_on_bitlocker,

        // -> Device Operations
        id.autoplay, id.eject, id.erase_this_disc, id.mount,

        // -> Media Actions
        id.play, id.print,

        // -> Window Arrangement
        id.cascade_windows, id.show_windows_side_by_side,
        id.show_windows_stacked,

        // -> Taskbar Management
        id.lock_all_taskbars, id.lock_the_taskbar,

        // -> Windows Features
        id.cortana, id.news_and_interests, id.send_to, id.store,

        // -> General
        id.add_a_network_location, id.cancel, id.collapse,
        id.collapse_all_groups, id.collapse_group, id.configure,
        id.empty_recycle_bin, id.exit_explorer, id.expand, id.expand_all_groups,
        id.expand_group, id.format, id.include_in_library,
        id.insert_unicode_control_character, id.merge, id.more_options,
        id.next_desktop_background, id.open_as_portable, id.open_autoplay,
        id.open_in_new_process, id.open_in_new_tab, id.open_in_new_window,
        id.open_new_tab, id.open_new_window,
        id.pin_current_folder_to_quick_access, id.pin_to_quick_access,
        id.pin_to_start, id.pin_to_taskbar, id.preview, id.reconversion,
        id.redo, id.remove_from_quick_access, id.remove_properties, id.restore,
        id.restore_default_libraries,
        id.select_all, id.set_as_desktop_background,
        id.set_as_desktop_wallpaper, id.shield, id.show_pen_button,
        id.show_the_desktop, id.show_this_pc, id.undo,
        id.unpin_from_quick_access, id.unpin_from_start, id.unpin_from_taskbar,
        id.view
    ) menu='ORG'
)


// Menu: Apps
// menu(mode="multiple" type="*" title="Apps" image=\uE00D) {}
// modify(where=regex.match(this.name, ".*Open in Terminal.*")  menu="Apps")
// modify(where=regex.match(this.name, ".*Git Bash.*")  menu="Apps")
// item(title=title.command_prompt tip=tip_run_admin admin=key.shift() image cmd='cmd.exe' args='/K TITLE ^<Prompt^> ..\@sel.dir.name' menu="Apps")
// modify(where=regex.match(this.name, ".*PowerRename.*")  menu="Apps")
// modify(where=regex.match(this.name, ".*Open with Visual Studio.*")  menu="Apps")
// remove(where=regex.match(this.name, "^.*CMD.*$")  menu="Apps")
// modify(where=regex.match(this.name, ".*Bulk Rename.*")  menu="Apps")
// modify(where=regex.match(this.name, ".*Add to archive.*")  menu="Apps")


// // Menu: Unimportant
// menu(mode="multiple" type="*" title="Unimportant" image=[\uE1B8, #000000]) {}
// modify(where=regex.match(this.name, ".*NordVPN.*") menu="Unimportant")
// modify(where=regex.match(this.name, ".*Onedrive.*") menu="Unimportant")
// modify(where=regex.match(this.name, ".*Dropbox.*") menu="Unimportant")
// modify(where=regex.match(this.name, ".*^Add to Favorites.*")  menu="Unimportant")
// modify(where=regex.match(this.name, ".*^Open in Terminal.*")  menu="Unimportant")
// modify(where=regex.match(this.name, ".*^Run Sandboxed.*")  menu="Unimportant")
// modify(where=regex.match(this.name, ".*^Sync or Backup.*")  menu="Unimportant")
// modify(where=regex.match(this.name, ".*^Add to VLC Media Player.*")  menu="Unimportant")
// modify(where=regex.match(this.name, ".*^Git GUI.*")  menu="Unimportant")
// modify(where=regex.match(this.name, ".*^Git Bash.*")  menu="Unimportant")
// modify(where=regex.match(this.name, ".*^VENV Utils.*")  menu="Unimportant")
// modify(where=regex.match(this.name, ".*^ShareX.*")  menu="Unimportant")
// modify(where=regex.match(this.name, ".*^GitKraken.*")  menu="Unimportant")
// modify(where=regex.match(this.name, ".*^Microsoft.*")  menu="Unimportant")
// modify(where=regex.match(this.name, ".*^Enqueue in Winamp.*") menu="Unimportant")
// modify(where=regex.match(this.name, ".*^JetBrains.*") menu="Unimportant")
// modify(where=regex.match(this.name, ".*^CFF Explorer.*") menu="Unimportant")
// modify(where=regex.match(this.name, ".*^PE Explorer.*") menu="Unimportant")
// modify(where=regex.match(this.name, ".*^JustDecompile.*") menu="Unimportant")
// modify(where=regex.match(this.name, ".*^Send a copy.*") menu="Unimportant")
// modify(where=regex.match(this.name, ".*^Print.*") menu="Unimportant")
// item(mode=mode.multiple where=this.id(id.restore_previous_versions) menu="Unimportant")
// item(mode=mode.multiple where=this.id(id.cast_to_device) menu="Unimportant")
// item(mode=mode.multiple where=this.id(id.print) menu="Unimportant")

