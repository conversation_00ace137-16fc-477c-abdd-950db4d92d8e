
/* explorer */
menu(type='~Taskbar|~Desktop|~Titlebar' mode='multiple' expanded=true) {

    // [shift] or [ctrl+shift]
    separator()
    menu(vis='@KEYS_MNU_VISIBILITY_1_SHIFT' OR '@KEYS_MNU_VISIBILITY_2_CTRL_SHIFT' expanded=true) {
        import '@app.dir/NSS/_3_items/itm_bat_githistorygraph.nss'
        import '@app.dir/NSS/_3_items/itm_bat_pyvenvexecute.nss'
        import '@app.dir/NSS/_3_items/itm_bat_pyvenvterminal.nss'
        import '@app.dir/NSS/_3_items/itm_bat_pyvenvwriterequirements.nss'
        separator()
    }

    // [*]
    import '@app.dir/NSS/_3_items/itm_action_sys_copypath.nss'
    separator()

    // [ctrl]
    menu(vis='@KEYS_MNU_VISIBILITY_1_CTRL' expanded=true) {
        import '@app.dir/NSS/_5_menus/mnu_sys_actions_clipboard.nss'
        separator()
        import '@app.dir/NSS/_5_menus/mnu_user_jorn_apps.nss'
        separator()
    }

    // [ ]
    menu(vis=(KEYS_MNU_VISIBILITY_1_SHIFT OR KEYS_MNU_VISIBILITY_0_DEFAULT) expanded=true) {
        import '@app.dir/NSS/_5_menus/mnu_user_jorn_apps_7zip.nss'
        import '@app.dir/NSS/_5_menus/mnu_user_jorn_apps_everything.nss'
        import '@app.dir/NSS/_5_menus/mnu_user_jorn_apps_git.nss'
        separator()
        import '@app.dir/NSS/_3_items/itm_app_sys_cmd.nss'
        separator()
        import '@app.dir/NSS/_3_items/itm_app_3dsmax.nss'
        import '@app.dir/NSS/_3_items/itm_app_blender.nss'
        import '@app.dir/NSS/_3_items/itm_app_bulkrenameutility.nss'
        import '@app.dir/NSS/_3_items/itm_app_cursor.nss'
        import '@app.dir/NSS/_3_items/itm_app_everything.nss'
        import '@app.dir/NSS/_3_items/itm_app_libreoffice.nss'
        import '@app.dir/NSS/_3_items/itm_app_mpvplayer.nss'
        import '@app.dir/NSS/_3_items/itm_app_sublimemerge.nss'
        import '@app.dir/NSS/_3_items/itm_app_sublimetext.nss'
        import '@app.dir/NSS/_3_items/itm_app_vscode.nss'
        separator()
    }

    // [ctrl]
    menu(vis='@KEYS_MNU_VISIBILITY_1_CTRL' expanded=true) {
        import '@app.dir/NSS/_3_items/itm_app_3dsmax.nss'
        import '@app.dir/NSS/_3_items/itm_app_audacity.nss'
        import '@app.dir/NSS/_3_items/itm_app_beyondcompare.nss'
        import '@app.dir/NSS/_3_items/itm_app_blender.nss'
        import '@app.dir/NSS/_3_items/itm_app_bulkrenameutility.nss'
        import '@app.dir/NSS/_3_items/itm_app_davinciresolve.nss'
        import '@app.dir/NSS/_3_items/itm_app_everything.nss'
        import '@app.dir/NSS/_3_items/itm_app_filezilla.nss'
        import '@app.dir/NSS/_3_items/itm_app_kdenlive.nss'
        import '@app.dir/NSS/_3_items/itm_app_libreoffice.nss'
        import '@app.dir/NSS/_3_items/itm_app_losslesscut.nss'
        import '@app.dir/NSS/_3_items/itm_app_mpvplayer.nss'
        import '@app.dir/NSS/_3_items/itm_app_notepad++.nss'
        import '@app.dir/NSS/_3_items/itm_app_pdf24.nss'
        import '@app.dir/NSS/_3_items/itm_app_rustdesk.nss'
        import '@app.dir/NSS/_3_items/itm_app_sonicvisualiser.nss'
        import '@app.dir/NSS/_3_items/itm_app_sublimemerge.nss'
        import '@app.dir/NSS/_3_items/itm_app_sublimetext.nss'
        import '@app.dir/NSS/_3_items/itm_app_sys_cmd.nss'
        import '@app.dir/NSS/_3_items/itm_app_telegram.nss'
        import '@app.dir/NSS/_3_items/itm_app_vlc.nss'
        import '@app.dir/NSS/_3_items/itm_app_vscode.nss'
        import '@app.dir/NSS/_3_items/itm_app_vscode_debug.nss' // [DEBUG]
        import '@app.dir/NSS/_3_items/itm_app_winmerge.nss'
        separator()
    }

    // [*]
    import '@app.dir/NSS/_5_menus/mnu_actions_user_create.nss'
    separator()

    // [*]
    // menu(vis='@KEYS_MNU_VISIBILITY_0_DEFAULT' expanded=true) {
    menu(vis='@KEYS_MNU_VISIBILITY_0_ALWAYS' expanded=true) {
        menu(title='&Goto@"\t"dir' type='Taskbar|File|Desktop|Dir|Drive|Back.Dir|Back.Drive' image=[E0E8,WHITE1] image-sel=[E0E8,BLUE] sep='None') {
            //
            import '@app.dir/NSS/_3_items/itm_dir_jorn_scratch.nss'
            import '@app.dir/NSS/_3_items/itm_dir_sys_thispc.nss'
            import '@app.dir/NSS/_3_items/itm_dir_sys_desktop.nss'
            separator()
            //
            import '@app.dir/NSS/_5_menus/mnu_user_jorn_dirs_common.nss'
            separator()
            import '@app.dir/NSS/_5_menus/mnu_user_jorn_dirs.nss'
            separator()
            //
            import '@app.dir/NSS/_5_menus/mnu_scratchpad.nss'
            separator()
        }
        separator()
    }

    // [*]
    import '@app.dir/NSS/_5_menus/mnu_user_jorn_scripts.nss'
    separator()

    // [shift]
    menu(vis='@KEYS_MNU_VISIBILITY_1_SHIFT' expanded=true) {
        import '@app.dir/NSS/_5_menus/mnu_sys_debug_commands.nss'
    }

}
