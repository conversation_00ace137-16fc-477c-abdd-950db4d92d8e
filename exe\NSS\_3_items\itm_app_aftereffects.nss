
//
$APP_AFTEREFFECTS_DIR = '@sys.prog\Adobe\Adobe After Effects 2024\Support Files'
$APP_AFTEREFFECTS_EXE = '@APP_AFTEREFFECTS_DIR\AfterFX.exe'
$APP_AFTEREFFECTS_TIP = "..."+str.trimstart('@APP_AFTEREFFECTS_EXE','@app.dir')
//
$APP_AFTEREFFECTS_DIR_CFG = '@user.appdata\Adobe\After Effects'
$APP_AFTEREFFECTS_DIR_NSS = '@app.dir\NSS\_3_items\user_apps'
$APP_AFTEREFFECTS_DIR_SRC = '@user.desktop\my\flow\home\__GOTO__\Apps\app_aftereffects'

// Context: File
item(
    title  = ":  &AfterEffects"
    keys   = "exe"
    type   = 'File'
    args   = '"@sel.file"'
    where  = str.equals(sel.file.ext,[".aep"])
    //
    image  = APP_AFTEREFFECTS_EXE
    tip    = [APP_AFTEREFFECTS_TIP,TIP3,1.0]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_AFTEREFFECTS_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_AFTEREFFECTS_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_AFTEREFFECTS_EXE')),
        cmd=if(KEYS_EXE_GOTO_CFG,('@APP_AFTEREFFECTS_DIR_CFG')),
        cmd=if(KEYS_EXE_GOTO_NSS,('@APP_AFTEREFFECTS_DIR_NSS')),
        cmd=if(KEYS_EXE_GOTO_SRC,('@APP_AFTEREFFECTS_DIR_SRC')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_AFTEREFFECTS_DIR')),
    }
)
// Context: Directory
item(
    title  = ":  &AfterEffects"
    keys   = "exe"
    type   = 'Desktop|Dir|Drive|Back.Dir|Back.Drive'
    args   = '--local="@sel.dir"'
    //
    image  = APP_AFTEREFFECTS_EXE
    tip    = [APP_AFTEREFFECTS_TIP,TIP3,1.0]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_AFTEREFFECTS_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_AFTEREFFECTS_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_AFTEREFFECTS_EXE')),
        cmd=if(KEYS_EXE_GOTO_CFG,('@APP_AFTEREFFECTS_DIR_CFG')),
        cmd=if(KEYS_EXE_GOTO_NSS,('@APP_AFTEREFFECTS_DIR_NSS')),
        cmd=if(KEYS_EXE_GOTO_SRC,('@APP_AFTEREFFECTS_DIR_SRC')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_AFTEREFFECTS_DIR')),
    }
)
// Context: Taskbar
item(
    title  = ":  &AfterEffects"
    keys   = "exe"
    type   = 'Taskbar'
    args   = ''
    //
    image  = APP_AFTEREFFECTS_EXE
    tip    = [APP_AFTEREFFECTS_TIP,TIP3,1.0]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_AFTEREFFECTS_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_AFTEREFFECTS_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_AFTEREFFECTS_EXE')),
        cmd=if(KEYS_EXE_GOTO_CFG,('@APP_AFTEREFFECTS_DIR_CFG')),
        cmd=if(KEYS_EXE_GOTO_NSS,('@APP_AFTEREFFECTS_DIR_NSS')),
        cmd=if(KEYS_EXE_GOTO_SRC,('@APP_AFTEREFFECTS_DIR_SRC')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_AFTEREFFECTS_DIR')),
    }
)
