
//
$PY_BOOKMARKFOLDERIZER_DIR = '@user.desktop\my\flow\home\__GOTO__\Scripts\Python\Py_Cli_Utils\py__BookmarkFolderizer'
$PY_BOOKMARKFOLDERIZER_EXE = '@PY_BOOKMARKFOLDERIZER_DIR\venv\Scripts\python.exe'
$PY_BOOKMARKFOLDERIZER_APP = '@PY_BOOKMARKFOLDERIZER_DIR\main.py'
//

// Context: Explorer
$PY_BOOKMARKFOLDERIZER_EXPLORER = '-i "bookmarks.html" -op "@sel.dir" --prompt'
item(
    title="&BookmarkFolderizer"
    keys="py"
    type='File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    //
    image=[E17C,GREY] image-sel=[E17C,PURPLE]
    tip=['"@PY_BOOKMARKFOLDERIZER_APP" @PY_BOOKMARKFOLDERIZER_EXPLORER',TIP3,0.75]
    //
    admin=keys.rbutton()
    cmd=if(KEYS_EXE_OPEN_EXE,('"@PY_BOOKMARKFOLDERIZER_EXE"'))
    args='"@PY_BOOKMARKFOLDERIZER_APP" @PY_BOOKMARKFOLDERIZER_EXPLORER'
    //
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@PY_BOOKMARKFOLDERIZER_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@PY_BOOKMARKFOLDERIZER_APP')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@PY_BOOKMARKFOLDERIZER_DIR')),
    }
)
// Context: Taskbar
$PY_BOOKMARKFOLDERIZER_TASKBAR = '-i "bookmarks.html" -op "@user.desktop" --prompt'
item(
    title="&BookmarkFolderizer"
    keys="py"
    type='Taskbar'
    //
    image=[E17C,GREY]
    image-sel=[E17C,PURPLE]
    //
    admin=keys.rbutton()
    cmd=if(KEYS_EXE_OPEN_EXE,('"@PY_BOOKMARKFOLDERIZER_EXE"'))
    args='"@PY_BOOKMARKFOLDERIZER_APP" @PY_BOOKMARKFOLDERIZER_TASKBAR'
    tip=['"@PY_BOOKMARKFOLDERIZER_APP" @PY_BOOKMARKFOLDERIZER_TASKBAR',TIP3,0.75]
    //
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@PY_BOOKMARKFOLDERIZER_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@PY_BOOKMARKFOLDERIZER_APP')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@PY_BOOKMARKFOLDERIZER_DIR')),
    }
)
