@ECHO OFF
SETLOCAL ENABLEDELAYEDEXPANSION

:: =============================================================================
:: Shell Script with Backup and Initialization Logic
:: =============================================================================

CALL :InitBatchScript
CALL :BackupShellLog
:: CALL :ReloadShell
EXIT /B 0

:: =============================================================================
:: Subroutines
:: =============================================================================

:InitBatchScript
    IF EXIST "%~1" (CD /D "%~1") ELSE (CD /D "%~dp0")
    SET "CmdStartDirectory=%CD%"
    SET "CmdFullPath=%~dp0%~nx0"
    SET "CmdDirectory=%~dp0"
    SET "CmdFileName=%~nx0"
    SET "CmdBaseName=%~n0"
    SET "CmdExtension=%~x0"
GOTO :EOF

:BackupShellLog
  SET "sourceFile=shell.log"
  SET "targetDir=%CD%\logs"
  IF NOT EXIST "%targetDir%\" MKDIR "%targetDir%"

  SET "maxNum=0"

  ECHO Starting to process log files...
  FOR /F "tokens=*" %%F IN ('dir /B /A-D /O-N "%targetDir%\shell.*.log"') DO (
    CALL :ProcessLogFile "%%F"
  )

  ECHO Highest number found: !maxNum!
  SET /A newNum=!maxNum! + 1
  ECHO New number before padding: !newNum!
  CALL :PadNumber !newNum! newPaddedNum
  SET "newFileName=shell.!newPaddedNum!.log"

  ECHO New file name: !newFileName!

  IF "!newFileName!"=="shell..log" (
    GOTO :EOF
  )

  COPY "%sourceFile%" "%targetDir%\!newFileName!"
  GOTO :EOF

:: Subroutine to process each log file.
:ProcessLogFile
  SET "processFile=true"
  SET "fileName=%~n1"
  SET "fileNum=!fileName:shell.=!"

  ECHO Checking file %~1
  IF "!fileNum!"=="" SET "processFile=false"
  IF "!processFile!"=="true" (
    SET /A fileNum=1!fileNum! - 1000000
    ECHO Found file %~1 with number !fileNum!
    IF !fileNum! GTR !maxNum! SET "maxNum=!fileNum!"
  ) ELSE (
    ECHO Skipping invalid file: %~1
  )
  GOTO :EOF

:: Subroutine to pad a number.
:PadNumber
  SET "num=%~1"
  SET "paddedNum=000000%num%"
  SET "paddedNum=!paddedNum:~-6!"
  SET "%~2=!paddedNum!"
  GOTO :EOF


:ReloadShell
    CLS
    shell -register -restart
    EXPLORER %CmdStartDirectory%
GOTO :EOF
