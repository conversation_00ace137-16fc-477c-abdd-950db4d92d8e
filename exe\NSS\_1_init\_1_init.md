# Project Files Documentation for `_1_init`

### File Structure

```
├── _a_constants
│   ├── def_bools.nss
│   ├── def_colors.nss
│   ├── def_icons.nss
│   ├── def_keys.nss
│   ├── def_paths.nss
│   ├── def_tooltips.nss
│   └── def_uris.nss
├── _b_config
│   ├── cfg_settings.nss
│   └── cfg_theme.nss
├── _c_overrides
│   ├── __update_everything64.nss
│   ├── mod_icons.nss
│   ├── mod_positions.nss
│   └── mod_visibility.nss
└── _d_cleanup
    ├── cln_desktop.nss
    ├── cln_explorer.nss
    └── cln_taskbar.nss
```
### 1. `_a_constants\def_bools.nss`

#### `_a_constants\def_bools.nss`

```java

// docs: `...`


/*
    :: system checks

    :: usage: `where=IS_SYS_WIN10`
*/
$IS_SYS_PRIMARY = sys.is_primary_monitor()
$IS_SYS_WIN10 = sys.is10()
$IS_SYS_WIN11 = sys.is11()

```
### 2. `_a_constants\def_colors.nss`

#### `_a_constants\def_colors.nss`

```java

// docs: `...`


/*
    :: predefined colors

    :: usage: `image=["\uE23B",LOWKEY] image-sel=["\uE23B",HOVER]`
*/


$WHITE1 = #B4BFE4


// -> generalized colors
$LOWKEY        = #E0D9EA
$SOFT          = #8D8199
$DARK          = #46466F

// -> categorized colors
$HOVER         = #FFFA00

// -> specific colors
$BLUE1         = #1A377D
$BLUE2         = #285CF1
$BLUE3         = #3893FF
$BLUE4         = #75B4FF

$BLUE          = #3893FF
// $BLUE_DARK     = #285CF1
//
$WHITE         = #FFFFFF
$BLUE2         = #34B6FF80
//
$RED           = #FF1C1A
$RED_SOFT      = #FF4642
$PINK_BRIGHT   = #FF4DAE
$PINK          = #FF00FF
$PURPLE        = #A457FF
$PURPLE1       = #DC4DFF
$PURPLE2       = #A457FF80
//
$BLACK         = #000000
//
$GREEN         = #39C65A
$GREEN2        = #34FF6580
//
$YELLOW_BRIGHT = #FFFA00
$YELLOW        = #FFD420
$YELLOW2        = #FFD42080
$ORANGE        = #FF904D
$ORANGE2       = #FF904D80

//
$PINK2         = #FF00FF60
$RED2          = #F7A1A1B3
$GREY          = #717482

// -> new

// $YELLOW1 = #FFFA00 // bright
// $YELLOW2 = #FFD420 // normal
// $YELLOW3 = #D6B426 // subtle/soft

```
### 3. `_a_constants\def_icons.nss`

#### `_a_constants\def_icons.nss`

```java

// docs: `...`


/*
    :: predefined icons

    :: usage: `image=[E23B,#E0D9EA]`
*/
$E153 = "\uE153" // streaming

$E120 = "\uE120" // network/nas
$E121 = "\uE121" // network/nas
$E11F = "\uE11F" // network
$E265 = "\uE265" // device
$E26F = "\uE26F" // commandbrackets
$E0CE = "\uE0CE" // cleanup
$E26D = "\uE26D" // system script
$E276 = "\uE276" // restart shell
$E0FB = "\uE0FB" // home outline
$E0FC = "\uE0FC" // home filled
$E09D = "\uE09D" // cloud userfolders
$E282 = "\uE282" // squared minus
$E0EA = "\uE0EA" // system folder
$E0E3 = "\uE0E3" // user directory
$E286 = "\uE286" // system / nodegroups
$E0FE = "\uE0FE" // admin / user
$E228 = "\uE228" // arrow down / v-shape
$E00A = "\uE00A" // arrow down
$E1A0 = "\uE1A0" // bubble
$E1E7 = "\uE1E7" // calculator outline
$E1D1 = "\uE1D1" // close/cancel
$E23C = "\uE23C" // circle filled
$E12A = "\uE12A" // clock recent
$E09C = "\uE09C" // cloud outline
$E0D1 = "\uE0D1" // computer large
$E1D9 = "\uE1D9" // computer small
$E114 = "\uE114" // copy full path
$E0B2 = "\uE0B2" // copy
$E0A0 = "\uE0A0" // customize folder
$E0B8 = "\uE0B8" // cut
$E0E0 = "\uE0E0" // dir arrow
$E0E2 = "\uE0E2" // dir checkbox
$E0FA = "\uE0FA" // dir gear
$E154 = "\uE154" // dir media
$E0DE = "\uE0DE" // dir music
$E203 = "\uE203" // dir plus
$E09E = "\uE09E" // dir shield
$E0E4 = "\uE0E4" // dir sync
$E0A7 = "\uE0A7" // dir user libraries 1
$E0E1 = "\uE0E1" // dir user libraries 2
$E0FF = "\uE0FF" // dir user libraries 3
$E09F = "\uE09F" // dir user
$E0E8 = "\uE0E8" // directory
$E0BD = "\uE0BD" // downloads
$E17A = "\uE17A" // edit text
$E14D = "\uE14D" // executables
$E12E = "\uE12E" // eye look
$E1A3 = "\uE1A3" // file children
$E109 = "\uE109" // file icon filled
$E108 = "\uE108" // file icon outline
$E275 = "\uE275" // file outline
$E0E6 = "\uE0E6" // folder icon filled
$E0E7 = "\uE0E7" // folder icon outline
$E184 = "\uE184" // generate dirtree
$E22C = "\uE22C" // git logo
$E11E = "\uE11E" // globe
$E123 = "\uE123" // goto filled
$E122 = "\uE122" // goto outline
$E14A = "\uE14A" // goto target active
$E159 = "\uE159" // graph monitor
$E09A = "\uE09A" // group by
$E25E = "\uE25E" // hash
$E1A8 = "\uE1A8" // heart filled
$E15E = "\uE15E" // heart outline
$E1A7 = "\uE1A7" // heart outline
$E18C = "\uE18C" // keyboard
$E158 = "\uE158" // lightbulb filled
$E157 = "\uE157" // lightbulb outline
$E187 = "\uE187" // magnifying glass outline / search
$E1B1 = "\uE1B1" // maximize
$E1D3 = "\uE1D3" // minimize
$E0DC = "\uE0DC" // monitor with crescent
$E0BE = "\uE0BE" // monitor with gear
$E087 = "\uE087" // move
$E0CA = "\uE0CA" // move
$E107 = "\uE107" // multiple users
$E16A = "\uE16A" // open dir
$E1F6 = "\uE1F6" // open in new window
$E160 = "\uE160" // open in new tab
$E0A6 = "\uE0A6" // open with 2
$E089 = "\uE089" // open with
$E201 = "\uE201" // open with
$E173 = "\uE173" // open
$E116 = "\uE116" // paint
$E0AF = "\uE0AF" // paste 1
$E16D = "\uE16D" // paste 2
$E0B1 = "\uE0B1" // paste 3
$E0A1 = "\uE0A1" // pencil edit
$E0C9 = "\uE0C9" // pin icon 1
$E03F = "\uE03F" // pin icon 2
$E1D2 = "\uE1D2" // plus
$E15D = "\uE15D" // properties
$E230 = "\uE230" // python
$E132 = "\uE132" // questionmark outline
$E0B4 = "\uE0B4" // recycle bin
$E1AA = "\uE1AA" // recycle
$E094 = "\uE094" // refresh
$E0B5 = "\uE0B5" // rename / copy path
$E0B7 = "\uE0B7" // rename / copy path
$E12B = "\uE12B" // restart process
$E1AB = "\uE1AB" // restart process
$E172 = "\uE172" // round plus
$E099 = "\uE099" // share / send to
$E17C = "\uE17C" // script
$E0F4 = "\uE0F4" // settings gear filled
$E0F3 = "\uE0F3" // settings gear outline
$E0ED = "\uE0ED" // settings slider horizontal
$E0EE = "\uE0EE" // settings slider vertical
$E10A = "\uE10A" // shell config
$E1C4 = "\uE1C4" // shell docs
$E249 = "\uE249" // shell
$E1B5 = "\uE1B5" // shield filled
$E194 = "\uE194" // shield outline
$E1A6 = "\uE0B3" // shortcut add
$E12F = "\uE12F" // shotdown / close
$E28A = "\uE28A" // signal
$E1B0 = "\uE1B0" // size
$E0A2 = "\uE0A2" // sort 1
$E0D7 = "\uE0D7" // sort 2
$E23B = "\uE23B" // square filled / work
$E11B = "\uE11B" // star filled
$E124 = "\uE124" // star offset
$E0C8 = "\uE0C8" // star outline
$E092 = "\uE092" // startup
$E046 = "\uE046" // target
$E0A4 = "\uE0A4" // task manager
$E0C5 = "\uE0C5" // tasks
$E0D6 = "\uE0D6" // terminal filled ps1
$E0AC = "\uE0AC" // terminal outline cmd
$E26E = "\uE26E" // terminal outline general
$E0AB = "\uE0AB" // terminal outline ps1
$E271 = "\uE271" // terminal
$E061 = "\uE061" // text content
$E19D = "\uE19D" // text lines / list content
$E10E = "\uE10E" // textfile outline
$E163 = "\uE163" // thick minus shape / menu-sel
$E167 = "\uE167" // three horizontal dots
$E1DE = "\uE1DE" // trashbin
$E03A = "\uE03A" // undo 1
$E076 = "\uE076" // undo 2
$E04E = "\uE04E" // undo 3
$E093 = "\uE093" // undo 4
$E09B = "\uE09B" // url
$E1BC = "\uE1BC" // user
$E1D7 = "\uE1D7" // utils
$E253 = "\uE253" // view 1
$E150 = "\uE150" // view 2
$E1B8 = "\uE1B8" // windows logo large
$E1B6 = "\uE1B6" // windows logo normal
$E254 = "\uE254" // windows logo offset
$E142 = "\uE142" // windows logo outline
$E1FB = "\uE1FB" // windows
$E0F8 = "\uE0F8" // wrench outline
$E0AA = "\uE0AA" // zip / archived folder
$E1A4 = "\uE1A4" // zip / archived folder

```
### 4. `_a_constants\def_keys.nss`

#### `_a_constants\def_keys.nss`

```java

// docs: `https://nilesoft.org/docs/configuration/properties#keys`

/*
    :: key-combinations for directories

    :: usage:
        cmd=if(KEYS_DIR_OPEN,('@app.dir')),
        commands{
            cmd=if(KEYS_DIR_COPY,clipboard.set('@app.dir')),
            cmd=if(KEYS_DIR_GOTO,command.navigate('@app.dir')),
        }
*/
$KEYS_DIR_COPY = (key(key.control, key.lbutton) OR key(key.control, key.rbutton)) // [Ctrl + Left/Right]
$KEYS_DIR_GOTO = ((!key(key.control) AND window.is_explorer) AND !key(key.shift)) // [Explorer, -Ctrl, -Shift]
$KEYS_DIR_OPEN = ((!key(key.control) AND !window.is_explorer) OR key(key.shift))  // [-Ctrl, Not Explorer] or [Shift]
$KEYS_DIR_TIP  = "» &Dir Keyboard Modifiers\n» Key.CTRL\t// Copy Path\n» Key.SHIFT\t// Open Folder\n"

/*
    :: key-combinations for executables

    :: usage:
        admin=KEYS_EXE_ADMIN
        cmd=if(KEYS_EXE_OPEN_EXE,('"@sys.dir\System32\cmd.exe"')),
        commands{
           cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@sys.dir\System32\')),
           cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@sys.dir\System32\cmd.exe')),
           cmd=if(KEYS_EXE_OPEN_DIR,('"@sys.dir\System32\"')),
        }
*/
$KEYS_EXE_ADMIN    = key.rbutton() AND !key(key.control OR key.shift OR key.alt)   // [Right, -Ctrl, -Alt, -Shift]
$KEYS_EXE_COPY_DIR = key(key.control, key.lbutton) AND !key.alt()                  // [Ctrl + Left, -Alt]
$KEYS_EXE_COPY_EXE = key(key.control, key.rbutton) AND !key.alt()                  // [Ctrl + Right, -Alt]
$KEYS_EXE_OPEN_DIR = key(key.shift, key.lbutton) AND !(key.control() OR key.alt()) // [Shift + Left, -Ctrl, -Alt]
$KEYS_EXE_OPEN_EXE = (!key(key.shift) AND !key(key.control) AND !key(key.alt))     // [-Ctrl, -Alt, -Shift]
//
$KEYS_EXE_GOTO_CFG = key(key.alt, key.lbutton) AND !(key.control() OR key.shift()) // [Alt + Left, -Ctrl, -Shift]
$KEYS_EXE_GOTO_NSS = key(key.alt, key.shift, key.lbutton) AND !key.control()       // [Alt + Shift + Left, -Ctrl]
$KEYS_EXE_GOTO_SRC = key(key.alt, key.rbutton) AND !(key.control() OR key.shift()) // [Alt + Right, -Ctrl, -Shift]
//
$KEYS_EXE_TIP      = "» &Exe Keyboard Modifiers\n» Key.CTRL\t// Copy Path\n» Key.SHIFT\t// Open Folder\n» Mouse.RIGHT\t// Admin\n"

/*
    :: key-combinations for urls

    :: usage:
        commands{
            cmd=if('@KEYS_URL_COPY',clipboard.set('@URL_GOOGLE')),
            cmd=if('@KEYS_URL_OPEN',('@URL_GOOGLE')),
        }
*/
$KEYS_URL_COPY = (key(key.control, key.lbutton) OR key(key.control, key.rbutton)) // [Ctrl + Left/Right]
$KEYS_URL_OPEN = (!key(key.control)) // [-Ctrl]
$KEYS_URL_TIP  = "» &Url Keyboard Modifiers\n» Key.CTRL\t// Copy URL\n» Mouse.LEFT\t// Open\n"

/*
    :: key-combinations for conditional menu visibility

    :: usage:
        menu(type='Taskbar' vis=KEYS_MNU_VISIBILITY_0_ALWAYS expanded=true) {
            import '@app.dir/NSS/_3_items/itm_app_sys_cmd.nss'
        }
        menu(type='Taskbar' vis=KEYS_MNU_VISIBILITY_1_CTRL expanded=true) {
            import '@app.dir/NSS/_3_items/itm_user_app_bulkrenameutility.nss'
        }
*/
$KEYS_MNU_VISIBILITY_0_ALWAYS              = true                             // [Always Visible]
$KEYS_MNU_VISIBILITY_0_DEFAULT             = !(key.control() OR key.shift())  // [-Ctrl, -Shift]
//
$KEYS_MNU_VISIBILITY_1_ALT                 = (key.alt())                      // [Alt]
$KEYS_MNU_VISIBILITY_1_CTRL                = (key.control() AND !key.shift()) // [Ctrl, -Shift]
$KEYS_MNU_VISIBILITY_1_SHIFT               = (!key.control() AND key.shift()) // [Shift, -Ctrl]
//
$KEYS_MNU_VISIBILITY_2_CTRL_SHIFT          = (key.control() AND key.shift())  // [Ctrl + Shift]
//
$KEYS_MNU_VISIBILITY_3_NONE_OR_CTRL_SHIFT  = ('@KEYS_MNU_VISIBILITY_0_DEFAULT' OR '@KEYS_MNU_VISIBILITY_2_CTRL_SHIFT')  // [No Modifiers or Ctrl + Shift]
$KEYS_MNU_VISIBILITY_3_SHIFT_OR_CTRL_SHIFT = ('@KEYS_MNU_VISIBILITY_1_SHIFT' OR '@KEYS_MNU_VISIBILITY_2_CTRL_SHIFT') // [Shift or Ctrl + Shift]

```
### 5. `_a_constants\def_paths.nss`

#### `_a_constants\def_paths.nss`

```java

// docs: `https://nilesoft.org/docs/configuration/properties#tip`


/*
    :: system paths

    :: usage: `cmd=command.navigate('@DIR_SYS_DESKTOP')`
*/

// --- directories
$DIR_SYS_SHELL           = '@app.dir'
$DIR_SYS_PROGRAMDATA     = '@sys.programdata'
$DIR_SYS_PROGRAMFILES    = '@sys.prog'
$DIR_SYS_PROGRAMFILESX86 = '@sys.prog32'
$DIR_SYS_SYSTEM32        = '@sys.bin'
$DIR_SYS_SYSTEMROOT      = '@sys.root'
$DIR_SYS_SYSWOW64        = '@sys.wow'
$DIR_SYS_TEMP            = '@sys.temp'
$DIR_SYS_USERS           = '@sys.users'
$DIR_SYS_WINDOWS         = '@sys.dir'
$DIR_SYS_FONTS           = '@sys.dir/Fonts'
$DIR_SYS_APPDATA         = '@user.appdata'
$DIR_SYS_DESKTOP         = '@user.desktop'
$DIR_SYS_DOCUMENTS       = '@user.documents'
$DIR_SYS_DOWNLOADS       = '@user.downloads'
$DIR_SYS_LOCALAPPDATA    = '@user.localappdata'
$DIR_SYS_RECENT          = '@user.appdata/Microsoft/Windows/Recent'
$DIR_SYS_STARTMENU       = '@user.startmenu'
$DIR_SYS_STARTUP         = '@user.startmenu/Programs/Startup'
$DIR_SYS_USERPROFILE     = '@user.directory'
$DIR_SYS_THISPC          = 'file:/'
$DIR_SYS_APPLICATIONS    = 'shell:appsfolder'
$DIR_SYS_CONTROLPANEL    = 'shell:::{5399e694-6ce5-4d6c-8fce-1d8870fdcba0}'
$DIR_SYS_NETWORKADAPTERS = 'shell:::{7007acc7-3202-11d1-aad2-00805fc1270e}'
$DIR_SYS_RECYCLEBIN      = 'shell:::{645ff040-5081-101b-9f08-00aa002f954e}'

```
### 6. `_a_constants\def_tooltips.nss`

#### `_a_constants\def_tooltips.nss`

```java

// docs: `https://nilesoft.org/docs/configuration/properties#tip`


/*
    :: tooltip colors
*/
$TIP1_COLOR = [#1B3A40, #fff] // tip.primary
$TIP2_COLOR = [#1A377D, #fff] // tip.info
$TIP3_COLOR = [#37275C, #fff] // tip.success
$TIP4_COLOR = [#434328, #fff] // tip.warning
$TIP5_COLOR = [#42140B, #fff] // tip.danger

/*
    :: agnostic tooltip style aliases

    :: usage: `tip=[app.dir,TIP1,0.1]`
*/
$TIP0 = ""
$TIP1 = tip.primary
$TIP2 = tip.info
$TIP3 = tip.success
$TIP4 = tip.warning
$TIP5 = tip.danger


/*
    :: predefined tooltip messages

    :: usage: `tip=TIP_ADMIN`
*/
$TIP_ADMIN = ["Right-click to run as &Admin",TIP5,0.0]

```
### 7. `_a_constants\def_uris.nss`

#### `_a_constants\def_uris.nss`

```java

// docs: `...`


/*
    :: system shortcuts

    :: usage: `cmd='@URI_SETTINGS_ABOUT'`
*/

// --- system
$URI_SETTINGS                     = 'ms-settings:'
$URI_SETTINGS_ABOUT               = 'ms-settings:about'
$URI_SETTINGS_CLIPBOARD           = 'ms-settings:clipboard'
$URI_SETTINGS_DISPLAY             = 'ms-settings:display'
$URI_SETTINGS_DISPLAY_ADVANCED    = 'ms-settings:display-advanced'
$URI_SETTINGS_DISPLAY_GRAPHICS    = 'ms-settings:display-advancedgraphics'
$URI_SETTINGS_DISPLAY_NIGHTLIGHT  = 'ms-settings:nightlight'
$URI_SETTINGS_DISPLAY_ORIENTATION = 'ms-settings:screenrotation'
$URI_SETTINGS_DISPLAY_WIRELESS    = 'ms-settings-connectabledevices:devicediscovery'
$URI_SETTINGS_ENCRYPTION          = 'ms-settings:deviceencryption'
$URI_SETTINGS_FOCUS               = 'ms-settings:quiethours'
$URI_SETTINGS_FOCUS_DISPLAY       = 'ms-settings:quietmomentspresentation'
$URI_SETTINGS_FOCUS_GAMING        = 'ms-settings:quietmomentsgame'
$URI_SETTINGS_FOCUS_HOURS         = 'ms-settings:quietmomentsscheduled'
$URI_SETTINGS_MULTITASKING        = 'ms-settings:multitasking'
$URI_SETTINGS_NOTIFICATIONS       = 'ms-settings:notifications'
$URI_SETTINGS_POWER_BATTERY       = 'ms-settings:batterysaver'
$URI_SETTINGS_POWER_BATTERY_SAVER = 'ms-settings:batterysaver-settings'
$URI_SETTINGS_POWER_BATTERY_USAGE = 'ms-settings:batterysaver-usagedetails'
$URI_SETTINGS_POWER_SLEEP         = 'ms-settings:powersleep'
$URI_SETTINGS_PROJECTING          = 'ms-settings:project'
$URI_SETTINGS_REMOTEDESKTOP       = 'ms-settings:remotedesktop'
$URI_SETTINGS_SHARING             = 'ms-settings:crossdevice'
$URI_SETTINGS_SOUND               = 'ms-settings:sound'
$URI_SETTINGS_SOUND_DEVICES       = 'ms-settings:sound-devices'
$URI_SETTINGS_SOUND_VOLUME        = 'ms-settings:apps-volume'
$URI_SETTINGS_STORAGE             = 'ms-settings:storagesense'
$URI_SETTINGS_STORAGE_LOCATIONS   = 'ms-settings:savelocations'
$URI_SETTINGS_STORAGE_SENSE       = 'ms-settings:storagepolicies'

// --- devices
$URI_SETTINGS_DEVICES_AUTOPLAY  = 'ms-settings:autoplay'
$URI_SETTINGS_DEVICES_BLUETOOTH = 'ms-settings:bluetooth'
$URI_SETTINGS_DEVICES_KEYBOARD  = 'ms-settings:devicestyping-hwkbtextsuggestions'
$URI_SETTINGS_DEVICES_MOUSE     = 'ms-settings:mousetouchpad'
$URI_SETTINGS_DEVICES_PEN       = 'ms-settings:pen'
$URI_SETTINGS_DEVICES_PRINTERS  = 'ms-settings:printers'
$URI_SETTINGS_DEVICES_TOUCHPAD  = 'ms-settings:devices-touchpad'
$URI_SETTINGS_DEVICES_TYPING    = 'ms-settings:typing'
$URI_SETTINGS_DEVICES_USB       = 'ms-settings:usb'
$URI_SETTINGS_DEVICES_WHEEL     = 'ms-settings:wheel'

// --- phone
$URI_SETTINGS_PHONE_ADD        = 'ms-settings:mobile-devices-addphone'
$URI_SETTINGS_PHONE_SETTINGS   = 'ms-settings:mobile-devices'
$URI_SETTINGS_PHONE_YOUR_PHONE = 'ms-settings:mobile-devices-addphone-direct'

// --- network & internet
$URI_SETTINGS_NETWORK              = 'ms-settings:network'
$URI_SETTINGS_NETWORK_AIRPLANE     = 'ms-settings:network-airplanemode'
$URI_SETTINGS_NETWORK_AVAILABLE    = 'ms-availablenetworks:'
$URI_SETTINGS_NETWORK_CELLULAR     = 'ms-settings:network-cellular'
$URI_SETTINGS_NETWORK_DATA_USAGE   = 'ms-settings:datausage'
$URI_SETTINGS_NETWORK_DIALUP       = 'ms-settings:network-dialup'
$URI_SETTINGS_NETWORK_ETHERNET     = 'ms-settings:network-ethernet'
$URI_SETTINGS_NETWORK_HOTSPOT      = 'ms-settings:network-mobilehotspot'
$URI_SETTINGS_NETWORK_NFC          = 'ms-settings:nfctransactions'
$URI_SETTINGS_NETWORK_PROXY        = 'ms-settings:network-proxy'
$URI_SETTINGS_NETWORK_STATUS       = 'ms-settings:network-status'
$URI_SETTINGS_NETWORK_VPN          = 'ms-settings:network-vpn'
$URI_SETTINGS_NETWORK_WIFI         = 'ms-settings:network-wifi'
$URI_SETTINGS_NETWORK_WIFI_CALLING = 'ms-settings:network-wificalling'
$URI_SETTINGS_NETWORK_WIFI_MANAGE  = 'ms-settings:network-wifisettings'

// --- personalization
$URI_SETTINGS_PERSONALIZATION               = 'ms-settings:personalization'
$URI_SETTINGS_PERSONALIZATION_BACKGROUND    = 'ms-settings:personalization-background'
$URI_SETTINGS_PERSONALIZATION_COLORS        = 'ms-settings:personalization-colors'
$URI_SETTINGS_PERSONALIZATION_FONTS         = 'ms-settings:fonts'
$URI_SETTINGS_PERSONALIZATION_LOCK_SCREEN   = 'ms-settings:lockscreen'
$URI_SETTINGS_PERSONALIZATION_START         = 'ms-settings:personalization-start'
$URI_SETTINGS_PERSONALIZATION_START_FOLDERS = 'ms-settings:personalization-start-places'
$URI_SETTINGS_PERSONALIZATION_TASKBAR       = 'ms-settings:taskbar'
$URI_SETTINGS_PERSONALIZATION_THEMES        = 'ms-settings:themes'

// --- apps
$URI_SETTINGS_APPS               = 'ms-settings:appsfeatures'
$URI_SETTINGS_APPS_DEFAULT       = 'ms-settings:defaultapps'
$URI_SETTINGS_APPS_MAPS_DOWNLOAD = 'ms-settings:maps-downloadmaps'
$URI_SETTINGS_APPS_OFFLINE_MAPS  = 'ms-settings:maps'
$URI_SETTINGS_APPS_OPTIONAL      = 'ms-settings:optionalfeatures'
$URI_SETTINGS_APPS_STARTUP       = 'ms-settings:startupapps'
$URI_SETTINGS_APPS_VIDEO         = 'ms-settings:videoplayback'
$URI_SETTINGS_APPS_WEBSITES      = 'ms-settings:appsforwebsites'

// --- accounts
$URI_SETTINGS_ACCOUNTS_EMAIL              = 'ms-settings:emailandaccounts'
$URI_SETTINGS_ACCOUNTS_FAMILY             = 'ms-settings:otherusers'
$URI_SETTINGS_ACCOUNTS_INFO               = 'ms-settings:yourinfo'
$URI_SETTINGS_ACCOUNTS_KIOSK              = 'ms-settings:assignedaccess'
$URI_SETTINGS_ACCOUNTS_SIGNIN             = 'ms-settings:signinoptions'
$URI_SETTINGS_ACCOUNTS_SIGNIN_DYNAMICLOCK = 'ms-settings:signinoptions-dynamiclock'
$URI_SETTINGS_ACCOUNTS_SIGNIN_FACE        = 'ms-settings:signinoptions-launchfaceenrollment'
$URI_SETTINGS_ACCOUNTS_SIGNIN_FINGERPRINT = 'ms-settings:signinoptions-launchfingerprintenrollment'
$URI_SETTINGS_ACCOUNTS_SIGNIN_SECURITYKEY = 'ms-settings:signinoptions-launchsecuritykeyenrollment'
$URI_SETTINGS_ACCOUNTS_SYNC               = 'ms-settings:sync'
$URI_SETTINGS_ACCOUNTS_WORKPLACE          = 'ms-settings:workplace'

// --- time & language
$URI_SETTINGS_TIME_DATE     = 'ms-settings:dateandtime'
$URI_SETTINGS_TIME_LANGUAGE = 'ms-settings:regionlanguage'
$URI_SETTINGS_TIME_REGION   = 'ms-settings:regionformatting'
$URI_SETTINGS_TIME_SPEECH   = 'ms-settings:speech'



// --- gaming
$URI_SETTINGS_GAMING_BAR          = 'ms-settings:gaming-gamebar'
$URI_SETTINGS_GAMING_BROADCASTING = 'ms-settings:gaming-broadcasting'
$URI_SETTINGS_GAMING_CAPTURES     = 'ms-settings:gaming-gamedvr'
$URI_SETTINGS_GAMING_MODE         = 'ms-settings:gaming-gamemode'
$URI_SETTINGS_GAMING_XBOX         = 'ms-settings:gaming-xboxnetworking'

// --- ease of access
$URI_SETTINGS_EASE_AUDIO        = 'ms-settings:easeofaccess-audio'
$URI_SETTINGS_EASE_CAPTIONS     = 'ms-settings:easeofaccess-closedcaptioning'
$URI_SETTINGS_EASE_COLORFILTERS = 'ms-settings:easeofaccess-colorfilter'
$URI_SETTINGS_EASE_CURSOR       = 'ms-settings:easeofaccess-cursor'
$URI_SETTINGS_EASE_DISPLAY      = 'ms-settings:easeofaccess-display'
$URI_SETTINGS_EASE_EYECONTROL   = 'ms-settings:easeofaccess-eyecontrol'
$URI_SETTINGS_EASE_HIGHCONTRAST = 'ms-settings:easeofaccess-highcontrast'
$URI_SETTINGS_EASE_KEYBOARD     = 'ms-settings:easeofaccess-keyboard'
$URI_SETTINGS_EASE_MAGNIFIER    = 'ms-settings:easeofaccess-magnifier'
$URI_SETTINGS_EASE_MOUSE        = 'ms-settings:easeofaccess-cursorandpointersize'
$URI_SETTINGS_EASE_MOUSE        = 'ms-settings:easeofaccess-mouse'
$URI_SETTINGS_EASE_NARRATOR     = 'ms-settings:easeofaccess-narrator'
$URI_SETTINGS_EASE_SPEECH       = 'ms-settings:easeofaccess-speechrecognition'

// --- search
$URI_SETTINGS_SEARCH_MORE        = 'ms-settings:search-moredetails'
$URI_SETTINGS_SEARCH_PERMISSIONS = 'ms-settings:search-permissions'
$URI_SETTINGS_SEARCH_WINDOWS     = 'ms-settings:cortana-windowssearch'

// --- cortana
$URI_SETTINGS_CORTANA             = 'ms-settings:cortana'
$URI_SETTINGS_CORTANA_MORE        = 'ms-settings:cortana-moredetails'
$URI_SETTINGS_CORTANA_PERMISSIONS = 'ms-settings:cortana-permissions'
$URI_SETTINGS_CORTANA_TALK        = 'ms-settings:cortana-talktocortana'

// --- privacy
$URI_SETTINGS_PRIVACY                 = 'ms-settings:privacy'
$URI_SETTINGS_PRIVACY_ACCOUNTINFO     = 'ms-settings:privacy-accountinfo'
$URI_SETTINGS_PRIVACY_ACTIVITY        = 'ms-settings:privacy-activityhistory'
$URI_SETTINGS_PRIVACY_APPDIAGNOSTICS  = 'ms-settings:privacy-appdiagnostics'
$URI_SETTINGS_PRIVACY_AUTODOWNLOADS   = 'ms-settings:privacy-automaticfiledownloads'
$URI_SETTINGS_PRIVACY_BACKGROUND_APPS = 'ms-settings:privacy-backgroundapps'
$URI_SETTINGS_PRIVACY_CALENDAR        = 'ms-settings:privacy-calendar'
$URI_SETTINGS_PRIVACY_CALL_HISTORY    = 'ms-settings:privacy-callhistory'
$URI_SETTINGS_PRIVACY_CAMERA          = 'ms-settings:privacy-webcam'
$URI_SETTINGS_PRIVACY_CONTACTS        = 'ms-settings:privacy-contacts'
$URI_SETTINGS_PRIVACY_DIAGNOSTICS     = 'ms-settings:privacy-feedback'
$URI_SETTINGS_PRIVACY_DOCUMENTS       = 'ms-settings:privacy-documents'
$URI_SETTINGS_PRIVACY_EMAIL           = 'ms-settings:privacy-email'
$URI_SETTINGS_PRIVACY_FILESYSTEM      = 'ms-settings:privacy-broadfilesystemaccess'
$URI_SETTINGS_PRIVACY_INKING          = 'ms-settings:privacy-speechtyping'
$URI_SETTINGS_PRIVACY_LOCATION        = 'ms-settings:privacy-location'
$URI_SETTINGS_PRIVACY_MESSAGING       = 'ms-settings:privacy-messaging'
$URI_SETTINGS_PRIVACY_MICROPHONE      = 'ms-settings:privacy-microphone'
$URI_SETTINGS_PRIVACY_NOTIFICATIONS   = 'ms-settings:privacy-notifications'
$URI_SETTINGS_PRIVACY_OTHER_DEVICES   = 'ms-settings:privacy-customdevices'
$URI_SETTINGS_PRIVACY_PICTURES        = 'ms-settings:privacy-pictures'
$URI_SETTINGS_PRIVACY_RADIOS          = 'ms-settings:privacy-radios'
$URI_SETTINGS_PRIVACY_SPEECH          = 'ms-settings:privacy-speech'
$URI_SETTINGS_PRIVACY_TASKS           = 'ms-settings:privacy-tasks'
$URI_SETTINGS_PRIVACY_VIDEOS          = 'ms-settings:privacy-documents'
$URI_SETTINGS_PRIVACY_VOICE           = 'ms-settings:privacy-voiceactivation'

// --- update & security
$URI_SETTINGS_UPDATE                 = 'ms-settings:windowsupdate'
$URI_SETTINGS_UPDATE_ACTIVATION      = 'ms-settings:activation'
$URI_SETTINGS_UPDATE_ADVANCEDOPTIONS = 'ms-settings:windowsupdate-options'
$URI_SETTINGS_UPDATE_BACKUP          = 'ms-settings:backup'
$URI_SETTINGS_UPDATE_DELIVERY        = 'ms-settings:delivery-optimization'
$URI_SETTINGS_UPDATE_DEVELOPERS      = 'ms-settings:developers'
$URI_SETTINGS_UPDATE_FINDMYDEVICE    = 'ms-settings:findmydevice'
$URI_SETTINGS_UPDATE_HISTORY         = 'ms-settings:windowsupdate-history'
$URI_SETTINGS_UPDATE_INSIDER         = 'ms-settings:windowsinsider'
$URI_SETTINGS_UPDATE_OPTIONAL        = 'ms-settings:windowsupdate-optionalupdates'
$URI_SETTINGS_UPDATE_RECOVERY        = 'ms-settings:recovery'
$URI_SETTINGS_UPDATE_RESTART         = 'ms-settings:windowsupdate-restartoptions'
$URI_SETTINGS_UPDATE_SECURITY        = 'ms-settings:windowsdefender'
$URI_SETTINGS_UPDATE_SECURITY_OPEN   = 'windowsdefender:'
$URI_SETTINGS_UPDATE_TROUBLESHOOT    = 'ms-settings:troubleshoot'

// --- mixed reality
$URI_SETTINGS_MR             = 'ms-settings:holographic'
$URI_SETTINGS_MR_AUDIO       = 'ms-settings:holographic-audio'
$URI_SETTINGS_MR_ENVIRONMENT = 'ms-settings:privacy-holographic-environment'
$URI_SETTINGS_MR_HEADSET     = 'ms-settings:holographic-headset'
$URI_SETTINGS_MR_UNINSTALL   = 'ms-settings:holographic-management'

// --- surface hub
$URI_SETTINGS_SH_ACCOUNTS          = 'ms-settings:surfacehub-accounts'
$URI_SETTINGS_SH_SESSION_CLEANUP   = 'ms-settings:surfacehub-sessioncleanup'
$URI_SETTINGS_SH_TEAM_CONFERENCING = 'ms-settings:surfacehub-calling'
$URI_SETTINGS_SH_TEAM_DEVICE       = 'ms-settings:surfacehub-devicemanagenent'
$URI_SETTINGS_SH_WELCOME           = 'ms-settings:surfacehub-welcome'

// =============================================================================
// -> system management consoles
$exe_comp_mgmt    = '"@sys.dir\System32\compmgmt.msc"'
$exe_dev_mgmt     = '"@sys.dir\System32\devmgmt.msc"'
$exe_disk_mgmt    = '"@sys.dir\System32\diskmgmt.msc"'
$exe_event_vwr    = '"@sys.dir\System32\eventvwr.msc"'
$exe_perf_mon     = '"@sys.dir\System32\perfmon.msc"'
$exe_print_mgmt   = '"@sys.dir\System32\printmanagement.msc"'
$exe_services     = '"@sys.dir\System32\services.msc"'
$exe_shared_fldrs = '"@sys.dir\System32\fsmgmt.msc"'
$exe_task_schd    = '"@sys.dir\System32\taskschd.msc"'
$exe_user_mgmt    = '"@sys.dir\System32\lusrmgr.msc"'

// -> control panel utilities
$exe_display      = '"@sys.dir\System32\desk.cpl"'
$exe_firewall     = '"@sys.dir\System32\firewall.cpl"'
$exe_net_adapters = '"@sys.dir\System32\ncpa.cpl"'
$exe_power_opts   = '"@sys.dir\System32\powercfg.cpl"'
$exe_programs     = '"@sys.dir\System32\appwiz.cpl"'
$exe_region       = '"@sys.dir\System32\intl.cpl"'
$exe_sound        = '"@sys.dir\System32\mmsys.cpl"'
$exe_sys_props    = '"@sys.dir\System32\sysdm.cpl"'

// -> common windows applications
$exe_calc         = '"@sys.dir\System32\calc.exe"'
$exe_charmap      = '"@sys.dir\System32\charmap.exe"'
$exe_cmd          = '"@sys.dir\System32\cmd.exe"'
$exe_control      = '"@sys.dir\System32\control.exe"'
$exe_ise          = '"@sys.dir\System32\WindowsPowerShell\v1.0\powershell_ise.exe"'
$exe_magnify      = '"@sys.dir\System32\magnify.exe"'
$exe_mspaint      = '"mspaint.exe"'
$exe_notepad      = '"@sys.dir\System32\notepad.exe"'
$exe_osk          = '"@sys.dir\System32\osk.exe"'
$exe_powershell   = '"@sys.dir\System32\WindowsPowerShell\v1.0\powershell.exe"'
$exe_regedit      = '"@sys.dir\regedit.exe"'
$exe_res_mon      = '"@sys.dir\System32\resmon.exe"'
$exe_task_mgr     = '"@sys.dir\System32\taskmgr.exe"'

// --- language bar options
$exe_languagebar = 'rundll32.exe Shell32.dll,Control_RunDLL input.dll,,{C07337D3-DB2C-4D0B-9A93-B722A6C106E2}'


// //
// // ===========================================================================
// // Windows 10 RUN commands (Win+R)
// // ---------------------------------------------------------------------------
// "Open Documents Folder"                           'documents'
// "Open Videos folder"                              'videos'
// "Open Downloads Folder"                           'downloads'
// "Open Favorites Folder"                           'favorites'
// "Open Recent Folder"                              'recent'
// "Open Pictures Folder"                            'pictures'
// "Adding a new Device"                             'devicepairingwizard'
// "About Windows dialog"                            'winver'
// "Add Hardware Wizard"                             'hdwwiz'
// "Advanced User Accounts"                          'netplwiz'
// "Authorization Manager"                           'azman.msc'
// "Backup and Restore"                              'sdclt'
// "Bluetooth File Transfer"                         'fsquirt'
// "Calculator"                                      'calc'
// "Certificates"                                    'certmgr.msc'
// "Change Computer Performance Settings"            'systempropertiesperformance'
// "Change Data Execution Prevention Settings"       'systempropertiesdataexecutionprevention'
// "Change Data Execution Prevention Settings"       'printui'
// "Character Map"                                   'charmap'
// "ClearType Tuner"                                 'cttune'
// "Color Management"                                'colorcpl'
// "Command Prompt"                                  'cmd'
// "Component Services"                              'comexp.msc'
// "Component Services"                              'dcomcnfg'
// "Computer Management"                             'compmgmt.msc'
// "Computer Management"                             'compmgmtlauncher'
// "Connect to a Projector"                          'displayswitch'
// "Control Panel"                                   'control'
// "Create A Shared Folder Wizard"                   'shrpubw'
// "Create a System Repair Disc"                     'recdisc'
// "Data Execution Prevention"                       'systempropertiesdataexecutionprevention'
// "Date and Time"                                   'timedate.cpl'
// "Default Location"                                'locationnotifications'
// "Device Manager"                                  'devmgmt.msc'
// "Device Manager"                                  'hdwwiz.cpl'
// "Device Pairing Wizard"                           'devicepairingwizard'
// "Diagnostics Troubleshooting Wizard"              'msdt'
// "Digitizer Calibration Tool"                      'tabcal'
// "DirectX Diagnostic Tool"                         'dxdiag'
// "Disk Cleanup"                                    'cleanmgr'
// "Disk Defragmenter"                               'dfrgui'
// "Disk Management"                                 'diskmgmt.msc'
// "Display"                                         'dpiscaling'
// "Display Color Calibration"                       'dccw'
// "Display Switch"                                  'displayswitch'
// "DPAPI Key Migration Wizard"                      'dpapimig'
// "Driver Verifier Manager"                         'verifier'
// "Ease of Access Center"                           'utilman'
// "EFS Wizard"                                      'rekeywiz'
// "Event Viewer"                                    'eventvwr.msc'
// "Fax Cover Page Editor"                           'fxscover'
// "File Signature Verification"                     'sigverif'
// "Font Viewer"                                     'fontview'
// "Game Controllers"                                'joy.cpl'
// "IExpress Wizard"                                 'iexpress'
// "Internet Explorer"                               'iexplore'
// "Internet Options"                                'inetcpl.cpl'
// "iSCSI Initiator Configuration Tool"              'iscsicpl'
// "Language Pack Installer"                         'lpksetup'
// "Local Group Policy Editor"                       'gpedit.msc'
// "Local Security Policy"                           'secpol.msc'
// "Local Users and Groups"                          'lusrmgr.msc'
// "Location Activity"                               'locationnotifications'
// "Magnifier"                                       'magnify'
// "Malicious Software Removal Tool"                 'mrt'
// "Manage Your File Encryption Certificates"        'rekeywiz'
// "Microsoft Management Console"                    'mmc'
// "Microsoft Support Diagnostic Tool"               'msdt'
// "Mouse"                                           'main.cpl'
// "NAP Client Configuration"                        'napclcfg.msc'
// "Narrator"                                        'narrator'
// "Network Connections"                             'ncpa.cpl'
// "New Scan Wizard"                                 'wiaacmgr'
// "Notepad"                                         'notepad'
// "ODBC Data Source Administrator"                  'odbcad32'
// "ODBC Driver Configuration"                       'odbcconf'
// "On-Screen Keyboard"                              'osk'
// "Paint"                                           'mspaint'
// "Pen and Touch"                                   'tabletpc.cpl'
// "People Near Me"                                  'collab.cpl'
// "Performance Monitor"                             'perfmon.msc'
// "Performance Options"                             'systempropertiesperformance'
// "Phone and Modem"                                 'telephon.cpl'
// "Phone Dialer"                                    'dialer'
// "Power Options"                                   'powercfg.cpl'
// "Presentation Settings"                           'presentationsettings'
// "Print Management"                                'printmanagement.msc'
// "Printer Migration"                               'printbrmui'
// "Printer User Interface"                          'printui'
// "Private Character Editor"                        'eudcedit'
// "Problem Steps Recorder"                          'psr'
// "Programs and Features"                           'appwiz.cpl'
// "Protected Content Migration"                     'dpapimig'
// "Region and Language"                             'intl.cpl'
// "Registry Editor"                                 'regedit'
// "Registry Editor 32"                              'regedt32'
// "Remote Access Phonebook"                         'rasphone'
// "Remote Desktop Connection"                       'mstsc'
// "Resource Monitor"                                'resmon'
// "Resultant Set of Policy"                         'rsop.msc'
// "SAM Lock Tool"                                   'syskey'
// "Screen Resolution"                               'desk.cpl'
// "Securing the Windows Account Database"           'syskey'
// "Services"                                        'services.msc'
// "Set Program Access and Computer Defaults"        'computerdefaults'
// "Share Creation Wizard"                           'shrpubw'
// "Shared Folders"                                  'fsmgmt.msc'
// "Signout"                                         'logoff'
// "Snipping Tool"                                   'snippingtool'
// "Sound"                                           'mmsys.cpl'
// "Sound recorder"                                  'soundrecorder'
// "SQL Server Client Network Utility"               'cliconfg'
// "Sticky Notes"                                    'stikynot'
// "Stored User Names and Passwords"                 'credwiz'
// "Sync Center"                                     'mobsync'
// "System Configuration"                            'msconfig'
// "System Configuration Editor"                     'sysedit'
// "System Information"                              'msinfo32'
// "System Properties"                               'sysdm.cpl'
// "System Properties (Advanced Tab)"                'systempropertiesadvanced'
// "System Properties (Computer Name Tab)"           'systempropertiescomputername'
// "System Properties (Hardware Tab)"                'systempropertieshardware'
// "System Properties (Remote Tab)"                  'systempropertiesremote'
// "System Properties (System Protection Tab)"       'systempropertiesprotection'
// "System Restore"                                  'rstrui'
// "Task Manager"                                    'taskmgr'
// "Task Scheduler"                                  'taskschd.msc'
// "Trusted Platform Module (TPM) Management"        'tpm.msc'
// "Turn Windows features on or off"                 'optionalfeatures'
// "User Account Control Settings"                   'useraccountcontrolsettings'
// "Utility Manager"                                 'utilman'
// "Volume Mixer"                                    'sndvol'
// "Windows Action Center"                           'wscui.cpl'
// "Windows Activation Client"                       'slui'
// "Windows Anytime Upgrade Results"                 'windowsanytimeupgraderesults'
// "Windows Disc Image Burning Tool"                 'isoburn'
// "Windows Explorer"                                'explorer'
// "Windows Fax and Scan"                            'wfs'
// "Windows Firewall"                                'firewall.cpl'
// "Windows Firewall with Advanced Security"         'wf.msc'
// "Windows Journal"                                 'journal'
// "Windows Media Player"                            'wmplayer'
// "Windows Memory Diagnostic Scheduler"             'mdsched'
// "Windows Mobility Center"                         'mblctr'
// "Windows Picture Acquisition Wizard"              'wiaacmgr'
// "Windows PowerShell"                              'powershell'
// "Windows PowerShell ISE"                          'powershell_ise'
// "Windows Remote Assistance"                       'msra'
// "Windows Repair Disc"                             'recdisc'
// "Windows Script Host"                             'wscript'
// "Windows Update"                                  'wuapp'
// "Windows Update Standalone Installer"             'wusa'
// "Versione Windows"                                'winver'
// "WMI Management"                                  'wmimgmt.msc'
// "WordPad"                                         'write'
// "XPS Viewer"                                      'xpsrchvw'
```
### 8. `_b_config\cfg_settings.nss`

#### `_b_config\cfg_settings.nss`

```java

// docs: `https://nilesoft.org/docs/configuration/settings`


settings {
    priority=1
    showdelay = 250
    tip.enabled=true
    //
    exclude.where = (str.equals(process.name, [
        "Bulk Rename Utility",
        "filezilla",
        "Dropbox"
    ]))
    //
    modify.remove.duplicate=1
}
```
### 9. `_b_config\cfg_theme.nss`

#### `_b_config\cfg_theme.nss`

```java

// docs: `https://nilesoft.org/docs/configuration/themes`


theme {
    // `[auto, classic, white, black, modern]`
    name = "modern"
    // `[auto, compact, small, medium, large, wide]`
    view = view.small
    // `[true, false]`
    dark = true
    //
    background {
        opacity = 95
        color = #0B1127
        effect = auto
    }
    //
    item {
        opacity = 100
        radius = 0
        prefix = 1
        text {
            normal = #B4BFE4
            select = #FFFFFF
            normal-disabled = #7C839E
            select-disabled = #8B93B1
        }
        back {
            select = #3AC9B326
            select-disabled = #171820
        }
    }
    //
    symbol {
        normal = #89B4FA
        select = #89B4FA
        normal-disabled = #A6ADC8
        select-disabled = #A6ADC8
    }
    //
    image {
        enabled = true
        color = [#CDD6F4, #89B4FA, #1E1E2E]
        gap = 13
        align = 2
    }
    //
    separator {
        size = 1
        color = #313244
    }
    //
    border {
        enabled = true
        size = 1
        color = #89B4FA
        opacity = 40
        radius = 1
    }
    //
    shadow {
        enabled = true
        size = 5
        opacity = 5
        color = #0D0D15
    }
    //
    tip {
        enabled = true
        opacity = 65
        radius = 0
        padding = [8, 4, 8, 4]
        primary = TIP1_COLOR
        info = TIP2_COLOR
        success = TIP3_COLOR
        warning = TIP4_COLOR
        danger = TIP5_COLOR
    }
}
```
### 10. `_c_overrides\__update_everything64.nss`

#### `_c_overrides\__update_everything64.nss`

```java

//
$IF_EVERYTHING64_OPEN                  = process.name=="Everything64" && str.equals(this.name,   "Open")
$IF_EVERYTHING64_OPEN_PATH             = process.name=="Everything64" && str.equals(this.name,   "Open Path")
$IF_EVERYTHING64_OPEN_WITH             = process.name=="Everything64" && str.equals(this.name,   "Open With")
$IF_EVERYTHING64_COPY_NAME             = process.name=="Everything64" && str.equals(this.name,   "Copy Name")
$IF_EVERYTHING64_COPY_FULL_PATH        = process.name=="Everything64" && str.equals(this.name,   "Copy Full Path")
$IF_EVERYTHING64_COPY_DATE_MODIFIED    = process.name=="Everything64" && str.equals(this.name,   "Copy Date Modified")
$IF_EVERYTHING64_COPY_EXTENSION        = process.name=="Everything64" && str.equals(this.name,   "Copy Extension")
$IF_EVERYTHING64_COPY_SIZE             = process.name=="Everything64" && str.equals(this.name,   "Copy Size")
$IF_EVERYTHING64_COPY_TYPE             = process.name=="Everything64" && str.equals(this.name,   "Copy Type")
$IF_EVERYTHING64_SET_RUN_COUNT         = process.name=="Everything64" && str.equals(this.name,   "Set Run Count")
$IF_EVERYTHING64_PIN_TO                = process.name=="Everything64" && str.contains(this.name, "Pin to")
$IF_EVERYTHING64_EXPLORE_IN_EVERYTHING = process.name=="Everything64" && str.equals(this.name,   "Explore in Everything")
$IF_EVERYTHING64_EDIT_IN_NOTEPAD       = process.name=="Everything64" && str.equals(this.name,   "Edit in Notepad")

//
modify(type='*' image=[E122,WHITE]  where='@IF_EVERYTHING64_OPEN'               sep='None')
modify(type='*' image=[E16A,WHITE]  where='@IF_EVERYTHING64_OPEN_PATH'          sep='None')
modify(type='*' image=[E201,WHITE]  where='@IF_EVERYTHING64_OPEN_WITH'          sep='Both'  pos=indexof('Open-Path',1))
//
modify(type='*' image=[E10E,BLUE]   where='@IF_EVERYTHING64_COPY_NAME'          sep='None')
modify(type='*' image=[E114,BLUE]   where='@IF_EVERYTHING64_COPY_FULL_PATH'     sep='None')
//
modify(type='*' image=[E10E,GREEN]  where='@IF_EVERYTHING64_COPY_DATE_MODIFIED' sep='None')
modify(type='*' image=[E10E,GREEN]  where='@IF_EVERYTHING64_COPY_EXTENSION'     sep='None')
modify(type='*' image=[E10E,GREEN]  where='@IF_EVERYTHING64_COPY_EXTENSION'     sep='None')
modify(type='*' image=[E10E,GREEN]  where='@IF_EVERYTHING64_COPY_SIZE'          sep='None')
modify(type='*' image=[E10E,GREEN]  where='@IF_EVERYTHING64_COPY_TYPE'          sep='None')
//
modify(type='*' image=[E167,ORANGE] where='@IF_EVERYTHING64_SET_RUN_COUNT'      sep='Both')
modify(type='*' image=[E0C9,PURPLE] where='@IF_EVERYTHING64_PIN_TO'             sep='None'  pos=indexof('Set-Run-Count',1))

// -> visibility overrides
modify(type='*' image=exe_everything where='@IF_EVERYTHING64_EXPLORE_IN_EVERYTHING' vis='Hidden')
```
### 11. `_c_overrides\mod_icons.nss`

#### `_c_overrides\mod_icons.nss`

```java

// :: update icons


// ---
modify(type='*' image=[E1D2,LOWKEY] image-sel=[E1D2,WHITE] where=this.id==id.new)
modify(type='*' image=[E094,LOWKEY] image-sel=[E094,WHITE] where=this.id==id.refresh)
// ---
modify(type='*' image=[E0A2,LOWKEY] image-sel=[E0A2,WHITE] where=this.id==id.sort_by)
modify(type='*' image=[E09A,LOWKEY] image-sel=[E09A,WHITE] where=this.id==id.group_by)
modify(type='*' image=[E253,LOWKEY] image-sel=[E253,WHITE] where=this.id==id.view)
modify(type='*' image=[E099,LOWKEY] image-sel=[E099,WHITE] where=this.id==id.send_to)
// ---
modify(type='*' image=[E0FE,LOWKEY] image-sel=[E0FE,WHITE] where=this.id==id.run_as_administrator)
modify(type='*' image=[E201,LOWKEY] image-sel=[E201,WHITE] where=this.id==id.open_with)
modify(type='*' image=[E173,LOWKEY] image-sel=[E173,WHITE] where=this.id==id.open)
modify(type='*' image=[E16A,LOWKEY] image-sel=[E16A,WHITE] where=this.id==id.open_in_new_window)
modify(type='*' image=[E160,LOWKEY] image-sel=[E160,WHITE] where=this.id==id.open_in_new_tab)
modify(type='*' image=[E0E8,LOWKEY] image-sel=[E0E8,WHITE] where=this.id==id.open_file_location)
// ---
modify(type='*' image=[E1D2,LOWKEY] image-sel=[E1D2,WHITE] where=this.id==id.create_shortcuts_here)
modify(type='*' image=[E0B2,LOWKEY] image-sel=[E0B2,WHITE] where=this.id==id.copy_here)
modify(type='*' image=[E0CA,LOWKEY] image-sel=[E0CA,WHITE] where=this.id==id.move_here)
// ---
modify(type='*' image=[E0B8,LOWKEY] image-sel=[E0B8,WHITE] where=this.id==id.cut)
modify(type='*' image=[E0B2,LOWKEY] image-sel=[E0B2,WHITE] where=this.id==id.copy)
modify(type='*' image=[E1A6,LOWKEY] image-sel=[E1A6,WHITE] where=this.id==id.create_shortcut)
modify(type='*' image=[E0AF,LOWKEY] image-sel=[E0AF,WHITE] where=this.id==id.paste)
// ---
modify(type='*' image=[E11B,LOWKEY] image-sel=[E11B,WHITE] where=this.id==id.add_to_favorites)
modify(type='*' image=[E17A,LOWKEY] image-sel=[E17A,WHITE] where=this.id==id.edit)
modify(type='*' image=[E0B5,LOWKEY] image-sel=[E0B5,WHITE] where=this.id==id.rename)
modify(type='*' image=[E1D1,LOWKEY] image-sel=[E1D1,WHITE] where=this.id==id.cancel)
// ---
modify(type='*' image=[E0C9,LOWKEY] image-sel=[E0C9,WHITE] where=this.id==id.pin_to_quick_access)
modify(type='*' image=[E0C9,LOWKEY] image-sel=[E0C9,WHITE] where=this.id==id.pin_to_start)
modify(type='*' image=[E0C9,LOWKEY] image-sel=[E0C9,WHITE] where=this.id==id.pin_to_taskbar)
// ---
modify(type='*' image=[E0A0,LOWKEY] image-sel=[E0A0,WHITE] where=this.id==id.customize_this_folder)
modify(type='*' image=[E1DE,LOWKEY] image-sel=[E1DE,WHITE] where=this.id==id.delete)
modify(type='*' image=[E15D,LOWKEY] image-sel=[E15D,WHITE] where=this.id==id.properties)
// ---
modify(type='*' image=[E1D1,RED]    image-sel=[E1D1,WHITE] where=this.id==id.close)
modify(type='*' image=[E1B1,SOFT]   image-sel=[E1B1,WHITE] where=this.id==id.maximize)
modify(type='*' image=[E1D3,SOFT]   image-sel=[E1D3,WHITE] where=this.id==id.minimize)
modify(type='*' image=[E087,SOFT]   image-sel=[E087,WHITE] where=this.id==id.move)
modify(type='*' image=[E1B0,SOFT]   image-sel=[E1B0,WHITE] where=this.id==id.size)

// ---
modify(type='*' image=[E0F8,LOWKEY] image-sel=[E0F8,WHITE] where=regex.match(this.name,".*Toolbars.*"))
modify(type='*' image=[E1B6,LOWKEY] image-sel=[E1B6,WHITE] where=regex.match(this.name,".*Cascade windows.*"))
modify(type='*' image=[E1B6,LOWKEY] image-sel=[E1B6,WHITE] where=regex.match(this.name,".*Show windows stacked.*"))
modify(type='*' image=[E1B6,LOWKEY] image-sel=[E1B6,WHITE] where=regex.match(this.name,".*Show windows side by side.*"))
modify(type='*' image=[E061,LOWKEY] image-sel=[E061,WHITE] where=regex.match(this.name,".*Copy Content to Clipboard.*"))

// ---
modify(type='*' image=exe_everything   where=(str.contains(this.name,"Everything")))
modify(type='*' image=exe_notepad_plus where=(str.contains(this.name,"Notepad\\+\\+")))
modify(type='*' image=exe_sublime      where=(str.contains(this.name,"Sublime")))
modify(type='*' image=exe_winmerge     where=(str.contains(this.name,"WinMerge")))
modify(type='*' image=exe_winmerge     where=(str.equals(this.name,[
                "Compare",
                "Compare As",
                "Select Left",
                "Select Right",
                "Select Middle",
                "Re-select Left"])))
```
### 12. `_c_overrides\mod_positions.nss`

#### `_c_overrides\mod_positions.nss`

```java

// :: update positions (be careful with root menus)



// --- /View
modify(pos=0 type='*' in='/View' where=(
        // reverse order
        this.id(
            id.extra_large_icons,
            id.large_icons,
            id.medium_icons,
            id.small_icons,
            id.list,
            id.details,
            id.tiles,
            id.content)))


// --- /Sort by
modify(pos=0 type='*' in='/Sort by' where=(
        // move to top
        str.contains(this.name, "Date")))


```
### 13. `_c_overrides\mod_visibility.nss`

#### `_c_overrides\mod_visibility.nss`

```java

// :: update visibility


// --- /
modify(vis='Hidden' type='*' in='/' where=(
        this.id(
            id.autoplay,
            id.cast_to_device,
            id.copy_as_path,
            id.copy_path,
            id.cortana,
            // id.disconnect_network_drive,
            id.give_access_to,
            id.include_in_library,
            id.make_available_offline,
            id.make_available_online,
            id.move,
            id.new_item,
            id.news_and_interests,
            id.open_as_portable,
            id.restore,
            id.show_cortana_button,
            id.show_people_on_the_taskbar,
            id.restore_previous_versions,
            id.size)))


// --- /
modify(vis='Hidden' type='*' in='/' where=(
        // ---
        str.contains(this.name, "Clipchamp") ||
        str.contains(this.name, "Dropbox") ||
        str.contains(this.name, "NordVPN") ||
        str.contains(this.name, "Onedrive") ||
        str.contains(this.name, "Send a copy") ||
        str.contains(this.name, "Sync and Backup") ||
        str.contains(this.name, "VLC Media Player") ||
        // ---
        str.equals(this.name, [
                "Add to VLC Media Player",
                "Back up to Dropbox",
                "Dropbox",
                "Enqueue in Winamp",
                "Move to Dropbox",
                "Move to Onedrive",
                "News and interests",
                "Open archive",
                "Open Command Prompt Here",
                "Open in Terminal",
                "Sync or Backup",
                "Windows Ink Workspace"])))


// --- /View
modify(vis='Hidden' type='*' in='/View' where=(
        this.id(
            id.list,
            id.tiles,
            id.content)))


// --- /New
modify(vis='Hidden' type='*' in='/New' where=(
        !str.equals(this.name, [
                "Folder",
                "Shortcut",
                "Text Document",
                "AutoHotkey"])))



// /* OLD */
// /* win10 */
// // -> hide by id
// remove(type='*' in='/View' where=sys.is10() & this.id(id.content, id.list, id.tiles))
// remove(type='*' in='/'
    //     where=sys.is10() & this.id(
        //         id.cortana, id.news_and_interests, id.show_cortana_button,
        //         id.show_people_on_the_taskbar,
        //         id.autoplay, id.cast_to_device,
        //         id.new_item, id.open_as_portable,
        //         id.copy_as_path, id.copy_path, id.give_access_to, id.include_in_library,
        //         id.make_available_offline, id.make_available_online,
        //         id.restore_previous_versions
        //     )
    // )
// // -> hide by name
// remove(type='*' in='/New' where=sys.is10() & (!regex.match(this.name, "^Folder$|^Shortcut$|^Text.*|.*AutoHotkey.*")))
// remove(type='*' in='/' where=sys.is10() find='Add to VLC Media Player|Dropbox|Enqueue in Winamp|News and interests|NordVPN|Onedrive|Open archive|Send a copy|Sync or Backup|Windows Ink Workspa')

// /* OLD */
// /* win11 */
// // EXTREMELY SLOW ON WINDOWS 10
// // -> hide by id
// remove(type='*' in='/View' where=sys.is11() & this.id(id.content))
// remove(type='*' in='/View' where=sys.is11() & this.id(id.list))
// remove(type='*' in='/View' where=sys.is11() & this.id(id.tiles))
// //
// remove(type='*' in='/' where=sys.is11() & this.id(id.cortana))
// remove(type='*' in='/' where=sys.is11() & this.id(id.news_and_interests))
// remove(type='*' in='/' where=sys.is11() & this.id(id.show_cortana_button))
// remove(type='*' in='/' where=sys.is11() & this.id(id.show_people_on_the_taskbar))
// //
// remove(type='*' in='/' where=sys.is11() & this.id(id.autoplay))
// remove(type='*' in='/' where=sys.is11() & this.id(id.cast_to_device))
// remove(type='*' in='/' where=sys.is11() & this.id(id.disconnect_network_drive))
// remove(type='*' in='/' where=sys.is11() & this.id(id.new_item))
// remove(type='*' in='/' where=sys.is11() & this.id(id.open_as_portable))
// //
// remove(type='*' in='/' where=sys.is11() & this.id(id.copy_as_path))
// remove(type='*' in='/' where=sys.is11() & this.id(id.copy_path))
// remove(type='*' in='/' where=sys.is11() & this.id(id.give_access_to))
// remove(type='*' in='/' where=sys.is11() & this.id(id.include_in_library))
// remove(type='*' in='/' where=sys.is11() & this.id(id.make_available_offline))
// remove(type='*' in='/' where=sys.is11() & this.id(id.make_available_online))
// remove(type='*' in='/' where=sys.is11() & this.id(id.restore_previous_versions))
// // -> hide by name
// remove(type='*' in='*' where=sys.is11() find='Add to VLC Media Player')
// remove(type='*' in='*' where=sys.is11() find='Dropbox')
// remove(type='*' in='*' where=sys.is11() find='Enqueue in Winamp')
// remove(type='*' in='*' where=sys.is11() find='News and interests')
// remove(type='*' in='*' where=sys.is11() find='NordVPN')
// remove(type='*' in='*' where=sys.is11() find='Onedrive')
// remove(type='*' in='*' where=sys.is11() find='Open archive')
// remove(type='*' in='*' where=sys.is11() find='Send a copy')
// remove(type='*' in='*' where=sys.is11() find='Sync or Backup')
// remove(type='*' in='*' where=sys.is11() find='Windows Ink Workspa')

```
### 14. `_d_cleanup\cln_desktop.nss`

#### `_d_cleanup\cln_desktop.nss`

```java

// :: cleanup desktop
menu(title="&ORG" pos=0 image=[E1B8,SOFT] image-sel=[E1B8,HOVER] type='Desktop' mode='Multiple' sep='Both') {
    modify(
        type='Desktop'
        where=!this.id(
            id.delete,
            id.copy_here,
            id.move_here,
            id.cut,
            id.copy,
            id.paste,
            //
            id.new,
            //
            id.create_shortcut,
            id.create_shortcuts_here,
            id.empty_recycle_bin,
            id.properties,
            id.refresh,
            //
            id.open,
            id.open_with,
            id.run,
            id.run_as_administrator,
            id.sort_by,
            id.view,
            //
            id.pin_current_folder_to_quick_access,
            id.pin_to_quick_access,
            id.pin_to_start,
            id.pin_to_taskbar
        )
        menu="ORG"
    )
}

```
### 15. `_d_cleanup\cln_explorer.nss`

#### `_d_cleanup\cln_explorer.nss`

```java

// :: cleanup explorer
menu(title="&ORG" pos=0 image=[E1B8,SOFT] image-sel=[E1B8,HOVER] type='~Taskbar|~Desktop' mode='Multiple' sep='Both') {
    modify(
        type='~Taskbar|~Desktop|~Titlebar'
        where=!this.id(
            id.delete,
            id.copy_here,
            id.move_here,
            id.cut,
            id.copy,
            id.paste,
            //
            id.new,
            //
            id.create_shortcut,
            id.create_shortcuts_here,
            id.empty_recycle_bin,
            id.properties,
            id.refresh,
            //
            id.open,
            id.open_with,
            id.run,
            id.run_as_administrator,
            id.sort_by,
            id.view,
            //
            id.pin_current_folder_to_quick_access,
            id.pin_to_quick_access,
            id.pin_to_start,
            id.pin_to_taskbar
        )
        menu="ORG"
    )
}
```
### 16. `_d_cleanup\cln_taskbar.nss`

#### `_d_cleanup\cln_taskbar.nss`

```java

// :: cleanup taskbar
menu(title="&ORG" pos=0 image=[E1B8,SOFT] image-sel=[E1B8,HOVER] type='Taskbar' sep='Both') {
    modify(
        type='Taskbar'
        where=this.id
        tip=this.pos
        menu="ORG"
    )
}
```
