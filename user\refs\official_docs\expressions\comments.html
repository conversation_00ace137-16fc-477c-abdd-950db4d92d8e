﻿<h4>Comments</h4>
<p>Comments can be used to explain <b>Shell</b> code, and to make it more readable. It can also be used to prevent execution <b>Shell</b> code. Comments can be singled-lined or multi-lined.</p>
<div id="single-line">
	<h5>Single-line Comments</h5>
	<p>
		Single-line comments start with two forward slashes (<code>//</code>).<br>
		Any text between <code>//</code> and the end of the line is ignored (will not be executed).
	</p>
	<p>This example uses a single-line comment before a line of code:</p>
	<pre><code class="lang-shell">dynamic 
{
	// This is a comment
	item(title='Hello World!')
	
	//item(title='Hello World!')
}</code></pre>

	<p>This example uses a single-line comment at the end of a line of code:</p>
	<pre><code class="lang-shell">dynamic 
{
    item(title='Hello World!') // This is a comment
}</code></pre>
</div>
<br>
<div id="multi-line">
	<h5>Multi-line Comments</h5>
	<p>
		Multi-line comments start with <code>/*</code> and ends with <code>*/</code>.<br>
		Any text between <code>/*</code> and <code>*/</code> will be ignored.
	</p>
<pre><code class="lang-shell">dynamic
{
	item(title='Hello,/* multiple-lines comment inside */ world')
	
	/*
	item(title='test item 1')
	item(title='test item 2')
	*/
}</code></pre>
	<p>
		Single or multi-line comments?<br>
		It is up to you which you want to use. Normally, we use <code>//</code> for short comments, and <code>/* */</code> for longer.
	</p>
</div>