﻿<h5><PERSON>EY</h5>
<p>Keyboard functions</p>
<section id="keys" class="my-5">
	<h5>keys enumerations</h5>
	<pre><code>key.alt
key.apps
key.back
key.cancel
key.capital
key.capslock
key.control
key.delete
key.down
key.end
key.enter
key.escape
key.execute
key.f1
key.f10
key.f11
key.f12
key.f2
key.f3
key.f4
key.f5
key.f6
key.f7
key.f8
key.f9
key.help
key.home
key.insert
key.lalt
key.lcontrol
key.left
key.lshift
key.lwin
key.next
key.none
key.pagedown
key.pageup
key.pause
key.play
key.print
key.printscreen
key.prior
key.ralt
key.rcontrol
key.return
key.right
key.rshift
key.rwin
key.shift
key.snapshot
key.space
key.tab
key.up
key.win</code></pre>
</section>

<section id="key" class="my-5">
	<h5>key</h5>
	<p>Syntax</p>
	<pre>
<code>// check if SHIFT key is pressed
key(key.shift)

// or
key == key.shift

// or
key.shift()

// check if tow keys (SHIFT+CTRL) is pressed
key(key.shift, key.control)

// check if keys (SHIFT+CTRL+X) is pressed
key(key.shift, key.control, 87)</code>
	</pre>
</section>

<section id="key.send" class="my-5">
	<h5>key.send</h5>
	<p>Send one or more keys to the current window.</p>
	<p>Syntax</p>
	<pre><code>key.send(key.f5)
key.send(key.ctrl,'n')
key.send(key.shift, key.delete)</code></pre>
</section>



