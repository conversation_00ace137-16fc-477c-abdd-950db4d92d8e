//

// menu(title="Goto :  &Common" type='Taskbar|File|Desktop|Dir|Drive|Back.Dir|Back.Drive' image=[E0E8,WHITE1] image-sel=[E0FC,WHITE] sep='None') {
// menu(title='Goto :@"\t"&Common' type='Taskbar|File|Desktop|Dir|Drive|Back.Dir|Back.Drive' image=[E0E8,WHITE1] image-sel=[E0FC,WHITE] sep='None') {
// menu(title='Goto : &System@"\t"Common' type='Taskbar|File|Desktop|Dir|Drive|Back.Dir|Back.Drive' image=[E0E8,WHITE1] image-sel=[E22C,WHITE] sep='None') {
menu(title='Goto : &Common@"\t"System' type='Taskbar|File|Desktop|Dir|Drive|Back.Dir|Back.Drive' image=[E0E8,WHITE1] image-sel=[E0E8,WHITE] sep='None') {

    //
    item(column vis='Static' image=[E00A,DARK])

    //
    item(title="System" image=[E0<PERSON>,GRE<PERSON>] vis='Static' sep='Both')
    import '@app.dir/NSS/_2_setup/_3_items/itm_dir_sys_desktop.nss'
    import '@app.dir/NSS/_2_setup/_3_items/itm_dir_sys_documents.nss'
    import '@app.dir/NSS/_2_setup/_3_items/itm_dir_sys_thispc.nss'
    separator()
    import '@app.dir/NSS/_2_setup/_3_items/itm_dir_sys_downloads.nss'
    import '@app.dir/NSS/_2_setup/_3_items/itm_dir_sys_recent.nss'
    import '@app.dir/NSS/_2_setup/_3_items/itm_dir_sys_recyclebin.nss'
    import '@app.dir/NSS/_2_setup/_3_items/itm_dir_sys_startup.nss'
    separator()
    import '@app.dir/NSS/_2_setup/_3_items/itm_dir_sys_appdata.nss'
    import '@app.dir/NSS/_2_setup/_3_items/itm_dir_sys_programfiles.nss'
    import '@app.dir/NSS/_2_setup/_3_items/itm_dir_sys_userprofile.nss'
    separator()


    //
    item(column vis='Static' image=[E00A,DARK])

    //
    import '@app.dir/NSS/_4_groups/grp_debug_user_shell.nss'
    separator()
    import '@app.dir/NSS/_4_groups/grp_sys_dirs_cloud.nss'
    separator()
}
