﻿<h4>Get Started</h4>
<br>
<p>
	This tutorial will teach you the basics of <b>Shell</b>.<br>
	It is not necessary to have any prior experience.
</p>
<p>
	To start using <b>Shell</b>, you need:<br>
    A text editor, like Notepad, to write <b>Shell</b> code.
</p>
<h5>Quickstart</h5>
<p>Let's create our first menu item.</p>
<p>Open the configuration file "shell.nss" and Write the following code and save.</p>
<div class="notification is-info mt-5"  role="alert">
	<i>Tip:</i> You find the configuration file <code>shell.nss</code> in the <i>Shell</i> program folder. To find the
	<i>Shell</i> program folder, use <kbd>shift</kbd>+<kbd>right-click</kbd> on the Taskbar. The <i>Shell</i> menu
	will appear at the top of the context menu. In its submenu, you can click on <code>directory</code> to open the folder
	where the <i>Shell</i> configuration files are saved.
</div>
<br/>
<pre><code class="lang-shell">item(title='Hello, World!' cmd=msg('Hello @user.name'))
</code></pre>
<p>Don't worry if you don't understand the code above - we will discuss it in detail in later chapters.</p>
<div class="notification is-info mt-5"  role="alert">
	<i>Tip:</i> After editing any nss file, you'll need to update changes: hold <kbd>ctrl</kbd>+<kbd>right-click</kbd>
	on the desktop area or Taskbar to force <i>Shell</i> to reload the nss files. Alternatively, or you can restart
	<i>Windows Explorer</i>.
</div>
<p>The result will look something for this when you press the <kbd>right-click</kbd> in an empty place on the desktop:</p>
<div class="has-text-centered my-5">
	<img class="preview" src="/docs/images/helloworld.png" title="">
</div>
<p><b>Congratulations</b> You have now added the first time a menu item to the context menu</p>
