
//
$APP_SYS_POWERSHELL_DIR = '@sys.dir\System32\WindowsPowerShell\v1.0'
$APP_SYS_POWERSHELL_EXE = '@APP_SYS_POWERSHELL_DIR\powershell.exe'
$APP_SYS_POWERSHELL_TIP = '@APP_SYS_POWERSHELL_EXE'

// Context: Explorer
item(
    title  = ":  &PowerShell"
    keys   = "exe"
    type   = 'File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    args   = '-NoExit -Command (Set-Location -Path "@sel.dir"); & { $Host.UI.RawUI.WindowTitle = \"powershell-prompt:@sys.datetime("H.M")\"}'
    //
    image  = APP_SYS_POWERSHELL_EXE
    tip    = [APP_SYS_POWERSHELL_TIP,TIP3,0.5]
    //
    admin  = keys.rbutton()
    cmd = if(KEYS_EXE_OPEN_EXE,('"@APP_SYS_POWERSHELL_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_SYS_POWERSHELL_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_SYS_POWERSHELL_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_SYS_POWERSHELL_DIR')),
    }
)
// Context: Taskbar
item(
    title  = ":  &PowerShell"
    keys   = "exe"
    type   = 'Taskbar'
    args   = '-NoExit -Command (Set-Location -Path "@user.desktop"); & { $Host.UI.RawUI.WindowTitle = \"powershell-prompt:@sys.datetime("H.M")\"}'
    //
    image  = APP_SYS_POWERSHELL_EXE
    tip    = [APP_SYS_POWERSHELL_TIP,TIP3,0.5]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_SYS_POWERSHELL_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_SYS_POWERSHELL_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_SYS_POWERSHELL_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_SYS_POWERSHELL_DIR')),
    }
)