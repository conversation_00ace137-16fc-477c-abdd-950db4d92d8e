﻿<h5>SYS (SYSTEM)</h5>

<section id="sys.name" class="my-5">
	<h5>sys.name</h5>
	<p>Returns Windows name.</p>
	<p>Syntax</p>
	<code>sys.name</code>
</section>

<section id="sys.type" class="my-5">
	<h5>sys.type</h5>
	<p>Returns system architecture.</p>
	<p>Syntax</p>
	<code>sys.type == 64</code><br>
	<code>sys.type == 32</code>
</section>

<section id="sys.is64" class="my-5">
	<h5>sys.is64</h5>
	<p>Returns if system architecture is x64.</p>
	<p>Syntax</p>
	<code>sys.is64</code>
</section>

<section id="sys.dark" class="my-5">
	<h5>sys.dark</h5>
	<p>Check if dark mode is enabled.</p>
	<p>Syntax</p>
	<code>sys.dark</code>
</section>

<section id="sys.var" class="my-5">
	<h5>sys.var</h5>
	<p>Retrieves the value of an environment variable.</p>
	<p>Syntax</p>
	<code>sys.var('WINDIR')</code>
</section>

<section id="sys.version" class="my-5">
	<h5>sys.version (sys.ver)</h5>
	<p>Windows version.</p>
	<p>Syntax</p>
	<pre><code>sys.version
sys.version.build
sys.version.major
sys.version.minor
sys.version.name
sys.version.type</code></pre>
</section>

<section id="sys.datetime" class="my-5">
	<h5>sys.datetime</h5>
	<p>Date and time format</p>
	<p>Syntax</p>
	<pre><code>sys.datetime
sys.datetime.date
sys.datetime.date_day
sys.datetime.date_dayofweek
sys.datetime.date_month
sys.datetime.date_y
sys.datetime.date_year
sys.datetime.date_yy
sys.datetime.short
sys.datetime.time
sys.datetime.time_hour
sys.datetime.time_milliseconds
sys.datetime.time_min
sys.datetime.time_minute
sys.datetime.time_ms
sys.datetime.time_pm
sys.datetime.time_second</code></pre>
</section>

<section id="sys.paths" class="my-5">
	<h5>Windows paths</h5>
	<pre><code>sys.appdata
sys.bin
sys.bin32
sys.bin64
sys.directory (sys.dir)
sys.path
sys.prog
sys.prog32
sys.programdata
sys.root
sys.temp
sys.templates
sys.users
sys.wow</code></pre>
</section>

<section id="sys.is_primary_monitor" class="my-5">
	<h5>sys.is_primary_monitor</h5>
	<p>Returns true if the current monitor is the primary</p>
	<p>Syntax</p>
	<code>sys.is_primary_monitor</code><br>
</section>
