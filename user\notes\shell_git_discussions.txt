Discussion 1: 
Link: https://github.com/moudey/Shell/discussions/453
Discussion options
stamminator
last weekApr 3, 2024
Sponsor
As a fan of this project who's hoping to make more contributions to it when I can, one thing I think isn't ideal about it is its name. "Shell" is a very commonly used term in computing that already has several different meanings. It's the name of a general computing concept, a language, Linux's command-line interpreter, and the Windows GUI.
In other words, "shell" on its own is overcrowded.
I think a more distinct name that still succeeds in communicating the project's intent would make Nilesoft Shell more discoverable and less confusing in discussions. One alternative I really like is <PERSON>shell.
Is this a discussion you would consider having, @moudey?
1
You must be logged in to vote
--------------------------------------------------------------------------------
Discussion 2: 
Link: https://github.com/moudey/Shell/discussions/449
Discussion options
edited
stamminator
3 weeks agoMar 19, 2024
Sponsor
The only way I've been able make changes to NSS's configuration has been to modify files in the main app directory.
I'm sure there are technical factors I haven't considered, but in my opinion, rather than modifying files in Program Files, it would be much better for settings customizations to live in a user-level folder as overrides to defaults defined in Program Files. This would have a few advantages:
It's clear to the user that their settings won't get lost when updating.
We're free to muck around with settings without fear of screwing up our install or needing to manually manage our own backups.
Users on multi-user machines could each have their own settings (I suppose this is already possible by installing with scoop, albeit with separate installs)
If a GUI editor for the settings ever becomes feasible, it could support showing a diff between the default setting and the user override. This would drastically simplify NSS's setup process and lower the barrier of entry for less technical users.
Do you think this idea is viable? Why or why not? What factors might I be overlooking?
Thank you for your work on this app, @moudey!
1
You must be logged in to vote

---

Comment options
RubicBG
3 weeks agoMar 20, 2024
Collaborator
I will share my opinion and some workarounds to deal with the situation:
Тhe program "must" be accessible to the most uneducated - there are "kids" who know how to install the game and play, there are people who know how to install an archiver and how to use it, ... no matter what example I give, none of them understand coding/programming - they are ordinary users. Ordinary users know a few "basic" things: directories like Windows and Program Files should not be touched, directories like Documents everything is allowed. If the nss files are moved to a more accessible folder, the probability of ruining the work of the program is high. The ordinary user also knows that when reinstalling the program "fixes" itself" - this means that the .nss files must be overwritten
NS works with its own script, which continues to develop. Until version 1.8, a syntax with "static{ }" and "dynamic{ }" was used - this syntax is dropped, dynamic{ item( ) } is converted to modify{ } - here again the .nss files are required to be overwritten. not everyone reads the documentation to be aware.
Тhe easiest way to redirect nss files: Create folder "Nilesoft Shell" in "C:\", copy "shell.nss" and "import" directory to newly created folder "C:\Nilesoft Shell", open "C:\Program Files\Nilesoft Shell\shell.nss", delete everything in it and add only this line:
import 'C:\Nilesoft Shell\shell.nss'
To edit the nss files it is not necessary to go to the installation directory every time. Example with VS Code:
item(title='Visual Studio Code' image=[\uE272, #22A7F2] cmd='code' args='"@app.directory"' window=hidden)
Creating a copy as a backup before installation - this is already taken into account
1
You must be logged in to vote
0 replies
--------------------------------------------------------------------------------
Discussion 3: 
Link: https://github.com/moudey/Shell/discussions/447
Discussion options
CodyOakes
last monthMar 19, 2024
I would like to use PDFtk to merge selected pdfs into a single pdf, this menu item currently works so long as there aren't any white spaces in the file names or file paths of the selected files.
item(type='file' mode='multi_single' find='.pdf' title='Merge PDFs' image=\uE202 cmd='cmd' arg='/k pdftk @sel cat output MERGED_PDF.pdf' wait='true')
(I have added the cmd as cmd and the to the args /k just so the output window would stay open after running the cmd)
Given the following path with the following files:
It assumes additional files for each whitespace:
If I replace @sel with "@sel" then all the selected files are treated as a single file selection by PDFtk:
Is there a way to add "" around each selection in the @sel reference? Is there another work around that I'm missing in the docs?
Any help would be greatly appreciated. Thanks!
1
You must be logged in to vote
Answered by RubicBG
last monthMar 19, 2024
sel(true) - https://nilesoft.org/docs/functions/sel
Add a third parameter to the sel function to change the path separators.
It is possible to assign the quote to the first parameter through these options
0 = Unquote, 1 = Double quote, 2 = Single quote, Or use a string instead.



View full answer

---

Comment options
RubicBG
last monthMar 19, 2024
Collaborator
sel(true) - https://nilesoft.org/docs/functions/sel
Add a third parameter to the sel function to change the path separators.
It is possible to assign the quote to the first parameter through these options
0 = Unquote, 1 = Double quote, 2 = Single quote, Or use a string instead.
sel(0, ",", "/")
sel(1, ",", '\')
sel(2, ",", "/")
sel('"', "\n", ">")
(source: https://discord.com/channels/1106387012707168318/1112126326665650176/1150156808829292615)
Marked as answer
1
You must be logged in to vote
1 reply
Comment options
CodyOakes
last monthMar 19, 2024
Author
Sweet! Worked like a charm, not sure how I missed that in the docs. Thank you for your help @RubicBG
👍
1
Answer selected by CodyOakes

---

Comment options
CodyOakes
last monthMar 19, 2024
Author
Sweet! Worked like a charm, not sure how I missed that in the docs. Thank you for your help @RubicBG
👍
1
--------------------------------------------------------------------------------
Discussion 4: 
Link: https://github.com/moudey/Shell/discussions/305
Discussion options
FroztySeven
on Aug 3, 2023Aug 2, 2023
Hi, I've noticed an empty gap appearing past the "Properties" item while using this program. It only seems to happen with files that show the "Edit with Notepad++" item in the context menu.
Can this be fixed?, it's a little bothering.

1
You must be logged in to vote

---

Comment options
RubicBG
on Aug 9, 2023Aug 9, 2023
Collaborator
I can't give a solution to the problem, but you can solve it in another way:
Remove the "default" Notepad++ command and add a new command via Nilesoft Shell
1
You must be logged in to vote
1 reply
Comment options
edited
FroztySeven
on Aug 9, 2023Aug 9, 2023
Author
Thanks for your reply, yeah actually figured out i could just add:
item(type='file' where=this.disabled find='Edit with Notepad++' vis="remove") to the static section in shell.nss.
That does the trick 👍

---

Comment options
edited
FroztySeven
on Aug 9, 2023Aug 9, 2023
Author
Thanks for your reply, yeah actually figured out i could just add:
item(type='file' where=this.disabled find='Edit with Notepad++' vis="remove") to the static section in shell.nss.
That does the trick 👍

---

Comment options
Alhlhli
on Mar 11Mar 11, 2024
تم حل هذه المشكلة بالاصدار الجديد 1.9.15
This problem has been resolved in the new version 1.9.15
1
You must be logged in to vote
👍
1
0 replies
--------------------------------------------------------------------------------
Discussion 5: 
Link: https://github.com/moudey/Shell/discussions/423
Discussion options
simonmcnair
on Feb 21Feb 21, 2024
I currently add reg entries for powershell scripts I have. It would be useful to be able to add them via this tool. The main thing it would be able to fix with ease that you cannot do via reg keys is the ability to select more than 20-30-50 items and run an action against them.
currently my powershell scripts are:
'move selected to a timestamped folder' which works badly as each runs sequentially and I don't get a timestamp that works for the 20 selected items without durations inbetween.
'move up' which moves the files a folder upward.
'move to photos' moves the highlighted folders/files to a specific folder
'move to documents' moves the highlighted folders/files to a specific folder
'make txt file' makes a text file with the same basename as the highlighted file.
'move to' moves the selected files to a specified folder
It would be cool to have an 'edit config' option that opens up the config file in notepad too.
I am sure some of these are already possible, but the big win for me is selecting more than 15-20 files and running an action on them as I know regedit can't do this, it has to be via a shell extension.
Might be useful to have a 'run via named ps script' which passes all the selected filenames to a powershell script.
I am new to this, so apologies if it already does the above.
Cheers
1
You must be logged in to vote

---

Comment options
RubicBG
on Feb 21Feb 21, 2024
Collaborator
Let's say that everything is achievable, but attention must be paid to one by one, there is no ready-made solution. This is what the Discord channel is for: https://discord.com/channels/1106387012707168318/
1
You must be logged in to vote
1 reply
Comment options
simonmcnair
on Feb 24Feb 24, 2024
Author
I think you need to publish an invite to the channel rather than a link, I don't think the link works unless youre' in the channel, which you can only be with an invite ?

---

Comment options
simonmcnair
on Feb 24Feb 24, 2024
Author
I think you need to publish an invite to the channel rather than a link, I don't think the link works unless youre' in the channel, which you can only be with an invite ?

---

Comment options
RubicBG
on Feb 24Feb 24, 2024
Collaborator
my knowledge of discord is poor, but as far as I know the channel has free access

https://discord.gg/ys7b7pHV
1
You must be logged in to vote
1 reply
Comment options
oscarus
on Mar 3Mar 3, 2024
Invite Invalid 😔

---

Comment options
oscarus
on Mar 3Mar 3, 2024
Invite Invalid 😔

---

Comment options
edited
RubicBG
on Mar 3Mar 3, 2024
Collaborator
https://discord.gg/cH62ySep

only 7 days activ
1
You must be logged in to vote
0 replies

---

Comment options
RubicBG
on Mar 3Mar 3, 2024
Collaborator
https://discord.gg/Mg3tum2sd8
2
You must be logged in to vote
1 reply
Comment options
oscarus
on Mar 3Mar 3, 2024
I'm in! thank you!
👍
1

---

Comment options
oscarus
on Mar 3Mar 3, 2024
I'm in! thank you!
👍
1
--------------------------------------------------------------------------------
Discussion 6: 
Link: https://github.com/moudey/Shell/discussions/427
Discussion options
nosar77
on Feb 25Feb 25, 2024
Has anyone figured out a way to replace copy with just robo copy on shell? This would be an awesome feature to use robo copy as default and not regular copy.
1
You must be logged in to vote

---

Comment options
RubicBG
on Feb 26Feb 26, 2024
Collaborator
if you make sample commands for Robocopy in PowerShell that you find useful, I'll convert them to NS script
https://learn.microsoft.com/en-us/windows-server/administration/windows-commands/robocopy
2
You must be logged in to vote
0 replies
--------------------------------------------------------------------------------
Discussion 7: 
Link: https://github.com/moudey/Shell/discussions/426
Discussion options
luckyizl
on Feb 23Feb 23, 2024
i have a windows 11 23h2 (22631.3155) version.
The right click menu do not work on clicking on the taskbar and clicking on the start menu button
I use start11 third app
1
You must be logged in to vote
--------------------------------------------------------------------------------
Discussion 8: 
Link: https://github.com/moudey/Shell/discussions/424
Discussion options
Eta-Beta
on Feb 21Feb 21, 2024
Good morning, as per the title, today I updated Nilesoft from version 1.8 to the latest via Winget. The problem is that all the changes made and translations from the English language have disappeared, so I proceeded to replace the imports folder and the shell.nss file with the modified ones but the program generates an error. The problem is certainly the new version of Nilesoft which certainly has some differences from the ver. 4,4.. changes that I can't resolve. If anyone could help me I would be grateful. Thanks anyway for a reply.
1
You must be logged in to vote

---

Comment options
RubicBG
on Feb 21Feb 21, 2024
Collaborator
#422 (comment)
look in shell.log to see which line contains the error(s). static{ } and dynamic are no longer used
1
You must be logged in to vote
0 replies
--------------------------------------------------------------------------------
Discussion 9: 
Link: https://github.com/moudey/Shell/discussions/401
Discussion options
Demoe17
on Jan 18Jan 18, 2024
Hello!
I need some help with this software as I am extremely clueless as to how to even go about this and I have been trying and trying but nothing seems to work. Everytime I add someting in the shell.nss file everything breaks and I end up with the classic WIN 10 context menu instead of the one from shell.
I have been looking at the examples and the documentation provided for this software but unfortunately it's not getting me anywhere (I mean literally no where)...
I want to add an option into the context menu so that I can change my windows power plan on the fly via the context menu. Is this even possible and if it is, how would I go about adding that code and where?
1
You must be logged in to vote
Answered by RubicBG
on Jan 19Jan 19, 2024
https://discord.com/channels/1106387012707168318/1106387015425069151/1197887784745177139
https://discord.com/channels/1106387012707168318/1106387015425069151/1197892082820452352
there will be an improved version of these two coming soon
View full answer

---

Comment options
RubicBG
on Jan 19Jan 19, 2024
Collaborator
https://discord.com/channels/1106387012707168318/1106387015425069151/1197887784745177139
https://discord.com/channels/1106387012707168318/1106387015425069151/1197892082820452352
there will be an improved version of these two coming soon
Marked as answer
1
You must be logged in to vote
👍
1
0 replies
Answer selected by Demoe17
--------------------------------------------------------------------------------
Discussion 10: 
Link: https://github.com/moudey/Shell/discussions/84
Discussion options
nicholas-ochoa
on Dec 9, 2022Dec 9, 2022
This is probably a huge ask, but I keep running in to issues with the syntax (and it seems others do as well).
Is it possible to add support for (or switch entirely over to) a different syntax for configuration? Ideally, something that already exists.
JSON5 (or regular JSON), YAML, or TOML for example.
Something like JSON5 or YAML would allow for a number of huge benefits:
Support for a defined schema that defines allowed keys, properties, values, structure
Built in support for most text editors for syntax highlighting. If a schema is created some editors (VS Code, Sublime Text, others I'm sure) will show auto completion suggestions for configuration settings and values.
The current syntax is powerful it seems, but I feel like a lot of that power is locked up behind a large learning curve.
2
You must be logged in to vote

---

Comment options
moudey
on Dec 12, 2022Dec 12, 2022
Maintainer
We'll definitely get to a standard format for the configuration file. In the future, a UI will be created to simplify the work as much as possible.
The current structure is dynamic and the option to import from other files has been added to split the settings file and make it more flexible. Take a look at this example
default
{
    import 'imports/settings.nss'
}

dynamic
{
    //...
    import 'imports/develop.nss'
    item(title='test')
    import if(sys.is11, 'imports/taskbar.nss', null)
    //...
}
2
You must be logged in to vote
2 replies
Comment options
edited
brain246
on Feb 2, 2023Feb 2, 2023
Hi @moudey!
Instead of an own UI i would also prefer a standard file-format combined with a nss-config-schema, as proposed by @nicholas-ochoa. Because in the end, i am already editing all the files in VSCode, so any UI you create will be inferior to VSCode in terms of editing/syntax-highlighting/linting/formatting etc.. If the standard markup/structured formats aren't sufficient for your needs you can also create your own language.
I am really no expert on this topic but i think VSCode/Sublime/Atom etc. can all work with https://github.com/microsoft/TypeScript-TmLanguage.
And if i remember correctly there have been more features regarding languages... just throwing this link in ere as a teaser: https://code.visualstudio.com/api/language-extensions/language-server-extension-guide
I think your valuable dev-time is better spent on other features than implementing a config UI.
Other than that, i wanted to thank you a thousand times for creating this extremely useful tool! Without it Win 11 would be a nightmare to use. Since kind words are nice, but food is nicer, i sent you a little donation@paypal today... should be enough for a lunch at McDonalds 😆 Good appetite, thanks again and please keep working on it!
❤️
1
Comment options
moudey
on Feb 2, 2023Feb 2, 2023
Maintainer
Thank you very much @brain246.
I am really working on making the configuration file context as standard as possible so that it is more stable. I will create a new repository for this feature so that we can provide an extension for most editors.
The UI is required for the normal user, because he has difficulty editing the config file which makes the file explorer unstable, so I am thinking of converting the config file into two layers. The NSS layer is compiled after validation into an NSO binary file to be more stable to use.
I apologize for any unclear points due to the use of google translator.
👍
2

---

Comment options
edited
brain246
on Feb 2, 2023Feb 2, 2023
Hi @moudey!
Instead of an own UI i would also prefer a standard file-format combined with a nss-config-schema, as proposed by @nicholas-ochoa. Because in the end, i am already editing all the files in VSCode, so any UI you create will be inferior to VSCode in terms of editing/syntax-highlighting/linting/formatting etc.. If the standard markup/structured formats aren't sufficient for your needs you can also create your own language.
I am really no expert on this topic but i think VSCode/Sublime/Atom etc. can all work with https://github.com/microsoft/TypeScript-TmLanguage.
And if i remember correctly there have been more features regarding languages... just throwing this link in ere as a teaser: https://code.visualstudio.com/api/language-extensions/language-server-extension-guide
I think your valuable dev-time is better spent on other features than implementing a config UI.
Other than that, i wanted to thank you a thousand times for creating this extremely useful tool! Without it Win 11 would be a nightmare to use. Since kind words are nice, but food is nicer, i sent you a little donation@paypal today... should be enough for a lunch at McDonalds 😆 Good appetite, thanks again and please keep working on it!
❤️
1

---

Comment options
moudey
on Feb 2, 2023Feb 2, 2023
Maintainer
Thank you very much @brain246.
I am really working on making the configuration file context as standard as possible so that it is more stable. I will create a new repository for this feature so that we can provide an extension for most editors.
The UI is required for the normal user, because he has difficulty editing the config file which makes the file explorer unstable, so I am thinking of converting the config file into two layers. The NSS layer is compiled after validation into an NSO binary file to be more stable to use.
I apologize for any unclear points due to the use of google translator.
👍
2

---

Comment options
moudey
on Dec 12, 2022Dec 12, 2022
Maintainer
The import feature can be extended using import from json or xml file.
2
You must be logged in to vote
0 replies

---

Comment options
bugficks
on Jan 2Jan 2, 2024
for the fun of it I did a quick hack "json to nss" python script
1
You must be logged in to vote
0 replies
--------------------------------------------------------------------------------
Discussion 11: 
Link: https://github.com/moudey/Shell/discussions/81
Discussion options
Natejoestev
on Dec 1, 2022Dec 1, 2022
it would be nice if you made a vscode addon for Nilesoft Shell Script (.shl).
it would add simple text highlighting and support for:
collapsing {...} and multi-line items
maybe IntelliSense

(also, it would be nice if you had a discord server👍)
0
You must be logged in to vote

---

Comment options
Natejoestev
on Dec 1, 2022Dec 1, 2022
Author
i'm looking into it and i think i might beable to do it myself.
0
You must be logged in to vote
👍
2
0 replies

---

Comment options
moudey
on Dec 1, 2022Dec 1, 2022
Maintainer
This would be a really great contribution
1
You must be logged in to vote
👍
1
0 replies

---

Comment options
edited
ChristopheL77
on May 25, 2023May 25, 2023
Hi, I've made a vscode extension for that. With a grammar, if you wanna try.
I'm new to this, but it seems to work well :-)
Folding works, comments and hightlighting of some elements is supported.
I can give you the code if you want to use it as a starter.
vsix extension file
1
You must be logged in to vote
❤️
1
3 replies
Comment options
moudey
on May 25, 2023May 25, 2023
Maintainer
This is a really great contribution
Comment options
moudey
on May 25, 2023May 25, 2023
Maintainer
You can add this extension to the vscode folder inside the extensions folder
Comment options
ChristopheL77
on May 26, 2023May 26, 2023
I don't have rights to create a branch.

---

Comment options
moudey
on May 25, 2023May 25, 2023
Maintainer
This is a really great contribution

---

Comment options
moudey
on May 25, 2023May 25, 2023
Maintainer
You can add this extension to the vscode folder inside the extensions folder

---

Comment options
ChristopheL77
on May 26, 2023May 26, 2023
I don't have rights to create a branch.

---

Comment options
Natejoestev
on May 25, 2023May 25, 2023
Author
nice, i will use that.
1
You must be logged in to vote
0 replies

---

Comment options
edited
ChristopheL77
on May 25, 2023May 25, 2023
Fixed variables not colored:
updated vsix extension
Also added snippets (me/it) for menu/item
1
You must be logged in to vote
👍
1
9 replies
Show 4 previous replies
Comment options
Natejoestev
on Jun 2, 2023Jun 2, 2023
Author
i understand what you did.
the file extension is not .nss it's .shl
Comment options
edited
Natejoestev
on Jun 2, 2023Jun 2, 2023
Author
line 22, change to .shl in package.json
Comment options
edited
ChristopheL77
on Jun 2, 2023Jun 2, 2023
You mean the extensions of files for nilesoft shell ?
I think it is .nss because it is what is specified in documentation and also in the example files delivered by the setup : Doc
By the way, you can switch to nilesoft in the vscode state bar even if it is the wrong extension no ?
Comment options
ChristopheL77
on Jun 2, 2023Jun 2, 2023
@ChristopheL77 the newest versions 0.0.5 and 0.1.0 don't do anything. I can't get the old ones, link expired
Sorry for expired links. I thought I could put it in github but I can't create a branch right now.
Comment options
ChristopheL77
on Jun 2, 2023Jun 2, 2023
I published it on the market place, you should be able to install it from vscode now
❤️
1

---

Comment options
Natejoestev
on Jun 2, 2023Jun 2, 2023
Author
i understand what you did.
the file extension is not .nss it's .shl

---

Comment options
edited
Natejoestev
on Jun 2, 2023Jun 2, 2023
Author
line 22, change to .shl in package.json

---

Comment options
edited
ChristopheL77
on Jun 2, 2023Jun 2, 2023
You mean the extensions of files for nilesoft shell ?
I think it is .nss because it is what is specified in documentation and also in the example files delivered by the setup : Doc
By the way, you can switch to nilesoft in the vscode state bar even if it is the wrong extension no ?

---

Comment options
ChristopheL77
on Jun 2, 2023Jun 2, 2023
@ChristopheL77 the newest versions 0.0.5 and 0.1.0 don't do anything. I can't get the old ones, link expired
Sorry for expired links. I thought I could put it in github but I can't create a branch right now.

---

Comment options
ChristopheL77
on Jun 2, 2023Jun 2, 2023
I published it on the market place, you should be able to install it from vscode now
❤️
1

---

Comment options
Natejoestev
on Jun 3, 2023Jun 3, 2023
Author
that is weird, my nilesoft download has shell.shl as the file.
also, i found a bug when you have smth like a='@myFnc("b")' it doesn't like the quotes or smth.
1
You must be logged in to vote
6 replies
Show 1 previous reply
Comment options
edited
ChristopheL77
on Jun 3, 2023Jun 3, 2023
i found a bug when you have smth like a='@myFnc("b")' it doesn't like the quotes or smth.
Yes I didn't find a regular expression to match these expressions because nss language allows to break item/menu properties into multiple lines mixed with properties in same line and there is not an end of statement char as far as I know. I'm still trying though ;)
I mean we can do
menu(prop1=abc())
menu(prop1=abc()
prop2=def
)
menu(prop1=abc(aa()) prop2=def
)
menu(prop1=abc prop2=def()
)
And I have to identity the last parenthesis to know where the menu properties ends.
Here, the right parenthesis may be interpreted as the end of menu or item properties that's why it doesn't highlight well after.
Comment options
Natejoestev
on Jun 3, 2023Jun 3, 2023
Author
i have 1.7 rn. how do i update it?
Comment options
edited
ChristopheL77
on Jun 3, 2023Jun 3, 2023
winget update nilesoft.shell
Or others methods : https://nilesoft.org/download
Backup your config files before if you updated them.
Comment options
Natejoestev
on Jun 3, 2023Jun 3, 2023
Author
could you add the nilesoft icon to the file extension?
Comment options
ChristopheL77
on Jun 5, 2023Jun 4, 2023
i found a bug when you have smth like a='@myFnc("b")' it doesn't like the quotes or smth.
I think it is fixed now in v1.1.0
I learned about regular expression recursion ;-)

---

Comment options
edited
ChristopheL77
on Jun 3, 2023Jun 3, 2023
i found a bug when you have smth like a='@myFnc("b")' it doesn't like the quotes or smth.
Yes I didn't find a regular expression to match these expressions because nss language allows to break item/menu properties into multiple lines mixed with properties in same line and there is not an end of statement char as far as I know. I'm still trying though ;)
I mean we can do
menu(prop1=abc())
menu(prop1=abc()
prop2=def
)
menu(prop1=abc(aa()) prop2=def
)
menu(prop1=abc prop2=def()
)
And I have to identity the last parenthesis to know where the menu properties ends.
Here, the right parenthesis may be interpreted as the end of menu or item properties that's why it doesn't highlight well after.

---

Comment options
Natejoestev
on Jun 3, 2023Jun 3, 2023
Author
i have 1.7 rn. how do i update it?

---

Comment options
edited
ChristopheL77
on Jun 3, 2023Jun 3, 2023
winget update nilesoft.shell
Or others methods : https://nilesoft.org/download
Backup your config files before if you updated them.

---

Comment options
Natejoestev
on Jun 3, 2023Jun 3, 2023
Author
could you add the nilesoft icon to the file extension?

---

Comment options
ChristopheL77
on Jun 5, 2023Jun 4, 2023
i found a bug when you have smth like a='@myFnc("b")' it doesn't like the quotes or smth.
I think it is fixed now in v1.1.0
I learned about regular expression recursion ;-)

---

Comment options
ChristopheL77
on Jun 4, 2023Jun 4, 2023
Yes if @moudey allows me to use his icon.
1
You must be logged in to vote
👍
1
4 replies
Comment options
moudey
on Jun 4, 2023Jun 4, 2023
Maintainer
Yes you can
Comment options
edited
ChristopheL77
on Jun 4, 2023Jun 4, 2023
Cool ;). Updated in v1.0.1
Comment options
edited
Natejoestev
on Jun 5, 2023Jun 4, 2023
Author
that is not what i meant. i meant for the files so you can see that they are nilesoft shell files instead of just this:
Comment options
ChristopheL77
on Jun 5, 2023Jun 5, 2023
Done in 1.2.0 ;)
👍
1

---

Comment options
moudey
on Jun 4, 2023Jun 4, 2023
Maintainer
Yes you can

---

Comment options
edited
ChristopheL77
on Jun 4, 2023Jun 4, 2023
Cool ;). Updated in v1.0.1

---

Comment options
edited
Natejoestev
on Jun 5, 2023Jun 4, 2023
Author
that is not what i meant. i meant for the files so you can see that they are nilesoft shell files instead of just this:

---

Comment options
ChristopheL77
on Jun 5, 2023Jun 5, 2023
Done in 1.2.0 ;)
👍
1

---

Comment options
ChristopheL77
on Jun 6, 2023Jun 6, 2023
Added basic completion in 1.3.0
1
You must be logged in to vote
❤️
1
5 replies
Comment options
ChristopheL77
on Jun 22, 2023Jun 22, 2023
Updated to 1.5.0 with autocompletion with documentation (based on nilesoft doc)
Comment options
edited
Natejoestev
on Dec 10, 2023Dec 10, 2023
Author
@ChristopheL77 i would like to get access to the extension repo so i can fix some things.
Comment options
edited
ChristopheL77
on Dec 10, 2023Dec 10, 2023
What do you want to fix ?
Send me your email address so I can add you to project.
Comment options
Natejoestev
on Dec 11, 2023Dec 11, 2023
Author
are you in the nilesoft discord?
also, i don't think you need an email, you can enter username, full name, or email.
just use my username @Natejoestev
Comment options
Natejoestev
on Dec 11, 2023Dec 11, 2023
Author
unless your planning to add me as a contributor to the marketplace.

---

Comment options
ChristopheL77
on Jun 22, 2023Jun 22, 2023
Updated to 1.5.0 with autocompletion with documentation (based on nilesoft doc)

---

Comment options
edited
Natejoestev
on Dec 10, 2023Dec 10, 2023
Author
@ChristopheL77 i would like to get access to the extension repo so i can fix some things.

---

Comment options
edited
ChristopheL77
on Dec 10, 2023Dec 10, 2023
What do you want to fix ?
Send me your email address so I can add you to project.

---

Comment options
Natejoestev
on Dec 11, 2023Dec 11, 2023
Author
are you in the nilesoft discord?
also, i don't think you need an email, you can enter username, full name, or email.
just use my username @Natejoestev

---

Comment options
Natejoestev
on Dec 11, 2023Dec 11, 2023
Author
unless your planning to add me as a contributor to the marketplace.

---

Comment options
ChristopheL77
on Dec 11, 2023Dec 11, 2023
Repo is not in github

Le lun. 11 déc. 2023, 02:20, Natejoestev ***@***.***> a
écrit :
…
1
You must be logged in to vote
2 replies
Comment options
Natejoestev
on Dec 11, 2023Dec 11, 2023
Author
can we talk on discord?
Comment options
Natejoestev
on Dec 11, 2023Dec 11, 2023
Author
my username is @natejoestev

---

Comment options
Natejoestev
on Dec 11, 2023Dec 11, 2023
Author
can we talk on discord?

---

Comment options
Natejoestev
on Dec 11, 2023Dec 11, 2023
Author
my username is @natejoestev
--------------------------------------------------------------------------------
Discussion 12: 
Link: https://github.com/moudey/Shell/discussions/257
Discussion options
xRorkr
on May 13, 2023May 13, 2023
Hello, I do not know very well how this works, I'm not a programmer, but I like the application and the ease that brings for some actions, my question is
How can I add a new section "APP" and inside it run applications from my pc ? as steam, winscp and other launcher ? not to have them on my desktop if not in the context menu ?
1
You must be logged in to vote

---

Comment options
moudey
on May 13, 2023May 13, 2023
Maintainer
Open shell.nss file and add this code to dynamic section
menu(type="desktop" title="APP")
{
    item(title="app1" cmd='path/to/app1.exe')
    item(title="app2" cmd='path/to/app2.exe')
    item(title="app3" cmd='path/to/app3.exe')
}
Save changes, press CTRL + RIGHT-CLICK to reload settings.
3
You must be logged in to vote
12 replies
Show 7 previous replies
Comment options
Eta-Beta
on May 17, 2023May 17, 2023
Trying and trying again, I always managed to have your scripts that you gave me in other discussions to create the section and transfer 7-Zip........ , but not in the folder menu. Thanks for the umpteenth script (but does "dir" indicate folders?). Another thing to put the icon of the section I have to write "menu(type='file|dir' title="Compressors" image??)" where do I get the icons?
Comment options
moudey
on May 17, 2023May 17, 2023
Maintainer
but does "dir" indicate folders?
dir = directory
where do I get the icons?
https://nilesoft.org/gallery/glyphs
👍
1
Comment options
Eta-Beta
on May 17, 2023May 17, 2023
Ok everything works, thanks as always for the explanations. Thanks for the link to the icons (I could have looked it up, sorry). One last question, can't putting an icon like Winrar for example be done?
Comment options
moudey
on May 17, 2023May 17, 2023
Maintainer
image='path/to/icon'

image='path/to/dll_or_exe,0'
https://nilesoft.org/docs/functions/image
Comment options
Eta-Beta
on May 17, 2023May 17, 2023
Thanks and ..... maybe better read me all the documentation before asking questions. Thank you always for your availability, see you next time.

---

Comment options
Eta-Beta
on May 17, 2023May 17, 2023
Trying and trying again, I always managed to have your scripts that you gave me in other discussions to create the section and transfer 7-Zip........ , but not in the folder menu. Thanks for the umpteenth script (but does "dir" indicate folders?). Another thing to put the icon of the section I have to write "menu(type='file|dir' title="Compressors" image??)" where do I get the icons?

---

Comment options
moudey
on May 17, 2023May 17, 2023
Maintainer
but does "dir" indicate folders?
dir = directory
where do I get the icons?
https://nilesoft.org/gallery/glyphs
👍
1

---

Comment options
Eta-Beta
on May 17, 2023May 17, 2023
Ok everything works, thanks as always for the explanations. Thanks for the link to the icons (I could have looked it up, sorry). One last question, can't putting an icon like Winrar for example be done?

---

Comment options
moudey
on May 17, 2023May 17, 2023
Maintainer
image='path/to/icon'

image='path/to/dll_or_exe,0'
https://nilesoft.org/docs/functions/image

---

Comment options
Eta-Beta
on May 17, 2023May 17, 2023
Thanks and ..... maybe better read me all the documentation before asking questions. Thank you always for your availability, see you next time.

---

Comment options
Eta-Beta
on Nov 16, 2023Nov 16, 2023
Good morning Moudey, I'm re-editing this post due to a problem relating to the changes made thanks to your work. Thanks to you I have my "COMPATTATORI" section where I have moved the software to compress the files (as you can see from the screenshot). The problem arises when I want to select many files to make a single archive, the section does not appear (as can be seen from the second screenshot). Can you kindly tell me what changes I need to make so that the "COMPATTATORI" section appears?. Thanks in advance for your help.
1
You must be logged in to vote
0 replies

---

Comment options
RubicBG
on Dec 3, 2023Dec 3, 2023
Collaborator
menu(mode='multiple' title='COMPATTATORI') {}
2
You must be logged in to vote
👍
1
1 reply
Comment options
Eta-Beta
on Dec 3, 2023Dec 3, 2023
Hello and thank you very much, I didn't think so little would be enough, now it works perfectly.

---

Comment options
Eta-Beta
on Dec 3, 2023Dec 3, 2023
Hello and thank you very much, I didn't think so little would be enough, now it works perfectly.
--------------------------------------------------------------------------------
Discussion 13: 
Link: https://github.com/moudey/Shell/discussions/374
Discussion options
afublog
on Nov 29, 2023Nov 29, 2023
I found that the menu (title=title. go_to) has a default hotkey (G).
So, how to set the hotkeys for a item?
1
You must be logged in to vote
Answered by RubicBG
on Dec 3, 2023Dec 3, 2023
title='&Go To'
View full answer

---

Comment options
RubicBG
on Dec 3, 2023Dec 3, 2023
Collaborator
title='&Go To'
Marked as answer
1
You must be logged in to vote
0 replies
Answer selected by afublog
--------------------------------------------------------------------------------
Discussion 14: 
Link: https://github.com/moudey/Shell/discussions/295
Discussion options
Alhlhli
on Jul 5, 2023Jul 5, 2023
I've been trying to add the Restart Explorer menu to the taskbar.nss list
I tried with cmd
command
I dont sucss
Orders not executed
 item(title='Restart Explorer' image=\uE092 cmd='taskkill /f /im explorer.exe  & start explorer.exe')
 item(title='Restart Explorer' image=\uE092 command.restart_explorer)
1
You must be logged in to vote
Answered by moudey
on Jul 5, 2023Jul 5, 2023
اهلا امير
الامر الثاني ينقصه فقط اضافة خاصية cmd ليصبح بهذا الشكل
item(title='Restart Explorer' image=\uE092 cmd=command.restart_explorer)
View full answer

---

Comment options
moudey
on Jul 5, 2023Jul 5, 2023
Maintainer
اهلا امير
الامر الثاني ينقصه فقط اضافة خاصية cmd ليصبح بهذا الشكل
item(title='Restart Explorer' image=\uE092 cmd=command.restart_explorer)
Marked as answer
1
You must be logged in to vote
👍
2
5 replies
Comment options
Alhlhli
on Jul 5, 2023Jul 5, 2023
Author
Thank you very much
million Thanks
Comment options
kaser2010
on Aug 20, 2023Aug 20, 2023
المشكلة لو عملت شي خطأ بالاكواد وتريد تعمل ريستارت explorer.exe ماتقدر لأن البرنامج shell ماراح يشتغل معاك
لكن عملت طريقة حلوه يكون الامر في This PC يمين بالماوس ويكون فيه Restart Windows Explorer
تحفظ الامر Notepad والامتداد .reg
وبعدين افحه وبس
Windows Registry Editor Version 5.00

[HKEY_CLASSES_ROOT\CLSID\{20D04FE0-3AEA-1069-A2D8-08002B30309D}\shell\Restart Windows Explorer]
"MUIVerb"="Restart Windows Explorer"
"Icon"=hex(2):25,00,50,00,72,00,6f,00,67,00,72,00,61,00,6d,00,46,00,69,00,6c,\
  00,65,00,73,00,25,00,5c,00,45,00,61,00,73,00,79,00,20,00,43,00,6f,00,6e,00,\
  74,00,65,00,78,00,74,00,20,00,4d,00,65,00,6e,00,75,00,5c,00,45,00,63,00,4d,\
  00,65,00,6e,00,75,00,2e,00,65,00,78,00,65,00,2c,00,32,00,33,00,00,00

[HKEY_CLASSES_ROOT\CLSID\{20D04FE0-3AEA-1069-A2D8-08002B30309D}\shell\Restart Windows Explorer\command]
@=hex(2):22,00,25,00,50,00,72,00,6f,00,67,00,72,00,61,00,6d,00,46,00,69,00,6c,\
  00,65,00,73,00,25,00,5c,00,45,00,61,00,73,00,79,00,20,00,43,00,6f,00,6e,00,\
  74,00,65,00,78,00,74,00,20,00,4d,00,65,00,6e,00,75,00,5c,00,45,00,63,00,4d,\
  00,65,00,6e,00,75,00,2e,00,65,00,78,00,65,00,22,00,20,00,2f,00,52,00,65,00,\
  45,00,78,00,70,00,6c,00,6f,00,72,00,65,00,72,00,00,00
👍
1
Comment options
moudey
on Aug 23, 2023Aug 23, 2023
Maintainer
@kaser2010
سوف اقوم بتزويد شيل باختصار يتيح لك إعادة تشغيل المستكشف من خلال لوحة المفاتيح حتى يسهل على المستخدم اعادة تحميل الاعدادات في حالة تحريرها.
👍
1
Comment options
Alhlhli
on Nov 7, 2023Nov 7, 2023
Author
ملف الريجستري لم يتم تشغيله ويعطي رسالة خطأ
لدي ويندوز 11
Comment options
kaser2010
on Nov 7, 2023Nov 7, 2023
استعمل هذا البرنامج وفيه المطلوب
https://www.sordum.org/7615/easy-context-menu-v1-6/
Answer selected by Alhlhli

---

Comment options
Alhlhli
on Jul 5, 2023Jul 5, 2023
Author
Thank you very much
million Thanks

---

Comment options
kaser2010
on Aug 20, 2023Aug 20, 2023
المشكلة لو عملت شي خطأ بالاكواد وتريد تعمل ريستارت explorer.exe ماتقدر لأن البرنامج shell ماراح يشتغل معاك
لكن عملت طريقة حلوه يكون الامر في This PC يمين بالماوس ويكون فيه Restart Windows Explorer
تحفظ الامر Notepad والامتداد .reg
وبعدين افحه وبس
Windows Registry Editor Version 5.00

[HKEY_CLASSES_ROOT\CLSID\{20D04FE0-3AEA-1069-A2D8-08002B30309D}\shell\Restart Windows Explorer]
"MUIVerb"="Restart Windows Explorer"
"Icon"=hex(2):25,00,50,00,72,00,6f,00,67,00,72,00,61,00,6d,00,46,00,69,00,6c,\
  00,65,00,73,00,25,00,5c,00,45,00,61,00,73,00,79,00,20,00,43,00,6f,00,6e,00,\
  74,00,65,00,78,00,74,00,20,00,4d,00,65,00,6e,00,75,00,5c,00,45,00,63,00,4d,\
  00,65,00,6e,00,75,00,2e,00,65,00,78,00,65,00,2c,00,32,00,33,00,00,00

[HKEY_CLASSES_ROOT\CLSID\{20D04FE0-3AEA-1069-A2D8-08002B30309D}\shell\Restart Windows Explorer\command]
@=hex(2):22,00,25,00,50,00,72,00,6f,00,67,00,72,00,61,00,6d,00,46,00,69,00,6c,\
  00,65,00,73,00,25,00,5c,00,45,00,61,00,73,00,79,00,20,00,43,00,6f,00,6e,00,\
  74,00,65,00,78,00,74,00,20,00,4d,00,65,00,6e,00,75,00,5c,00,45,00,63,00,4d,\
  00,65,00,6e,00,75,00,2e,00,65,00,78,00,65,00,22,00,20,00,2f,00,52,00,65,00,\
  45,00,78,00,70,00,6c,00,6f,00,72,00,65,00,72,00,00,00
👍
1

---

Comment options
moudey
on Aug 23, 2023Aug 23, 2023
Maintainer
@kaser2010
سوف اقوم بتزويد شيل باختصار يتيح لك إعادة تشغيل المستكشف من خلال لوحة المفاتيح حتى يسهل على المستخدم اعادة تحميل الاعدادات في حالة تحريرها.
👍
1

---

Comment options
Alhlhli
on Nov 7, 2023Nov 7, 2023
Author
ملف الريجستري لم يتم تشغيله ويعطي رسالة خطأ
لدي ويندوز 11

---

Comment options
kaser2010
on Nov 7, 2023Nov 7, 2023
استعمل هذا البرنامج وفيه المطلوب
https://www.sordum.org/7615/easy-context-menu-v1-6/

---

Comment options
Alhlhli
on Jul 8, 2023Jul 8, 2023
Author
I found the following command in taskbar.nss
item(vis=@key.shift() title=title.exit_explorer cmd=command.restart_explorer)
1
You must be logged in to vote
👍
1
0 replies
--------------------------------------------------------------------------------
Discussion 15: 
Link: https://github.com/moudey/Shell/discussions/367
Discussion options
rzh0504
on Nov 5, 2023Nov 5, 2023
Chinese i18n seems not really complete
1
You must be logged in to vote
--------------------------------------------------------------------------------
Discussion 16: 
Link: https://github.com/moudey/Shell/discussions/65
Discussion options
boromyr
on Nov 28, 2022Nov 28, 2022
Would it be possible to use shell as context menu even in processes other than explorer.exe? For example in XYplorer or in compression programs?
1
You must be logged in to vote

---

Comment options
moudey
on Nov 28, 2022Nov 28, 2022
Maintainer
Shell in the following update supports any program that load system context menu such as Explorer++, Xyplorer, 7zip, etc.
With an exclude option to exclude any process or window
1
You must be logged in to vote
0 replies

---

Comment options
boromyr
on Nov 29, 2022Nov 29, 2022
Author
Are you referring to the 1.8 beta version? How can I enable this feature?
1
You must be logged in to vote
0 replies

---

Comment options
moudey
on Nov 30, 2022Nov 30, 2022
Maintainer
@boromyr Please test this build
Comment this line to enable shell with other programs
exclude.where = !process.is_explorer
2
You must be logged in to vote
0 replies

---

Comment options
boromyr
on Dec 12, 2022Dec 12, 2022
Author
i tried in a virtual machine with windows 11 22H2 but it didn't work in 7zip, but in bandizip it does
4
You must be logged in to vote
0 replies

---

Comment options
boromyr
on Mar 9, 2023Mar 9, 2023
Author
With shell 1.8.1 commenting exclude.where = !process.is_explorer works with 7zip and other apps that don't use custom context menus, but unfortunately it doesn't work with XYplorer, I don't know if it is compatible with other file managers out there, or if is an exclusive problem of XY
1
You must be logged in to vote
0 replies

---

Comment options
adambisho
on Oct 24, 2023Oct 24, 2023
Shell looks like an amazing program and I really want to use it but I'm having issues. I am using the app Q-dir from softwareok.com and can't get the shell context menu to work with it. I commented out the "exclude.where" line and restarted explorer, but that didn't seem to work. The shell menu works fine with windows explorer. Any help with this issue?
1
You must be logged in to vote
1 reply
Comment options
adambisho
on Oct 24, 2023Oct 24, 2023
I think I figured it out. With Q-dir you must use SHIFT, RIGHT-CLICK to get to the MS context menu. It looks like the SHELL menu works when doing this. I'm not a programmer and just getting started with SHELL, but I'm very excited about customizing it. Thanks for this great app.

---

Comment options
adambisho
on Oct 24, 2023Oct 24, 2023
I think I figured it out. With Q-dir you must use SHIFT, RIGHT-CLICK to get to the MS context menu. It looks like the SHELL menu works when doing this. I'm not a programmer and just getting started with SHELL, but I'm very excited about customizing it. Thanks for this great app.
--------------------------------------------------------------------------------
Discussion 17: 
Link: https://github.com/moudey/Shell/discussions/345
Discussion options
ElTagoury
on Oct 8, 2023Oct 8, 2023
I hoped for Microsoft to do it in Windows 11's new Context menu but they didn't, i wish if there is any tool that gives the ability to scroll long context menu's but unfortunately there isn't, I'm very happy of knowing and using Nilesoft it's a great tool & i hope if they can add this feature and be able to scroll through Windows Explorer's Context Menu.
Good Effort & Much Appreciated.
1
You must be logged in to vote

---

Comment options
moudey
on Oct 8, 2023Oct 8, 2023
Maintainer
Thank you for your interest.
The scrolling issue is being developed. You can use the up and down keys to avoid this issue.
1
You must be logged in to vote
0 replies
--------------------------------------------------------------------------------
Discussion 18: 
Link: https://github.com/moudey/Shell/discussions/332
Discussion options
edited
PitchAbyss
on Sep 18, 2023Sep 18, 2023
hi @moudey I have a script that copies font file from one place to another when I run the script alone it works but if I add it to the shell contect menu and run the script the font doesn't get copied...this is the command I used
item(title='Conversion' image=\uE1ED pos=1 cmd args='/k "C:\Users\<USER>\OneDrive - MSFT\My-All-Setup-Guides\01 Random Guides\02 My Scripts\Anime Scripts\My Final Scripts\Final_Script_For Media_Conversion.bat"')
also is it possible to replace cmd with latest PowerShell core, please reply
1
You must be logged in to vote

---

Comment options
PitchAbyss
on Sep 21, 2023Sep 21, 2023
Author
@moudey sir please reply
1
You must be logged in to vote
0 replies

---

Comment options
moudey
on Sep 22, 2023Sep 21, 2023
Maintainer
@moudey sir please reply
My children and I have been detained in the hospital for 4 days. When I get out, I will check your issue.
1
You must be logged in to vote
1 reply
Comment options
PitchAbyss
on Sep 22, 2023Sep 22, 2023
Author
I am extremely sorry sir @moudey dint knew please tc of everyone and check once time allows

---

Comment options
PitchAbyss
on Sep 22, 2023Sep 22, 2023
Author
I am extremely sorry sir @moudey dint knew please tc of everyone and check once time allows

---

Comment options
moudey
on Sep 25, 2023Sep 25, 2023
Maintainer
The script may need to change the current folder to the path of the script folder
item(title='Conversion' image=\uE1ED pos=1 
    cmd args='/k "C:\Users\<USER>\OneDrive - MSFT\My-All-Setup-Guides\01 Random Guides\02 My Scripts\Anime Scripts\My Final Scripts\Final_Script_For Media_Conversion.bat"'

    dir="C:\Users\<USER>\OneDrive - MSFT\My-All-Setup-Guides\01 Random Guides\02 My Scripts\Anime Scripts\My Final Scripts")
1
You must be logged in to vote
0 replies

---

Comment options
PitchAbyss
on Sep 26, 2023Sep 26, 2023
Author
@moudey
thankyou for reply sir and i hope everything is fine now
sir this command simply stops the shell and takes context menu to stock
1
You must be logged in to vote
0 replies

---

Comment options
moudey
on Sep 26, 2023Sep 26, 2023
Maintainer
Sorry. Replace the double quotation mark with a single quotation mark with the path in the dir property
item(title='Conversion' image=\uE1ED pos=1 
    cmd args='/k "C:\Users\<USER>\OneDrive - MSFT\My-All-Setup-Guides\01 Random Guides\02 My Scripts\Anime Scripts\My Final Scripts\Final_Script_For Media_Conversion.bat"'

    dir='C:\Users\<USER>\OneDrive - MSFT\My-All-Setup-Guides\01 Random Guides\02 My Scripts\Anime Scripts\My Final Scripts')
1
You must be logged in to vote
0 replies

---

Comment options
PitchAbyss
on Sep 26, 2023Sep 26, 2023
Author
Sorry. Replace the double quotation mark with a single quotation mark with the path in the dir property
item(title='Conversion' image=\uE1ED pos=1 
    cmd args='/k "C:\Users\<USER>\OneDrive - MSFT\My-All-Setup-Guides\01 Random Guides\02 My Scripts\Anime Scripts\My Final Scripts\Final_Script_For Media_Conversion.bat"'

    dir='C:\Users\<USER>\OneDrive - MSFT\My-All-Setup-Guides\01 Random Guides\02 My Scripts\Anime Scripts\My Final Scripts')
thankyou so much sir it did sort the earlier issue but still doest copies the font file to the directory
if u want i can make a screen capture
1
You must be logged in to vote
1 reply
Comment options
moudey
on Sep 26, 2023Sep 26, 2023
Maintainer
Please attach "Final_Script_For Media_Conversion.bat"

---

Comment options
moudey
on Sep 26, 2023Sep 26, 2023
Maintainer
Please attach "Final_Script_For Media_Conversion.bat"

---

Comment options
PitchAbyss
on Sep 26, 2023Sep 26, 2023
Author
Please attach "Final_Script_For Media_Conversion.bat"
please check mail sir , also enjoy a little coffe :)
1
You must be logged in to vote
0 replies
--------------------------------------------------------------------------------
Discussion 19: 
Link: https://github.com/moudey/Shell/discussions/329
Discussion options
Neobond
on Sep 12, 2023Sep 12, 2023
I found how to disable the More options menu for rotating images (very helpful since I do this often) but that has increased the size of the menu somewhat, now I want to remove some entries.
I want to remove the following:
WinRAR (top one)
Share with Skype
Add to Favorites (what even is this?)
Snagit
Is it possible to move some items like the bottom WinRAR and PowerRename into the same place as 7-Zip?
Thanks in advance
1
You must be logged in to vote

---

Comment options
edited
RubicBG
on Sep 12, 2023Sep 12, 2023
Collaborator
Which version of NS are you using?
To remove duplicate items you need debug build 43 (v1.8.43) (https://discord.com/channels/1106387012707168318/1112126326665650176/1146552576381304882)
unwanted items/menus can be hidden, can be removed (not loaded at all), can be moved to a sub-menu, or can be moved below - whichever way you want it?
1
You must be logged in to vote
1 reply
Comment options
Neobond
on Sep 12, 2023Sep 12, 2023
Author
I am using 1.8.1 which is the latest official build I think.

---

Comment options
Neobond
on Sep 12, 2023Sep 12, 2023
Author
I am using 1.8.1 which is the latest official build I think.
--------------------------------------------------------------------------------
Discussion 20: 
Link: https://github.com/moudey/Shell/discussions/174
Discussion options
0-BlackSpectrum-0
on Feb 27, 2023Feb 27, 2023
As shown in the images:
Gradient:
https://github.com/moudey/Shell/blob/main/screenshots/gradient.png
Acrylic:
https://github.com/moudey/Shell/blob/main/screenshots/acrylic.png
how do I apply these themes?
1
You must be logged in to vote
Answered by moudey
on Feb 27, 2023Feb 27, 2023
Add this codes to theme section in shell.nss file
Gradient
background
{
    opacity=0
    gradient
    {
        enabled = 1
        linear = [0, 100, 0, 0]
        stop = [
            [0, color.accent_dark3, 90],
            [.5, color.accent, 90],
            [1, color.accent_dark3, 90]
        ]
    }
}
View full answer

---

Comment options
moudey
on Feb 27, 2023Feb 27, 2023
Maintainer
Add this codes to theme section in shell.nss file
Gradient
background
{
    opacity=0
    gradient
    {
        enabled = 1
        linear = [0, 100, 0, 0]
        stop = [
            [0, color.accent_dark3, 90],
            [.5, color.accent, 90],
            [1, color.accent_dark3, 90]
        ]
    }
}
Acrylic
background
{
    opacity=0
    effect=[3, color.accent, 50]
}
Save changes, press CTRL + RIGHT-CLICK or restart Explorer to reload settings.
https://nilesoft.org/docs/syntax/set
Marked as answer
2
You must be logged in to vote
5 replies
Comment options
bachig26
on Feb 27, 2023Feb 27, 2023
I tried these same for acrylic, but I can't able to reproduce the theme in win10. do I need to include the gradient too for acrylic theme?
Comment options
moudey
on Feb 27, 2023Feb 27, 2023
Maintainer
I tried these same for acrylic, but I can't able to reproduce the theme in win10. do I need to include the gradient too for acrylic theme?
Use the blur effect in Windows 10 if the acrylic effect is not available.
You can composite blur and gradient effect with background color too
background
{
    color=#fff
    opacity=20
    effect=2 // blur effect 
    gradient
    {
        enabled = 1
        linear = [0, 100, 0, 0]
        stop = [
            [0, #00f, 10],
            [.5, #0f0, 40],
            [1, #f00, 50]
        ]
    }
}
❤️
1
Comment options
0-BlackSpectrum-0
on Feb 28, 2023Feb 28, 2023
Author
Acrylic
background
{
    opacity=0
    effect=[3, color.accent, 50]
}
Save changes, press CTRL + RIGHT-CLICK or restart Explorer to reload settings.
https://nilesoft.org/docs/syntax/set
Can you tell me how do I change the color as well? color.accent is used to get the color so how do I change it?
Comment options
moudey
on Feb 28, 2023Feb 28, 2023
Maintainer
Can you tell me how do I change the color as well? color.accent is used to get the color so how do I change it?
background
{
    opacity=0
    effect=[3, #00ff00, 50]  // [3, tint color, opacity value from 0 to 100]
}
👍
1
Comment options
0-BlackSpectrum-0
on Feb 28, 2023Feb 28, 2023
Author
Thanks
Answer selected by 0-BlackSpectrum-0

---

Comment options
bachig26
on Feb 27, 2023Feb 27, 2023
I tried these same for acrylic, but I can't able to reproduce the theme in win10. do I need to include the gradient too for acrylic theme?

---

Comment options
moudey
on Feb 27, 2023Feb 27, 2023
Maintainer
I tried these same for acrylic, but I can't able to reproduce the theme in win10. do I need to include the gradient too for acrylic theme?
Use the blur effect in Windows 10 if the acrylic effect is not available.
You can composite blur and gradient effect with background color too
background
{
    color=#fff
    opacity=20
    effect=2 // blur effect 
    gradient
    {
        enabled = 1
        linear = [0, 100, 0, 0]
        stop = [
            [0, #00f, 10],
            [.5, #0f0, 40],
            [1, #f00, 50]
        ]
    }
}
❤️
1

---

Comment options
0-BlackSpectrum-0
on Feb 28, 2023Feb 28, 2023
Author
Acrylic
background
{
    opacity=0
    effect=[3, color.accent, 50]
}
Save changes, press CTRL + RIGHT-CLICK or restart Explorer to reload settings.
https://nilesoft.org/docs/syntax/set
Can you tell me how do I change the color as well? color.accent is used to get the color so how do I change it?

---

Comment options
moudey
on Feb 28, 2023Feb 28, 2023
Maintainer
Can you tell me how do I change the color as well? color.accent is used to get the color so how do I change it?
background
{
    opacity=0
    effect=[3, #00ff00, 50]  // [3, tint color, opacity value from 0 to 100]
}
👍
1

---

Comment options
0-BlackSpectrum-0
on Feb 28, 2023Feb 28, 2023
Author
Thanks

---

Comment options
edited
JZersche
on Sep 8, 2023Sep 8, 2023
Possible to use a background image with a path for the context menu background, or only gradient/colors are supported currently?
1
You must be logged in to vote
1 reply
Comment options
RubicBG
on Sep 9, 2023Sep 9, 2023
Collaborator
only gradient/colors

---

Comment options
RubicBG
on Sep 9, 2023Sep 9, 2023
Collaborator
only gradient/colors
--------------------------------------------------------------------------------
Discussion 21: 
Link: https://github.com/moudey/Shell/discussions/307
Discussion options
Shanti099
on Aug 4, 2023Aug 4, 2023
Hello,
Thank you for this great application,
Would you please help,
How to run commands like this?
cmd='control.exe /name Microsoft.PowerOptions /page pagePlanSettings')
or
cmd='mmc.exe /s devmgmt.msc')
1
You must be logged in to vote

---

Comment options
Shanti099
on Aug 5, 2023Aug 5, 2023
Author
The answer is:
cmd='control.exe' args='/name Microsoft.PowerOptions /page pagePlanSettings'
cmd='mmc.exe' args='/s devmgmt.msc'
1
You must be logged in to vote
👍
1
0 replies
--------------------------------------------------------------------------------
Discussion 22: 
Link: https://github.com/moudey/Shell/discussions/304
Discussion options
luckyizl
on Jul 25, 2023Jul 25, 2023
Hello,
It is possible to add glyphs (Cut, Copy, Paste, Rename, etc.) at the top or at the end in the menu windows.
Thank you.
1
You must be logged in to vote
--------------------------------------------------------------------------------
Discussion 23: 
Link: https://github.com/moudey/Shell/discussions/212
Discussion options
edited
PitchAbyss
on Feb 21, 2023Feb 21, 2023
hi @moudey i have made atweak to restart explorer and placed under context menu
when its not placed under any category it works fine like this
but when i place it under a category with
item(find='restart exlorer ' pos=0 sep='bottom' menu="Misc Tools")
the further menu doesnt appear
Also I am trying to remove vlc
commands i used
item(find='add to vlc media player's playlist' find='add to vlc media player's playlist' vis=vis.remove)
item(find='play with vlc media player' find='play with vlc media player' vis=vis.remove)
but still they are intact...thankyou very much
1
You must be logged in to vote

---

Comment options
moudey
on Feb 21, 2023Feb 21, 2023
Maintainer
Is this "restart explorer" item static or dynamic?
Also I am trying to remove vlc
Use double quotes if the text contains single quotes or vice versa
item(find="add to vlc media player's playlist" vis=vis.remove)
item(find="play with vlc media player" vis=vis.remove)
Or use | sign. To search for more than one phrase
item(find="add to vlc media player's playlist | play with vlc media player" vis=vis.remove)
Or through this command it removes all items that contain "vlc media player".
item(find='vlc media player' vis=vis.remove)
1
You must be logged in to vote
❤️
1
0 replies

---

Comment options
PitchAbyss
on Feb 21, 2023Feb 21, 2023
Author
Is this "restart explorer" item static or dynamic?
Also I am trying to remove vlc
Use double quotes if the text contains single quotes or vice versa
item(find="add to vlc media player's playlist" vis=vis.remove)
item(find="play with vlc media player" vis=vis.remove)
Or use | sign. To search for more than one phrase
item(find="add to vlc media player's playlist | play with vlc media player" vis=vis.remove)
Or through this command it removes all items that contain "vlc media player".
item(find='vlc media player' vis=vis.remove)
i am not sure if its dynamic or static ...how can i check this sir ?
also when i right click on a folder there are shortcuts for even apps that i uninstalled...i think thats creating issue with removinf the vlc
anyhow i can reset the context menu sir ?
1
You must be logged in to vote
0 replies

---

Comment options
moudey
on Feb 21, 2023Feb 21, 2023
Maintainer
This item appears on the desktop only, so we define the type and search by matching the name of the item with double quotes inside find property
item(type='desktop' find='"restart explorer"' pos=0 sep='bottom' menu="Misc Tools")
1
You must be logged in to vote
❤️
1
0 replies

---

Comment options
edited
PitchAbyss
on Feb 21, 2023Feb 21, 2023
Author
you are simply the best sir , amazingggg :)
aah same another one is
item(type='desktop' find='"system file checker"' pos=0 sep='bottom' menu="Misc Tools")
the further menu doesnt opens sir
1
You must be logged in to vote
0 replies

---

Comment options
moudey
on Feb 21, 2023Feb 21, 2023
Maintainer
This command is correct and should be work. Restart the explorer to load the settings changes
1
You must be logged in to vote
❤️
1
0 replies

---

Comment options
edited
PitchAbyss
on Feb 21, 2023Feb 21, 2023
Author
This command is correct and should be work. Restart the explorer to load the settings changes
it surely worked for restart explorer but not working for the system file checker
edit :
i guess its the issue with my windows...il reinstall and update u sir
1
You must be logged in to vote
0 replies

---

Comment options
moudey
on Feb 21, 2023Feb 21, 2023
Maintainer
You can add these commands directly to Shell instead of adding them to the registry
menu(title='Restart Explorer' image='explorer.exe')
{
    item(title='Restart Explorer Now' cmd args='/c taskkill /f /im explorer.exe & start explorer.exe')
    item(title='Restart Explorer with Pause' cmd args='/c @@echo off & echo. & echo Stopping explorer.exe process . . . & echo. & taskkill /f /im explorer.exe & echo. & echo. & echo Waiting to start explorer.exe process when you are ready . . . & pause && start explorer.exe && exit')
}

menu(title='System File Checker' image='WmiPrvSE.exe')
{
    tem(title='Run System File Checker' admin=true cmd='PowerShell' args="-windowstyle hidden -command \"Start-Process cmd -ArgumentList '/s,/k, sfc /scannow' -Verb runAs\"")
    item(title='System File Checker log' cmd='PowerShell' args='(sls [SR] $env:windir\Logs\CBS\CBS.log -s).Line >"$env:userprofile\Desktop\sfcdetails.txt\"')
}
1
You must be logged in to vote
❤️
1
0 replies

---

Comment options
PitchAbyss
on Feb 21, 2023Feb 21, 2023
Author
You can add these commands directly to Shell instead of adding them to the registry
menu(title='Restart Explorer' image='explorer.exe')
{
    item(title='Restart Explorer Now' cmd args='/c taskkill /f /im explorer.exe & start explorer.exe')
    item(title='Restart Explorer with Pause' cmd args='/c @@echo off & echo. & echo Stopping explorer.exe process . . . & echo. & taskkill /f /im explorer.exe & echo. & echo. & echo Waiting to start explorer.exe process when you are ready . . . & pause && start explorer.exe && exit')
}

menu(title='System File Checker' image='WmiPrvSE.exe')
{
    tem(title='Run System File Checker' admin=true cmd='PowerShell' args="-windowstyle hidden -command \"Start-Process cmd -ArgumentList '/s,/k, sfc /scannow' -Verb runAs\"")
    item(title='System File Checker log' cmd='PowerShell' args='(sls [SR] $env:windir\Logs\CBS\CBS.log -s).Line >"$env:userprofile\Desktop\sfcdetails.txt\"')
}
sir restart explorer works great but there is something wrong in system file checker it sets the conext menu back to normal
1
You must be logged in to vote
0 replies

---

Comment options
moudey
on Feb 21, 2023Feb 21, 2023
Maintainer
sir restart explorer works great but there is something wrong in system file checker it sets the conext menu back to normal
Sorry
menu(title='System File Checker' image='WmiPrvSE.exe')
{
    item(title='Run System File Checker' admin=true cmd='PowerShell' args="-windowstyle hidden -command \"Start-Process cmd -ArgumentList '/s,/k, sfc /scannow' -Verb runAs\"")
    item(title='System File Checker log' cmd='PowerShell' args='(sls [SR] $env:windir\Logs\CBS\CBS.log -s).Line >"$env:userprofile\Desktop\sfcdetails.txt\"')
}
1
You must be logged in to vote
❤️
1
0 replies

---

Comment options
PitchAbyss
on Feb 21, 2023Feb 21, 2023
Author
sir restart explorer works great but there is something wrong in system file checker it sets the conext menu back to normal
Sorry
menu(title='System File Checker' image='WmiPrvSE.exe')
{
    item(title='Run System File Checker' admin=true cmd='PowerShell' args="-windowstyle hidden -command \"Start-Process cmd -ArgumentList '/s,/k, sfc /scannow' -Verb runAs\"")
    item(title='System File Checker log' cmd='PowerShell' args='(sls [SR] $env:windir\Logs\CBS\CBS.log -s).Line >"$env:userprofile\Desktop\sfcdetails.txt\"')
}
ur the best sir works flawlessly thankyou so much sir ... my pay day is near for some coffees <3
1
You must be logged in to vote
👍
1
0 replies

---

Comment options
PitchAbyss
on Feb 22, 2023Feb 21, 2023
Author
sir restart explorer works great but there is something wrong in system file checker it sets the conext menu back to normal
Sorry
menu(title='System File Checker' image='WmiPrvSE.exe')
{
    item(title='Run System File Checker' admin=true cmd='PowerShell' args="-windowstyle hidden -command \"Start-Process cmd -ArgumentList '/s,/k, sfc /scannow' -Verb runAs\"")
    item(title='System File Checker log' cmd='PowerShell' args='(sls [SR] $env:windir\Logs\CBS\CBS.log -s).Line >"$env:userprofile\Desktop\sfcdetails.txt\"')
}
one last thing sir
is it possible to move these under like this
1
You must be logged in to vote
0 replies

---

Comment options
moudey
on Feb 22, 2023Feb 21, 2023
Maintainer
Paste these items inside the "Misc Tools" items as in the following example:
dynamic
{
    ...
    menu(title="Misc Tools")
    {
        menu(title='Restart Explorer' image='explorer.exe')
        {
            item(title='Restart Explorer Now' cmd args='/c taskkill /f /im explorer.exe & start explorer.exe')
            item(title='Restart Explorer with Pause' cmd args='/c @@echo off & echo. & echo Stopping explorer.exe process . . . & echo. & taskkill /f /im explorer.exe & echo. & echo. & echo Waiting to start explorer.exe process when you are ready . . . & pause && start explorer.exe && exit')
        }

        menu(title='System File Checker' image='WmiPrvSE.exe')
        {
            item(title='Run System File Checker' admin=true cmd='PowerShell' args="-windowstyle hidden -command \"Start-Process cmd -ArgumentList '/s,/k, sfc /scannow' -Verb runAs\"")
            item(title='System File Checker log' cmd='PowerShell' args='(sls [SR] $env:windir\Logs\CBS\CBS.log -s).Line >"$env:userprofile\Desktop\sfcdetails.txt\"')
        }
    }
}
Don't forget to delete these items from the registry to avoid duplicating them
reg delete "HKEY_CLASSES_ROOT\DesktopBackground\Shell\Restart Explorer" /f
reg delete "HKEY_CLASSES_ROOT\DesktopBackground\Shell\SFC" /f
2
You must be logged in to vote
❤️
1
0 replies

---

Comment options
PitchAbyss
on Feb 22, 2023Feb 21, 2023
Author
Paste these items inside the "Misc Tools" items as in the following example:
dynamic
{
    ...
    menu(title="Misc Tools")
    {
        menu(title='Restart Explorer' image='explorer.exe')
        {
            item(title='Restart Explorer Now' cmd args='/c taskkill /f /im explorer.exe & start explorer.exe')
            item(title='Restart Explorer with Pause' cmd args='/c @@echo off & echo. & echo Stopping explorer.exe process . . . & echo. & taskkill /f /im explorer.exe & echo. & echo. & echo Waiting to start explorer.exe process when you are ready . . . & pause && start explorer.exe && exit')
        }

        menu(title='System File Checker' image='WmiPrvSE.exe')
        {
            item(title='Run System File Checker' admin=true cmd='PowerShell' args="-windowstyle hidden -command \"Start-Process cmd -ArgumentList '/s,/k, sfc /scannow' -Verb runAs\"")
            item(title='System File Checker log' cmd='PowerShell' args='(sls [SR] $env:windir\Logs\CBS\CBS.log -s).Line >"$env:userprofile\Desktop\sfcdetails.txt\"')
        }
    }
}
Don't forget to delete these items from the registry to avoid duplicating them
reg delete "HKEY_CLASSES_ROOT\DesktopBackground\Shell\Restart Explorer" /f
reg delete "HKEY_CLASSES_ROOT\DesktopBackground\Shell\SFC" /f
Will try and update u sir thankyou so much again
1
You must be logged in to vote
0 replies

---

Comment options
PitchAbyss
on Feb 25, 2023Feb 25, 2023
Author
@moudey sir i am trying to add a exe file in shell
item(title='app' "Z:/Pc Stuff/Install/More Apps/Must Have Free Apps and Alternatives/Keyboard.exe"')
what argument i need to pass to make it work ?
1
You must be logged in to vote
0 replies

---

Comment options
moudey
on Feb 25, 2023Feb 25, 2023
Maintainer
You forgot to add the command property
item(title='app' cmd='Z:/Pc Stuff/Install/More Apps/Must Have Free Apps and Alternatives/Keyboard.exe')
1
You must be logged in to vote
❤️
1
0 replies

---

Comment options
moudey
on Mar 1, 2023Mar 1, 2023
Maintainer
It may work fine after pressing CTRL + RIGHT-CLICK
1
You must be logged in to vote
❤️
1
0 replies

---

Comment options
PitchAbyss
on Mar 1, 2023Mar 1, 2023
Author
It may work fine after pressing CTRL + RIGHT-CLICK
yes works this way but isnt there any fix to this sir ?
1
You must be logged in to vote
0 replies

---

Comment options
moudey
on Mar 1, 2023Mar 1, 2023
Maintainer
I will fix this issue soon
1
You must be logged in to vote
❤️
1
0 replies

---

Comment options
PitchAbyss
on Mar 1, 2023Mar 1, 2023
Author
I will fix this issue soon
thankyou so much sir , have a greta day
1
You must be logged in to vote
0 replies

---

Comment options
PitchAbyss
on Mar 1, 2023Mar 1, 2023
Author
I will fix this issue soon
@moudey sir one quick question
i need to always run a script with admin privilidges...is there a way to do ? either in script or always making the cmd run as admin when i run this script
1
You must be logged in to vote
0 replies

---

Comment options
edited
moudey
on Mar 1, 2023Mar 1, 2023
Maintainer
Set the admin property to 1 to always execute the command as Administrator.
admin=1
1
You must be logged in to vote
❤️
1
0 replies

---

Comment options
PitchAbyss
on Mar 1, 2023Mar 1, 2023
Author
Set the admin property to 1 to always execute the command as Administrator.
admin=1
Where do I add this admin = 1
item(title='cache clean' image=\uE1A8 pos=3 sep='bottom' menu="My Scripts" cmd args='/k "D:\cache.bat"')
1
You must be logged in to vote
0 replies

---

Comment options
moudey
on Mar 1, 2023Mar 1, 2023
Maintainer
item(title='cache clean' image=\uE1A8 pos=3 sep='bottom' menu="My Scripts" admin=1 cmd args='/k "D:\cache.bat"')
1
You must be logged in to vote
❤️
1
0 replies

---

Comment options
PitchAbyss
on Jun 11, 2023Jun 11, 2023
Author
@moudey hi sir i ran into another confusion i hope u can help....when i select single file the options are all available but when i select multiple files all options are gone

1
You must be logged in to vote
0 replies

---

Comment options
moudey
on Jun 13, 2023Jun 13, 2023
Maintainer
Please attach the configuration file
1
You must be logged in to vote
1 reply
Comment options
PitchAbyss
on Jun 14, 2023Jun 14, 2023
Author
hi thanks sir here it is
shell.zip

---

Comment options
PitchAbyss
on Jun 14, 2023Jun 14, 2023
Author
hi thanks sir here it is
shell.zip

---

Comment options
moudey
on Jun 15, 2023Jun 15, 2023
Maintainer
Add a mode property to the item and assign it a multiple value
menu(mode="multiple" title="Misc Tools" image=\uE0F6)
{
}
1
You must be logged in to vote
❤️
1
1 reply
Comment options
PitchAbyss
on Jun 15, 2023Jun 15, 2023
Author
hi sir sorry i am confused could u edit the above file for "bulk rename here" please

---

Comment options
PitchAbyss
on Jun 15, 2023Jun 15, 2023
Author
hi sir sorry i am confused could u edit the above file for "bulk rename here" please

---

Comment options
moudey
on Jun 15, 2023Jun 15, 2023
Maintainer
Modified configuration file. Improved removal of items, making them in one line, and moving them to the front of the static items section.
1
You must be logged in to vote
❤️
1
1 reply
Comment options
PitchAbyss
on Jun 15, 2023Jun 15, 2023
Author
thankyou sooo much sir works flawless... will be getting a coffee ur way asap :)

---

Comment options
PitchAbyss
on Jun 15, 2023Jun 15, 2023
Author
thankyou sooo much sir works flawless... will be getting a coffee ur way asap :)

---

Comment options
edited
PitchAbyss
on Jun 24, 2023Jun 24, 2023
Author
@moudey a coffe your way sir :)
il keep them coming 🗡️
sir one question
To get all these options under one drive
is this the correct way ? and if so what shall i search and place there
1
You must be logged in to vote
6 replies
Show 1 previous reply
Comment options
PitchAbyss
on Jun 26, 2023Jun 26, 2023
Author
thanks for the command but i think there is some error shell no longer loads after this
Comment options
moudey
on Jun 26, 2023Jun 26, 2023
Maintainer
debug version
modify(find='always keep on this device|free up space|copy link|manage access|view online' menu="/One drive")
version 1.8.1
item(find='always keep on this device|free up space|copy link|manage access|view online' menu="/One drive")
Comment options
PitchAbyss
on Jun 26, 2023Jun 26, 2023
Author
sir though those options dont appear but there's no one drive option either
Comment options
moudey
on Jun 26, 2023Jun 26, 2023
Maintainer
try this
item(find='always keep on this device|free up space|copy link|manage access|view online' parent="One drive")
Comment options
PitchAbyss
on Jun 27, 2023Jun 27, 2023
Author
still same sir

---

Comment options
PitchAbyss
on Jun 26, 2023Jun 26, 2023
Author
thanks for the command but i think there is some error shell no longer loads after this

---

Comment options
moudey
on Jun 26, 2023Jun 26, 2023
Maintainer
debug version
modify(find='always keep on this device|free up space|copy link|manage access|view online' menu="/One drive")
version 1.8.1
item(find='always keep on this device|free up space|copy link|manage access|view online' menu="/One drive")

---

Comment options
PitchAbyss
on Jun 26, 2023Jun 26, 2023
Author
sir though those options dont appear but there's no one drive option either

---

Comment options
moudey
on Jun 26, 2023Jun 26, 2023
Maintainer
try this
item(find='always keep on this device|free up space|copy link|manage access|view online' parent="One drive")

---

Comment options
PitchAbyss
on Jun 27, 2023Jun 27, 2023
Author
still same sir

---

Comment options
PitchAbyss
on Jul 4, 2023Jul 4, 2023
Author
@moudey any help sir ?
1
You must be logged in to vote
0 replies

---

Comment options
moudey
on Jul 4, 2023Jul 4, 2023
Maintainer
sir though those options dont appear but there's no one drive option either
If there are no items in the menu, they are removed by default
1
You must be logged in to vote
5 replies
Comment options
PitchAbyss
on Jul 5, 2023Jul 5, 2023
Author
they got removed after this
item(find='always keep on this device|free up space|copy link|manage access|view online' parent="One drive")
Comment options
moudey
on Jul 10, 2023Jul 10, 2023
Maintainer
Put it in the static section
Comment options
PitchAbyss
on Jul 11, 2023Jul 11, 2023
Author
Put it in the static section
It was placed under static sir
Comment options
moudey
on Jul 12, 2023Jul 12, 2023
Maintainer
Which version are you using?
Comment options
PitchAbyss
on Jul 13, 2023Jul 13, 2023
Author
Which version are you using?
Official one from chocolatey sir

---

Comment options
PitchAbyss
on Jul 5, 2023Jul 5, 2023
Author
they got removed after this
item(find='always keep on this device|free up space|copy link|manage access|view online' parent="One drive")

---

Comment options
moudey
on Jul 10, 2023Jul 10, 2023
Maintainer
Put it in the static section

---

Comment options
PitchAbyss
on Jul 11, 2023Jul 11, 2023
Author
Put it in the static section
It was placed under static sir

---

Comment options
moudey
on Jul 12, 2023Jul 12, 2023
Maintainer
Which version are you using?

---

Comment options
PitchAbyss
on Jul 13, 2023Jul 13, 2023
Author
Which version are you using?
Official one from chocolatey sir
--------------------------------------------------------------------------------
Discussion 24: 
Link: https://github.com/moudey/Shell/discussions/297
Discussion options
Alhlhli
on Jul 5, 2023Jul 5, 2023
Hello
I noticed several variables to modify the registry values, I tried and it didn't work
You need to change two values from 0 to 1
Attempts you wrote
reg.set('HKCU\Software\Microsoft\Windows\CurrentVersion\Themes\Personalize', "AppsUseLightTheme", 1, reg.dword)
reg.set('HKCU\Software\Microsoft\Windows\CurrentVersion\Themes\Personalize', "SystemUsesLightTheme", 1, reg.dword)
 item(title='light mod' image=\uE095 reg.set('HKCU\Software\Microsoft\Windows\CurrentVersion\Themes\Personalize', "AppsUseLightTheme", 1 reg.dword reg.set('HKCU\Software\Microsoft\Windows\CurrentVersion\Themes\Personalize', "SystemUsesLightTheme", 1, reg.dword))
 item(title='Dark mod' image=\uE094 reg.set('HKCU\Software\Microsoft\Windows\CurrentVersion\Themes\Personalize', "AppsUseLightTheme", 0, reg.dword)reg.set('HKCU\Software\Microsoft\Windows\CurrentVersion\Themes\Personalize', "SystemUsesLightTheme", 0, reg.dword))


 item(title='light mod' image=\uE092 reg.set('HKCU\Software\Microsoft\Windows\CurrentVersion\Themes\Personalize', "SystemUsesLightTheme", 1, reg.dword)
 item(title='Dark mod' image=\uE092 reg.set('HKCU\Software\Microsoft\Windows\CurrentVersion\Themes\Personalize', "SystemUsesLightTheme", 0, reg.dword)
1
You must be logged in to vote

---

Comment options
Alhlhli
on Jul 5, 2023Jul 5, 2023
Author
item(title='light mod' image=\uE092 reg.set('HKCU\Software\Microsoft\Windows\CurrentVersion\Themes\Personalize', "SystemUsesLightTheme", 1,))
item(title='Dark mod' image=\uE092 reg.set('HKCU\Software\Microsoft\Windows\CurrentVersion\Themes\Personalize', "SystemUsesLightTheme", 0,))
1
You must be logged in to vote
0 replies

---

Comment options
Alhlhli
on Jul 5, 2023Jul 5, 2023
Author
I don't know it might be the = sign or the parenthesis
item(title='light mod' image=\uE092)
(
cmd=reg.set('HKCU\Software\Microsoft\Windows\CurrentVersion\Themes\Personalize', 'SystemUsesLightTheme', 0, reg.dword)
)
1
You must be logged in to vote
0 replies

---

Comment options
RubicBG
on Jul 6, 2023Jul 6, 2023
Collaborator
I'll try to help, but first, which version are you using?
1
You must be logged in to vote
1 reply
Comment options
Alhlhli
on Jul 7, 2023Jul 7, 2023
Author
1.8.1

---

Comment options
Alhlhli
on Jul 7, 2023Jul 7, 2023
Author
1.8.1

---

Comment options
edited
RubicBG
on Jul 7, 2023Jul 7, 2023
Collaborator
the correct syntax is cmd=reg.set()
item(title='Light Mode' image=\uE092 cmd=reg.set('HKCU\Software\Microsoft\Windows\CurrentVersion\Themes\Personalize', "SystemUsesLightTheme", 1,))
item(title='Dark Mode' image=\uE092 cmd=reg.set('HKCU\Software\Microsoft\Windows\CurrentVersion\Themes\Personalize', "SystemUsesLightTheme", 0,))
There are some minor things in version 1.8.1 that don't work properly with the reg function - better use one of the beta versions from discord
here is an improved version
$is_Light = reg.get('HKCU\Software\Microsoft\Windows\CurrentVersion\Themes\Personalize', 'SystemUsesLightTheme')==1  // !sys.dark
item(title='Change to @if(is_Light, 'Dark', 'Light') Mode' image=\uE092 
    cmd=reg.set('HKCU\Software\Microsoft\Windows\CurrentVersion\Themes\Personalize', "SystemUsesLightTheme", if(is_Light,0,1), reg.dword) & command.restart_explorer)
1
You must be logged in to vote
2 replies
Comment options
Alhlhli
on Jul 8, 2023Jul 8, 2023
Author
Unfortunately it didn't work for me
I took a brief idea
menu(title="Theme" image=\uE26B)
{
 item(title='Light' image=\uE137 cmd='Light')
 item(title='Dark' image=\uE138 cmd='Dark')
 
}
And create files Light.cmd $Dark.cmd
then copy 2 Files to windows directory
`@echo off
cd %~dp0
cd /d "%~dp0"
:: BatchGotAdmin================================================================
:: Amer Alhlhli
REM --> Check for permissions
nul 2>&1 "%SYSTEMROOT%\system32\cacls.exe" "%SYSTEMROOT%\system32\config\system"
REM --> If error flag set, we do not have admin.
if '%errorlevel%' NEQ '0' (
echo Requesting administrative privileges...
goto UACPrompt
) else ( goto gotAdmin )
:UACPrompt
echo Set UAC = CreateObject^("Shell.Application"^) > "%temp%\getadmin.vbs"
echo UAC.ShellExecute "%~s0", "", "", "runas", 1 >> "%temp%\getadmin.vbs"
"%temp%\getadmin.vbs"
exit /B
:gotAdmin
if exist "%temp%\getadmin.vbs" ( del "%temp%\getadmin.vbs" )
pushd "%CD%"
CD /D "%~dp0"
:: Start========================================================================
Reg.exe add "HKCU\Software\Microsoft\Windows\CurrentVersion\Themes\Personalize" /v "AppsUseLightTheme" /t REG_DWORD /d "0" /f
Reg.exe add "HKCU\Software\Microsoft\Windows\CurrentVersion\Themes\Personalize" /v "SystemUsesLightTheme" /t REG_DWORD /d "0" /f
:: End========================================================================
taskkill /f /im explorer.exe & start explorer.exe
`
I would like it to be done directly without the CMD files
Comment options
moudey
on Jul 8, 2023Jul 8, 2023
Maintainer
This function reg.get is only in the debug version

---

Comment options
Alhlhli
on Jul 8, 2023Jul 8, 2023
Author
Unfortunately it didn't work for me
I took a brief idea
menu(title="Theme" image=\uE26B)
{
 item(title='Light' image=\uE137 cmd='Light')
 item(title='Dark' image=\uE138 cmd='Dark')
 
}
And create files Light.cmd $Dark.cmd
then copy 2 Files to windows directory
`@echo off
cd %~dp0
cd /d "%~dp0"
:: BatchGotAdmin================================================================
:: Amer Alhlhli
REM --> Check for permissions
nul 2>&1 "%SYSTEMROOT%\system32\cacls.exe" "%SYSTEMROOT%\system32\config\system"
REM --> If error flag set, we do not have admin.
if '%errorlevel%' NEQ '0' (
echo Requesting administrative privileges...
goto UACPrompt
) else ( goto gotAdmin )
:UACPrompt
echo Set UAC = CreateObject^("Shell.Application"^) > "%temp%\getadmin.vbs"
echo UAC.ShellExecute "%~s0", "", "", "runas", 1 >> "%temp%\getadmin.vbs"
"%temp%\getadmin.vbs"
exit /B
:gotAdmin
if exist "%temp%\getadmin.vbs" ( del "%temp%\getadmin.vbs" )
pushd "%CD%"
CD /D "%~dp0"
:: Start========================================================================
Reg.exe add "HKCU\Software\Microsoft\Windows\CurrentVersion\Themes\Personalize" /v "AppsUseLightTheme" /t REG_DWORD /d "0" /f
Reg.exe add "HKCU\Software\Microsoft\Windows\CurrentVersion\Themes\Personalize" /v "SystemUsesLightTheme" /t REG_DWORD /d "0" /f
:: End========================================================================
taskkill /f /im explorer.exe & start explorer.exe
`
I would like it to be done directly without the CMD files

---

Comment options
moudey
on Jul 8, 2023Jul 8, 2023
Maintainer
This function reg.get is only in the debug version
--------------------------------------------------------------------------------
Discussion 25: 
Link: https://github.com/moudey/Shell/discussions/267
Discussion options
edited
ChristopheL77
on May 21, 2023May 21, 2023
Hi,
Firstly, thanks for this great tool :-)
Is there a way to open a folder in current file explorer like the standard open command in registry ?
I tried with and without cmd property
item(title='Windows1' cmd=explorer verb="Explore" dir=sys.dir)

item(title='Windows1' cmd=explorer verb="Open" dir=sys.dir)
but it didn't work.
Thanks
1
You must be logged in to vote

---

Comment options
moudey
on May 23, 2023May 23, 2023
Maintainer
Do you mean to navigate in the same window?
1
You must be logged in to vote
1 reply
Comment options
edited
ChristopheL77
on May 24, 2023May 24, 2023
Author
Yes, in order to make a favorites folders menu.

---

Comment options
edited
ChristopheL77
on May 24, 2023May 24, 2023
Author
Yes, in order to make a favorites folders menu.

---

Comment options
ChristopheL77
on May 29, 2023May 29, 2023
Author
Is this scenario supported ?
1
You must be logged in to vote
2 replies
Comment options
moudey
on Jun 1, 2023Jun 1, 2023
Maintainer
Work to support this.
👍
1
Comment options
ChristopheL77
on Jun 1, 2023Jun 1, 2023
Author
Nice thanks :)

---

Comment options
moudey
on Jun 1, 2023Jun 1, 2023
Maintainer
Work to support this.
👍
1

---

Comment options
ChristopheL77
on Jun 1, 2023Jun 1, 2023
Author
Nice thanks :)

---

Comment options
moudey
on Jul 4, 2023Jul 4, 2023
Maintainer
@ChristopheL77 try this build 35
item(title="navigate" cmd=command.navigate('c:\windows\system32'))
1
You must be logged in to vote
👍
1
1 reply
Comment options
edited
ChristopheL77
on Jul 6, 2023Jul 6, 2023
Author
Awesome, thanks a lot :)
I tried quickly but it seems you changed configuration files format, so I will have to test it later when I have more time.

---

Comment options
edited
ChristopheL77
on Jul 6, 2023Jul 6, 2023
Author
Awesome, thanks a lot :)
I tried quickly but it seems you changed configuration files format, so I will have to test it later when I have more time.
--------------------------------------------------------------------------------
Discussion 26: 
Link: https://github.com/moudey/Shell/discussions/296
Discussion options
Alhlhli
on Jul 5, 2023Jul 5, 2023
To keep your right click commands in a shell form and without extra additions for the simple user
It is recommended to delete NSS files from imports folder
And keep only the three files
taskbar.nss
static.nss
images.nss
and delet any program by ecmenu
The final result
1
You must be logged in to vote
👍
1
--------------------------------------------------------------------------------
Discussion 27: 
Link: https://github.com/moudey/Shell/discussions/292
Discussion options
edited
pavichokche
on Jun 23, 2023Jun 23, 2023
I want to hide certain programs' action items in the context menu, but they don't all just have the program's name in their action item title, or it's a generic word. For example, Microsoft Access has a 'Preview' item that appears for some file types, and I never use that software. I just want to hide all items where "MSACCESS.EXE" is the command executable.
I tried things like this but nothing worked:
item(where=this.exe = "msaccess.exe" vis="remove")
Unfortunately the documentation is so limited and lacking of examples to give context to how commands should/could be used, I'm quite lost...
1
You must be logged in to vote
Answered by RubicBG
on Jun 24, 2023Jun 24, 2023
this code is simple and it will work:
modify(find='Open As Read-Only|Open in Protected View|"new"|Preview' where=(sel.file.ext=='.accdb' or sel.file.ext=='.maw') vis=remove)
you can use NirSort ShellMenuView ( http://www.nirsoft.net/utils/shell_menu_view.html ) to see which extensions are associated with which commands.
View full answer

---

Comment options
RubicBG
on Jun 24, 2023Jun 24, 2023
Collaborator
this code is simple and it will work:
modify(find='Open As Read-Only|Open in Protected View|"new"|Preview' where=(sel.file.ext=='.accdb' or sel.file.ext=='.maw') vis=remove)
you can use NirSort ShellMenuView ( http://www.nirsoft.net/utils/shell_menu_view.html ) to see which extensions are associated with which commands.
PS: what you are actually asking for the moment is not achievable with simple commands. You can do complex logic: search the windows registry for the commands for the corresponding extension, check if the command contains the executable you are looking for ...
I was able to create something similar with another command where I check 5 paths in the registry to get the same desired result
Marked as answer
1
You must be logged in to vote
👍
2
1 reply
Comment options
pavichokche
on Jun 26, 2023Jun 26, 2023
Author
Thanks for the reply! Using shell menu view I was actually just able to disable all of the microsoft access associated items, not just see what their names/extensions are!
Answer selected by pavichokche

---

Comment options
pavichokche
on Jun 26, 2023Jun 26, 2023
Author
Thanks for the reply! Using shell menu view I was actually just able to disable all of the microsoft access associated items, not just see what their names/extensions are!
--------------------------------------------------------------------------------
Discussion 28: 
Link: https://github.com/moudey/Shell/discussions/291
Discussion options
gnogni
on Jun 23, 2023Jun 23, 2023
Every time I use the letter change from the disk manager there are too many steps. So this function would be useful.
0
You must be logged in to vote

---

Comment options
edited by moudey
RubicBG
on Jun 24, 2023Jun 24, 2023
Collaborator
$current_drive_letter = path.root(sel.path)

menu(title='Swap drive letter' where=not(current_drive_letter==sys.root) type='drive' image=\uE1A9)
{
    Item (title = 'A:' where=!path.exists('A:') and key.shift()
        admin cmd='powershell.exe' args='-command Set-Partition -DriveLetter @str.left(current_drive_letter, 1) -NewDriveLetter A; Exit' window=hidden)

    Item (title = 'B:' where=!path.exists('B:') and key.shift()
        admin cmd='powershell.exe' args='-command Set-Partition -DriveLetter @str.left(current_drive_letter, 1) -NewDriveLetter B; Exit' window=hidden)

    Item (title = 'C:' where=!path.exists('C:')
        admin cmd='powershell.exe' args='-command Set-Partition -DriveLetter @str.left(current_drive_letter, 1) -NewDriveLetter C; Exit' )

    Item (title = 'D:' where=!path.exists('D:')
        admin cmd='powershell.exe' args='-command Set-Partition -DriveLetter @str.left(current_drive_letter, 1) -NewDriveLetter D; Exit')

    Item (title = 'E:' where=!path.exists('E:')
        admin cmd='powershell.exe' args='-command Set-Partition -DriveLetter @str.left(current_drive_letter, 1) -NewDriveLetter E; Exit')

    Item (title = 'F:' where=!path.exists('F:')
        admin cmd='powershell.exe' args='-command Set-Partition -DriveLetter @str.left(current_drive_letter, 1) -NewDriveLetter F; Exit')

    Item (title = 'G:' where=!path.exists('G:')
        admin cmd='powershell.exe' args='-command Set-Partition -DriveLetter @str.left(current_drive_letter, 1) -NewDriveLetter G; Exit')

    Item (title = 'H:' where=!path.exists('H:')
        admin cmd='powershell.exe' args='-command Set-Partition -DriveLetter @str.left(current_drive_letter, 1) -NewDriveLetter H; Exit')
}
PS: More lines can be added for the remaining letters. window=hidden can be added to hide the Powershell window.
1
You must be logged in to vote
👍
2
0 replies
--------------------------------------------------------------------------------
Discussion 29: 
Link: https://github.com/moudey/Shell/discussions/289
Discussion options
Sebo1090
on Jun 15, 2023Jun 15, 2023
Is it possible to move item from the more options menu to the main menu
1
You must be logged in to vote
Answered by moudey
on Jun 15, 2023Jun 15, 2023
In the shell file, disable static item modifications like the following example
static
{ 
    // import 'imports/static.nss'
    // item(where=this.title.length > 25 menu=title.more_options)
}
View full answer

---

Comment options
moudey
on Jun 15, 2023Jun 15, 2023
Maintainer
In the shell file, disable static item modifications like the following example
static
{ 
    // import 'imports/static.nss'
    // item(where=this.title.length > 25 menu=title.more_options)
}
Save changes, press CTRL + RIGHT-CLICK to reload settings.
Marked as answer
1
You must be logged in to vote
👍
1
0 replies
Answer selected by Sebo1090

---

Comment options
Sebo1090
on Jun 15, 2023Jun 15, 2023
Author
thank you finally programs are after cataloging
1
You must be logged in to vote
0 replies
--------------------------------------------------------------------------------
Discussion 30: 
Link: https://github.com/moudey/Shell/discussions/286
Discussion options
edited
RubicBG
on Jun 8, 2023Jun 8, 2023
Collaborator
Can u build some simple function to read and write ini files:
ini.exists=('Key') -> boolean
ini.get('Key') -> Value
ini.set(('Key','Value')
ini.set(('Key','Value', reload=true) -> ini.set(('Key','Value') & app.reload
ini.delete('Key')
ini.delete('Key','Value', true) -> ini.delete(('Key','Value') & app.reload
I am trying to create menu that change settings in theme.nss, for example:
from theme.name="auto" to theme.name="modern"
from theme.border.size=1 to theme.border.size=15
Right now is possible with power-shell, but it is too messy:
(Get-Content 'theme.nss') | ForEach-Object {
$_ -replace 'theme.name="auto"', 'theme.name="modern"'
} | Set-Content 'theme.nss'
1
You must be logged in to vote
--------------------------------------------------------------------------------
Discussion 31: 
Link: https://github.com/moudey/Shell/discussions/284
Discussion options
RubicBG
on Jun 6, 2023Jun 6, 2023
Collaborator
hello
Is it possible with the current debug version to add hex values (or binary values) to the registry?
no information here: https://nilesoft.org/blog/new-functions-for-handling-the-registry
I tried to guess but to no avail:
cmd=reg.set('HKCU\...', 'TargetFolderPath', '44,00,3a,00,5c,00,00,00', reg.hex)
1
You must be logged in to vote

---

Comment options
RubicBG
on Jun 6, 2023Jun 6, 2023
Collaborator
Author
I struggled for three days, finally I wrote a post, 10 minutes later I found the solutions. phew
1
You must be logged in to vote
0 replies

---

Comment options
moudey
on Jun 8, 2023Jun 8, 2023
Maintainer
I will work on adding another function that allows writing a binary type.
1
You must be logged in to vote
👍
1
0 replies
--------------------------------------------------------------------------------
Discussion 32: 
Link: https://github.com/moudey/Shell/discussions/281
Discussion options
Natejoestev
on Jun 3, 2023Jun 3, 2023
i just updated from 1.7 to 1.8.
i backed up my old shell.shl file.
how do i migrate this file to the new import system stuff?
1
You must be logged in to vote
--------------------------------------------------------------------------------
Discussion 33: 
Link: https://github.com/moudey/Shell/discussions/278
Discussion options
nhth31
on May 29, 2023May 29, 2023
Yeah, just add animation like default
1
You must be logged in to vote

---

Comment options
hiranokite
on May 30, 2023May 29, 2023
Sponsor
#216
1
You must be logged in to vote
0 replies
--------------------------------------------------------------------------------
Discussion 34: 
Link: https://github.com/moudey/Shell/discussions/279
Discussion options
Eta-Beta
on May 29, 2023May 29, 2023
Hello, in goto there is the "Startmenu" element which opens the "C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu" folder. I would like to add a second element "Startmenu2" which opens the folder "C:\ProgramData\Microsoft\Windows\Start Menu". I duplicated the item but I don't know the syntax to enter the address of the folder to open. Thank you.
1
You must be logged in to vote

---

Comment options
moudey
on May 29, 2023May 29, 2023
Maintainer
item(title="Startmenu2" cmd='@sys.programdata\Microsoft\Windows\Start Menu')
1
You must be logged in to vote
👍
1
1 reply
Comment options
edited
Eta-Beta
on May 29, 2023May 29, 2023
Author
Hi, it works and thanks as always, I added the icon
item(title="Startmenu2" image=inherit cmd='@sys.programdata\Microsoft\Windows\Start Menu')
P.S. I forgot, if I want to start a program I simply add item(title="Startmenu2" image=inherit cmd='@sys.programdata\Microsoft\Windows\Start Menu\prova.exe') ?
While if you wanted to open a folder located on disk D for example what would change?

---

Comment options
edited
Eta-Beta
on May 29, 2023May 29, 2023
Author
Hi, it works and thanks as always, I added the icon
item(title="Startmenu2" image=inherit cmd='@sys.programdata\Microsoft\Windows\Start Menu')
P.S. I forgot, if I want to start a program I simply add item(title="Startmenu2" image=inherit cmd='@sys.programdata\Microsoft\Windows\Start Menu\prova.exe') ?
While if you wanted to open a folder located on disk D for example what would change?
--------------------------------------------------------------------------------
Discussion 35: 
Link: https://github.com/moudey/Shell/discussions/277
Discussion options
TCNOco
on May 27, 2023May 27, 2023
Moved from Discord, re: FierySpectre#7775
The current system of having a link in the discord works kinda... for downloading it. But a real place for it where e.g. changes vs the stable build could also be posted would be nice
Alternatively a dedicated discord channel in this server containing this information would be an easier to implement solution that could serve much of the same function
For now, I've created a Beta Downloads tab in the Discord for the beta
1
You must be logged in to vote
--------------------------------------------------------------------------------
Discussion 36: 
Link: https://github.com/moudey/Shell/discussions/268
Discussion options
nthnpxtadcgrp
on May 22, 2023May 22, 2023
Hi,
I don't have admin rights on my computer but would highly benefit being able to use Shell on my work computer.
Would there be a known workaround for this use case ?
Best regards,
1
You must be logged in to vote

---

Comment options
moudey
on May 23, 2023May 23, 2023
Maintainer
You only need administrator privileges during registration. I will look into this issue.
1
You must be logged in to vote
0 replies

---

Comment options
moudey
on May 27, 2023May 26, 2023
Maintainer
Hi @nthnpxtadcgrp Try this solution
Windows Registry Editor Version 5.00

[HKEY_CURRENT_USER\Software\Classes\CLSID\{BAE3934B-8A6A-4BFB-81BD-3FC599A1BAF3}]
@="Nilesoft.Shell"

[HKEY_CURRENT_USER\Software\Classes\CLSID\{BAE3934B-8A6A-4BFB-81BD-3FC599A1BAF3}\InprocServer32]
@="path\\to\\shell.dll"
"ThreadingModel"="Apartment"

[HKEY_CURRENT_USER\Software\Classes\Drive\shellex\FolderExtensions\{BAE3934B-8A6A-4BFB-81BD-3FC599A1BAF3}]
@="Nilesoft.Shell"
"DriveMask"=dword:000000ff
Make sure to write down the path to the "shell.dll" file.
Save this code in an empty file and name it "shell.reg". Double click to merge the file into the Windows Registry and restart the explorer.
1
You must be logged in to vote
0 replies
--------------------------------------------------------------------------------
Discussion 37: 
Link: https://github.com/moudey/Shell/discussions/147
Discussion options
edited
PitchAbyss
on Feb 1, 2023Feb 1, 2023
Hi Is there any chance to have this great tool as chocolatey package ?
@moudey
1
You must be logged in to vote

---

Comment options
moudey
on Feb 9, 2023Feb 9, 2023
Maintainer
Hi Is there any chance to have this great tool as chocolatey package ?
I will work to support it in the future
1
You must be logged in to vote
❤️
1
1 reply
Comment options
PitchAbyss
on Feb 9, 2023Feb 9, 2023
Author
Thankyou so much sir that day il be prepared with some more coffees :D
❤️
1

---

Comment options
PitchAbyss
on Feb 9, 2023Feb 9, 2023
Author
Thankyou so much sir that day il be prepared with some more coffees :D
❤️
1

---

Comment options
moudey
on Feb 21, 2023Feb 21, 2023
Maintainer
https://community.chocolatey.org/packages/nilesoft.shell
1
You must be logged in to vote
❤️
1
9 replies
Show 4 previous replies
Comment options
moudey
on Feb 22, 2023Feb 21, 2023
Maintainer
I don't know, but there was a false positive on a virus scan and it was corrected
😄
1
Comment options
PitchAbyss
on Feb 22, 2023Feb 21, 2023
Author
I don't know, but there was a false positive on a virus scan and it was corrected
How dare they give positive on virus scan to this beauty of a program
😄
1
Comment options
PitchAbyss
on Feb 24, 2023Feb 24, 2023
Author
https://community.chocolatey.org/packages/nilesoft.shell
bro i still cant install or find the package.... is there any update when will be the package be passed ?
Comment options
moudey
on Feb 24, 2023Feb 24, 2023
Maintainer
It's still in review but you can use the following command
choco install nilesoft.shell --version=1.8.1
❤️
1
Comment options
PitchAbyss
on Feb 24, 2023Feb 24, 2023
Author
It's still in review but you can use the following command
choco install nilesoft.shell --version=1.8.1
damn are they reviewing global crisis....thankyou sooo much works flawlessly sir thanks
😄
1

---

Comment options
moudey
on Feb 22, 2023Feb 21, 2023
Maintainer
I don't know, but there was a false positive on a virus scan and it was corrected
😄
1

---

Comment options
PitchAbyss
on Feb 22, 2023Feb 21, 2023
Author
I don't know, but there was a false positive on a virus scan and it was corrected
How dare they give positive on virus scan to this beauty of a program
😄
1

---

Comment options
PitchAbyss
on Feb 24, 2023Feb 24, 2023
Author
https://community.chocolatey.org/packages/nilesoft.shell
bro i still cant install or find the package.... is there any update when will be the package be passed ?

---

Comment options
moudey
on Feb 24, 2023Feb 24, 2023
Maintainer
It's still in review but you can use the following command
choco install nilesoft.shell --version=1.8.1
❤️
1

---

Comment options
PitchAbyss
on Feb 24, 2023Feb 24, 2023
Author
It's still in review but you can use the following command
choco install nilesoft.shell --version=1.8.1
damn are they reviewing global crisis....thankyou sooo much works flawlessly sir thanks
😄
1

---

Comment options
PitchAbyss
on Mar 10, 2023Mar 10, 2023
Author
@moudey bro is the package still not approved ? whats the status
1
You must be logged in to vote
3 replies
Comment options
moudey
on Mar 10, 2023Mar 10, 2023
Maintainer
This is their message response
Moderators are volunteers, so sometimes the queue can build up a bit. Rest assured that your package will be reviewed soon.
Comment options
PitchAbyss
on Mar 12, 2023Mar 12, 2023
Author
This is their message response
Moderators are volunteers, so sometimes the queue can build up a bit. Rest assured that your package will be reviewed soon.
i am speechless
Comment options
PitchAbyss
on Apr 11, 2023Apr 11, 2023
Author
This is their message response
Moderators are volunteers, so sometimes the queue can build up a bit. Rest assured that your package will be reviewed soon.
Bro there is an update :(

---

Comment options
moudey
on Mar 10, 2023Mar 10, 2023
Maintainer
This is their message response
Moderators are volunteers, so sometimes the queue can build up a bit. Rest assured that your package will be reviewed soon.

---

Comment options
PitchAbyss
on Mar 12, 2023Mar 12, 2023
Author
This is their message response
Moderators are volunteers, so sometimes the queue can build up a bit. Rest assured that your package will be reviewed soon.
i am speechless

---

Comment options
PitchAbyss
on Apr 11, 2023Apr 11, 2023
Author
This is their message response
Moderators are volunteers, so sometimes the queue can build up a bit. Rest assured that your package will be reviewed soon.
Bro there is an update :(

---

Comment options
PitchAbyss
on Apr 28, 2023Apr 28, 2023
Author
@moudey i hope this gets released soon sir :D
1
You must be logged in to vote
👍
1
6 replies
Show 1 previous reply
Comment options
PitchAbyss
on May 9, 2023May 9, 2023
Author
Well as soon as possible I will solve this issue
any updates sir ? :D
Comment options
PitchAbyss
on May 9, 2023May 9, 2023
Author
Well as soon as possible I will solve this issue
Comment options
moudey
on May 10, 2023May 10, 2023
Maintainer
Please refer to this page https://community.chocolatey.org/packages/nilesoft-shell/1.8.1
Comment options
moudey
on May 26, 2023May 26, 2023
Maintainer
Has been approved
https://community.chocolatey.org/packages/nilesoft-shell/1.8.1
Comment options
PitchAbyss
on May 26, 2023May 26, 2023
Author
Has been approved
https://community.chocolatey.org/packages/nilesoft-shell/1.8.1
Hahahaha finally before humanity ended they did it...congrats sir
😄
1

---

Comment options
PitchAbyss
on May 9, 2023May 9, 2023
Author
Well as soon as possible I will solve this issue
any updates sir ? :D

---

Comment options
PitchAbyss
on May 9, 2023May 9, 2023
Author
Well as soon as possible I will solve this issue

---

Comment options
moudey
on May 10, 2023May 10, 2023
Maintainer
Please refer to this page https://community.chocolatey.org/packages/nilesoft-shell/1.8.1

---

Comment options
moudey
on May 26, 2023May 26, 2023
Maintainer
Has been approved
https://community.chocolatey.org/packages/nilesoft-shell/1.8.1

---

Comment options
PitchAbyss
on May 26, 2023May 26, 2023
Author
Has been approved
https://community.chocolatey.org/packages/nilesoft-shell/1.8.1
Hahahaha finally before humanity ended they did it...congrats sir
😄
1
--------------------------------------------------------------------------------
Discussion 38: 
Link: https://github.com/moudey/Shell/discussions/220
Discussion options
sigmaphi007
on Mar 24, 2023Mar 24, 2023
Hi,
I used your application with NTLITE to post-setup unattended using the silent parameter, Your app opens the browser which leads to your website.
This break makes unattended install kinda useless as I have to click the error to go away before the final stages boots up.
1
You must be logged in to vote

---

Comment options
sigmaphi007
on Mar 24, 2023Mar 24, 2023
Author
Here is the error I got
1
You must be logged in to vote
0 replies

---

Comment options
moudey
on Mar 24, 2023Mar 24, 2023
Maintainer
I will add a parameter that allows you to bypass opening online documentation during installation.
2
You must be logged in to vote
0 replies

---

Comment options
sigmaphi007
on May 25, 2023May 25, 2023
Author
Hi, please any update on this?
Thanks in advance!
1
You must be logged in to vote
0 replies
--------------------------------------------------------------------------------
Discussion 39: 
Link: https://github.com/moudey/Shell/discussions/269
Discussion options
rpc31
on May 22, 2023May 22, 2023
Pretty please ?
1
You must be logged in to vote

---

Comment options
moudey
on May 23, 2023May 23, 2023
Maintainer
Unfortunately, I don't have experience in developing macOS programs, but we will take a look in the future.
1
You must be logged in to vote
0 replies
--------------------------------------------------------------------------------
Discussion 40: 
Link: https://github.com/moudey/Shell/discussions/211
Discussion options
edited
JonrGull
on Mar 18, 2023Mar 18, 2023
My apologies if this should be submitted to Power Toys instead, but I love using both of these tools.
I installed SHELL today and noticed this duplicated selection of choosing "PowerRename"
This behavior only seems to appear when trying to PowerRename a folder.
It may have something to do with this setting.
I've tried playing with the Power Toys settings to see if I could fix it, but the only way to fix it is to turn the feature off. If anyone has an idea of how I could solve this without a codebase update, I'd appreciate it.
Thank you.
1
You must be logged in to vote

---

Comment options
edited
moudey
on Mar 18, 2023Mar 18, 2023
Maintainer
The last item can be removed by this method
open imports/static.nss and add this code
item(where=this.pos>10 find='"powerrename"' vis=vis.remove)
save changes, press ctrl + right click to reload settings.
I will add a new option to make it easier to control duplicate items.
2
You must be logged in to vote
❤️
1
0 replies

---

Comment options
edited
JonrGull
on Mar 18, 2023Mar 18, 2023
Author
Thank you! That helps a ton.
1
You must be logged in to vote
👍
1
0 replies

---

Comment options
moudey
on Mar 18, 2023Mar 18, 2023
Maintainer
If this duplicate item is with folders only, the type property must be specified to narrow the search process
item(
    type='dir' // dir only
    where=this.pos>10 and this.type==1 // Determine the item's position and type (sep=0, item=1, submenu=2)
    find='"powerrename"' // Search the item title by matching
    vis=vis.remove 
)
1
You must be logged in to vote
0 replies

---

Comment options
detile
on May 20, 2023May 20, 2023
The duplicate entry when one or several files are selected can be removed with the code above in this comment.
But when there is no file selected (right click on an empty space in explorer) the duplicate entry is still there. Not a big deal though.
1
You must be logged in to vote
0 replies
--------------------------------------------------------------------------------
Discussion 41: 
Link: https://github.com/moudey/Shell/discussions/88
Discussion options
edited
SaiyajinK
on Dec 13, 2022Dec 13, 2022
Hi,
I translated the entries into French.
1.7 : shell_fr.zip
it would be nice to have an interface to easily manage the settings without going through a text editor.
++
1
You must be logged in to vote
👍
2

---

Comment options
Eta-Beta
on May 11, 2023May 11, 2023
Hi, congratulations, unfortunately I don't know the programming language and I had to change to Italian directly in the *nss files
1
You must be logged in to vote
0 replies

---

Comment options
Elrick91
on May 20, 2023May 20, 2023
How to use this French translation ? how to install it please?!
1
You must be logged in to vote
0 replies
--------------------------------------------------------------------------------
Discussion 42: 
Link: https://github.com/moudey/Shell/discussions/262
Discussion options
Eta-Beta
on May 18, 2023May 18, 2023
Good morning everyone, as the title suggests, the commands in select file don't work. I made several changes but I never touched this code I just translated the names of the sections, but the commands just don't work. I normally use keyboard shortcuts but I don't understand why these 3 commands don't work. The remaining commands in the "File Manager" section all work.
Does anyone know why they don't work?
1
You must be logged in to vote

---

Comment options
moudey
on May 18, 2023May 18, 2023
Maintainer
What version of Windows are you using?
1
You must be logged in to vote
1 reply
Comment options
Eta-Beta
on May 18, 2023May 18, 2023
Author
Windows 11 Home
Build 22000.co release.210604-1628

---

Comment options
Eta-Beta
on May 18, 2023May 18, 2023
Author
Windows 11 Home
Build 22000.co release.210604-1628
--------------------------------------------------------------------------------
Discussion 43: 
Link: https://github.com/moudey/Shell/discussions/259
Discussion options
Dante2202
on May 14, 2023May 14, 2023
I am using a registry tweak to have the firewall settings display in the context menu:
I want to move this to the "Favorites" menu, I am able to get it inside the Favorites menu but the subfolder for the "Windows Defender Firewall" doesn't completely display:
I was using the below code in the "static" section on the shell.nss
item(find='Windows Defender Firewall' menu='Favorites')
1
You must be logged in to vote
Answered by moudey
on May 15, 2023May 15, 2023
Where do I add the static & dynamic sections to in the debug version? I tried adding it to the following files with no success:
The static, dynamic, var and images sections has been dropped.
Use modify in "shell.nss" body instead of static item.
View full answer

---

Comment options
moudey
on May 14, 2023May 14, 2023
Maintainer
There is an bug when moving items in this version, specifically when the names are similar, but it has been fixed in the debug version. To avoid this issue as much as possible, match the search by putting double quotes.
item(find=' "Windows Defender Firewall" ' menu='Favorites')
1
You must be logged in to vote
0 replies

---

Comment options
edited
Dante2202
on May 15, 2023May 15, 2023
Author
Where do I add the static & dynamic sections to in the debug version? I tried adding it to the following files with no success:
shell.nss
modifications.nss
new.nss
theme.nss
I am trying to add the following that I had working in the latest stable build:
static
 { 
  import 'imports/static.nss'
  item(where=this.title.length > 25 menu=title.more_options)
  item(find='NVIDIA Control Panel' vis=vis.remove)
  item(find='Share with Skype' vis=vis.remove)
  item(find='WinMerge' vis=vis.remove)
  item(find='Kill not responding tasks' position=-1)
  item(find='Display Settings' menu='Favorites') 
  item(find='Personalize' menu='Favorites') 
  item(find='MSI True Color' vis=vis.remove)
 }

 dynamic
 {
   
  menu(type='desktop|taskbar' title='Favorites' image=\uE0C8)
        {
                item(title='Registry editor' image cmd='regedit.exe')
                item(title='Paint' image cmd='C:\Program Files\Classic Paint\mspaint1.exe')
                item(title='Notepad++' image cmd='C:\Program Files (x86)\Notepad++\notepad++.exe')
  item(title='MSI True Color' image cmd='C:\Program Files\Portrait Displays\MSI True Color\MsiTrueColor.exe')
        } 
1
You must be logged in to vote
1 reply
Comment options
moudey
on May 15, 2023May 15, 2023
Maintainer
Where do I add the static & dynamic sections to in the debug version? I tried adding it to the following files with no success:
The static, dynamic, var and images sections has been dropped.
Use modify in "shell.nss" body instead of static item.
shell.nss
// static item
modify(find='Personalize' menu='Favorites') 

// dynamic item
menu(type='desktop|taskbar' title='Favorites' image=\uE0C8)
{
    item(title='Registry editor' image cmd='regedit.exe')
    ...
} 
Marked as answer
Answer selected by Dante2202

---

Comment options
moudey
on May 15, 2023May 15, 2023
Maintainer
Where do I add the static & dynamic sections to in the debug version? I tried adding it to the following files with no success:
The static, dynamic, var and images sections has been dropped.
Use modify in "shell.nss" body instead of static item.
shell.nss
// static item
modify(find='Personalize' menu='Favorites') 

// dynamic item
menu(type='desktop|taskbar' title='Favorites' image=\uE0C8)
{
    item(title='Registry editor' image cmd='regedit.exe')
    ...
} 
Marked as answer
Answer selected by Dante2202

---

Comment options
edited
Dante2202
on May 16, 2023May 15, 2023
Author
Perfect, I can confirm debug version fixes my issue. Thank you so much!
If I want to add a shortcut to the device manager, what would the command be to add to Shell?
1
You must be logged in to vote
0 replies
--------------------------------------------------------------------------------
Discussion 44: 
Link: https://github.com/moudey/Shell/discussions/173
Discussion options
0-BlackSpectrum-0
on Feb 27, 2023Feb 27, 2023
1
You must be logged in to vote
Answered by moudey
on Feb 27, 2023Feb 27, 2023
Add this code to shell.nss in static section
item(find='graphics*|nvidia*' vis=0)
View full answer

---

Comment options
moudey
on Feb 27, 2023Feb 27, 2023
Maintainer
Add this code to shell.nss in static section
item(find='graphics*|nvidia*' vis=0)
Save changes, press CTRL + RIGHT-CLICK or restart Explorer to reload settings.
Marked as answer
2
You must be logged in to vote
12 replies
Show 7 previous replies
Comment options
moudey
on May 14, 2023May 13, 2023
Maintainer
A menu must be created first in the dynamic section, if it does not exist, to which the items will be moved from the static section.
We created a "common" item in the dynamic section because it does not exist, then we gave a command in the static section to move a specific "copy" item to the "common" menu.
Comment options
Eta-Beta
on May 14, 2023May 13, 2023
Thanks a lot, now I understand and it will be useful to me, but I wanted to move the items to the "More Options" submenu in the figure that already exists.
Comment options
edited
moudey
on May 14, 2023May 13, 2023
Maintainer
item(find='DefenderUI add exclusion' menu=title.more_options)
Comment options
Eta-Beta
on May 14, 2023May 13, 2023
DOOOOOO!!!! .. Thanks again for holding my hand up to here. Unfortunately there is the problem that I'm not good with scripts and I don't even know English so I have to use Google translate. I greet you and go to sleep here it is 02:00 AM
Comment options
moudey
on May 14, 2023May 14, 2023
Maintainer
It seems that you are near me because the time is the same as in Egypt. Have a good night's sleep.
👍
1
Answer selected by 0-BlackSpectrum-0

---

Comment options
moudey
on May 14, 2023May 13, 2023
Maintainer
A menu must be created first in the dynamic section, if it does not exist, to which the items will be moved from the static section.
We created a "common" item in the dynamic section because it does not exist, then we gave a command in the static section to move a specific "copy" item to the "common" menu.

---

Comment options
Eta-Beta
on May 14, 2023May 13, 2023
Thanks a lot, now I understand and it will be useful to me, but I wanted to move the items to the "More Options" submenu in the figure that already exists.

---

Comment options
edited
moudey
on May 14, 2023May 13, 2023
Maintainer
item(find='DefenderUI add exclusion' menu=title.more_options)

---

Comment options
Eta-Beta
on May 14, 2023May 13, 2023
DOOOOOO!!!! .. Thanks again for holding my hand up to here. Unfortunately there is the problem that I'm not good with scripts and I don't even know English so I have to use Google translate. I greet you and go to sleep here it is 02:00 AM

---

Comment options
moudey
on May 14, 2023May 14, 2023
Maintainer
It seems that you are near me because the time is the same as in Egypt. Have a good night's sleep.
👍
1
--------------------------------------------------------------------------------
Discussion 45: 
Link: https://github.com/moudey/Shell/discussions/253
Discussion options
Eta-Beta
on May 11, 2023May 11, 2023
Good morning, I am not an expert in programming codes but I can do something so I ask, if possible, for help to be able to move the elements of the various menus
as they were in Windows. I also looked at the examples on the site but I just can't figure out how to do it. I have successfully changed the color of the menus. Thanks very much for an answer.
WINDOWS
Nilesoft Shell
1
You must be logged in to vote

---

Comment options
RubicBG
on May 12, 2023May 12, 2023
Collaborator
If I guess correctly, you are using the latest release (Shell version 1.8.1). There is a new dev version (Bata - if you want to call it). Тhe syntax is slightly different. If you make changes now, they may not be compatible with the next version. Do you want the release syntax or the dev version?
2
You must be logged in to vote
👍
2
1 reply
Comment options
Eta-Beta
on May 12, 2023May 12, 2023
Author
Hello and thanks a lot for the answer. Unfortunately I'm not able to do that, to write the codes, so I don't understand what you mean.

---

Comment options
Eta-Beta
on May 12, 2023May 12, 2023
Author
Hello and thanks a lot for the answer. Unfortunately I'm not able to do that, to write the codes, so I don't understand what you mean.

---

Comment options
moudey
on May 12, 2023May 12, 2023
Maintainer
Open the shell.nss file and edit this line in the static section by adding a // sign at the beginning of the line to disable it.
item(where=this.title.length > 25 menu=title.more_options)
to
// item(where=this.title.length > 25 menu=title.more_options)
Save changes, press CTRL + RIGHT-CLICK to reload settings.
2
You must be logged in to vote
👍
1
2 replies
Comment options
Eta-Beta
on May 12, 2023May 12, 2023
Author
Hello and thanks for the answer. Perfect I had to restart the PC though, because the settings weren't updated.
Thanks again. The menu is still long, but I should be able to disable Defender UI, Scan with Microsoft Defender and Create Quick Backup... from the software settings. I had read in another post of yours that you could disable an item from Nilesoft Shell but I didn't understand the changes.
Comment options
Eta-Beta
on May 12, 2023May 12, 2023
Author
I have found my answer #173 . Ok for the desktop but what are the other menus called? sorry for the maybe stupid question.

---

Comment options
Eta-Beta
on May 12, 2023May 12, 2023
Author
Hello and thanks for the answer. Perfect I had to restart the PC though, because the settings weren't updated.
Thanks again. The menu is still long, but I should be able to disable Defender UI, Scan with Microsoft Defender and Create Quick Backup... from the software settings. I had read in another post of yours that you could disable an item from Nilesoft Shell but I didn't understand the changes.

---

Comment options
Eta-Beta
on May 12, 2023May 12, 2023
Author
I have found my answer #173 . Ok for the desktop but what are the other menus called? sorry for the maybe stupid question.
--------------------------------------------------------------------------------
Discussion 46: 
Link: https://github.com/moudey/Shell/discussions/221
Discussion options
Flax4
on Mar 29, 2023Mar 29, 2023
I just found this Shell and I am loving it, I was wondering though (or of it's a problem on my end?) if it's possible to have the Shell replace the context menu for the Win+X menu, right clicking on programs on the taskbar (both pinned and the ones in the system tray) and network/audio + time/date?
On a computer with Windows 10 it seems to replace the Win+X menu but on Windows 11 for me it's not.
1
You must be logged in to vote

---

Comment options
Eta-Beta
on May 12, 2023May 12, 2023
Hi, it works for me, I have Win 11 with Explorer Patcher also installed.
1
You must be logged in to vote
0 replies
--------------------------------------------------------------------------------
Discussion 47: 
Link: https://github.com/moudey/Shell/discussions/255
Discussion options
edited
Dante2202
on May 12, 2023May 12, 2023
I must be doing something wrong with the shell.nss file for the debug version because when I extracted the build and registered it with both the shell.nss file that came with the zip and when I replaced it with the working shell.nss file I was using I just get an over sized contex menu.
Is there a default template I can use for the shell.nss that will work with the debug version?
1
You must be logged in to vote
Answered by RubicBG
on May 12, 2023May 12, 2023
Some of the settings are moved in imports\theme.nss. There 'font.size=20' is responsible for over sized context menu. if you remove this line it will use default settings like the release version.
One more tip: use 'name="modern"' instead of 'name="black"'
I hope this helps
View full answer

---

Comment options
RubicBG
on May 12, 2023May 12, 2023
Collaborator
Some of the settings are moved in imports\theme.nss. There 'font.size=20' is responsible for over sized context menu. if you remove this line it will use default settings like the release version.
One more tip: use 'name="modern"' instead of 'name="black"'
I hope this helps
Marked as answer
1
You must be logged in to vote
👍
1
0 replies
Answer selected by Dante2202
--------------------------------------------------------------------------------
Discussion 48: 
Link: https://github.com/moudey/Shell/discussions/254
Discussion options
Eta-Beta
on May 11, 2023May 11, 2023
Good morning, I noticed that with Explorer Patcher installed or not installed the system tray menu changes.

Explorer Patcher installed

Explorer Patcher not installed
1
You must be logged in to vote
--------------------------------------------------------------------------------
Discussion 49: 
Link: https://github.com/moudey/Shell/discussions/249
Discussion options
RubicBG
on May 6, 2023May 6, 2023
Collaborator
I have a few questions:
There is something "weird" in the Navigation Pane of Windows Explorer. There are different context menus:
the 'first menu' is when selecting an object (sel.count=1) (everything is fine here)
the 'second menu' is when choosing between objects (sel.count=1)
the 'third menu' is when no object is selected (sel.count=0)
Q1: All check menus have position '-4'. However, they are separated by the Shell menus for the 'second menu'. Why?
Q2: when using
where=(window.is_tree and sel.count==0)
or
where=(window.parent.name=="NamespaceTreeControl" and sel.count==0 )
I can only choose the 'third menu'. However, how do I distinguish the 'first' from the 'second menu'?
Q3: command.refresh doesn't work for Navigation Pane. is there any way to refresh the Navigation Pane?
Q4: When creating a checked item, I have to use different commands for cmd=. How do I merge them? Something like cmd=IF(,,)? (Currently it works as I have created for each Item two new ones - one for checked=1 and one for checked=0 with different cmd=, but it looks terrible with so many repetitions)
Another strange thing I noticed on my computer. When I use the reg.set and reg.delete functions, sometimes they work, sometimes they don't!
$reg1 = 'HKEY_CLASSES_ROOT...'
$reg2 = 'HKEY_CURRENT_USER...'
$reg3 = 'HKEY_LOCAL_MACHINE...'
reg.get(reg1,... - work
reg.set(reg1,... - work
reg.delete(reg1,... - work
reg.get(reg2,... - work
reg.set(reg2,... - work
reg.delete(reg2,... - work
reg.get(reg3,... - work
reg.set(reg3,... - DON'T work
reg.delete(reg3,... - DON'T work
With the ones I have used so far, I notice that when using HKLM the reg function cannot change values, regardless of whether they can be read.
Q5: Assuming the reg function works, why can't I change the values for HKLM?
1
You must be logged in to vote
Answered by moudey
on May 7, 2023May 7, 2023
Then how do the menus 'Terminal', 'File Manage', 'Develop' and 'Go to' come between items with the same position '-4'? If I give to standard Item a fixed position with pos='Number', they are positioned, but when I add the new items, again with pos='Number' they are arranged in a jumbled manner - they are not in the order in which I put them. I don't see any logic - I don't know what's going on, and I don't know how to explain it more precisely, and I don't know what to ask! (However I managed by positioning them all using pos='bottom' and then making the new items with position=indexof() ) If there is a logical explanation I want to know what it is.
View full answer

---

Comment options
moudey
on May 6, 2023May 6, 2023
Maintainer
Q1: All check menus have position '-4'. However, they are separated by the Shell menus for the 'second menu'. Why?
Explain more.
Q2: when using
where=(window.is_tree and sel.count==0)
or
where=(window.parent.name=="NamespaceTreeControl" and sel.count==0 )
I can only choose the 'third menu'. However, how do I distinguish the 'first' from the 'second menu'?
It is determined by (the names of the menu items, menu items count, the names of the windows, the path of the selected objects, their type, etc.).
Q3: command.refresh doesn't work for Navigation Pane. is there any way to refresh the Navigation Pane?
command.refresh works for me with the navigation pane. The implementation of the system's commands will be developed later and most of these commands will be included.
Q4: When creating a checked item, I have to use different commands for cmd=. How do I merge them? Something like cmd=IF(,,)? (Currently it works as I have created for each Item two new ones - one for checked=1 and one for checked=0 with different cmd=, but it looks terrible with so many repetitions)
Give me a simple example to clarify the idea further so that a solution can be found.
Q5: Assuming the reg function works, why can't I change the values for HKLM?
These commands need to run the explorer with administrator privileges.
Sorry, I've been getting really nervous lately and my focus has been distracted
1
You must be logged in to vote
0 replies

---

Comment options
RubicBG
on May 6, 2023May 6, 2023
Collaborator
Author
First of all : Great Work!!!
I'm not a programmer, but with Nilesoft Shell I feel like a Microsoft programmer developing the new context menu for Windows 12! I am so excited!
It all started very simply: I just wanted to add an item, just one... now the menu looks like this:

With as many Items as I can add, it took me more than a day to position them:
Regarding Q1: when I use
modify(find='"Show this PC"' type='*' where=(window.is_tree) tip=this.pos)
to see how a standard (built-in) Item is positioned. I always get '-4' - for all items.


Then how do the menus 'Terminal', 'File Manage', 'Develop' and 'Go to' come between items with the same position '-4'? If I give to standard Item a fixed position with pos='Number', they are positioned, but when I add the new items, again with pos='Number' they are arranged in a jumbled manner - they are not in the order in which I put them. I don't see any logic - I don't know what's going on, and I don't know how to explain it more precisely, and I don't know what to ask! (However I managed by positioning them all using pos='bottom' and then making the new items with position=indexof() ) If there is a logical explanation I want to know what it is.
Regarding Q2: I use
'type='*' where=(window.is_tree and sel.count==0)'
thus it works with no object selected.
I haven't been able to find a way where by right-clicking between two objects (there is a space between every two objects), I can add the same Items without them being displayed when the object is selected - these are two different menus (shown above). I wanted to find a solution myself and spent half a day wandering around with no results.
Regarding Q3: You gave me an answer here. just for information: I use
cmd=reg.set( .. ) & command.refresh
I see refreshing on the right in the View Pane, but on the left in the Navigation Pane nothing happens. If I press F5 the update happens.
Regarding Q4: This is the simplest code that applies to this question. All others are more complicated:
$regLinux = 'HKCU\Software\Classes\CLSID{B2B4A4D1-2754-4140-A2EB-9A76D9D7CDC6}'
$regLinuxShowed = reg.get(regLinux,'System.IsPinnedToNameSpaceTree')!=0
item(type='*' where=(window.is_tree and sel.count==0 and regLinuxShowed)
checked=regLinuxShowed title='Show Linux' position=indexof("Show Network", 1)
cmd=reg.set(regLinux, 'System.IsPinnedToNameSpaceTree', '0', reg.dword)
& command.refresh)
item(type='*' where=(window.is_tree and sel.count==0 and !regLinuxShowed)
checked=regLinuxShowed title='Show Linux' position=indexof("Show Network", 1)
cmd=reg.delete(regLinux, 'System.IsPinnedToNameSpaceTree')
& command.refresh)`
I can't merge commands under one cmd= . One item has reg.set and the other reg.delete (command.refresh doesn't work here either)
Regarding Q5: You gave me an answer here. I'll try using 'admin cmd args=' to force it.
1
You must be logged in to vote
0 replies

---

Comment options
moudey
on May 7, 2023May 7, 2023
Maintainer
Then how do the menus 'Terminal', 'File Manage', 'Develop' and 'Go to' come between items with the same position '-4'? If I give to standard Item a fixed position with pos='Number', they are positioned, but when I add the new items, again with pos='Number' they are arranged in a jumbled manner - they are not in the order in which I put them. I don't see any logic - I don't know what's going on, and I don't know how to explain it more precisely, and I don't know what to ask! (However I managed by positioning them all using pos='bottom' and then making the new items with position=indexof() ) If there is a logical explanation I want to know what it is.
Position values
auto = -4
middle = -3
top = -2
bottom = - 1
custome >= 0
The "this.pos" function returns the item's default position value auto unless it has been modified in the position property.
Marked as answer
1
You must be logged in to vote
0 replies
Answer selected by RubicBG
--------------------------------------------------------------------------------
Discussion 50: 
Link: https://github.com/moudey/Shell/discussions/250
Discussion options
edited
RubicBG
on May 6, 2023May 6, 2023
Collaborator
most of my nss files and the commands in them do not depend on the order of import. I wish I didn't have to edit shell.nss for each one each time
import 'specific folder/.nss'
import 'specific folder/a.nss'
import 'specific folder/ab*.nss'
import 'specific folder/a*c.nss'
import 'specific folder/*bc.nss'
or perhaps
import 'specific folder/*.nss'
import 'specific folder/'+ "\*" +'.nss'
1
You must be logged in to vote
👍
1
--------------------------------------------------------------------------------
Discussion 51: 
Link: https://github.com/moudey/Shell/discussions/223
Discussion options
RubicBG
on Apr 1, 2023Apr 1, 2023
Collaborator
It would be great if the Shell program could have a feature that enables users to permanently delete a file when they hover the mouse over the Delete option and press the SHIFT key. This feature can provide an extra layer of safety while deleting files.
Please consider adding this feature in the next release of the Shell program.
0
You must be logged in to vote

---

Comment options
hiranokite
on Apr 2, 2023Apr 1, 2023
Sponsor
wait, but isn't that standard in windows?
1
You must be logged in to vote
0 replies

---

Comment options
RubicBG
on Apr 12, 2023Apr 12, 2023
Collaborator
Author
I saw that the commands from the terminal menu have a tooltip and I thought it would be a good idea to have a tooltip on the delete menu as well. It didn't occur to me at all to test whether the "delete permanently" functionality exists at all. My Bad! :)
shell
{
 static
 {
  code: item(find='Delete' tip=["\xE1A7 Press SHIFT key to permanently delete selected file(s)", tip.warning, 1.0])
 }
}
1
You must be logged in to vote
0 replies

---

Comment options
Syuanz
on Apr 30, 2023Apr 30, 2023
https://zj.syuanz.top/1822.html
1
You must be logged in to vote
0 replies

---

Comment options
Syuanz
on Apr 30, 2023Apr 30, 2023
This system has this feature
1
You must be logged in to vote
0 replies
--------------------------------------------------------------------------------
Discussion 52: 
Link: https://github.com/moudey/Shell/discussions/244
Discussion options
Syuanz
on Apr 30, 2023Apr 30, 2023
This is the download address: https://www.123pan.com/s/YSjA-q5CJv.html
1
You must be logged in to vote
👍
1
--------------------------------------------------------------------------------
Discussion 53: 
Link: https://github.com/moudey/Shell/discussions/241
Discussion options
RubicBG
on Apr 26, 2023Apr 26, 2023
Collaborator
I am attempting to retrieve stored settings from the registry. However, when I use the Registry functions, the results I receive contain a space at the end. The 'str.trim()' function fails to remove it, as it is not a typical space. Currently, I am using 'str.left(reg_data, str.len(reg_data)-1)' to remove the space, and it is functioning correctly. Are there any more efficient solutions available?
1
You must be logged in to vote
Answered by moudey
on Apr 26, 2023Apr 26, 2023
There is a bug in the reg function and it has been fixed in the latest build
New functions for handling the Registry
View full answer

---

Comment options
moudey
on Apr 26, 2023Apr 26, 2023
Maintainer
Does the space at the end of the value already exist in the Windows registry?
1
You must be logged in to vote
0 replies

---

Comment options
RubicBG
on Apr 26, 2023Apr 26, 2023
Collaborator
Author
Here is part of reg export:
Windows Registry Editor Version 5.00

[HKEY_CURRENT_USER\Software\WinRAR\Interface\Themes]
"ActivePath"="POPCOL_96x96"

[HKEY_CURRENT_USER\Software\WinRAR\Profiles\0]
"Name"="Default Profile"

[HKEY_CURRENT_USER\Software\WinRAR\Profiles\1]
"Name"="Create e-mail attachment"

[HKEY_LOCAL_MACHINE\Software\WinRAR]
"exe64"="C:\\Program Files\\WinRar\\WinRAR.exe"
and from a dynamic .nss file, here's a var {} tail:
var  
 {
 reg_winrar_exe64 = reg(reg.lm, 'Software\WinRAR', 'exe64') 
 reg_winrar_theme = reg(reg.cu, 'Software\WinRAR\Interface\Themes', 'ActivePath')
 
 path_winrar_theme = path.combine(sys.appdata, 'WinRAR\Themes', str.left(reg_winrar_theme, str.len(reg_winrar_theme)-1)) 
 
 reg_Prof0_tmp = reg(reg.cu, 'Software\WinRAR\Profiles\0','Name')
 reg_Prof0 = str.left(reg_Prof0_tmp, str.len(reg_Prof0_tmp)-1)
 }
At first, I didn't notice it because reg_winrar_exe64 didn't require any modifications, and the argument needed a space.
1
You must be logged in to vote
1 reply
Comment options
moudey
on Apr 26, 2023Apr 26, 2023
Maintainer
There is a bug in the reg function and it has been fixed in the latest build
New functions for handling the Registry
Marked as answer
Answer selected by RubicBG

---

Comment options
moudey
on Apr 26, 2023Apr 26, 2023
Maintainer
There is a bug in the reg function and it has been fixed in the latest build
New functions for handling the Registry
Marked as answer
Answer selected by RubicBG

---

Comment options
RubicBG
on Apr 27, 2023Apr 27, 2023
Collaborator
Author
Thank you for sending the latest build of the program. However, I'm encountering some issues with it.
Firstly, I've noticed that the tooltip doesn't appear anywhere, even in the Terminal menu, even when I test it with only the package and no other additional changes from me.
Secondly, after every restart of the explorer, everything works except the tooltip, and the additional menu on the taskbar remains visible. However, after I right-click on a file/folder/background in explorer (not on Desktop, only in explorer), the additional menu disappears completely and permanently, and it's only fixed by restarting the explorer.
I've tried removing the imports one by one to see if any of them cause the problem, but the condition remained the same. I also tried running the program in Windows Sandbox, and it worked without any issues. Additionally, I installed the old version using 'winget install nilesoft.shell,' and everything worked fine.
I'm not sure how to diagnose the issue, but I suspect that it may be conflicting with another program that's running on my computer.
Please let me know if there's anything I can do to help identify and resolve these issues.
1
You must be logged in to vote
2 replies
Comment options
moudey
on Apr 28, 2023Apr 28, 2023
Maintainer
Tip are disabled by default. You can activate them by adding this command to the settings section:
tip.enabled=true
Comment options
RubicBG
on Apr 29, 2023Apr 29, 2023
Collaborator
Author
i tried before with 'tip.enabled=true' in both shell.nss and theme.nss without success, but with this tip, 'settings.tip.enabled=true' works without fail. thanks.
about the disappearing taskbar menu, the problem is it's still there. if you have an idea for the future - write me back

---

Comment options
moudey
on Apr 28, 2023Apr 28, 2023
Maintainer
Tip are disabled by default. You can activate them by adding this command to the settings section:
tip.enabled=true

---

Comment options
RubicBG
on Apr 29, 2023Apr 29, 2023
Collaborator
Author
i tried before with 'tip.enabled=true' in both shell.nss and theme.nss without success, but with this tip, 'settings.tip.enabled=true' works without fail. thanks.
about the disappearing taskbar menu, the problem is it's still there. if you have an idea for the future - write me back
--------------------------------------------------------------------------------
Discussion 54: 
Link: https://github.com/moudey/Shell/discussions/236
Discussion options
edited
TheFangster
on Apr 17, 2023Apr 17, 2023
Hi there,
Just wondering, is there any or will there be any options to customize the context menu for currently open applications in the taskbar?
I recently saw that microsoft added in windows 11 an option to end a task by right clicking on it on the taskbar. This really saves time compared to the old way of opening the task manager, finding the app and terminating it. I'm asking this because I'm using windows 10.
*Forgot to add, it seems a shell menu does open if you shift+right click an app on the taskbar, but that app must be closed.
1
You must be logged in to vote
--------------------------------------------------------------------------------
Discussion 55: 
Link: https://github.com/moudey/Shell/discussions/233
Discussion options
zilpe
on Apr 11, 2023Apr 11, 2023
I would like to move a submenu item from 7-zip to the main menu. Is this possible in Shell?
Thanks Mahmoud for all your efforts!
1
You must be logged in to vote

---

Comment options
zilpe
on Apr 11, 2023Apr 11, 2023
Author
Nevermind. This is discussed in #137
1
You must be logged in to vote
0 replies
--------------------------------------------------------------------------------
Discussion 56: 
Link: https://github.com/moudey/Shell/discussions/181
Discussion options
edited
hiranokite
on Mar 4, 2023Mar 4, 2023
Sponsor
can we have a option to make "vis" and "admin" work without need to keep the activation key pressed until the context menu show-up or the app start?
to show hidden menus i prefer to hold LMB and press RMB, but i can use Shift Keys and press RMB too
menu(type='desktop' title='Shor&tcuts' separator="before" image=\uE14A vis=@key(1) or @key(key.lshift) or @key(key.rshift))
and
to open file as admin i prefer to hold RMB and press LMB, but i can use Shift Keys and press LMB too
item(title='Open with Notepad'type='file' admin=@key(2) or @key(key.lshift) or @key(key.rshift) cmd='notepad.exe' args='@sel.path.quote' image='notepad.exe')
1
You must be logged in to vote

---

Comment options
moudey
on Mar 4, 2023Mar 4, 2023
Maintainer
The keyboard has two events, the first before the menu appears, and the second before the command is executed
RMB + LMB = CTRL + RMB
1
You must be logged in to vote
0 replies

---

Comment options
moudey
on Mar 4, 2023Mar 4, 2023
Maintainer
Refer to the terminal file, you will find that the value of true is assigned to the admin property in the event that Shift only before executing the command and not before the menu appears.
1
You must be logged in to vote
0 replies

---

Comment options
hiranokite
on Mar 11, 2023Mar 11, 2023
Author
Sponsor
Sorry, my English is not the best, here is a example of what I'm trying to explain.
the last delay of 50ms is the problem, with 50ms everything works perfectly but, if you lower the number to 30ms or less the key activation will not work. so this mean you need to keep the activation key pressed til the menu open.
in my use I hold shift and right after press the right click I release the shift so the vis=key.shift() take no effect
1
You must be logged in to vote
0 replies

---

Comment options
moudey
on Mar 12, 2023Mar 12, 2023
Maintainer
It works fine with me. I will check this issue.
Mouse functionality has been added to the command execution event. Where you can run the command as administrator, for example, by right-clicking on the item
item(title="Command Prompt" admin=key.rbutton()  cmd args='/K echo  RB=@key.rbutton(), LB=@key.lbutton()')
https://nilesoft.org/download/shell/debug.zip
2
You must be logged in to vote
27 replies
Show 22 previous replies
Comment options
hiranokite
on Mar 22, 2023Mar 22, 2023
Author
Sponsor
How to combine displays in a video?
that was a VM i just expanded the screens and cropped the record area.
Comment options
moudey
on Mar 22, 2023Mar 22, 2023
Maintainer
Sorry test it now
Comment options
hiranokite
on Mar 22, 2023Mar 22, 2023
Author
Sponsor
Sorry test it now
Tested so many combinations, seems unbreakable now!
worked on every test with and without ExplorerPatcher.
👍
1
Comment options
moudey
on Mar 23, 2023Mar 23, 2023
Maintainer
This build works on all connected displays
❤️
1
Comment options
hiranokite
on Mar 23, 2023Mar 23, 2023
Author
Sponsor
This build works on all connected displays
Nice, since I only have 2 displays I can't test it properly but thanks.
on my tests everything worked flawless on Windows 11
not tested on Windows 10 or 11 with 10 taskbar (ExplorerPatcher).

---

Comment options
hiranokite
on Mar 22, 2023Mar 22, 2023
Author
Sponsor
How to combine displays in a video?
that was a VM i just expanded the screens and cropped the record area.

---

Comment options
moudey
on Mar 22, 2023Mar 22, 2023
Maintainer
Sorry test it now

---

Comment options
hiranokite
on Mar 22, 2023Mar 22, 2023
Author
Sponsor
Sorry test it now
Tested so many combinations, seems unbreakable now!
worked on every test with and without ExplorerPatcher.
👍
1

---

Comment options
moudey
on Mar 23, 2023Mar 23, 2023
Maintainer
This build works on all connected displays
❤️
1

---

Comment options
hiranokite
on Mar 23, 2023Mar 23, 2023
Author
Sponsor
This build works on all connected displays
Nice, since I only have 2 displays I can't test it properly but thanks.
on my tests everything worked flawless on Windows 11
not tested on Windows 10 or 11 with 10 taskbar (ExplorerPatcher).

---

Comment options
hiranokite
on Mar 13, 2023Mar 13, 2023
Author
Sponsor
if possible can you add a button on shell to open the shell.exe location on explorer, or maybe just a hidden shortcut eg. Crtl+S or change email to Ctrl+M and add Ctrl+E to open shell on explorer, it will be helpful while testing.
1
You must be logged in to vote
👍
2
0 replies

---

Comment options
hiranokite
on Mar 31, 2023Mar 31, 2023
Author
Sponsor
@moudey seems the new build broke taskbar context menu, are you aware of this?
can't get to work with my or default nss files.
Windows 11 build 22621.1485
Only show the default windows menu.
1
You must be logged in to vote
👍
1
1 reply
Comment options
moudey
on Mar 31, 2023Mar 31, 2023
Maintainer
It was working fine on Windows, but it didn't work on Sandbox. Now it works
👍
1

---

Comment options
moudey
on Mar 31, 2023Mar 31, 2023
Maintainer
It was working fine on Windows, but it didn't work on Sandbox. Now it works
👍
1
--------------------------------------------------------------------------------
Discussion 57: 
Link: https://github.com/moudey/Shell/discussions/113
Discussion options
Natejoestev
on Dec 10, 2022Dec 10, 2022
Before discovering Shell, i had to use the registry keys to add/remove file types to the New File menu.
Now that i use Shell, how can i change these options under New?
1
You must be logged in to vote

---

Comment options
edited
moudey
on Dec 10, 2022Dec 10, 2022
Maintainer
Hi @Natejoestev, Let's talk through examples with this bulld
Delete a shortcut item from New menu
static
{
    item(
         // Check when clicking in an empty space
         type='back'

         // The first parameter to search for the title of the item. 
         // The second is the location of the item
         find=['shortcut', '/new'] 

         // Item removal command
         vis=remove
     )
}
Add a custom item that creates a text file containing a message
dynamic
{
    item(title="my item in 'New Menu'" 
        parent="/new" 
        image=#0f0 
        cmd=@io.file.create('@(str.guid).txt', 'Hello World!')
    )
}
1
You must be logged in to vote
0 replies

---

Comment options
Natejoestev
on Dec 30, 2022Dec 30, 2022
Author
does regedit still affect the menu, or is it all in the shell.shl?
1
You must be logged in to vote
0 replies

---

Comment options
moudey
on Dec 31, 2022Dec 31, 2022
Maintainer
Shell does not use the Windows registry to edit items. Modifications are made through the configuration file.
1
You must be logged in to vote
0 replies

---

Comment options
Natejoestev
on Dec 31, 2022Dec 31, 2022
Author
so how can i change the files in the new menu, add stuff like open in Visual Studio, and change the file manage options for the file context menu?
1
You must be logged in to vote
0 replies

---

Comment options
moudey
on Dec 31, 2022Dec 31, 2022
Maintainer
Please refer to the first comment
1
You must be logged in to vote
0 replies

---

Comment options
Natejoestev
on Dec 31, 2022Dec 31, 2022
Author
so, everything that isn't in the .shl file now, is a default?
1
You must be logged in to vote
0 replies

---

Comment options
Natejoestev
on Dec 31, 2022Dec 31, 2022
Author
what is wrong with line 53???
line[53] column[8], Property unexpected "shell.shl":
1
You must be logged in to vote
0 replies

---

Comment options
moudey
on Dec 31, 2022Dec 31, 2022
Maintainer
type='back' and vis='remove'
1
You must be logged in to vote
0 replies

---

Comment options
moudey
on Jan 2, 2023Jan 2, 2023
Maintainer
Please try this build
The settings file contains several ways to customize static and dynamic items, with the possibility of dividing the config file into several files to simplify work on it.
1
You must be logged in to vote
0 replies

---

Comment options
edited
s17534
on Mar 21, 2023Mar 21, 2023
Hi! My issue relates to this discussion so I just chime in ;)
I can't disable New file menu (or edit it's contents), tried this but nothing happens as you can see below:
static
{
    item(
         // Check when clicking in an empty space
         type='back'

         // The first parameter to search for the title of the item. 
         // The second is the location of the item
         find=['shortcut', '/new'] 

         // Item removal command
         vis=remove
     )
}
also, adding new entry there isn't working, tried this sample:
dynamic
{
    item(title="my item in 'New Menu'" 
        parent="/new" 
        image=#0f0 
        cmd=@io.file.create('@(str.guid).txt', 'Hello World!')
    )
}
1
You must be logged in to vote
8 replies
Show 3 previous replies
Comment options
s17534
on Mar 22, 2023Mar 22, 2023
Oh believe me, I tried that and restart explorer too. Even on english windows too:
Comment options
moudey
on Mar 22, 2023Mar 22, 2023
Maintainer
Use the in property instead of [search, parent] and there is a bug when writing / at the beginning of the name
static
{
    item(
         type='back'
         find='shortcut' 
         in='new'
         vis="remove"
     )
}

dynamic
{
    item(title="my item in 'New Menu'" 
        parent="new"
        image=#0f0 
        cmd=@io.file.create('@(str.guid).txt', 'Hello World!')
    )
}
Comment options
edited
s17534
on Mar 22, 2023Mar 22, 2023
And now we are getting somewhere. By adding those lines:
item(type='back' find='skrót' in='new' vis="remove") item(type='back' find='Microsoft' in='new' vis="remove") item(type='back' find='Folder' in='new' vis="remove") item(type='back' find='Obraz' in='new' vis="remove")
I got rid of every one of the items in the "Nowy" menu. Then I figured out why not just set one like this while commenting previous ones:
item(type='back' find='nowy' vis="remove")
Now I have no "Nowy" menu, Good Job! May I ask you, is it possible to move some of the items from other apps to my custom menu?
For example I have some "Git GUI here" or "Git Bash here" items and I want to move them to "Develop" menu. Is it possible?
👍
1
Comment options
edited
moudey
on Mar 22, 2023Mar 22, 2023
Maintainer
It can be improved to remove multiple items with one command:
item(type='back' find='skrót|Microsoft|Folder|Obraz' in='new' vis="remove") 

// Or remove all items in New submenu
item(type='back' find='*' in='new' vis="remove") 

// Move items to submenu
item(find='Git GUI here|Git Bash here' menu='Develop')
Comment options
s17534
on Mar 22, 2023Mar 22, 2023
It's perfect now, thank you :)

---

Comment options
s17534
on Mar 22, 2023Mar 22, 2023
Oh believe me, I tried that and restart explorer too. Even on english windows too:

---

Comment options
moudey
on Mar 22, 2023Mar 22, 2023
Maintainer
Use the in property instead of [search, parent] and there is a bug when writing / at the beginning of the name
static
{
    item(
         type='back'
         find='shortcut' 
         in='new'
         vis="remove"
     )
}

dynamic
{
    item(title="my item in 'New Menu'" 
        parent="new"
        image=#0f0 
        cmd=@io.file.create('@(str.guid).txt', 'Hello World!')
    )
}

---

Comment options
edited
s17534
on Mar 22, 2023Mar 22, 2023
And now we are getting somewhere. By adding those lines:
item(type='back' find='skrót' in='new' vis="remove") item(type='back' find='Microsoft' in='new' vis="remove") item(type='back' find='Folder' in='new' vis="remove") item(type='back' find='Obraz' in='new' vis="remove")
I got rid of every one of the items in the "Nowy" menu. Then I figured out why not just set one like this while commenting previous ones:
item(type='back' find='nowy' vis="remove")
Now I have no "Nowy" menu, Good Job! May I ask you, is it possible to move some of the items from other apps to my custom menu?
For example I have some "Git GUI here" or "Git Bash here" items and I want to move them to "Develop" menu. Is it possible?
👍
1

---

Comment options
edited
moudey
on Mar 22, 2023Mar 22, 2023
Maintainer
It can be improved to remove multiple items with one command:
item(type='back' find='skrót|Microsoft|Folder|Obraz' in='new' vis="remove") 

// Or remove all items in New submenu
item(type='back' find='*' in='new' vis="remove") 

// Move items to submenu
item(find='Git GUI here|Git Bash here' menu='Develop')

---

Comment options
s17534
on Mar 22, 2023Mar 22, 2023
It's perfect now, thank you :)
--------------------------------------------------------------------------------
Discussion 58: 
Link: https://github.com/moudey/Shell/discussions/118
Discussion options
griseouslight
on Jan 20, 2023Jan 19, 2023
I would like to know if it would be possible to add the feature to replace the Alt+Space menu for programs:
I would like to point out that it already does this for the taskbar specifically if you focus on it and press Alt+Space:
2
You must be logged in to vote
👍
1

---

Comment options
moudey
on Mar 18, 2023Mar 18, 2023
Maintainer
Please refer to #181
1
You must be logged in to vote
0 replies
--------------------------------------------------------------------------------
Discussion 59: 
Link: https://github.com/moudey/Shell/discussions/202
Discussion options
edited
martin-rueegg
on Mar 13, 2023Mar 13, 2023
Collaborator
I've been working extensively with the configuration files and the documentation. I have found some terms to be confusing or "characterless" and would suggest to change them in order to improve understanding and readability, also avoiding lingual ambiguity or ambiguity with other terms.
The following therms I have found to be confusing.
set
Current New Comments
set settings The term "set" reminds me more of the actual linguistic noun set, rather than a short form of "settings" or "defaults" (as it was termed before). Hence, I consider settings to be more explanatory.
static and dynamic
These terms are not very self-explanatory. Even after understanding the concept, I find the terms still meaningless or rather a bit confusion. static seems to refer to the "existing" items, as in "already built", hence "static". Whereas dynamic might refer to the action of "creating/building". However, the static section is about changing items (hence also dynamic) while the dynamic section is not necessarily as dynamic as it might seem.
(I'm aware that the author is not a native English speaker [neither am I, btw.] it it might be the result of automatic translation.)
My suggestion would be to deprecate the two terms and replace them as follows:
Current New Comments
static update As explained above, the term static seems to refer to the existing items. However, the configuration here is really about the change done to those items - in terms of appearance or function. The term "change", however, seems not to be a fantastic keyword for a section. Hence update, which is already known in the IT world, particularly from SQL, where also existing entries are changed/updated.
dynamic insert Again, the term dynamic is not very explanatory. Analogous to the above entry, I'd suggest insert as it is adding/inserting new menuitems.
Static item
Current New Comments
item process1 or change2 The main issue here is the ambiguity with the dynamic item entry. While they are similar, they are not the same. A dynamic item is always a item, never a menu. Also, it does not actually represent a menuitem, but rather the instruction on how to identify and change them.3
Footnotes
process might not be a great choice. It came up due to the term process instructions I've used in the documentation to describe, what those items do. ↩
change might be the better choice: In the update sections are the instructions how to change existing menuitems ... ↩
Of course, also update would work here, but if update would be used as the section name, ambiguity there must be avoided. ↩
2
You must be logged in to vote
👍
1

---

Comment options
moudey
on Mar 14, 2023Mar 14, 2023
Maintainer
I amended by adding the following while keeping the current names
set | settings

static | update
{
    item | change
}

dynamic | new | insert
I also plan to change the name of Shell after many users suggested it.
3
You must be logged in to vote
❤️
1
4 replies
Comment options
martin-rueegg
on Mar 14, 2023Mar 14, 2023
Collaborator
Author
I amended by adding the following while keeping the current names
That sounds awesome!
Would you agree on the new names be the default, and the old names being "deprecated"? Then I would go and update the documentation accordingly.
I also plan to change the name of Shell after many users suggested it.
Yes, I can understand that. Firstly, most people don't know that explorer.exe in Windows is primarily the shell, and only kinda secondary a File Explorer. So for them the name "Shell" might not make much sense. And then, Shell is actually not a "shell" in the technical sense, but rather a tool to modify the real shell, explorer.exe.
Do you already have a name in mind?
Is there maybe a beautiful word in Arabic for customize, re-think, improve, enhance, make it your own, that could be transliterated?
Comment options
moudey
on Mar 14, 2023Mar 14, 2023
Maintainer
Would you agree on the new names be the default, and the old names being "deprecated"? Then I would go and update the documentation accordingly.
But it is supposed to be published with the new version, right?
Do you already have a name in mind?
I already have the name "dija", which is short for my little girl, Khadija.
Comment options
martin-rueegg
on Mar 14, 2023Mar 14, 2023
Collaborator
Author
Would you agree on the new names be the default, and the old names being "deprecated"? Then I would go and update the documentation accordingly.
But it is supposed to be published with the new version, right?
Absolutely. Do you have an estimate on when to release? There might be some other "naming" issues in the functions, that we could discuss.
Comment options
martin-rueegg
on Mar 14, 2023Mar 14, 2023
Collaborator
Author
Do you already have a name in mind?
I already have the name "dija", which is short for my little girl, Khadija.
Sounds nice! - But you should ask her! Princesses do not like to be dubbed. And then it's confusing within your family whether you're referring to your work, or your love.... Just my two cents. :-)

---

Comment options
martin-rueegg
on Mar 14, 2023Mar 14, 2023
Collaborator
Author
I amended by adding the following while keeping the current names
That sounds awesome!
Would you agree on the new names be the default, and the old names being "deprecated"? Then I would go and update the documentation accordingly.
I also plan to change the name of Shell after many users suggested it.
Yes, I can understand that. Firstly, most people don't know that explorer.exe in Windows is primarily the shell, and only kinda secondary a File Explorer. So for them the name "Shell" might not make much sense. And then, Shell is actually not a "shell" in the technical sense, but rather a tool to modify the real shell, explorer.exe.
Do you already have a name in mind?
Is there maybe a beautiful word in Arabic for customize, re-think, improve, enhance, make it your own, that could be transliterated?

---

Comment options
moudey
on Mar 14, 2023Mar 14, 2023
Maintainer
Would you agree on the new names be the default, and the old names being "deprecated"? Then I would go and update the documentation accordingly.
But it is supposed to be published with the new version, right?
Do you already have a name in mind?
I already have the name "dija", which is short for my little girl, Khadija.

---

Comment options
martin-rueegg
on Mar 14, 2023Mar 14, 2023
Collaborator
Author
Would you agree on the new names be the default, and the old names being "deprecated"? Then I would go and update the documentation accordingly.
But it is supposed to be published with the new version, right?
Absolutely. Do you have an estimate on when to release? There might be some other "naming" issues in the functions, that we could discuss.

---

Comment options
martin-rueegg
on Mar 14, 2023Mar 14, 2023
Collaborator
Author
Do you already have a name in mind?
I already have the name "dija", which is short for my little girl, Khadija.
Sounds nice! - But you should ask her! Princesses do not like to be dubbed. And then it's confusing within your family whether you're referring to your work, or your love.... Just my two cents. :-)
--------------------------------------------------------------------------------
Discussion 60: 
Link: https://github.com/moudey/Shell/discussions/155
Discussion options
SkewelVsAll
on Feb 5, 2023Feb 5, 2023
I wanted to know how I can reduce the intensity of the shadows of the Shell right click menu to something close or similar to the stock context menu.
Wanted to know how I could set Shell as my default system-wide context menu.
How do I add my GPUs control panel (AMD Adrenalin Software in my case) in the Shell context menu?
Thanks
0
You must be logged in to vote
👍
1

---

Comment options
edited
moudey
on Feb 5, 2023Feb 5, 2023
Maintainer
Open shell.nss file in Shell folder
To customize the size and color of the shadow
shell
{
    set
    {
        theme
        {
            shadow
            {
                enabled=1
                size=4 // value from 0 to 20
                offest = 2
                color=#000000
                opacity=25
            }
        }
    }
}
Shell does not support system-wide context menu yet
In dynamic section, enter this command
item(type='desktop' title='AMD Adrenalin Software' image cmd='path\to\amd.exe')
Save file, press CTRL+RIGHT-CLICK or restart Explorer to reload settings.
To restart Explorer use this command shell.exe -restart
1
You must be logged in to vote
4 replies
Comment options
moudey
on Feb 5, 2023Feb 5, 2023
Maintainer
Shell only works with 3rd party that use system shell context menus
Comment options
SkewelVsAll
on Mar 8, 2023Mar 8, 2023
Author
Hey, sorry for the late response - so far I have only been able to add the AMD Adrenaline driver as an entry on Shell and I'm okay with it.
Another issue that I noticed is that - everything I have something copied in the clipboard the "Paste" entry on Shell seems to change position.
For example: If I have nothing copied the "Paste" option will be greyed out and it will be listed as the 4th option from the top everytime I right click the mouse. But if I do have something copied in my clipboard, the option shifts it's position to the being the bottom 3rd/4th option with other similar options such as "Paste shortcut" etc right below it.
I wanted to know - is there any way, that I could permanently fix the position of the "Paste" option to being at the top of the context menu?
Comment options
moudey
on Mar 8, 2023Mar 8, 2023
Maintainer
Use the position property to specify the position of the menuitem
static
{
    // find by name
    item(find='"paste"' pos=5)
    // or find by id
    item(where=this.id==id.paste pos=5)
}
Comment options
SkewelVsAll
on Mar 8, 2023Mar 8, 2023
Author
Oh, alright. Thanks.
Also, I was wondering would it be possible for you to replicate the menu items with their positions, seperation bar etc as seen on a fresh install of Windows 10 and ship it out by default with future versions of Shell, where the users job will only trickle down to adding custom options if they wish to?
Because me and many other casual users just want the same consistent context menu options available like in Windows 10, with the difference being that Shell UI is of Windows 11.
Thanks.

---

Comment options
moudey
on Feb 5, 2023Feb 5, 2023
Maintainer
Shell only works with 3rd party that use system shell context menus

---

Comment options
SkewelVsAll
on Mar 8, 2023Mar 8, 2023
Author
Hey, sorry for the late response - so far I have only been able to add the AMD Adrenaline driver as an entry on Shell and I'm okay with it.
Another issue that I noticed is that - everything I have something copied in the clipboard the "Paste" entry on Shell seems to change position.
For example: If I have nothing copied the "Paste" option will be greyed out and it will be listed as the 4th option from the top everytime I right click the mouse. But if I do have something copied in my clipboard, the option shifts it's position to the being the bottom 3rd/4th option with other similar options such as "Paste shortcut" etc right below it.
I wanted to know - is there any way, that I could permanently fix the position of the "Paste" option to being at the top of the context menu?

---

Comment options
moudey
on Mar 8, 2023Mar 8, 2023
Maintainer
Use the position property to specify the position of the menuitem
static
{
    // find by name
    item(find='"paste"' pos=5)
    // or find by id
    item(where=this.id==id.paste pos=5)
}

---

Comment options
SkewelVsAll
on Mar 8, 2023Mar 8, 2023
Author
Oh, alright. Thanks.
Also, I was wondering would it be possible for you to replicate the menu items with their positions, seperation bar etc as seen on a fresh install of Windows 10 and ship it out by default with future versions of Shell, where the users job will only trickle down to adding custom options if they wish to?
Because me and many other casual users just want the same consistent context menu options available like in Windows 10, with the difference being that Shell UI is of Windows 11.
Thanks.
--------------------------------------------------------------------------------
Discussion 61: 
Link: https://github.com/moudey/Shell/discussions/179
Discussion options
edited
Yash-Yadav-0
on Mar 3, 2023Mar 3, 2023
I was trying to install courser concept file in folder and when i right click the .inf file , unlike typical windows right click there is no option to install .inf file in Shell . plz add this feature , that will be helpful. Thanks
1
You must be logged in to vote

---

Comment options
edited
moudey
on Mar 3, 2023Mar 3, 2023
Maintainer
Add this code to shell.nss in dynamic section
item(type="file" find='.inf' title="Install" cmd='InfDefaultInstall.exe' args='"@sel.path"')
Save changes, press ctrl + right-click to reload setting.
https://stackoverflow.com/questions/619604/installing-a-inf-file-using-a-windows-batch-file
1
You must be logged in to vote
0 replies

---

Comment options
Yash-Yadav-0
on Mar 3, 2023Mar 3, 2023
Author
Thanks
…
1
You must be logged in to vote
0 replies
--------------------------------------------------------------------------------
Discussion 62: 
Link: https://github.com/moudey/Shell/discussions/158
Discussion options
NVHT
on Feb 9, 2023Feb 9, 2023
How can I make the text stretch and fill the space?
1
You must be logged in to vote
Answered by moudey
on Feb 9, 2023Feb 9, 2023
Try this build
You can set the minimum and maximum width
theme
{
    layout.width=300 // minimum 
    layout.width=[300, 500] // [min, max]
}
View full answer

---

Comment options
moudey
on Feb 9, 2023Feb 9, 2023
Maintainer
How can I make the text stretch and fill the space?
Do you want to increase the width?
To reduce size
theme
{
    item.padding=[2,auto]
    item.margin=[0, auto]
    
    border.padding=[4,6]
}
1
You must be logged in to vote
2 replies
Comment options
NVHT
on Feb 9, 2023Feb 9, 2023
Author

I want to increase the width. I want to move the two elements to the edge

Basically like the original, but themed.
Comment options
moudey
on Feb 9, 2023Feb 9, 2023
Maintainer
I will add a width option to theme.layout to set the minimum width

---

Comment options
NVHT
on Feb 9, 2023Feb 9, 2023
Author

I want to increase the width. I want to move the two elements to the edge

Basically like the original, but themed.

---

Comment options
moudey
on Feb 9, 2023Feb 9, 2023
Maintainer
I will add a width option to theme.layout to set the minimum width

---

Comment options
moudey
on Feb 9, 2023Feb 9, 2023
Maintainer
Try this build
You can set the minimum and maximum width
theme
{
    layout.width=300 // minimum 
    layout.width=[300, 500] // [min, max]
}
Marked as answer
2
You must be logged in to vote
🎉
1
1 reply
Comment options
NVHT
on Feb 9, 2023Feb 9, 2023
Author
Works great now. Thanks.
👍
1
Answer selected by moudey

---

Comment options
NVHT
on Feb 9, 2023Feb 9, 2023
Author
Works great now. Thanks.
👍
1
--------------------------------------------------------------------------------
Discussion 63: 
Link: https://github.com/moudey/Shell/discussions/122
Discussion options
edited
sarathdm
on Jan 21, 2023Jan 21, 2023
How to remove this item
1
You must be logged in to vote

---

Comment options
moudey
on Jan 21, 2023Jan 21, 2023
Maintainer
Open the /imports/terminal.nss file located in Shell directory.
replace title.terminal with id.terminal.title, and then restart File Explorer from Shell.exe
1
You must be logged in to vote
0 replies

---

Comment options
sarathdm
on Jan 21, 2023Jan 21, 2023
Author
Icon misplaced and no title showing
1
You must be logged in to vote
0 replies

---

Comment options
moudey
on Jan 21, 2023Jan 21, 2023
Maintainer
Open /imports/terminal.nss replace title.windows_terminal with id.windows_terminal.title
1
You must be logged in to vote
0 replies

---

Comment options
sarathdm
on Jan 21, 2023Jan 21, 2023
Author
thanks
1
You must be logged in to vote
0 replies

---

Comment options
sarathdm
on Jan 21, 2023Jan 21, 2023
Author
Any way to remove or hide these from this context menu
1
You must be logged in to vote
0 replies

---

Comment options
moudey
on Jan 21, 2023Jan 21, 2023
Maintainer
Open shell.nss and add this code to static section
item(type='taskbar' find='toolbar' vis=vis.remove)
item(type='taskbar' find='show cortana' vis=vis.disable)
//etc...
1
You must be logged in to vote
0 replies

---

Comment options
sarathdm
on Jan 21, 2023Jan 21, 2023
Author
ok
1
You must be logged in to vote
0 replies

---

Comment options
sarathdm
on Jan 21, 2023Jan 21, 2023
Author
Icon misplaced
1
You must be logged in to vote
0 replies

---

Comment options
moudey
on Jan 21, 2023Jan 21, 2023
Maintainer
Choose one from this link
Open shell.nss and add this code to static section
item(type='taskbar' find='lock the taskbar' image=\uE19A)
1
You must be logged in to vote
0 replies

---

Comment options
sarathdm
on Jan 21, 2023Jan 21, 2023
Author
ok
1
You must be logged in to vote
0 replies

---

Comment options
sarathdm
on Jan 21, 2023Jan 21, 2023
Author
thanks
1
You must be logged in to vote
0 replies

---

Comment options
sarathdm
on Jan 21, 2023Jan 21, 2023
Author
remove item from this context menu only
1
You must be logged in to vote
0 replies

---

Comment options
sarathdm
on Jan 21, 2023Jan 21, 2023
Author
Remove form the menu and add icons
1
You must be logged in to vote
3 replies
Comment options
moudey
on Jan 21, 2023Jan 21, 2023
Maintainer
Please refer to this topic #117
Comment options
moudey
on Jan 21, 2023Jan 21, 2023
Maintainer
Not all items have an icon
Comment options
sarathdm
on Jan 22, 2023Jan 22, 2023
Author
ok

---

Comment options
moudey
on Jan 21, 2023Jan 21, 2023
Maintainer
Please refer to this topic #117

---

Comment options
moudey
on Jan 21, 2023Jan 21, 2023
Maintainer
Not all items have an icon

---

Comment options
sarathdm
on Jan 22, 2023Jan 22, 2023
Author
ok

---

Comment options
sarathdm
on Jan 22, 2023Jan 22, 2023
Author
item goto remove from menu
i typed item(type='*' where=window.is_start find='goto' vis=vis.remove) in shell.nss
but not working......
any idea to remove goto in that menu
1
You must be logged in to vote
2 replies
Comment options
edited
moudey
on Jan 22, 2023Jan 22, 2023
Maintainer
"Go to" is a dynamic item we just need to change some properties
Open /imports/goto.nss replace the value of where property with the following
where=!window.is_start && (window.is_taskbar||sel.count)
Comment options
sarathdm
on Jan 22, 2023Jan 22, 2023
Author
ok
Thanks

---

Comment options
edited
moudey
on Jan 22, 2023Jan 22, 2023
Maintainer
"Go to" is a dynamic item we just need to change some properties
Open /imports/goto.nss replace the value of where property with the following
where=!window.is_start && (window.is_taskbar||sel.count)

---

Comment options
sarathdm
on Jan 22, 2023Jan 22, 2023
Author
ok
Thanks

---

Comment options
PitchAbyss
on Jan 22, 2023Jan 22, 2023
hi sir how do i remove this item
1
You must be logged in to vote
15 replies
Show 10 previous replies
Comment options
PitchAbyss
on Jan 25, 2023Jan 25, 2023
@moudey sir
Comment options
moudey
on Jan 27, 2023Jan 27, 2023
Maintainer
Hi @ifelixculpa
can you tell me how can i move this out side of more section
Moved this item because items longer than 25 characters have been migrated to another "More options" submenu.
You have two options, either exclude this item from moving or unmove items with long titles
exclude this item from moving
Open shell.nss and find this item in static section
item(where=this.title.length > 25 menu=title.more_options)
item(find="dell display" vis=0)
Change it to
item(where=(this.title.length > 25 && !str.start(this.title, "windows terminal here")) menu=title.more_options)
unmove items with long titles,
Add // at the front of the line to ignore this command
// item(where=)this.title.length > 25( menu=title.more_options)
❤️
1
Comment options
edited
PitchAbyss
on Jan 27, 2023Jan 27, 2023
Hi @ifelixculpa
can you tell me how can i move this out side of more section
Moved this item because items longer than 25 characters have been migrated to another "More options" submenu.
You have two options, either exclude this item from moving or unmove items with long titles
exclude this item from moving
Open shell.nss and find this item in static section
item(where=this.title.length > 25 menu=title.more_options)
item(find="dell display" vis=0)
Change it to
item(where=(this.title.length > 25 && !str.start(this.title, "windows terminal here")) menu=title.more_options)
unmove items with long titles,
Add // at the front of the line to ignore this command
// item(where=)this.title.length > 25( menu=title.more_options)
Thanks amillion sir just last question is this sir , you sorted everything else nicely sir
thanks sir please can u post command to add program or script i have kept in drive d:/mystuff/scripts/mydata.bat
👍
1
Comment options
moudey
on Jan 28, 2023Jan 27, 2023
Maintainer
can u post command to add program or script i have kept in drive d:/mystuff/scripts/mydata.bat
dynamic
{
    // ...
    item(title='run myscript' cmd='d:/mystuff/scripts/mydata.bat')
    // ....
}
❤️
1
Comment options
PitchAbyss
on Jan 28, 2023Jan 28, 2023
can u post command to add program or script i have kept in drive d:/mystuff/scripts/mydata.bat
dynamic
{
    // ...
    item(title='run myscript' cmd='d:/mystuff/scripts/mydata.bat')
    // ....
}
thankyou soooooo much sir

---

Comment options
PitchAbyss
on Jan 25, 2023Jan 25, 2023
@moudey sir

---

Comment options
moudey
on Jan 27, 2023Jan 27, 2023
Maintainer
Hi @ifelixculpa
can you tell me how can i move this out side of more section
Moved this item because items longer than 25 characters have been migrated to another "More options" submenu.
You have two options, either exclude this item from moving or unmove items with long titles
exclude this item from moving
Open shell.nss and find this item in static section
item(where=this.title.length > 25 menu=title.more_options)
item(find="dell display" vis=0)
Change it to
item(where=(this.title.length > 25 && !str.start(this.title, "windows terminal here")) menu=title.more_options)
unmove items with long titles,
Add // at the front of the line to ignore this command
// item(where=)this.title.length > 25( menu=title.more_options)
❤️
1

---

Comment options
edited
PitchAbyss
on Jan 27, 2023Jan 27, 2023
Hi @ifelixculpa
can you tell me how can i move this out side of more section
Moved this item because items longer than 25 characters have been migrated to another "More options" submenu.
You have two options, either exclude this item from moving or unmove items with long titles
exclude this item from moving
Open shell.nss and find this item in static section
item(where=this.title.length > 25 menu=title.more_options)
item(find="dell display" vis=0)
Change it to
item(where=(this.title.length > 25 && !str.start(this.title, "windows terminal here")) menu=title.more_options)
unmove items with long titles,
Add // at the front of the line to ignore this command
// item(where=)this.title.length > 25( menu=title.more_options)
Thanks amillion sir just last question is this sir , you sorted everything else nicely sir
thanks sir please can u post command to add program or script i have kept in drive d:/mystuff/scripts/mydata.bat
👍
1

---

Comment options
moudey
on Jan 28, 2023Jan 27, 2023
Maintainer
can u post command to add program or script i have kept in drive d:/mystuff/scripts/mydata.bat
dynamic
{
    // ...
    item(title='run myscript' cmd='d:/mystuff/scripts/mydata.bat')
    // ....
}
❤️
1

---

Comment options
PitchAbyss
on Jan 28, 2023Jan 28, 2023
can u post command to add program or script i have kept in drive d:/mystuff/scripts/mydata.bat
dynamic
{
    // ...
    item(title='run myscript' cmd='d:/mystuff/scripts/mydata.bat')
    // ....
}
thankyou soooooo much sir

---

Comment options
PitchAbyss
on Jan 28, 2023Jan 28, 2023
sir i added the script which runs fine when i double click it but when i add that to the right click shell extension nothing happens
1
You must be logged in to vote
2 replies
Comment options
moudey
on Jan 29, 2023Jan 29, 2023
Maintainer
sir i added the script which runs fine when i double click it but when i add that to the right click shell extension nothing happens
The code should work. Is the path of the patch file correct?
To keep the patch window from closing
item(title='run myscript' cmd args='/k d:/mystuff/scripts/mydata.bat')
❤️
1
Comment options
PitchAbyss
on Jan 29, 2023Jan 29, 2023
item(title='run myscript' cmd args='/k d:/mystuff/scripts/mydata.bat')
this one worked like a charm , thanks a million for non stop help thanks alot sir
👍
1

---

Comment options
moudey
on Jan 29, 2023Jan 29, 2023
Maintainer
sir i added the script which runs fine when i double click it but when i add that to the right click shell extension nothing happens
The code should work. Is the path of the patch file correct?
To keep the patch window from closing
item(title='run myscript' cmd args='/k d:/mystuff/scripts/mydata.bat')
❤️
1

---

Comment options
PitchAbyss
on Jan 29, 2023Jan 29, 2023
item(title='run myscript' cmd args='/k d:/mystuff/scripts/mydata.bat')
this one worked like a charm , thanks a million for non stop help thanks alot sir
👍
1
--------------------------------------------------------------------------------
Discussion 64: 
Link: https://github.com/moudey/Shell/discussions/111
Discussion options
edited
krystofkrticka
on Jan 17, 2023Jan 17, 2023
Hello, today I installed shell but I'm unable to see all context menu entries because my context menu is too long. So scrolling context menu or something like windows has by default when you context menu won't fit on your screen would be useful.
Some of icons where pushed of to more options section but some disappeared completely.
This is how long context menu looks with shell enabled: This is how long context menu looks when just restored and tweaked by StartAllBack:
1
You must be logged in to vote

---

Comment options
moudey
on Jan 17, 2023Jan 17, 2023
Maintainer
it's too too too long,
You can shorten it by moving items to a submenu, for example
add this code to static section
item (find='winamp|git|insync|powerrename|sign and encrypt' menu="more options")
Or you can long press in an empty place below the context menu and it will scroll the items.
1
You must be logged in to vote
3 replies
Comment options
ghost
on Jan 29, 2023Jan 29, 2023
This seems to completely remove those items in multiple items context menu.
For example if you select two files and open context menu, the Power Rename is completely gone, it doesn't appear in more options.
Comment options
moudey
on Jan 29, 2023Jan 29, 2023
Maintainer
This seems to completely remove those items in multiple items context menu.
For example if you select two files and open context menu, the Power Rename is completely gone, it doesn't appear in more options.
add mode="multiple" property
item (mode="multiple" find='winamp|git|insync|powerrename|sign and encrypt' menu="more options")
Comment options
ghost
on Jan 29, 2023Jan 29, 2023

---

Comment options
ghost
on Jan 29, 2023Jan 29, 2023
This seems to completely remove those items in multiple items context menu.
For example if you select two files and open context menu, the Power Rename is completely gone, it doesn't appear in more options.

---

Comment options
moudey
on Jan 29, 2023Jan 29, 2023
Maintainer
This seems to completely remove those items in multiple items context menu.
For example if you select two files and open context menu, the Power Rename is completely gone, it doesn't appear in more options.
add mode="multiple" property
item (mode="multiple" find='winamp|git|insync|powerrename|sign and encrypt' menu="more options")

---

Comment options
ghost
on Jan 29, 2023Jan 29, 2023

---

Comment options
edited
krystofkrticka
on Jan 17, 2023Jan 17, 2023
Author
@moudey Thank you! I thought that there would be a way but without updated docs there is not much I can do.
1
You must be logged in to vote
0 replies
--------------------------------------------------------------------------------
Discussion 65: 
Link: https://github.com/moudey/Shell/discussions/132
Discussion options
bigplayer-ai
on Jan 24, 2023Jan 24, 2023
Hey Sometimes I get "keep on device" in other option cascade menu. I want to change it to be outside by default.
Is there an option to order/sort the the context menu items?
1
You must be logged in to vote

---

Comment options
moudey
on Jan 24, 2023Jan 24, 2023
Maintainer
Moving child items to parent items is not currently supported.
Sorting is only available manually at the moment as well.
1
You must be logged in to vote
0 replies

---

Comment options
bigplayer-ai
on Jan 25, 2023Jan 25, 2023
Author
How can I change it manually?
1
You must be logged in to vote
0 replies

---

Comment options
moudey
on Jan 25, 2023Jan 25, 2023
Maintainer
static
{
    item(find='"copy"'   pos=0) 
    item(find='"cut"'    pos=1) 
    item(find='"delete"' pos=2) 
    ...
}
1
You must be logged in to vote
2 replies
Comment options
bigplayer-ai
on Jan 25, 2023Jan 25, 2023
Author
But I can't move child items to parent items this way?
Comment options
edited
moudey
on Jan 28, 2023Jan 28, 2023
Maintainer
This option not supported yet, It is only possible to move items to submenus

---

Comment options
bigplayer-ai
on Jan 25, 2023Jan 25, 2023
Author
But I can't move child items to parent items this way?

---

Comment options
edited
moudey
on Jan 28, 2023Jan 28, 2023
Maintainer
This option not supported yet, It is only possible to move items to submenus
--------------------------------------------------------------------------------
Discussion 66: 
Link: https://github.com/moudey/Shell/discussions/131
Discussion options
hiranokite
on Jan 25, 2023Jan 25, 2023
Sponsor
Im porting my old config to the new release, but i can't figure out how to set the glyph icons colors.
on the older 1.8 build, the syntax used was this
glyph=[color.??, color.??]
but on the new buid, if i save this line inside theme { } the shell won't load anymore.
1
You must be logged in to vote
Answered by moudey
on Jan 25, 2023Jan 25, 2023
shell
{
    set
    {
        theme.image
        {
     color = [#f00, #0f0]
     // or
     color.color1 = #f00
     color.color2 = #0f0
        }
    }
}
View full answer

---

Comment options
moudey
on Jan 25, 2023Jan 25, 2023
Maintainer
shell
{
    set
    {
        theme.image
        {
     color = [#f00, #0f0]
     // or
     color.color1 = #f00
     color.color2 = #0f0
        }
    }
}
Please refer to this doc
Marked as answer
2
You must be logged in to vote
❤️
1
1 reply
Comment options
hiranokite
on Jan 25, 2023Jan 25, 2023
Author
Sponsor
Thank you, worked perfectly. I'm now using the document to finish all the adjustments.
Answer selected by hiranokite

---

Comment options
hiranokite
on Jan 25, 2023Jan 25, 2023
Author
Sponsor
Thank you, worked perfectly. I'm now using the document to finish all the adjustments.
--------------------------------------------------------------------------------
Discussion 67: 
Link: https://github.com/moudey/Shell/discussions/73
Discussion options
4NXIE7Y
on Dec 2, 2022Dec 2, 2022
I can't seem to find the option to format removable storage devices...
1
You must be logged in to vote

---

Comment options
moudey
on Dec 2, 2022Dec 2, 2022
Maintainer
Press Shift + Right-Click and it will appear
1
You must be logged in to vote
0 replies

---

Comment options
Natejoestev
on Jan 18, 2023Jan 18, 2023
is there a way to make it appear in the non-shift click menu?
(what code does it take to add that option?)
1
You must be logged in to vote
1 reply
Comment options
moudey
on Jan 18, 2023Jan 18, 2023
Maintainer
This is in version 1.7 and has been moved to more options menu in 1.8

---

Comment options
moudey
on Jan 18, 2023Jan 18, 2023
Maintainer
This is in version 1.7 and has been moved to more options menu in 1.8
--------------------------------------------------------------------------------
Discussion 68: 
Link: https://github.com/moudey/Shell/discussions/112
Discussion options
edited
coinkillerl
on Jan 11, 2023Jan 11, 2023
I can't figure out how to do this. What i want to do, is basically replace the "Create Shortcut" item with a dynamic menu, and then place the "Create Shortcut" item into the menu, along with a few more options. The menu needs to be located where the "Create Shortcut" item is normallu located.
It should look something like this :
...
-------
Create Shortcut >sub0 Normal (performs "create shortcut" command)
                >sub1 Symbolic (custom cmd)
                >sub2 Hard (custom cmd)
Delete
Rename
-------
...
is this possible on 1.8?
1
You must be logged in to vote

---

Comment options
moudey
on Jan 11, 2023Jan 11, 2023
Maintainer
You can customize it easily see the following example:
    static
    {
        item(where=this.id==id.create_shortcut pos=0 sep="bottom" title="Normal" menu=id.create_shortcut.title)
    }

    dynamic
    {
        menu(type='file|dir' pos=indexof(id.cut.title) title=id.create_shortcut.title sep="both" image=icon.create_shortcut)
        {
            item(title="Symbolic")
            item(title="Hard")
        }
    }
Download this build and try it
1
You must be logged in to vote
0 replies

---

Comment options
coinkillerl
on Jan 11, 2023Jan 11, 2023
Author
Thanks, i have 3 other questions :
Is there any way to repeat a command for each selected item when multiple items are selected?
Is there any way to make a glyph use the accent color instead of being just white?
Will some various missing icons (like open, open in new tab, etc.) be added?
1
You must be logged in to vote
0 replies

---

Comment options
edited
moudey
on Jan 12, 2023Jan 11, 2023
Maintainer
Is there any way to repeat a command for each selected item when multiple items are selected?
Add a foreach property to the item with the value 1 or true
Is there any way to make a glyph use the accent color instead of being just white?
image=[\ue008, #0f0] or The color can be changed from the general settings
shell  {
    set  {
        theme  {
            image.color=[#f00, #0f0]
        }
    }
}
Will some various missing icons (like open, open in new tab, etc.) be added?
Please provide me with a screenshot of this issue.
1
You must be logged in to vote
0 replies

---

Comment options
coinkillerl
on Jan 12, 2023Jan 12, 2023
Author
Thanks! The foreach=true method worked perfectly, i couldn't find in in the docs, is this a new 1.8 beta feature?
About the image.color property, setting it prevents shell from opening on beta 1.8, does that only work for the build you just sent?
About the missing icons, Open, Open in new tab, Edit, Undo and Redo icon are missing


trying to set them manually with Image=icon.x will not work and they'll still be blank, you must use a glyph or an external resource, however, glyphs if set like this (Image=[\uXXXX]) will only be monochrome, and the accent color does not get applied, unlike the icon.x ones.
1
You must be logged in to vote
0 replies

---

Comment options
moudey
on Jan 12, 2023Jan 12, 2023
Maintainer
The latest version made some changes to it, so you will find it somewhat different from the previous version, especially in the settings section
1
You must be logged in to vote
0 replies
--------------------------------------------------------------------------------
Discussion 69: 
Link: https://github.com/moudey/Shell/discussions/30
Discussion options
moudey
on Aug 27, 2022Aug 27, 2022
Maintainer


6
You must be logged in to vote
🎉
2
❤️
1

---

Comment options
HuyHung1408
on Aug 27, 2022Aug 27, 2022
cool, good job!!
2
You must be logged in to vote
👍
1
2 replies
Comment options
CyCoSyS
on Dec 24, 2022Dec 24, 2022
I'm really enjoying your Shell app. Getting my Context menu just right. Wasted 9 hours on a snowy storm day, and had a wonderful time putzing around and experimenting. Great work!
❤️
1
Comment options
moudey
on Dec 25, 2022Dec 25, 2022
Maintainer
Author
I'm really enjoying your Shell app. Getting my Context menu just right. Wasted 9 hours on a snowy storm day, and had a wonderful time putzing around and experimenting. Great work!
Thank you. Stay tuned for the next update as it will introduce many new features and improvements.

---

Comment options
CyCoSyS
on Dec 24, 2022Dec 24, 2022
I'm really enjoying your Shell app. Getting my Context menu just right. Wasted 9 hours on a snowy storm day, and had a wonderful time putzing around and experimenting. Great work!
❤️
1

---

Comment options
moudey
on Dec 25, 2022Dec 25, 2022
Maintainer
Author
I'm really enjoying your Shell app. Getting my Context menu just right. Wasted 9 hours on a snowy storm day, and had a wonderful time putzing around and experimenting. Great work!
Thank you. Stay tuned for the next update as it will introduce many new features and improvements.

---

Comment options
Jai-JAP
on Aug 27, 2022Aug 27, 2022
Does this also include mica effect?
1
You must be logged in to vote
👍
1
0 replies

---

Comment options
moudey
on Aug 30, 2022Aug 30, 2022
Maintainer
Author
Hi all, Acrylic and Blur effects added
Acrylic effect
Blur effect
2
You must be logged in to vote
❤️
2
0 replies
--------------------------------------------------------------------------------
Discussion 70: 
Link: https://github.com/moudey/Shell/discussions/115
Discussion options
XevianDT
on Dec 19, 2022Dec 19, 2022
Hi, already was to able to add some third apps with Shell and works flawlessly, but with several files selected, items in new context menu doesn't work, just won't open. Any advice in the code?
SS in example
Thanks in advance
1
You must be logged in to vote

---

Comment options
edited
moudey
on Dec 19, 2022Dec 19, 2022
Maintainer
Set mode property to multiple
item(mode=multiple title='item 1')

item(type='file' mode=multiple title='item 1')
For more on the values of mode property of this link
1
You must be logged in to vote
0 replies

---

Comment options
XevianDT
on Dec 21, 2022Dec 21, 2022
Author
Thanks, changes made and shell now open submenu, but when i select several files, opens only the highlighted one
1
You must be logged in to vote
0 replies

---

Comment options
moudey
on Dec 24, 2022Dec 24, 2022
Maintainer
Use @sel function to fetch all selected files
item(type='file' mode=multiple title='item 1' cmd='app.exe' args='@sel')
1
You must be logged in to vote
0 replies
--------------------------------------------------------------------------------
Discussion 71: 
Link: https://github.com/moudey/Shell/discussions/92
Discussion options
edited
HelderRocket
on Dec 19, 2022Dec 19, 2022
I find it a very good tool, but I would like to be able to change the size of the taskbar context menu, as it feels really small.
1
You must be logged in to vote

---

Comment options
moudey
on Dec 19, 2022Dec 19, 2022
Maintainer
Add this code to static section in shell.shl file. Where he makes an amendment to the name whose letters exceed 30 characters.
item(where=@(this.title.len > 30) title=@(str.sub(this.title,0,30) + "..."))
1
You must be logged in to vote
0 replies
--------------------------------------------------------------------------------
Discussion 72: 
Link: https://github.com/moudey/Shell/discussions/74
Discussion options
Natejoestev
on Dec 3, 2022Dec 3, 2022
It would be nice if Nilesoft had a discord server.
Discord has a very dynamic user experience for discussion and development.
1
You must be logged in to vote

---

Comment options
Natejoestev
on Dec 10, 2022Dec 10, 2022
Author
I want to chat more about this app and contributing with it. but for me it would help if i could just dm on discord or something.
1
You must be logged in to vote
0 replies

---

Comment options
moudey
on Dec 12, 2022Dec 12, 2022
Maintainer
Hi @Natejoestev, I will try to provide that as soon as possible
1
You must be logged in to vote
0 replies
--------------------------------------------------------------------------------
Discussion 73: 
Link: https://github.com/moudey/Shell/discussions/114
Discussion options
XxnittanixX
on Dec 8, 2022Dec 8, 2022
i dont like defualt things
1
You must be logged in to vote

---

Comment options
moudey
on Dec 9, 2022Dec 8, 2022
Maintainer
The options are provided with the ability to disable them with a value of false or 0, see the following example:
default
{
    theme
    {
        border.enabled=false
        shadow.enabled=false
        item.radius=0
    }
    static=false
    dynamic=false
    tip.enabled=false
}
Or you can delete default section.
1
You must be logged in to vote
0 replies
--------------------------------------------------------------------------------
Discussion 74: 
Link: https://github.com/moudey/Shell/discussions/80
Discussion options
edited
ThePython10110
on Dec 5, 2022Dec 5, 2022
Shell currently does not apply any of the static/dynamic customizations to other applications' context menus. It only applies the theme. I tested this with Tablacus Explorer and Explorer++.
Windows Explorer works as expected (with my custom menus and a couple of moved items)

Tablacus doesn't have that; just the theme (although my screenshot makes it hard to tell). "Open in new tab" and "Open in background" are part of Tablacus.
1
You must be logged in to vote

---

Comment options
moudey
on Dec 5, 2022Dec 5, 2022
Maintainer
Hi @ThePython10110 Please test this build
Set * in the type property to show the item in all menus
dynamic
{
    item(type="*" title='hello from any menu' image=#f00)
    item(type="*" where=window.is_start title='hello from Winx menu' image=#00f)
    item(title='hello from normal menu' image=#0f0)
}
1
You must be logged in to vote
0 replies

---

Comment options
ThePython10110
on Dec 6, 2022Dec 5, 2022
Author
Now it applies dynamic customization, but nothing static.
1
You must be logged in to vote
0 replies

---

Comment options
edited
moudey
on Dec 6, 2022Dec 5, 2022
Maintainer
static
{
    item(type='taskbar' find='search' vis=disabled)
    // or
    item(type='*' where=window.is_taskbar find='search' vis=disabled)

    item(type='*' where=window.is_start find='search' vis=disabled)
}
1
You must be logged in to vote
0 replies

---

Comment options
edited
ThePython10110
on Dec 6, 2022Dec 6, 2022
Author
Some things do work, but changing the parent apparently doesn't. I'm trying to make Git Bash appear under "Terminal," as well as hiding disabled items. It works great in Explorer, but not in Tablacus.
item(type='*' find='Git Bash here|Powershell' parent='Terminal')
menu(type='taskbar' title='Terminall' image=\uE0D6)
  {
   item(title='CMD' icon='cmd.exe' admin=@key(key.shift) cmd='cmd.exe')
  }
Explorer:

Tablacus:
1
You must be logged in to vote
0 replies

---

Comment options
moudey
on Dec 6, 2022Dec 6, 2022
Maintainer
static
{
    item(type='*' find='Git Bash here|Powershell' parent='Terminal')
}

dynamic
{
    menu(type='*' title='Terminal' image=\uE0D6)
    {
        item(title='CMD' admin=key.shift() image cmd='cmd.exe')
    }
}
1
You must be logged in to vote
0 replies

---

Comment options
moudey
on Dec 6, 2022Dec 6, 2022
Maintainer
To remove all disabled items
static
{
    item(type='*' where=this.disabled vis=remove)
}
1
You must be logged in to vote
0 replies
--------------------------------------------------------------------------------
Discussion 75: 
Link: https://github.com/moudey/Shell/discussions/59
Discussion options
deepanshpandey
on Nov 25, 2022Nov 25, 2022
I just wanted to make shell a bit smaller because in my case shell covers a huge vertical space and I want to reduce that if it is possible.
1
You must be logged in to vote
Answered by moudey
on Nov 26, 2022Nov 26, 2022
Hi @deepanshpandey With these options, you can reduce the size of the context menu
font You can make the font smaller or larger, which will change the size of the context menu
item.margin and item.padding You can modify the values of these two options to adjust the size of the height or width of the items
View full answer

---

Comment options
edited
moudey
on Nov 26, 2022Nov 26, 2022
Maintainer
Hi @deepanshpandey With these options, you can reduce the size of the context menu
font You can make the font smaller or larger, which will change the size of the context menu
item.margin and item.padding You can modify the values of these two options to adjust the size of the height or width of the items
// version 1.8
default
{
    theme
    {
        font.size=12
    
        item
        {

            padding.top=0
            padding.bottom=0

            margin.top=0
            margin.bottom=0
        }
    }
}
Marked as answer
2
You must be logged in to vote
1 reply
Comment options
deepanshpandey
on Nov 26, 2022Nov 26, 2022
Author
thank you so much sir
Answer selected by moudey

---

Comment options
deepanshpandey
on Nov 26, 2022Nov 26, 2022
Author
thank you so much sir
--------------------------------------------------------------------------------
Discussion 76: 
Link: https://github.com/moudey/Shell/discussions/55
Discussion options
fnfontana
on Nov 15, 2022Nov 15, 2022
I've seen on the documentation that syntax examples are highlighted, but on VS Code I couldn't find any language that matches it.
How can I enable it?
1
You must be logged in to vote

---

Comment options
moudey
on Nov 15, 2022Nov 15, 2022
Maintainer
Please refer to this topic
1
You must be logged in to vote
0 replies
--------------------------------------------------------------------------------
Discussion 77: 
Link: https://github.com/moudey/Shell/discussions/82
Discussion options
nicholas-ochoa
on Nov 2, 2022Nov 2, 2022
Is there a syntax highlighter definition for use in any editors? Ideally Sublime Text, or even VS Code. Is the configuration syntax based on any existing language?
I've been able to use a "generic config" syntax in sublime to add some basic highlighting but it's definitely not perfect.
1
You must be logged in to vote

---

Comment options
moudey
on Nov 2, 2022Nov 2, 2022
Maintainer
The structure of config file and expressions is very simple and somewhat similar to the languages of C/C++, C#, Java, Javascript.
1
You must be logged in to vote
0 replies
--------------------------------------------------------------------------------
Discussion 78: 
Link: https://github.com/moudey/Shell/discussions/83
Discussion options
edited
NVHT
on Oct 24, 2022Oct 24, 2022
Is there a way to hide the entry with the tick?
1
You must be logged in to vote

---

Comment options
moudey
on Oct 24, 2022Oct 24, 2022
Maintainer
static
{
    item(find='convert to adobe pdf'
         where=this.checked 
         vis=remove)
}
1
You must be logged in to vote
👍
1
0 replies

---

Comment options
NVHT
on Oct 26, 2022Oct 26, 2022
Author

Also how can I replicate this from the default taskbar menu?
1
You must be logged in to vote
0 replies

---

Comment options
moudey
on Oct 26, 2022Oct 26, 2022
Maintainer
What is the Windows version number you are using?
1
You must be logged in to vote
0 replies

---

Comment options
NVHT
on Oct 27, 2022Oct 27, 2022
Author
What is the Windows version number you are using?
Win 11, Shell 1.7
1
You must be logged in to vote
0 replies

---

Comment options
moudey
on Oct 27, 2022Oct 27, 2022
Maintainer
Looks like you got your old taskbar back. If so what program did you use.
Please try beta version 1.8
1
You must be logged in to vote
0 replies

---

Comment options
NVHT
on Oct 28, 2022Oct 28, 2022
Author
Looks like you got your old taskbar back. If so what program did you use. Please try beta version 1.8
No, that's not the screenshot I took from my computer. It is just reference on how to make the taskbar menu to look like that.
1
You must be logged in to vote
0 replies
--------------------------------------------------------------------------------
Discussion 79: 
Link: https://github.com/moudey/Shell/discussions/116
Discussion options
ooTruffle
on Sep 4, 2022Sep 4, 2022
When shell registers itself it appears on some 3 party apps
On Openshell it adds options that dont need to be there

is there any way to modify this menu / remove certain enteries
1
You must be logged in to vote

---

Comment options
moudey
on Sep 4, 2022Sep 4, 2022
Maintainer
Most Explorer context menus are handled by Shell with editing and others by redrawing only. But a new option will be added to allow editing other menus that appear through the Explorer or a third party loaded by it.
1
You must be logged in to vote
0 replies
--------------------------------------------------------------------------------
Discussion 80: 
Link: https://github.com/moudey/Shell/discussions/13
Discussion options
RafaelLorenzoni
on May 31, 2022May 31, 2022
When downloaded or when i Register in the system, the Microsoft Security report this Shell as a trojan.
Trojan:Script/Wacatac.B!ml
1
You must be logged in to vote

---

Comment options
moudey
on Jun 1, 2022May 31, 2022
Maintainer
When downloaded or when i Register in the system, the Microsoft Security report this Shell as a trojan.
Trojan:Script/Wacatac.B!ml
Shell does not contain any viruses or malware, and It was checked by virustotal.com and hybrid-analysis.com. mostly this is a false positive detection
virustotal.com result
shell.exe
shell.dll
hybrid-analysis.com result
shell.exe
shell.dll
1
You must be logged in to vote
0 replies

---

Comment options
moudey
on Aug 27, 2022Aug 27, 2022
Maintainer
Please report Shell as safe if possible
1
You must be logged in to vote
0 replies

---

Comment options
win98se
on Aug 29, 2022Aug 29, 2022
@RafaelLorenzoni @moudey Both of you can try to submit the program for clarification as a home customer and a software developer respectively at https://www.microsoft.com/en-us/wdsi/filesubmission.
Macrohard shall not mark it as a trojan anymore, as it is a false positive and does not harm Windows in any way.
1
You must be logged in to vote
👍
1
0 replies

---

Comment options
edited
moudey
on Aug 29, 2022Aug 29, 2022
Maintainer
Result in Microsoft review No malware was detected "setup.exe" :)
2
You must be logged in to vote
0 replies

---

Comment options
moudey
on Aug 29, 2022Aug 29, 2022
Maintainer
Result in Microsoft review No malware was detected "shell.exe, shell.dll" :)
2
You must be logged in to vote
0 replies
--------------------------------------------------------------------------------
