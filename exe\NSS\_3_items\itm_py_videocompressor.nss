
//
$PY_VIDEOCOMPRESSOR_DIR = '@user.desktop\my\flow\home\__GOTO__\Scripts\Python\Py_Cli_Utils\py__VideoCompressor'
$PY_VIDEOCOMPRESSOR_EXE = '@PY_VIDEOCOMPRESSOR_DIR\venv\Scripts\python.exe'
$PY_VIDEOCOMPRESSOR_APP = '@PY_VIDEOCOMPRESSOR_DIR\main.py'
//
$BATCH_SEL_AS_STRING = for(i=0, i< sel.count, '"@sel[i]" ')

// Context: Explorer
$PY_VIDEOCOMPRESSOR_EXPLORER = '-i @BATCH_SEL_AS_STRING --prompt'
item(
    title="&VideoCompressor"
    keys="py"
    type='File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    //
    image=[E17C,GREY] image-sel=[E17C,PURPLE]
    tip=['"@PY_VIDEOCOMPRESSOR_APP" @PY_VIDEOCOMPRESSOR_EXPLORER',TIP3,0.75]
    //
    admin=keys.rbutton()
    cmd=if(KEYS_EXE_OPEN_EXE,('"@PY_VIDEOCOMPRESSOR_EXE"'))
    args='"@PY_VIDEOCOMPRESSOR_APP" @PY_VIDEOCOMPRESSOR_EXPLORER'
    //
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@PY_VIDEOCOMPRESSOR_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@PY_VIDEOCOMPRESSOR_APP')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@PY_VIDEOCOMPRESSOR_DIR')),
    }
)
// Context: Taskbar
$PY_VIDEOCOMPRESSOR_TASKBAR = '--prompt'
item(
    title="&VideoCompressor"
    keys="py"
    type='Taskbar'
    //
    image=[E17C,GREY] image-sel=[E17C,PURPLE]
    tip=['"@PY_VIDEOCOMPRESSOR_APP" @PY_VIDEOCOMPRESSOR_TASKBAR',TIP3,0.75]
    //
    admin=keys.rbutton()
    args='"@PY_VIDEOCOMPRESSOR_APP" @PY_VIDEOCOMPRESSOR_TASKBAR'
    cmd=if(KEYS_EXE_OPEN_EXE,('"@PY_VIDEOCOMPRESSOR_EXE"'))
    //
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@PY_VIDEOCOMPRESSOR_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@PY_VIDEOCOMPRESSOR_APP')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@PY_VIDEOCOMPRESSOR_DIR')),
    }
)
