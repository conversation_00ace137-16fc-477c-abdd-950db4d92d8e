$default_icon_size=20

menu(type='taskbar'  pos=0 title='NS Shell Icons' separator="None" image=icon.search)
{
    menu(title='NS Images')
    {
        item(title=' ' image=icon.copy tip=['Copy'] cmd=command.copy('image=icon.copy'))
        item(title=' ' image=icon.cut tip=['Cut'] cmd=command.copy('image=icon.cut'))
        item(title=' ' image=icon.paste tip=['Paste'] cmd=command.copy('image=icon.paste'))
        item(title=' ' image=icon.paste_shortcut tip=['Paste Shortcut'] cmd=command.copy('image=icon.paste_shortcut'))
        item(title=' ' image=icon.move_to tip=['Move to'] cmd=command.copy('image=icon.move_to'))
        item(title=' ' image=icon.copy_as_path tip=['Copy as Path'] cmd=command.copy('image=icon.copy_as_path'))
        item(title=' ' image=icon.settings tip=['Settings'] cmd=command.copy('image=icon.settings'))
        item(title=' ' image=icon.task_manager tip=['Task Manager'] cmd=command.copy('image=icon.task_manager'))
        item(title=' ' image=icon.run_as_administrator tip=['Run as Administrator'] cmd=command.copy('image=icon.run_as_administrator'))
        item(title=' ' image=icon.run_as_different_user tip=['Run as Different User'] cmd=command.copy('image=icon.run_as_different_user'))
        sep
        item(title=' ' tip=['Customize'] image=icon.personalize cmd=command.copy('image=icon.personalize') col)
        item(title=' ' tip=['Display Settings'] image=icon.display_settings cmd=command.copy('image=icon.display_settings'))
        item(title=' ' tip=['Pin'] image=icon.pin cmd=command.copy('image=icon.pin'))
        item(title=' ' tip=['Unpin'] image=icon.unpin cmd=command.copy('image=icon.unpin'))
        item(title=' ' tip=['Add to Favorites'] image=icon.add_to_favorites cmd=command.copy('image=icon.add_to_favorites'))
        item(title=' ' tip=['Remove from Favorites'] image=icon.remove_from_favorites cmd=command.copy('image=icon.remove_from_favorites'))
        item(title=' ' tip=['Delete'] image=icon.delete cmd=command.copy('image=icon.delete'))
        item(title=' ' tip=['Sort by'] image=icon.sort_by cmd=command.copy('image=icon.sort_by'))
        item(title=' ' tip=['Group by'] image=icon.group_by cmd=command.copy('image=icon.group_by'))
        item(title=' ' tip=['View'] image=icon.view cmd=command.copy('image=icon.view'))
        sep
        item(title=' ' tip=['View2'] image=icon.view2 cmd=command.copy('image=icon.view2') col)
        item(title=' ' tip=['Align Icons to Grid'] image=icon.align_icons_to_grid cmd=command.copy('image=icon.align_icons_to_grid'))
        item(title=' ' tip=['Auto Arrange Icons'] image=icon.auto_arrange_icons cmd=command.copy('image=icon.auto_arrange_icons'))
        item(title=' ' tip=['Close'] image=icon.close cmd=command.copy('image=icon.close'))
        item(title=' ' tip=['Expand'] image=icon.expand cmd=command.copy('image=icon.expand'))
        item(title=' ' tip=['Expand All'] image=icon.expand_all cmd=command.copy('image=icon.expand_all'))
        item(title=' ' tip=['Collapse'] image=icon.collapse cmd=command.copy('image=icon.collapse'))
        item(title=' ' tip=['Collapse All'] image=icon.collapse_all cmd=command.copy('image=icon.collapse_all'))
        item(title=' ' tip=['Format'] image=icon.format cmd=command.copy('image=icon.format'))
        item(title=' ' tip=['Eject'] image=icon.eject cmd=command.copy('image=icon.eject'))
        sep
        item(title=' ' tip=['Extra Large Icons'] image=icon.extra_large_icons cmd=command.copy('image=icon.extra_large_icons') col)
        item(title=' ' tip=['Large Icons'] image=icon.large_icons cmd=command.copy('image=icon.large_icons'))
        item(title=' ' tip=['Medium Icons'] image=icon.medium_icons cmd=command.copy('image=icon.medium_icons'))
        item(title=' ' tip=['Small Icons'] image=icon.small_icons cmd=command.copy('image=icon.small_icons'))
        item(title=' ' tip=['List'] image=icon.list cmd=command.copy('image=icon.list'))
        item(title=' ' tip=['Details'] image=icon.details cmd=command.copy('image=icon.details'))
        item(title=' ' tip=['Tiles'] image=icon.tiles cmd=command.copy('image=icon.tiles'))
        item(title=' ' tip=['Content'] image=icon.content cmd=command.copy('image=icon.content'))
        item(title=' ' tip=['Mount'] image=icon.mount cmd=command.copy('image=icon.mount'))
        item(title=' ' tip=['Install'] image=icon.install cmd=command.copy('image=icon.install'))
        sep
        item(title=' ' tip=['Select All'] image=icon.select_all cmd=command.copy('image=icon.select_all') col)
        item(title=' ' tip=['Invert Selection'] image=icon.invert_selection cmd=command.copy('image=icon.invert_selection'))
        item(title=' ' tip=['Select None'] image=icon.select_none cmd=command.copy('image=icon.select_none'))
        item(title=' ' tip=['Share'] image=icon.share cmd=command.copy('image=icon.share'))
        item(title=' ' tip=['New'] image=icon.new cmd=command.copy('image=icon.new'))
        item(title=' ' tip=['New Folder'] image=icon.new_folder cmd=command.copy('image=icon.new_folder'))
        item(title=' ' tip=['New File'] image=icon.new_file cmd=command.copy('image=icon.new_file'))
        item(title=' ' tip=['Open Folder'] image=icon.open_folder cmd=command.copy('image=icon.open_folder'))
        item(title=' ' tip=['Open in New Window'] image=icon.open_new_window cmd=command.copy('image=icon.open_new_window'))
        item(title=' ' tip=['Open in New Tab'] image=icon.open_new_tab cmd=command.copy('image=icon.open_new_tab'))
        sep
        item(title=' ' tip=['Open Spotlight'] image=icon.open_spot_light cmd=command.copy('image=icon.open_spot_light') col)
        item(title=' ' tip=['Open with'] image=icon.open_with cmd=command.copy('image=icon.open_with'))
        item(title=' ' tip=['Run with PowerShell'] image=icon.run_with_powershell cmd=command.copy('image=icon.run_with_powershell'))
        item(title=' ' tip=['Properties'] image=icon.properties cmd=command.copy('image=icon.properties'))
        item(title=' ' tip=['Restore'] image=icon.restore cmd=command.copy('image=icon.restore'))
        item(title=' ' tip=['Undo'] image=icon.undo cmd=command.copy('image=icon.undo'))
        item(title=' ' tip=['Redo'] image=icon.redo cmd=command.copy('image=icon.redo'))
        item(title=' ' tip=['Refresh'] image=icon.refresh cmd=command.copy('image=icon.refresh'))
        item(title=' ' tip=['Rename'] image=icon.rename cmd=command.copy('image=icon.rename'))
        item(title=' ' tip=['Compress'] image=icon.compressed cmd=command.copy('image=icon.compressed'))
        sep
        item(title=' ' tip=['Rotate Right'] image=icon.rotate_right cmd=command.copy('image=icon.rotate_right') col)
        item(title=' ' tip=['Rotate Left'] image=icon.rotate_left cmd=command.copy('image=icon.rotate_left'))
        item(title=' ' image=icon.set_as_desktop_wallpaper tip=['Set as Desktop Wallpaper'] cmd=command.copy('image=icon.set_as_desktop_wallpaper'))
        item(title=' ' tip=['Next Desktop Background'] image=icon.next_desktop_background cmd=command.copy('image=icon.next_desktop_background'))
        item(title=' ' tip=['Desktop'] image=icon.desktop cmd=command.copy('image=icon.desktop'))
        item(title=' ' tip=['Restore Previous Versions'] image=icon.restore_previous_versions cmd=command.copy('image=icon.restore_previous_versions'))
        item(title=' ' tip=['Create Shortcut'] image=icon.create_shortcut cmd=command.copy('image=icon.create_shortcut'))
        item(title=' ' tip=['Turn On BitLocker'] image=icon.turn_on_bitlocker cmd=command.copy('image=icon.turn_on_bitlocker'))
        item(title=' ' tip=['Show File Extensions'] image=icon.show_file_extensions cmd=command.copy('image=icon.show_file_extensions'))
        item(title=' ' tip=['Show Hidden Files'] image=icon.show_hidden_files cmd=command.copy('image=icon.show_hidden_files'))

        sep
        item(title=' ' tip=['More Options'] image=icon.more_options cmd=command.copy('image=icon.more_options') col)
        item(title=' ' tip=['Burn Disc Image'] image=icon.burn_disc_image cmd=command.copy('image=icon.burn_disc_image'))
        item(title=' ' tip=['Clean Up'] image=icon.cleanup cmd=command.copy('image=icon.cleanup'))
        item(title=' ' tip=['Move to'] image=icon.move_to cmd=command.copy('image=icon.move_to'))
        item(title=' ' tip=['Copy to'] image=icon.copy_to cmd=command.copy('image=icon.copy_to'))
        item(title=' ' tip=['This PC'] image=icon.pc cmd=command.copy('image=icon.pc'))
        item(title=' ' tip=['Command Prompt'] image=icon.command_prompt cmd=command.copy('image=icon.command_prompt'))
        item(title=' ' tip=['Manage'] image=icon.manage cmd=command.copy('image=icon.manage'))
        item(title=' ' tip=['Edit'] image=icon.edit cmd=command.copy('image=icon.edit'))
        item(title=' ' tip=['Troubleshoot Compatibility'] image=icon.troubleshoot_compatibility cmd=command.copy('image=icon.troubleshoot_compatibility'))
        sep
        item(title=' ' tip=['Customize Folder'] image=icon.customize_this_folder cmd=command.copy('image=icon.customize_this_folder') col)
        item(title=' ' tip=['Grant Access to'] image=icon.give_access_to cmd=command.copy('image=icon.give_access_to'))
        item(title=' ' tip=['Send to'] image=icon.send_to cmd=command.copy('image=icon.send_to'))
        item(title=' ' tip=['Include in Library'] image=icon.include_in_library cmd=command.copy('image=icon.include_in_library'))
        item(title=' ' tip=['Add a Network Location'] image=icon.add_a_network_location cmd=command.copy('image=icon.add_a_network_location'))
        item(title=' ' tip=['Disconnect Network Drive'] image=icon.disconnect_network_drive cmd=command.copy('image=icon.disconnect_network_drive'))
        item(title=' ' tip=['Map Network Drive'] image=icon.map_network_drive cmd=command.copy('image=icon.map_network_drive'))
        item(title=' ' tip=['Make Available Offline'] image=icon.make_available_offline cmd=command.copy('image=icon.make_available_offline'))
        item(title=' ' tip=['Make Available Online'] image=icon.make_available_online cmd=command.copy('image=icon.make_available_online'))
        item(title=' ' tip=['File Explorer'] image=icon.file_explorer cmd=command.copy('image=icon.file_explorer'))
        sep
        item(title=' ' tip=['File Explorer Options'] image=icon.file_explorer_options cmd=command.copy('image=icon.file_explorer_options') col)
        item(title=' ' tip=['Print'] image=icon.print cmd=command.copy('image=icon.print'))
        item(title=' ' tip=['Device Manager'] image=icon.device_manager cmd=command.copy('image=icon.device_manager'))
        item(title=' ' tip=['Disk Management'] image=icon.disk_management cmd=command.copy('image=icon.disk_management'))
        item(title=' ' tip=['Filter'] image=icon.filter cmd=command.copy('image=icon.filter'))
        item(title=' ' tip=['Window'] image=icon.window cmd=command.copy('image=icon.window'))
        item(title=' ' tip=['Code'] image=icon.code cmd=command.copy('image=icon.code'))
        item(title=' ' tip=['Reddit'] image=icon.reddit cmd=command.copy('image=icon.reddit'))
        item(title=' ' image=icon.cortana tip=['Cortana'] cmd=command.copy('image=icon.cortana'))
        item(title=' ' image=icon.nvidia tip=['Nvidia'] cmd=command.copy('image=icon.nvidia'))
    }
    separator

    menu(title='NS Gallery #0')
    {
        item(image=icon.glyph(\uE000,default_icon_size) tip=['E000 Not found'] cmd=command.copy('\uE000'))
        item(image=icon.glyph(\uE001,default_icon_size) tip=['E001'] cmd=command.copy('\uE001'))
        item(image=icon.glyph(\uE002,default_icon_size) tip=['E002'] cmd=command.copy('\uE002'))
        item(image=icon.glyph(\uE003,default_icon_size) tip=['E003'] cmd=command.copy('\uE003'))
        item(image=icon.glyph(\uE004,default_icon_size) tip=['E004'] cmd=command.copy('\uE004'))
        item(image=icon.glyph(\uE005,default_icon_size) tip=['E005'] cmd=command.copy('\uE005'))
        item(image=icon.glyph(\uE006,default_icon_size) tip=['E006'] cmd=command.copy('\uE006'))
        item(image=icon.glyph(\uE007,default_icon_size) tip=['E007'] cmd=command.copy('\uE007'))
        item(image=icon.glyph(\uE008,default_icon_size) tip=['E008'] cmd=command.copy('\uE008'))
        item(image=icon.glyph(\uE009,default_icon_size) tip=['E009'] cmd=command.copy('\uE009'))
        item(image=icon.glyph(\uE00A,default_icon_size) tip=['E00A'] cmd=command.copy('\uE00A'))
        item(image=icon.glyph(\uE00B,default_icon_size) tip=['E00B'] cmd=command.copy('\uE00B'))
        item(image=icon.glyph(\uE00C,default_icon_size) tip=['E00C'] cmd=command.copy('\uE00C'))
        item(image=icon.glyph(\uE00D,default_icon_size) tip=['E00D'] cmd=command.copy('\uE00D'))
        item(image=icon.glyph(\uE00E,default_icon_size) tip=['E00E'] cmd=command.copy('\uE00E'))
        item(image=icon.glyph(\uE00F,default_icon_size) tip=['E00F'] cmd=command.copy('\uE00F'))

        item(image=icon.glyph(\uE010,default_icon_size) tip=['E010'] cmd=command.copy('\uE010') col)
        item(image=icon.glyph(\uE011,default_icon_size) tip=['E011'] cmd=command.copy('\uE011'))
        item(image=icon.glyph(\uE012,default_icon_size) tip=['E012'] cmd=command.copy('\uE012'))
        item(image=icon.glyph(\uE013,default_icon_size) tip=['E013'] cmd=command.copy('\uE013'))
        item(image=icon.glyph(\uE014,default_icon_size) tip=['E014'] cmd=command.copy('\uE014'))
        item(image=icon.glyph(\uE015,default_icon_size) tip=['E015'] cmd=command.copy('\uE015'))
        item(image=icon.glyph(\uE016,default_icon_size) tip=['E016'] cmd=command.copy('\uE016'))
        item(image=icon.glyph(\uE017,default_icon_size) tip=['E017'] cmd=command.copy('\uE017'))
        item(image=icon.glyph(\uE018,default_icon_size) tip=['E018'] cmd=command.copy('\uE018'))
        item(image=icon.glyph(\uE019,default_icon_size) tip=['E019'] cmd=command.copy('\uE019'))
        item(image=icon.glyph(\uE01A,default_icon_size) tip=['E01A'] cmd=command.copy('\uE01A'))
        item(image=icon.glyph(\uE01B,default_icon_size) tip=['E01B'] cmd=command.copy('\uE01B'))
        item(image=icon.glyph(\uE01C,default_icon_size) tip=['E01C'] cmd=command.copy('\uE01C'))
        item(image=icon.glyph(\uE01D,default_icon_size) tip=['E01D'] cmd=command.copy('\uE01D'))
        item(image=icon.glyph(\uE01E,default_icon_size) tip=['E01E'] cmd=command.copy('\uE01E'))
        item(image=icon.glyph(\uE01F,default_icon_size) tip=['E01F'] cmd=command.copy('\uE01F'))

        item(image=icon.glyph(\uE020,default_icon_size) tip=['E020'] cmd=command.copy('\uE020') col)
        item(image=icon.glyph(\uE021,default_icon_size) tip=['E021'] cmd=command.copy('\uE021'))
        item(image=icon.glyph(\uE022,default_icon_size) tip=['E022'] cmd=command.copy('\uE022'))
        item(image=icon.glyph(\uE023,default_icon_size) tip=['E023'] cmd=command.copy('\uE023'))
        item(image=icon.glyph(\uE024,default_icon_size) tip=['E024'] cmd=command.copy('\uE024'))
        item(image=icon.glyph(\uE025,default_icon_size) tip=['E025'] cmd=command.copy('\uE025'))
        item(image=icon.glyph(\uE026,default_icon_size) tip=['E026'] cmd=command.copy('\uE026'))
        item(image=icon.glyph(\uE027,default_icon_size) tip=['E027'] cmd=command.copy('\uE027'))
        item(image=icon.glyph(\uE028,default_icon_size) tip=['E028'] cmd=command.copy('\uE028'))
        item(image=icon.glyph(\uE029,default_icon_size) tip=['E029'] cmd=command.copy('\uE029'))
        item(image=icon.glyph(\uE02A,default_icon_size) tip=['E02A'] cmd=command.copy('\uE02A'))
        item(image=icon.glyph(\uE02B,default_icon_size) tip=['E02B'] cmd=command.copy('\uE02B'))
        item(image=icon.glyph(\uE02C,default_icon_size) tip=['E02C'] cmd=command.copy('\uE02C'))
        item(image=icon.glyph(\uE02D,default_icon_size) tip=['E02D'] cmd=command.copy('\uE02D'))
        item(image=icon.glyph(\uE02E,default_icon_size) tip=['E02E'] cmd=command.copy('\uE02E'))
        item(image=icon.glyph(\uE02F,default_icon_size) tip=['E02F'] cmd=command.copy('\uE02F'))

        item(image=icon.glyph(\uE030,default_icon_size) tip=['E030'] cmd=command.copy('\uE030') col)
        item(image=icon.glyph(\uE031,default_icon_size) tip=['E031'] cmd=command.copy('\uE031'))
        item(image=icon.glyph(\uE032,default_icon_size) tip=['E032'] cmd=command.copy('\uE032'))
        item(image=icon.glyph(\uE033,default_icon_size) tip=['E033'] cmd=command.copy('\uE033'))
        item(image=icon.glyph(\uE034,default_icon_size) tip=['E034'] cmd=command.copy('\uE034'))
        item(image=icon.glyph(\uE035,default_icon_size) tip=['E035'] cmd=command.copy('\uE035'))
        item(image=icon.glyph(\uE036,default_icon_size) tip=['E036'] cmd=command.copy('\uE036'))
        item(image=icon.glyph(\uE037,default_icon_size) tip=['E037'] cmd=command.copy('\uE037'))
        item(image=icon.glyph(\uE038,default_icon_size) tip=['E038'] cmd=command.copy('\uE038'))
        item(image=icon.glyph(\uE039,default_icon_size) tip=['E039'] cmd=command.copy('\uE039'))
        item(image=icon.glyph(\uE03A,default_icon_size) tip=['E03A'] cmd=command.copy('\uE03A'))
        item(image=icon.glyph(\uE03B,default_icon_size) tip=['E03B'] cmd=command.copy('\uE03B'))
        item(image=icon.glyph(\uE03C,default_icon_size) tip=['E03C'] cmd=command.copy('\uE03C'))
        item(image=icon.glyph(\uE03D,default_icon_size) tip=['E03D'] cmd=command.copy('\uE03D'))
        item(image=icon.glyph(\uE03E,default_icon_size) tip=['E03E'] cmd=command.copy('\uE03E'))
        item(image=icon.glyph(\uE03F,default_icon_size) tip=['E03F'] cmd=command.copy('\uE03F'))

        item(image=icon.glyph(\uE040,default_icon_size) tip=['E040'] cmd=command.copy('\uE040') col)
        item(image=icon.glyph(\uE041,default_icon_size) tip=['E041'] cmd=command.copy('\uE041'))
        item(image=icon.glyph(\uE042,default_icon_size) tip=['E042'] cmd=command.copy('\uE042'))
        item(image=icon.glyph(\uE043,default_icon_size) tip=['E043'] cmd=command.copy('\uE043'))
        item(image=icon.glyph(\uE044,default_icon_size) tip=['E044'] cmd=command.copy('\uE044'))
        item(image=icon.glyph(\uE045,default_icon_size) tip=['E045'] cmd=command.copy('\uE045'))
        item(image=icon.glyph(\uE046,default_icon_size) tip=['E046'] cmd=command.copy('\uE046'))
        item(image=icon.glyph(\uE047,default_icon_size) tip=['E047'] cmd=command.copy('\uE047'))
        item(image=icon.glyph(\uE048,default_icon_size) tip=['E048'] cmd=command.copy('\uE048'))
        item(image=icon.glyph(\uE049,default_icon_size) tip=['E049'] cmd=command.copy('\uE049'))
        item(image=icon.glyph(\uE04A,default_icon_size) tip=['E04A'] cmd=command.copy('\uE04A'))
        item(image=icon.glyph(\uE04B,default_icon_size) tip=['E04B'] cmd=command.copy('\uE04B'))
        item(image=icon.glyph(\uE04C,default_icon_size) tip=['E04C'] cmd=command.copy('\uE04C'))
        item(image=icon.glyph(\uE04D,default_icon_size) tip=['E04D'] cmd=command.copy('\uE04D'))
        item(image=icon.glyph(\uE04E,default_icon_size) tip=['E04E'] cmd=command.copy('\uE04E'))
        item(image=icon.glyph(\uE04F,default_icon_size) tip=['E04F'] cmd=command.copy('\uE04F'))

        item(image=icon.glyph(\uE050,default_icon_size) tip=['E050'] cmd=command.copy('\uE050') col)
        item(image=icon.glyph(\uE051,default_icon_size) tip=['E051'] cmd=command.copy('\uE051'))
        item(image=icon.glyph(\uE052,default_icon_size) tip=['E052'] cmd=command.copy('\uE052'))
        item(image=icon.glyph(\uE053,default_icon_size) tip=['E053'] cmd=command.copy('\uE053'))
        item(image=icon.glyph(\uE054,default_icon_size) tip=['E054'] cmd=command.copy('\uE054'))
        item(image=icon.glyph(\uE055,default_icon_size) tip=['E055'] cmd=command.copy('\uE055'))
        item(image=icon.glyph(\uE056,default_icon_size) tip=['E056'] cmd=command.copy('\uE056'))
        item(image=icon.glyph(\uE057,default_icon_size) tip=['E057'] cmd=command.copy('\uE057'))
        item(image=icon.glyph(\uE058,default_icon_size) tip=['E058'] cmd=command.copy('\uE058'))
        item(image=icon.glyph(\uE059,default_icon_size) tip=['E059'] cmd=command.copy('\uE059'))
        item(image=icon.glyph(\uE05A,default_icon_size) tip=['E05A'] cmd=command.copy('\uE05A'))
        item(image=icon.glyph(\uE05B,default_icon_size) tip=['E05B'] cmd=command.copy('\uE05B'))
        item(image=icon.glyph(\uE05C,default_icon_size) tip=['E05C'] cmd=command.copy('\uE05C'))
        item(image=icon.glyph(\uE05D,default_icon_size) tip=['E05D'] cmd=command.copy('\uE05D'))
        item(image=icon.glyph(\uE05E,default_icon_size) tip=['E05E'] cmd=command.copy('\uE05E'))
        item(image=icon.glyph(\uE05F,default_icon_size) tip=['E05F'] cmd=command.copy('\uE05F'))

        item(image=icon.glyph(\uE060,default_icon_size) tip=['E060'] cmd=command.copy('\uE060') col)
        item(image=icon.glyph(\uE061,default_icon_size) tip=['E061'] cmd=command.copy('\uE061'))
        item(image=icon.glyph(\uE062,default_icon_size) tip=['E062'] cmd=command.copy('\uE062'))
        item(image=icon.glyph(\uE063,default_icon_size) tip=['E063'] cmd=command.copy('\uE063'))
        item(image=icon.glyph(\uE064,default_icon_size) tip=['E064'] cmd=command.copy('\uE064'))
        item(image=icon.glyph(\uE065,default_icon_size) tip=['E065'] cmd=command.copy('\uE065'))
        item(image=icon.glyph(\uE066,default_icon_size) tip=['E066'] cmd=command.copy('\uE066'))
        item(image=icon.glyph(\uE067,default_icon_size) tip=['E067'] cmd=command.copy('\uE067'))
        item(image=icon.glyph(\uE068,default_icon_size) tip=['E068'] cmd=command.copy('\uE068'))
        item(image=icon.glyph(\uE069,default_icon_size) tip=['E069'] cmd=command.copy('\uE069'))
        item(image=icon.glyph(\uE06A,default_icon_size) tip=['E06A'] cmd=command.copy('\uE06A'))
        item(image=icon.glyph(\uE06B,default_icon_size) tip=['E06B'] cmd=command.copy('\uE06B'))
        item(image=icon.glyph(\uE06C,default_icon_size) tip=['E06C'] cmd=command.copy('\uE06C'))
        item(image=icon.glyph(\uE06D,default_icon_size) tip=['E06D'] cmd=command.copy('\uE06D'))
        item(image=icon.glyph(\uE06E,default_icon_size) tip=['E06E'] cmd=command.copy('\uE06E'))
        item(image=icon.glyph(\uE06F,default_icon_size) tip=['E06F'] cmd=command.copy('\uE06F'))

        item(image=icon.glyph(\uE070,default_icon_size) tip=['E070'] cmd=command.copy('\uE070') col)
        item(image=icon.glyph(\uE071,default_icon_size) tip=['E071'] cmd=command.copy('\uE071'))
        item(image=icon.glyph(\uE072,default_icon_size) tip=['E072'] cmd=command.copy('\uE072'))
        item(image=icon.glyph(\uE073,default_icon_size) tip=['E073'] cmd=command.copy('\uE073'))
        item(image=icon.glyph(\uE074,default_icon_size) tip=['E074'] cmd=command.copy('\uE074'))
        item(image=icon.glyph(\uE075,default_icon_size) tip=['E075'] cmd=command.copy('\uE075'))
        item(image=icon.glyph(\uE076,default_icon_size) tip=['E076'] cmd=command.copy('\uE076'))
        item(image=icon.glyph(\uE077,default_icon_size) tip=['E077'] cmd=command.copy('\uE077'))
        item(image=icon.glyph(\uE078,default_icon_size) tip=['E078'] cmd=command.copy('\uE078'))
        item(image=icon.glyph(\uE079,default_icon_size) tip=['E079'] cmd=command.copy('\uE079'))
        item(image=icon.glyph(\uE07A,default_icon_size) tip=['E07A'] cmd=command.copy('\uE07A'))
        item(image=icon.glyph(\uE07B,default_icon_size) tip=['E07B'] cmd=command.copy('\uE07B'))
        item(image=icon.glyph(\uE07C,default_icon_size) tip=['E07C'] cmd=command.copy('\uE07C'))
        item(image=icon.glyph(\uE07D,default_icon_size) tip=['E07D'] cmd=command.copy('\uE07D'))
        item(image=icon.glyph(\uE07E,default_icon_size) tip=['E07E'] cmd=command.copy('\uE07E'))
        item(image=icon.glyph(\uE07F,default_icon_size) tip=['E07F'] cmd=command.copy('\uE07F'))

        item(image=icon.glyph(\uE080,default_icon_size) tip=['E080'] cmd=command.copy('\uE080') col)
        item(image=icon.glyph(\uE081,default_icon_size) tip=['E081'] cmd=command.copy('\uE081'))
        item(image=icon.glyph(\uE082,default_icon_size) tip=['E082'] cmd=command.copy('\uE082'))
        item(image=icon.glyph(\uE083,default_icon_size) tip=['E083'] cmd=command.copy('\uE083'))
        item(image=icon.glyph(\uE084,default_icon_size) tip=['E084'] cmd=command.copy('\uE084'))
        item(image=icon.glyph(\uE085,default_icon_size) tip=['E085'] cmd=command.copy('\uE085'))
        item(image=icon.glyph(\uE086,default_icon_size) tip=['E086'] cmd=command.copy('\uE086'))
        item(image=icon.glyph(\uE087,default_icon_size) tip=['E087'] cmd=command.copy('\uE087'))
        item(image=icon.glyph(\uE088,default_icon_size) tip=['E088'] cmd=command.copy('\uE088'))
        item(image=icon.glyph(\uE089,default_icon_size) tip=['E089'] cmd=command.copy('\uE089'))
        item(image=icon.glyph(\uE08A,default_icon_size) tip=['E08A'] cmd=command.copy('\uE08A'))
        item(image=icon.glyph(\uE08B,default_icon_size) tip=['E08B'] cmd=command.copy('\uE08B'))
        item(image=icon.glyph(\uE08C,default_icon_size) tip=['E08C'] cmd=command.copy('\uE08C'))
        item(image=icon.glyph(\uE08D,default_icon_size) tip=['E08D'] cmd=command.copy('\uE08D'))
        item(image=icon.glyph(\uE08E,default_icon_size) tip=['E08E'] cmd=command.copy('\uE08E'))
        item(image=icon.glyph(\uE08F,default_icon_size) tip=['E08F'] cmd=command.copy('\uE08F'))
    }

    menu(title='NS Gallery #1')
    {
        item(image=icon.glyph(\uE090,default_icon_size) tip=['E090'] cmd=command.copy('\uE090'))
        item(image=icon.glyph(\uE091,default_icon_size) tip=['E091'] cmd=command.copy('\uE091'))
        item(image=icon.glyph(\uE092,default_icon_size) tip=['E092'] cmd=command.copy('\uE092'))
        item(image=icon.glyph(\uE093,default_icon_size) tip=['E093'] cmd=command.copy('\uE093'))
        item(image=icon.glyph(\uE094,default_icon_size) tip=['E094'] cmd=command.copy('\uE094'))
        item(image=icon.glyph(\uE095,default_icon_size) tip=['E095'] cmd=command.copy('\uE095'))
        item(image=icon.glyph(\uE096,default_icon_size) tip=['E096'] cmd=command.copy('\uE096'))
        item(image=icon.glyph(\uE097,default_icon_size) tip=['E097'] cmd=command.copy('\uE097'))
        item(image=icon.glyph(\uE098,default_icon_size) tip=['E098'] cmd=command.copy('\uE098'))
        item(image=icon.glyph(\uE099,default_icon_size) tip=['E099'] cmd=command.copy('\uE099'))
        item(image=icon.glyph(\uE09A,default_icon_size) tip=['E09A'] cmd=command.copy('\uE09A'))
        item(image=icon.glyph(\uE09B,default_icon_size) tip=['E09B'] cmd=command.copy('\uE09B'))
        item(image=icon.glyph(\uE09C,default_icon_size) tip=['E09C'] cmd=command.copy('\uE09C'))
        item(image=icon.glyph(\uE09D,default_icon_size) tip=['E09D'] cmd=command.copy('\uE09D'))
        item(image=icon.glyph(\uE09E,default_icon_size) tip=['E09E'] cmd=command.copy('\uE09E'))
        item(image=icon.glyph(\uE09F,default_icon_size) tip=['E09F'] cmd=command.copy('\uE09F'))

        item(image=icon.glyph(\uE100,default_icon_size) tip=['E100'] cmd=command.copy('\uE100') col)
        item(image=icon.glyph(\uE101,default_icon_size) tip=['E101'] cmd=command.copy('\uE101'))
        item(image=icon.glyph(\uE102,default_icon_size) tip=['E102'] cmd=command.copy('\uE102'))
        item(image=icon.glyph(\uE103,default_icon_size) tip=['E103'] cmd=command.copy('\uE103'))
        item(image=icon.glyph(\uE104,default_icon_size) tip=['E104'] cmd=command.copy('\uE104'))
        item(image=icon.glyph(\uE105,default_icon_size) tip=['E105'] cmd=command.copy('\uE105'))
        item(image=icon.glyph(\uE106,default_icon_size) tip=['E106'] cmd=command.copy('\uE106'))
        item(image=icon.glyph(\uE107,default_icon_size) tip=['E107'] cmd=command.copy('\uE107'))
        item(image=icon.glyph(\uE108,default_icon_size) tip=['E108'] cmd=command.copy('\uE108'))
        item(image=icon.glyph(\uE109,default_icon_size) tip=['E109'] cmd=command.copy('\uE109'))
        item(image=icon.glyph(\uE10A,default_icon_size) tip=['E10A'] cmd=command.copy('\uE10A'))
        item(image=icon.glyph(\uE10B,default_icon_size) tip=['E10B'] cmd=command.copy('\uE10B'))
        item(image=icon.glyph(\uE10C,default_icon_size) tip=['E10C'] cmd=command.copy('\uE10C'))
        item(image=icon.glyph(\uE10D,default_icon_size) tip=['E10D'] cmd=command.copy('\uE10D'))
        item(image=icon.glyph(\uE10E,default_icon_size) tip=['E10E'] cmd=command.copy('\uE10E'))
        item(image=icon.glyph(\uE10F,default_icon_size) tip=['E10F'] cmd=command.copy('\uE10F'))

        item(image=icon.glyph(\uE110,default_icon_size) tip=['E110'] cmd=command.copy('\uE110') col)
        item(image=icon.glyph(\uE111,default_icon_size) tip=['E111'] cmd=command.copy('\uE111'))
        item(image=icon.glyph(\uE112,default_icon_size) tip=['E112'] cmd=command.copy('\uE112'))
        item(image=icon.glyph(\uE113,default_icon_size) tip=['E113'] cmd=command.copy('\uE113'))
        item(image=icon.glyph(\uE114,default_icon_size) tip=['E114'] cmd=command.copy('\uE114'))
        item(image=icon.glyph(\uE115,default_icon_size) tip=['E115'] cmd=command.copy('\uE115'))
        item(image=icon.glyph(\uE116,default_icon_size) tip=['E116'] cmd=command.copy('\uE116'))
        item(image=icon.glyph(\uE117,default_icon_size) tip=['E117'] cmd=command.copy('\uE117'))
        item(image=icon.glyph(\uE118,default_icon_size) tip=['E118'] cmd=command.copy('\uE118'))
        item(image=icon.glyph(\uE119,default_icon_size) tip=['E119'] cmd=command.copy('\uE119'))
        item(image=icon.glyph(\uE11A,default_icon_size) tip=['E11A'] cmd=command.copy('\uE11A'))
        item(image=icon.glyph(\uE11B,default_icon_size) tip=['E11B'] cmd=command.copy('\uE11B'))
        item(image=icon.glyph(\uE11C,default_icon_size) tip=['E11C'] cmd=command.copy('\uE11C'))
        item(image=icon.glyph(\uE11D,default_icon_size) tip=['E11D'] cmd=command.copy('\uE11D'))
        item(image=icon.glyph(\uE11E,default_icon_size) tip=['E11E'] cmd=command.copy('\uE11E'))
        item(image=icon.glyph(\uE11F,default_icon_size) tip=['E11F'] cmd=command.copy('\uE11F'))

        item(image=icon.glyph(\uE120,default_icon_size) tip=['E120'] cmd=command.copy('\uE120') col)
        item(image=icon.glyph(\uE121,default_icon_size) tip=['E121'] cmd=command.copy('\uE121'))
        item(image=icon.glyph(\uE122,default_icon_size) tip=['E122'] cmd=command.copy('\uE122'))
        item(image=icon.glyph(\uE123,default_icon_size) tip=['E123'] cmd=command.copy('\uE123'))
        item(image=icon.glyph(\uE124,default_icon_size) tip=['E124'] cmd=command.copy('\uE124'))
        item(image=icon.glyph(\uE125,default_icon_size) tip=['E125'] cmd=command.copy('\uE125'))
        item(image=icon.glyph(\uE126,default_icon_size) tip=['E126'] cmd=command.copy('\uE126'))
        item(image=icon.glyph(\uE127,default_icon_size) tip=['E127'] cmd=command.copy('\uE127'))
        item(image=icon.glyph(\uE128,default_icon_size) tip=['E128'] cmd=command.copy('\uE128'))
        item(image=icon.glyph(\uE129,default_icon_size) tip=['E129'] cmd=command.copy('\uE129'))
        item(image=icon.glyph(\uE12A,default_icon_size) tip=['E12A'] cmd=command.copy('\uE12A'))
        item(image=icon.glyph(\uE12B,default_icon_size) tip=['E12B'] cmd=command.copy('\uE12B'))
        item(image=icon.glyph(\uE12C,default_icon_size) tip=['E12C'] cmd=command.copy('\uE12C'))
        item(image=icon.glyph(\uE12D,default_icon_size) tip=['E12D'] cmd=command.copy('\uE12D'))
        item(image=icon.glyph(\uE12E,default_icon_size) tip=['E12E'] cmd=command.copy('\uE12E'))
        item(image=icon.glyph(\uE12F,default_icon_size) tip=['E12F'] cmd=command.copy('\uE12F'))

        item(image=icon.glyph(\uE130,default_icon_size) tip=['E130'] cmd=command.copy('\uE130') col)
        item(image=icon.glyph(\uE131,default_icon_size) tip=['E131'] cmd=command.copy('\uE131'))
        item(image=icon.glyph(\uE132,default_icon_size) tip=['E132'] cmd=command.copy('\uE132'))
        item(image=icon.glyph(\uE133,default_icon_size) tip=['E133'] cmd=command.copy('\uE133'))
        item(image=icon.glyph(\uE134,default_icon_size) tip=['E134'] cmd=command.copy('\uE134'))
        item(image=icon.glyph(\uE135,default_icon_size) tip=['E135'] cmd=command.copy('\uE135'))
        item(image=icon.glyph(\uE136,default_icon_size) tip=['E136'] cmd=command.copy('\uE136'))
        item(image=icon.glyph(\uE137,default_icon_size) tip=['E137'] cmd=command.copy('\uE137'))
        item(image=icon.glyph(\uE138,default_icon_size) tip=['E138'] cmd=command.copy('\uE138'))
        item(image=icon.glyph(\uE139,default_icon_size) tip=['E139'] cmd=command.copy('\uE139'))
        item(image=icon.glyph(\uE13A,default_icon_size) tip=['E13A'] cmd=command.copy('\uE13A'))
        item(image=icon.glyph(\uE13B,default_icon_size) tip=['E13B'] cmd=command.copy('\uE13B'))
        item(image=icon.glyph(\uE13C,default_icon_size) tip=['E13C'] cmd=command.copy('\uE13C'))
        item(image=icon.glyph(\uE13D,default_icon_size) tip=['E13D'] cmd=command.copy('\uE13D'))
        item(image=icon.glyph(\uE13E,default_icon_size) tip=['E13E'] cmd=command.copy('\uE13E'))
        item(image=icon.glyph(\uE13F,default_icon_size) tip=['E13F'] cmd=command.copy('\uE13F'))

        item(image=icon.glyph(\uE140,default_icon_size) tip=['E140'] cmd=command.copy('\uE140') col)
        item(image=icon.glyph(\uE141,default_icon_size) tip=['E141'] cmd=command.copy('\uE141'))
        item(image=icon.glyph(\uE142,default_icon_size) tip=['E142'] cmd=command.copy('\uE142'))
        item(image=icon.glyph(\uE143,default_icon_size) tip=['E143'] cmd=command.copy('\uE143'))
        item(image=icon.glyph(\uE144,default_icon_size) tip=['E144'] cmd=command.copy('\uE144'))
        item(image=icon.glyph(\uE145,default_icon_size) tip=['E145'] cmd=command.copy('\uE145'))
        item(image=icon.glyph(\uE146,default_icon_size) tip=['E146'] cmd=command.copy('\uE146'))
        item(image=icon.glyph(\uE147,default_icon_size) tip=['E147'] cmd=command.copy('\uE147'))
        item(image=icon.glyph(\uE148,default_icon_size) tip=['E148'] cmd=command.copy('\uE148'))
        item(image=icon.glyph(\uE149,default_icon_size) tip=['E149'] cmd=command.copy('\uE149'))
        item(image=icon.glyph(\uE14A,default_icon_size) tip=['E14A'] cmd=command.copy('\uE14A'))
        item(image=icon.glyph(\uE14B,default_icon_size) tip=['E14B'] cmd=command.copy('\uE14B'))
        item(image=icon.glyph(\uE14C,default_icon_size) tip=['E14C'] cmd=command.copy('\uE14C'))
        item(image=icon.glyph(\uE14D,default_icon_size) tip=['E14D'] cmd=command.copy('\uE14D'))
        item(image=icon.glyph(\uE14E,default_icon_size) tip=['E14E Unicode'] cmd=command.copy('\uE14E'))
        item(image=icon.glyph(\uE14F,default_icon_size) tip=['E14F'] cmd=command.copy('\uE14F'))

        item(image=icon.glyph(\uE150,default_icon_size) tip=['E150'] cmd=command.copy('\uE150') col)
        item(image=icon.glyph(\uE151,default_icon_size) tip=['E151'] cmd=command.copy('\uE151'))
        item(image=icon.glyph(\uE152,default_icon_size) tip=['E152'] cmd=command.copy('\uE152'))
        item(image=icon.glyph(\uE153,default_icon_size) tip=['E153'] cmd=command.copy('\uE153'))
        item(image=icon.glyph(\uE154,default_icon_size) tip=['E154'] cmd=command.copy('\uE154'))
        item(image=icon.glyph(\uE155,default_icon_size) tip=['E155'] cmd=command.copy('\uE155'))
        item(image=icon.glyph(\uE156,default_icon_size) tip=['E156'] cmd=command.copy('\uE156'))
        item(image=icon.glyph(\uE157,default_icon_size) tip=['E157'] cmd=command.copy('\uE157'))
        item(image=icon.glyph(\uE158,default_icon_size) tip=['E158'] cmd=command.copy('\uE158'))
        item(image=icon.glyph(\uE159,default_icon_size) tip=['E159'] cmd=command.copy('\uE159'))
        item(image=icon.glyph(\uE15A,default_icon_size) tip=['E15A'] cmd=command.copy('\uE15A'))
        item(image=icon.glyph(\uE15B,default_icon_size) tip=['E15B'] cmd=command.copy('\uE15B'))
        item(image=icon.glyph(\uE15C,default_icon_size) tip=['E15C'] cmd=command.copy('\uE15C'))
        item(image=icon.glyph(\uE15D,default_icon_size) tip=['E15D'] cmd=command.copy('\uE15D'))
        item(image=icon.glyph(\uE15E,default_icon_size) tip=['E15E'] cmd=command.copy('\uE15E'))
        item(image=icon.glyph(\uE15F,default_icon_size) tip=['E15F'] cmd=command.copy('\uE15F'))

        item(image=icon.glyph(\uE160,default_icon_size) tip=['E160'] cmd=command.copy('\uE160') col)
        item(image=icon.glyph(\uE161,default_icon_size) tip=['E161'] cmd=command.copy('\uE161'))
        item(image=icon.glyph(\uE162,default_icon_size) tip=['E162'] cmd=command.copy('\uE162'))
        item(image=icon.glyph(\uE163,default_icon_size) tip=['E163'] cmd=command.copy('\uE163'))
        item(image=icon.glyph(\uE164,default_icon_size) tip=['E164'] cmd=command.copy('\uE164'))
        item(image=icon.glyph(\uE165,default_icon_size) tip=['E165'] cmd=command.copy('\uE165'))
        item(image=icon.glyph(\uE166,default_icon_size) tip=['E166'] cmd=command.copy('\uE166'))
        item(image=icon.glyph(\uE167,default_icon_size) tip=['E167'] cmd=command.copy('\uE167'))
        item(image=icon.glyph(\uE168,default_icon_size) tip=['E168'] cmd=command.copy('\uE168'))
        item(image=icon.glyph(\uE169,default_icon_size) tip=['E169'] cmd=command.copy('\uE169'))
        item(image=icon.glyph(\uE16A,default_icon_size) tip=['E16A'] cmd=command.copy('\uE16A'))
        item(image=icon.glyph(\uE16B,default_icon_size) tip=['E16B'] cmd=command.copy('\uE16B'))
        item(image=icon.glyph(\uE16C,default_icon_size) tip=['E16C'] cmd=command.copy('\uE16C'))
        item(image=icon.glyph(\uE16D,default_icon_size) tip=['E16D'] cmd=command.copy('\uE16D'))
        item(image=icon.glyph(\uE16E,default_icon_size) tip=['E16E'] cmd=command.copy('\uE16E'))
        item(image=icon.glyph(\uE16F,default_icon_size) tip=['E16F'] cmd=command.copy('\uE16F'))

        item(image=icon.glyph(\uE170,default_icon_size) tip=['E170'] cmd=command.copy('\uE170') col)
        item(image=icon.glyph(\uE171,default_icon_size) tip=['E171'] cmd=command.copy('\uE171'))
        item(image=icon.glyph(\uE172,default_icon_size) tip=['E172'] cmd=command.copy('\uE172'))
        item(image=icon.glyph(\uE173,default_icon_size) tip=['E173'] cmd=command.copy('\uE173'))
        item(image=icon.glyph(\uE174,default_icon_size) tip=['E174'] cmd=command.copy('\uE174'))
        item(image=icon.glyph(\uE175,default_icon_size) tip=['E175'] cmd=command.copy('\uE175'))
        item(image=icon.glyph(\uE176,default_icon_size) tip=['E176'] cmd=command.copy('\uE176'))
        item(image=icon.glyph(\uE177,default_icon_size) tip=['E177'] cmd=command.copy('\uE177'))
        item(image=icon.glyph(\uE178,default_icon_size) tip=['E178'] cmd=command.copy('\uE178'))
        item(image=icon.glyph(\uE179,default_icon_size) tip=['E179'] cmd=command.copy('\uE179'))
        item(image=icon.glyph(\uE17A,default_icon_size) tip=['E17A'] cmd=command.copy('\uE17A'))
        item(image=icon.glyph(\uE17B,default_icon_size) tip=['E17B'] cmd=command.copy('\uE17B'))
        item(image=icon.glyph(\uE17C,default_icon_size) tip=['E17C'] cmd=command.copy('\uE17C'))
        item(image=icon.glyph(\uE17D,default_icon_size) tip=['E17D'] cmd=command.copy('\uE17D'))
        item(image=icon.glyph(\uE17E,default_icon_size) tip=['E17E'] cmd=command.copy('\uE17E'))
        item(image=icon.glyph(\uE17F,default_icon_size) tip=['E17F'] cmd=command.copy('\uE17F'))

        item(image=icon.glyph(\uE180,default_icon_size) tip=['E180'] cmd=command.copy('\uE180') col)
        item(image=icon.glyph(\uE181,default_icon_size) tip=['E181'] cmd=command.copy('\uE181'))
        item(image=icon.glyph(\uE182,default_icon_size) tip=['E182'] cmd=command.copy('\uE182'))
        item(image=icon.glyph(\uE183,default_icon_size) tip=['E183'] cmd=command.copy('\uE183'))
        item(image=icon.glyph(\uE184,default_icon_size) tip=['E184'] cmd=command.copy('\uE184'))
        item(image=icon.glyph(\uE185,default_icon_size) tip=['E185'] cmd=command.copy('\uE185'))
        item(image=icon.glyph(\uE186,default_icon_size) tip=['E186'] cmd=command.copy('\uE186'))
        item(image=icon.glyph(\uE187,default_icon_size) tip=['E187'] cmd=command.copy('\uE187'))
        item(image=icon.glyph(\uE188,default_icon_size) tip=['E188'] cmd=command.copy('\uE188'))
        item(image=icon.glyph(\uE189,default_icon_size) tip=['E189'] cmd=command.copy('\uE189'))
        item(image=icon.glyph(\uE18A,default_icon_size) tip=['E18A'] cmd=command.copy('\uE18A'))
        item(image=icon.glyph(\uE18B,default_icon_size) tip=['E18B'] cmd=command.copy('\uE18B'))
        item(image=icon.glyph(\uE18C,default_icon_size) tip=['E18C'] cmd=command.copy('\uE18C'))
        item(image=icon.glyph(\uE18D,default_icon_size) tip=['E18D'] cmd=command.copy('\uE18D'))
        item(image=icon.glyph(\uE18E,default_icon_size) tip=['E18E'] cmd=command.copy('\uE18E'))
        item(image=icon.glyph(\uE18F,default_icon_size) tip=['E18F'] cmd=command.copy('\uE18F'))

        item(image=icon.glyph(\uE190,default_icon_size) tip=['E190'] cmd=command.copy('\uE190') col)
        item(image=icon.glyph(\uE191,default_icon_size) tip=['E191'] cmd=command.copy('\uE191'))
        item(image=icon.glyph(\uE192,default_icon_size) tip=['E192'] cmd=command.copy('\uE192'))
        item(image=icon.glyph(\uE193,default_icon_size) tip=['E193'] cmd=command.copy('\uE193'))
        item(image=icon.glyph(\uE194,default_icon_size) tip=['E194'] cmd=command.copy('\uE194'))
        item(image=icon.glyph(\uE195,default_icon_size) tip=['E195'] cmd=command.copy('\uE195'))
        item(image=icon.glyph(\uE196,default_icon_size) tip=['E196'] cmd=command.copy('\uE196'))
        item(image=icon.glyph(\uE197,default_icon_size) tip=['E197'] cmd=command.copy('\uE197'))
        item(image=icon.glyph(\uE198,default_icon_size) tip=['E198'] cmd=command.copy('\uE198'))
        item(image=icon.glyph(\uE199,default_icon_size) tip=['E199'] cmd=command.copy('\uE199'))
        item(image=icon.glyph(\uE19A,default_icon_size) tip=['E19A'] cmd=command.copy('\uE19A'))
        item(image=icon.glyph(\uE19B,default_icon_size) tip=['E19B'] cmd=command.copy('\uE19B'))
        item(image=icon.glyph(\uE19C,default_icon_size) tip=['E19C'] cmd=command.copy('\uE19C'))
        item(image=icon.glyph(\uE19D,default_icon_size) tip=['E19D'] cmd=command.copy('\uE19D'))
        item(image=icon.glyph(\uE19E,default_icon_size) tip=['E19E'] cmd=command.copy('\uE19E'))
        item(image=icon.glyph(\uE19F,default_icon_size) tip=['E19F'] cmd=command.copy('\uE19F'))

        item(image=icon.glyph(\uE1A0,default_icon_size) tip=['E1A0'] cmd=command.copy('\uE1A0') col)
        item(image=icon.glyph(\uE1A1,default_icon_size) tip=['E1A1'] cmd=command.copy('\uE1A1'))
        item(image=icon.glyph(\uE1A2,default_icon_size) tip=['E1A2'] cmd=command.copy('\uE1A2'))
        item(image=icon.glyph(\uE1A3,default_icon_size) tip=['E1A3'] cmd=command.copy('\uE1A3'))
        item(image=icon.glyph(\uE1A4,default_icon_size) tip=['E1A4'] cmd=command.copy('\uE1A4'))
        item(image=icon.glyph(\uE1A5,default_icon_size) tip=['E1A5'] cmd=command.copy('\uE1A5'))
        item(image=icon.glyph(\uE1A6,default_icon_size) tip=['E1A6'] cmd=command.copy('\uE1A6'))
        item(image=icon.glyph(\uE1A7,default_icon_size) tip=['E1A7'] cmd=command.copy('\uE1A7'))
        item(image=icon.glyph(\uE1A8,default_icon_size) tip=['E1A8'] cmd=command.copy('\uE1A8'))
        item(image=icon.glyph(\uE1A9,default_icon_size) tip=['E1A9'] cmd=command.copy('\uE1A9'))
        item(image=icon.glyph(\uE1AA,default_icon_size) tip=['E1AA'] cmd=command.copy('\uE1AA'))
        item(image=icon.glyph(\uE1AB,default_icon_size) tip=['E1AB'] cmd=command.copy('\uE1AB'))
        item(image=icon.glyph(\uE1AC,default_icon_size) tip=['E1AC'] cmd=command.copy('\uE1AC'))
        item(image=icon.glyph(\uE1AD,default_icon_size) tip=['E1AD'] cmd=command.copy('\uE1AD'))
        item(image=icon.glyph(\uE1AE,default_icon_size) tip=['E1AE'] cmd=command.copy('\uE1AE'))
        item(image=icon.glyph(\uE1AF,default_icon_size) tip=['E1AF'] cmd=command.copy('\uE1AF'))

        item(image=icon.glyph(\uE1B0,default_icon_size) tip=['E1B0'] cmd=command.copy('\uE1B0') col)
        item(image=icon.glyph(\uE1B1,default_icon_size) tip=['E1B1'] cmd=command.copy('\uE1B1'))
        item(image=icon.glyph(\uE1B2,default_icon_size) tip=['E1B2'] cmd=command.copy('\uE1B2'))
        item(image=icon.glyph(\uE1B3,default_icon_size) tip=['E1B3'] cmd=command.copy('\uE1B3'))
        item(image=icon.glyph(\uE1B4,default_icon_size) tip=['E1B4'] cmd=command.copy('\uE1B4'))
        item(image=icon.glyph(\uE1B5,default_icon_size) tip=['E1B5'] cmd=command.copy('\uE1B5'))
        item(image=icon.glyph(\uE1B6,default_icon_size) tip=['E1B6'] cmd=command.copy('\uE1B6'))
        item(image=icon.glyph(\uE1B7,default_icon_size) tip=['E1B7'] cmd=command.copy('\uE1B7'))
        item(image=icon.glyph(\uE1B8,default_icon_size) tip=['E1B8'] cmd=command.copy('\uE1B8'))
        item(image=icon.glyph(\uE1B9,default_icon_size) tip=['E1B9'] cmd=command.copy('\uE1B9'))
        item(image=icon.glyph(\uE1BA,default_icon_size) tip=['E1BA'] cmd=command.copy('\uE1BA'))
        item(image=icon.glyph(\uE1BB,default_icon_size) tip=['E1BB'] cmd=command.copy('\uE1BB'))
        item(image=icon.glyph(\uE1BC,default_icon_size) tip=['E1BC'] cmd=command.copy('\uE1BC'))
        item(image=icon.glyph(\uE1BD,default_icon_size) tip=['E1BD'] cmd=command.copy('\uE1BD'))
        item(image=icon.glyph(\uE1BE,default_icon_size) tip=['E1BE'] cmd=command.copy('\uE1BE'))
        item(image=icon.glyph(\uE1BF,default_icon_size) tip=['E1BF'] cmd=command.copy('\uE1BF'))

        item(image=icon.glyph(\uE1C0,default_icon_size) tip=['E1C0'] cmd=command.copy('\uE1C0') col)
        item(image=icon.glyph(\uE1C1,default_icon_size) tip=['E1C1'] cmd=command.copy('\uE1C1'))
        item(image=icon.glyph(\uE1C2,default_icon_size) tip=['E1C2'] cmd=command.copy('\uE1C2'))
        item(image=icon.glyph(\uE1C3,default_icon_size) tip=['E1C3'] cmd=command.copy('\uE1C3'))
        item(image=icon.glyph(\uE1C4,default_icon_size) tip=['E1C4'] cmd=command.copy('\uE1C4'))
        item(image=icon.glyph(\uE1C5,default_icon_size) tip=['E1C5'] cmd=command.copy('\uE1C5'))
        item(image=icon.glyph(\uE1C6,default_icon_size) tip=['E1C6'] cmd=command.copy('\uE1C6'))
        item(image=icon.glyph(\uE1C7,default_icon_size) tip=['E1C7'] cmd=command.copy('\uE1C7'))
        item(image=icon.glyph(\uE1C8,default_icon_size) tip=['E1C8'] cmd=command.copy('\uE1C8'))
        item(image=icon.glyph(\uE1C9,default_icon_size) tip=['E1C9'] cmd=command.copy('\uE1C9'))
        item(image=icon.glyph(\uE1CA,default_icon_size) tip=['E1CA'] cmd=command.copy('\uE1CA'))
        item(image=icon.glyph(\uE1CB,default_icon_size) tip=['E1CB'] cmd=command.copy('\uE1CB'))
        item(image=icon.glyph(\uE1CC,default_icon_size) tip=['E1CC'] cmd=command.copy('\uE1CC'))
        item(image=icon.glyph(\uE1CD,default_icon_size) tip=['E1CD'] cmd=command.copy('\uE1CD'))
        item(image=icon.glyph(\uE1CE,default_icon_size) tip=['E1CE'] cmd=command.copy('\uE1CE'))
        item(image=icon.glyph(\uE1CF,default_icon_size) tip=['E1CF'] cmd=command.copy('\uE1CF'))

        item(image=icon.glyph(\uE1D0,default_icon_size) tip=['E1D0'] cmd=command.copy('\uE1D0') col)
        item(image=icon.glyph(\uE1D1,default_icon_size) tip=['E1D1'] cmd=command.copy('\uE1D1'))
        item(image=icon.glyph(\uE1D2,default_icon_size) tip=['E1D2'] cmd=command.copy('\uE1D2'))
        item(image=icon.glyph(\uE1D3,default_icon_size) tip=['E1D3'] cmd=command.copy('\uE1D3'))
        item(image=icon.glyph(\uE1D4,default_icon_size) tip=['E1D4'] cmd=command.copy('\uE1D4'))
        item(image=icon.glyph(\uE1D5,default_icon_size) tip=['E1D5'] cmd=command.copy('\uE1D5'))
        item(image=icon.glyph(\uE1D6,default_icon_size) tip=['E1D6'] cmd=command.copy('\uE1D6'))
        item(image=icon.glyph(\uE1D7,default_icon_size) tip=['E1D7'] cmd=command.copy('\uE1D7'))
        item(image=icon.glyph(\uE1D8,default_icon_size) tip=['E1D8'] cmd=command.copy('\uE1D8'))
        item(image=icon.glyph(\uE1D9,default_icon_size) tip=['E1D9'] cmd=command.copy('\uE1D9'))
        item(image=icon.glyph(\uE1DA,default_icon_size) tip=['E1DA'] cmd=command.copy('\uE1DA'))
        item(image=icon.glyph(\uE1DB,default_icon_size) tip=['E1DB'] cmd=command.copy('\uE1DB'))
        item(image=icon.glyph(\uE1DC,default_icon_size) tip=['E1DC'] cmd=command.copy('\uE1DC'))
        item(image=icon.glyph(\uE1DD,default_icon_size) tip=['E1DD'] cmd=command.copy('\uE1DD'))
        item(image=icon.glyph(\uE1DE,default_icon_size) tip=['E1DE'] cmd=command.copy('\uE1DE'))
        item(image=icon.glyph(\uE1DF,default_icon_size) tip=['E1DF'] cmd=command.copy('\uE1DF'))

        item(image=icon.glyph(\uE1E0,default_icon_size) tip=['E1E0'] cmd=command.copy('\uE1E0') col)
        item(image=icon.glyph(\uE1E1,default_icon_size) tip=['E1E1'] cmd=command.copy('\uE1E1'))
        item(image=icon.glyph(\uE1E2,default_icon_size) tip=['E1E2'] cmd=command.copy('\uE1E2'))
        item(image=icon.glyph(\uE1E3,default_icon_size) tip=['E1E3'] cmd=command.copy('\uE1E3'))
        item(image=icon.glyph(\uE1E4,default_icon_size) tip=['E1E4'] cmd=command.copy('\uE1E4'))
        item(image=icon.glyph(\uE1E5,default_icon_size) tip=['E1E5'] cmd=command.copy('\uE1E5'))
        item(image=icon.glyph(\uE1E6,default_icon_size) tip=['E1E6'] cmd=command.copy('\uE1E6'))
        item(image=icon.glyph(\uE1E7,default_icon_size) tip=['E1E7'] cmd=command.copy('\uE1E7'))
        item(image=icon.glyph(\uE1E8,default_icon_size) tip=['E1E8'] cmd=command.copy('\uE1E8'))
        item(image=icon.glyph(\uE1E9,default_icon_size) tip=['E1E9'] cmd=command.copy('\uE1E9'))
        item(image=icon.glyph(\uE1EA,default_icon_size) tip=['E1EA'] cmd=command.copy('\uE1EA'))
        item(image=icon.glyph(\uE1EB,default_icon_size) tip=['E1EB'] cmd=command.copy('\uE1EB'))
        item(image=icon.glyph(\uE1EC,default_icon_size) tip=['E1EC'] cmd=command.copy('\uE1EC'))
        item(image=icon.glyph(\uE1ED,default_icon_size) tip=['E1ED'] cmd=command.copy('\uE1ED'))
        item(image=icon.glyph(\uE1EE,default_icon_size) tip=['E1EE'] cmd=command.copy('\uE1EE'))
        item(image=icon.glyph(\uE1EF,default_icon_size) tip=['E1EF'] cmd=command.copy('\uE1EF'))

        item(image=icon.glyph(\uE1F0,default_icon_size) tip=['E1F0'] cmd=command.copy('\uE1F0') col)
        item(image=icon.glyph(\uE1F1,default_icon_size) tip=['E1F1'] cmd=command.copy('\uE1F1'))
        item(image=icon.glyph(\uE1F2,default_icon_size) tip=['E1F2'] cmd=command.copy('\uE1F2'))
        item(image=icon.glyph(\uE1F3,default_icon_size) tip=['E1F3'] cmd=command.copy('\uE1F3'))
        item(image=icon.glyph(\uE1F4,default_icon_size) tip=['E1F4'] cmd=command.copy('\uE1F4'))
        item(image=icon.glyph(\uE1F5,default_icon_size) tip=['E1F5'] cmd=command.copy('\uE1F5'))
        item(image=icon.glyph(\uE1F6,default_icon_size) tip=['E1F6'] cmd=command.copy('\uE1F6'))
        item(image=icon.glyph(\uE1F7,default_icon_size) tip=['E1F7'] cmd=command.copy('\uE1F7'))
        item(image=icon.glyph(\uE1F8,default_icon_size) tip=['E1F8'] cmd=command.copy('\uE1F8'))
        item(image=icon.glyph(\uE1F9,default_icon_size) tip=['E1F9'] cmd=command.copy('\uE1F9'))
        item(image=icon.glyph(\uE1FA,default_icon_size) tip=['E1FA'] cmd=command.copy('\uE1FA'))
        item(image=icon.glyph(\uE1FB,default_icon_size) tip=['E1FB'] cmd=command.copy('\uE1FB'))
        item(image=icon.glyph(\uE1FC,default_icon_size) tip=['E1FC'] cmd=command.copy('\uE1FC'))
        item(image=icon.glyph(\uE1FD,default_icon_size) tip=['E1FD'] cmd=command.copy('\uE1FD'))
        item(image=icon.glyph(\uE1FE,default_icon_size) tip=['E1FE'] cmd=command.copy('\uE1FE'))
        item(image=icon.glyph(\uE1FF,default_icon_size) tip=['E1FF'] cmd=command.copy('\uE1FF'))
    }

    menu(title='NS Gallery #2')
    {
        item(image=icon.glyph(\uE200,default_icon_size) tip=['E200'] cmd=command.copy('\uE200') col)
        item(image=icon.glyph(\uE201,default_icon_size) tip=['E201'] cmd=command.copy('\uE201'))
        item(image=icon.glyph(\uE202,default_icon_size) tip=['E202'] cmd=command.copy('\uE202'))
        item(image=icon.glyph(\uE203,default_icon_size) tip=['E203'] cmd=command.copy('\uE203'))
        item(image=icon.glyph(\uE204,default_icon_size) tip=['E204'] cmd=command.copy('\uE204'))
        item(image=icon.glyph(\uE205,default_icon_size) tip=['E205'] cmd=command.copy('\uE205'))
        item(image=icon.glyph(\uE206,default_icon_size) tip=['E206'] cmd=command.copy('\uE206'))
        item(image=icon.glyph(\uE207,default_icon_size) tip=['E207'] cmd=command.copy('\uE207'))
        item(image=icon.glyph(\uE208,default_icon_size) tip=['E208'] cmd=command.copy('\uE208'))
        item(image=icon.glyph(\uE209,default_icon_size) tip=['E209'] cmd=command.copy('\uE209'))
        item(image=icon.glyph(\uE20A,default_icon_size) tip=['E20A'] cmd=command.copy('\uE20A'))
        item(image=icon.glyph(\uE20B,default_icon_size) tip=['E20B'] cmd=command.copy('\uE20B'))
        item(image=icon.glyph(\uE20C,default_icon_size) tip=['E20C'] cmd=command.copy('\uE20C'))
        item(image=icon.glyph(\uE20D,default_icon_size) tip=['E20D'] cmd=command.copy('\uE20D'))
        item(image=icon.glyph(\uE20E,default_icon_size) tip=['E20E'] cmd=command.copy('\uE20E'))
        item(image=icon.glyph(\uE20F,default_icon_size) tip=['E20F'] cmd=command.copy('\uE20F'))

        item(image=icon.glyph(\uE210,default_icon_size) tip=['E210'] cmd=command.copy('\uE210') col)
        item(image=icon.glyph(\uE211,default_icon_size) tip=['E211'] cmd=command.copy('\uE211'))
        item(image=icon.glyph(\uE212,default_icon_size) tip=['E212'] cmd=command.copy('\uE212'))
        item(image=icon.glyph(\uE213,default_icon_size) tip=['E213'] cmd=command.copy('\uE213'))
        item(image=icon.glyph(\uE214,default_icon_size) tip=['E214'] cmd=command.copy('\uE214'))
        item(image=icon.glyph(\uE215,default_icon_size) tip=['E215'] cmd=command.copy('\uE215'))
        item(image=icon.glyph(\uE216,default_icon_size) tip=['E216'] cmd=command.copy('\uE216'))
        item(image=icon.glyph(\uE217,default_icon_size) tip=['E217'] cmd=command.copy('\uE217'))
        item(image=icon.glyph(\uE218,default_icon_size) tip=['E218'] cmd=command.copy('\uE218'))
        item(image=icon.glyph(\uE219,default_icon_size) tip=['E219'] cmd=command.copy('\uE219'))
        item(image=icon.glyph(\uE21A,default_icon_size) tip=['E21A'] cmd=command.copy('\uE21A'))
        item(image=icon.glyph(\uE21B,default_icon_size) tip=['E21B'] cmd=command.copy('\uE21B'))
        item(image=icon.glyph(\uE21C,default_icon_size) tip=['E21C Bitcoin'] cmd=command.copy('\uE21C'))
        item(image=icon.glyph(\uE21D,default_icon_size) tip=['E21D Dollar'] cmd=command.copy('\uE21D'))
        item(image=icon.glyph(\uE21E,default_icon_size) tip=['E21E Euro'] cmd=command.copy('\uE21E'))
        item(image=icon.glyph(\uE21F,default_icon_size) tip=['E21F'] cmd=command.copy('\uE21F'))

        item(image=icon.glyph(\uE220,default_icon_size) tip=['E220 PayPal'] cmd=command.copy('\uE220') col)
        item(image=icon.glyph(\uE221,default_icon_size) tip=['E221'] cmd=command.copy('\uE221'))
        item(image=icon.glyph(\uE222,default_icon_size) tip=['E222'] cmd=command.copy('\uE222'))
        item(image=icon.glyph(\uE223,default_icon_size) tip=['E223'] cmd=command.copy('\uE223'))
        item(image=icon.glyph(\uE224,default_icon_size) tip=['E224'] cmd=command.copy('\uE224'))
        item(image=icon.glyph(\uE225,default_icon_size) tip=['E225'] cmd=command.copy('\uE225'))
        item(image=icon.glyph(\uE226,default_icon_size) tip=['E226'] cmd=command.copy('\uE226'))
        item(image=icon.glyph(\uE227,default_icon_size) tip=['E227'] cmd=command.copy('\uE227'))
        item(image=icon.glyph(\uE228,default_icon_size) tip=['E228'] cmd=command.copy('\uE228'))
        item(image=icon.glyph(\uE229,default_icon_size) tip=['E229'] cmd=command.copy('\uE229'))
        item(image=icon.glyph(\uE22A,default_icon_size) tip=['E22A git'] cmd=command.copy('\uE22A'))
        item(image=icon.glyph(\uE22B,default_icon_size) tip=['E22B Discord'] cmd=command.copy('\uE22B'))
        item(image=icon.glyph(\uE22C,default_icon_size) tip=['E22C GitHub'] cmd=command.copy('\uE22C'))
        item(image=icon.glyph(\uE22D,default_icon_size) tip=['E22D'] cmd=command.copy('\uE22D'))
        item(image=icon.glyph(\uE22E,default_icon_size) tip=['E22E JavaScript'] cmd=command.copy('\uE22E'))
        item(image=icon.glyph(\uE22F,default_icon_size) tip=['E22F'] cmd=command.copy('\uE22F'))

        item(image=icon.glyph(\uE230,default_icon_size) tip=['E230 Python'] cmd=command.copy('\uE230') col)
        item(image=icon.glyph(\uE231,default_icon_size) tip=['E231'] cmd=command.copy('\uE231'))
        item(image=icon.glyph(\uE232,default_icon_size) tip=['E232 VLC'] cmd=command.copy('\uE232'))
        item(image=icon.glyph(\uE233,default_icon_size) tip=['E233'] cmd=command.copy('\uE233'))
        item(image=icon.glyph(\uE234,default_icon_size) tip=['E234 Office'] cmd=command.copy('\uE234'))
        item(image=icon.glyph(\uE235,default_icon_size) tip=['E235'] cmd=command.copy('\uE235'))
        item(image=icon.glyph(\uE236,default_icon_size) tip=['E236'] cmd=command.copy('\uE236'))
        item(image=icon.glyph(\uE237,default_icon_size) tip=['E237 stackoverflow'] cmd=command.copy('\uE237'))
        item(image=icon.glyph(\uE238,default_icon_size) tip=['E238 digg'] cmd=command.copy('\uE238'))
        item(image=icon.glyph(\uE239,default_icon_size) tip=['E239'] cmd=command.copy('\uE239'))
        item(image=icon.glyph(\uE23A,default_icon_size) tip=['E23A'] cmd=command.copy('\uE23A'))
        item(image=icon.glyph(\uE23B,default_icon_size) tip=['E23B*'] cmd=command.copy('\uE23B'))
        item(image=icon.glyph(\uE23C,default_icon_size) tip=['E23C*'] cmd=command.copy('\uE23C'))
        item(image=icon.glyph(\uE23D,default_icon_size) tip=['E23D reddit regular'] cmd=command.copy('\uE23D'))
        item(image=icon.glyph(\uE23E,default_icon_size) tip=['E23E reddit filled'] cmd=command.copy('\uE23E'))
        item(image=icon.glyph(\uE23F,default_icon_size) tip=['E23F reddit filled2'] cmd=command.copy('\uE23F'))

        item(image=icon.glyph(\uE240,default_icon_size) tip=['E240 Linkedin'] cmd=command.copy('\uE240') col)
        item(image=icon.glyph(\uE241,default_icon_size) tip=['E241'] cmd=command.copy('\uE241'))
        item(image=icon.glyph(\uE242,default_icon_size) tip=['E242 twitter'] cmd=command.copy('\uE242'))
        item(image=icon.glyph(\uE243,default_icon_size) tip=['E243 tumblr'] cmd=command.copy('\uE243'))
        item(image=icon.glyph(\uE244,default_icon_size) tip=['E244 facebook'] cmd=command.copy('\uE244'))
        item(image=icon.glyph(\uE245,default_icon_size) tip=['E245'] cmd=command.copy('\uE245'))
        item(image=icon.glyph(\uE246,default_icon_size) tip=['E246 Whatsapp'] cmd=command.copy('\uE246'))
        item(image=icon.glyph(\uE247,default_icon_size) tip=['E247 Skype'] cmd=command.copy('\uE247'))
        item(image=icon.glyph(\uE248,default_icon_size) tip=['E248 YouTube'] cmd=command.copy('\uE248'))
        item(image=icon.glyph(\uE249,default_icon_size) tip=['E249 NS Shell filled'] cmd=command.copy('\uE249'))
        item(image=icon.glyph(\uE24A,default_icon_size) tip=['E24A NS Shell regular'] cmd=command.copy('\uE24A'))
        item(image=icon.glyph(\uE24B,default_icon_size) tip=['E24B'] cmd=command.copy('\uE24B'))
        item(image=icon.glyph(\uE24C,default_icon_size) tip=['E24C .NET'] cmd=command.copy('\uE24C'))
        item(image=icon.glyph(\uE24D,default_icon_size) tip=['E24D Android'] cmd=command.copy('\uE24D'))
        item(image=icon.glyph(\uE24E,default_icon_size) tip=['E24E Linux'] cmd=command.copy('\uE24E'))
        item(image=icon.glyph(\uE24F,default_icon_size) tip=['E24F Apple'] cmd=command.copy('\uE24F'))

        item(image=icon.glyph(\uE250,default_icon_size) tip=['E250'] cmd=command.copy('\uE250') col)
        item(image=icon.glyph(\uE251,default_icon_size) tip=['E251'] cmd=command.copy('\uE251'))
        item(image=icon.glyph(\uE252,default_icon_size) tip=['E252'] cmd=command.copy('\uE252'))
        item(image=icon.glyph(\uE253,default_icon_size) tip=['E253'] cmd=command.copy('\uE253'))
        item(image=icon.glyph(\uE254,default_icon_size) tip=['E254'] cmd=command.copy('\uE254'))
        item(image=icon.glyph(\uE255,default_icon_size) tip=['E255'] cmd=command.copy('\uE255'))
        item(image=icon.glyph(\uE256,default_icon_size) tip=['E256'] cmd=command.copy('\uE256'))
        item(image=icon.glyph(\uE257,default_icon_size) tip=['E257'] cmd=command.copy('\uE257'))
        item(image=icon.glyph(\uE258,default_icon_size) tip=['E258'] cmd=command.copy('\uE258'))
        item(image=icon.glyph(\uE259,default_icon_size) tip=['E259'] cmd=command.copy('\uE259'))
        item(image=icon.glyph(\uE25A,default_icon_size) tip=['E25A'] cmd=command.copy('\uE25A'))
        item(image=icon.glyph(\uE25B,default_icon_size) tip=['E25B'] cmd=command.copy('\uE25B'))
        item(image=icon.glyph(\uE25C,default_icon_size) tip=['E25C'] cmd=command.copy('\uE25C'))
        item(image=icon.glyph(\uE25D,default_icon_size) tip=['E25D'] cmd=command.copy('\uE25D'))
        item(image=icon.glyph(\uE25E,default_icon_size) tip=['E25E'] cmd=command.copy('\uE25E'))
        item(image=icon.glyph(\uE25F,default_icon_size) tip=['E25F'] cmd=command.copy('\uE25F'))

        item(image=icon.glyph(\uE260,default_icon_size) tip=['E260'] cmd=command.copy('\uE260') col)
        item(image=icon.glyph(\uE261,default_icon_size) tip=['E261 Not found'] cmd=command.copy('\uE261'))
        item(image=icon.glyph(\uE262,default_icon_size) tip=['E262'] cmd=command.copy('\uE262'))
        item(image=icon.glyph(\uE263,default_icon_size) tip=['E263'] cmd=command.copy('\uE263'))
        item(image=icon.glyph(\uE264,default_icon_size) tip=['E264'] cmd=command.copy('\uE264'))
        item(image=icon.glyph(\uE265,default_icon_size) tip=['E265'] cmd=command.copy('\uE265'))
        item(image=icon.glyph(\uE266,default_icon_size) tip=['E266'] cmd=command.copy('\uE266'))
        item(image=icon.glyph(\uE267,default_icon_size) tip=['E267'] cmd=command.copy('\uE267'))
        item(image=icon.glyph(\uE268,default_icon_size) tip=['E268'] cmd=command.copy('\uE268'))
        item(image=icon.glyph(\uE269,default_icon_size) tip=['E269'] cmd=command.copy('\uE269'))
        item(image=icon.glyph(\uE26A,default_icon_size) tip=['E26A'] cmd=command.copy('\uE26A'))
        item(image=icon.glyph(\uE26B,default_icon_size) tip=['E26B'] cmd=command.copy('\uE26B'))
        item(image=icon.glyph(\uE26C,default_icon_size) tip=['E26C'] cmd=command.copy('\uE26C'))
        item(image=icon.glyph(\uE26D,default_icon_size) tip=['E26D'] cmd=command.copy('\uE26D'))
        item(image=icon.glyph(\uE26E,default_icon_size) tip=['E26E'] cmd=command.copy('\uE26E'))
        item(image=icon.glyph(\uE26F,default_icon_size) tip=['E26F'] cmd=command.copy('\uE26F'))

        item(image=icon.glyph(\uE270,default_icon_size) tip=['E270'] cmd=command.copy('\uE270') col)
        item(image=icon.glyph(\uE271,default_icon_size) tip=['E271'] cmd=command.copy('\uE271'))
        item(image=icon.glyph(\uE272,default_icon_size) tip=['E272 Visual Studio Code'] cmd=command.copy('\uE272'))
        item(image=icon.glyph(\uE273,default_icon_size) tip=['E273 Visual Studio'] cmd=command.copy('\uE273'))
        item(image=icon.glyph(\uE274,default_icon_size) tip=['E274'] cmd=command.copy('\uE274'))
        item(image=icon.glyph(\uE275,default_icon_size) tip=['E275'] cmd=command.copy('\uE275'))
        item(image=icon.glyph(\uE276,default_icon_size) tip=['E276'] cmd=command.copy('\uE276'))
        item(image=icon.glyph(\uE277,default_icon_size) tip=['E277'] cmd=command.copy('\uE277'))
        item(image=icon.glyph(\uE278,default_icon_size) tip=['E278'] cmd=command.copy('\uE278'))
        item(image=icon.glyph(\uE279,default_icon_size) tip=['E279'] cmd=command.copy('\uE279'))
        item(image=icon.glyph(\uE27A,default_icon_size) tip=['E27A'] cmd=command.copy('\uE27A'))
        item(image=icon.glyph(\uE27B,default_icon_size) tip=['E27B'] cmd=command.copy('\uE27B'))
        item(image=icon.glyph(\uE27C,default_icon_size) tip=['E27C'] cmd=command.copy('\uE27C'))
        item(image=icon.glyph(\uE27D,default_icon_size) tip=['E27D'] cmd=command.copy('\uE27D'))
        item(image=icon.glyph(\uE27E,default_icon_size) tip=['E27E'] cmd=command.copy('\uE27E'))
        item(image=icon.glyph(\uE27F,default_icon_size) tip=['E27F'] cmd=command.copy('\uE27F'))

        item(image=icon.glyph(\uE280,default_icon_size) tip=['E280'] cmd=command.copy('\uE280') col)
        item(image=icon.glyph(\uE281,default_icon_size) tip=['E281'] cmd=command.copy('\uE281'))
        item(image=icon.glyph(\uE282,default_icon_size) tip=['E282'] cmd=command.copy('\uE282'))
        item(image=icon.glyph(\uE283,default_icon_size) tip=['E283'] cmd=command.copy('\uE283'))
        item(image=icon.glyph(\uE284,default_icon_size) tip=['E284'] cmd=command.copy('\uE284'))
        item(image=icon.glyph(\uE285,default_icon_size) tip=['E285'] cmd=command.copy('\uE285'))
        item(image=icon.glyph(\uE286,default_icon_size) tip=['E286'] cmd=command.copy('\uE286'))
        item(image=icon.glyph(\uE287,default_icon_size) tip=['E287'] cmd=command.copy('\uE287'))
        item(image=icon.glyph(\uE288,default_icon_size) tip=['E288'] cmd=command.copy('\uE288'))
        item(image=icon.glyph(\uE289,default_icon_size) tip=['E289'] cmd=command.copy('\uE289'))
        item(image=icon.glyph(\uE28A,default_icon_size) tip=['E28A'] cmd=command.copy('\uE28A'))
        item(image=icon.glyph(\uE28B,default_icon_size) tip=['E28B'] cmd=command.copy('\uE28B'))
        item(image=icon.glyph(\uE28C,default_icon_size) tip=['E28C'] cmd=command.copy('\uE28C'))
        item(image=icon.glyph(\uE28D,default_icon_size) tip=['E28D'] cmd=command.copy('\uE28D'))
        item(image=icon.glyph(\uE28E,default_icon_size) tip=['E28E'] cmd=command.copy('\uE28E'))
        item(image=icon.glyph(\uE28F,default_icon_size) tip=['E28F'] cmd=command.copy('\uE28F'))

        item(image=icon.glyph(\uE290,default_icon_size) tip=['E290'] cmd=command.copy('\uE290') col)
        item(image=icon.glyph(\uE291,default_icon_size) tip=['E291'] cmd=command.copy('\uE291'))
        item(image=icon.glyph(\uE292,default_icon_size) tip=['E292'] cmd=command.copy('\uE292'))
        item(image=icon.glyph(\uE293,default_icon_size) tip=['E293'] cmd=command.copy('\uE293'))
        item(image=icon.glyph(\uE294,default_icon_size) tip=['E294'] cmd=command.copy('\uE294'))
        item(image=icon.glyph(\uE295,default_icon_size) tip=['E295'] cmd=command.copy('\uE295'))
        item(image=icon.glyph(\uE296,default_icon_size) tip=['E296'] cmd=command.copy('\uE296'))
        item(image=icon.glyph(\uE297,default_icon_size) tip=['E297'] cmd=command.copy('\uE297'))
        item(image=icon.glyph(\uE298,default_icon_size) tip=['E298'] cmd=command.copy('\uE298'))
        item(image=icon.glyph(\uE299,default_icon_size) tip=['E299'] cmd=command.copy('\uE299'))
        item(image=icon.glyph(\uE29A,default_icon_size) tip=['E29A'] cmd=command.copy('\uE29A'))
        item(image=icon.glyph(\uE29B,default_icon_size) tip=['E29B (Wifi filled). Not working'] cmd=command.copy('\uE29B'))
        item(image=icon.glyph(\uE29C,default_icon_size) tip=['E29C (Wifi regular). Not working'] cmd=command.copy('\uE29C'))
    }

    separator

    menu(title='Fluent #1')
    {
        item(image=image.fluent(\uE700,default_icon_size) tip=['GlobalNavButton',tip.info] cmd=command.copy('image.fluent(\uE700,12)'))
        item(image=image.fluent(\uE701,default_icon_size) tip=['Wifi',tip.info] cmd=command.copy('image.fluent(\uE701,12)'))
        item(image=image.fluent(\uE702,default_icon_size) tip=['Bluetooth',tip.info] cmd=command.copy('image.fluent(\uE702,12)'))
        item(image=image.fluent(\uE703,default_icon_size) tip=['Connect',tip.info] cmd=command.copy('image.fluent(\uE703,12)'))
        item(image=image.fluent(\uE704,default_icon_size) tip=['InternetSharing',tip.info] cmd=command.copy('image.fluent(\uE704,12)'))
        item(image=image.fluent(\uE705,default_icon_size) tip=['VPN',tip.info] cmd=command.copy('image.fluent(\uE705,12)'))
        item(image=image.fluent(\uE706,default_icon_size) tip=['Brightness',tip.info] cmd=command.copy('image.fluent(\uE706,12)'))
        item(image=image.fluent(\uE707,default_icon_size) tip=['MapPin',tip.info] cmd=command.copy('image.fluent(\uE707,12)'))
        item(image=image.fluent(\uE708,default_icon_size) tip=['QuietHours',tip.info] cmd=command.copy('image.fluent(\uE708,12)'))
        item(image=image.fluent(\uE709,default_icon_size) tip=['Airplane',tip.info] cmd=command.copy('image.fluent(\uE709,12)'))
        item(image=image.fluent(\uE70A,default_icon_size) tip=['Tablet',tip.info] cmd=command.copy('image.fluent(\uE70A,12)'))
        item(image=image.fluent(\uE70B,default_icon_size) tip=['QuickNote',tip.info] cmd=command.copy('image.fluent(\uE70B,12)'))
        item(image=image.fluent(\uE70C,default_icon_size) tip=['RememberedDevice',tip.info] cmd=command.copy('image.fluent(\uE70C,12)'))
        item(image=image.fluent(\uE70D,default_icon_size) tip=['ChevronDown',tip.info] cmd=command.copy('image.fluent(\uE70D,12)'))
        item(image=image.fluent(\uE70E,default_icon_size) tip=['ChevronUp',tip.info] cmd=command.copy('image.fluent(\uE70E,12)'))
        item(image=image.fluent(\uE70F,default_icon_size) tip=['Edit',tip.info] cmd=command.copy('image.fluent(\uE70F,12)'))

        item(image=image.fluent(\uE710,default_icon_size) tip=['Add',tip.info] cmd=command.copy('image.fluent(\uE710,12)') col)
        item(image=image.fluent(\uE711,default_icon_size) tip=['Cancel',tip.info] cmd=command.copy('image.fluent(\uE711,12)'))
        item(image=image.fluent(\uE712,default_icon_size) tip=['More',tip.info] cmd=command.copy('image.fluent(\uE712,12)'))
        item(image=image.fluent(\uE713,default_icon_size) tip=['Settings',tip.info] cmd=command.copy('image.fluent(\uE713,12)'))
        item(image=image.fluent(\uE714,default_icon_size) tip=['Video',tip.info] cmd=command.copy('image.fluent(\uE714,12)'))
        item(image=image.fluent(\uE715,default_icon_size) tip=['Mail',tip.info] cmd=command.copy('image.fluent(\uE715,12)'))
        item(image=image.fluent(\uE716,default_icon_size) tip=['People',tip.info] cmd=command.copy('image.fluent(\uE716,12)'))
        item(image=image.fluent(\uE717,default_icon_size) tip=['Phone',tip.info] cmd=command.copy('image.fluent(\uE717,12)'))
        item(image=image.fluent(\uE718,default_icon_size) tip=['Pin',tip.info] cmd=command.copy('image.fluent(\uE718,12)'))
        item(image=image.fluent(\uE719,default_icon_size) tip=['Shop',tip.info] cmd=command.copy('image.fluent(\uE719,12)'))
        item(image=image.fluent(\uE71A,default_icon_size) tip=['Stop',tip.info] cmd=command.copy('image.fluent(\uE71A,12)'))
        item(image=image.fluent(\uE71B,default_icon_size) tip=['Link',tip.info] cmd=command.copy('image.fluent(\uE71B,12)'))
        item(image=image.fluent(\uE71C,default_icon_size) tip=['Filter',tip.info] cmd=command.copy('image.fluent(\uE71C,12)'))
        item(image=image.fluent(\uE71D,default_icon_size) tip=['AllApps',tip.info] cmd=command.copy('image.fluent(\uE71D,12)'))
        item(image=image.fluent(\uE71E,default_icon_size) tip=['Zoom',tip.info] cmd=command.copy('image.fluent(\uE71E,12)'))
        item(image=image.fluent(\uE71F,default_icon_size) tip=['ZoomOut',tip.info] cmd=command.copy('image.fluent(\uE71F,12)'))

        item(image=image.fluent(\uE720,default_icon_size) tip=['Microphone',tip.info] cmd=command.copy('image.fluent(\uE720,12)') col)
        item(image=image.fluent(\uE721,default_icon_size) tip=['Search',tip.info] cmd=command.copy('image.fluent(\uE721,12)'))
        item(image=image.fluent(\uE722,default_icon_size) tip=['Camera',tip.info] cmd=command.copy('image.fluent(\uE722,12)'))
        item(image=image.fluent(\uE723,default_icon_size) tip=['Attach',tip.info] cmd=command.copy('image.fluent(\uE723,12)'))
        item(image=image.fluent(\uE724,default_icon_size) tip=['Send',tip.info] cmd=command.copy('image.fluent(\uE724,12)'))
        item(image=image.fluent(\uE725,default_icon_size) tip=['SendFill',tip.info] cmd=command.copy('image.fluent(\uE725,12)'))
        item(image=image.fluent(\uE726,default_icon_size) tip=['WalkSolid',tip.info] cmd=command.copy('image.fluent(\uE726,12)'))
        item(image=image.fluent(\uE727,default_icon_size) tip=['InPrivate',tip.info] cmd=command.copy('image.fluent(\uE727,12)'))
        item(image=image.fluent(\uE728,default_icon_size) tip=['FavoriteList',tip.info] cmd=command.copy('image.fluent(\uE728,12)'))
        item(image=image.fluent(\uE729,default_icon_size) tip=['PageSolid',tip.info] cmd=command.copy('image.fluent(\uE729,12)'))
        item(image=image.fluent(\uE72A,default_icon_size) tip=['Forward',tip.info] cmd=command.copy('image.fluent(\uE72A,12)'))
        item(image=image.fluent(\uE72B,default_icon_size) tip=['Back',tip.info] cmd=command.copy('image.fluent(\uE72B,12)'))
        item(image=image.fluent(\uE72C,default_icon_size) tip=['Refresh',tip.info] cmd=command.copy('image.fluent(\uE72C,12)'))
        item(image=image.fluent(\uE72D,default_icon_size) tip=['Share',tip.info] cmd=command.copy('image.fluent(\uE72D,12)'))
        item(image=image.fluent(\uE72E,default_icon_size) tip=['Lock',tip.info] cmd=command.copy('image.fluent(\uE72E,12)'))
        item(image=image.fluent(\uE730,default_icon_size) tip=['ReportHacked',tip.info] cmd=command.copy('image.fluent(\uE730,12)'))

        item(image=image.fluent(\uE731,default_icon_size) tip=['EMI',tip.info] cmd=command.copy('image.fluent(\uE731,12)') col)
        item(image=image.fluent(\uE734,default_icon_size) tip=['FavoriteStar',tip.info] cmd=command.copy('image.fluent(\uE734,12)'))
        item(image=image.fluent(\uE735,default_icon_size) tip=['FavoriteStarFill',tip.info] cmd=command.copy('image.fluent(\uE735,12)'))
        item(image=image.fluent(\uE736,default_icon_size) tip=['ReadingMode',tip.info] cmd=command.copy('image.fluent(\uE736,12)'))
        item(image=image.fluent(\uE737,default_icon_size) tip=['Favicon',tip.info] cmd=command.copy('image.fluent(\uE737,12)'))
        item(image=image.fluent(\uE738,default_icon_size) tip=['Remove',tip.info] cmd=command.copy('image.fluent(\uE738,12)'))
        item(image=image.fluent(\uE739,default_icon_size) tip=['Checkbox',tip.info] cmd=command.copy('image.fluent(\uE739,12)'))
        item(image=image.fluent(\uE73A,default_icon_size) tip=['CheckboxComposite',tip.info] cmd=command.copy('image.fluent(\uE73A,12)'))
        item(image=image.fluent(\uE73B,default_icon_size) tip=['CheckboxFill',tip.info] cmd=command.copy('image.fluent(\uE73B,12)'))
        item(image=image.fluent(\uE73C,default_icon_size) tip=['CheckboxIndeterminate',tip.info] cmd=command.copy('image.fluent(\uE73C,12)'))
        item(image=image.fluent(\uE73D,default_icon_size) tip=['CheckboxCompositeReversed',tip.info] cmd=command.copy('image.fluent(\uE73D,12)'))
        item(image=image.fluent(\uE73E,default_icon_size) tip=['CheckMark',tip.info] cmd=command.copy('image.fluent(\uE73E,12)'))
        item(image=image.fluent(\uE73F,default_icon_size) tip=['BackToWindow',tip.info] cmd=command.copy('image.fluent(\uE73F,12)'))
        item(image=image.fluent(\uE740,default_icon_size) tip=['FullScreen',tip.info] cmd=command.copy('image.fluent(\uE740,12)'))
        item(image=image.fluent(\uE741,default_icon_size) tip=['ResizeTouchLarger',tip.info] cmd=command.copy('image.fluent(\uE741,12)'))
        item(image=image.fluent(\uE742,default_icon_size) tip=['ResizeTouchSmaller',tip.info] cmd=command.copy('image.fluent(\uE742,12)'))

        item(image=image.fluent(\uE743,default_icon_size) tip=['ResizeMouseSmall',tip.info] cmd=command.copy('image.fluent(\uE743,12)') col)
        item(image=image.fluent(\uE744,default_icon_size) tip=['ResizeMouseMedium',tip.info] cmd=command.copy('image.fluent(\uE744,12)'))
        item(image=image.fluent(\uE745,default_icon_size) tip=['ResizeMouseWide',tip.info] cmd=command.copy('image.fluent(\uE745,12)'))
        item(image=image.fluent(\uE746,default_icon_size) tip=['ResizeMouseTall',tip.info] cmd=command.copy('image.fluent(\uE746,12)'))
        item(image=image.fluent(\uE747,default_icon_size) tip=['ResizeMouseLarge',tip.info] cmd=command.copy('image.fluent(\uE747,12)'))
        item(image=image.fluent(\uE748,default_icon_size) tip=['SwitchUser',tip.info] cmd=command.copy('image.fluent(\uE748,12)'))
        item(image=image.fluent(\uE749,default_icon_size) tip=['Print',tip.info] cmd=command.copy('image.fluent(\uE749,12)'))
        item(image=image.fluent(\uE74A,default_icon_size) tip=['Up',tip.info] cmd=command.copy('image.fluent(\uE74A,12)'))
        item(image=image.fluent(\uE74B,default_icon_size) tip=['Down',tip.info] cmd=command.copy('image.fluent(\uE74B,12)'))
        item(image=image.fluent(\uE74C,default_icon_size) tip=['OEM',tip.info] cmd=command.copy('image.fluent(\uE74C,12)'))
        item(image=image.fluent(\uE74D,default_icon_size) tip=['Delete',tip.info] cmd=command.copy('image.fluent(\uE74D,12)'))
        item(image=image.fluent(\uE74E,default_icon_size) tip=['Save',tip.info] cmd=command.copy('image.fluent(\uE74E,12)'))
        item(image=image.fluent(\uE74F,default_icon_size) tip=['Mute',tip.info] cmd=command.copy('image.fluent(\uE74F,12)'))
        item(image=image.fluent(\uE750,default_icon_size) tip=['BackSpaceQWERTY',tip.info] cmd=command.copy('image.fluent(\uE750,12)'))
        item(image=image.fluent(\uE751,default_icon_size) tip=['ReturnKey',tip.info] cmd=command.copy('image.fluent(\uE751,12)'))
        item(image=image.fluent(\uE752,default_icon_size) tip=['UpArrowShiftKey',tip.info] cmd=command.copy('image.fluent(\uE752,12)'))

        item(image=image.fluent(\uE753,default_icon_size) tip=['Cloud',tip.info] cmd=command.copy('image.fluent(\uE753,12)') col)
        item(image=image.fluent(\uE754,default_icon_size) tip=['Flashlight',tip.info] cmd=command.copy('image.fluent(\uE754,12)'))
        item(image=image.fluent(\uE755,default_icon_size) tip=['RotationLock',tip.info] cmd=command.copy('image.fluent(\uE755,12)'))
        item(image=image.fluent(\uE756,default_icon_size) tip=['CommandPrompt',tip.info] cmd=command.copy('image.fluent(\uE756,12)'))
        item(image=image.fluent(\uE759,default_icon_size) tip=['SIPMove',tip.info] cmd=command.copy('image.fluent(\uE759,12)'))
        item(image=image.fluent(\uE75A,default_icon_size) tip=['SIPUndock',tip.info] cmd=command.copy('image.fluent(\uE75A,12)'))
        item(image=image.fluent(\uE75B,default_icon_size) tip=['SIPRedock',tip.info] cmd=command.copy('image.fluent(\uE75B,12)'))
        item(image=image.fluent(\uE75C,default_icon_size) tip=['EraseTool',tip.info] cmd=command.copy('image.fluent(\uE75C,12)'))
        item(image=image.fluent(\uE75D,default_icon_size) tip=['UnderscoreSpace',tip.info] cmd=command.copy('image.fluent(\uE75D,12)'))
        item(image=image.fluent(\uE75E,default_icon_size) tip=['GripperTool',tip.info] cmd=command.copy('image.fluent(\uE75E,12)'))
        item(image=image.fluent(\uE75F,default_icon_size) tip=['Dialpad',tip.info] cmd=command.copy('image.fluent(\uE75F,12)'))
        item(image=image.fluent(\uE760,default_icon_size) tip=['PageLeft',tip.info] cmd=command.copy('image.fluent(\uE760,12)'))
        item(image=image.fluent(\uE761,default_icon_size) tip=['PageRight',tip.info] cmd=command.copy('image.fluent(\uE761,12)'))
        item(image=image.fluent(\uE762,default_icon_size) tip=['MultiSelect',tip.info] cmd=command.copy('image.fluent(\uE762,12)'))
        item(image=image.fluent(\uE763,default_icon_size) tip=['KeyboardLeftHanded',tip.info] cmd=command.copy('image.fluent(\uE763,12)'))
        item(image=image.fluent(\uE764,default_icon_size) tip=['KeyboardRightHanded',tip.info] cmd=command.copy('image.fluent(\uE764,12)'))

        item(image=image.fluent(\uE765,default_icon_size) tip=['KeyboardClassic',tip.info] cmd=command.copy('image.fluent(\uE765,12)') col)
        item(image=image.fluent(\uE766,default_icon_size) tip=['KeyboardSplit',tip.info] cmd=command.copy('image.fluent(\uE766,12)'))
        item(image=image.fluent(\uE767,default_icon_size) tip=['Volume',tip.info] cmd=command.copy('image.fluent(\uE767,12)'))
        item(image=image.fluent(\uE768,default_icon_size) tip=['Play',tip.info] cmd=command.copy('image.fluent(\uE768,12)'))
        item(image=image.fluent(\uE769,default_icon_size) tip=['Pause',tip.info] cmd=command.copy('image.fluent(\uE769,12)'))
        item(image=image.fluent(\uE76B,default_icon_size) tip=['ChevronLeft',tip.info] cmd=command.copy('image.fluent(\uE76B,12)'))
        item(image=image.fluent(\uE76C,default_icon_size) tip=['ChevronRight',tip.info] cmd=command.copy('image.fluent(\uE76C,12)'))
        item(image=image.fluent(\uE76D,default_icon_size) tip=['InkingTool',tip.info] cmd=command.copy('image.fluent(\uE76D,12)'))
        item(image=image.fluent(\uE76E,default_icon_size) tip=['Emoji2',tip.info] cmd=command.copy('image.fluent(\uE76E,12)'))
        item(image=image.fluent(\uE76F,default_icon_size) tip=['GripperBarHorizontal',tip.info] cmd=command.copy('image.fluent(\uE76F,12)'))
        item(image=image.fluent(\uE770,default_icon_size) tip=['System',tip.info] cmd=command.copy('image.fluent(\uE770,12)'))
        item(image=image.fluent(\uE771,default_icon_size) tip=['Personalize',tip.info] cmd=command.copy('image.fluent(\uE771,12)'))
        item(image=image.fluent(\uE772,default_icon_size) tip=['Devices',tip.info] cmd=command.copy('image.fluent(\uE772,12)'))
        item(image=image.fluent(\uE773,default_icon_size) tip=['SearchAndApps',tip.info] cmd=command.copy('image.fluent(\uE773,12)'))
        item(image=image.fluent(\uE774,default_icon_size) tip=['Globe',tip.info] cmd=command.copy('image.fluent(\uE774,12)'))
        item(image=image.fluent(\uE775,default_icon_size) tip=['TimeLanguage',tip.info] cmd=command.copy('image.fluent(\uE775,12)'))

        item(image=image.fluent(\uE776,default_icon_size) tip=['EaseOfAccess',tip.info] cmd=command.copy('image.fluent(\uE776,12)') col)
        item(image=image.fluent(\uE777,default_icon_size) tip=['UpdateRestore',tip.info] cmd=command.copy('image.fluent(\uE777,12)'))
        item(image=image.fluent(\uE778,default_icon_size) tip=['HangUp',tip.info] cmd=command.copy('image.fluent(\uE778,12)'))
        item(image=image.fluent(\uE779,default_icon_size) tip=['ContactInfo',tip.info] cmd=command.copy('image.fluent(\uE779,12)'))
        item(image=image.fluent(\uE77A,default_icon_size) tip=['Unpin',tip.info] cmd=command.copy('image.fluent(\uE77A,12)'))
        item(image=image.fluent(\uE77B,default_icon_size) tip=['Contact',tip.info] cmd=command.copy('image.fluent(\uE77B,12)'))
        item(image=image.fluent(\uE77C,default_icon_size) tip=['Memo',tip.info] cmd=command.copy('image.fluent(\uE77C,12)'))
        item(image=image.fluent(\uE77E,default_icon_size) tip=['IncomingCall',tip.info] cmd=command.copy('image.fluent(\uE77E,12)'))
        item(image=image.fluent(\uE77F,default_icon_size) tip=['Paste',tip.info] cmd=command.copy('image.fluent(\uE77F,12)'))
        item(image=image.fluent(\uE780,default_icon_size) tip=['PhoneBook',tip.info] cmd=command.copy('image.fluent(\uE780,12)'))
        item(image=image.fluent(\uE781,default_icon_size) tip=['LEDLight',tip.info] cmd=command.copy('image.fluent(\uE781,12)'))
        item(image=image.fluent(\uE783,default_icon_size) tip=['Error',tip.info] cmd=command.copy('image.fluent(\uE783,12)'))
        item(image=image.fluent(\uE784,default_icon_size) tip=['GripperBarVertical',tip.info] cmd=command.copy('image.fluent(\uE784,12)'))
        item(image=image.fluent(\uE785,default_icon_size) tip=['Unlock',tip.info] cmd=command.copy('image.fluent(\uE785,12)'))
        item(image=image.fluent(\uE786,default_icon_size) tip=['Slideshow',tip.info] cmd=command.copy('image.fluent(\uE786,12)'))
        item(image=image.fluent(\uE787,default_icon_size) tip=['Calendar',tip.info] cmd=command.copy('image.fluent(\uE787,12)'))

        item(image=image.fluent(\uE788,default_icon_size) tip=['GripperResize',tip.info] cmd=command.copy('image.fluent(\uE788,12)') col)
        item(image=image.fluent(\uE789,default_icon_size) tip=['Megaphone',tip.info] cmd=command.copy('image.fluent(\uE789,12)'))
        item(image=image.fluent(\uE78A,default_icon_size) tip=['Trim',tip.info] cmd=command.copy('image.fluent(\uE78A,12)'))
        item(image=image.fluent(\uE78B,default_icon_size) tip=['NewWindow',tip.info] cmd=command.copy('image.fluent(\uE78B,12)'))
        item(image=image.fluent(\uE78C,default_icon_size) tip=['SaveLocal',tip.info] cmd=command.copy('image.fluent(\uE78C,12)'))
        item(image=image.fluent(\uE790,default_icon_size) tip=['Color',tip.info] cmd=command.copy('image.fluent(\uE790,12)'))
        item(image=image.fluent(\uE791,default_icon_size) tip=['DataSense',tip.info] cmd=command.copy('image.fluent(\uE791,12)'))
        item(image=image.fluent(\uE792,default_icon_size) tip=['SaveAs',tip.info] cmd=command.copy('image.fluent(\uE792,12)'))
        item(image=image.fluent(\uE793,default_icon_size) tip=['Light',tip.info] cmd=command.copy('image.fluent(\uE793,12)'))
        item(image=image.fluent(\uE799,default_icon_size) tip=['AspectRatio',tip.info] cmd=command.copy('image.fluent(\uE799,12)'))
        item(image=image.fluent(\uE7A5,default_icon_size) tip=['DataSenseBar',tip.info] cmd=command.copy('image.fluent(\uE7A5,12)'))
        item(image=image.fluent(\uE7A6,default_icon_size) tip=['Redo',tip.info] cmd=command.copy('image.fluent(\uE7A6,12)'))
        item(image=image.fluent(\uE7A7,default_icon_size) tip=['Undo',tip.info] cmd=command.copy('image.fluent(\uE7A7,12)'))
        item(image=image.fluent(\uE7A8,default_icon_size) tip=['Crop',tip.info] cmd=command.copy('image.fluent(\uE7A8,12)'))
        item(image=image.fluent(\uE7AC,default_icon_size) tip=['OpenWith',tip.info] cmd=command.copy('image.fluent(\uE7AC,12)'))
        item(image=image.fluent(\uE7AD,default_icon_size) tip=['Rotate',tip.info] cmd=command.copy('image.fluent(\uE7AD,12)'))

        item(image=image.fluent(\uE7B3,default_icon_size) tip=['RedEye',tip.info] cmd=command.copy('image.fluent(\uE7B3,12)') col)
        item(image=image.fluent(\uE7B5,default_icon_size) tip=['SetlockScreen',tip.info] cmd=command.copy('image.fluent(\uE7B5,12)'))
        item(image=image.fluent(\uE7B7,default_icon_size) tip=['MapPin2',tip.info] cmd=command.copy('image.fluent(\uE7B7,12)'))
        item(image=image.fluent(\uE7B8,default_icon_size) tip=['Package',tip.info] cmd=command.copy('image.fluent(\uE7B8,12)'))
        item(image=image.fluent(\uE7BA,default_icon_size) tip=['Warning',tip.info] cmd=command.copy('image.fluent(\uE7BA,12)'))
        item(image=image.fluent(\uE7BC,default_icon_size) tip=['ReadingList',tip.info] cmd=command.copy('image.fluent(\uE7BC,12)'))
        item(image=image.fluent(\uE7BE,default_icon_size) tip=['Education',tip.info] cmd=command.copy('image.fluent(\uE7BE,12)'))
        item(image=image.fluent(\uE7BF,default_icon_size) tip=['ShoppingCart',tip.info] cmd=command.copy('image.fluent(\uE7BF,12)'))
        item(image=image.fluent(\uE7C0,default_icon_size) tip=['Train',tip.info] cmd=command.copy('image.fluent(\uE7C0,12)'))
        item(image=image.fluent(\uE7C1,default_icon_size) tip=['Flag',tip.info] cmd=command.copy('image.fluent(\uE7C1,12)'))
        item(image=image.fluent(\uE7C2,default_icon_size) tip=['Move',tip.info] cmd=command.copy('image.fluent(\uE7C2,12)'))
        item(image=image.fluent(\uE7C3,default_icon_size) tip=['Page',tip.info] cmd=command.copy('image.fluent(\uE7C3,12)'))
        item(image=image.fluent(\uE7C4,default_icon_size) tip=['TaskView',tip.info] cmd=command.copy('image.fluent(\uE7C4,12)'))
        item(image=image.fluent(\uE7C5,default_icon_size) tip=['BrowsePhotos',tip.info] cmd=command.copy('image.fluent(\uE7C5,12)'))
        item(image=image.fluent(\uE7C6,default_icon_size) tip=['HalfStarLeft',tip.info] cmd=command.copy('image.fluent(\uE7C6,12)'))
        item(image=image.fluent(\uE7C7,default_icon_size) tip=['HalfStarRight',tip.info] cmd=command.copy('image.fluent(\uE7C7,12)'))

        item(image=image.fluent(\uE7C8,default_icon_size) tip=['Record',tip.info] cmd=command.copy('image.fluent(\uE7C8,12)') col)
        item(image=image.fluent(\uE7C9,default_icon_size) tip=['TouchPointer',tip.info] cmd=command.copy('image.fluent(\uE7C9,12)'))
        item(image=image.fluent(\uE7DE,default_icon_size) tip=['LangJPN',tip.info] cmd=command.copy('image.fluent(\uE7DE,12)'))
        item(image=image.fluent(\uE7E3,default_icon_size) tip=['Ferry',tip.info] cmd=command.copy('image.fluent(\uE7E3,12)'))
        item(image=image.fluent(\uE7E6,default_icon_size) tip=['Highlight',tip.info] cmd=command.copy('image.fluent(\uE7E6,12)'))
        item(image=image.fluent(\uE7E7,default_icon_size) tip=['ActionCenterNotification',tip.info] cmd=command.copy('image.fluent(\uE7E7,12)'))
        item(image=image.fluent(\uE7E8,default_icon_size) tip=['PowerButton',tip.info] cmd=command.copy('image.fluent(\uE7E8,12)'))
        item(image=image.fluent(\uE7EA,default_icon_size) tip=['ResizeTouchNarrower',tip.info] cmd=command.copy('image.fluent(\uE7EA,12)'))
        item(image=image.fluent(\uE7EB,default_icon_size) tip=['ResizeTouchShorter',tip.info] cmd=command.copy('image.fluent(\uE7EB,12)'))
        item(image=image.fluent(\uE7EC,default_icon_size) tip=['DrivingMode',tip.info] cmd=command.copy('image.fluent(\uE7EC,12)'))
        item(image=image.fluent(\uE7ED,default_icon_size) tip=['RingerSilent',tip.info] cmd=command.copy('image.fluent(\uE7ED,12)'))
        item(image=image.fluent(\uE7EE,default_icon_size) tip=['OtherUser',tip.info] cmd=command.copy('image.fluent(\uE7EE,12)'))
        item(image=image.fluent(\uE7EF,default_icon_size) tip=['Admin',tip.info] cmd=command.copy('image.fluent(\uE7EF,12)'))
        item(image=image.fluent(\uE7F0,default_icon_size) tip=['CC',tip.info] cmd=command.copy('image.fluent(\uE7F0,12)'))
        item(image=image.fluent(\uE7F1,default_icon_size) tip=['SDCard',tip.info] cmd=command.copy('image.fluent(\uE7F1,12)'))
        item(image=image.fluent(\uE7F2,default_icon_size) tip=['CallForwarding',tip.info] cmd=command.copy('image.fluent(\uE7F2,12)'))

        item(image=image.fluent(\uE7F3,default_icon_size) tip=['SettingsDisplaySound',tip.info] cmd=command.copy('image.fluent(\uE7F3,12)') col)
        item(image=image.fluent(\uE7F4,default_icon_size) tip=['TVMonitor',tip.info] cmd=command.copy('image.fluent(\uE7F4,12)'))
        item(image=image.fluent(\uE7F5,default_icon_size) tip=['Speakers',tip.info] cmd=command.copy('image.fluent(\uE7F5,12)'))
        item(image=image.fluent(\uE7F6,default_icon_size) tip=['Headphone',tip.info] cmd=command.copy('image.fluent(\uE7F6,12)'))
        item(image=image.fluent(\uE7F7,default_icon_size) tip=['DeviceLaptopPic',tip.info] cmd=command.copy('image.fluent(\uE7F7,12)'))
        item(image=image.fluent(\uE7F8,default_icon_size) tip=['DeviceLaptopNoPic',tip.info] cmd=command.copy('image.fluent(\uE7F8,12)'))
        item(image=image.fluent(\uE7F9,default_icon_size) tip=['DeviceMonitorRightPic',tip.info] cmd=command.copy('image.fluent(\uE7F9,12)'))
        item(image=image.fluent(\uE7FA,default_icon_size) tip=['DeviceMonitorLeftPic',tip.info] cmd=command.copy('image.fluent(\uE7FA,12)'))
        item(image=image.fluent(\uE7FB,default_icon_size) tip=['DeviceMonitorNoPic',tip.info] cmd=command.copy('image.fluent(\uE7FB,12)'))
        item(image=image.fluent(\uE7FC,default_icon_size) tip=['Game',tip.info] cmd=command.copy('image.fluent(\uE7FC,12)'))
        item(image=image.fluent(\uE7FD,default_icon_size) tip=['HorizontalTabKey',tip.info] cmd=command.copy('image.fluent(\uE7FD,12)'))
        item(image=image.fluent(\uE802,default_icon_size) tip=['StreetsideSplitMinimize',tip.info] cmd=command.copy('image.fluent(\uE802,12)'))
        item(image=image.fluent(\uE803,default_icon_size) tip=['StreetsideSplitExpand',tip.info] cmd=command.copy('image.fluent(\uE803,12)'))
        item(image=image.fluent(\uE804,default_icon_size) tip=['Car',tip.info] cmd=command.copy('image.fluent(\uE804,12)'))
        item(image=image.fluent(\uE805,default_icon_size) tip=['Walk',tip.info] cmd=command.copy('image.fluent(\uE805,12)'))
        item(image=image.fluent(\uE806,default_icon_size) tip=['Bus',tip.info] cmd=command.copy('image.fluent(\uE806,12)'))

        item(image=image.fluent(\uE809,default_icon_size) tip=['TiltUp',tip.info] cmd=command.copy('image.fluent(\uE809,12)') col)
        item(image=image.fluent(\uE80A,default_icon_size) tip=['TiltDown',tip.info] cmd=command.copy('image.fluent(\uE80A,12)'))
        item(image=image.fluent(\uE80B,default_icon_size) tip=['CallControl',tip.info] cmd=command.copy('image.fluent(\uE80B,12)'))
        item(image=image.fluent(\uE80C,default_icon_size) tip=['RotateMapRight',tip.info] cmd=command.copy('image.fluent(\uE80C,12)'))
        item(image=image.fluent(\uE80D,default_icon_size) tip=['RotateMapLeft',tip.info] cmd=command.copy('image.fluent(\uE80D,12)'))
        item(image=image.fluent(\uE80F,default_icon_size) tip=['Home',tip.info] cmd=command.copy('image.fluent(\uE80F,12)'))
        item(image=image.fluent(\uE811,default_icon_size) tip=['ParkingLocation',tip.info] cmd=command.copy('image.fluent(\uE811,12)'))
        item(image=image.fluent(\uE812,default_icon_size) tip=['MapCompassTop',tip.info] cmd=command.copy('image.fluent(\uE812,12)'))
        item(image=image.fluent(\uE813,default_icon_size) tip=['MapCompassBottom',tip.info] cmd=command.copy('image.fluent(\uE813,12)'))
        item(image=image.fluent(\uE814,default_icon_size) tip=['IncidentTriangle',tip.info] cmd=command.copy('image.fluent(\uE814,12)'))
        item(image=image.fluent(\uE815,default_icon_size) tip=['Touch',tip.info] cmd=command.copy('image.fluent(\uE815,12)'))
        item(image=image.fluent(\uE816,default_icon_size) tip=['MapDirections',tip.info] cmd=command.copy('image.fluent(\uE816,12)'))
        item(image=image.fluent(\uE819,default_icon_size) tip=['StartPoint',tip.info] cmd=command.copy('image.fluent(\uE819,12)'))
        item(image=image.fluent(\uE81A,default_icon_size) tip=['StopPoint',tip.info] cmd=command.copy('image.fluent(\uE81A,12)'))
        item(image=image.fluent(\uE81B,default_icon_size) tip=['EndPoint',tip.info] cmd=command.copy('image.fluent(\uE81B,12)'))
        item(image=image.fluent(\uE81C,default_icon_size) tip=['History',tip.info] cmd=command.copy('image.fluent(\uE81C,12)'))

        item(image=image.fluent(\uE81D,default_icon_size) tip=['Location',tip.info] cmd=command.copy('image.fluent(\uE81D,12)') col)
        item(image=image.fluent(\uE81E,default_icon_size) tip=['MapLayers',tip.info] cmd=command.copy('image.fluent(\uE81E,12)'))
        item(image=image.fluent(\uE81F,default_icon_size) tip=['Accident',tip.info] cmd=command.copy('image.fluent(\uE81F,12)'))
        item(image=image.fluent(\uE821,default_icon_size) tip=['Work',tip.info] cmd=command.copy('image.fluent(\uE821,12)'))
        item(image=image.fluent(\uE822,default_icon_size) tip=['Construction',tip.info] cmd=command.copy('image.fluent(\uE822,12)'))
        item(image=image.fluent(\uE823,default_icon_size) tip=['Recent',tip.info] cmd=command.copy('image.fluent(\uE823,12)'))
        item(image=image.fluent(\uE825,default_icon_size) tip=['Bank',tip.info] cmd=command.copy('image.fluent(\uE825,12)'))
        item(image=image.fluent(\uE826,default_icon_size) tip=['DownloadMap',tip.info] cmd=command.copy('image.fluent(\uE826,12)'))
        item(image=image.fluent(\uE829,default_icon_size) tip=['InkingToolFill2',tip.info] cmd=command.copy('image.fluent(\uE829,12)'))
        item(image=image.fluent(\uE82A,default_icon_size) tip=['HighlightFill2',tip.info] cmd=command.copy('image.fluent(\uE82A,12)'))
        item(image=image.fluent(\uE82B,default_icon_size) tip=['EraseToolFill',tip.info] cmd=command.copy('image.fluent(\uE82B,12)'))
        item(image=image.fluent(\uE82C,default_icon_size) tip=['EraseToolFill2',tip.info] cmd=command.copy('image.fluent(\uE82C,12)'))
        item(image=image.fluent(\uE82D,default_icon_size) tip=['Dictionary',tip.info] cmd=command.copy('image.fluent(\uE82D,12)'))
        item(image=image.fluent(\uE82E,default_icon_size) tip=['DictionaryAdd',tip.info] cmd=command.copy('image.fluent(\uE82E,12)'))
        item(image=image.fluent(\uE82F,default_icon_size) tip=['ToolTip',tip.info] cmd=command.copy('image.fluent(\uE82F,12)'))
        item(image=image.fluent(\uE830,default_icon_size) tip=['ChromeBack',tip.info] cmd=command.copy('image.fluent(\uE830,12)'))

        item(image=image.fluent(\uE835,default_icon_size) tip=['ProvisioningPackage',tip.info] cmd=command.copy('image.fluent(\uE835,12)') col)
        item(image=image.fluent(\uE836,default_icon_size) tip=['AddRemoteDevice',tip.info] cmd=command.copy('image.fluent(\uE836,12)'))
        item(image=image.fluent(\uE838,default_icon_size) tip=['FolderOpen',tip.info] cmd=command.copy('image.fluent(\uE838,12)'))
        item(image=image.fluent(\uE839,default_icon_size) tip=['Ethernet',tip.info] cmd=command.copy('image.fluent(\uE839,12)'))
        item(image=image.fluent(\uE83A,default_icon_size) tip=['ShareBroadband',tip.info] cmd=command.copy('image.fluent(\uE83A,12)'))
        item(image=image.fluent(\uE83B,default_icon_size) tip=['DirectAccess',tip.info] cmd=command.copy('image.fluent(\uE83B,12)'))
        item(image=image.fluent(\uE83C,default_icon_size) tip=['DialUp',tip.info] cmd=command.copy('image.fluent(\uE83C,12)'))
        item(image=image.fluent(\uE83D,default_icon_size) tip=['DefenderApp',tip.info] cmd=command.copy('image.fluent(\uE83D,12)'))
        item(image=image.fluent(\uE83E,default_icon_size) tip=['BatteryCharging9',tip.info] cmd=command.copy('image.fluent(\uE83E,12)'))
        item(image=image.fluent(\uE83F,default_icon_size) tip=['Battery10',tip.info] cmd=command.copy('image.fluent(\uE83F,12)'))
        item(image=image.fluent(\uE840,default_icon_size) tip=['Pinned',tip.info] cmd=command.copy('image.fluent(\uE840,12)'))
        item(image=image.fluent(\uE841,default_icon_size) tip=['PinFill',tip.info] cmd=command.copy('image.fluent(\uE841,12)'))
        item(image=image.fluent(\uE842,default_icon_size) tip=['PinnedFill',tip.info] cmd=command.copy('image.fluent(\uE842,12)'))
        item(image=image.fluent(\uE843,default_icon_size) tip=['PeriodKey',tip.info] cmd=command.copy('image.fluent(\uE843,12)'))
        item(image=image.fluent(\uE844,default_icon_size) tip=['PuncKey',tip.info] cmd=command.copy('image.fluent(\uE844,12)'))
        item(image=image.fluent(\uE845,default_icon_size) tip=['RevToggleKey',tip.info] cmd=command.copy('image.fluent(\uE845,12)'))

        item(image=image.fluent(\uE846,default_icon_size) tip=['RightArrowKeyTime1',tip.info] cmd=command.copy('image.fluent(\uE846,12)') col)
        item(image=image.fluent(\uE847,default_icon_size) tip=['RightArrowKeyTime2',tip.info] cmd=command.copy('image.fluent(\uE847,12)'))
        item(image=image.fluent(\uE848,default_icon_size) tip=['LeftQuote',tip.info] cmd=command.copy('image.fluent(\uE848,12)'))
        item(image=image.fluent(\uE849,default_icon_size) tip=['RightQuote',tip.info] cmd=command.copy('image.fluent(\uE849,12)'))
        item(image=image.fluent(\uE84A,default_icon_size) tip=['DownShiftKey',tip.info] cmd=command.copy('image.fluent(\uE84A,12)'))
        item(image=image.fluent(\uE84B,default_icon_size) tip=['UpShiftKey',tip.info] cmd=command.copy('image.fluent(\uE84B,12)'))
        item(image=image.fluent(\uE84C,default_icon_size) tip=['PuncKey0',tip.info] cmd=command.copy('image.fluent(\uE84C,12)'))
        item(image=image.fluent(\uE84D,default_icon_size) tip=['PuncKeyLeftBottom',tip.info] cmd=command.copy('image.fluent(\uE84D,12)'))
        item(image=image.fluent(\uE84E,default_icon_size) tip=['RightArrowKeyTime3',tip.info] cmd=command.copy('image.fluent(\uE84E,12)'))
        item(image=image.fluent(\uE84F,default_icon_size) tip=['RightArrowKeyTime4',tip.info] cmd=command.copy('image.fluent(\uE84F,12)'))
        item(image=image.fluent(\uE850,default_icon_size) tip=['Battery0',tip.info] cmd=command.copy('image.fluent(\uE850,12)'))
        item(image=image.fluent(\uE851,default_icon_size) tip=['Battery1',tip.info] cmd=command.copy('image.fluent(\uE851,12)'))
        item(image=image.fluent(\uE852,default_icon_size) tip=['Battery2',tip.info] cmd=command.copy('image.fluent(\uE852,12)'))
        item(image=image.fluent(\uE853,default_icon_size) tip=['Battery3',tip.info] cmd=command.copy('image.fluent(\uE853,12)'))
        item(image=image.fluent(\uE854,default_icon_size) tip=['Battery4',tip.info] cmd=command.copy('image.fluent(\uE854,12)'))
        item(image=image.fluent(\uE855,default_icon_size) tip=['Battery5',tip.info] cmd=command.copy('image.fluent(\uE855,12)'))
    }

    menu(title='Fluent #2')
    {
        item(image=image.fluent(\uE856,default_icon_size) tip=['Battery6',tip.info] cmd=command.copy('image.fluent(\uE856,12)'))
        item(image=image.fluent(\uE857,default_icon_size) tip=['Battery7',tip.info] cmd=command.copy('image.fluent(\uE857,12)'))
        item(image=image.fluent(\uE858,default_icon_size) tip=['Battery8',tip.info] cmd=command.copy('image.fluent(\uE858,12)'))
        item(image=image.fluent(\uE859,default_icon_size) tip=['Battery9',tip.info] cmd=command.copy('image.fluent(\uE859,12)'))
        item(image=image.fluent(\uE85A,default_icon_size) tip=['BatteryCharging0',tip.info] cmd=command.copy('image.fluent(\uE85A,12)'))
        item(image=image.fluent(\uE85B,default_icon_size) tip=['BatteryCharging1',tip.info] cmd=command.copy('image.fluent(\uE85B,12)'))
        item(image=image.fluent(\uE85C,default_icon_size) tip=['BatteryCharging2',tip.info] cmd=command.copy('image.fluent(\uE85C,12)'))
        item(image=image.fluent(\uE85D,default_icon_size) tip=['BatteryCharging3',tip.info] cmd=command.copy('image.fluent(\uE85D,12)'))
        item(image=image.fluent(\uE85E,default_icon_size) tip=['BatteryCharging4',tip.info] cmd=command.copy('image.fluent(\uE85E,12)'))
        item(image=image.fluent(\uE85F,default_icon_size) tip=['BatteryCharging5',tip.info] cmd=command.copy('image.fluent(\uE85F,12)'))
        item(image=image.fluent(\uE860,default_icon_size) tip=['BatteryCharging6',tip.info] cmd=command.copy('image.fluent(\uE860,12)'))
        item(image=image.fluent(\uE861,default_icon_size) tip=['BatteryCharging7',tip.info] cmd=command.copy('image.fluent(\uE861,12)'))
        item(image=image.fluent(\uE862,default_icon_size) tip=['BatteryCharging8',tip.info] cmd=command.copy('image.fluent(\uE862,12)'))
        item(image=image.fluent(\uE863,default_icon_size) tip=['BatterySaver0',tip.info] cmd=command.copy('image.fluent(\uE863,12)'))
        item(image=image.fluent(\uE864,default_icon_size) tip=['BatterySaver1',tip.info] cmd=command.copy('image.fluent(\uE864,12)'))
        item(image=image.fluent(\uE865,default_icon_size) tip=['BatterySaver2',tip.info] cmd=command.copy('image.fluent(\uE865,12)'))

        item(image=image.fluent(\uE866,default_icon_size) tip=['BatterySaver3',tip.info] cmd=command.copy('image.fluent(\uE866,12)') col)
        item(image=image.fluent(\uE867,default_icon_size) tip=['BatterySaver4',tip.info] cmd=command.copy('image.fluent(\uE867,12)'))
        item(image=image.fluent(\uE868,default_icon_size) tip=['BatterySaver5',tip.info] cmd=command.copy('image.fluent(\uE868,12)'))
        item(image=image.fluent(\uE869,default_icon_size) tip=['BatterySaver6',tip.info] cmd=command.copy('image.fluent(\uE869,12)'))
        item(image=image.fluent(\uE86A,default_icon_size) tip=['BatterySaver7',tip.info] cmd=command.copy('image.fluent(\uE86A,12)'))
        item(image=image.fluent(\uE86B,default_icon_size) tip=['BatterySaver8',tip.info] cmd=command.copy('image.fluent(\uE86B,12)'))
        item(image=image.fluent(\uE86C,default_icon_size) tip=['SignalBars1',tip.info] cmd=command.copy('image.fluent(\uE86C,12)'))
        item(image=image.fluent(\uE86D,default_icon_size) tip=['SignalBars2',tip.info] cmd=command.copy('image.fluent(\uE86D,12)'))
        item(image=image.fluent(\uE86E,default_icon_size) tip=['SignalBars3',tip.info] cmd=command.copy('image.fluent(\uE86E,12)'))
        item(image=image.fluent(\uE86F,default_icon_size) tip=['SignalBars4',tip.info] cmd=command.copy('image.fluent(\uE86F,12)'))
        item(image=image.fluent(\uE870,default_icon_size) tip=['SignalBars5',tip.info] cmd=command.copy('image.fluent(\uE870,12)'))
        item(image=image.fluent(\uE871,default_icon_size) tip=['SignalNotConnected',tip.info] cmd=command.copy('image.fluent(\uE871,12)'))
        item(image=image.fluent(\uE872,default_icon_size) tip=['Wifi1',tip.info] cmd=command.copy('image.fluent(\uE872,12)'))
        item(image=image.fluent(\uE873,default_icon_size) tip=['Wifi2',tip.info] cmd=command.copy('image.fluent(\uE873,12)'))
        item(image=image.fluent(\uE874,default_icon_size) tip=['Wifi3',tip.info] cmd=command.copy('image.fluent(\uE874,12)'))
        item(image=image.fluent(\uE875,default_icon_size) tip=['MobSIMLock',tip.info] cmd=command.copy('image.fluent(\uE875,12)'))

        item(image=image.fluent(\uE876,default_icon_size) tip=['MobSIMMissing',tip.info] cmd=command.copy('image.fluent(\uE876,12)') col)
        item(image=image.fluent(\uE877,default_icon_size) tip=['Vibrate',tip.info] cmd=command.copy('image.fluent(\uE877,12)'))
        item(image=image.fluent(\uE878,default_icon_size) tip=['RoamingInternational',tip.info] cmd=command.copy('image.fluent(\uE878,12)'))
        item(image=image.fluent(\uE879,default_icon_size) tip=['RoamingDomestic',tip.info] cmd=command.copy('image.fluent(\uE879,12)'))
        item(image=image.fluent(\uE87A,default_icon_size) tip=['CallForwardInternational',tip.info] cmd=command.copy('image.fluent(\uE87A,12)'))
        item(image=image.fluent(\uE87B,default_icon_size) tip=['CallForwardRoaming',tip.info] cmd=command.copy('image.fluent(\uE87B,12)'))
        item(image=image.fluent(\uE87C,default_icon_size) tip=['JpnRomanji',tip.info] cmd=command.copy('image.fluent(\uE87C,12)'))
        item(image=image.fluent(\uE87D,default_icon_size) tip=['JpnRomanjiLock',tip.info] cmd=command.copy('image.fluent(\uE87D,12)'))
        item(image=image.fluent(\uE87E,default_icon_size) tip=['JpnRomanjiShift',tip.info] cmd=command.copy('image.fluent(\uE87E,12)'))
        item(image=image.fluent(\uE87F,default_icon_size) tip=['JpnRomanjiShiftLock',tip.info] cmd=command.copy('image.fluent(\uE87F,12)'))
        item(image=image.fluent(\uE880,default_icon_size) tip=['StatusDataTransfer',tip.info] cmd=command.copy('image.fluent(\uE880,12)'))
        item(image=image.fluent(\uE881,default_icon_size) tip=['StatusDataTransferVPN',tip.info] cmd=command.copy('image.fluent(\uE881,12)'))
        item(image=image.fluent(\uE882,default_icon_size) tip=['StatusDualSIM2',tip.info] cmd=command.copy('image.fluent(\uE882,12)'))
        item(image=image.fluent(\uE883,default_icon_size) tip=['StatusDualSIM2VPN',tip.info] cmd=command.copy('image.fluent(\uE883,12)'))
        item(image=image.fluent(\uE884,default_icon_size) tip=['StatusDualSIM1',tip.info] cmd=command.copy('image.fluent(\uE884,12)'))
        item(image=image.fluent(\uE885,default_icon_size) tip=['StatusDualSIM1VPN',tip.info] cmd=command.copy('image.fluent(\uE885,12)'))

        item(image=image.fluent(\uE886,default_icon_size) tip=['StatusSGLTE',tip.info] cmd=command.copy('image.fluent(\uE886,12)') col)
        item(image=image.fluent(\uE887,default_icon_size) tip=['StatusSGLTECell',tip.info] cmd=command.copy('image.fluent(\uE887,12)'))
        item(image=image.fluent(\uE888,default_icon_size) tip=['StatusSGLTEDataVPN',tip.info] cmd=command.copy('image.fluent(\uE888,12)'))
        item(image=image.fluent(\uE889,default_icon_size) tip=['StatusVPN',tip.info] cmd=command.copy('image.fluent(\uE889,12)'))
        item(image=image.fluent(\uE88A,default_icon_size) tip=['WifiHotspot',tip.info] cmd=command.copy('image.fluent(\uE88A,12)'))
        item(image=image.fluent(\uE88B,default_icon_size) tip=['LanguageKor',tip.info] cmd=command.copy('image.fluent(\uE88B,12)'))
        item(image=image.fluent(\uE88C,default_icon_size) tip=['LanguageCht',tip.info] cmd=command.copy('image.fluent(\uE88C,12)'))
        item(image=image.fluent(\uE88D,default_icon_size) tip=['LanguageChs',tip.info] cmd=command.copy('image.fluent(\uE88D,12)'))
        item(image=image.fluent(\uE88E,default_icon_size) tip=['USB',tip.info] cmd=command.copy('image.fluent(\uE88E,12)'))
        item(image=image.fluent(\uE88F,default_icon_size) tip=['InkingToolFill',tip.info] cmd=command.copy('image.fluent(\uE88F,12)'))
        item(image=image.fluent(\uE890,default_icon_size) tip=['View',tip.info] cmd=command.copy('image.fluent(\uE890,12)'))
        item(image=image.fluent(\uE891,default_icon_size) tip=['HighlightFill',tip.info] cmd=command.copy('image.fluent(\uE891,12)'))
        item(image=image.fluent(\uE892,default_icon_size) tip=['Previous',tip.info] cmd=command.copy('image.fluent(\uE892,12)'))
        item(image=image.fluent(\uE893,default_icon_size) tip=['Next',tip.info] cmd=command.copy('image.fluent(\uE893,12)'))
        item(image=image.fluent(\uE894,default_icon_size) tip=['Clear',tip.info] cmd=command.copy('image.fluent(\uE894,12)'))
        item(image=image.fluent(\uE895,default_icon_size) tip=['Sync',tip.info] cmd=command.copy('image.fluent(\uE895,12)'))

        item(image=image.fluent(\uE896,default_icon_size) tip=['Download',tip.info] cmd=command.copy('image.fluent(\uE896,12)') col)
        item(image=image.fluent(\uE897,default_icon_size) tip=['Help',tip.info] cmd=command.copy('image.fluent(\uE897,12)'))
        item(image=image.fluent(\uE898,default_icon_size) tip=['Upload',tip.info] cmd=command.copy('image.fluent(\uE898,12)'))
        item(image=image.fluent(\uE899,default_icon_size) tip=['Emoji',tip.info] cmd=command.copy('image.fluent(\uE899,12)'))
        item(image=image.fluent(\uE89A,default_icon_size) tip=['TwoPage',tip.info] cmd=command.copy('image.fluent(\uE89A,12)'))
        item(image=image.fluent(\uE89B,default_icon_size) tip=['LeaveChat',tip.info] cmd=command.copy('image.fluent(\uE89B,12)'))
        item(image=image.fluent(\uE89C,default_icon_size) tip=['MailForward',tip.info] cmd=command.copy('image.fluent(\uE89C,12)'))
        item(image=image.fluent(\uE89E,default_icon_size) tip=['RotateCamera',tip.info] cmd=command.copy('image.fluent(\uE89E,12)'))
        item(image=image.fluent(\uE89F,default_icon_size) tip=['ClosePane',tip.info] cmd=command.copy('image.fluent(\uE89F,12)'))
        item(image=image.fluent(\uE8A0,default_icon_size) tip=['OpenPane',tip.info] cmd=command.copy('image.fluent(\uE8A0,12)'))
        item(image=image.fluent(\uE8A1,default_icon_size) tip=['PreviewLink',tip.info] cmd=command.copy('image.fluent(\uE8A1,12)'))
        item(image=image.fluent(\uE8A2,default_icon_size) tip=['AttachCamera',tip.info] cmd=command.copy('image.fluent(\uE8A2,12)'))
        item(image=image.fluent(\uE8A3,default_icon_size) tip=['ZoomIn',tip.info] cmd=command.copy('image.fluent(\uE8A3,12)'))
        item(image=image.fluent(\uE8A4,default_icon_size) tip=['Bookmarks',tip.info] cmd=command.copy('image.fluent(\uE8A4,12)'))
        item(image=image.fluent(\uE8A5,default_icon_size) tip=['Document',tip.info] cmd=command.copy('image.fluent(\uE8A5,12)'))
        item(image=image.fluent(\uE8A6,default_icon_size) tip=['ProtectedDocument',tip.info] cmd=command.copy('image.fluent(\uE8A6,12)'))

        item(image=image.fluent(\uE8A7,default_icon_size) tip=['OpenInNewWindow',tip.info] cmd=command.copy('image.fluent(\uE8A7,12)') col)
        item(image=image.fluent(\uE8A8,default_icon_size) tip=['MailFill',tip.info] cmd=command.copy('image.fluent(\uE8A8,12)'))
        item(image=image.fluent(\uE8A9,default_icon_size) tip=['ViewAll',tip.info] cmd=command.copy('image.fluent(\uE8A9,12)'))
        item(image=image.fluent(\uE8AA,default_icon_size) tip=['VideoChat',tip.info] cmd=command.copy('image.fluent(\uE8AA,12)'))
        item(image=image.fluent(\uE8AB,default_icon_size) tip=['Switch',tip.info] cmd=command.copy('image.fluent(\uE8AB,12)'))
        item(image=image.fluent(\uE8AC,default_icon_size) tip=['Rename',tip.info] cmd=command.copy('image.fluent(\uE8AC,12)'))
        item(image=image.fluent(\uE8AD,default_icon_size) tip=['Go',tip.info] cmd=command.copy('image.fluent(\uE8AD,12)'))
        item(image=image.fluent(\uE8AE,default_icon_size) tip=['SurfaceHub',tip.info] cmd=command.copy('image.fluent(\uE8AE,12)'))
        item(image=image.fluent(\uE8AF,default_icon_size) tip=['Remote',tip.info] cmd=command.copy('image.fluent(\uE8AF,12)'))
        item(image=image.fluent(\uE8B0,default_icon_size) tip=['Click',tip.info] cmd=command.copy('image.fluent(\uE8B0,12)'))
        item(image=image.fluent(\uE8B1,default_icon_size) tip=['Shuffle',tip.info] cmd=command.copy('image.fluent(\uE8B1,12)'))
        item(image=image.fluent(\uE8B2,default_icon_size) tip=['Movies',tip.info] cmd=command.copy('image.fluent(\uE8B2,12)'))
        item(image=image.fluent(\uE8B3,default_icon_size) tip=['SelectAll',tip.info] cmd=command.copy('image.fluent(\uE8B3,12)'))
        item(image=image.fluent(\uE8B4,default_icon_size) tip=['Orientation',tip.info] cmd=command.copy('image.fluent(\uE8B4,12)'))
        item(image=image.fluent(\uE8B5,default_icon_size) tip=['Import',tip.info] cmd=command.copy('image.fluent(\uE8B5,12)'))
        item(image=image.fluent(\uE8B6,default_icon_size) tip=['ImportAll',tip.info] cmd=command.copy('image.fluent(\uE8B6,12)'))

        item(image=image.fluent(\uE8B7,default_icon_size) tip=['Folder',tip.info] cmd=command.copy('image.fluent(\uE8B7,12)') col)
        item(image=image.fluent(\uE8B8,default_icon_size) tip=['Webcam',tip.info] cmd=command.copy('image.fluent(\uE8B8,12)'))
        item(image=image.fluent(\uE8B9,default_icon_size) tip=['Picture',tip.info] cmd=command.copy('image.fluent(\uE8B9,12)'))
        item(image=image.fluent(\uE8BA,default_icon_size) tip=['Caption',tip.info] cmd=command.copy('image.fluent(\uE8BA,12)'))
        item(image=image.fluent(\uE8BB,default_icon_size) tip=['ChromeClose',tip.info] cmd=command.copy('image.fluent(\uE8BB,12)'))
        item(image=image.fluent(\uE8BC,default_icon_size) tip=['ShowResults',tip.info] cmd=command.copy('image.fluent(\uE8BC,12)'))
        item(image=image.fluent(\uE8BD,default_icon_size) tip=['Message',tip.info] cmd=command.copy('image.fluent(\uE8BD,12)'))
        item(image=image.fluent(\uE8BE,default_icon_size) tip=['Leaf',tip.info] cmd=command.copy('image.fluent(\uE8BE,12)'))
        item(image=image.fluent(\uE8BF,default_icon_size) tip=['CalendarDay',tip.info] cmd=command.copy('image.fluent(\uE8BF,12)'))
        item(image=image.fluent(\uE8C0,default_icon_size) tip=['CalendarWeek',tip.info] cmd=command.copy('image.fluent(\uE8C0,12)'))
        item(image=image.fluent(\uE8C1,default_icon_size) tip=['Characters',tip.info] cmd=command.copy('image.fluent(\uE8C1,12)'))
        item(image=image.fluent(\uE8C2,default_icon_size) tip=['MailReplyAll',tip.info] cmd=command.copy('image.fluent(\uE8C2,12)'))
        item(image=image.fluent(\uE8C3,default_icon_size) tip=['Read',tip.info] cmd=command.copy('image.fluent(\uE8C3,12)'))
        item(image=image.fluent(\uE8C4,default_icon_size) tip=['ShowBcc',tip.info] cmd=command.copy('image.fluent(\uE8C4,12)'))
        item(image=image.fluent(\uE8C5,default_icon_size) tip=['HideBcc',tip.info] cmd=command.copy('image.fluent(\uE8C5,12)'))
        item(image=image.fluent(\uE8C6,default_icon_size) tip=['Cut',tip.info] cmd=command.copy('image.fluent(\uE8C6,12)'))

        item(image=image.fluent(\uE8C7,default_icon_size) tip=['PaymentCard',tip.info] cmd=command.copy('image.fluent(\uE8C7,12)') col)
        item(image=image.fluent(\uE8C8,default_icon_size) tip=['Copy',tip.info] cmd=command.copy('image.fluent(\uE8C8,12)'))
        item(image=image.fluent(\uE8C9,default_icon_size) tip=['Important',tip.info] cmd=command.copy('image.fluent(\uE8C9,12)'))
        item(image=image.fluent(\uE8CA,default_icon_size) tip=['MailReply',tip.info] cmd=command.copy('image.fluent(\uE8CA,12)'))
        item(image=image.fluent(\uE8CB,default_icon_size) tip=['Sort',tip.info] cmd=command.copy('image.fluent(\uE8CB,12)'))
        item(image=image.fluent(\uE8CC,default_icon_size) tip=['MobileTablet',tip.info] cmd=command.copy('image.fluent(\uE8CC,12)'))
        item(image=image.fluent(\uE8CD,default_icon_size) tip=['DisconnectDrive',tip.info] cmd=command.copy('image.fluent(\uE8CD,12)'))
        item(image=image.fluent(\uE8CE,default_icon_size) tip=['MapDrive',tip.info] cmd=command.copy('image.fluent(\uE8CE,12)'))
        item(image=image.fluent(\uE8CF,default_icon_size) tip=['ContactPresence',tip.info] cmd=command.copy('image.fluent(\uE8CF,12)'))
        item(image=image.fluent(\uE8D0,default_icon_size) tip=['Priority',tip.info] cmd=command.copy('image.fluent(\uE8D0,12)'))
        item(image=image.fluent(\uE8D1,default_icon_size) tip=['GotoToday',tip.info] cmd=command.copy('image.fluent(\uE8D1,12)'))
        item(image=image.fluent(\uE8D2,default_icon_size) tip=['Font',tip.info] cmd=command.copy('image.fluent(\uE8D2,12)'))
        item(image=image.fluent(\uE8D3,default_icon_size) tip=['FontColor',tip.info] cmd=command.copy('image.fluent(\uE8D3,12)'))
        item(image=image.fluent(\uE8D4,default_icon_size) tip=['Contact2',tip.info] cmd=command.copy('image.fluent(\uE8D4,12)'))
        item(image=image.fluent(\uE8D5,default_icon_size) tip=['FolderFill',tip.info] cmd=command.copy('image.fluent(\uE8D5,12)'))
        item(image=image.fluent(\uE8D6,default_icon_size) tip=['Audio',tip.info] cmd=command.copy('image.fluent(\uE8D6,12)'))

        item(image=image.fluent(\uE8D7,default_icon_size) tip=['Permissions',tip.info] cmd=command.copy('image.fluent(\uE8D7,12)') col)
        item(image=image.fluent(\uE8D8,default_icon_size) tip=['DisableUpdates',tip.info] cmd=command.copy('image.fluent(\uE8D8,12)'))
        item(image=image.fluent(\uE8D9,default_icon_size) tip=['Unfavorite',tip.info] cmd=command.copy('image.fluent(\uE8D9,12)'))
        item(image=image.fluent(\uE8DA,default_icon_size) tip=['OpenLocal',tip.info] cmd=command.copy('image.fluent(\uE8DA,12)'))
        item(image=image.fluent(\uE8DB,default_icon_size) tip=['Italic',tip.info] cmd=command.copy('image.fluent(\uE8DB,12)'))
        item(image=image.fluent(\uE8DC,default_icon_size) tip=['Underline',tip.info] cmd=command.copy('image.fluent(\uE8DC,12)'))
        item(image=image.fluent(\uE8DD,default_icon_size) tip=['Bold',tip.info] cmd=command.copy('image.fluent(\uE8DD,12)'))
        item(image=image.fluent(\uE8DE,default_icon_size) tip=['MoveToFolder',tip.info] cmd=command.copy('image.fluent(\uE8DE,12)'))
        item(image=image.fluent(\uE8DF,default_icon_size) tip=['LikeDislike',tip.info] cmd=command.copy('image.fluent(\uE8DF,12)'))
        item(image=image.fluent(\uE8E0,default_icon_size) tip=['Dislike',tip.info] cmd=command.copy('image.fluent(\uE8E0,12)'))
        item(image=image.fluent(\uE8E1,default_icon_size) tip=['Like',tip.info] cmd=command.copy('image.fluent(\uE8E1,12)'))
        item(image=image.fluent(\uE8E2,default_icon_size) tip=['AlignRight',tip.info] cmd=command.copy('image.fluent(\uE8E2,12)'))
        item(image=image.fluent(\uE8E3,default_icon_size) tip=['AlignCenter',tip.info] cmd=command.copy('image.fluent(\uE8E3,12)'))
        item(image=image.fluent(\uE8E4,default_icon_size) tip=['AlignLeft',tip.info] cmd=command.copy('image.fluent(\uE8E4,12)'))
        item(image=image.fluent(\uE8E5,default_icon_size) tip=['OpenFile',tip.info] cmd=command.copy('image.fluent(\uE8E5,12)'))
        item(image=image.fluent(\uE8E6,default_icon_size) tip=['ClearSelection',tip.info] cmd=command.copy('image.fluent(\uE8E6,12)'))

        item(image=image.fluent(\uE8E7,default_icon_size) tip=['FontDecrease',tip.info] cmd=command.copy('image.fluent(\uE8E7,12)') col)
        item(image=image.fluent(\uE8E8,default_icon_size) tip=['FontIncrease',tip.info] cmd=command.copy('image.fluent(\uE8E8,12)'))
        item(image=image.fluent(\uE8E9,default_icon_size) tip=['FontSize',tip.info] cmd=command.copy('image.fluent(\uE8E9,12)'))
        item(image=image.fluent(\uE8EA,default_icon_size) tip=['CellPhone',tip.info] cmd=command.copy('image.fluent(\uE8EA,12)'))
        item(image=image.fluent(\uE8EB,default_icon_size) tip=['Reshare',tip.info] cmd=command.copy('image.fluent(\uE8EB,12)'))
        item(image=image.fluent(\uE8EC,default_icon_size) tip=['Tag',tip.info] cmd=command.copy('image.fluent(\uE8EC,12)'))
        item(image=image.fluent(\uE8ED,default_icon_size) tip=['RepeatOne',tip.info] cmd=command.copy('image.fluent(\uE8ED,12)'))
        item(image=image.fluent(\uE8EE,default_icon_size) tip=['RepeatAll',tip.info] cmd=command.copy('image.fluent(\uE8EE,12)'))
        item(image=image.fluent(\uE8EF,default_icon_size) tip=['Calculator',tip.info] cmd=command.copy('image.fluent(\uE8EF,12)'))
        item(image=image.fluent(\uE8F0,default_icon_size) tip=['Directions',tip.info] cmd=command.copy('image.fluent(\uE8F0,12)'))
        item(image=image.fluent(\uE8F1,default_icon_size) tip=['Library',tip.info] cmd=command.copy('image.fluent(\uE8F1,12)'))
        item(image=image.fluent(\uE8F2,default_icon_size) tip=['ChatBubbles',tip.info] cmd=command.copy('image.fluent(\uE8F2,12)'))
        item(image=image.fluent(\uE8F3,default_icon_size) tip=['PostUpdate',tip.info] cmd=command.copy('image.fluent(\uE8F3,12)'))
        item(image=image.fluent(\uE8F4,default_icon_size) tip=['NewFolder',tip.info] cmd=command.copy('image.fluent(\uE8F4,12)'))
        item(image=image.fluent(\uE8F5,default_icon_size) tip=['CalendarReply',tip.info] cmd=command.copy('image.fluent(\uE8F5,12)'))
        item(image=image.fluent(\uE8F6,default_icon_size) tip=['UnsyncFolder',tip.info] cmd=command.copy('image.fluent(\uE8F6,12)'))

        item(image=image.fluent(\uE8F7,default_icon_size) tip=['SyncFolder',tip.info] cmd=command.copy('image.fluent(\uE8F7,12)') col)
        item(image=image.fluent(\uE8F8,default_icon_size) tip=['BlockContact',tip.info] cmd=command.copy('image.fluent(\uE8F8,12)'))
        item(image=image.fluent(\uE8F9,default_icon_size) tip=['SwitchApps',tip.info] cmd=command.copy('image.fluent(\uE8F9,12)'))
        item(image=image.fluent(\uE8FA,default_icon_size) tip=['AddFriend',tip.info] cmd=command.copy('image.fluent(\uE8FA,12)'))
        item(image=image.fluent(\uE8FB,default_icon_size) tip=['Accept',tip.info] cmd=command.copy('image.fluent(\uE8FB,12)'))
        item(image=image.fluent(\uE8FC,default_icon_size) tip=['GoToStart',tip.info] cmd=command.copy('image.fluent(\uE8FC,12)'))
        item(image=image.fluent(\uE8FD,default_icon_size) tip=['BulletedList',tip.info] cmd=command.copy('image.fluent(\uE8FD,12)'))
        item(image=image.fluent(\uE8FE,default_icon_size) tip=['Scan',tip.info] cmd=command.copy('image.fluent(\uE8FE,12)'))
        item(image=image.fluent(\uE8FF,default_icon_size) tip=['Preview',tip.info] cmd=command.copy('image.fluent(\uE8FF,12)'))
        item(image=image.fluent(\uE902,default_icon_size) tip=['Group',tip.info] cmd=command.copy('image.fluent(\uE902,12)'))
        item(image=image.fluent(\uE904,default_icon_size) tip=['ZeroBars',tip.info] cmd=command.copy('image.fluent(\uE904,12)'))
        item(image=image.fluent(\uE905,default_icon_size) tip=['OneBar',tip.info] cmd=command.copy('image.fluent(\uE905,12)'))
        item(image=image.fluent(\uE906,default_icon_size) tip=['TwoBars',tip.info] cmd=command.copy('image.fluent(\uE906,12)'))
        item(image=image.fluent(\uE907,default_icon_size) tip=['ThreeBars',tip.info] cmd=command.copy('image.fluent(\uE907,12)'))
        item(image=image.fluent(\uE908,default_icon_size) tip=['FourBars',tip.info] cmd=command.copy('image.fluent(\uE908,12)'))
        item(image=image.fluent(\uE909,default_icon_size) tip=['World',tip.info] cmd=command.copy('image.fluent(\uE909,12)'))

        item(image=image.fluent(\uE90A,default_icon_size) tip=['Comment',tip.info] cmd=command.copy('image.fluent(\uE90A,12)') col)
        item(image=image.fluent(\uE90B,default_icon_size) tip=['MusicInfo',tip.info] cmd=command.copy('image.fluent(\uE90B,12)'))
        item(image=image.fluent(\uE90C,default_icon_size) tip=['DockLeft',tip.info] cmd=command.copy('image.fluent(\uE90C,12)'))
        item(image=image.fluent(\uE90D,default_icon_size) tip=['DockRight',tip.info] cmd=command.copy('image.fluent(\uE90D,12)'))
        item(image=image.fluent(\uE90E,default_icon_size) tip=['DockBottom',tip.info] cmd=command.copy('image.fluent(\uE90E,12)'))
        item(image=image.fluent(\uE90F,default_icon_size) tip=['Repair',tip.info] cmd=command.copy('image.fluent(\uE90F,12)'))
        item(image=image.fluent(\uE910,default_icon_size) tip=['Accounts',tip.info] cmd=command.copy('image.fluent(\uE910,12)'))
        item(image=image.fluent(\uE911,default_icon_size) tip=['DullSound',tip.info] cmd=command.copy('image.fluent(\uE911,12)'))
        item(image=image.fluent(\uE912,default_icon_size) tip=['Manage',tip.info] cmd=command.copy('image.fluent(\uE912,12)'))
        item(image=image.fluent(\uE913,default_icon_size) tip=['Street',tip.info] cmd=command.copy('image.fluent(\uE913,12)'))
        item(image=image.fluent(\uE914,default_icon_size) tip=['Printer3D',tip.info] cmd=command.copy('image.fluent(\uE914,12)'))
        item(image=image.fluent(\uE915,default_icon_size) tip=['RadioBullet',tip.info] cmd=command.copy('image.fluent(\uE915,12)'))
        item(image=image.fluent(\uE916,default_icon_size) tip=['Stopwatch',tip.info] cmd=command.copy('image.fluent(\uE916,12)'))
        item(image=image.fluent(\uE91B,default_icon_size) tip=['Photo',tip.info] cmd=command.copy('image.fluent(\uE91B,12)'))
        item(image=image.fluent(\uE91C,default_icon_size) tip=['ActionCenter',tip.info] cmd=command.copy('image.fluent(\uE91C,12)'))
        item(image=image.fluent(\uE91F,default_icon_size) tip=['FullCircleMask',tip.info] cmd=command.copy('image.fluent(\uE91F,12)'))

        item(image=image.fluent(\uE921,default_icon_size) tip=['ChromeMinimize',tip.info] cmd=command.copy('image.fluent(\uE921,12)') col)
        item(image=image.fluent(\uE922,default_icon_size) tip=['ChromeMaximize',tip.info] cmd=command.copy('image.fluent(\uE922,12)'))
        item(image=image.fluent(\uE923,default_icon_size) tip=['ChromeRestore',tip.info] cmd=command.copy('image.fluent(\uE923,12)'))
        item(image=image.fluent(\uE924,default_icon_size) tip=['Annotation',tip.info] cmd=command.copy('image.fluent(\uE924,12)'))
        item(image=image.fluent(\uE925,default_icon_size) tip=['BackSpaceQWERTYSm',tip.info] cmd=command.copy('image.fluent(\uE925,12)'))
        item(image=image.fluent(\uE926,default_icon_size) tip=['BackSpaceQWERTYMd',tip.info] cmd=command.copy('image.fluent(\uE926,12)'))
        item(image=image.fluent(\uE927,default_icon_size) tip=['Swipe',tip.info] cmd=command.copy('image.fluent(\uE927,12)'))
        item(image=image.fluent(\uE928,default_icon_size) tip=['Fingerprint',tip.info] cmd=command.copy('image.fluent(\uE928,12)'))
        item(image=image.fluent(\uE929,default_icon_size) tip=['Handwriting',tip.info] cmd=command.copy('image.fluent(\uE929,12)'))
        item(image=image.fluent(\uE92C,default_icon_size) tip=['ChromeBackToWindow',tip.info] cmd=command.copy('image.fluent(\uE92C,12)'))
        item(image=image.fluent(\uE92D,default_icon_size) tip=['ChromeFullScreen',tip.info] cmd=command.copy('image.fluent(\uE92D,12)'))
        item(image=image.fluent(\uE92E,default_icon_size) tip=['KeyboardStandard',tip.info] cmd=command.copy('image.fluent(\uE92E,12)'))
        item(image=image.fluent(\uE92F,default_icon_size) tip=['KeyboardDismiss',tip.info] cmd=command.copy('image.fluent(\uE92F,12)'))
        item(image=image.fluent(\uE930,default_icon_size) tip=['Completed',tip.info] cmd=command.copy('image.fluent(\uE930,12)'))
        item(image=image.fluent(\uE931,default_icon_size) tip=['ChromeAnnotate',tip.info] cmd=command.copy('image.fluent(\uE931,12)'))
        item(image=image.fluent(\uE932,default_icon_size) tip=['Label',tip.info] cmd=command.copy('image.fluent(\uE932,12)'))

        item(image=image.fluent(\uE933,default_icon_size) tip=['IBeam',tip.info] cmd=command.copy('image.fluent(\uE933,12)') col)
        item(image=image.fluent(\uE934,default_icon_size) tip=['IBeamOutline',tip.info] cmd=command.copy('image.fluent(\uE934,12)'))
        item(image=image.fluent(\uE935,default_icon_size) tip=['FlickDown',tip.info] cmd=command.copy('image.fluent(\uE935,12)'))
        item(image=image.fluent(\uE936,default_icon_size) tip=['FlickUp',tip.info] cmd=command.copy('image.fluent(\uE936,12)'))
        item(image=image.fluent(\uE937,default_icon_size) tip=['FlickLeft',tip.info] cmd=command.copy('image.fluent(\uE937,12)'))
        item(image=image.fluent(\uE938,default_icon_size) tip=['FlickRight',tip.info] cmd=command.copy('image.fluent(\uE938,12)'))
        item(image=image.fluent(\uE939,default_icon_size) tip=['FeedbackApp',tip.info] cmd=command.copy('image.fluent(\uE939,12)'))
        item(image=image.fluent(\uE93C,default_icon_size) tip=['MusicAlbum',tip.info] cmd=command.copy('image.fluent(\uE93C,12)'))
        item(image=image.fluent(\uE93E,default_icon_size) tip=['Streaming',tip.info] cmd=command.copy('image.fluent(\uE93E,12)'))
        item(image=image.fluent(\uE943,default_icon_size) tip=['Code',tip.info] cmd=command.copy('image.fluent(\uE943,12)'))
        item(image=image.fluent(\uE944,default_icon_size) tip=['ReturnToWindow',tip.info] cmd=command.copy('image.fluent(\uE944,12)'))
        item(image=image.fluent(\uE945,default_icon_size) tip=['LightningBolt',tip.info] cmd=command.copy('image.fluent(\uE945,12)'))
        item(image=image.fluent(\uE946,default_icon_size) tip=['Info',tip.info] cmd=command.copy('image.fluent(\uE946,12)'))
        item(image=image.fluent(\uE947,default_icon_size) tip=['CalculatorMultiply',tip.info] cmd=command.copy('image.fluent(\uE947,12)'))
        item(image=image.fluent(\uE948,default_icon_size) tip=['CalculatorAddition',tip.info] cmd=command.copy('image.fluent(\uE948,12)'))
        item(image=image.fluent(\uE949,default_icon_size) tip=['CalculatorSubtract',tip.info] cmd=command.copy('image.fluent(\uE949,12)'))

        item(image=image.fluent(\uE94A,default_icon_size) tip=['CalculatorDivide',tip.info] cmd=command.copy('image.fluent(\uE94A,12)') col)
        item(image=image.fluent(\uE94B,default_icon_size) tip=['CalculatorSquareroot',tip.info] cmd=command.copy('image.fluent(\uE94B,12)'))
        item(image=image.fluent(\uE94C,default_icon_size) tip=['CalculatorPercentage',tip.info] cmd=command.copy('image.fluent(\uE94C,12)'))
        item(image=image.fluent(\uE94D,default_icon_size) tip=['CalculatorNegate',tip.info] cmd=command.copy('image.fluent(\uE94D,12)'))
        item(image=image.fluent(\uE94E,default_icon_size) tip=['CalculatorEqualTo',tip.info] cmd=command.copy('image.fluent(\uE94E,12)'))
        item(image=image.fluent(\uE94F,default_icon_size) tip=['CalculatorBackspace',tip.info] cmd=command.copy('image.fluent(\uE94F,12)'))
        item(image=image.fluent(\uE950,default_icon_size) tip=['Component',tip.info] cmd=command.copy('image.fluent(\uE950,12)'))
        item(image=image.fluent(\uE951,default_icon_size) tip=['DMC',tip.info] cmd=command.copy('image.fluent(\uE951,12)'))
        item(image=image.fluent(\uE952,default_icon_size) tip=['Dock',tip.info] cmd=command.copy('image.fluent(\uE952,12)'))
        item(image=image.fluent(\uE953,default_icon_size) tip=['MultimediaDMS',tip.info] cmd=command.copy('image.fluent(\uE953,12)'))
        item(image=image.fluent(\uE954,default_icon_size) tip=['MultimediaDVR',tip.info] cmd=command.copy('image.fluent(\uE954,12)'))
        item(image=image.fluent(\uE955,default_icon_size) tip=['MultimediaPMP',tip.info] cmd=command.copy('image.fluent(\uE955,12)'))
        item(image=image.fluent(\uE956,default_icon_size) tip=['PrintfaxPrinterFile',tip.info] cmd=command.copy('image.fluent(\uE956,12)'))
        item(image=image.fluent(\uE957,default_icon_size) tip=['Sensor',tip.info] cmd=command.copy('image.fluent(\uE957,12)'))
        item(image=image.fluent(\uE958,default_icon_size) tip=['StorageOptical',tip.info] cmd=command.copy('image.fluent(\uE958,12)'))
        item(image=image.fluent(\uE95A,default_icon_size) tip=['Communications',tip.info] cmd=command.copy('image.fluent(\uE95A,12)'))

        item(image=image.fluent(\uE95B,default_icon_size) tip=['Headset',tip.info] cmd=command.copy('image.fluent(\uE95B,12)') col)
        item(image=image.fluent(\uE95D,default_icon_size) tip=['Projector',tip.info] cmd=command.copy('image.fluent(\uE95D,12)'))
        item(image=image.fluent(\uE95E,default_icon_size) tip=['Health',tip.info] cmd=command.copy('image.fluent(\uE95E,12)'))
        item(image=image.fluent(\uE95F,default_icon_size) tip=['Wire',tip.info] cmd=command.copy('image.fluent(\uE95F,12)'))
        item(image=image.fluent(\uE960,default_icon_size) tip=['Webcam2',tip.info] cmd=command.copy('image.fluent(\uE960,12)'))
        item(image=image.fluent(\uE961,default_icon_size) tip=['Input',tip.info] cmd=command.copy('image.fluent(\uE961,12)'))
        item(image=image.fluent(\uE962,default_icon_size) tip=['Mouse',tip.info] cmd=command.copy('image.fluent(\uE962,12)'))
        item(image=image.fluent(\uE963,default_icon_size) tip=['Smartcard',tip.info] cmd=command.copy('image.fluent(\uE963,12)'))
        item(image=image.fluent(\uE964,default_icon_size) tip=['SmartcardVirtual',tip.info] cmd=command.copy('image.fluent(\uE964,12)'))
        item(image=image.fluent(\uE965,default_icon_size) tip=['MediaStorageTower',tip.info] cmd=command.copy('image.fluent(\uE965,12)'))
        item(image=image.fluent(\uE966,default_icon_size) tip=['ReturnKeySm',tip.info] cmd=command.copy('image.fluent(\uE966,12)'))
        item(image=image.fluent(\uE967,default_icon_size) tip=['GameConsole',tip.info] cmd=command.copy('image.fluent(\uE967,12)'))
        item(image=image.fluent(\uE968,default_icon_size) tip=['Network',tip.info] cmd=command.copy('image.fluent(\uE968,12)'))
        item(image=image.fluent(\uE969,default_icon_size) tip=['StorageNetworkWireless',tip.info] cmd=command.copy('image.fluent(\uE969,12)'))
        item(image=image.fluent(\uE96A,default_icon_size) tip=['StorageTape',tip.info] cmd=command.copy('image.fluent(\uE96A,12)'))
        item(image=image.fluent(\uE96D,default_icon_size) tip=['ChevronUpSmall',tip.info] cmd=command.copy('image.fluent(\uE96D,12)'))
    }

    menu(title='Fluent #3')
    {
        item(image=image.fluent(\uE96E,default_icon_size) tip=['ChevronDownSmall',tip.info] cmd=command.copy('image.fluent(\uE96E,12)'))
        item(image=image.fluent(\uE96F,default_icon_size) tip=['ChevronLeftSmall',tip.info] cmd=command.copy('image.fluent(\uE96F,12)'))
        item(image=image.fluent(\uE970,default_icon_size) tip=['ChevronRightSmall',tip.info] cmd=command.copy('image.fluent(\uE970,12)'))
        item(image=image.fluent(\uE971,default_icon_size) tip=['ChevronUpMed',tip.info] cmd=command.copy('image.fluent(\uE971,12)'))
        item(image=image.fluent(\uE972,default_icon_size) tip=['ChevronDownMed',tip.info] cmd=command.copy('image.fluent(\uE972,12)'))
        item(image=image.fluent(\uE973,default_icon_size) tip=['ChevronLeftMed',tip.info] cmd=command.copy('image.fluent(\uE973,12)'))
        item(image=image.fluent(\uE974,default_icon_size) tip=['ChevronRightMed',tip.info] cmd=command.copy('image.fluent(\uE974,12)'))
        item(image=image.fluent(\uE975,default_icon_size) tip=['Devices2',tip.info] cmd=command.copy('image.fluent(\uE975,12)'))
        item(image=image.fluent(\uE976,default_icon_size) tip=['ExpandTile',tip.info] cmd=command.copy('image.fluent(\uE976,12)'))
        item(image=image.fluent(\uE977,default_icon_size) tip=['PC1',tip.info] cmd=command.copy('image.fluent(\uE977,12)'))
        item(image=image.fluent(\uE978,default_icon_size) tip=['PresenceChicklet',tip.info] cmd=command.copy('image.fluent(\uE978,12)'))
        item(image=image.fluent(\uE979,default_icon_size) tip=['PresenceChickletVideo',tip.info] cmd=command.copy('image.fluent(\uE979,12)'))
        item(image=image.fluent(\uE97A,default_icon_size) tip=['Reply',tip.info] cmd=command.copy('image.fluent(\uE97A,12)'))
        item(image=image.fluent(\uE97B,default_icon_size) tip=['SetTile',tip.info] cmd=command.copy('image.fluent(\uE97B,12)'))
        item(image=image.fluent(\uE97C,default_icon_size) tip=['Type',tip.info] cmd=command.copy('image.fluent(\uE97C,12)'))
        item(image=image.fluent(\uE97D,default_icon_size) tip=['Korean',tip.info] cmd=command.copy('image.fluent(\uE97D,12)'))

        item(image=image.fluent(\uE97E,default_icon_size) tip=['HalfAlpha',tip.info] cmd=command.copy('image.fluent(\uE97E,12)') col)
        item(image=image.fluent(\uE97F,default_icon_size) tip=['FullAlpha',tip.info] cmd=command.copy('image.fluent(\uE97F,12)'))
        item(image=image.fluent(\uE980,default_icon_size) tip=['Key12On',tip.info] cmd=command.copy('image.fluent(\uE980,12)'))
        item(image=image.fluent(\uE981,default_icon_size) tip=['ChineseChangjie',tip.info] cmd=command.copy('image.fluent(\uE981,12)'))
        item(image=image.fluent(\uE982,default_icon_size) tip=['QWERTYOn',tip.info] cmd=command.copy('image.fluent(\uE982,12)'))
        item(image=image.fluent(\uE983,default_icon_size) tip=['QWERTYOff',tip.info] cmd=command.copy('image.fluent(\uE983,12)'))
        item(image=image.fluent(\uE984,default_icon_size) tip=['ChineseQuick',tip.info] cmd=command.copy('image.fluent(\uE984,12)'))
        item(image=image.fluent(\uE985,default_icon_size) tip=['Japanese',tip.info] cmd=command.copy('image.fluent(\uE985,12)'))
        item(image=image.fluent(\uE986,default_icon_size) tip=['FullHiragana',tip.info] cmd=command.copy('image.fluent(\uE986,12)'))
        item(image=image.fluent(\uE987,default_icon_size) tip=['FullKatakana',tip.info] cmd=command.copy('image.fluent(\uE987,12)'))
        item(image=image.fluent(\uE988,default_icon_size) tip=['HalfKatakana',tip.info] cmd=command.copy('image.fluent(\uE988,12)'))
        item(image=image.fluent(\uE989,default_icon_size) tip=['ChineseBoPoMoFo',tip.info] cmd=command.copy('image.fluent(\uE989,12)'))
        item(image=image.fluent(\uE98A,default_icon_size) tip=['ChinesePinyin',tip.info] cmd=command.copy('image.fluent(\uE98A,12)'))
        item(image=image.fluent(\uE98F,default_icon_size) tip=['ConstructionCone',tip.info] cmd=command.copy('image.fluent(\uE98F,12)'))
        item(image=image.fluent(\uE990,default_icon_size) tip=['XboxOneConsole',tip.info] cmd=command.copy('image.fluent(\uE990,12)'))
        item(image=image.fluent(\uE992,default_icon_size) tip=['Volume0',tip.info] cmd=command.copy('image.fluent(\uE992,12)'))

        item(image=image.fluent(\uE993,default_icon_size) tip=['Volume1',tip.info] cmd=command.copy('image.fluent(\uE993,12)') col)
        item(image=image.fluent(\uE994,default_icon_size) tip=['Volume2',tip.info] cmd=command.copy('image.fluent(\uE994,12)'))
        item(image=image.fluent(\uE995,default_icon_size) tip=['Volume3',tip.info] cmd=command.copy('image.fluent(\uE995,12)'))
        item(image=image.fluent(\uE996,default_icon_size) tip=['BatteryUnknown',tip.info] cmd=command.copy('image.fluent(\uE996,12)'))
        item(image=image.fluent(\uE998,default_icon_size) tip=['WifiAttentionOverlay',tip.info] cmd=command.copy('image.fluent(\uE998,12)'))
        item(image=image.fluent(\uE99A,default_icon_size) tip=['Robot',tip.info] cmd=command.copy('image.fluent(\uE99A,12)'))
        item(image=image.fluent(\uE9A1,default_icon_size) tip=['TapAndSend',tip.info] cmd=command.copy('image.fluent(\uE9A1,12)'))
        item(image=image.fluent(\uE9A6,default_icon_size) tip=['FitPage',tip.info] cmd=command.copy('image.fluent(\uE9A6,12)'))
        item(image=image.fluent(\uE9A8,default_icon_size) tip=['PasswordKeyShow',tip.info] cmd=command.copy('image.fluent(\uE9A8,12)'))
        item(image=image.fluent(\uE9A9,default_icon_size) tip=['PasswordKeyHide',tip.info] cmd=command.copy('image.fluent(\uE9A9,12)'))
        item(image=image.fluent(\uE9AA,default_icon_size) tip=['BidiLtr',tip.info] cmd=command.copy('image.fluent(\uE9AA,12)'))
        item(image=image.fluent(\uE9AB,default_icon_size) tip=['BidiRtl',tip.info] cmd=command.copy('image.fluent(\uE9AB,12)'))
        item(image=image.fluent(\uE9AC,default_icon_size) tip=['ForwardSm',tip.info] cmd=command.copy('image.fluent(\uE9AC,12)'))
        item(image=image.fluent(\uE9AD,default_icon_size) tip=['CommaKey',tip.info] cmd=command.copy('image.fluent(\uE9AD,12)'))
        item(image=image.fluent(\uE9AE,default_icon_size) tip=['DashKey',tip.info] cmd=command.copy('image.fluent(\uE9AE,12)'))
        item(image=image.fluent(\uE9AF,default_icon_size) tip=['DullSoundKey',tip.info] cmd=command.copy('image.fluent(\uE9AF,12)'))

        item(image=image.fluent(\uE9B0,default_icon_size) tip=['HalfDullSound',tip.info] cmd=command.copy('image.fluent(\uE9B0,12)') col)
        item(image=image.fluent(\uE9B1,default_icon_size) tip=['RightDoubleQuote',tip.info] cmd=command.copy('image.fluent(\uE9B1,12)'))
        item(image=image.fluent(\uE9B2,default_icon_size) tip=['LeftDoubleQuote',tip.info] cmd=command.copy('image.fluent(\uE9B2,12)'))
        item(image=image.fluent(\uE9B3,default_icon_size) tip=['PuncKeyRightBottom',tip.info] cmd=command.copy('image.fluent(\uE9B3,12)'))
        item(image=image.fluent(\uE9B4,default_icon_size) tip=['PuncKey1',tip.info] cmd=command.copy('image.fluent(\uE9B4,12)'))
        item(image=image.fluent(\uE9B5,default_icon_size) tip=['PuncKey2',tip.info] cmd=command.copy('image.fluent(\uE9B5,12)'))
        item(image=image.fluent(\uE9B6,default_icon_size) tip=['PuncKey3',tip.info] cmd=command.copy('image.fluent(\uE9B6,12)'))
        item(image=image.fluent(\uE9B7,default_icon_size) tip=['PuncKey4',tip.info] cmd=command.copy('image.fluent(\uE9B7,12)'))
        item(image=image.fluent(\uE9B8,default_icon_size) tip=['PuncKey5',tip.info] cmd=command.copy('image.fluent(\uE9B8,12)'))
        item(image=image.fluent(\uE9B9,default_icon_size) tip=['PuncKey6',tip.info] cmd=command.copy('image.fluent(\uE9B9,12)'))
        item(image=image.fluent(\uE9BA,default_icon_size) tip=['PuncKey9',tip.info] cmd=command.copy('image.fluent(\uE9BA,12)'))
        item(image=image.fluent(\uE9BB,default_icon_size) tip=['PuncKey7',tip.info] cmd=command.copy('image.fluent(\uE9BB,12)'))
        item(image=image.fluent(\uE9BC,default_icon_size) tip=['PuncKey8',tip.info] cmd=command.copy('image.fluent(\uE9BC,12)'))
        item(image=image.fluent(\uE9CA,default_icon_size) tip=['Frigid',tip.info] cmd=command.copy('image.fluent(\uE9CA,12)'))
        item(image=image.fluent(\uE9CE,default_icon_size) tip=['Unknown',tip.info] cmd=command.copy('image.fluent(\uE9CE,12)'))
        item(image=image.fluent(\uE9D2,default_icon_size) tip=['AreaChart',tip.info] cmd=command.copy('image.fluent(\uE9D2,12)'))

        item(image=image.fluent(\uE9D5,default_icon_size) tip=['CheckList',tip.info] cmd=command.copy('image.fluent(\uE9D5,12)') col)
        item(image=image.fluent(\uE9D9,default_icon_size) tip=['Diagnostic',tip.info] cmd=command.copy('image.fluent(\uE9D9,12)'))
        item(image=image.fluent(\uE9E9,default_icon_size) tip=['Equalizer',tip.info] cmd=command.copy('image.fluent(\uE9E9,12)'))
        item(image=image.fluent(\uE9F3,default_icon_size) tip=['Process',tip.info] cmd=command.copy('image.fluent(\uE9F3,12)'))
        item(image=image.fluent(\uE9F5,default_icon_size) tip=['Processing',tip.info] cmd=command.copy('image.fluent(\uE9F5,12)'))
        item(image=image.fluent(\uE9F9,default_icon_size) tip=['ReportDocument',tip.info] cmd=command.copy('image.fluent(\uE9F9,12)'))
        item(image=image.fluent(\uEA0C,default_icon_size) tip=['VideoSolid',tip.info] cmd=command.copy('image.fluent(\uEA0C,12)'))
        item(image=image.fluent(\uEA0D,default_icon_size) tip=['MixedMediaBadge',tip.info] cmd=command.copy('image.fluent(\uEA0D,12)'))
        item(image=image.fluent(\uEA14,default_icon_size) tip=['DisconnectDisplay',tip.info] cmd=command.copy('image.fluent(\uEA14,12)'))
        item(image=image.fluent(\uEA18,default_icon_size) tip=['Shield',tip.info] cmd=command.copy('image.fluent(\uEA18,12)'))
        item(image=image.fluent(\uEA1F,default_icon_size) tip=['Info2',tip.info] cmd=command.copy('image.fluent(\uEA1F,12)'))
        item(image=image.fluent(\uEA21,default_icon_size) tip=['ActionCenterAsterisk',tip.info] cmd=command.copy('image.fluent(\uEA21,12)'))
        item(image=image.fluent(\uEA24,default_icon_size) tip=['Beta',tip.info] cmd=command.copy('image.fluent(\uEA24,12)'))
        item(image=image.fluent(\uEA35,default_icon_size) tip=['SaveCopy',tip.info] cmd=command.copy('image.fluent(\uEA35,12)'))
        item(image=image.fluent(\uEA37,default_icon_size) tip=['List',tip.info] cmd=command.copy('image.fluent(\uEA37,12)'))
        item(image=image.fluent(\uEA38,default_icon_size) tip=['Asterisk',tip.info] cmd=command.copy('image.fluent(\uEA38,12)'))

        item(image=image.fluent(\uEA39,default_icon_size) tip=['ErrorBadge',tip.info] cmd=command.copy('image.fluent(\uEA39,12)') col)
        item(image=image.fluent(\uEA3A,default_icon_size) tip=['CircleRing',tip.info] cmd=command.copy('image.fluent(\uEA3A,12)'))
        item(image=image.fluent(\uEA3B,default_icon_size) tip=['CircleFill',tip.info] cmd=command.copy('image.fluent(\uEA3B,12)'))
        item(image=image.fluent(\uEA3C,default_icon_size) tip=['MergeCall',tip.info] cmd=command.copy('image.fluent(\uEA3C,12)'))
        item(image=image.fluent(\uEA3D,default_icon_size) tip=['PrivateCall',tip.info] cmd=command.copy('image.fluent(\uEA3D,12)'))
        item(image=image.fluent(\uEA3F,default_icon_size) tip=['Record2',tip.info] cmd=command.copy('image.fluent(\uEA3F,12)'))
        item(image=image.fluent(\uEA40,default_icon_size) tip=['AllAppsMirrored',tip.info] cmd=command.copy('image.fluent(\uEA40,12)'))
        item(image=image.fluent(\uEA41,default_icon_size) tip=['BookmarksMirrored',tip.info] cmd=command.copy('image.fluent(\uEA41,12)'))
        item(image=image.fluent(\uEA42,default_icon_size) tip=['BulletedListMirrored',tip.info] cmd=command.copy('image.fluent(\uEA42,12)'))
        item(image=image.fluent(\uEA43,default_icon_size) tip=['CallForwardInternationalMirrored',tip.info] cmd=command.copy('image.fluent(\uEA43,12)'))
        item(image=image.fluent(\uEA44,default_icon_size) tip=['CallForwardRoamingMirrored',tip.info] cmd=command.copy('image.fluent(\uEA44,12)'))
        item(image=image.fluent(\uEA47,default_icon_size) tip=['ChromeBackMirrored',tip.info] cmd=command.copy('image.fluent(\uEA47,12)'))
        item(image=image.fluent(\uEA48,default_icon_size) tip=['ClearSelectionMirrored',tip.info] cmd=command.copy('image.fluent(\uEA48,12)'))
        item(image=image.fluent(\uEA49,default_icon_size) tip=['ClosePaneMirrored',tip.info] cmd=command.copy('image.fluent(\uEA49,12)'))
        item(image=image.fluent(\uEA4A,default_icon_size) tip=['ContactInfoMirrored',tip.info] cmd=command.copy('image.fluent(\uEA4A,12)'))
        item(image=image.fluent(\uEA4B,default_icon_size) tip=['DockRightMirrored',tip.info] cmd=command.copy('image.fluent(\uEA4B,12)'))

        item(image=image.fluent(\uEA4C,default_icon_size) tip=['DockLeftMirrored',tip.info] cmd=command.copy('image.fluent(\uEA4C,12)') col)
        item(image=image.fluent(\uEA4E,default_icon_size) tip=['ExpandTileMirrored',tip.info] cmd=command.copy('image.fluent(\uEA4E,12)'))
        item(image=image.fluent(\uEA4F,default_icon_size) tip=['GoMirrored',tip.info] cmd=command.copy('image.fluent(\uEA4F,12)'))
        item(image=image.fluent(\uEA50,default_icon_size) tip=['GripperResizeMirrored',tip.info] cmd=command.copy('image.fluent(\uEA50,12)'))
        item(image=image.fluent(\uEA51,default_icon_size) tip=['HelpMirrored',tip.info] cmd=command.copy('image.fluent(\uEA51,12)'))
        item(image=image.fluent(\uEA52,default_icon_size) tip=['ImportMirrored',tip.info] cmd=command.copy('image.fluent(\uEA52,12)'))
        item(image=image.fluent(\uEA53,default_icon_size) tip=['ImportAllMirrored',tip.info] cmd=command.copy('image.fluent(\uEA53,12)'))
        item(image=image.fluent(\uEA54,default_icon_size) tip=['LeaveChatMirrored',tip.info] cmd=command.copy('image.fluent(\uEA54,12)'))
        item(image=image.fluent(\uEA55,default_icon_size) tip=['ListMirrored',tip.info] cmd=command.copy('image.fluent(\uEA55,12)'))
        item(image=image.fluent(\uEA56,default_icon_size) tip=['MailForwardMirrored',tip.info] cmd=command.copy('image.fluent(\uEA56,12)'))
        item(image=image.fluent(\uEA57,default_icon_size) tip=['MailReplyMirrored',tip.info] cmd=command.copy('image.fluent(\uEA57,12)'))
        item(image=image.fluent(\uEA58,default_icon_size) tip=['MailReplyAllMirrored',tip.info] cmd=command.copy('image.fluent(\uEA58,12)'))
        item(image=image.fluent(\uEA5B,default_icon_size) tip=['OpenPaneMirrored',tip.info] cmd=command.copy('image.fluent(\uEA5B,12)'))
        item(image=image.fluent(\uEA5C,default_icon_size) tip=['OpenWithMirrored',tip.info] cmd=command.copy('image.fluent(\uEA5C,12)'))
        item(image=image.fluent(\uEA5E,default_icon_size) tip=['ParkingLocationMirrored',tip.info] cmd=command.copy('image.fluent(\uEA5E,12)'))
        item(image=image.fluent(\uEA5F,default_icon_size) tip=['ResizeMouseMediumMirrored',tip.info] cmd=command.copy('image.fluent(\uEA5F,12)'))

        item(image=image.fluent(\uEA60,default_icon_size) tip=['ResizeMouseSmallMirrored',tip.info] cmd=command.copy('image.fluent(\uEA60,12)') col)
        item(image=image.fluent(\uEA61,default_icon_size) tip=['ResizeMouseTallMirrored',tip.info] cmd=command.copy('image.fluent(\uEA61,12)'))
        item(image=image.fluent(\uEA62,default_icon_size) tip=['ResizeTouchNarrowerMirrored',tip.info] cmd=command.copy('image.fluent(\uEA62,12)'))
        item(image=image.fluent(\uEA63,default_icon_size) tip=['SendMirrored',tip.info] cmd=command.copy('image.fluent(\uEA63,12)'))
        item(image=image.fluent(\uEA64,default_icon_size) tip=['SendFillMirrored',tip.info] cmd=command.copy('image.fluent(\uEA64,12)'))
        item(image=image.fluent(\uEA65,default_icon_size) tip=['ShowResultsMirrored',tip.info] cmd=command.copy('image.fluent(\uEA65,12)'))
        item(image=image.fluent(\uEA69,default_icon_size) tip=['Media',tip.info] cmd=command.copy('image.fluent(\uEA69,12)'))
        item(image=image.fluent(\uEA6A,default_icon_size) tip=['SyncError',tip.info] cmd=command.copy('image.fluent(\uEA6A,12)'))
        item(image=image.fluent(\uEA6C,default_icon_size) tip=['Devices3',tip.info] cmd=command.copy('image.fluent(\uEA6C,12)'))
        item(image=image.fluent(\uEA79,default_icon_size) tip=['SlowMotionOn',tip.info] cmd=command.copy('image.fluent(\uEA79,12)'))
        item(image=image.fluent(\uEA80,default_icon_size) tip=['Lightbulb',tip.info] cmd=command.copy('image.fluent(\uEA80,12)'))
        item(image=image.fluent(\uEA81,default_icon_size) tip=['StatusCircle',tip.info] cmd=command.copy('image.fluent(\uEA81,12)'))
        item(image=image.fluent(\uEA82,default_icon_size) tip=['StatusTriangle',tip.info] cmd=command.copy('image.fluent(\uEA82,12)'))
        item(image=image.fluent(\uEA83,default_icon_size) tip=['StatusError',tip.info] cmd=command.copy('image.fluent(\uEA83,12)'))
        item(image=image.fluent(\uEA84,default_icon_size) tip=['StatusWarning',tip.info] cmd=command.copy('image.fluent(\uEA84,12)'))
        item(image=image.fluent(\uEA86,default_icon_size) tip=['Puzzle',tip.info] cmd=command.copy('image.fluent(\uEA86,12)'))

        item(image=image.fluent(\uEA89,default_icon_size) tip=['CalendarSolid',tip.info] cmd=command.copy('image.fluent(\uEA89,12)') col)
        item(image=image.fluent(\uEA8A,default_icon_size) tip=['HomeSolid',tip.info] cmd=command.copy('image.fluent(\uEA8A,12)'))
        item(image=image.fluent(\uEA8B,default_icon_size) tip=['ParkingLocationSolid',tip.info] cmd=command.copy('image.fluent(\uEA8B,12)'))
        item(image=image.fluent(\uEA8C,default_icon_size) tip=['ContactSolid',tip.info] cmd=command.copy('image.fluent(\uEA8C,12)'))
        item(image=image.fluent(\uEA8D,default_icon_size) tip=['ConstructionSolid',tip.info] cmd=command.copy('image.fluent(\uEA8D,12)'))
        item(image=image.fluent(\uEA8E,default_icon_size) tip=['AccidentSolid',tip.info] cmd=command.copy('image.fluent(\uEA8E,12)'))
        item(image=image.fluent(\uEA8F,default_icon_size) tip=['Ringer',tip.info] cmd=command.copy('image.fluent(\uEA8F,12)'))
        item(image=image.fluent(\uEA90,default_icon_size) tip=['PDF',tip.info] cmd=command.copy('image.fluent(\uEA90,12)'))
        item(image=image.fluent(\uEA91,default_icon_size) tip=['ThoughtBubble',tip.info] cmd=command.copy('image.fluent(\uEA91,12)'))
        item(image=image.fluent(\uEA92,default_icon_size) tip=['HeartBroken',tip.info] cmd=command.copy('image.fluent(\uEA92,12)'))
        item(image=image.fluent(\uEA93,default_icon_size) tip=['BatteryCharging10',tip.info] cmd=command.copy('image.fluent(\uEA93,12)'))
        item(image=image.fluent(\uEA94,default_icon_size) tip=['BatterySaver9',tip.info] cmd=command.copy('image.fluent(\uEA94,12)'))
        item(image=image.fluent(\uEA95,default_icon_size) tip=['BatterySaver10',tip.info] cmd=command.copy('image.fluent(\uEA95,12)'))
        item(image=image.fluent(\uEA97,default_icon_size) tip=['CallForwardingMirrored',tip.info] cmd=command.copy('image.fluent(\uEA97,12)'))
        item(image=image.fluent(\uEA98,default_icon_size) tip=['MultiSelectMirrored',tip.info] cmd=command.copy('image.fluent(\uEA98,12)'))
        item(image=image.fluent(\uEA99,default_icon_size) tip=['Broom',tip.info] cmd=command.copy('image.fluent(\uEA99,12)'))

        item(image=image.fluent(\uEAC2,default_icon_size) tip=['ForwardCall',tip.info] cmd=command.copy('image.fluent(\uEAC2,12)') col)
        item(image=image.fluent(\uEADF,default_icon_size) tip=['Trackers',tip.info] cmd=command.copy('image.fluent(\uEADF,12)'))
        item(image=image.fluent(\uEAFC,default_icon_size) tip=['Market',tip.info] cmd=command.copy('image.fluent(\uEAFC,12)'))
        item(image=image.fluent(\uEB05,default_icon_size) tip=['PieSingle',tip.info] cmd=command.copy('image.fluent(\uEB05,12)'))
        item(image=image.fluent(\uEB0F,default_icon_size) tip=['StockUp',tip.info] cmd=command.copy('image.fluent(\uEB0F,12)'))
        item(image=image.fluent(\uEB11,default_icon_size) tip=['StockDown',tip.info] cmd=command.copy('image.fluent(\uEB11,12)'))
        item(image=image.fluent(\uEB3C,default_icon_size) tip=['Design',tip.info] cmd=command.copy('image.fluent(\uEB3C,12)'))
        item(image=image.fluent(\uEB41,default_icon_size) tip=['Website',tip.info] cmd=command.copy('image.fluent(\uEB41,12)'))
        item(image=image.fluent(\uEB42,default_icon_size) tip=['Drop',tip.info] cmd=command.copy('image.fluent(\uEB42,12)'))
        item(image=image.fluent(\uEB44,default_icon_size) tip=['Radar',tip.info] cmd=command.copy('image.fluent(\uEB44,12)'))
        item(image=image.fluent(\uEB47,default_icon_size) tip=['BusSolid',tip.info] cmd=command.copy('image.fluent(\uEB47,12)'))
        item(image=image.fluent(\uEB48,default_icon_size) tip=['FerrySolid',tip.info] cmd=command.copy('image.fluent(\uEB48,12)'))
        item(image=image.fluent(\uEB49,default_icon_size) tip=['StartPointSolid',tip.info] cmd=command.copy('image.fluent(\uEB49,12)'))
        item(image=image.fluent(\uEB4A,default_icon_size) tip=['StopPointSolid',tip.info] cmd=command.copy('image.fluent(\uEB4A,12)'))
        item(image=image.fluent(\uEB4B,default_icon_size) tip=['EndPointSolid',tip.info] cmd=command.copy('image.fluent(\uEB4B,12)'))
        item(image=image.fluent(\uEB4C,default_icon_size) tip=['AirplaneSolid',tip.info] cmd=command.copy('image.fluent(\uEB4C,12)'))

        item(image=image.fluent(\uEB4D,default_icon_size) tip=['TrainSolid',tip.info] cmd=command.copy('image.fluent(\uEB4D,12)') col)
        item(image=image.fluent(\uEB4E,default_icon_size) tip=['WorkSolid',tip.info] cmd=command.copy('image.fluent(\uEB4E,12)'))
        item(image=image.fluent(\uEB4F,default_icon_size) tip=['ReminderFill',tip.info] cmd=command.copy('image.fluent(\uEB4F,12)'))
        item(image=image.fluent(\uEB50,default_icon_size) tip=['Reminder',tip.info] cmd=command.copy('image.fluent(\uEB50,12)'))
        item(image=image.fluent(\uEB51,default_icon_size) tip=['Heart',tip.info] cmd=command.copy('image.fluent(\uEB51,12)'))
        item(image=image.fluent(\uEB52,default_icon_size) tip=['HeartFill',tip.info] cmd=command.copy('image.fluent(\uEB52,12)'))
        item(image=image.fluent(\uEB55,default_icon_size) tip=['EthernetError',tip.info] cmd=command.copy('image.fluent(\uEB55,12)'))
        item(image=image.fluent(\uEB56,default_icon_size) tip=['EthernetWarning',tip.info] cmd=command.copy('image.fluent(\uEB56,12)'))
        item(image=image.fluent(\uEB57,default_icon_size) tip=['StatusConnecting1',tip.info] cmd=command.copy('image.fluent(\uEB57,12)'))
        item(image=image.fluent(\uEB58,default_icon_size) tip=['StatusConnecting2',tip.info] cmd=command.copy('image.fluent(\uEB58,12)'))
        item(image=image.fluent(\uEB59,default_icon_size) tip=['StatusUnsecure',tip.info] cmd=command.copy('image.fluent(\uEB59,12)'))
        item(image=image.fluent(\uEB5A,default_icon_size) tip=['WifiError0',tip.info] cmd=command.copy('image.fluent(\uEB5A,12)'))
        item(image=image.fluent(\uEB5B,default_icon_size) tip=['WifiError1',tip.info] cmd=command.copy('image.fluent(\uEB5B,12)'))
        item(image=image.fluent(\uEB5C,default_icon_size) tip=['WifiError2',tip.info] cmd=command.copy('image.fluent(\uEB5C,12)'))
        item(image=image.fluent(\uEB5D,default_icon_size) tip=['WifiError3',tip.info] cmd=command.copy('image.fluent(\uEB5D,12)'))
        item(image=image.fluent(\uEB5E,default_icon_size) tip=['WifiError4',tip.info] cmd=command.copy('image.fluent(\uEB5E,12)'))

        item(image=image.fluent(\uEB5F,default_icon_size) tip=['WifiWarning0',tip.info] cmd=command.copy('image.fluent(\uEB5F,12)') col)
        item(image=image.fluent(\uEB60,default_icon_size) tip=['WifiWarning1',tip.info] cmd=command.copy('image.fluent(\uEB60,12)'))
        item(image=image.fluent(\uEB61,default_icon_size) tip=['WifiWarning2',tip.info] cmd=command.copy('image.fluent(\uEB61,12)'))
        item(image=image.fluent(\uEB62,default_icon_size) tip=['WifiWarning3',tip.info] cmd=command.copy('image.fluent(\uEB62,12)'))
        item(image=image.fluent(\uEB63,default_icon_size) tip=['WifiWarning4',tip.info] cmd=command.copy('image.fluent(\uEB63,12)'))
        item(image=image.fluent(\uEB66,default_icon_size) tip=['Devices4',tip.info] cmd=command.copy('image.fluent(\uEB66,12)'))
        item(image=image.fluent(\uEB67,default_icon_size) tip=['NUIIris',tip.info] cmd=command.copy('image.fluent(\uEB67,12)'))
        item(image=image.fluent(\uEB68,default_icon_size) tip=['NUIFace',tip.info] cmd=command.copy('image.fluent(\uEB68,12)'))
        item(image=image.fluent(\uEB77,default_icon_size) tip=['GatewayRouter',tip.info] cmd=command.copy('image.fluent(\uEB77,12)'))
        item(image=image.fluent(\uEB7E,default_icon_size) tip=['EditMirrored',tip.info] cmd=command.copy('image.fluent(\uEB7E,12)'))
        item(image=image.fluent(\uEB82,default_icon_size) tip=['NUIFPStartSlideHand',tip.info] cmd=command.copy('image.fluent(\uEB82,12)'))
        item(image=image.fluent(\uEB83,default_icon_size) tip=['NUIFPStartSlideAction',tip.info] cmd=command.copy('image.fluent(\uEB83,12)'))
        item(image=image.fluent(\uEB84,default_icon_size) tip=['NUIFPContinueSlideHand',tip.info] cmd=command.copy('image.fluent(\uEB84,12)'))
        item(image=image.fluent(\uEB85,default_icon_size) tip=['NUIFPContinueSlideAction',tip.info] cmd=command.copy('image.fluent(\uEB85,12)'))
        item(image=image.fluent(\uEB86,default_icon_size) tip=['NUIFPRollRightHand',tip.info] cmd=command.copy('image.fluent(\uEB86,12)'))
        item(image=image.fluent(\uEB87,default_icon_size) tip=['NUIFPRollRightHandAction',tip.info] cmd=command.copy('image.fluent(\uEB87,12)'))

        item(image=image.fluent(\uEB88,default_icon_size) tip=['NUIFPRollLeftHand',tip.info] cmd=command.copy('image.fluent(\uEB88,12)') col)
        item(image=image.fluent(\uEB89,default_icon_size) tip=['NUIFPRollLeftAction',tip.info] cmd=command.copy('image.fluent(\uEB89,12)'))
        item(image=image.fluent(\uEB8A,default_icon_size) tip=['NUIFPPressHand',tip.info] cmd=command.copy('image.fluent(\uEB8A,12)'))
        item(image=image.fluent(\uEB8B,default_icon_size) tip=['NUIFPPressAction',tip.info] cmd=command.copy('image.fluent(\uEB8B,12)'))
        item(image=image.fluent(\uEB8C,default_icon_size) tip=['NUIFPPressRepeatHand',tip.info] cmd=command.copy('image.fluent(\uEB8C,12)'))
        item(image=image.fluent(\uEB8D,default_icon_size) tip=['NUIFPPressRepeatAction',tip.info] cmd=command.copy('image.fluent(\uEB8D,12)'))
        item(image=image.fluent(\uEB90,default_icon_size) tip=['StatusErrorFull',tip.info] cmd=command.copy('image.fluent(\uEB90,12)'))
        item(image=image.fluent(\uEB91,default_icon_size) tip=['TaskViewExpanded',tip.info] cmd=command.copy('image.fluent(\uEB91,12)'))
        item(image=image.fluent(\uEB95,default_icon_size) tip=['Certificate',tip.info] cmd=command.copy('image.fluent(\uEB95,12)'))
        item(image=image.fluent(\uEB96,default_icon_size) tip=['BackSpaceQWERTYLg',tip.info] cmd=command.copy('image.fluent(\uEB96,12)'))
        item(image=image.fluent(\uEB97,default_icon_size) tip=['ReturnKeyLg',tip.info] cmd=command.copy('image.fluent(\uEB97,12)'))
        item(image=image.fluent(\uEB9D,default_icon_size) tip=['FastForward',tip.info] cmd=command.copy('image.fluent(\uEB9D,12)'))
        item(image=image.fluent(\uEB9E,default_icon_size) tip=['Rewind',tip.info] cmd=command.copy('image.fluent(\uEB9E,12)'))
        item(image=image.fluent(\uEB9F,default_icon_size) tip=['Photo2',tip.info] cmd=command.copy('image.fluent(\uEB9F,12)'))
        item(image=image.fluent(\uEBA0,default_icon_size) tip=['MobBattery0',tip.info] cmd=command.copy('image.fluent(\uEBA0,12)'))
        item(image=image.fluent(\uEBA1,default_icon_size) tip=['MobBattery1',tip.info] cmd=command.copy('image.fluent(\uEBA1,12)'))

        item(image=image.fluent(\uEBA2,default_icon_size) tip=['MobBattery2',tip.info] cmd=command.copy('image.fluent(\uEBA2,12)') col)
        item(image=image.fluent(\uEBA3,default_icon_size) tip=['MobBattery3',tip.info] cmd=command.copy('image.fluent(\uEBA3,12)'))
        item(image=image.fluent(\uEBA4,default_icon_size) tip=['MobBattery4',tip.info] cmd=command.copy('image.fluent(\uEBA4,12)'))
        item(image=image.fluent(\uEBA5,default_icon_size) tip=['MobBattery5',tip.info] cmd=command.copy('image.fluent(\uEBA5,12)'))
        item(image=image.fluent(\uEBA6,default_icon_size) tip=['MobBattery6',tip.info] cmd=command.copy('image.fluent(\uEBA6,12)'))
        item(image=image.fluent(\uEBA7,default_icon_size) tip=['MobBattery7',tip.info] cmd=command.copy('image.fluent(\uEBA7,12)'))
        item(image=image.fluent(\uEBA8,default_icon_size) tip=['MobBattery8',tip.info] cmd=command.copy('image.fluent(\uEBA8,12)'))
        item(image=image.fluent(\uEBA9,default_icon_size) tip=['MobBattery9',tip.info] cmd=command.copy('image.fluent(\uEBA9,12)'))
        item(image=image.fluent(\uEBAA,default_icon_size) tip=['MobBattery10',tip.info] cmd=command.copy('image.fluent(\uEBAA,12)'))
        item(image=image.fluent(\uEBAB,default_icon_size) tip=['MobBatteryCharging0',tip.info] cmd=command.copy('image.fluent(\uEBAB,12)'))
        item(image=image.fluent(\uEBAC,default_icon_size) tip=['MobBatteryCharging1',tip.info] cmd=command.copy('image.fluent(\uEBAC,12)'))
        item(image=image.fluent(\uEBAD,default_icon_size) tip=['MobBatteryCharging2',tip.info] cmd=command.copy('image.fluent(\uEBAD,12)'))
        item(image=image.fluent(\uEBAE,default_icon_size) tip=['MobBatteryCharging3',tip.info] cmd=command.copy('image.fluent(\uEBAE,12)'))
        item(image=image.fluent(\uEBAF,default_icon_size) tip=['MobBatteryCharging4',tip.info] cmd=command.copy('image.fluent(\uEBAF,12)'))
        item(image=image.fluent(\uEBB0,default_icon_size) tip=['MobBatteryCharging5',tip.info] cmd=command.copy('image.fluent(\uEBB0,12)'))
        item(image=image.fluent(\uEBB1,default_icon_size) tip=['MobBatteryCharging6',tip.info] cmd=command.copy('image.fluent(\uEBB1,12)'))

        item(image=image.fluent(\uEBB2,default_icon_size) tip=['MobBatteryCharging7',tip.info] cmd=command.copy('image.fluent(\uEBB2,12)') col)
        item(image=image.fluent(\uEBB3,default_icon_size) tip=['MobBatteryCharging8',tip.info] cmd=command.copy('image.fluent(\uEBB3,12)'))
        item(image=image.fluent(\uEBB4,default_icon_size) tip=['MobBatteryCharging9',tip.info] cmd=command.copy('image.fluent(\uEBB4,12)'))
        item(image=image.fluent(\uEBB5,default_icon_size) tip=['MobBatteryCharging10',tip.info] cmd=command.copy('image.fluent(\uEBB5,12)'))
        item(image=image.fluent(\uEBB6,default_icon_size) tip=['MobBatterySaver0',tip.info] cmd=command.copy('image.fluent(\uEBB6,12)'))
        item(image=image.fluent(\uEBB7,default_icon_size) tip=['MobBatterySaver1',tip.info] cmd=command.copy('image.fluent(\uEBB7,12)'))
        item(image=image.fluent(\uEBB8,default_icon_size) tip=['MobBatterySaver2',tip.info] cmd=command.copy('image.fluent(\uEBB8,12)'))
        item(image=image.fluent(\uEBB9,default_icon_size) tip=['MobBatterySaver3',tip.info] cmd=command.copy('image.fluent(\uEBB9,12)'))
        item(image=image.fluent(\uEBBA,default_icon_size) tip=['MobBatterySaver4',tip.info] cmd=command.copy('image.fluent(\uEBBA,12)'))
        item(image=image.fluent(\uEBBB,default_icon_size) tip=['MobBatterySaver5',tip.info] cmd=command.copy('image.fluent(\uEBBB,12)'))
        item(image=image.fluent(\uEBBC,default_icon_size) tip=['MobBatterySaver6',tip.info] cmd=command.copy('image.fluent(\uEBBC,12)'))
        item(image=image.fluent(\uEBBD,default_icon_size) tip=['MobBatterySaver7',tip.info] cmd=command.copy('image.fluent(\uEBBD,12)'))
        item(image=image.fluent(\uEBBE,default_icon_size) tip=['MobBatterySaver8',tip.info] cmd=command.copy('image.fluent(\uEBBE,12)'))
        item(image=image.fluent(\uEBBF,default_icon_size) tip=['MobBatterySaver9',tip.info] cmd=command.copy('image.fluent(\uEBBF,12)'))
        item(image=image.fluent(\uEBC0,default_icon_size) tip=['MobBatterySaver10',tip.info] cmd=command.copy('image.fluent(\uEBC0,12)'))
        item(image=image.fluent(\uEBC3,default_icon_size) tip=['DictionaryCloud',tip.info] cmd=command.copy('image.fluent(\uEBC3,12)'))

        item(image=image.fluent(\uEBC4,default_icon_size) tip=['ResetDrive',tip.info] cmd=command.copy('image.fluent(\uEBC4,12)') col)
        item(image=image.fluent(\uEBC5,default_icon_size) tip=['VolumeBars',tip.info] cmd=command.copy('image.fluent(\uEBC5,12)'))
        item(image=image.fluent(\uEBC6,default_icon_size) tip=['Project',tip.info] cmd=command.copy('image.fluent(\uEBC6,12)'))
        item(image=image.fluent(\uEBD2,default_icon_size) tip=['AdjustHologram',tip.info] cmd=command.copy('image.fluent(\uEBD2,12)'))
        item(image=image.fluent(\uEBD3,default_icon_size) tip=['CloudDownload',tip.info] cmd=command.copy('image.fluent(\uEBD3,12)'))
        item(image=image.fluent(\uEBD4,default_icon_size) tip=['MobWifiCallBars',tip.info] cmd=command.copy('image.fluent(\uEBD4,12)'))
        item(image=image.fluent(\uEBD5,default_icon_size) tip=['MobWifiCall0',tip.info] cmd=command.copy('image.fluent(\uEBD5,12)'))
        item(image=image.fluent(\uEBD6,default_icon_size) tip=['MobWifiCall1',tip.info] cmd=command.copy('image.fluent(\uEBD6,12)'))
        item(image=image.fluent(\uEBD7,default_icon_size) tip=['MobWifiCall2',tip.info] cmd=command.copy('image.fluent(\uEBD7,12)'))
        item(image=image.fluent(\uEBD8,default_icon_size) tip=['MobWifiCall3',tip.info] cmd=command.copy('image.fluent(\uEBD8,12)'))
        item(image=image.fluent(\uEBD9,default_icon_size) tip=['MobWifiCall4',tip.info] cmd=command.copy('image.fluent(\uEBD9,12)'))
        item(image=image.fluent(\uEBDA,default_icon_size) tip=['Family',tip.info] cmd=command.copy('image.fluent(\uEBDA,12)'))
        item(image=image.fluent(\uEBDB,default_icon_size) tip=['LockFeedback',tip.info] cmd=command.copy('image.fluent(\uEBDB,12)'))
        item(image=image.fluent(\uEBDE,default_icon_size) tip=['DeviceDiscovery',tip.info] cmd=command.copy('image.fluent(\uEBDE,12)'))
        item(image=image.fluent(\uEBE6,default_icon_size) tip=['WindDirection',tip.info] cmd=command.copy('image.fluent(\uEBE6,12)'))
        item(image=image.fluent(\uEBE7,default_icon_size) tip=['RightArrowKeyTime0',tip.info] cmd=command.copy('image.fluent(\uEBE7,12)'))
    }

    menu(title='Fluent #4')
    {
        item(image=image.fluent(\uEBE8,default_icon_size) tip=['Bug',tip.info] cmd=command.copy('image.fluent(\uEBE8,12)'))
        item(image=image.fluent(\uEBFC,default_icon_size) tip=['TabletMode',tip.info] cmd=command.copy('image.fluent(\uEBFC,12)'))
        item(image=image.fluent(\uEBFD,default_icon_size) tip=['StatusCircleLeft',tip.info] cmd=command.copy('image.fluent(\uEBFD,12)'))
        item(image=image.fluent(\uEBFE,default_icon_size) tip=['StatusTriangleLeft',tip.info] cmd=command.copy('image.fluent(\uEBFE,12)'))
        item(image=image.fluent(\uEBFF,default_icon_size) tip=['StatusErrorLeft',tip.info] cmd=command.copy('image.fluent(\uEBFF,12)'))
        item(image=image.fluent(\uEC00,default_icon_size) tip=['StatusWarningLeft',tip.info] cmd=command.copy('image.fluent(\uEC00,12)'))
        item(image=image.fluent(\uEC02,default_icon_size) tip=['MobBatteryUnknown',tip.info] cmd=command.copy('image.fluent(\uEC02,12)'))
        item(image=image.fluent(\uEC05,default_icon_size) tip=['NetworkTower',tip.info] cmd=command.copy('image.fluent(\uEC05,12)'))
        item(image=image.fluent(\uEC06,default_icon_size) tip=['CityNext',tip.info] cmd=command.copy('image.fluent(\uEC06,12)'))
        item(image=image.fluent(\uEC07,default_icon_size) tip=['CityNext2',tip.info] cmd=command.copy('image.fluent(\uEC07,12)'))
        item(image=image.fluent(\uEC08,default_icon_size) tip=['Courthouse',tip.info] cmd=command.copy('image.fluent(\uEC08,12)'))
        item(image=image.fluent(\uEC09,default_icon_size) tip=['Groceries',tip.info] cmd=command.copy('image.fluent(\uEC09,12)'))
        item(image=image.fluent(\uEC0A,default_icon_size) tip=['Sustainable',tip.info] cmd=command.copy('image.fluent(\uEC0A,12)'))
        item(image=image.fluent(\uEC0B,default_icon_size) tip=['BuildingEnergy',tip.info] cmd=command.copy('image.fluent(\uEC0B,12)'))
        item(image=image.fluent(\uEC11,default_icon_size) tip=['ToggleFilled',tip.info] cmd=command.copy('image.fluent(\uEC11,12)'))
        item(image=image.fluent(\uEC12,default_icon_size) tip=['ToggleBorder',tip.info] cmd=command.copy('image.fluent(\uEC12,12)'))

        item(image=image.fluent(\uEC13,default_icon_size) tip=['SliderThumb',tip.info] cmd=command.copy('image.fluent(\uEC13,12)') col)
        item(image=image.fluent(\uEC14,default_icon_size) tip=['ToggleThumb',tip.info] cmd=command.copy('image.fluent(\uEC14,12)'))
        item(image=image.fluent(\uEC15,default_icon_size) tip=['MiracastLogoSmall',tip.info] cmd=command.copy('image.fluent(\uEC15,12)'))
        item(image=image.fluent(\uEC16,default_icon_size) tip=['MiracastLogoLarge',tip.info] cmd=command.copy('image.fluent(\uEC16,12)'))
        item(image=image.fluent(\uEC19,default_icon_size) tip=['PLAP',tip.info] cmd=command.copy('image.fluent(\uEC19,12)'))
        item(image=image.fluent(\uEC1B,default_icon_size) tip=['Badge',tip.info] cmd=command.copy('image.fluent(\uEC1B,12)'))
        item(image=image.fluent(\uEC1E,default_icon_size) tip=['SignalRoaming',tip.info] cmd=command.copy('image.fluent(\uEC1E,12)'))
        item(image=image.fluent(\uEC20,default_icon_size) tip=['MobileLocked',tip.info] cmd=command.copy('image.fluent(\uEC20,12)'))
        item(image=image.fluent(\uEC24,default_icon_size) tip=['InsiderHubApp',tip.info] cmd=command.copy('image.fluent(\uEC24,12)'))
        item(image=image.fluent(\uEC25,default_icon_size) tip=['PersonalFolder',tip.info] cmd=command.copy('image.fluent(\uEC25,12)'))
        item(image=image.fluent(\uEC26,default_icon_size) tip=['HomeGroup',tip.info] cmd=command.copy('image.fluent(\uEC26,12)'))
        item(image=image.fluent(\uEC27,default_icon_size) tip=['MyNetwork',tip.info] cmd=command.copy('image.fluent(\uEC27,12)'))
        item(image=image.fluent(\uEC31,default_icon_size) tip=['KeyboardFull',tip.info] cmd=command.copy('image.fluent(\uEC31,12)'))
        item(image=image.fluent(\uEC32,default_icon_size) tip=['Cafe',tip.info] cmd=command.copy('image.fluent(\uEC32,12)'))
        item(image=image.fluent(\uEC37,default_icon_size) tip=['MobSignal1',tip.info] cmd=command.copy('image.fluent(\uEC37,12)'))
        item(image=image.fluent(\uEC38,default_icon_size) tip=['MobSignal2',tip.info] cmd=command.copy('image.fluent(\uEC38,12)'))

        item(image=image.fluent(\uEC39,default_icon_size) tip=['MobSignal3',tip.info] cmd=command.copy('image.fluent(\uEC39,12)') col)
        item(image=image.fluent(\uEC3A,default_icon_size) tip=['MobSignal4',tip.info] cmd=command.copy('image.fluent(\uEC3A,12)'))
        item(image=image.fluent(\uEC3B,default_icon_size) tip=['MobSignal5',tip.info] cmd=command.copy('image.fluent(\uEC3B,12)'))
        item(image=image.fluent(\uEC3C,default_icon_size) tip=['MobWifi1',tip.info] cmd=command.copy('image.fluent(\uEC3C,12)'))
        item(image=image.fluent(\uEC3E,default_icon_size) tip=['MobWifi3',tip.info] cmd=command.copy('image.fluent(\uEC3E,12)'))
        item(image=image.fluent(\uEC3F,default_icon_size) tip=['MobWifi4',tip.info] cmd=command.copy('image.fluent(\uEC3F,12)'))
        item(image=image.fluent(\uEC40,default_icon_size) tip=['MobAirplane',tip.info] cmd=command.copy('image.fluent(\uEC40,12)'))
        item(image=image.fluent(\uEC41,default_icon_size) tip=['MobBluetooth',tip.info] cmd=command.copy('image.fluent(\uEC41,12)'))
        item(image=image.fluent(\uEC42,default_icon_size) tip=['MobActionCenter',tip.info] cmd=command.copy('image.fluent(\uEC42,12)'))
        item(image=image.fluent(\uEC43,default_icon_size) tip=['MobLocation',tip.info] cmd=command.copy('image.fluent(\uEC43,12)'))
        item(image=image.fluent(\uEC44,default_icon_size) tip=['MobWifiHotspot',tip.info] cmd=command.copy('image.fluent(\uEC44,12)'))
        item(image=image.fluent(\uEC45,default_icon_size) tip=['LanguageJpn',tip.info] cmd=command.copy('image.fluent(\uEC45,12)'))
        item(image=image.fluent(\uEC46,default_icon_size) tip=['MobQuietHours',tip.info] cmd=command.copy('image.fluent(\uEC46,12)'))
        item(image=image.fluent(\uEC47,default_icon_size) tip=['MobDrivingMode',tip.info] cmd=command.copy('image.fluent(\uEC47,12)'))
        item(image=image.fluent(\uEC48,default_icon_size) tip=['SpeedOff',tip.info] cmd=command.copy('image.fluent(\uEC48,12)'))

        item(image=image.fluent(\uEC49,default_icon_size) tip=['SpeedMedium',tip.info] cmd=command.copy('image.fluent(\uEC49,12)') col)
        item(image=image.fluent(\uEC4A,default_icon_size) tip=['SpeedHigh',tip.info] cmd=command.copy('image.fluent(\uEC4A,12)'))
        item(image=image.fluent(\uEC4E,default_icon_size) tip=['ThisPC',tip.info] cmd=command.copy('image.fluent(\uEC4E,12)'))
        item(image=image.fluent(\uEC4F,default_icon_size) tip=['MusicNote',tip.info] cmd=command.copy('image.fluent(\uEC4F,12)'))
        item(image=image.fluent(\uEC50,default_icon_size) tip=['FileExplorer',tip.info] cmd=command.copy('image.fluent(\uEC50,12)'))
        item(image=image.fluent(\uEC51,default_icon_size) tip=['FileExplorerApp',tip.info] cmd=command.copy('image.fluent(\uEC51,12)'))
        item(image=image.fluent(\uEC52,default_icon_size) tip=['LeftArrowKeyTime0',tip.info] cmd=command.copy('image.fluent(\uEC52,12)'))
        item(image=image.fluent(\uEC54,default_icon_size) tip=['MicOff',tip.info] cmd=command.copy('image.fluent(\uEC54,12)'))
        item(image=image.fluent(\uEC55,default_icon_size) tip=['MicSleep',tip.info] cmd=command.copy('image.fluent(\uEC55,12)'))
        item(image=image.fluent(\uEC56,default_icon_size) tip=['MicError',tip.info] cmd=command.copy('image.fluent(\uEC56,12)'))
        item(image=image.fluent(\uEC3E,default_icon_size) tip=['MobWifi3',tip.info] cmd=command.copy('image.fluent(\uEC3E,12)'))
        item(image=image.fluent(\uEC3F,default_icon_size) tip=['MobWifi4',tip.info] cmd=command.copy('image.fluent(\uEC3F,12)'))
        item(image=image.fluent(\uEC40,default_icon_size) tip=['MobAirplane',tip.info] cmd=command.copy('image.fluent(\uEC40,12)'))
        item(image=image.fluent(\uEC41,default_icon_size) tip=['MobBluetooth',tip.info] cmd=command.copy('image.fluent(\uEC41,12)'))
        item(image=image.fluent(\uEC42,default_icon_size) tip=['MobActionCenter',tip.info] cmd=command.copy('image.fluent(\uEC42,12)'))
        item(image=image.fluent(\uEC43,default_icon_size) tip=['MobLocation',tip.info] cmd=command.copy('image.fluent(\uEC43,12)'))
        item(image=image.fluent(\uEC44,default_icon_size) tip=['MobWifiHotspot',tip.info] cmd=command.copy('image.fluent(\uEC44,12)'))
        item(image=image.fluent(\uEC45,default_icon_size) tip=['LanguageJpn',tip.info] cmd=command.copy('image.fluent(\uEC45,12)'))
        item(image=image.fluent(\uEC46,default_icon_size) tip=['MobQuietHours',tip.info] cmd=command.copy('image.fluent(\uEC46,12)'))
        item(image=image.fluent(\uEC47,default_icon_size) tip=['MobDrivingMode',tip.info] cmd=command.copy('image.fluent(\uEC47,12)'))
        item(image=image.fluent(\uEC48,default_icon_size) tip=['SpeedOff',tip.info] cmd=command.copy('image.fluent(\uEC48,12)'))

        item(image=image.fluent(\uEC49,default_icon_size) tip=['SpeedMedium',tip.info] cmd=command.copy('image.fluent(\uEC49,12)') col)
        item(image=image.fluent(\uEC4A,default_icon_size) tip=['SpeedHigh',tip.info] cmd=command.copy('image.fluent(\uEC4A,12)'))
        item(image=image.fluent(\uEC4E,default_icon_size) tip=['ThisPC',tip.info] cmd=command.copy('image.fluent(\uEC4E,12)'))
        item(image=image.fluent(\uEC4F,default_icon_size) tip=['MusicNote',tip.info] cmd=command.copy('image.fluent(\uEC4F,12)'))
        item(image=image.fluent(\uEC50,default_icon_size) tip=['FileExplorer',tip.info] cmd=command.copy('image.fluent(\uEC50,12)'))
        item(image=image.fluent(\uEC51,default_icon_size) tip=['FileExplorerApp',tip.info] cmd=command.copy('image.fluent(\uEC51,12)'))
        item(image=image.fluent(\uEC52,default_icon_size) tip=['LeftArrowKeyTime0',tip.info] cmd=command.copy('image.fluent(\uEC52,12)'))
        item(image=image.fluent(\uEC54,default_icon_size) tip=['MicOff',tip.info] cmd=command.copy('image.fluent(\uEC54,12)'))
        item(image=image.fluent(\uEC55,default_icon_size) tip=['MicSleep',tip.info] cmd=command.copy('image.fluent(\uEC55,12)'))
        item(image=image.fluent(\uEC56,default_icon_size) tip=['MicError',tip.info] cmd=command.copy('image.fluent(\uEC56,12)'))
        item(image=image.fluent(\uEC57,default_icon_size) tip=['PlaybackRate1x',tip.info] cmd=command.copy('image.fluent(\uEC57,12)'))
        item(image=image.fluent(\uEC58,default_icon_size) tip=['PlaybackRateOther',tip.info] cmd=command.copy('image.fluent(\uEC58,12)'))
        item(image=image.fluent(\uEC59,default_icon_size) tip=['CashDrawer',tip.info] cmd=command.copy('image.fluent(\uEC59,12)'))
        item(image=image.fluent(\uEC5A,default_icon_size) tip=['BarcodeScanner',tip.info] cmd=command.copy('image.fluent(\uEC5A,12)'))
        item(image=image.fluent(\uEC5B,default_icon_size) tip=['ReceiptPrinter',tip.info] cmd=command.copy('image.fluent(\uEC5B,12)'))
        item(image=image.fluent(\uEC5C,default_icon_size) tip=['MagStripeReader',tip.info] cmd=command.copy('image.fluent(\uEC5C,12)'))

        item(image=image.fluent(\uEC61,default_icon_size) tip=['CompletedSolid',tip.info] cmd=command.copy('image.fluent(\uEC61,12)') col)
        item(image=image.fluent(\uEC64,default_icon_size) tip=['CompanionApp',tip.info] cmd=command.copy('image.fluent(\uEC64,12)'))
        item(image=image.fluent(\uEC6C,default_icon_size) tip=['Favicon2',tip.info] cmd=command.copy('image.fluent(\uEC6C,12)'))
        item(image=image.fluent(\uEC6D,default_icon_size) tip=['SwipeRevealArt',tip.info] cmd=command.copy('image.fluent(\uEC6D,12)'))
        item(image=image.fluent(\uEC71,default_icon_size) tip=['MicOn',tip.info] cmd=command.copy('image.fluent(\uEC71,12)'))
        item(image=image.fluent(\uEC72,default_icon_size) tip=['MicClipping',tip.info] cmd=command.copy('image.fluent(\uEC72,12)'))
        item(image=image.fluent(\uEC74,default_icon_size) tip=['TabletSelected',tip.info] cmd=command.copy('image.fluent(\uEC74,12)'))
        item(image=image.fluent(\uEC75,default_icon_size) tip=['MobileSelected',tip.info] cmd=command.copy('image.fluent(\uEC75,12)'))
        item(image=image.fluent(\uEC76,default_icon_size) tip=['LaptopSelected',tip.info] cmd=command.copy('image.fluent(\uEC76,12)'))
        item(image=image.fluent(\uEC77,default_icon_size) tip=['TVMonitorSelected',tip.info] cmd=command.copy('image.fluent(\uEC77,12)'))
        item(image=image.fluent(\uEC7A,default_icon_size) tip=['DeveloperTools',tip.info] cmd=command.copy('image.fluent(\uEC7A,12)'))
        item(image=image.fluent(\uEC7E,default_icon_size) tip=['MobCallForwarding',tip.info] cmd=command.copy('image.fluent(\uEC7E,12)'))
        item(image=image.fluent(\uEC7F,default_icon_size) tip=['MobCallForwardingMirrored',tip.info] cmd=command.copy('image.fluent(\uEC7F,12)'))
        item(image=image.fluent(\uEC80,default_icon_size) tip=['BodyCam',tip.info] cmd=command.copy('image.fluent(\uEC80,12)'))
        item(image=image.fluent(\uEC81,default_icon_size) tip=['PoliceCar',tip.info] cmd=command.copy('image.fluent(\uEC81,12)'))
        item(image=image.fluent(\uEC87,default_icon_size) tip=['Draw',tip.info] cmd=command.copy('image.fluent(\uEC87,12)'))

        item(image=image.fluent(\uEC88,default_icon_size) tip=['DrawSolid',tip.info] cmd=command.copy('image.fluent(\uEC88,12)') col)
        item(image=image.fluent(\uEC8A,default_icon_size) tip=['LowerBrightness',tip.info] cmd=command.copy('image.fluent(\uEC8A,12)'))
        item(image=image.fluent(\uEC8F,default_icon_size) tip=['ScrollUpDown',tip.info] cmd=command.copy('image.fluent(\uEC8F,12)'))
        item(image=image.fluent(\uEC92,default_icon_size) tip=['DateTime',tip.info] cmd=command.copy('image.fluent(\uEC92,12)'))
        item(image=image.fluent(\uEC94,default_icon_size) tip=['HoloLens',tip.info] cmd=command.copy('image.fluent(\uEC94,12)'))
        item(image=image.fluent(\uECA5,default_icon_size) tip=['Tiles',tip.info] cmd=command.copy('image.fluent(\uECA5,12)'))
        item(image=image.fluent(\uECA7,default_icon_size) tip=['PartyLeader',tip.info] cmd=command.copy('image.fluent(\uECA7,12)'))
        item(image=image.fluent(\uECAA,default_icon_size) tip=['AppIconDefault',tip.info] cmd=command.copy('image.fluent(\uECAA,12)'))
        item(image=image.fluent(\uECAD,default_icon_size) tip=['Calories',tip.info] cmd=command.copy('image.fluent(\uECAD,12)'))
        item(image=image.fluent(\uECAF,default_icon_size) tip=['POI',tip.info] cmd=command.copy('image.fluent(\uECAF,12)'))
        item(image=image.fluent(\uECB9,default_icon_size) tip=['BandBattery0',tip.info] cmd=command.copy('image.fluent(\uECB9,12)'))
        item(image=image.fluent(\uECBA,default_icon_size) tip=['BandBattery1',tip.info] cmd=command.copy('image.fluent(\uECBA,12)'))
        item(image=image.fluent(\uECBB,default_icon_size) tip=['BandBattery2',tip.info] cmd=command.copy('image.fluent(\uECBB,12)'))
        item(image=image.fluent(\uECBC,default_icon_size) tip=['BandBattery3',tip.info] cmd=command.copy('image.fluent(\uECBC,12)'))
        item(image=image.fluent(\uECBD,default_icon_size) tip=['BandBattery4',tip.info] cmd=command.copy('image.fluent(\uECBD,12)'))
        item(image=image.fluent(\uECBE,default_icon_size) tip=['BandBattery5',tip.info] cmd=command.copy('image.fluent(\uECBE,12)'))

        item(image=image.fluent(\uECBF,default_icon_size) tip=['BandBattery6',tip.info] cmd=command.copy('image.fluent(\uECBF,12)') col)
        item(image=image.fluent(\uECC4,default_icon_size) tip=['AddSurfaceHub',tip.info] cmd=command.copy('image.fluent(\uECC4,12)'))
        item(image=image.fluent(\uECC5,default_icon_size) tip=['DevUpdate',tip.info] cmd=command.copy('image.fluent(\uECC5,12)'))
        item(image=image.fluent(\uECC6,default_icon_size) tip=['Unit',tip.info] cmd=command.copy('image.fluent(\uECC6,12)'))
        item(image=image.fluent(\uECC8,default_icon_size) tip=['AddTo',tip.info] cmd=command.copy('image.fluent(\uECC8,12)'))
        item(image=image.fluent(\uECC9,default_icon_size) tip=['RemoveFrom',tip.info] cmd=command.copy('image.fluent(\uECC9,12)'))
        item(image=image.fluent(\uECCA,default_icon_size) tip=['RadioBtnOff',tip.info] cmd=command.copy('image.fluent(\uECCA,12)'))
        item(image=image.fluent(\uECCB,default_icon_size) tip=['RadioBtnOn',tip.info] cmd=command.copy('image.fluent(\uECCB,12)'))
        item(image=image.fluent(\uECCC,default_icon_size) tip=['RadioBullet2',tip.info] cmd=command.copy('image.fluent(\uECCC,12)'))
        item(image=image.fluent(\uECCD,default_icon_size) tip=['ExploreContent',tip.info] cmd=command.copy('image.fluent(\uECCD,12)'))
        item(image=image.fluent(\uECE4,default_icon_size) tip=['Blocked2',tip.info] cmd=command.copy('image.fluent(\uECE4,12)'))
        item(image=image.fluent(\uECE7,default_icon_size) tip=['ScrollMode',tip.info] cmd=command.copy('image.fluent(\uECE7,12)'))
        item(image=image.fluent(\uECE8,default_icon_size) tip=['ZoomMode',tip.info] cmd=command.copy('image.fluent(\uECE8,12)'))
        item(image=image.fluent(\uECE9,default_icon_size) tip=['PanMode',tip.info] cmd=command.copy('image.fluent(\uECE9,12)'))
        item(image=image.fluent(\uECF0,default_icon_size) tip=['WiredUSB',tip.info] cmd=command.copy('image.fluent(\uECF0,12)'))
        item(image=image.fluent(\uECF1,default_icon_size) tip=['WirelessUSB',tip.info] cmd=command.copy('image.fluent(\uECF1,12)'))

        item(image=image.fluent(\uECF3,default_icon_size) tip=['USBSafeConnect',tip.info] cmd=command.copy('image.fluent(\uECF3,12)') col)
        item(image=image.fluent(\uED0C,default_icon_size) tip=['ActionCenterNotificationMirrored',tip.info] cmd=command.copy('image.fluent(\uED0C,12)'))
        item(image=image.fluent(\uED0D,default_icon_size) tip=['ActionCenterMirrored',tip.info] cmd=command.copy('image.fluent(\uED0D,12)'))
        item(image=image.fluent(\uED0E,default_icon_size) tip=['SubscriptionAdd',tip.info] cmd=command.copy('image.fluent(\uED0E,12)'))
        item(image=image.fluent(\uED10,default_icon_size) tip=['ResetDevice',tip.info] cmd=command.copy('image.fluent(\uED10,12)'))
        item(image=image.fluent(\uED11,default_icon_size) tip=['SubscriptionAddMirrored',tip.info] cmd=command.copy('image.fluent(\uED11,12)'))
        item(image=image.fluent(\uED14,default_icon_size) tip=['QRCode',tip.info] cmd=command.copy('image.fluent(\uED14,12)'))
        item(image=image.fluent(\uED15,default_icon_size) tip=['Feedback',tip.info] cmd=command.copy('image.fluent(\uED15,12)'))
        item(image=image.fluent(\uED1A,default_icon_size) tip=['Hide',tip.info] cmd=command.copy('image.fluent(\uED1A,12)'))
        item(image=image.fluent(\uED1E,default_icon_size) tip=['Subtitles',tip.info] cmd=command.copy('image.fluent(\uED1E,12)'))
        item(image=image.fluent(\uED1F,default_icon_size) tip=['SubtitlesAudio',tip.info] cmd=command.copy('image.fluent(\uED1F,12)'))
        item(image=image.fluent(\uED25,default_icon_size) tip=['OpenFolderHorizontal',tip.info] cmd=command.copy('image.fluent(\uED25,12)'))
        item(image=image.fluent(\uED28,default_icon_size) tip=['CalendarMirrored',tip.info] cmd=command.copy('image.fluent(\uED28,12)'))
        item(image=image.fluent(\uED2A,default_icon_size) tip=['MobeSIM',tip.info] cmd=command.copy('image.fluent(\uED2A,12)'))
        item(image=image.fluent(\uED2B,default_icon_size) tip=['MobeSIMNoProfile',tip.info] cmd=command.copy('image.fluent(\uED2B,12)'))
        item(image=image.fluent(\uED2C,default_icon_size) tip=['MobeSIMLocked',tip.info] cmd=command.copy('image.fluent(\uED2C,12)'))

        item(image=image.fluent(\uED2D,default_icon_size) tip=['MobeSIMBusy',tip.info] cmd=command.copy('image.fluent(\uED2D,12)') col)
        item(image=image.fluent(\uED2E,default_icon_size) tip=['SignalError',tip.info] cmd=command.copy('image.fluent(\uED2E,12)'))
        item(image=image.fluent(\uED2F,default_icon_size) tip=['StreamingEnterprise',tip.info] cmd=command.copy('image.fluent(\uED2F,12)'))
        item(image=image.fluent(\uED30,default_icon_size) tip=['Headphone0',tip.info] cmd=command.copy('image.fluent(\uED30,12)'))
        item(image=image.fluent(\uED31,default_icon_size) tip=['Headphone1',tip.info] cmd=command.copy('image.fluent(\uED31,12)'))
        item(image=image.fluent(\uED32,default_icon_size) tip=['Headphone2',tip.info] cmd=command.copy('image.fluent(\uED32,12)'))
        item(image=image.fluent(\uED33,default_icon_size) tip=['Headphone3',tip.info] cmd=command.copy('image.fluent(\uED33,12)'))
        item(image=image.fluent(\uED35,default_icon_size) tip=['Apps',tip.info] cmd=command.copy('image.fluent(\uED35,12)'))
        item(image=image.fluent(\uED39,default_icon_size) tip=['KeyboardBrightness',tip.info] cmd=command.copy('image.fluent(\uED39,12)'))
        item(image=image.fluent(\uED3A,default_icon_size) tip=['KeyboardLowerBrightness',tip.info] cmd=command.copy('image.fluent(\uED3A,12)'))
        item(image=image.fluent(\uED3C,default_icon_size) tip=['SkipBack10',tip.info] cmd=command.copy('image.fluent(\uED3C,12)'))
        item(image=image.fluent(\uED3D,default_icon_size) tip=['SkipForward30',tip.info] cmd=command.copy('image.fluent(\uED3D,12)'))
        item(image=image.fluent(\uED41,default_icon_size) tip=['TreeFolderFolder',tip.info] cmd=command.copy('image.fluent(\uED41,12)'))
        item(image=image.fluent(\uED42,default_icon_size) tip=['TreeFolderFolderFill',tip.info] cmd=command.copy('image.fluent(\uED42,12)'))
        item(image=image.fluent(\uED43,default_icon_size) tip=['TreeFolderFolderOpen',tip.info] cmd=command.copy('image.fluent(\uED43,12)'))
        item(image=image.fluent(\uED44,default_icon_size) tip=['TreeFolderFolderOpenFill',tip.info] cmd=command.copy('image.fluent(\uED44,12)'))

        item(image=image.fluent(\uED47,default_icon_size) tip=['MultimediaDMP',tip.info] cmd=command.copy('image.fluent(\uED47,12)') col)
        item(image=image.fluent(\uED4C,default_icon_size) tip=['KeyboardOneHanded',tip.info] cmd=command.copy('image.fluent(\uED4C,12)'))
        item(image=image.fluent(\uED4D,default_icon_size) tip=['Narrator',tip.info] cmd=command.copy('image.fluent(\uED4D,12)'))
        item(image=image.fluent(\uED53,default_icon_size) tip=['EmojiTabPeople',tip.info] cmd=command.copy('image.fluent(\uED53,12)'))
        item(image=image.fluent(\uED54,default_icon_size) tip=['EmojiTabSmilesAnimals',tip.info] cmd=command.copy('image.fluent(\uED54,12)'))
        item(image=image.fluent(\uED55,default_icon_size) tip=['EmojiTabCelebrationObjects',tip.info] cmd=command.copy('image.fluent(\uED55,12)'))
        item(image=image.fluent(\uED56,default_icon_size) tip=['EmojiTabFoodPlants',tip.info] cmd=command.copy('image.fluent(\uED56,12)'))
        item(image=image.fluent(\uED57,default_icon_size) tip=['EmojiTabTransitPlaces',tip.info] cmd=command.copy('image.fluent(\uED57,12)'))
        item(image=image.fluent(\uED58,default_icon_size) tip=['EmojiTabSymbols',tip.info] cmd=command.copy('image.fluent(\uED58,12)'))
        item(image=image.fluent(\uED59,default_icon_size) tip=['EmojiTabTextSmiles',tip.info] cmd=command.copy('image.fluent(\uED59,12)'))
        item(image=image.fluent(\uED5A,default_icon_size) tip=['EmojiTabFavorites',tip.info] cmd=command.copy('image.fluent(\uED5A,12)'))
        item(image=image.fluent(\uED5B,default_icon_size) tip=['EmojiSwatch',tip.info] cmd=command.copy('image.fluent(\uED5B,12)'))
        item(image=image.fluent(\uED5C,default_icon_size) tip=['ConnectApp',tip.info] cmd=command.copy('image.fluent(\uED5C,12)'))
        item(image=image.fluent(\uED5D,default_icon_size) tip=['CompanionDeviceFramework',tip.info] cmd=command.copy('image.fluent(\uED5D,12)'))
        item(image=image.fluent(\uED5E,default_icon_size) tip=['Ruler',tip.info] cmd=command.copy('image.fluent(\uED5E,12)'))
        item(image=image.fluent(\uED5F,default_icon_size) tip=['FingerInking',tip.info] cmd=command.copy('image.fluent(\uED5F,12)'))

        item(image=image.fluent(\uED60,default_icon_size) tip=['StrokeErase',tip.info] cmd=command.copy('image.fluent(\uED60,12)') col)
        item(image=image.fluent(\uED61,default_icon_size) tip=['PointErase',tip.info] cmd=command.copy('image.fluent(\uED61,12)'))
        item(image=image.fluent(\uED62,default_icon_size) tip=['ClearAllInk',tip.info] cmd=command.copy('image.fluent(\uED62,12)'))
        item(image=image.fluent(\uED63,default_icon_size) tip=['Pencil',tip.info] cmd=command.copy('image.fluent(\uED63,12)'))
        item(image=image.fluent(\uED64,default_icon_size) tip=['Marker',tip.info] cmd=command.copy('image.fluent(\uED64,12)'))
        item(image=image.fluent(\uED65,default_icon_size) tip=['InkingCaret',tip.info] cmd=command.copy('image.fluent(\uED65,12)'))
        item(image=image.fluent(\uED66,default_icon_size) tip=['InkingColorOutline',tip.info] cmd=command.copy('image.fluent(\uED66,12)'))
        item(image=image.fluent(\uED67,default_icon_size) tip=['InkingColorFill',tip.info] cmd=command.copy('image.fluent(\uED67,12)'))
        item(image=image.fluent(\uEDA2,default_icon_size) tip=['HardDrive',tip.info] cmd=command.copy('image.fluent(\uEDA2,12)'))
        item(image=image.fluent(\uEDA3,default_icon_size) tip=['NetworkAdapter',tip.info] cmd=command.copy('image.fluent(\uEDA3,12)'))
        item(image=image.fluent(\uEDA4,default_icon_size) tip=['Touchscreen',tip.info] cmd=command.copy('image.fluent(\uEDA4,12)'))
        item(image=image.fluent(\uEDA5,default_icon_size) tip=['NetworkPrinter',tip.info] cmd=command.copy('image.fluent(\uEDA5,12)'))
        item(image=image.fluent(\uEDA6,default_icon_size) tip=['CloudPrinter',tip.info] cmd=command.copy('image.fluent(\uEDA6,12)'))
        item(image=image.fluent(\uEDA7,default_icon_size) tip=['KeyboardShortcut',tip.info] cmd=command.copy('image.fluent(\uEDA7,12)'))
        item(image=image.fluent(\uEDA8,default_icon_size) tip=['BrushSize',tip.info] cmd=command.copy('image.fluent(\uEDA8,12)'))
        item(image=image.fluent(\uEDA9,default_icon_size) tip=['NarratorForward',tip.info] cmd=command.copy('image.fluent(\uEDA9,12)'))

        item(image=image.fluent(\uEDAA,default_icon_size) tip=['NarratorForwardMirrored',tip.info] cmd=command.copy('image.fluent(\uEDAA,12)') col)
        item(image=image.fluent(\uEDAB,default_icon_size) tip=['SyncBadge12',tip.info] cmd=command.copy('image.fluent(\uEDAB,12)'))
        item(image=image.fluent(\uEDAC,default_icon_size) tip=['RingerBadge12',tip.info] cmd=command.copy('image.fluent(\uEDAC,12)'))
        item(image=image.fluent(\uEDAD,default_icon_size) tip=['AsteriskBadge12',tip.info] cmd=command.copy('image.fluent(\uEDAD,12)'))
        item(image=image.fluent(\uEDAE,default_icon_size) tip=['ErrorBadge12',tip.info] cmd=command.copy('image.fluent(\uEDAE,12)'))
        item(image=image.fluent(\uEDAF,default_icon_size) tip=['CircleRingBadge12',tip.info] cmd=command.copy('image.fluent(\uEDAF,12)'))
        item(image=image.fluent(\uEDB0,default_icon_size) tip=['CircleFillBadge12',tip.info] cmd=command.copy('image.fluent(\uEDB0,12)'))
        item(image=image.fluent(\uEDB1,default_icon_size) tip=['ImportantBadge12',tip.info] cmd=command.copy('image.fluent(\uEDB1,12)'))
        item(image=image.fluent(\uEDB3,default_icon_size) tip=['MailBadge12',tip.info] cmd=command.copy('image.fluent(\uEDB3,12)'))
        item(image=image.fluent(\uEDB4,default_icon_size) tip=['PauseBadge12',tip.info] cmd=command.copy('image.fluent(\uEDB4,12)'))
        item(image=image.fluent(\uEDB5,default_icon_size) tip=['PlayBadge12',tip.info] cmd=command.copy('image.fluent(\uEDB5,12)'))
        item(image=image.fluent(\uEDC6,default_icon_size) tip=['PenWorkspace',tip.info] cmd=command.copy('image.fluent(\uEDC6,12)'))
        item(image=image.fluent(\uEDD5,default_icon_size) tip=['CaretLeft8',tip.info] cmd=command.copy('image.fluent(\uEDD5,12)'))
        item(image=image.fluent(\uEDD6,default_icon_size) tip=['CaretRight8',tip.info] cmd=command.copy('image.fluent(\uEDD6,12)'))
        item(image=image.fluent(\uEDD7,default_icon_size) tip=['CaretUp8',tip.info] cmd=command.copy('image.fluent(\uEDD7,12)'))
        item(image=image.fluent(\uEDD8,default_icon_size) tip=['CaretDown8',tip.info] cmd=command.copy('image.fluent(\uEDD8,12)'))

        item(image=image.fluent(\uEDD9,default_icon_size) tip=['CaretLeftSolid8',tip.info] cmd=command.copy('image.fluent(\uEDD9,12)') col)
        item(image=image.fluent(\uEDDA,default_icon_size) tip=['CaretRightSolid8',tip.info] cmd=command.copy('image.fluent(\uEDDA,12)'))
        item(image=image.fluent(\uEDDB,default_icon_size) tip=['CaretUpSolid8',tip.info] cmd=command.copy('image.fluent(\uEDDB,12)'))
        item(image=image.fluent(\uEDDC,default_icon_size) tip=['CaretDownSolid8',tip.info] cmd=command.copy('image.fluent(\uEDDC,12)'))
        item(image=image.fluent(\uEDE0,default_icon_size) tip=['Strikethrough',tip.info] cmd=command.copy('image.fluent(\uEDE0,12)'))
        item(image=image.fluent(\uEDE1,default_icon_size) tip=['Export',tip.info] cmd=command.copy('image.fluent(\uEDE1,12)'))
        item(image=image.fluent(\uEDE2,default_icon_size) tip=['ExportMirrored',tip.info] cmd=command.copy('image.fluent(\uEDE2,12)'))
        item(image=image.fluent(\uEDE3,default_icon_size) tip=['ButtonMenu',tip.info] cmd=command.copy('image.fluent(\uEDE3,12)'))
        item(image=image.fluent(\uEDE4,default_icon_size) tip=['CloudSearch',tip.info] cmd=command.copy('image.fluent(\uEDE4,12)'))
        item(image=image.fluent(\uEDE5,default_icon_size) tip=['PinyinIMELogo',tip.info] cmd=command.copy('image.fluent(\uEDE5,12)'))
        item(image=image.fluent(\uEDFB,default_icon_size) tip=['CalligraphyPen',tip.info] cmd=command.copy('image.fluent(\uEDFB,12)'))
        item(image=image.fluent(\uEE35,default_icon_size) tip=['ReplyMirrored',tip.info] cmd=command.copy('image.fluent(\uEE35,12)'))
        item(image=image.fluent(\uEE3F,default_icon_size) tip=['LockscreenDesktop',tip.info] cmd=command.copy('image.fluent(\uEE3F,12)'))
        item(image=image.fluent(\uEE40,default_icon_size) tip=['TaskViewSettings',tip.info] cmd=command.copy('image.fluent(\uEE40,12)'))
        item(image=image.fluent(\uEE47,default_icon_size) tip=['MiniExpand2Mirrored',tip.info] cmd=command.copy('image.fluent(\uEE47,12)'))
        item(image=image.fluent(\uEE49,default_icon_size) tip=['MiniContract2Mirrored',tip.info] cmd=command.copy('image.fluent(\uEE49,12)'))

        item(image=image.fluent(\uEE4A,default_icon_size) tip=['Play36',tip.info] cmd=command.copy('image.fluent(\uEE4A,12)') col)
        item(image=image.fluent(\uEE56,default_icon_size) tip=['PenPalette',tip.info] cmd=command.copy('image.fluent(\uEE56,12)'))
        item(image=image.fluent(\uEE57,default_icon_size) tip=['GuestUser',tip.info] cmd=command.copy('image.fluent(\uEE57,12)'))
        item(image=image.fluent(\uEE63,default_icon_size) tip=['SettingsBattery',tip.info] cmd=command.copy('image.fluent(\uEE63,12)'))
        item(image=image.fluent(\uEE64,default_icon_size) tip=['TaskbarPhone',tip.info] cmd=command.copy('image.fluent(\uEE64,12)'))
        item(image=image.fluent(\uEE65,default_icon_size) tip=['LockScreenGlance',tip.info] cmd=command.copy('image.fluent(\uEE65,12)'))
        item(image=image.fluent(\uEE6F,default_icon_size) tip=['GenericScan',tip.info] cmd=command.copy('image.fluent(\uEE6F,12)'))
        item(image=image.fluent(\uEE71,default_icon_size) tip=['ImageExport',tip.info] cmd=command.copy('image.fluent(\uEE71,12)'))
        item(image=image.fluent(\uEE77,default_icon_size) tip=['WifiEthernet',tip.info] cmd=command.copy('image.fluent(\uEE77,12)'))
        item(image=image.fluent(\uEE79,default_icon_size) tip=['ActionCenterQuiet',tip.info] cmd=command.copy('image.fluent(\uEE79,12)'))
        item(image=image.fluent(\uEE7A,default_icon_size) tip=['ActionCenterQuietNotification',tip.info] cmd=command.copy('image.fluent(\uEE7A,12)'))
        item(image=image.fluent(\uEE92,default_icon_size) tip=['TrackersMirrored',tip.info] cmd=command.copy('image.fluent(\uEE92,12)'))
        item(image=image.fluent(\uEE93,default_icon_size) tip=['DateTimeMirrored',tip.info] cmd=command.copy('image.fluent(\uEE93,12)'))
        item(image=image.fluent(\uEE94,default_icon_size) tip=['Wheel',tip.info] cmd=command.copy('image.fluent(\uEE94,12)'))
        item(image=image.fluent(\uEEA3,default_icon_size) tip=['VirtualMachineGroup',tip.info] cmd=command.copy('image.fluent(\uEEA3,12)'))
        item(image=image.fluent(\uEECA,default_icon_size) tip=['ButtonView2',tip.info] cmd=command.copy('image.fluent(\uEECA,12)'))

        item(image=image.fluent(\uEF15,default_icon_size) tip=['PenWorkspaceMirrored',tip.info] cmd=command.copy('image.fluent(\uEF15,12)') col)
        item(image=image.fluent(\uEF16,default_icon_size) tip=['PenPaletteMirrored',tip.info] cmd=command.copy('image.fluent(\uEF16,12)'))
        item(image=image.fluent(\uEF17,default_icon_size) tip=['StrokeEraseMirrored',tip.info] cmd=command.copy('image.fluent(\uEF17,12)'))
        item(image=image.fluent(\uEF18,default_icon_size) tip=['PointEraseMirrored',tip.info] cmd=command.copy('image.fluent(\uEF18,12)'))
        item(image=image.fluent(\uEF19,default_icon_size) tip=['ClearAllInkMirrored',tip.info] cmd=command.copy('image.fluent(\uEF19,12)'))
        item(image=image.fluent(\uEF1F,default_icon_size) tip=['BackgroundToggle',tip.info] cmd=command.copy('image.fluent(\uEF1F,12)'))
        item(image=image.fluent(\uEF20,default_icon_size) tip=['Marquee',tip.info] cmd=command.copy('image.fluent(\uEF20,12)'))
        item(image=image.fluent(\uEF2C,default_icon_size) tip=['ChromeCloseContrast',tip.info] cmd=command.copy('image.fluent(\uEF2C,12)'))
        item(image=image.fluent(\uEF2D,default_icon_size) tip=['ChromeMinimizeContrast',tip.info] cmd=command.copy('image.fluent(\uEF2D,12)'))
        item(image=image.fluent(\uEF2E,default_icon_size) tip=['ChromeMaximizeContrast',tip.info] cmd=command.copy('image.fluent(\uEF2E,12)'))
        item(image=image.fluent(\uEF2F,default_icon_size) tip=['ChromeRestoreContrast',tip.info] cmd=command.copy('image.fluent(\uEF2F,12)'))
        item(image=image.fluent(\uEF31,default_icon_size) tip=['TrafficLight',tip.info] cmd=command.copy('image.fluent(\uEF31,12)'))
        item(image=image.fluent(\uEF3B,default_icon_size) tip=['Replay',tip.info] cmd=command.copy('image.fluent(\uEF3B,12)'))
        item(image=image.fluent(\uEF3C,default_icon_size) tip=['Eyedropper',tip.info] cmd=command.copy('image.fluent(\uEF3C,12)'))
        item(image=image.fluent(\uEF3D,default_icon_size) tip=['LineDisplay',tip.info] cmd=command.copy('image.fluent(\uEF3D,12)'))
        item(image=image.fluent(\uEF3E,default_icon_size) tip=['PINPad',tip.info] cmd=command.copy('image.fluent(\uEF3E,12)'))

        item(image=image.fluent(\uEF3F,default_icon_size) tip=['SignatureCapture',tip.info] cmd=command.copy('image.fluent(\uEF3F,12)') col)
        item(image=image.fluent(\uEF40,default_icon_size) tip=['ChipCardCreditCardReader',tip.info] cmd=command.copy('image.fluent(\uEF40,12)'))
        item(image=image.fluent(\uEF42,default_icon_size) tip=['MarketDown',tip.info] cmd=command.copy('image.fluent(\uEF42,12)'))
        item(image=image.fluent(\uEF58,default_icon_size) tip=['PlayerSettings',tip.info] cmd=command.copy('image.fluent(\uEF58,12)'))
        item(image=image.fluent(\uEF6B,default_icon_size) tip=['LandscapeOrientation',tip.info] cmd=command.copy('image.fluent(\uEF6B,12)'))
        item(image=image.fluent(\uEF90,default_icon_size) tip=['Flow',tip.info] cmd=command.copy('image.fluent(\uEF90,12)'))
        item(image=image.fluent(\uEFA5,default_icon_size) tip=['Touchpad',tip.info] cmd=command.copy('image.fluent(\uEFA5,12)'))
        item(image=image.fluent(\uEFA9,default_icon_size) tip=['Speech',tip.info] cmd=command.copy('image.fluent(\uEFA9,12)'))
        item(image=image.fluent(\uF000,default_icon_size) tip=['KnowledgeArticle',tip.info] cmd=command.copy('image.fluent(\uF000,12)'))
        item(image=image.fluent(\uF003,default_icon_size) tip=['Relationship',tip.info] cmd=command.copy('image.fluent(\uF003,12)'))
        item(image=image.fluent(\uF012,default_icon_size) tip=['ZipFolder',tip.info] cmd=command.copy('image.fluent(\uF012,12)'))
        item(image=image.fluent(\uF080,default_icon_size) tip=['DefaultAPN',tip.info] cmd=command.copy('image.fluent(\uF080,12)'))
        item(image=image.fluent(\uF081,default_icon_size) tip=['UserAPN',tip.info] cmd=command.copy('image.fluent(\uF081,12)'))
        item(image=image.fluent(\uF085,default_icon_size) tip=['DoublePinyin',tip.info] cmd=command.copy('image.fluent(\uF085,12)'))
        item(image=image.fluent(\uF08C,default_icon_size) tip=['BlueLight',tip.info] cmd=command.copy('image.fluent(\uF08C,12)'))
        item(image=image.fluent(\uF08D,default_icon_size) tip=['CaretSolidLeft',tip.info] cmd=command.copy('image.fluent(\uF08D,12)'))
    }

    menu(title='Fluent #5')
    {
        item(image=image.fluent(\uF08E,default_icon_size) tip=['CaretSolidDown',tip.info] cmd=command.copy('image.fluent(\uF08E,12)'))
        item(image=image.fluent(\uF08F,default_icon_size) tip=['CaretSolidRight',tip.info] cmd=command.copy('image.fluent(\uF08F,12)'))
        item(image=image.fluent(\uF090,default_icon_size) tip=['CaretSolidUp',tip.info] cmd=command.copy('image.fluent(\uF090,12)'))
        item(image=image.fluent(\uF093,default_icon_size) tip=['ButtonA',tip.info] cmd=command.copy('image.fluent(\uF093,12)'))
        item(image=image.fluent(\uF094,default_icon_size) tip=['ButtonB',tip.info] cmd=command.copy('image.fluent(\uF094,12)'))
        item(image=image.fluent(\uF095,default_icon_size) tip=['ButtonY',tip.info] cmd=command.copy('image.fluent(\uF095,12)'))
        item(image=image.fluent(\uF096,default_icon_size) tip=['ButtonX',tip.info] cmd=command.copy('image.fluent(\uF096,12)'))
        item(image=image.fluent(\uF0AD,default_icon_size) tip=['ArrowUp8',tip.info] cmd=command.copy('image.fluent(\uF0AD,12)'))
        item(image=image.fluent(\uF0AE,default_icon_size) tip=['ArrowDown8',tip.info] cmd=command.copy('image.fluent(\uF0AE,12)'))
        item(image=image.fluent(\uF0AF,default_icon_size) tip=['ArrowRight8',tip.info] cmd=command.copy('image.fluent(\uF0AF,12)'))
        item(image=image.fluent(\uF0B0,default_icon_size) tip=['ArrowLeft8',tip.info] cmd=command.copy('image.fluent(\uF0B0,12)'))
        item(image=image.fluent(\uF0B2,default_icon_size) tip=['QuarentinedItems',tip.info] cmd=command.copy('image.fluent(\uF0B2,12)'))
        item(image=image.fluent(\uF0B3,default_icon_size) tip=['QuarentinedItemsMirrored',tip.info] cmd=command.copy('image.fluent(\uF0B3,12)'))
        item(image=image.fluent(\uF0B4,default_icon_size) tip=['Protractor',tip.info] cmd=command.copy('image.fluent(\uF0B4,12)'))
        item(image=image.fluent(\uF0B5,default_icon_size) tip=['ChecklistMirrored',tip.info] cmd=command.copy('image.fluent(\uF0B5,12)'))
        item(image=image.fluent(\uF0B6,default_icon_size) tip=['StatusCircle7',tip.info] cmd=command.copy('image.fluent(\uF0B6,12)'))

        item(image=image.fluent(\uF0B7,default_icon_size) tip=['StatusCheckmark7',tip.info] cmd=command.copy('image.fluent(\uF0B7,12)') col)
        item(image=image.fluent(\uF0B8,default_icon_size) tip=['StatusErrorCircle7',tip.info] cmd=command.copy('image.fluent(\uF0B8,12)'))
        item(image=image.fluent(\uF0B9,default_icon_size) tip=['Connected',tip.info] cmd=command.copy('image.fluent(\uF0B9,12)'))
        item(image=image.fluent(\uF0C6,default_icon_size) tip=['PencilFill',tip.info] cmd=command.copy('image.fluent(\uF0C6,12)'))
        item(image=image.fluent(\uF0C7,default_icon_size) tip=['CalligraphyFill',tip.info] cmd=command.copy('image.fluent(\uF0C7,12)'))
        item(image=image.fluent(\uF0CA,default_icon_size) tip=['QuarterStarLeft',tip.info] cmd=command.copy('image.fluent(\uF0CA,12)'))
        item(image=image.fluent(\uF0CB,default_icon_size) tip=['QuarterStarRight',tip.info] cmd=command.copy('image.fluent(\uF0CB,12)'))
        item(image=image.fluent(\uF0CC,default_icon_size) tip=['ThreeQuarterStarLeft',tip.info] cmd=command.copy('image.fluent(\uF0CC,12)'))
        item(image=image.fluent(\uF0CD,default_icon_size) tip=['ThreeQuarterStarRight',tip.info] cmd=command.copy('image.fluent(\uF0CD,12)'))
        item(image=image.fluent(\uF0CE,default_icon_size) tip=['QuietHoursBadge12',tip.info] cmd=command.copy('image.fluent(\uF0CE,12)'))
        item(image=image.fluent(\uF0D2,default_icon_size) tip=['BackMirrored',tip.info] cmd=command.copy('image.fluent(\uF0D2,12)'))
        item(image=image.fluent(\uF0D3,default_icon_size) tip=['ForwardMirrored',tip.info] cmd=command.copy('image.fluent(\uF0D3,12)'))
        item(image=image.fluent(\uF0D5,default_icon_size) tip=['ChromeBackContrast',tip.info] cmd=command.copy('image.fluent(\uF0D5,12)'))
        item(image=image.fluent(\uF0D6,default_icon_size) tip=['ChromeBackContrastMirrored',tip.info] cmd=command.copy('image.fluent(\uF0D6,12)'))
        item(image=image.fluent(\uF0D7,default_icon_size) tip=['ChromeBackToWindowContrast',tip.info] cmd=command.copy('image.fluent(\uF0D7,12)'))
        item(image=image.fluent(\uF0D8,default_icon_size) tip=['ChromeFullScreenContrast',tip.info] cmd=command.copy('image.fluent(\uF0D8,12)'))

        item(image=image.fluent(\uF0E2,default_icon_size) tip=['GridView',tip.info] cmd=command.copy('image.fluent(\uF0E2,12)') col)
        item(image=image.fluent(\uF0E3,default_icon_size) tip=['ClipboardList',tip.info] cmd=command.copy('image.fluent(\uF0E3,12)'))
        item(image=image.fluent(\uF0E4,default_icon_size) tip=['ClipboardListMirrored',tip.info] cmd=command.copy('image.fluent(\uF0E4,12)'))
        item(image=image.fluent(\uF0E5,default_icon_size) tip=['OutlineQuarterStarLeft',tip.info] cmd=command.copy('image.fluent(\uF0E5,12)'))
        item(image=image.fluent(\uF0E6,default_icon_size) tip=['OutlineQuarterStarRight',tip.info] cmd=command.copy('image.fluent(\uF0E6,12)'))
        item(image=image.fluent(\uF0E7,default_icon_size) tip=['OutlineHalfStarLeft',tip.info] cmd=command.copy('image.fluent(\uF0E7,12)'))
        item(image=image.fluent(\uF0E8,default_icon_size) tip=['OutlineHalfStarRight',tip.info] cmd=command.copy('image.fluent(\uF0E8,12)'))
        item(image=image.fluent(\uF0E9,default_icon_size) tip=['OutlineThreeQuarterStarLeft',tip.info] cmd=command.copy('image.fluent(\uF0E9,12)'))
        item(image=image.fluent(\uF0EA,default_icon_size) tip=['OutlineThreeQuarterStarRight',tip.info] cmd=command.copy('image.fluent(\uF0EA,12)'))
        item(image=image.fluent(\uF0EB,default_icon_size) tip=['SpatialVolume0',tip.info] cmd=command.copy('image.fluent(\uF0EB,12)'))
        item(image=image.fluent(\uF0EC,default_icon_size) tip=['SpatialVolume1',tip.info] cmd=command.copy('image.fluent(\uF0EC,12)'))
        item(image=image.fluent(\uF0ED,default_icon_size) tip=['SpatialVolume2',tip.info] cmd=command.copy('image.fluent(\uF0ED,12)'))
        item(image=image.fluent(\uF0EE,default_icon_size) tip=['SpatialVolume3',tip.info] cmd=command.copy('image.fluent(\uF0EE,12)'))
        item(image=image.fluent(\uF0EF,default_icon_size) tip=['ApplicationGuard',tip.info] cmd=command.copy('image.fluent(\uF0EF,12)'))
        item(image=image.fluent(\uF0F7,default_icon_size) tip=['OutlineStarLeftHalf',tip.info] cmd=command.copy('image.fluent(\uF0F7,12)'))
        item(image=image.fluent(\uF0F8,default_icon_size) tip=['OutlineStarRightHalf',tip.info] cmd=command.copy('image.fluent(\uF0F8,12)'))

        item(image=image.fluent(\uF0F9,default_icon_size) tip=['ChromeAnnotateContrast',tip.info] cmd=command.copy('image.fluent(\uF0F9,12)') col)
        item(image=image.fluent(\uF0FB,default_icon_size) tip=['DefenderBadge12',tip.info] cmd=command.copy('image.fluent(\uF0FB,12)'))
        item(image=image.fluent(\uF103,default_icon_size) tip=['DetachablePC',tip.info] cmd=command.copy('image.fluent(\uF103,12)'))
        item(image=image.fluent(\uF108,default_icon_size) tip=['LeftStick',tip.info] cmd=command.copy('image.fluent(\uF108,12)'))
        item(image=image.fluent(\uF109,default_icon_size) tip=['RightStick',tip.info] cmd=command.copy('image.fluent(\uF109,12)'))
        item(image=image.fluent(\uF10A,default_icon_size) tip=['TriggerLeft',tip.info] cmd=command.copy('image.fluent(\uF10A,12)'))
        item(image=image.fluent(\uF10B,default_icon_size) tip=['TriggerRight',tip.info] cmd=command.copy('image.fluent(\uF10B,12)'))
        item(image=image.fluent(\uF10C,default_icon_size) tip=['BumperLeft',tip.info] cmd=command.copy('image.fluent(\uF10C,12)'))
        item(image=image.fluent(\uF10D,default_icon_size) tip=['BumperRight',tip.info] cmd=command.copy('image.fluent(\uF10D,12)'))
        item(image=image.fluent(\uF10E,default_icon_size) tip=['Dpad',tip.info] cmd=command.copy('image.fluent(\uF10E,12)'))
        item(image=image.fluent(\uF110,default_icon_size) tip=['EnglishPunctuation',tip.info] cmd=command.copy('image.fluent(\uF110,12)'))
        item(image=image.fluent(\uF111,default_icon_size) tip=['ChinesePunctuation',tip.info] cmd=command.copy('image.fluent(\uF111,12)'))
        item(image=image.fluent(\uF119,default_icon_size) tip=['HMD',tip.info] cmd=command.copy('image.fluent(\uF119,12)'))
        item(image=image.fluent(\uF11B,default_icon_size) tip=['CtrlSpatialRight',tip.info] cmd=command.copy('image.fluent(\uF11B,12)'))
        item(image=image.fluent(\uF126,default_icon_size) tip=['PaginationDotOutline10',tip.info] cmd=command.copy('image.fluent(\uF126,12)'))
        item(image=image.fluent(\uF127,default_icon_size) tip=['PaginationDotSolid10',tip.info] cmd=command.copy('image.fluent(\uF127,12)'))

        item(image=image.fluent(\uF128,default_icon_size) tip=['StrokeErase2',tip.info] cmd=command.copy('image.fluent(\uF128,12)') col)
        item(image=image.fluent(\uF129,default_icon_size) tip=['SmallErase',tip.info] cmd=command.copy('image.fluent(\uF129,12)'))
        item(image=image.fluent(\uF12A,default_icon_size) tip=['LargeErase',tip.info] cmd=command.copy('image.fluent(\uF12A,12)'))
        item(image=image.fluent(\uF12B,default_icon_size) tip=['FolderHorizontal',tip.info] cmd=command.copy('image.fluent(\uF12B,12)'))
        item(image=image.fluent(\uF12E,default_icon_size) tip=['MicrophoneListening',tip.info] cmd=command.copy('image.fluent(\uF12E,12)'))
        item(image=image.fluent(\uF12F,default_icon_size) tip=['StatusExclamationCircle7',tip.info] cmd=command.copy('image.fluent(\uF12F,12)'))
        item(image=image.fluent(\uF131,default_icon_size) tip=['Video360',tip.info] cmd=command.copy('image.fluent(\uF131,12)'))
        item(image=image.fluent(\uF133,default_icon_size) tip=['GiftboxOpen',tip.info] cmd=command.copy('image.fluent(\uF133,12)'))
        item(image=image.fluent(\uF136,default_icon_size) tip=['StatusCircleOuter',tip.info] cmd=command.copy('image.fluent(\uF136,12)'))
        item(image=image.fluent(\uF137,default_icon_size) tip=['StatusCircleInner',tip.info] cmd=command.copy('image.fluent(\uF137,12)'))
        item(image=image.fluent(\uF138,default_icon_size) tip=['StatusCircleRing',tip.info] cmd=command.copy('image.fluent(\uF138,12)'))
        item(image=image.fluent(\uF139,default_icon_size) tip=['StatusTriangleOuter',tip.info] cmd=command.copy('image.fluent(\uF139,12)'))
        item(image=image.fluent(\uF13A,default_icon_size) tip=['StatusTriangleInner',tip.info] cmd=command.copy('image.fluent(\uF13A,12)'))
        item(image=image.fluent(\uF13B,default_icon_size) tip=['StatusTriangleExclamation',tip.info] cmd=command.copy('image.fluent(\uF13B,12)'))
        item(image=image.fluent(\uF13C,default_icon_size) tip=['StatusCircleExclamation',tip.info] cmd=command.copy('image.fluent(\uF13C,12)'))
        item(image=image.fluent(\uF13D,default_icon_size) tip=['StatusCircleErrorX',tip.info] cmd=command.copy('image.fluent(\uF13D,12)'))

        item(image=image.fluent(\uF13E,default_icon_size) tip=['StatusCircleCheckmark',tip.info] cmd=command.copy('image.fluent(\uF13E,12)') col)
        item(image=image.fluent(\uF13F,default_icon_size) tip=['StatusCircleInfo',tip.info] cmd=command.copy('image.fluent(\uF13F,12)'))
        item(image=image.fluent(\uF140,default_icon_size) tip=['StatusCircleBlock',tip.info] cmd=command.copy('image.fluent(\uF140,12)'))
        item(image=image.fluent(\uF141,default_icon_size) tip=['StatusCircleBlock2',tip.info] cmd=command.copy('image.fluent(\uF141,12)'))
        item(image=image.fluent(\uF142,default_icon_size) tip=['StatusCircleQuestionMark',tip.info] cmd=command.copy('image.fluent(\uF142,12)'))
        item(image=image.fluent(\uF143,default_icon_size) tip=['StatusCircleSync',tip.info] cmd=command.copy('image.fluent(\uF143,12)'))
        item(image=image.fluent(\uF146,default_icon_size) tip=['Dial1',tip.info] cmd=command.copy('image.fluent(\uF146,12)'))
        item(image=image.fluent(\uF147,default_icon_size) tip=['Dial2',tip.info] cmd=command.copy('image.fluent(\uF147,12)'))
        item(image=image.fluent(\uF148,default_icon_size) tip=['Dial3',tip.info] cmd=command.copy('image.fluent(\uF148,12)'))
        item(image=image.fluent(\uF149,default_icon_size) tip=['Dial4',tip.info] cmd=command.copy('image.fluent(\uF149,12)'))
        item(image=image.fluent(\uF14A,default_icon_size) tip=['Dial5',tip.info] cmd=command.copy('image.fluent(\uF14A,12)'))
        item(image=image.fluent(\uF14B,default_icon_size) tip=['Dial6',tip.info] cmd=command.copy('image.fluent(\uF14B,12)'))
        item(image=image.fluent(\uF14C,default_icon_size) tip=['Dial7',tip.info] cmd=command.copy('image.fluent(\uF14C,12)'))
        item(image=image.fluent(\uF14D,default_icon_size) tip=['Dial8',tip.info] cmd=command.copy('image.fluent(\uF14D,12)'))
        item(image=image.fluent(\uF14E,default_icon_size) tip=['Dial9',tip.info] cmd=command.copy('image.fluent(\uF14E,12)'))
        item(image=image.fluent(\uF14F,default_icon_size) tip=['Dial10',tip.info] cmd=command.copy('image.fluent(\uF14F,12)'))

        item(image=image.fluent(\uF150,default_icon_size) tip=['Dial11',tip.info] cmd=command.copy('image.fluent(\uF150,12)') col)
        item(image=image.fluent(\uF151,default_icon_size) tip=['Dial12',tip.info] cmd=command.copy('image.fluent(\uF151,12)'))
        item(image=image.fluent(\uF152,default_icon_size) tip=['Dial13',tip.info] cmd=command.copy('image.fluent(\uF152,12)'))
        item(image=image.fluent(\uF153,default_icon_size) tip=['Dial14',tip.info] cmd=command.copy('image.fluent(\uF153,12)'))
        item(image=image.fluent(\uF154,default_icon_size) tip=['Dial15',tip.info] cmd=command.copy('image.fluent(\uF154,12)'))
        item(image=image.fluent(\uF155,default_icon_size) tip=['Dial16',tip.info] cmd=command.copy('image.fluent(\uF155,12)'))
        item(image=image.fluent(\uF156,default_icon_size) tip=['DialShape1',tip.info] cmd=command.copy('image.fluent(\uF156,12)'))
        item(image=image.fluent(\uF157,default_icon_size) tip=['DialShape2',tip.info] cmd=command.copy('image.fluent(\uF157,12)'))
        item(image=image.fluent(\uF158,default_icon_size) tip=['DialShape3',tip.info] cmd=command.copy('image.fluent(\uF158,12)'))
        item(image=image.fluent(\uF159,default_icon_size) tip=['DialShape4',tip.info] cmd=command.copy('image.fluent(\uF159,12)'))
        item(image=image.fluent(\uF15F,default_icon_size) tip=['ClosedCaptionsInternational',tip.info] cmd=command.copy('image.fluent(\uF15F,12)'))
        item(image=image.fluent(\uF161,default_icon_size) tip=['TollSolid',tip.info] cmd=command.copy('image.fluent(\uF161,12)'))
        item(image=image.fluent(\uF163,default_icon_size) tip=['TrafficCongestionSolid',tip.info] cmd=command.copy('image.fluent(\uF163,12)'))
        item(image=image.fluent(\uF164,default_icon_size) tip=['ExploreContentSingle',tip.info] cmd=command.copy('image.fluent(\uF164,12)'))
        item(image=image.fluent(\uF165,default_icon_size) tip=['CollapseContent',tip.info] cmd=command.copy('image.fluent(\uF165,12)'))
        item(image=image.fluent(\uF166,default_icon_size) tip=['CollapseContentSingle',tip.info] cmd=command.copy('image.fluent(\uF166,12)'))

        item(image=image.fluent(\uF167,default_icon_size) tip=['InfoSolid',tip.info] cmd=command.copy('image.fluent(\uF167,12)') col)
        item(image=image.fluent(\uF168,default_icon_size) tip=['GroupList',tip.info] cmd=command.copy('image.fluent(\uF168,12)'))
        item(image=image.fluent(\uF169,default_icon_size) tip=['CaretBottomRightSolidCenter8',tip.info] cmd=command.copy('image.fluent(\uF169,12)'))
        item(image=image.fluent(\uF16A,default_icon_size) tip=['ProgressRingDots',tip.info] cmd=command.copy('image.fluent(\uF16A,12)'))
        item(image=image.fluent(\uF16B,default_icon_size) tip=['Checkbox14',tip.info] cmd=command.copy('image.fluent(\uF16B,12)'))
        item(image=image.fluent(\uF16C,default_icon_size) tip=['CheckboxComposite14',tip.info] cmd=command.copy('image.fluent(\uF16C,12)'))
        item(image=image.fluent(\uF16D,default_icon_size) tip=['CheckboxIndeterminateCombo14',tip.info] cmd=command.copy('image.fluent(\uF16D,12)'))
        item(image=image.fluent(\uF16E,default_icon_size) tip=['CheckboxIndeterminateCombo',tip.info] cmd=command.copy('image.fluent(\uF16E,12)'))
        item(image=image.fluent(\uF175,default_icon_size) tip=['StatusPause7',tip.info] cmd=command.copy('image.fluent(\uF175,12)'))
        item(image=image.fluent(\uF17F,default_icon_size) tip=['CharacterAppearance',tip.info] cmd=command.copy('image.fluent(\uF17F,12)'))
        item(image=image.fluent(\uF180,default_icon_size) tip=['Lexicon',tip.info] cmd=command.copy('image.fluent(\uF180,12)'))
        item(image=image.fluent(\uF182,default_icon_size) tip=['ScreenTime',tip.info] cmd=command.copy('image.fluent(\uF182,12)'))
        item(image=image.fluent(\uF191,default_icon_size) tip=['HeadlessDevice',tip.info] cmd=command.copy('image.fluent(\uF191,12)'))
        item(image=image.fluent(\uF193,default_icon_size) tip=['NetworkSharing',tip.info] cmd=command.copy('image.fluent(\uF193,12)'))
        item(image=image.fluent(\uF19D,default_icon_size) tip=['EyeGaze',tip.info] cmd=command.copy('image.fluent(\uF19D,12)'))
        item(image=image.fluent(\uF19E,default_icon_size) tip=['ToggleLeft',tip.info] cmd=command.copy('image.fluent(\uF19E,12)'))

        item(image=image.fluent(\uF19F,default_icon_size) tip=['ToggleRight',tip.info] cmd=command.copy('image.fluent(\uF19F,12)') col)
        item(image=image.fluent(\uF1AD,default_icon_size) tip=['WindowsInsider',tip.info] cmd=command.copy('image.fluent(\uF1AD,12)'))
        item(image=image.fluent(\uF1CB,default_icon_size) tip=['ChromeSwitch',tip.info] cmd=command.copy('image.fluent(\uF1CB,12)'))
        item(image=image.fluent(\uF1CC,default_icon_size) tip=['ChromeSwitchContast',tip.info] cmd=command.copy('image.fluent(\uF1CC,12)'))
        item(image=image.fluent(\uF1D8,default_icon_size) tip=['StatusCheckmark',tip.info] cmd=command.copy('image.fluent(\uF1D8,12)'))
        item(image=image.fluent(\uF1D9,default_icon_size) tip=['StatusCheckmarkLeft',tip.info] cmd=command.copy('image.fluent(\uF1D9,12)'))
        item(image=image.fluent(\uF20C,default_icon_size) tip=['KeyboardLeftAligned',tip.info] cmd=command.copy('image.fluent(\uF20C,12)'))
        item(image=image.fluent(\uF20D,default_icon_size) tip=['KeyboardRightAligned',tip.info] cmd=command.copy('image.fluent(\uF20D,12)'))
        item(image=image.fluent(\uF210,default_icon_size) tip=['KeyboardSettings',tip.info] cmd=command.copy('image.fluent(\uF210,12)'))
        item(image=image.fluent(\uF211,default_icon_size) tip=['NetworkPhysical',tip.info] cmd=command.copy('image.fluent(\uF211,12)'))
        item(image=image.fluent(\uF22C,default_icon_size) tip=['IOT',tip.info] cmd=command.copy('image.fluent(\uF22C,12)'))
        item(image=image.fluent(\uF22E,default_icon_size) tip=['UnknownMirrored',tip.info] cmd=command.copy('image.fluent(\uF22E,12)'))
        item(image=image.fluent(\uF246,default_icon_size) tip=['ViewDashboard',tip.info] cmd=command.copy('image.fluent(\uF246,12)'))
        item(image=image.fluent(\uF259,default_icon_size) tip=['ExploitProtectionSettings',tip.info] cmd=command.copy('image.fluent(\uF259,12)'))
        item(image=image.fluent(\uF260,default_icon_size) tip=['KeyboardNarrow',tip.info] cmd=command.copy('image.fluent(\uF260,12)'))
        item(image=image.fluent(\uF261,default_icon_size) tip=['Keyboard12Key',tip.info] cmd=command.copy('image.fluent(\uF261,12)'))

        item(image=image.fluent(\uF26B,default_icon_size) tip=['KeyboardDock',tip.info] cmd=command.copy('image.fluent(\uF26B,12)') col)
        item(image=image.fluent(\uF26C,default_icon_size) tip=['KeyboardUndock',tip.info] cmd=command.copy('image.fluent(\uF26C,12)'))
        item(image=image.fluent(\uF26D,default_icon_size) tip=['KeyboardLeftDock',tip.info] cmd=command.copy('image.fluent(\uF26D,12)'))
        item(image=image.fluent(\uF26E,default_icon_size) tip=['KeyboardRightDock',tip.info] cmd=command.copy('image.fluent(\uF26E,12)'))
        item(image=image.fluent(\uF270,default_icon_size) tip=['Ear',tip.info] cmd=command.copy('image.fluent(\uF270,12)'))
        item(image=image.fluent(\uF271,default_icon_size) tip=['PointerHand',tip.info] cmd=command.copy('image.fluent(\uF271,12)'))
        item(image=image.fluent(\uF272,default_icon_size) tip=['Bullseye',tip.info] cmd=command.copy('image.fluent(\uF272,12)'))
        item(image=image.fluent(\uF28B,default_icon_size) tip=['DocumentApproval',tip.info] cmd=command.copy('image.fluent(\uF28B,12)'))
        item(image=image.fluent(\uF2B7,default_icon_size) tip=['LocaleLanguage',tip.info] cmd=command.copy('image.fluent(\uF2B7,12)'))
        item(image=image.fluent(\uF32A,default_icon_size) tip=['PassiveAuthentication',tip.info] cmd=command.copy('image.fluent(\uF32A,12)'))
        item(image=image.fluent(\uF354,default_icon_size) tip=['ColorSolid',tip.info] cmd=command.copy('image.fluent(\uF354,12)'))
        item(image=image.fluent(\uF384,default_icon_size) tip=['NetworkOffline',tip.info] cmd=command.copy('image.fluent(\uF384,12)'))
        item(image=image.fluent(\uF385,default_icon_size) tip=['NetworkConnected',tip.info] cmd=command.copy('image.fluent(\uF385,12)'))
        item(image=image.fluent(\uF386,default_icon_size) tip=['NetworkConnectedCheckmark',tip.info] cmd=command.copy('image.fluent(\uF386,12)'))
        item(image=image.fluent(\uF3B1,default_icon_size) tip=['SignOut',tip.info] cmd=command.copy('image.fluent(\uF3B1,12)'))
        item(image=image.fluent(\uF3CC,default_icon_size) tip=['StatusInfo',tip.info] cmd=command.copy('image.fluent(\uF3CC,12)'))

        item(image=image.fluent(\uF3CD,default_icon_size) tip=['StatusInfoLeft',tip.info] cmd=command.copy('image.fluent(\uF3CD,12)') col)
        item(image=image.fluent(\uF3E2,default_icon_size) tip=['NearbySharing',tip.info] cmd=command.copy('image.fluent(\uF3E2,12)'))
        item(image=image.fluent(\uF3E7,default_icon_size) tip=['CtrlSpatialLeft',tip.info] cmd=command.copy('image.fluent(\uF3E7,12)'))
        item(image=image.fluent(\uF404,default_icon_size) tip=['InteractiveDashboard',tip.info] cmd=command.copy('image.fluent(\uF404,12)'))
        item(image=image.fluent(\uF405,default_icon_size) tip=['DeclineCall',tip.info] cmd=command.copy('image.fluent(\uF405,12)'))
        item(image=image.fluent(\uF406,default_icon_size) tip=['ClippingTool',tip.info] cmd=command.copy('image.fluent(\uF406,12)'))
        item(image=image.fluent(\uF407,default_icon_size) tip=['RectangularClipping',tip.info] cmd=command.copy('image.fluent(\uF407,12)'))
        item(image=image.fluent(\uF408,default_icon_size) tip=['FreeFormClipping',tip.info] cmd=command.copy('image.fluent(\uF408,12)'))
        item(image=image.fluent(\uF413,default_icon_size) tip=['CopyTo',tip.info] cmd=command.copy('image.fluent(\uF413,12)'))
        item(image=image.fluent(\uF427,default_icon_size) tip=['IDBadge',tip.info] cmd=command.copy('image.fluent(\uF427,12)'))
        item(image=image.fluent(\uF439,default_icon_size) tip=['DynamicLock',tip.info] cmd=command.copy('image.fluent(\uF439,12)'))
        item(image=image.fluent(\uF45E,default_icon_size) tip=['PenTips',tip.info] cmd=command.copy('image.fluent(\uF45E,12)'))
        item(image=image.fluent(\uF45F,default_icon_size) tip=['PenTipsMirrored',tip.info] cmd=command.copy('image.fluent(\uF45F,12)'))
        item(image=image.fluent(\uF460,default_icon_size) tip=['HWPJoin',tip.info] cmd=command.copy('image.fluent(\uF460,12)'))
        item(image=image.fluent(\uF461,default_icon_size) tip=['HWPInsert',tip.info] cmd=command.copy('image.fluent(\uF461,12)'))
        item(image=image.fluent(\uF462,default_icon_size) tip=['HWPStrikeThrough',tip.info] cmd=command.copy('image.fluent(\uF462,12)'))

        item(image=image.fluent(\uF463,default_icon_size) tip=['HWPScratchOut',tip.info] cmd=command.copy('image.fluent(\uF463,12)') col)
        item(image=image.fluent(\uF464,default_icon_size) tip=['HWPSplit',tip.info] cmd=command.copy('image.fluent(\uF464,12)'))
        item(image=image.fluent(\uF465,default_icon_size) tip=['HWPNewLine',tip.info] cmd=command.copy('image.fluent(\uF465,12)'))
        item(image=image.fluent(\uF466,default_icon_size) tip=['HWPOverwrite',tip.info] cmd=command.copy('image.fluent(\uF466,12)'))
        item(image=image.fluent(\uF473,default_icon_size) tip=['MobWifiWarning1',tip.info] cmd=command.copy('image.fluent(\uF473,12)'))
        item(image=image.fluent(\uF474,default_icon_size) tip=['MobWifiWarning2',tip.info] cmd=command.copy('image.fluent(\uF474,12)'))
        item(image=image.fluent(\uF475,default_icon_size) tip=['MobWifiWarning3',tip.info] cmd=command.copy('image.fluent(\uF475,12)'))
        item(image=image.fluent(\uF476,default_icon_size) tip=['MobWifiWarning4',tip.info] cmd=command.copy('image.fluent(\uF476,12)'))
        item(image=image.fluent(\uF47F,default_icon_size) tip=['MicLocationCombo',tip.info] cmd=command.copy('image.fluent(\uF47F,12)'))
        item(image=image.fluent(\uF49A,default_icon_size) tip=['Globe2',tip.info] cmd=command.copy('image.fluent(\uF49A,12)'))
        item(image=image.fluent(\uF4A5,default_icon_size) tip=['SpecialEffectSize',tip.info] cmd=command.copy('image.fluent(\uF4A5,12)'))
        item(image=image.fluent(\uF4A9,default_icon_size) tip=['GIF',tip.info] cmd=command.copy('image.fluent(\uF4A9,12)'))
        item(image=image.fluent(\uF4AA,default_icon_size) tip=['Sticker2',tip.info] cmd=command.copy('image.fluent(\uF4AA,12)'))
        item(image=image.fluent(\uF4BE,default_icon_size) tip=['SurfaceHubSelected',tip.info] cmd=command.copy('image.fluent(\uF4BE,12)'))
        item(image=image.fluent(\uF4BF,default_icon_size) tip=['HoloLensSelected',tip.info] cmd=command.copy('image.fluent(\uF4BF,12)'))
        item(image=image.fluent(\uF4C0,default_icon_size) tip=['Earbud',tip.info] cmd=command.copy('image.fluent(\uF4C0,12)'))

        item(image=image.fluent(\uF4C3,default_icon_size) tip=['MixVolumes',tip.info] cmd=command.copy('image.fluent(\uF4C3,12)') col)
        item(image=image.fluent(\uF540,default_icon_size) tip=['Safe',tip.info] cmd=command.copy('image.fluent(\uF540,12)'))
        item(image=image.fluent(\uF552,default_icon_size) tip=['LaptopSecure',tip.info] cmd=command.copy('image.fluent(\uF552,12)'))
        item(image=image.fluent(\uF56D,default_icon_size) tip=['PrintDefault',tip.info] cmd=command.copy('image.fluent(\uF56D,12)'))
        item(image=image.fluent(\uF56E,default_icon_size) tip=['PageMirrored',tip.info] cmd=command.copy('image.fluent(\uF56E,12)'))
        item(image=image.fluent(\uF56F,default_icon_size) tip=['LandscapeOrientationMirrored',tip.info] cmd=command.copy('image.fluent(\uF56F,12)'))
        item(image=image.fluent(\uF570,default_icon_size) tip=['ColorOff',tip.info] cmd=command.copy('image.fluent(\uF570,12)'))
        item(image=image.fluent(\uF571,default_icon_size) tip=['PrintAllPages',tip.info] cmd=command.copy('image.fluent(\uF571,12)'))
        item(image=image.fluent(\uF572,default_icon_size) tip=['PrintCustomRange',tip.info] cmd=command.copy('image.fluent(\uF572,12)'))
        item(image=image.fluent(\uF573,default_icon_size) tip=['PageMarginPortraitNarrow',tip.info] cmd=command.copy('image.fluent(\uF573,12)'))
        item(image=image.fluent(\uF574,default_icon_size) tip=['PageMarginPortraitNormal',tip.info] cmd=command.copy('image.fluent(\uF574,12)'))
        item(image=image.fluent(\uF575,default_icon_size) tip=['PageMarginPortraitModerate',tip.info] cmd=command.copy('image.fluent(\uF575,12)'))
        item(image=image.fluent(\uF576,default_icon_size) tip=['PageMarginPortraitWide',tip.info] cmd=command.copy('image.fluent(\uF576,12)'))
        item(image=image.fluent(\uF577,default_icon_size) tip=['PageMarginLandscapeNarrow',tip.info] cmd=command.copy('image.fluent(\uF577,12)'))
        item(image=image.fluent(\uF578,default_icon_size) tip=['PageMarginLandscapeNormal',tip.info] cmd=command.copy('image.fluent(\uF578,12)'))
        item(image=image.fluent(\uF579,default_icon_size) tip=['PageMarginLandscapeModerate',tip.info] cmd=command.copy('image.fluent(\uF579,12)'))

        item(image=image.fluent(\uF57A,default_icon_size) tip=['PageMarginLandscapeWide',tip.info] cmd=command.copy('image.fluent(\uF57A,12)') col)
        item(image=image.fluent(\uF57B,default_icon_size) tip=['CollateLandscape',tip.info] cmd=command.copy('image.fluent(\uF57B,12)'))
        item(image=image.fluent(\uF57C,default_icon_size) tip=['CollatePortrait',tip.info] cmd=command.copy('image.fluent(\uF57C,12)'))
        item(image=image.fluent(\uF57D,default_icon_size) tip=['CollatePortraitSeparated',tip.info] cmd=command.copy('image.fluent(\uF57D,12)'))
        item(image=image.fluent(\uF57E,default_icon_size) tip=['DuplexLandscapeOneSided',tip.info] cmd=command.copy('image.fluent(\uF57E,12)'))
        item(image=image.fluent(\uF57F,default_icon_size) tip=['DuplexLandscapeOneSidedMirrored',tip.info] cmd=command.copy('image.fluent(\uF57F,12)'))
        item(image=image.fluent(\uF580,default_icon_size) tip=['DuplexLandscapeTwoSidedLongEdge',tip.info] cmd=command.copy('image.fluent(\uF580,12)'))
        item(image=image.fluent(\uF581,default_icon_size) tip=['DuplexLandscapeTwoSidedLongEdgeMirrored',tip.info] cmd=command.copy('image.fluent(\uF581,12)'))
        item(image=image.fluent(\uF582,default_icon_size) tip=['DuplexLandscapeTwoSidedShortEdge',tip.info] cmd=command.copy('image.fluent(\uF582,12)'))
        item(image=image.fluent(\uF583,default_icon_size) tip=['DuplexLandscapeTwoSidedShortEdgeMirrored',tip.info] cmd=command.copy('image.fluent(\uF583,12)'))
        item(image=image.fluent(\uF584,default_icon_size) tip=['DuplexPortraitOneSided',tip.info] cmd=command.copy('image.fluent(\uF584,12)'))
        item(image=image.fluent(\uF585,default_icon_size) tip=['DuplexPortraitOneSidedMirrored',tip.info] cmd=command.copy('image.fluent(\uF585,12)'))
        item(image=image.fluent(\uF586,default_icon_size) tip=['DuplexPortraitTwoSidedLongEdge',tip.info] cmd=command.copy('image.fluent(\uF586,12)'))
        item(image=image.fluent(\uF587,default_icon_size) tip=['DuplexPortraitTwoSidedLongEdgeMirrored',tip.info] cmd=command.copy('image.fluent(\uF587,12)'))
        item(image=image.fluent(\uF588,default_icon_size) tip=['DuplexPortraitTwoSidedShortEdge',tip.info] cmd=command.copy('image.fluent(\uF588,12)'))
        item(image=image.fluent(\uF589,default_icon_size) tip=['DuplexPortraitTwoSidedShortEdgeMirrored',tip.info] cmd=command.copy('image.fluent(\uF589,12)'))

        item(image=image.fluent(\uF58A,default_icon_size) tip=['PPSOneLandscape',tip.info] cmd=command.copy('image.fluent(\uF58A,12)') col)
        item(image=image.fluent(\uF58B,default_icon_size) tip=['PPSTwoLandscape',tip.info] cmd=command.copy('image.fluent(\uF58B,12)'))
        item(image=image.fluent(\uF58C,default_icon_size) tip=['PPSTwoPortrait',tip.info] cmd=command.copy('image.fluent(\uF58C,12)'))
        item(image=image.fluent(\uF58D,default_icon_size) tip=['PPSFourLandscape',tip.info] cmd=command.copy('image.fluent(\uF58D,12)'))
        item(image=image.fluent(\uF58E,default_icon_size) tip=['PPSFourPortrait',tip.info] cmd=command.copy('image.fluent(\uF58E,12)'))
        item(image=image.fluent(\uF58F,default_icon_size) tip=['HolePunchOff',tip.info] cmd=command.copy('image.fluent(\uF58F,12)'))
        item(image=image.fluent(\uF590,default_icon_size) tip=['HolePunchPortraitLeft',tip.info] cmd=command.copy('image.fluent(\uF590,12)'))
        item(image=image.fluent(\uF591,default_icon_size) tip=['HolePunchPortraitRight',tip.info] cmd=command.copy('image.fluent(\uF591,12)'))
        item(image=image.fluent(\uF592,default_icon_size) tip=['HolePunchPortraitTop',tip.info] cmd=command.copy('image.fluent(\uF592,12)'))
        item(image=image.fluent(\uF593,default_icon_size) tip=['HolePunchPortraitBottom',tip.info] cmd=command.copy('image.fluent(\uF593,12)'))
        item(image=image.fluent(\uF594,default_icon_size) tip=['HolePunchLandscapeLeft',tip.info] cmd=command.copy('image.fluent(\uF594,12)'))
        item(image=image.fluent(\uF595,default_icon_size) tip=['HolePunchLandscapeRight',tip.info] cmd=command.copy('image.fluent(\uF595,12)'))
        item(image=image.fluent(\uF596,default_icon_size) tip=['HolePunchLandscapeTop',tip.info] cmd=command.copy('image.fluent(\uF596,12)'))
        item(image=image.fluent(\uF597,default_icon_size) tip=['HolePunchLandscapeBottom',tip.info] cmd=command.copy('image.fluent(\uF597,12)'))
        item(image=image.fluent(\uF598,default_icon_size) tip=['StaplingOff',tip.info] cmd=command.copy('image.fluent(\uF598,12)'))
        item(image=image.fluent(\uF599,default_icon_size) tip=['StaplingPortraitTopLeft',tip.info] cmd=command.copy('image.fluent(\uF599,12)'))

        item(image=image.fluent(\uF59A,default_icon_size) tip=['StaplingPortraitTopRight',tip.info] cmd=command.copy('image.fluent(\uF59A,12)') col)
        item(image=image.fluent(\uF59B,default_icon_size) tip=['StaplingPortraitBottomRight',tip.info] cmd=command.copy('image.fluent(\uF59B,12)'))
        item(image=image.fluent(\uF59C,default_icon_size) tip=['StaplingPortraitTwoLeft',tip.info] cmd=command.copy('image.fluent(\uF59C,12)'))
        item(image=image.fluent(\uF59D,default_icon_size) tip=['StaplingPortraitTwoRight',tip.info] cmd=command.copy('image.fluent(\uF59D,12)'))
        item(image=image.fluent(\uF59E,default_icon_size) tip=['StaplingPortraitTwoTop',tip.info] cmd=command.copy('image.fluent(\uF59E,12)'))
        item(image=image.fluent(\uF59F,default_icon_size) tip=['StaplingPortraitTwoBottom',tip.info] cmd=command.copy('image.fluent(\uF59F,12)'))
        item(image=image.fluent(\uF5A0,default_icon_size) tip=['StaplingPortraitBookBinding',tip.info] cmd=command.copy('image.fluent(\uF5A0,12)'))
        item(image=image.fluent(\uF5A1,default_icon_size) tip=['StaplingLandscapeTopLeft',tip.info] cmd=command.copy('image.fluent(\uF5A1,12)'))
        item(image=image.fluent(\uF5A2,default_icon_size) tip=['StaplingLandscapeTopRight',tip.info] cmd=command.copy('image.fluent(\uF5A2,12)'))
        item(image=image.fluent(\uF5A3,default_icon_size) tip=['StaplingLandscapeBottomLeft',tip.info] cmd=command.copy('image.fluent(\uF5A3,12)'))
        item(image=image.fluent(\uF5A4,default_icon_size) tip=['StaplingLandscapeBottomRight',tip.info] cmd=command.copy('image.fluent(\uF5A4,12)'))
        item(image=image.fluent(\uF5A5,default_icon_size) tip=['StaplingLandscapeTwoLeft',tip.info] cmd=command.copy('image.fluent(\uF5A5,12)'))
        item(image=image.fluent(\uF5A6,default_icon_size) tip=['StaplingLandscapeTwoRight',tip.info] cmd=command.copy('image.fluent(\uF5A6,12)'))
        item(image=image.fluent(\uF5A7,default_icon_size) tip=['StaplingLandscapeTwoTop',tip.info] cmd=command.copy('image.fluent(\uF5A7,12)'))
        item(image=image.fluent(\uF5A8,default_icon_size) tip=['StaplingLandscapeTwoBottom',tip.info] cmd=command.copy('image.fluent(\uF5A8,12)'))
        item(image=image.fluent(\uF5A9,default_icon_size) tip=['StaplingLandscapeBookBinding',tip.info] cmd=command.copy('image.fluent(\uF5A9,12)'))
    }

    separator

    menu(title='MDL2 #1')
    {
        item(image=image.mdl(\uE700,default_icon_size) tip=['GlobalNavigationButton',tip.info] cmd=command.copy('image.mdl(\uE700,12)'))
        item(image=image.mdl(\uE701,default_icon_size) tip=['Wifi',tip.info] cmd=command.copy('image.mdl(\uE701,12)'))
        item(image=image.mdl(\uE702,default_icon_size) tip=['Bluetooth',tip.info] cmd=command.copy('image.mdl(\uE702,12)'))
        item(image=image.mdl(\uE703,default_icon_size) tip=['Connect',tip.info] cmd=command.copy('image.mdl(\uE703,12)'))
        item(image=image.mdl(\uE704,default_icon_size) tip=['InternetSharing',tip.info] cmd=command.copy('image.mdl(\uE704,12)'))
        item(image=image.mdl(\uE705,default_icon_size) tip=['VPN',tip.info] cmd=command.copy('image.mdl(\uE705,12)'))
        item(image=image.mdl(\uE706,default_icon_size) tip=['Brightness',tip.info] cmd=command.copy('image.mdl(\uE706,12)'))
        item(image=image.mdl(\uE707,default_icon_size) tip=['MapPin',tip.info] cmd=command.copy('image.mdl(\uE707,12)'))
        item(image=image.mdl(\uE708,default_icon_size) tip=['QuietHours',tip.info] cmd=command.copy('image.mdl(\uE708,12)'))
        item(image=image.mdl(\uE709,default_icon_size) tip=['Airplane',tip.info] cmd=command.copy('image.mdl(\uE709,12)'))
        item(image=image.mdl(\uE70A,default_icon_size) tip=['Tablet',tip.info] cmd=command.copy('image.mdl(\uE70A,12)'))
        item(image=image.mdl(\uE70B,default_icon_size) tip=['QuickNote',tip.info] cmd=command.copy('image.mdl(\uE70B,12)'))
        item(image=image.mdl(\uE70C,default_icon_size) tip=['RememberedDevice',tip.info] cmd=command.copy('image.mdl(\uE70C,12)'))
        item(image=image.mdl(\uE70D,default_icon_size) tip=['ChevronDown',tip.info] cmd=command.copy('image.mdl(\uE70D,12)'))
        item(image=image.mdl(\uE70E,default_icon_size) tip=['ChevronUp',tip.info] cmd=command.copy('image.mdl(\uE70E,12)'))
        item(image=image.mdl(\uE70F,default_icon_size) tip=['Edit',tip.info] cmd=command.copy('image.mdl(\uE70F,12)'))

        item(image=image.mdl(\uE710,default_icon_size) tip=['Add',tip.info] cmd=command.copy('image.mdl(\uE710,12)') col)
        item(image=image.mdl(\uE711,default_icon_size) tip=['Cancel',tip.info] cmd=command.copy('image.mdl(\uE711,12)'))
        item(image=image.mdl(\uE712,default_icon_size) tip=['More',tip.info] cmd=command.copy('image.mdl(\uE712,12)'))
        item(image=image.mdl(\uE713,default_icon_size) tip=['Setting',tip.info] cmd=command.copy('image.mdl(\uE713,12)'))
        item(image=image.mdl(\uE714,default_icon_size) tip=['Video',tip.info] cmd=command.copy('image.mdl(\uE714,12)'))
        item(image=image.mdl(\uE715,default_icon_size) tip=['Mail',tip.info] cmd=command.copy('image.mdl(\uE715,12)'))
        item(image=image.mdl(\uE716,default_icon_size) tip=['People',tip.info] cmd=command.copy('image.mdl(\uE716,12)'))
        item(image=image.mdl(\uE717,default_icon_size) tip=['Phone',tip.info] cmd=command.copy('image.mdl(\uE717,12)'))
        item(image=image.mdl(\uE718,default_icon_size) tip=['Pin',tip.info] cmd=command.copy('image.mdl(\uE718,12)'))
        item(image=image.mdl(\uE719,default_icon_size) tip=['Shop',tip.info] cmd=command.copy('image.mdl(\uE719,12)'))
        item(image=image.mdl(\uE71A,default_icon_size) tip=['Stop',tip.info] cmd=command.copy('image.mdl(\uE71A,12)'))
        item(image=image.mdl(\uE71B,default_icon_size) tip=['Link',tip.info] cmd=command.copy('image.mdl(\uE71B,12)'))
        item(image=image.mdl(\uE71C,default_icon_size) tip=['Filter',tip.info] cmd=command.copy('image.mdl(\uE71C,12)'))
        item(image=image.mdl(\uE71D,default_icon_size) tip=['AllApps',tip.info] cmd=command.copy('image.mdl(\uE71D,12)'))
        item(image=image.mdl(\uE71E,default_icon_size) tip=['Zoom',tip.info] cmd=command.copy('image.mdl(\uE71E,12)'))
        item(image=image.mdl(\uE71F,default_icon_size) tip=['ZoomOut',tip.info] cmd=command.copy('image.mdl(\uE71F,12)'))

        item(image=image.mdl(\uE720,default_icon_size) tip=['Microphone',tip.info] cmd=command.copy('image.mdl(\uE720,12)') col)
        item(image=image.mdl(\uE721,default_icon_size) tip=['Search',tip.info] cmd=command.copy('image.mdl(\uE721,12)'))
        item(image=image.mdl(\uE722,default_icon_size) tip=['Camera',tip.info] cmd=command.copy('image.mdl(\uE722,12)'))
        item(image=image.mdl(\uE723,default_icon_size) tip=['Attach',tip.info] cmd=command.copy('image.mdl(\uE723,12)'))
        item(image=image.mdl(\uE724,default_icon_size) tip=['Send',tip.info] cmd=command.copy('image.mdl(\uE724,12)'))
        item(image=image.mdl(\uE725,default_icon_size) tip=['SendFill',tip.info] cmd=command.copy('image.mdl(\uE725,12)'))
        item(image=image.mdl(\uE726,default_icon_size) tip=['WalkSolid',tip.info] cmd=command.copy('image.mdl(\uE726,12)'))
        item(image=image.mdl(\uE727,default_icon_size) tip=['InPrivate',tip.info] cmd=command.copy('image.mdl(\uE727,12)'))
        item(image=image.mdl(\uE728,default_icon_size) tip=['FavoriteList',tip.info] cmd=command.copy('image.mdl(\uE728,12)'))
        item(image=image.mdl(\uE729,default_icon_size) tip=['PageSolid',tip.info] cmd=command.copy('image.mdl(\uE729,12)'))
        item(image=image.mdl(\uE72A,default_icon_size) tip=['Forward',tip.info] cmd=command.copy('image.mdl(\uE72A,12)'))
        item(image=image.mdl(\uE72B,default_icon_size) tip=['Back',tip.info] cmd=command.copy('image.mdl(\uE72B,12)'))
        item(image=image.mdl(\uE72C,default_icon_size) tip=['Refresh',tip.info] cmd=command.copy('image.mdl(\uE72C,12)'))
        item(image=image.mdl(\uE72D,default_icon_size) tip=['Share',tip.info] cmd=command.copy('image.mdl(\uE72D,12)'))
        item(image=image.mdl(\uE72E,default_icon_size) tip=['Lock',tip.info] cmd=command.copy('image.mdl(\uE72E,12)'))
        item(image=image.mdl(\uE730,default_icon_size) tip=['ReportHacked',tip.info] cmd=command.copy('image.mdl(\uE730,12)'))

        item(image=image.mdl(\uE731,default_icon_size) tip=['EMI',tip.info] cmd=command.copy('image.mdl(\uE731,12)') col)
        item(image=image.mdl(\uE734,default_icon_size) tip=['FavoriteStar',tip.info] cmd=command.copy('image.mdl(\uE734,12)'))
        item(image=image.mdl(\uE735,default_icon_size) tip=['FavoriteStarFill',tip.info] cmd=command.copy('image.mdl(\uE735,12)'))
        item(image=image.mdl(\uE736,default_icon_size) tip=['ReadingMode',tip.info] cmd=command.copy('image.mdl(\uE736,12)'))
        item(image=image.mdl(\uE737,default_icon_size) tip=['Favicon',tip.info] cmd=command.copy('image.mdl(\uE737,12)'))
        item(image=image.mdl(\uE738,default_icon_size) tip=['Remove',tip.info] cmd=command.copy('image.mdl(\uE738,12)'))
        item(image=image.mdl(\uE739,default_icon_size) tip=['Checkbox',tip.info] cmd=command.copy('image.mdl(\uE739,12)'))
        item(image=image.mdl(\uE73A,default_icon_size) tip=['CheckboxComposite',tip.info] cmd=command.copy('image.mdl(\uE73A,12)'))
        item(image=image.mdl(\uE73B,default_icon_size) tip=['CheckboxFill',tip.info] cmd=command.copy('image.mdl(\uE73B,12)'))
        item(image=image.mdl(\uE73C,default_icon_size) tip=['CheckboxIndeterminate',tip.info] cmd=command.copy('image.mdl(\uE73C,12)'))
        item(image=image.mdl(\uE73D,default_icon_size) tip=['CheckboxCompositeReversed',tip.info] cmd=command.copy('image.mdl(\uE73D,12)'))
        item(image=image.mdl(\uE73E,default_icon_size) tip=['CheckMark',tip.info] cmd=command.copy('image.mdl(\uE73E,12)'))
        item(image=image.mdl(\uE73F,default_icon_size) tip=['BackToWindow',tip.info] cmd=command.copy('image.mdl(\uE73F,12)'))
        item(image=image.mdl(\uE740,default_icon_size) tip=['FullScreen',tip.info] cmd=command.copy('image.mdl(\uE740,12)'))
        item(image=image.mdl(\uE741,default_icon_size) tip=['ResizeTouchLarger',tip.info] cmd=command.copy('image.mdl(\uE741,12)'))
        item(image=image.mdl(\uE742,default_icon_size) tip=['ResizeTouchSmaller',tip.info] cmd=command.copy('image.mdl(\uE742,12)'))

        item(image=image.mdl(\uE743,default_icon_size) tip=['ResizeMouseSmall',tip.info] cmd=command.copy('image.mdl(\uE743,12)') col)
        item(image=image.mdl(\uE744,default_icon_size) tip=['ResizeMouseMedium',tip.info] cmd=command.copy('image.mdl(\uE744,12)'))
        item(image=image.mdl(\uE745,default_icon_size) tip=['ResizeMouseWide',tip.info] cmd=command.copy('image.mdl(\uE745,12)'))
        item(image=image.mdl(\uE746,default_icon_size) tip=['ResizeMouseTall',tip.info] cmd=command.copy('image.mdl(\uE746,12)'))
        item(image=image.mdl(\uE747,default_icon_size) tip=['ResizeMouseLarge',tip.info] cmd=command.copy('image.mdl(\uE747,12)'))
        item(image=image.mdl(\uE748,default_icon_size) tip=['SwitchUser',tip.info] cmd=command.copy('image.mdl(\uE748,12)'))
        item(image=image.mdl(\uE749,default_icon_size) tip=['Print',tip.info] cmd=command.copy('image.mdl(\uE749,12)'))
        item(image=image.mdl(\uE74A,default_icon_size) tip=['Up',tip.info] cmd=command.copy('image.mdl(\uE74A,12)'))
        item(image=image.mdl(\uE74B,default_icon_size) tip=['Down',tip.info] cmd=command.copy('image.mdl(\uE74B,12)'))
        item(image=image.mdl(\uE74C,default_icon_size) tip=['OEM',tip.info] cmd=command.copy('image.mdl(\uE74C,12)'))
        item(image=image.mdl(\uE74D,default_icon_size) tip=['Delete',tip.info] cmd=command.copy('image.mdl(\uE74D,12)'))
        item(image=image.mdl(\uE74E,default_icon_size) tip=['Save',tip.info] cmd=command.copy('image.mdl(\uE74E,12)'))
        item(image=image.mdl(\uE74F,default_icon_size) tip=['Mute',tip.info] cmd=command.copy('image.mdl(\uE74F,12)'))
        item(image=image.mdl(\uE750,default_icon_size) tip=['BackSpaceQWERTY',tip.info] cmd=command.copy('image.mdl(\uE750,12)'))
        item(image=image.mdl(\uE751,default_icon_size) tip=['ReturnKey',tip.info] cmd=command.copy('image.mdl(\uE751,12)'))
        item(image=image.mdl(\uE752,default_icon_size) tip=['UpArrowShiftKey',tip.info] cmd=command.copy('image.mdl(\uE752,12)'))

        item(image=image.mdl(\uE753,default_icon_size) tip=['Cloud',tip.info] cmd=command.copy('image.mdl(\uE753,12)') col)
        item(image=image.mdl(\uE754,default_icon_size) tip=['Flashlight',tip.info] cmd=command.copy('image.mdl(\uE754,12)'))
        item(image=image.mdl(\uE755,default_icon_size) tip=['RotationLock',tip.info] cmd=command.copy('image.mdl(\uE755,12)'))
        item(image=image.mdl(\uE756,default_icon_size) tip=['CommandPrompt',tip.info] cmd=command.copy('image.mdl(\uE756,12)'))
        item(image=image.mdl(\uE759,default_icon_size) tip=['SIPMove',tip.info] cmd=command.copy('image.mdl(\uE759,12)'))
        item(image=image.mdl(\uE75A,default_icon_size) tip=['SIPUndock',tip.info] cmd=command.copy('image.mdl(\uE75A,12)'))
        item(image=image.mdl(\uE75B,default_icon_size) tip=['SIPRedock',tip.info] cmd=command.copy('image.mdl(\uE75B,12)'))
        item(image=image.mdl(\uE75C,default_icon_size) tip=['EraseTool',tip.info] cmd=command.copy('image.mdl(\uE75C,12)'))
        item(image=image.mdl(\uE75D,default_icon_size) tip=['UnderscoreSpace',tip.info] cmd=command.copy('image.mdl(\uE75D,12)'))
        item(image=image.mdl(\uE75E,default_icon_size) tip=['GripperTool',tip.info] cmd=command.copy('image.mdl(\uE75E,12)'))
        item(image=image.mdl(\uE75F,default_icon_size) tip=['Dialpad',tip.info] cmd=command.copy('image.mdl(\uE75F,12)'))
        item(image=image.mdl(\uE760,default_icon_size) tip=['PageLeft',tip.info] cmd=command.copy('image.mdl(\uE760,12)'))
        item(image=image.mdl(\uE761,default_icon_size) tip=['PageRight',tip.info] cmd=command.copy('image.mdl(\uE761,12)'))
        item(image=image.mdl(\uE762,default_icon_size) tip=['MultiSelect',tip.info] cmd=command.copy('image.mdl(\uE762,12)'))
        item(image=image.mdl(\uE763,default_icon_size) tip=['KeyboardLeftHanded',tip.info] cmd=command.copy('image.mdl(\uE763,12)'))
        item(image=image.mdl(\uE764,default_icon_size) tip=['KeyboardRightHanded',tip.info] cmd=command.copy('image.mdl(\uE764,12)'))

        item(image=image.mdl(\uE765,default_icon_size) tip=['KeyboardClassic',tip.info] cmd=command.copy('image.mdl(\uE765,12)') col)
        item(image=image.mdl(\uE766,default_icon_size) tip=['KeyboardSplit',tip.info] cmd=command.copy('image.mdl(\uE766,12)'))
        item(image=image.mdl(\uE767,default_icon_size) tip=['Volume',tip.info] cmd=command.copy('image.mdl(\uE767,12)'))
        item(image=image.mdl(\uE768,default_icon_size) tip=['Play',tip.info] cmd=command.copy('image.mdl(\uE768,12)'))
        item(image=image.mdl(\uE769,default_icon_size) tip=['Pause',tip.info] cmd=command.copy('image.mdl(\uE769,12)'))
        item(image=image.mdl(\uE76B,default_icon_size) tip=['ChevronLeft',tip.info] cmd=command.copy('image.mdl(\uE76B,12)'))
        item(image=image.mdl(\uE76C,default_icon_size) tip=['ChevronRight',tip.info] cmd=command.copy('image.mdl(\uE76C,12)'))
        item(image=image.mdl(\uE76D,default_icon_size) tip=['InkingTool',tip.info] cmd=command.copy('image.mdl(\uE76D,12)'))
        item(image=image.mdl(\uE76E,default_icon_size) tip=['Emoji2',tip.info] cmd=command.copy('image.mdl(\uE76E,12)'))
        item(image=image.mdl(\uE76F,default_icon_size) tip=['GripperBarHorizontal',tip.info] cmd=command.copy('image.mdl(\uE76F,12)'))
        item(image=image.mdl(\uE770,default_icon_size) tip=['System',tip.info] cmd=command.copy('image.mdl(\uE770,12)'))
        item(image=image.mdl(\uE771,default_icon_size) tip=['Personalize',tip.info] cmd=command.copy('image.mdl(\uE771,12)'))
        item(image=image.mdl(\uE772,default_icon_size) tip=['Devices',tip.info] cmd=command.copy('image.mdl(\uE772,12)'))
        item(image=image.mdl(\uE773,default_icon_size) tip=['SearchAndApps',tip.info] cmd=command.copy('image.mdl(\uE773,12)'))
        item(image=image.mdl(\uE774,default_icon_size) tip=['Globe',tip.info] cmd=command.copy('image.mdl(\uE774,12)'))
        item(image=image.mdl(\uE775,default_icon_size) tip=['TimeLanguage',tip.info] cmd=command.copy('image.mdl(\uE775,12)'))

        item(image=image.mdl(\uE776,default_icon_size) tip=['EaseOfAccess',tip.info] cmd=command.copy('image.mdl(\uE776,12)') col)
        item(image=image.mdl(\uE777,default_icon_size) tip=['UpdateRestore',tip.info] cmd=command.copy('image.mdl(\uE777,12)'))
        item(image=image.mdl(\uE778,default_icon_size) tip=['HangUp',tip.info] cmd=command.copy('image.mdl(\uE778,12)'))
        item(image=image.mdl(\uE779,default_icon_size) tip=['ContactInfo',tip.info] cmd=command.copy('image.mdl(\uE779,12)'))
        item(image=image.mdl(\uE77A,default_icon_size) tip=['Unpin',tip.info] cmd=command.copy('image.mdl(\uE77A,12)'))
        item(image=image.mdl(\uE77B,default_icon_size) tip=['Contact',tip.info] cmd=command.copy('image.mdl(\uE77B,12)'))
        item(image=image.mdl(\uE77C,default_icon_size) tip=['Memo',tip.info] cmd=command.copy('image.mdl(\uE77C,12)'))
        item(image=image.mdl(\uE77E,default_icon_size) tip=['IncomingCall',tip.info] cmd=command.copy('image.mdl(\uE77E,12)'))
        item(image=image.mdl(\uE77F,default_icon_size) tip=['Paste',tip.info] cmd=command.copy('image.mdl(\uE77F,12)'))
        item(image=image.mdl(\uE780,default_icon_size) tip=['PhoneBook',tip.info] cmd=command.copy('image.mdl(\uE780,12)'))
        item(image=image.mdl(\uE781,default_icon_size) tip=['LEDLight',tip.info] cmd=command.copy('image.mdl(\uE781,12)'))
        item(image=image.mdl(\uE783,default_icon_size) tip=['Error',tip.info] cmd=command.copy('image.mdl(\uE783,12)'))
        item(image=image.mdl(\uE784,default_icon_size) tip=['GripperBarVertical',tip.info] cmd=command.copy('image.mdl(\uE784,12)'))
        item(image=image.mdl(\uE785,default_icon_size) tip=['Unlock',tip.info] cmd=command.copy('image.mdl(\uE785,12)'))
        item(image=image.mdl(\uE786,default_icon_size) tip=['Slideshow',tip.info] cmd=command.copy('image.mdl(\uE786,12)'))
        item(image=image.mdl(\uE787,default_icon_size) tip=['Calendar',tip.info] cmd=command.copy('image.mdl(\uE787,12)'))

        item(image=image.mdl(\uE788,default_icon_size) tip=['GripperResize',tip.info] cmd=command.copy('image.mdl(\uE788,12)') col)
        item(image=image.mdl(\uE789,default_icon_size) tip=['Megaphone',tip.info] cmd=command.copy('image.mdl(\uE789,12)'))
        item(image=image.mdl(\uE78A,default_icon_size) tip=['Trim',tip.info] cmd=command.copy('image.mdl(\uE78A,12)'))
        item(image=image.mdl(\uE78B,default_icon_size) tip=['NewWindow',tip.info] cmd=command.copy('image.mdl(\uE78B,12)'))
        item(image=image.mdl(\uE78C,default_icon_size) tip=['SaveLocal',tip.info] cmd=command.copy('image.mdl(\uE78C,12)'))
        item(image=image.mdl(\uE790,default_icon_size) tip=['Color',tip.info] cmd=command.copy('image.mdl(\uE790,12)'))
        item(image=image.mdl(\uE791,default_icon_size) tip=['DataSense',tip.info] cmd=command.copy('image.mdl(\uE791,12)'))
        item(image=image.mdl(\uE792,default_icon_size) tip=['SaveAs',tip.info] cmd=command.copy('image.mdl(\uE792,12)'))
        item(image=image.mdl(\uE793,default_icon_size) tip=['Light',tip.info] cmd=command.copy('image.mdl(\uE793,12)'))
        item(image=image.mdl(\uE799,default_icon_size) tip=['AspectRatio',tip.info] cmd=command.copy('image.mdl(\uE799,12)'))
        item(image=image.mdl(\uE7A5,default_icon_size) tip=['DataSenseBar',tip.info] cmd=command.copy('image.mdl(\uE7A5,12)'))
        item(image=image.mdl(\uE7A6,default_icon_size) tip=['Redo',tip.info] cmd=command.copy('image.mdl(\uE7A6,12)'))
        item(image=image.mdl(\uE7A7,default_icon_size) tip=['Undo',tip.info] cmd=command.copy('image.mdl(\uE7A7,12)'))
        item(image=image.mdl(\uE7A8,default_icon_size) tip=['Crop',tip.info] cmd=command.copy('image.mdl(\uE7A8,12)'))
        item(image=image.mdl(\uE7AC,default_icon_size) tip=['OpenWith',tip.info] cmd=command.copy('image.mdl(\uE7AC,12)'))
        item(image=image.mdl(\uE7AD,default_icon_size) tip=['Rotate',tip.info] cmd=command.copy('image.mdl(\uE7AD,12)'))

        item(image=image.mdl(\uE7B3,default_icon_size) tip=['RedEye',tip.info] cmd=command.copy('image.mdl(\uE7B3,12)') col)
        item(image=image.mdl(\uE7B5,default_icon_size) tip=['SetlockScreen',tip.info] cmd=command.copy('image.mdl(\uE7B5,12)'))
        item(image=image.mdl(\uE7B7,default_icon_size) tip=['MapPin2',tip.info] cmd=command.copy('image.mdl(\uE7B7,12)'))
        item(image=image.mdl(\uE7B8,default_icon_size) tip=['Package',tip.info] cmd=command.copy('image.mdl(\uE7B8,12)'))
        item(image=image.mdl(\uE7BA,default_icon_size) tip=['Warning',tip.info] cmd=command.copy('image.mdl(\uE7BA,12)'))
        item(image=image.mdl(\uE7BC,default_icon_size) tip=['ReadingList',tip.info] cmd=command.copy('image.mdl(\uE7BC,12)'))
        item(image=image.mdl(\uE7BE,default_icon_size) tip=['Education',tip.info] cmd=command.copy('image.mdl(\uE7BE,12)'))
        item(image=image.mdl(\uE7BF,default_icon_size) tip=['ShoppingCart',tip.info] cmd=command.copy('image.mdl(\uE7BF,12)'))
        item(image=image.mdl(\uE7C0,default_icon_size) tip=['Train',tip.info] cmd=command.copy('image.mdl(\uE7C0,12)'))
        item(image=image.mdl(\uE7C1,default_icon_size) tip=['Flag',tip.info] cmd=command.copy('image.mdl(\uE7C1,12)'))
        item(image=image.mdl(\uE7C3,default_icon_size) tip=['Page',tip.info] cmd=command.copy('image.mdl(\uE7C3,12)'))
        item(image=image.mdl(\uE7C4,default_icon_size) tip=['TaskView',tip.info] cmd=command.copy('image.mdl(\uE7C4,12)'))
        item(image=image.mdl(\uE7C5,default_icon_size) tip=['BrowsePhotos',tip.info] cmd=command.copy('image.mdl(\uE7C5,12)'))
        item(image=image.mdl(\uE7C6,default_icon_size) tip=['HalfStarLeft',tip.info] cmd=command.copy('image.mdl(\uE7C6,12)'))
        item(image=image.mdl(\uE7C7,default_icon_size) tip=['HalfStarRight',tip.info] cmd=command.copy('image.mdl(\uE7C7,12)'))
        item(image=image.mdl(\uE7C8,default_icon_size) tip=['Record',tip.info] cmd=command.copy('image.mdl(\uE7C8,12)'))

        item(image=image.mdl(\uE7C9,default_icon_size) tip=['TouchPointer',tip.info] cmd=command.copy('image.mdl(\uE7C9,12)') col)
        item(image=image.mdl(\uE7DE,default_icon_size) tip=['LangJPN',tip.info] cmd=command.copy('image.mdl(\uE7DE,12)'))
        item(image=image.mdl(\uE7E3,default_icon_size) tip=['Ferry',tip.info] cmd=command.copy('image.mdl(\uE7E3,12)'))
        item(image=image.mdl(\uE7E6,default_icon_size) tip=['Highlight',tip.info] cmd=command.copy('image.mdl(\uE7E6,12)'))
        item(image=image.mdl(\uE7E7,default_icon_size) tip=['ActionCenterNotification',tip.info] cmd=command.copy('image.mdl(\uE7E7,12)'))
        item(image=image.mdl(\uE7E8,default_icon_size) tip=['PowerButton',tip.info] cmd=command.copy('image.mdl(\uE7E8,12)'))
        item(image=image.mdl(\uE7EA,default_icon_size) tip=['ResizeTouchNarrower',tip.info] cmd=command.copy('image.mdl(\uE7EA,12)'))
        item(image=image.mdl(\uE7EB,default_icon_size) tip=['ResizeTouchShorter',tip.info] cmd=command.copy('image.mdl(\uE7EB,12)'))
        item(image=image.mdl(\uE7EC,default_icon_size) tip=['DrivingMode',tip.info] cmd=command.copy('image.mdl(\uE7EC,12)'))
        item(image=image.mdl(\uE7ED,default_icon_size) tip=['RingerSilent',tip.info] cmd=command.copy('image.mdl(\uE7ED,12)'))
        item(image=image.mdl(\uE7EE,default_icon_size) tip=['OtherUser',tip.info] cmd=command.copy('image.mdl(\uE7EE,12)'))
        item(image=image.mdl(\uE7EF,default_icon_size) tip=['Admin',tip.info] cmd=command.copy('image.mdl(\uE7EF,12)'))
        item(image=image.mdl(\uE7F0,default_icon_size) tip=['CC',tip.info] cmd=command.copy('image.mdl(\uE7F0,12)'))
        item(image=image.mdl(\uE7F1,default_icon_size) tip=['SDCard',tip.info] cmd=command.copy('image.mdl(\uE7F1,12)'))
        item(image=image.mdl(\uE7F2,default_icon_size) tip=['CallForwarding',tip.info] cmd=command.copy('image.mdl(\uE7F2,12)'))
        item(image=image.mdl(\uE7F3,default_icon_size) tip=['SettingsDisplaySound',tip.info] cmd=command.copy('image.mdl(\uE7F3,12)'))

        item(image=image.mdl(\uE7F4,default_icon_size) tip=['TVMonitor',tip.info] cmd=command.copy('image.mdl(\uE7F4,12)') col)
        item(image=image.mdl(\uE7F5,default_icon_size) tip=['Speakers',tip.info] cmd=command.copy('image.mdl(\uE7F5,12)'))
        item(image=image.mdl(\uE7F6,default_icon_size) tip=['Headphone',tip.info] cmd=command.copy('image.mdl(\uE7F6,12)'))
        item(image=image.mdl(\uE7F7,default_icon_size) tip=['DeviceLaptopPic',tip.info] cmd=command.copy('image.mdl(\uE7F7,12)'))
        item(image=image.mdl(\uE7F8,default_icon_size) tip=['DeviceLaptopNoPic',tip.info] cmd=command.copy('image.mdl(\uE7F8,12)'))
        item(image=image.mdl(\uE7F9,default_icon_size) tip=['DeviceMonitorRightPic',tip.info] cmd=command.copy('image.mdl(\uE7F9,12)'))
        item(image=image.mdl(\uE7FA,default_icon_size) tip=['DeviceMonitorLeftPic',tip.info] cmd=command.copy('image.mdl(\uE7FA,12)'))
        item(image=image.mdl(\uE7FB,default_icon_size) tip=['DeviceMonitorNoPic',tip.info] cmd=command.copy('image.mdl(\uE7FB,12)'))
        item(image=image.mdl(\uE7FC,default_icon_size) tip=['Game',tip.info] cmd=command.copy('image.mdl(\uE7FC,12)'))
        item(image=image.mdl(\uE7FD,default_icon_size) tip=['HorizontalTabKey',tip.info] cmd=command.copy('image.mdl(\uE7FD,12)'))
        item(image=image.mdl(\uE802,default_icon_size) tip=['StreetsideSplitMinimize',tip.info] cmd=command.copy('image.mdl(\uE802,12)'))
        item(image=image.mdl(\uE803,default_icon_size) tip=['StreetsideSplitExpand',tip.info] cmd=command.copy('image.mdl(\uE803,12)'))
        item(image=image.mdl(\uE804,default_icon_size) tip=['Car',tip.info] cmd=command.copy('image.mdl(\uE804,12)'))
        item(image=image.mdl(\uE805,default_icon_size) tip=['Walk',tip.info] cmd=command.copy('image.mdl(\uE805,12)'))
        item(image=image.mdl(\uE806,default_icon_size) tip=['Bus',tip.info] cmd=command.copy('image.mdl(\uE806,12)'))
        item(image=image.mdl(\uE809,default_icon_size) tip=['TiltUp',tip.info] cmd=command.copy('image.mdl(\uE809,12)'))

        item(image=image.mdl(\uE80A,default_icon_size) tip=['TiltDown',tip.info] cmd=command.copy('image.mdl(\uE80A,12)') col)
        item(image=image.mdl(\uE80B,default_icon_size) tip=['CallControl',tip.info] cmd=command.copy('image.mdl(\uE80B,12)'))
        item(image=image.mdl(\uE80C,default_icon_size) tip=['RotateMapRight',tip.info] cmd=command.copy('image.mdl(\uE80C,12)'))
        item(image=image.mdl(\uE80D,default_icon_size) tip=['RotateMapLeft',tip.info] cmd=command.copy('image.mdl(\uE80D,12)'))
        item(image=image.mdl(\uE80F,default_icon_size) tip=['Home',tip.info] cmd=command.copy('image.mdl(\uE80F,12)'))
        item(image=image.mdl(\uE811,default_icon_size) tip=['ParkingLocation',tip.info] cmd=command.copy('image.mdl(\uE811,12)'))
        item(image=image.mdl(\uE812,default_icon_size) tip=['MapCompassTop',tip.info] cmd=command.copy('image.mdl(\uE812,12)'))
        item(image=image.mdl(\uE813,default_icon_size) tip=['MapCompassBottom',tip.info] cmd=command.copy('image.mdl(\uE813,12)'))
        item(image=image.mdl(\uE814,default_icon_size) tip=['IncidentTriangle',tip.info] cmd=command.copy('image.mdl(\uE814,12)'))
        item(image=image.mdl(\uE815,default_icon_size) tip=['Touch',tip.info] cmd=command.copy('image.mdl(\uE815,12)'))
        item(image=image.mdl(\uE816,default_icon_size) tip=['MapDirections',tip.info] cmd=command.copy('image.mdl(\uE816,12)'))
        item(image=image.mdl(\uE819,default_icon_size) tip=['StartPoint',tip.info] cmd=command.copy('image.mdl(\uE819,12)'))
        item(image=image.mdl(\uE81A,default_icon_size) tip=['StopPoint',tip.info] cmd=command.copy('image.mdl(\uE81A,12)'))
        item(image=image.mdl(\uE81B,default_icon_size) tip=['EndPoint',tip.info] cmd=command.copy('image.mdl(\uE81B,12)'))
        item(image=image.mdl(\uE81C,default_icon_size) tip=['History',tip.info] cmd=command.copy('image.mdl(\uE81C,12)'))
        item(image=image.mdl(\uE81D,default_icon_size) tip=['Location',tip.info] cmd=command.copy('image.mdl(\uE81D,12)'))

        item(image=image.mdl(\uE81E,default_icon_size) tip=['MapLayers',tip.info] cmd=command.copy('image.mdl(\uE81E,12)') col)
        item(image=image.mdl(\uE81F,default_icon_size) tip=['Accident',tip.info] cmd=command.copy('image.mdl(\uE81F,12)'))
        item(image=image.mdl(\uE821,default_icon_size) tip=['Work',tip.info] cmd=command.copy('image.mdl(\uE821,12)'))
        item(image=image.mdl(\uE822,default_icon_size) tip=['Construction',tip.info] cmd=command.copy('image.mdl(\uE822,12)'))
        item(image=image.mdl(\uE823,default_icon_size) tip=['Recent',tip.info] cmd=command.copy('image.mdl(\uE823,12)'))
        item(image=image.mdl(\uE825,default_icon_size) tip=['Bank',tip.info] cmd=command.copy('image.mdl(\uE825,12)'))
        item(image=image.mdl(\uE826,default_icon_size) tip=['DownloadMap',tip.info] cmd=command.copy('image.mdl(\uE826,12)'))
        item(image=image.mdl(\uE829,default_icon_size) tip=['InkingToolFill2',tip.info] cmd=command.copy('image.mdl(\uE829,12)'))
        item(image=image.mdl(\uE82A,default_icon_size) tip=['HighlightFill2',tip.info] cmd=command.copy('image.mdl(\uE82A,12)'))
        item(image=image.mdl(\uE82B,default_icon_size) tip=['EraseToolFill',tip.info] cmd=command.copy('image.mdl(\uE82B,12)'))
        item(image=image.mdl(\uE82C,default_icon_size) tip=['EraseToolFill2',tip.info] cmd=command.copy('image.mdl(\uE82C,12)'))
        item(image=image.mdl(\uE82D,default_icon_size) tip=['Dictionary',tip.info] cmd=command.copy('image.mdl(\uE82D,12)'))
        item(image=image.mdl(\uE82E,default_icon_size) tip=['DictionaryAdd',tip.info] cmd=command.copy('image.mdl(\uE82E,12)'))
        item(image=image.mdl(\uE82F,default_icon_size) tip=['ToolTip',tip.info] cmd=command.copy('image.mdl(\uE82F,12)'))
        item(image=image.mdl(\uE830,default_icon_size) tip=['ChromeBack',tip.info] cmd=command.copy('image.mdl(\uE830,12)'))
        item(image=image.mdl(\uE835,default_icon_size) tip=['ProvisioningPackage',tip.info] cmd=command.copy('image.mdl(\uE835,12)'))

        item(image=image.mdl(\uE836,default_icon_size) tip=['AddRemoteDevice',tip.info] cmd=command.copy('image.mdl(\uE836,12)') col)
        item(image=image.mdl(\uE838,default_icon_size) tip=['FolderOpen',tip.info] cmd=command.copy('image.mdl(\uE838,12)'))
        item(image=image.mdl(\uE839,default_icon_size) tip=['Ethernet',tip.info] cmd=command.copy('image.mdl(\uE839,12)'))
        item(image=image.mdl(\uE83A,default_icon_size) tip=['ShareBroadband',tip.info] cmd=command.copy('image.mdl(\uE83A,12)'))
        item(image=image.mdl(\uE83B,default_icon_size) tip=['DirectAccess',tip.info] cmd=command.copy('image.mdl(\uE83B,12)'))
        item(image=image.mdl(\uE83C,default_icon_size) tip=['DialUp',tip.info] cmd=command.copy('image.mdl(\uE83C,12)'))
        item(image=image.mdl(\uE83D,default_icon_size) tip=['DefenderApp',tip.info] cmd=command.copy('image.mdl(\uE83D,12)'))
        item(image=image.mdl(\uE83E,default_icon_size) tip=['BatteryCharging9',tip.info] cmd=command.copy('image.mdl(\uE83E,12)'))
        item(image=image.mdl(\uE83F,default_icon_size) tip=['Battery10',tip.info] cmd=command.copy('image.mdl(\uE83F,12)'))
        item(image=image.mdl(\uE840,default_icon_size) tip=['Pinned',tip.info] cmd=command.copy('image.mdl(\uE840,12)'))
        item(image=image.mdl(\uE841,default_icon_size) tip=['PinFill',tip.info] cmd=command.copy('image.mdl(\uE841,12)'))
        item(image=image.mdl(\uE842,default_icon_size) tip=['PinnedFill',tip.info] cmd=command.copy('image.mdl(\uE842,12)'))
        item(image=image.mdl(\uE843,default_icon_size) tip=['PeriodKey',tip.info] cmd=command.copy('image.mdl(\uE843,12)'))
        item(image=image.mdl(\uE844,default_icon_size) tip=['PuncKey',tip.info] cmd=command.copy('image.mdl(\uE844,12)'))
        item(image=image.mdl(\uE845,default_icon_size) tip=['RevToggleKey',tip.info] cmd=command.copy('image.mdl(\uE845,12)'))
        item(image=image.mdl(\uE846,default_icon_size) tip=['RightArrowKeyTime1',tip.info] cmd=command.copy('image.mdl(\uE846,12)'))

        item(image=image.mdl(\uE847,default_icon_size) tip=['RightArrowKeyTime2',tip.info] cmd=command.copy('image.mdl(\uE847,12)') col)
        item(image=image.mdl(\uE848,default_icon_size) tip=['LeftQuote',tip.info] cmd=command.copy('image.mdl(\uE848,12)'))
        item(image=image.mdl(\uE849,default_icon_size) tip=['RightQuote',tip.info] cmd=command.copy('image.mdl(\uE849,12)'))
        item(image=image.mdl(\uE84A,default_icon_size) tip=['DownShiftKey',tip.info] cmd=command.copy('image.mdl(\uE84A,12)'))
        item(image=image.mdl(\uE84B,default_icon_size) tip=['UpShiftKey',tip.info] cmd=command.copy('image.mdl(\uE84B,12)'))
        item(image=image.mdl(\uE84C,default_icon_size) tip=['PuncKey0',tip.info] cmd=command.copy('image.mdl(\uE84C,12)'))
        item(image=image.mdl(\uE84D,default_icon_size) tip=['PuncKeyLeftBottom',tip.info] cmd=command.copy('image.mdl(\uE84D,12)'))
        item(image=image.mdl(\uE84E,default_icon_size) tip=['RightArrowKeyTime3',tip.info] cmd=command.copy('image.mdl(\uE84E,12)'))
        item(image=image.mdl(\uE84F,default_icon_size) tip=['RightArrowKeyTime4',tip.info] cmd=command.copy('image.mdl(\uE84F,12)'))
        item(image=image.mdl(\uE850,default_icon_size) tip=['Battery0',tip.info] cmd=command.copy('image.mdl(\uE850,12)'))
        item(image=image.mdl(\uE851,default_icon_size) tip=['Battery1',tip.info] cmd=command.copy('image.mdl(\uE851,12)'))
        item(image=image.mdl(\uE852,default_icon_size) tip=['Battery2',tip.info] cmd=command.copy('image.mdl(\uE852,12)'))
        item(image=image.mdl(\uE853,default_icon_size) tip=['Battery3',tip.info] cmd=command.copy('image.mdl(\uE853,12)'))
        item(image=image.mdl(\uE854,default_icon_size) tip=['Battery4',tip.info] cmd=command.copy('image.mdl(\uE854,12)'))
        item(image=image.mdl(\uE855,default_icon_size) tip=['Battery5',tip.info] cmd=command.copy('image.mdl(\uE855,12)'))
        item(image=image.mdl(\uE856,default_icon_size) tip=['Battery6',tip.info] cmd=command.copy('image.mdl(\uE856,12)'))
    }

    menu(title='MDL2 #2')
    {
        item(image=image.mdl(\uE857,default_icon_size) tip=['Battery7',tip.info] cmd=command.copy('image.mdl(\uE857,12)'))
        item(image=image.mdl(\uE858,default_icon_size) tip=['Battery8',tip.info] cmd=command.copy('image.mdl(\uE858,12)'))
        item(image=image.mdl(\uE859,default_icon_size) tip=['Battery9',tip.info] cmd=command.copy('image.mdl(\uE859,12)'))
        item(image=image.mdl(\uE85A,default_icon_size) tip=['BatteryCharging0',tip.info] cmd=command.copy('image.mdl(\uE85A,12)'))
        item(image=image.mdl(\uE85B,default_icon_size) tip=['BatteryCharging1',tip.info] cmd=command.copy('image.mdl(\uE85B,12)'))
        item(image=image.mdl(\uE85C,default_icon_size) tip=['BatteryCharging2',tip.info] cmd=command.copy('image.mdl(\uE85C,12)'))
        item(image=image.mdl(\uE85D,default_icon_size) tip=['BatteryCharging3',tip.info] cmd=command.copy('image.mdl(\uE85D,12)'))
        item(image=image.mdl(\uE85E,default_icon_size) tip=['BatteryCharging4',tip.info] cmd=command.copy('image.mdl(\uE85E,12)'))
        item(image=image.mdl(\uE85F,default_icon_size) tip=['BatteryCharging5',tip.info] cmd=command.copy('image.mdl(\uE85F,12)'))
        item(image=image.mdl(\uE860,default_icon_size) tip=['BatteryCharging6',tip.info] cmd=command.copy('image.mdl(\uE860,12)'))
        item(image=image.mdl(\uE861,default_icon_size) tip=['BatteryCharging7',tip.info] cmd=command.copy('image.mdl(\uE861,12)'))
        item(image=image.mdl(\uE862,default_icon_size) tip=['BatteryCharging8',tip.info] cmd=command.copy('image.mdl(\uE862,12)'))
        item(image=image.mdl(\uE863,default_icon_size) tip=['BatterySaver0',tip.info] cmd=command.copy('image.mdl(\uE863,12)'))
        item(image=image.mdl(\uE864,default_icon_size) tip=['BatterySaver1',tip.info] cmd=command.copy('image.mdl(\uE864,12)'))
        item(image=image.mdl(\uE865,default_icon_size) tip=['BatterySaver2',tip.info] cmd=command.copy('image.mdl(\uE865,12)'))
        item(image=image.mdl(\uE866,default_icon_size) tip=['BatterySaver3',tip.info] cmd=command.copy('image.mdl(\uE866,12)'))

        item(image=image.mdl(\uE867,default_icon_size) tip=['BatterySaver4',tip.info] cmd=command.copy('image.mdl(\uE867,12)') col)
        item(image=image.mdl(\uE868,default_icon_size) tip=['BatterySaver5',tip.info] cmd=command.copy('image.mdl(\uE868,12)'))
        item(image=image.mdl(\uE869,default_icon_size) tip=['BatterySaver6',tip.info] cmd=command.copy('image.mdl(\uE869,12)'))
        item(image=image.mdl(\uE86A,default_icon_size) tip=['BatterySaver7',tip.info] cmd=command.copy('image.mdl(\uE86A,12)'))
        item(image=image.mdl(\uE86B,default_icon_size) tip=['BatterySaver8',tip.info] cmd=command.copy('image.mdl(\uE86B,12)'))
        item(image=image.mdl(\uE86C,default_icon_size) tip=['SignalBars1',tip.info] cmd=command.copy('image.mdl(\uE86C,12)'))
        item(image=image.mdl(\uE86D,default_icon_size) tip=['SignalBars2',tip.info] cmd=command.copy('image.mdl(\uE86D,12)'))
        item(image=image.mdl(\uE86E,default_icon_size) tip=['SignalBars3',tip.info] cmd=command.copy('image.mdl(\uE86E,12)'))
        item(image=image.mdl(\uE86F,default_icon_size) tip=['SignalBars4',tip.info] cmd=command.copy('image.mdl(\uE86F,12)'))
        item(image=image.mdl(\uE870,default_icon_size) tip=['SignalBars5',tip.info] cmd=command.copy('image.mdl(\uE870,12)'))
        item(image=image.mdl(\uE871,default_icon_size) tip=['SignalNotConnected',tip.info] cmd=command.copy('image.mdl(\uE871,12)'))
        item(image=image.mdl(\uE872,default_icon_size) tip=['Wifi1',tip.info] cmd=command.copy('image.mdl(\uE872,12)'))
        item(image=image.mdl(\uE873,default_icon_size) tip=['Wifi2',tip.info] cmd=command.copy('image.mdl(\uE873,12)'))
        item(image=image.mdl(\uE874,default_icon_size) tip=['Wifi3',tip.info] cmd=command.copy('image.mdl(\uE874,12)'))
        item(image=image.mdl(\uE875,default_icon_size) tip=['MobSIMLock',tip.info] cmd=command.copy('image.mdl(\uE875,12)'))
        item(image=image.mdl(\uE876,default_icon_size) tip=['MobSIMMissing',tip.info] cmd=command.copy('image.mdl(\uE876,12)'))

        item(image=image.mdl(\uE877,default_icon_size) tip=['Vibrate',tip.info] cmd=command.copy('image.mdl(\uE877,12)') col)
        item(image=image.mdl(\uE878,default_icon_size) tip=['RoamingInternational',tip.info] cmd=command.copy('image.mdl(\uE878,12)'))
        item(image=image.mdl(\uE879,default_icon_size) tip=['RoamingDomestic',tip.info] cmd=command.copy('image.mdl(\uE879,12)'))
        item(image=image.mdl(\uE87A,default_icon_size) tip=['CallForwardInternational',tip.info] cmd=command.copy('image.mdl(\uE87A,12)'))
        item(image=image.mdl(\uE87B,default_icon_size) tip=['CallForwardRoaming',tip.info] cmd=command.copy('image.mdl(\uE87B,12)'))
        item(image=image.mdl(\uE87C,default_icon_size) tip=['JpnRomanji',tip.info] cmd=command.copy('image.mdl(\uE87C,12)'))
        item(image=image.mdl(\uE87D,default_icon_size) tip=['JpnRomanjiLock',tip.info] cmd=command.copy('image.mdl(\uE87D,12)'))
        item(image=image.mdl(\uE87E,default_icon_size) tip=['JpnRomanjiShift',tip.info] cmd=command.copy('image.mdl(\uE87E,12)'))
        item(image=image.mdl(\uE87F,default_icon_size) tip=['JpnRomanjiShiftLock',tip.info] cmd=command.copy('image.mdl(\uE87F,12)'))
        item(image=image.mdl(\uE880,default_icon_size) tip=['StatusDataTransfer',tip.info] cmd=command.copy('image.mdl(\uE880,12)'))
        item(image=image.mdl(\uE881,default_icon_size) tip=['StatusDataTransferVPN',tip.info] cmd=command.copy('image.mdl(\uE881,12)'))
        item(image=image.mdl(\uE882,default_icon_size) tip=['StatusDualSIM2',tip.info] cmd=command.copy('image.mdl(\uE882,12)'))
        item(image=image.mdl(\uE883,default_icon_size) tip=['StatusDualSIM2VPN',tip.info] cmd=command.copy('image.mdl(\uE883,12)'))
        item(image=image.mdl(\uE884,default_icon_size) tip=['StatusDualSIM1',tip.info] cmd=command.copy('image.mdl(\uE884,12)'))
        item(image=image.mdl(\uE885,default_icon_size) tip=['StatusDualSIM1VPN',tip.info] cmd=command.copy('image.mdl(\uE885,12)'))
        item(image=image.mdl(\uE886,default_icon_size) tip=['StatusSGLTE',tip.info] cmd=command.copy('image.mdl(\uE886,12)'))

        item(image=image.mdl(\uE887,default_icon_size) tip=['StatusSGLTECell',tip.info] cmd=command.copy('image.mdl(\uE887,12)') col)
        item(image=image.mdl(\uE888,default_icon_size) tip=['StatusSGLTEDataVPN',tip.info] cmd=command.copy('image.mdl(\uE888,12)'))
        item(image=image.mdl(\uE889,default_icon_size) tip=['StatusVPN',tip.info] cmd=command.copy('image.mdl(\uE889,12)'))
        item(image=image.mdl(\uE88A,default_icon_size) tip=['WifiHotspot',tip.info] cmd=command.copy('image.mdl(\uE88A,12)'))
        item(image=image.mdl(\uE88B,default_icon_size) tip=['LanguageKor',tip.info] cmd=command.copy('image.mdl(\uE88B,12)'))
        item(image=image.mdl(\uE88C,default_icon_size) tip=['LanguageCht',tip.info] cmd=command.copy('image.mdl(\uE88C,12)'))
        item(image=image.mdl(\uE88D,default_icon_size) tip=['LanguageChs',tip.info] cmd=command.copy('image.mdl(\uE88D,12)'))
        item(image=image.mdl(\uE88E,default_icon_size) tip=['USB',tip.info] cmd=command.copy('image.mdl(\uE88E,12)'))
        item(image=image.mdl(\uE88F,default_icon_size) tip=['InkingToolFill',tip.info] cmd=command.copy('image.mdl(\uE88F,12)'))
        item(image=image.mdl(\uE890,default_icon_size) tip=['View',tip.info] cmd=command.copy('image.mdl(\uE890,12)'))
        item(image=image.mdl(\uE891,default_icon_size) tip=['HighlightFill',tip.info] cmd=command.copy('image.mdl(\uE891,12)'))
        item(image=image.mdl(\uE892,default_icon_size) tip=['Previous',tip.info] cmd=command.copy('image.mdl(\uE892,12)'))
        item(image=image.mdl(\uE893,default_icon_size) tip=['Next',tip.info] cmd=command.copy('image.mdl(\uE893,12)'))
        item(image=image.mdl(\uE894,default_icon_size) tip=['Clear',tip.info] cmd=command.copy('image.mdl(\uE894,12)'))
        item(image=image.mdl(\uE895,default_icon_size) tip=['Sync',tip.info] cmd=command.copy('image.mdl(\uE895,12)'))
        item(image=image.mdl(\uE896,default_icon_size) tip=['Download',tip.info] cmd=command.copy('image.mdl(\uE896,12)'))

        item(image=image.mdl(\uE897,default_icon_size) tip=['Help',tip.info] cmd=command.copy('image.mdl(\uE897,12)') col)
        item(image=image.mdl(\uE898,default_icon_size) tip=['Upload',tip.info] cmd=command.copy('image.mdl(\uE898,12)'))
        item(image=image.mdl(\uE899,default_icon_size) tip=['Emoji',tip.info] cmd=command.copy('image.mdl(\uE899,12)'))
        item(image=image.mdl(\uE89A,default_icon_size) tip=['TwoPage',tip.info] cmd=command.copy('image.mdl(\uE89A,12)'))
        item(image=image.mdl(\uE89B,default_icon_size) tip=['LeaveChat',tip.info] cmd=command.copy('image.mdl(\uE89B,12)'))
        item(image=image.mdl(\uE89C,default_icon_size) tip=['MailForward',tip.info] cmd=command.copy('image.mdl(\uE89C,12)'))
        item(image=image.mdl(\uE89E,default_icon_size) tip=['RotateCamera',tip.info] cmd=command.copy('image.mdl(\uE89E,12)'))
        item(image=image.mdl(\uE89F,default_icon_size) tip=['ClosePane',tip.info] cmd=command.copy('image.mdl(\uE89F,12)'))
        item(image=image.mdl(\uE8A0,default_icon_size) tip=['OpenPane',tip.info] cmd=command.copy('image.mdl(\uE8A0,12)'))
        item(image=image.mdl(\uE8A1,default_icon_size) tip=['PreviewLink',tip.info] cmd=command.copy('image.mdl(\uE8A1,12)'))
        item(image=image.mdl(\uE8A2,default_icon_size) tip=['AttachCamera',tip.info] cmd=command.copy('image.mdl(\uE8A2,12)'))
        item(image=image.mdl(\uE8A3,default_icon_size) tip=['ZoomIn',tip.info] cmd=command.copy('image.mdl(\uE8A3,12)'))
        item(image=image.mdl(\uE8A4,default_icon_size) tip=['Bookmarks',tip.info] cmd=command.copy('image.mdl(\uE8A4,12)'))
        item(image=image.mdl(\uE8A5,default_icon_size) tip=['Document',tip.info] cmd=command.copy('image.mdl(\uE8A5,12)'))
        item(image=image.mdl(\uE8A6,default_icon_size) tip=['ProtectedDocument',tip.info] cmd=command.copy('image.mdl(\uE8A6,12)'))
        item(image=image.mdl(\uE8A7,default_icon_size) tip=['OpenInNewWindow',tip.info] cmd=command.copy('image.mdl(\uE8A7,12)'))

        item(image=image.mdl(\uE8A8,default_icon_size) tip=['MailFill',tip.info] cmd=command.copy('image.mdl(\uE8A8,12)') col)
        item(image=image.mdl(\uE8A9,default_icon_size) tip=['ViewAll',tip.info] cmd=command.copy('image.mdl(\uE8A9,12)'))
        item(image=image.mdl(\uE8AA,default_icon_size) tip=['VideoChat',tip.info] cmd=command.copy('image.mdl(\uE8AA,12)'))
        item(image=image.mdl(\uE8AB,default_icon_size) tip=['Switch',tip.info] cmd=command.copy('image.mdl(\uE8AB,12)'))
        item(image=image.mdl(\uE8AC,default_icon_size) tip=['Rename',tip.info] cmd=command.copy('image.mdl(\uE8AC,12)'))
        item(image=image.mdl(\uE8AD,default_icon_size) tip=['Go',tip.info] cmd=command.copy('image.mdl(\uE8AD,12)'))
        item(image=image.mdl(\uE8AE,default_icon_size) tip=['SurfaceHub',tip.info] cmd=command.copy('image.mdl(\uE8AE,12)'))
        item(image=image.mdl(\uE8AF,default_icon_size) tip=['Remote',tip.info] cmd=command.copy('image.mdl(\uE8AF,12)'))
        item(image=image.mdl(\uE8B0,default_icon_size) tip=['Click',tip.info] cmd=command.copy('image.mdl(\uE8B0,12)'))
        item(image=image.mdl(\uE8B1,default_icon_size) tip=['Shuffle',tip.info] cmd=command.copy('image.mdl(\uE8B1,12)'))
        item(image=image.mdl(\uE8B2,default_icon_size) tip=['Movies',tip.info] cmd=command.copy('image.mdl(\uE8B2,12)'))
        item(image=image.mdl(\uE8B3,default_icon_size) tip=['SelectAll',tip.info] cmd=command.copy('image.mdl(\uE8B3,12)'))
        item(image=image.mdl(\uE8B4,default_icon_size) tip=['Orientation',tip.info] cmd=command.copy('image.mdl(\uE8B4,12)'))
        item(image=image.mdl(\uE8B5,default_icon_size) tip=['Import',tip.info] cmd=command.copy('image.mdl(\uE8B5,12)'))
        item(image=image.mdl(\uE8B6,default_icon_size) tip=['ImportAll',tip.info] cmd=command.copy('image.mdl(\uE8B6,12)'))
        item(image=image.mdl(\uE8B7,default_icon_size) tip=['Folder',tip.info] cmd=command.copy('image.mdl(\uE8B7,12)'))

        item(image=image.mdl(\uE8B8,default_icon_size) tip=['Webcam',tip.info] cmd=command.copy('image.mdl(\uE8B8,12)') col)
        item(image=image.mdl(\uE8B9,default_icon_size) tip=['Picture',tip.info] cmd=command.copy('image.mdl(\uE8B9,12)'))
        item(image=image.mdl(\uE8BA,default_icon_size) tip=['Caption',tip.info] cmd=command.copy('image.mdl(\uE8BA,12)'))
        item(image=image.mdl(\uE8BB,default_icon_size) tip=['ChromeClose',tip.info] cmd=command.copy('image.mdl(\uE8BB,12)'))
        item(image=image.mdl(\uE8BC,default_icon_size) tip=['ShowResults',tip.info] cmd=command.copy('image.mdl(\uE8BC,12)'))
        item(image=image.mdl(\uE8BD,default_icon_size) tip=['Message',tip.info] cmd=command.copy('image.mdl(\uE8BD,12)'))
        item(image=image.mdl(\uE8BE,default_icon_size) tip=['Leaf',tip.info] cmd=command.copy('image.mdl(\uE8BE,12)'))
        item(image=image.mdl(\uE8BF,default_icon_size) tip=['CalendarDay',tip.info] cmd=command.copy('image.mdl(\uE8BF,12)'))
        item(image=image.mdl(\uE8C0,default_icon_size) tip=['CalendarWeek',tip.info] cmd=command.copy('image.mdl(\uE8C0,12)'))
        item(image=image.mdl(\uE8C1,default_icon_size) tip=['Characters',tip.info] cmd=command.copy('image.mdl(\uE8C1,12)'))
        item(image=image.mdl(\uE8C2,default_icon_size) tip=['MailReplyAll',tip.info] cmd=command.copy('image.mdl(\uE8C2,12)'))
        item(image=image.mdl(\uE8C3,default_icon_size) tip=['Read',tip.info] cmd=command.copy('image.mdl(\uE8C3,12)'))
        item(image=image.mdl(\uE8C4,default_icon_size) tip=['ShowBcc',tip.info] cmd=command.copy('image.mdl(\uE8C4,12)'))
        item(image=image.mdl(\uE8C5,default_icon_size) tip=['HideBcc',tip.info] cmd=command.copy('image.mdl(\uE8C5,12)'))
        item(image=image.mdl(\uE8C6,default_icon_size) tip=['Cut',tip.info] cmd=command.copy('image.mdl(\uE8C6,12)'))
        item(image=image.mdl(\uE8C7,default_icon_size) tip=['PaymentCard',tip.info] cmd=command.copy('image.mdl(\uE8C7,12)'))

        item(image=image.mdl(\uE8C8,default_icon_size) tip=['Copy',tip.info] cmd=command.copy('image.mdl(\uE8C8,12)') col)
        item(image=image.mdl(\uE8C9,default_icon_size) tip=['Important',tip.info] cmd=command.copy('image.mdl(\uE8C9,12)'))
        item(image=image.mdl(\uE8CA,default_icon_size) tip=['MailReply',tip.info] cmd=command.copy('image.mdl(\uE8CA,12)'))
        item(image=image.mdl(\uE8CB,default_icon_size) tip=['Sort',tip.info] cmd=command.copy('image.mdl(\uE8CB,12)'))
        item(image=image.mdl(\uE8CC,default_icon_size) tip=['MobileTablet',tip.info] cmd=command.copy('image.mdl(\uE8CC,12)'))
        item(image=image.mdl(\uE8CD,default_icon_size) tip=['DisconnectDrive',tip.info] cmd=command.copy('image.mdl(\uE8CD,12)'))
        item(image=image.mdl(\uE8CE,default_icon_size) tip=['MapDrive',tip.info] cmd=command.copy('image.mdl(\uE8CE,12)'))
        item(image=image.mdl(\uE8CF,default_icon_size) tip=['ContactPresence',tip.info] cmd=command.copy('image.mdl(\uE8CF,12)'))
        item(image=image.mdl(\uE8D0,default_icon_size) tip=['Priority',tip.info] cmd=command.copy('image.mdl(\uE8D0,12)'))
        item(image=image.mdl(\uE8D1,default_icon_size) tip=['GotoToday',tip.info] cmd=command.copy('image.mdl(\uE8D1,12)'))
        item(image=image.mdl(\uE8D2,default_icon_size) tip=['Font',tip.info] cmd=command.copy('image.mdl(\uE8D2,12)'))
        item(image=image.mdl(\uE8D3,default_icon_size) tip=['FontColor',tip.info] cmd=command.copy('image.mdl(\uE8D3,12)'))
        item(image=image.mdl(\uE8D4,default_icon_size) tip=['Contact2',tip.info] cmd=command.copy('image.mdl(\uE8D4,12)'))
        item(image=image.mdl(\uE8D5,default_icon_size) tip=['FolderFill',tip.info] cmd=command.copy('image.mdl(\uE8D5,12)'))
        item(image=image.mdl(\uE8D6,default_icon_size) tip=['Audio',tip.info] cmd=command.copy('image.mdl(\uE8D6,12)'))
        item(image=image.mdl(\uE8D7,default_icon_size) tip=['Permissions',tip.info] cmd=command.copy('image.mdl(\uE8D7,12)'))

        item(image=image.mdl(\uE8D8,default_icon_size) tip=['DisableUpdates',tip.info] cmd=command.copy('image.mdl(\uE8D8,12)') col)
        item(image=image.mdl(\uE8D9,default_icon_size) tip=['Unfavorite',tip.info] cmd=command.copy('image.mdl(\uE8D9,12)'))
        item(image=image.mdl(\uE8DA,default_icon_size) tip=['OpenLocal',tip.info] cmd=command.copy('image.mdl(\uE8DA,12)'))
        item(image=image.mdl(\uE8DB,default_icon_size) tip=['Italic',tip.info] cmd=command.copy('image.mdl(\uE8DB,12)'))
        item(image=image.mdl(\uE8DC,default_icon_size) tip=['Underline',tip.info] cmd=command.copy('image.mdl(\uE8DC,12)'))
        item(image=image.mdl(\uE8DD,default_icon_size) tip=['Bold',tip.info] cmd=command.copy('image.mdl(\uE8DD,12)'))
        item(image=image.mdl(\uE8DE,default_icon_size) tip=['MoveToFolder',tip.info] cmd=command.copy('image.mdl(\uE8DE,12)'))
        item(image=image.mdl(\uE8DF,default_icon_size) tip=['LikeDislike',tip.info] cmd=command.copy('image.mdl(\uE8DF,12)'))
        item(image=image.mdl(\uE8E0,default_icon_size) tip=['Dislike',tip.info] cmd=command.copy('image.mdl(\uE8E0,12)'))
        item(image=image.mdl(\uE8E1,default_icon_size) tip=['Like',tip.info] cmd=command.copy('image.mdl(\uE8E1,12)'))
        item(image=image.mdl(\uE8E2,default_icon_size) tip=['AlignRight',tip.info] cmd=command.copy('image.mdl(\uE8E2,12)'))
        item(image=image.mdl(\uE8E3,default_icon_size) tip=['AlignCenter',tip.info] cmd=command.copy('image.mdl(\uE8E3,12)'))
        item(image=image.mdl(\uE8E4,default_icon_size) tip=['AlignLeft',tip.info] cmd=command.copy('image.mdl(\uE8E4,12)'))
        item(image=image.mdl(\uE8E5,default_icon_size) tip=['OpenFile',tip.info] cmd=command.copy('image.mdl(\uE8E5,12)'))
        item(image=image.mdl(\uE8E6,default_icon_size) tip=['ClearSelection',tip.info] cmd=command.copy('image.mdl(\uE8E6,12)'))
        item(image=image.mdl(\uE8E7,default_icon_size) tip=['FontDecrease',tip.info] cmd=command.copy('image.mdl(\uE8E7,12)'))

        item(image=image.mdl(\uE8E8,default_icon_size) tip=['FontIncrease',tip.info] cmd=command.copy('image.mdl(\uE8E8,12)') col)
        item(image=image.mdl(\uE8E9,default_icon_size) tip=['FontSize',tip.info] cmd=command.copy('image.mdl(\uE8E9,12)'))
        item(image=image.mdl(\uE8EA,default_icon_size) tip=['CellPhone',tip.info] cmd=command.copy('image.mdl(\uE8EA,12)'))
        item(image=image.mdl(\uE8EB,default_icon_size) tip=['Reshare',tip.info] cmd=command.copy('image.mdl(\uE8EB,12)'))
        item(image=image.mdl(\uE8EC,default_icon_size) tip=['Tag',tip.info] cmd=command.copy('image.mdl(\uE8EC,12)'))
        item(image=image.mdl(\uE8ED,default_icon_size) tip=['RepeatOne',tip.info] cmd=command.copy('image.mdl(\uE8ED,12)'))
        item(image=image.mdl(\uE8EE,default_icon_size) tip=['RepeatAll',tip.info] cmd=command.copy('image.mdl(\uE8EE,12)'))
        item(image=image.mdl(\uE8EF,default_icon_size) tip=['Calculator',tip.info] cmd=command.copy('image.mdl(\uE8EF,12)'))
        item(image=image.mdl(\uE8F0,default_icon_size) tip=['Directions',tip.info] cmd=command.copy('image.mdl(\uE8F0,12)'))
        item(image=image.mdl(\uE8F1,default_icon_size) tip=['Library',tip.info] cmd=command.copy('image.mdl(\uE8F1,12)'))
        item(image=image.mdl(\uE8F2,default_icon_size) tip=['ChatBubbles',tip.info] cmd=command.copy('image.mdl(\uE8F2,12)'))
        item(image=image.mdl(\uE8F3,default_icon_size) tip=['PostUpdate',tip.info] cmd=command.copy('image.mdl(\uE8F3,12)'))
        item(image=image.mdl(\uE8F4,default_icon_size) tip=['NewFolder',tip.info] cmd=command.copy('image.mdl(\uE8F4,12)'))
        item(image=image.mdl(\uE8F5,default_icon_size) tip=['CalendarReply',tip.info] cmd=command.copy('image.mdl(\uE8F5,12)'))
        item(image=image.mdl(\uE8F6,default_icon_size) tip=['UnsyncFolder',tip.info] cmd=command.copy('image.mdl(\uE8F6,12)'))
        item(image=image.mdl(\uE8F7,default_icon_size) tip=['SyncFolder',tip.info] cmd=command.copy('image.mdl(\uE8F7,12)'))

        item(image=image.mdl(\uE8F8,default_icon_size) tip=['BlockContact',tip.info] cmd=command.copy('image.mdl(\uE8F8,12)') col)
        item(image=image.mdl(\uE8F9,default_icon_size) tip=['SwitchApps',tip.info] cmd=command.copy('image.mdl(\uE8F9,12)'))
        item(image=image.mdl(\uE8FA,default_icon_size) tip=['AddFriend',tip.info] cmd=command.copy('image.mdl(\uE8FA,12)'))
        item(image=image.mdl(\uE8FB,default_icon_size) tip=['Accept',tip.info] cmd=command.copy('image.mdl(\uE8FB,12)'))
        item(image=image.mdl(\uE8FC,default_icon_size) tip=['GoToStart',tip.info] cmd=command.copy('image.mdl(\uE8FC,12)'))
        item(image=image.mdl(\uE8FD,default_icon_size) tip=['BulletedList',tip.info] cmd=command.copy('image.mdl(\uE8FD,12)'))
        item(image=image.mdl(\uE8FE,default_icon_size) tip=['Scan',tip.info] cmd=command.copy('image.mdl(\uE8FE,12)'))
        item(image=image.mdl(\uE8FF,default_icon_size) tip=['Preview',tip.info] cmd=command.copy('image.mdl(\uE8FF,12)'))
        item(image=image.mdl(\uE902,default_icon_size) tip=['Group',tip.info] cmd=command.copy('image.mdl(\uE902,12)'))
        item(image=image.mdl(\uE904,default_icon_size) tip=['ZeroBars',tip.info] cmd=command.copy('image.mdl(\uE904,12)'))
        item(image=image.mdl(\uE905,default_icon_size) tip=['OneBar',tip.info] cmd=command.copy('image.mdl(\uE905,12)'))
        item(image=image.mdl(\uE906,default_icon_size) tip=['TwoBars',tip.info] cmd=command.copy('image.mdl(\uE906,12)'))
        item(image=image.mdl(\uE907,default_icon_size) tip=['ThreeBars',tip.info] cmd=command.copy('image.mdl(\uE907,12)'))
        item(image=image.mdl(\uE908,default_icon_size) tip=['FourBars',tip.info] cmd=command.copy('image.mdl(\uE908,12)'))
        item(image=image.mdl(\uE909,default_icon_size) tip=['World',tip.info] cmd=command.copy('image.mdl(\uE909,12)'))
        item(image=image.mdl(\uE90A,default_icon_size) tip=['Comment',tip.info] cmd=command.copy('image.mdl(\uE90A,12)'))

        item(image=image.mdl(\uE90B,default_icon_size) tip=['MusicInfo',tip.info] cmd=command.copy('image.mdl(\uE90B,12)') col)
        item(image=image.mdl(\uE90C,default_icon_size) tip=['DockLeft',tip.info] cmd=command.copy('image.mdl(\uE90C,12)'))
        item(image=image.mdl(\uE90D,default_icon_size) tip=['DockRight',tip.info] cmd=command.copy('image.mdl(\uE90D,12)'))
        item(image=image.mdl(\uE90E,default_icon_size) tip=['DockBottom',tip.info] cmd=command.copy('image.mdl(\uE90E,12)'))
        item(image=image.mdl(\uE90F,default_icon_size) tip=['Repair',tip.info] cmd=command.copy('image.mdl(\uE90F,12)'))
        item(image=image.mdl(\uE910,default_icon_size) tip=['Accounts',tip.info] cmd=command.copy('image.mdl(\uE910,12)'))
        item(image=image.mdl(\uE911,default_icon_size) tip=['DullSound',tip.info] cmd=command.copy('image.mdl(\uE911,12)'))
        item(image=image.mdl(\uE912,default_icon_size) tip=['Manage',tip.info] cmd=command.copy('image.mdl(\uE912,12)'))
        item(image=image.mdl(\uE913,default_icon_size) tip=['Street',tip.info] cmd=command.copy('image.mdl(\uE913,12)'))
        item(image=image.mdl(\uE914,default_icon_size) tip=['Printer3D',tip.info] cmd=command.copy('image.mdl(\uE914,12)'))
        item(image=image.mdl(\uE915,default_icon_size) tip=['RadioBullet',tip.info] cmd=command.copy('image.mdl(\uE915,12)'))
        item(image=image.mdl(\uE916,default_icon_size) tip=['Stopwatch',tip.info] cmd=command.copy('image.mdl(\uE916,12)'))
        item(image=image.mdl(\uE91B,default_icon_size) tip=['Photo',tip.info] cmd=command.copy('image.mdl(\uE91B,12)'))
        item(image=image.mdl(\uE91C,default_icon_size) tip=['ActionCenter',tip.info] cmd=command.copy('image.mdl(\uE91C,12)'))
        item(image=image.mdl(\uE91F,default_icon_size) tip=['FullCircleMask',tip.info] cmd=command.copy('image.mdl(\uE91F,12)'))
        item(image=image.mdl(\uE921,default_icon_size) tip=['ChromeMinimize',tip.info] cmd=command.copy('image.mdl(\uE921,12)'))

        item(image=image.mdl(\uE922,default_icon_size) tip=['ChromeMaximize',tip.info] cmd=command.copy('image.mdl(\uE922,12)') col)
        item(image=image.mdl(\uE923,default_icon_size) tip=['ChromeRestore',tip.info] cmd=command.copy('image.mdl(\uE923,12)'))
        item(image=image.mdl(\uE924,default_icon_size) tip=['Annotation',tip.info] cmd=command.copy('image.mdl(\uE924,12)'))
        item(image=image.mdl(\uE925,default_icon_size) tip=['BackSpaceQWERTYSm',tip.info] cmd=command.copy('image.mdl(\uE925,12)'))
        item(image=image.mdl(\uE926,default_icon_size) tip=['BackSpaceQWERTYMd',tip.info] cmd=command.copy('image.mdl(\uE926,12)'))
        item(image=image.mdl(\uE927,default_icon_size) tip=['Swipe',tip.info] cmd=command.copy('image.mdl(\uE927,12)'))
        item(image=image.mdl(\uE928,default_icon_size) tip=['Fingerprint',tip.info] cmd=command.copy('image.mdl(\uE928,12)'))
        item(image=image.mdl(\uE929,default_icon_size) tip=['Handwriting',tip.info] cmd=command.copy('image.mdl(\uE929,12)'))
        item(image=image.mdl(\uE92C,default_icon_size) tip=['ChromeBackToWindow',tip.info] cmd=command.copy('image.mdl(\uE92C,12)'))
        item(image=image.mdl(\uE92D,default_icon_size) tip=['ChromeFullScreen',tip.info] cmd=command.copy('image.mdl(\uE92D,12)'))
        item(image=image.mdl(\uE92E,default_icon_size) tip=['KeyboardStandard',tip.info] cmd=command.copy('image.mdl(\uE92E,12)'))
        item(image=image.mdl(\uE92F,default_icon_size) tip=['KeyboardDismiss',tip.info] cmd=command.copy('image.mdl(\uE92F,12)'))
        item(image=image.mdl(\uE930,default_icon_size) tip=['Completed',tip.info] cmd=command.copy('image.mdl(\uE930,12)'))
        item(image=image.mdl(\uE931,default_icon_size) tip=['ChromeAnnotate',tip.info] cmd=command.copy('image.mdl(\uE931,12)'))
        item(image=image.mdl(\uE932,default_icon_size) tip=['Label',tip.info] cmd=command.copy('image.mdl(\uE932,12)'))
        item(image=image.mdl(\uE933,default_icon_size) tip=['IBeam',tip.info] cmd=command.copy('image.mdl(\uE933,12)'))

        item(image=image.mdl(\uE934,default_icon_size) tip=['IBeamOutline',tip.info] cmd=command.copy('image.mdl(\uE934,12)') col)
        item(image=image.mdl(\uE935,default_icon_size) tip=['FlickDown',tip.info] cmd=command.copy('image.mdl(\uE935,12)'))
        item(image=image.mdl(\uE936,default_icon_size) tip=['FlickUp',tip.info] cmd=command.copy('image.mdl(\uE936,12)'))
        item(image=image.mdl(\uE937,default_icon_size) tip=['FlickLeft',tip.info] cmd=command.copy('image.mdl(\uE937,12)'))
        item(image=image.mdl(\uE938,default_icon_size) tip=['FlickRight',tip.info] cmd=command.copy('image.mdl(\uE938,12)'))
        item(image=image.mdl(\uE939,default_icon_size) tip=['FeedbackApp',tip.info] cmd=command.copy('image.mdl(\uE939,12)'))
        item(image=image.mdl(\uE93C,default_icon_size) tip=['MusicAlbum',tip.info] cmd=command.copy('image.mdl(\uE93C,12)'))
        item(image=image.mdl(\uE93E,default_icon_size) tip=['Streaming',tip.info] cmd=command.copy('image.mdl(\uE93E,12)'))
        item(image=image.mdl(\uE943,default_icon_size) tip=['Code',tip.info] cmd=command.copy('image.mdl(\uE943,12)'))
        item(image=image.mdl(\uE944,default_icon_size) tip=['ReturnToWindow',tip.info] cmd=command.copy('image.mdl(\uE944,12)'))
        item(image=image.mdl(\uE945,default_icon_size) tip=['LightningBolt',tip.info] cmd=command.copy('image.mdl(\uE945,12)'))
        item(image=image.mdl(\uE946,default_icon_size) tip=['Info',tip.info] cmd=command.copy('image.mdl(\uE946,12)'))
        item(image=image.mdl(\uE947,default_icon_size) tip=['CalculatorMultiply',tip.info] cmd=command.copy('image.mdl(\uE947,12)'))
        item(image=image.mdl(\uE948,default_icon_size) tip=['CalculatorAddition',tip.info] cmd=command.copy('image.mdl(\uE948,12)'))
        item(image=image.mdl(\uE949,default_icon_size) tip=['CalculatorSubtract',tip.info] cmd=command.copy('image.mdl(\uE949,12)'))
        item(image=image.mdl(\uE94A,default_icon_size) tip=['CalculatorDivide',tip.info] cmd=command.copy('image.mdl(\uE94A,12)'))

        item(image=image.mdl(\uE94B,default_icon_size) tip=['CalculatorSquareroot',tip.info] cmd=command.copy('image.mdl(\uE94B,12)') col)
        item(image=image.mdl(\uE94C,default_icon_size) tip=['CalculatorPercentage',tip.info] cmd=command.copy('image.mdl(\uE94C,12)'))
        item(image=image.mdl(\uE94D,default_icon_size) tip=['CalculatorNegate',tip.info] cmd=command.copy('image.mdl(\uE94D,12)'))
        item(image=image.mdl(\uE94E,default_icon_size) tip=['CalculatorEqualTo',tip.info] cmd=command.copy('image.mdl(\uE94E,12)'))
        item(image=image.mdl(\uE94F,default_icon_size) tip=['CalculatorBackspace',tip.info] cmd=command.copy('image.mdl(\uE94F,12)'))
        item(image=image.mdl(\uE950,default_icon_size) tip=['Component',tip.info] cmd=command.copy('image.mdl(\uE950,12)'))
        item(image=image.mdl(\uE951,default_icon_size) tip=['DMC',tip.info] cmd=command.copy('image.mdl(\uE951,12)'))
        item(image=image.mdl(\uE952,default_icon_size) tip=['Dock',tip.info] cmd=command.copy('image.mdl(\uE952,12)'))
        item(image=image.mdl(\uE953,default_icon_size) tip=['MultimediaDMS',tip.info] cmd=command.copy('image.mdl(\uE953,12)'))
        item(image=image.mdl(\uE954,default_icon_size) tip=['MultimediaDVR',tip.info] cmd=command.copy('image.mdl(\uE954,12)'))
        item(image=image.mdl(\uE955,default_icon_size) tip=['MultimediaPMP',tip.info] cmd=command.copy('image.mdl(\uE955,12)'))
        item(image=image.mdl(\uE956,default_icon_size) tip=['PrintfaxPrinterFile',tip.info] cmd=command.copy('image.mdl(\uE956,12)'))
        item(image=image.mdl(\uE957,default_icon_size) tip=['Sensor',tip.info] cmd=command.copy('image.mdl(\uE957,12)'))
        item(image=image.mdl(\uE958,default_icon_size) tip=['StorageOptical',tip.info] cmd=command.copy('image.mdl(\uE958,12)'))
        item(image=image.mdl(\uE95A,default_icon_size) tip=['Communications',tip.info] cmd=command.copy('image.mdl(\uE95A,12)'))
        item(image=image.mdl(\uE95B,default_icon_size) tip=['Headset',tip.info] cmd=command.copy('image.mdl(\uE95B,12)'))

        item(image=image.mdl(\uE95D,default_icon_size) tip=['Projector',tip.info] cmd=command.copy('image.mdl(\uE95D,12)') col)
        item(image=image.mdl(\uE95E,default_icon_size) tip=['Health',tip.info] cmd=command.copy('image.mdl(\uE95E,12)'))
        item(image=image.mdl(\uE95F,default_icon_size) tip=['Wire',tip.info] cmd=command.copy('image.mdl(\uE95F,12)'))
        item(image=image.mdl(\uE960,default_icon_size) tip=['Webcam2',tip.info] cmd=command.copy('image.mdl(\uE960,12)'))
        item(image=image.mdl(\uE961,default_icon_size) tip=['Input',tip.info] cmd=command.copy('image.mdl(\uE961,12)'))
        item(image=image.mdl(\uE962,default_icon_size) tip=['Mouse',tip.info] cmd=command.copy('image.mdl(\uE962,12)'))
        item(image=image.mdl(\uE963,default_icon_size) tip=['Smartcard',tip.info] cmd=command.copy('image.mdl(\uE963,12)'))
        item(image=image.mdl(\uE964,default_icon_size) tip=['SmartcardVirtual',tip.info] cmd=command.copy('image.mdl(\uE964,12)'))
        item(image=image.mdl(\uE965,default_icon_size) tip=['MediaStorageTower',tip.info] cmd=command.copy('image.mdl(\uE965,12)'))
        item(image=image.mdl(\uE966,default_icon_size) tip=['ReturnKeySm',tip.info] cmd=command.copy('image.mdl(\uE966,12)'))
        item(image=image.mdl(\uE967,default_icon_size) tip=['GameConsole',tip.info] cmd=command.copy('image.mdl(\uE967,12)'))
        item(image=image.mdl(\uE968,default_icon_size) tip=['Network',tip.info] cmd=command.copy('image.mdl(\uE968,12)'))
        item(image=image.mdl(\uE969,default_icon_size) tip=['StorageNetworkWireless',tip.info] cmd=command.copy('image.mdl(\uE969,12)'))
        item(image=image.mdl(\uE96A,default_icon_size) tip=['StorageTape',tip.info] cmd=command.copy('image.mdl(\uE96A,12)'))
        item(image=image.mdl(\uE96D,default_icon_size) tip=['ChevronUpSmall',tip.info] cmd=command.copy('image.mdl(\uE96D,12)'))
        item(image=image.mdl(\uE96E,default_icon_size) tip=['ChevronDownSmall',tip.info] cmd=command.copy('image.mdl(\uE96E,12)'))
    }

    menu(title='MDL2 #3')
    {
        item(image=image.mdl(\uE96F,default_icon_size) tip=['ChevronLeftSmall',tip.info] cmd=command.copy('image.mdl(\uE96F,12)'))
        item(image=image.mdl(\uE970,default_icon_size) tip=['ChevronRightSmall',tip.info] cmd=command.copy('image.mdl(\uE970,12)'))
        item(image=image.mdl(\uE971,default_icon_size) tip=['ChevronUpMed',tip.info] cmd=command.copy('image.mdl(\uE971,12)'))
        item(image=image.mdl(\uE972,default_icon_size) tip=['ChevronDownMed',tip.info] cmd=command.copy('image.mdl(\uE972,12)'))
        item(image=image.mdl(\uE973,default_icon_size) tip=['ChevronLeftMed',tip.info] cmd=command.copy('image.mdl(\uE973,12)'))
        item(image=image.mdl(\uE974,default_icon_size) tip=['ChevronRightMed',tip.info] cmd=command.copy('image.mdl(\uE974,12)'))
        item(image=image.mdl(\uE975,default_icon_size) tip=['Devices2',tip.info] cmd=command.copy('image.mdl(\uE975,12)'))
        item(image=image.mdl(\uE976,default_icon_size) tip=['ExpandTile',tip.info] cmd=command.copy('image.mdl(\uE976,12)'))
        item(image=image.mdl(\uE977,default_icon_size) tip=['PC1',tip.info] cmd=command.copy('image.mdl(\uE977,12)'))
        item(image=image.mdl(\uE978,default_icon_size) tip=['PresenceChicklet',tip.info] cmd=command.copy('image.mdl(\uE978,12)'))
        item(image=image.mdl(\uE979,default_icon_size) tip=['PresenceChickletVideo',tip.info] cmd=command.copy('image.mdl(\uE979,12)'))
        item(image=image.mdl(\uE97A,default_icon_size) tip=['Reply',tip.info] cmd=command.copy('image.mdl(\uE97A,12)'))
        item(image=image.mdl(\uE97B,default_icon_size) tip=['SetTile',tip.info] cmd=command.copy('image.mdl(\uE97B,12)'))
        item(image=image.mdl(\uE97C,default_icon_size) tip=['Type',tip.info] cmd=command.copy('image.mdl(\uE97C,12)'))
        item(image=image.mdl(\uE97D,default_icon_size) tip=['Korean',tip.info] cmd=command.copy('image.mdl(\uE97D,12)'))
        item(image=image.mdl(\uE97E,default_icon_size) tip=['HalfAlpha',tip.info] cmd=command.copy('image.mdl(\uE97E,12)'))

        item(image=image.mdl(\uE97F,default_icon_size) tip=['FullAlpha',tip.info] cmd=command.copy('image.mdl(\uE97F,12)') col)
        item(image=image.mdl(\uE980,default_icon_size) tip=['Key12On',tip.info] cmd=command.copy('image.mdl(\uE980,12)'))
        item(image=image.mdl(\uE981,default_icon_size) tip=['ChineseChangjie',tip.info] cmd=command.copy('image.mdl(\uE981,12)'))
        item(image=image.mdl(\uE982,default_icon_size) tip=['QWERTYOn',tip.info] cmd=command.copy('image.mdl(\uE982,12)'))
        item(image=image.mdl(\uE983,default_icon_size) tip=['QWERTYOff',tip.info] cmd=command.copy('image.mdl(\uE983,12)'))
        item(image=image.mdl(\uE984,default_icon_size) tip=['ChineseQuick',tip.info] cmd=command.copy('image.mdl(\uE984,12)'))
        item(image=image.mdl(\uE985,default_icon_size) tip=['Japanese',tip.info] cmd=command.copy('image.mdl(\uE985,12)'))
        item(image=image.mdl(\uE986,default_icon_size) tip=['FullHiragana',tip.info] cmd=command.copy('image.mdl(\uE986,12)'))
        item(image=image.mdl(\uE987,default_icon_size) tip=['FullKatakana',tip.info] cmd=command.copy('image.mdl(\uE987,12)'))
        item(image=image.mdl(\uE988,default_icon_size) tip=['HalfKatakana',tip.info] cmd=command.copy('image.mdl(\uE988,12)'))
        item(image=image.mdl(\uE989,default_icon_size) tip=['ChineseBoPoMoFo',tip.info] cmd=command.copy('image.mdl(\uE989,12)'))
        item(image=image.mdl(\uE98A,default_icon_size) tip=['ChinesePinyin',tip.info] cmd=command.copy('image.mdl(\uE98A,12)'))
        item(image=image.mdl(\uE98F,default_icon_size) tip=['ConstructionCone',tip.info] cmd=command.copy('image.mdl(\uE98F,12)'))
        item(image=image.mdl(\uE990,default_icon_size) tip=['XboxOneConsole',tip.info] cmd=command.copy('image.mdl(\uE990,12)'))
        item(image=image.mdl(\uE992,default_icon_size) tip=['Volume0',tip.info] cmd=command.copy('image.mdl(\uE992,12)'))
        item(image=image.mdl(\uE993,default_icon_size) tip=['Volume1',tip.info] cmd=command.copy('image.mdl(\uE993,12)'))

        item(image=image.mdl(\uE994,default_icon_size) tip=['Volume2',tip.info] cmd=command.copy('image.mdl(\uE994,12)') col)
        item(image=image.mdl(\uE995,default_icon_size) tip=['Volume3',tip.info] cmd=command.copy('image.mdl(\uE995,12)'))
        item(image=image.mdl(\uE996,default_icon_size) tip=['BatteryUnknown',tip.info] cmd=command.copy('image.mdl(\uE996,12)'))
        item(image=image.mdl(\uE998,default_icon_size) tip=['WifiAttentionOverlay',tip.info] cmd=command.copy('image.mdl(\uE998,12)'))
        item(image=image.mdl(\uE99A,default_icon_size) tip=['Robot',tip.info] cmd=command.copy('image.mdl(\uE99A,12)'))
        item(image=image.mdl(\uE9A1,default_icon_size) tip=['TapAndSend',tip.info] cmd=command.copy('image.mdl(\uE9A1,12)'))
        item(image=image.mdl(\uE9A6,default_icon_size) tip=['FitPage',tip.info] cmd=command.copy('image.mdl(\uE9A6,12)'))
        item(image=image.mdl(\uE9A8,default_icon_size) tip=['PasswordKeyShow',tip.info] cmd=command.copy('image.mdl(\uE9A8,12)'))
        item(image=image.mdl(\uE9A9,default_icon_size) tip=['PasswordKeyHide',tip.info] cmd=command.copy('image.mdl(\uE9A9,12)'))
        item(image=image.mdl(\uE9AA,default_icon_size) tip=['BidiLtr',tip.info] cmd=command.copy('image.mdl(\uE9AA,12)'))
        item(image=image.mdl(\uE9AB,default_icon_size) tip=['BidiRtl',tip.info] cmd=command.copy('image.mdl(\uE9AB,12)'))
        item(image=image.mdl(\uE9AC,default_icon_size) tip=['ForwardSm',tip.info] cmd=command.copy('image.mdl(\uE9AC,12)'))
        item(image=image.mdl(\uE9AD,default_icon_size) tip=['CommaKey',tip.info] cmd=command.copy('image.mdl(\uE9AD,12)'))
        item(image=image.mdl(\uE9AE,default_icon_size) tip=['DashKey',tip.info] cmd=command.copy('image.mdl(\uE9AE,12)'))
        item(image=image.mdl(\uE9AF,default_icon_size) tip=['DullSoundKey',tip.info] cmd=command.copy('image.mdl(\uE9AF,12)'))
        item(image=image.mdl(\uE9B0,default_icon_size) tip=['HalfDullSound',tip.info] cmd=command.copy('image.mdl(\uE9B0,12)'))

        item(image=image.mdl(\uE9B1,default_icon_size) tip=['RightDoubleQuote',tip.info] cmd=command.copy('image.mdl(\uE9B1,12)') col)
        item(image=image.mdl(\uE9B2,default_icon_size) tip=['LeftDoubleQuote',tip.info] cmd=command.copy('image.mdl(\uE9B2,12)'))
        item(image=image.mdl(\uE9B3,default_icon_size) tip=['PuncKeyRightBottom',tip.info] cmd=command.copy('image.mdl(\uE9B3,12)'))
        item(image=image.mdl(\uE9B4,default_icon_size) tip=['PuncKey1',tip.info] cmd=command.copy('image.mdl(\uE9B4,12)'))
        item(image=image.mdl(\uE9B5,default_icon_size) tip=['PuncKey2',tip.info] cmd=command.copy('image.mdl(\uE9B5,12)'))
        item(image=image.mdl(\uE9B6,default_icon_size) tip=['PuncKey3',tip.info] cmd=command.copy('image.mdl(\uE9B6,12)'))
        item(image=image.mdl(\uE9B7,default_icon_size) tip=['PuncKey4',tip.info] cmd=command.copy('image.mdl(\uE9B7,12)'))
        item(image=image.mdl(\uE9B8,default_icon_size) tip=['PuncKey5',tip.info] cmd=command.copy('image.mdl(\uE9B8,12)'))
        item(image=image.mdl(\uE9B9,default_icon_size) tip=['PuncKey6',tip.info] cmd=command.copy('image.mdl(\uE9B9,12)'))
        item(image=image.mdl(\uE9BA,default_icon_size) tip=['PuncKey9',tip.info] cmd=command.copy('image.mdl(\uE9BA,12)'))
        item(image=image.mdl(\uE9BB,default_icon_size) tip=['PuncKey7',tip.info] cmd=command.copy('image.mdl(\uE9BB,12)'))
        item(image=image.mdl(\uE9BC,default_icon_size) tip=['PuncKey8',tip.info] cmd=command.copy('image.mdl(\uE9BC,12)'))
        item(image=image.mdl(\uE9CA,default_icon_size) tip=['Frigid',tip.info] cmd=command.copy('image.mdl(\uE9CA,12)'))
        item(image=image.mdl(\uE9CE,default_icon_size) tip=['Unknown',tip.info] cmd=command.copy('image.mdl(\uE9CE,12)'))
        item(image=image.mdl(\uE9D2,default_icon_size) tip=['AreaChart',tip.info] cmd=command.copy('image.mdl(\uE9D2,12)'))
        item(image=image.mdl(\uE9D5,default_icon_size) tip=['CheckList',tip.info] cmd=command.copy('image.mdl(\uE9D5,12)'))

        item(image=image.mdl(\uE9D9,default_icon_size) tip=['Diagnostic',tip.info] cmd=command.copy('image.mdl(\uE9D9,12)') col)
        item(image=image.mdl(\uE9E9,default_icon_size) tip=['Equalizer',tip.info] cmd=command.copy('image.mdl(\uE9E9,12)'))
        item(image=image.mdl(\uE9F3,default_icon_size) tip=['Process',tip.info] cmd=command.copy('image.mdl(\uE9F3,12)'))
        item(image=image.mdl(\uE9F5,default_icon_size) tip=['Processing',tip.info] cmd=command.copy('image.mdl(\uE9F5,12)'))
        item(image=image.mdl(\uE9F9,default_icon_size) tip=['ReportDocument',tip.info] cmd=command.copy('image.mdl(\uE9F9,12)'))
        item(image=image.mdl(\uEA0C,default_icon_size) tip=['VideoSolid',tip.info] cmd=command.copy('image.mdl(\uEA0C,12)'))
        item(image=image.mdl(\uEA0D,default_icon_size) tip=['MixedMediaBadge',tip.info] cmd=command.copy('image.mdl(\uEA0D,12)'))
        item(image=image.mdl(\uEA14,default_icon_size) tip=['DisconnectDisplay',tip.info] cmd=command.copy('image.mdl(\uEA14,12)'))
        item(image=image.mdl(\uEA18,default_icon_size) tip=['Shield',tip.info] cmd=command.copy('image.mdl(\uEA18,12)'))
        item(image=image.mdl(\uEA1F,default_icon_size) tip=['Info2',tip.info] cmd=command.copy('image.mdl(\uEA1F,12)'))
        item(image=image.mdl(\uEA21,default_icon_size) tip=['ActionCenterAsterisk',tip.info] cmd=command.copy('image.mdl(\uEA21,12)'))
        item(image=image.mdl(\uEA24,default_icon_size) tip=['Beta',tip.info] cmd=command.copy('image.mdl(\uEA24,12)'))
        item(image=image.mdl(\uEA35,default_icon_size) tip=['SaveCopy',tip.info] cmd=command.copy('image.mdl(\uEA35,12)'))
        item(image=image.mdl(\uEA37,default_icon_size) tip=['List',tip.info] cmd=command.copy('image.mdl(\uEA37,12)'))
        item(image=image.mdl(\uEA38,default_icon_size) tip=['Asterisk',tip.info] cmd=command.copy('image.mdl(\uEA38,12)'))
        item(image=image.mdl(\uEA39,default_icon_size) tip=['ErrorBadge',tip.info] cmd=command.copy('image.mdl(\uEA39,12)'))

        item(image=image.mdl(\uEA3A,default_icon_size) tip=['CircleRing',tip.info] cmd=command.copy('image.mdl(\uEA3A,12)') col)
        item(image=image.mdl(\uEA3B,default_icon_size) tip=['CircleFill',tip.info] cmd=command.copy('image.mdl(\uEA3B,12)'))
        item(image=image.mdl(\uEA3C,default_icon_size) tip=['MergeCall',tip.info] cmd=command.copy('image.mdl(\uEA3C,12)'))
        item(image=image.mdl(\uEA3D,default_icon_size) tip=['PrivateCall',tip.info] cmd=command.copy('image.mdl(\uEA3D,12)'))
        item(image=image.mdl(\uEA3F,default_icon_size) tip=['Record2',tip.info] cmd=command.copy('image.mdl(\uEA3F,12)'))
        item(image=image.mdl(\uEA40,default_icon_size) tip=['AllAppsMirrored',tip.info] cmd=command.copy('image.mdl(\uEA40,12)'))
        item(image=image.mdl(\uEA41,default_icon_size) tip=['BookmarksMirrored',tip.info] cmd=command.copy('image.mdl(\uEA41,12)'))
        item(image=image.mdl(\uEA42,default_icon_size) tip=['BulletedListMirrored',tip.info] cmd=command.copy('image.mdl(\uEA42,12)'))
        item(image=image.mdl(\uEA43,default_icon_size) tip=['CallForwardInternationalMirrored',tip.info] cmd=command.copy('image.mdl(\uEA43,12)'))
        item(image=image.mdl(\uEA44,default_icon_size) tip=['CallForwardRoamingMirrored',tip.info] cmd=command.copy('image.mdl(\uEA44,12)'))
        item(image=image.mdl(\uEA47,default_icon_size) tip=['ChromeBackMirrored',tip.info] cmd=command.copy('image.mdl(\uEA47,12)'))
        item(image=image.mdl(\uEA48,default_icon_size) tip=['ClearSelectionMirrored',tip.info] cmd=command.copy('image.mdl(\uEA48,12)'))
        item(image=image.mdl(\uEA49,default_icon_size) tip=['ClosePaneMirrored',tip.info] cmd=command.copy('image.mdl(\uEA49,12)'))
        item(image=image.mdl(\uEA4A,default_icon_size) tip=['ContactInfoMirrored',tip.info] cmd=command.copy('image.mdl(\uEA4A,12)'))
        item(image=image.mdl(\uEA4B,default_icon_size) tip=['DockRightMirrored',tip.info] cmd=command.copy('image.mdl(\uEA4B,12)'))
        item(image=image.mdl(\uEA4C,default_icon_size) tip=['DockLeftMirrored',tip.info] cmd=command.copy('image.mdl(\uEA4C,12)'))

        item(image=image.mdl(\uEA4E,default_icon_size) tip=['ExpandTileMirrored',tip.info] cmd=command.copy('image.mdl(\uEA4E,12)') col)
        item(image=image.mdl(\uEA4F,default_icon_size) tip=['GoMirrored',tip.info] cmd=command.copy('image.mdl(\uEA4F,12)'))
        item(image=image.mdl(\uEA50,default_icon_size) tip=['GripperResizeMirrored',tip.info] cmd=command.copy('image.mdl(\uEA50,12)'))
        item(image=image.mdl(\uEA51,default_icon_size) tip=['HelpMirrored',tip.info] cmd=command.copy('image.mdl(\uEA51,12)'))
        item(image=image.mdl(\uEA52,default_icon_size) tip=['ImportMirrored',tip.info] cmd=command.copy('image.mdl(\uEA52,12)'))
        item(image=image.mdl(\uEA53,default_icon_size) tip=['ImportAllMirrored',tip.info] cmd=command.copy('image.mdl(\uEA53,12)'))
        item(image=image.mdl(\uEA54,default_icon_size) tip=['LeaveChatMirrored',tip.info] cmd=command.copy('image.mdl(\uEA54,12)'))
        item(image=image.mdl(\uEA55,default_icon_size) tip=['ListMirrored',tip.info] cmd=command.copy('image.mdl(\uEA55,12)'))
        item(image=image.mdl(\uEA56,default_icon_size) tip=['MailForwardMirrored',tip.info] cmd=command.copy('image.mdl(\uEA56,12)'))
        item(image=image.mdl(\uEA57,default_icon_size) tip=['MailReplyMirrored',tip.info] cmd=command.copy('image.mdl(\uEA57,12)'))
        item(image=image.mdl(\uEA58,default_icon_size) tip=['MailReplyAllMirrored',tip.info] cmd=command.copy('image.mdl(\uEA58,12)'))
        item(image=image.mdl(\uEA5B,default_icon_size) tip=['OpenPaneMirrored',tip.info] cmd=command.copy('image.mdl(\uEA5B,12)'))
        item(image=image.mdl(\uEA5C,default_icon_size) tip=['OpenWithMirrored',tip.info] cmd=command.copy('image.mdl(\uEA5C,12)'))
        item(image=image.mdl(\uEA5E,default_icon_size) tip=['ParkingLocationMirrored',tip.info] cmd=command.copy('image.mdl(\uEA5E,12)'))
        item(image=image.mdl(\uEA5F,default_icon_size) tip=['ResizeMouseMediumMirrored',tip.info] cmd=command.copy('image.mdl(\uEA5F,12)'))
        item(image=image.mdl(\uEA60,default_icon_size) tip=['ResizeMouseSmallMirrored',tip.info] cmd=command.copy('image.mdl(\uEA60,12)'))

        item(image=image.mdl(\uEA61,default_icon_size) tip=['ResizeMouseTallMirrored',tip.info] cmd=command.copy('image.mdl(\uEA61,12)') col)
        item(image=image.mdl(\uEA62,default_icon_size) tip=['ResizeTouchNarrowerMirrored',tip.info] cmd=command.copy('image.mdl(\uEA62,12)'))
        item(image=image.mdl(\uEA63,default_icon_size) tip=['SendMirrored',tip.info] cmd=command.copy('image.mdl(\uEA63,12)'))
        item(image=image.mdl(\uEA64,default_icon_size) tip=['SendFillMirrored',tip.info] cmd=command.copy('image.mdl(\uEA64,12)'))
        item(image=image.mdl(\uEA65,default_icon_size) tip=['ShowResultsMirrored',tip.info] cmd=command.copy('image.mdl(\uEA65,12)'))
        item(image=image.mdl(\uEA69,default_icon_size) tip=['Media',tip.info] cmd=command.copy('image.mdl(\uEA69,12)'))
        item(image=image.mdl(\uEA6A,default_icon_size) tip=['SyncError',tip.info] cmd=command.copy('image.mdl(\uEA6A,12)'))
        item(image=image.mdl(\uEA6C,default_icon_size) tip=['Devices3',tip.info] cmd=command.copy('image.mdl(\uEA6C,12)'))
        item(image=image.mdl(\uEA79,default_icon_size) tip=['SlowMotionOn',tip.info] cmd=command.copy('image.mdl(\uEA79,12)'))
        item(image=image.mdl(\uEA80,default_icon_size) tip=['Lightbulb',tip.info] cmd=command.copy('image.mdl(\uEA80,12)'))
        item(image=image.mdl(\uEA81,default_icon_size) tip=['StatusCircle',tip.info] cmd=command.copy('image.mdl(\uEA81,12)'))
        item(image=image.mdl(\uEA82,default_icon_size) tip=['StatusTriangle',tip.info] cmd=command.copy('image.mdl(\uEA82,12)'))
        item(image=image.mdl(\uEA83,default_icon_size) tip=['StatusError',tip.info] cmd=command.copy('image.mdl(\uEA83,12)'))
        item(image=image.mdl(\uEA84,default_icon_size) tip=['StatusWarning',tip.info] cmd=command.copy('image.mdl(\uEA84,12)'))
        item(image=image.mdl(\uEA86,default_icon_size) tip=['Puzzle',tip.info] cmd=command.copy('image.mdl(\uEA86,12)'))
        item(image=image.mdl(\uEA89,default_icon_size) tip=['CalendarSolid',tip.info] cmd=command.copy('image.mdl(\uEA89,12)'))

        item(image=image.mdl(\uEA8A,default_icon_size) tip=['HomeSolid',tip.info] cmd=command.copy('image.mdl(\uEA8A,12)') col)
        item(image=image.mdl(\uEA8B,default_icon_size) tip=['ParkingLocationSolid',tip.info] cmd=command.copy('image.mdl(\uEA8B,12)'))
        item(image=image.mdl(\uEA8C,default_icon_size) tip=['ContactSolid',tip.info] cmd=command.copy('image.mdl(\uEA8C,12)'))
        item(image=image.mdl(\uEA8D,default_icon_size) tip=['ConstructionSolid',tip.info] cmd=command.copy('image.mdl(\uEA8D,12)'))
        item(image=image.mdl(\uEA8E,default_icon_size) tip=['AccidentSolid',tip.info] cmd=command.copy('image.mdl(\uEA8E,12)'))
        item(image=image.mdl(\uEA8F,default_icon_size) tip=['Ringer',tip.info] cmd=command.copy('image.mdl(\uEA8F,12)'))
        item(image=image.mdl(\uEA90,default_icon_size) tip=['PDF',tip.info] cmd=command.copy('image.mdl(\uEA90,12)'))
        item(image=image.mdl(\uEA91,default_icon_size) tip=['ThoughtBubble',tip.info] cmd=command.copy('image.mdl(\uEA91,12)'))
        item(image=image.mdl(\uEA92,default_icon_size) tip=['HeartBroken',tip.info] cmd=command.copy('image.mdl(\uEA92,12)'))
        item(image=image.mdl(\uEA93,default_icon_size) tip=['BatteryCharging10',tip.info] cmd=command.copy('image.mdl(\uEA93,12)'))
        item(image=image.mdl(\uEA94,default_icon_size) tip=['BatterySaver9',tip.info] cmd=command.copy('image.mdl(\uEA94,12)'))
        item(image=image.mdl(\uEA95,default_icon_size) tip=['BatterySaver10',tip.info] cmd=command.copy('image.mdl(\uEA95,12)'))
        item(image=image.mdl(\uEA97,default_icon_size) tip=['CallForwardingMirrored',tip.info] cmd=command.copy('image.mdl(\uEA97,12)'))
        item(image=image.mdl(\uEA98,default_icon_size) tip=['MultiSelectMirrored',tip.info] cmd=command.copy('image.mdl(\uEA98,12)'))
        item(image=image.mdl(\uEA99,default_icon_size) tip=['Broom',tip.info] cmd=command.copy('image.mdl(\uEA99,12)'))
        item(image=image.mdl(\uEAC2,default_icon_size) tip=['ForwardCall',tip.info] cmd=command.copy('image.mdl(\uEAC2,12)'))

        item(image=image.mdl(\uEADF,default_icon_size) tip=['Trackers',tip.info] cmd=command.copy('image.mdl(\uEADF,12)') col)
        item(image=image.mdl(\uEAFC,default_icon_size) tip=['Market',tip.info] cmd=command.copy('image.mdl(\uEAFC,12)'))
        item(image=image.mdl(\uEB05,default_icon_size) tip=['PieSingle',tip.info] cmd=command.copy('image.mdl(\uEB05,12)'))
        item(image=image.mdl(\uEB0F,default_icon_size) tip=['StockDown',tip.info] cmd=command.copy('image.mdl(\uEB0F,12)'))
        item(image=image.mdl(\uEB11,default_icon_size) tip=['StockUp',tip.info] cmd=command.copy('image.mdl(\uEB11,12)'))
        item(image=image.mdl(\uEB3C,default_icon_size) tip=['Design',tip.info] cmd=command.copy('image.mdl(\uEB3C,12)'))
        item(image=image.mdl(\uEB41,default_icon_size) tip=['Website',tip.info] cmd=command.copy('image.mdl(\uEB41,12)'))
        item(image=image.mdl(\uEB42,default_icon_size) tip=['Drop',tip.info] cmd=command.copy('image.mdl(\uEB42,12)'))
        item(image=image.mdl(\uEB44,default_icon_size) tip=['Radar',tip.info] cmd=command.copy('image.mdl(\uEB44,12)'))
        item(image=image.mdl(\uEB47,default_icon_size) tip=['BusSolid',tip.info] cmd=command.copy('image.mdl(\uEB47,12)'))
        item(image=image.mdl(\uEB48,default_icon_size) tip=['FerrySolid',tip.info] cmd=command.copy('image.mdl(\uEB48,12)'))
        item(image=image.mdl(\uEB49,default_icon_size) tip=['StartPointSolid',tip.info] cmd=command.copy('image.mdl(\uEB49,12)'))
        item(image=image.mdl(\uEB4A,default_icon_size) tip=['StopPointSolid',tip.info] cmd=command.copy('image.mdl(\uEB4A,12)'))
        item(image=image.mdl(\uEB4B,default_icon_size) tip=['EndPointSolid',tip.info] cmd=command.copy('image.mdl(\uEB4B,12)'))
        item(image=image.mdl(\uEB4C,default_icon_size) tip=['AirplaneSolid',tip.info] cmd=command.copy('image.mdl(\uEB4C,12)'))
        item(image=image.mdl(\uEB4D,default_icon_size) tip=['TrainSolid',tip.info] cmd=command.copy('image.mdl(\uEB4D,12)'))

        item(image=image.mdl(\uEB4E,default_icon_size) tip=['WorkSolid',tip.info] cmd=command.copy('image.mdl(\uEB4E,12)') col)
        item(image=image.mdl(\uEB4F,default_icon_size) tip=['ReminderFill',tip.info] cmd=command.copy('image.mdl(\uEB4F,12)'))
        item(image=image.mdl(\uEB50,default_icon_size) tip=['Reminder',tip.info] cmd=command.copy('image.mdl(\uEB50,12)'))
        item(image=image.mdl(\uEB51,default_icon_size) tip=['Heart',tip.info] cmd=command.copy('image.mdl(\uEB51,12)'))
        item(image=image.mdl(\uEB52,default_icon_size) tip=['HeartFill',tip.info] cmd=command.copy('image.mdl(\uEB52,12)'))
        item(image=image.mdl(\uEB55,default_icon_size) tip=['EthernetError',tip.info] cmd=command.copy('image.mdl(\uEB55,12)'))
        item(image=image.mdl(\uEB56,default_icon_size) tip=['EthernetWarning',tip.info] cmd=command.copy('image.mdl(\uEB56,12)'))
        item(image=image.mdl(\uEB57,default_icon_size) tip=['StatusConnecting1',tip.info] cmd=command.copy('image.mdl(\uEB57,12)'))
        item(image=image.mdl(\uEB58,default_icon_size) tip=['StatusConnecting2',tip.info] cmd=command.copy('image.mdl(\uEB58,12)'))
        item(image=image.mdl(\uEB59,default_icon_size) tip=['StatusUnsecure',tip.info] cmd=command.copy('image.mdl(\uEB59,12)'))
        item(image=image.mdl(\uEB5A,default_icon_size) tip=['WifiError0',tip.info] cmd=command.copy('image.mdl(\uEB5A,12)'))
        item(image=image.mdl(\uEB5B,default_icon_size) tip=['WifiError1',tip.info] cmd=command.copy('image.mdl(\uEB5B,12)'))
        item(image=image.mdl(\uEB5C,default_icon_size) tip=['WifiError2',tip.info] cmd=command.copy('image.mdl(\uEB5C,12)'))
        item(image=image.mdl(\uEB5D,default_icon_size) tip=['WifiError3',tip.info] cmd=command.copy('image.mdl(\uEB5D,12)'))
        item(image=image.mdl(\uEB5E,default_icon_size) tip=['WifiError4',tip.info] cmd=command.copy('image.mdl(\uEB5E,12)'))
        item(image=image.mdl(\uEB5F,default_icon_size) tip=['WifiWarning0',tip.info] cmd=command.copy('image.mdl(\uEB5F,12)'))

        item(image=image.mdl(\uEB60,default_icon_size) tip=['WifiWarning1',tip.info] cmd=command.copy('image.mdl(\uEB60,12)') col)
        item(image=image.mdl(\uEB61,default_icon_size) tip=['WifiWarning2',tip.info] cmd=command.copy('image.mdl(\uEB61,12)'))
        item(image=image.mdl(\uEB62,default_icon_size) tip=['WifiWarning3',tip.info] cmd=command.copy('image.mdl(\uEB62,12)'))
        item(image=image.mdl(\uEB63,default_icon_size) tip=['WifiWarning4',tip.info] cmd=command.copy('image.mdl(\uEB63,12)'))
        item(image=image.mdl(\uEB66,default_icon_size) tip=['Devices4',tip.info] cmd=command.copy('image.mdl(\uEB66,12)'))
        item(image=image.mdl(\uEB67,default_icon_size) tip=['NUIIris',tip.info] cmd=command.copy('image.mdl(\uEB67,12)'))
        item(image=image.mdl(\uEB68,default_icon_size) tip=['NUIFace',tip.info] cmd=command.copy('image.mdl(\uEB68,12)'))
        item(image=image.mdl(\uEB7E,default_icon_size) tip=['EditMirrored',tip.info] cmd=command.copy('image.mdl(\uEB7E,12)'))
        item(image=image.mdl(\uEB82,default_icon_size) tip=['NUIFPStartSlideHand',tip.info] cmd=command.copy('image.mdl(\uEB82,12)'))
        item(image=image.mdl(\uEB83,default_icon_size) tip=['NUIFPStartSlideAction',tip.info] cmd=command.copy('image.mdl(\uEB83,12)'))
        item(image=image.mdl(\uEB84,default_icon_size) tip=['NUIFPContinueSlideHand',tip.info] cmd=command.copy('image.mdl(\uEB84,12)'))
        item(image=image.mdl(\uEB85,default_icon_size) tip=['NUIFPContinueSlideAction',tip.info] cmd=command.copy('image.mdl(\uEB85,12)'))
        item(image=image.mdl(\uEB86,default_icon_size) tip=['NUIFPRollRightHand',tip.info] cmd=command.copy('image.mdl(\uEB86,12)'))
        item(image=image.mdl(\uEB87,default_icon_size) tip=['NUIFPRollRightHandAction',tip.info] cmd=command.copy('image.mdl(\uEB87,12)'))
        item(image=image.mdl(\uEB88,default_icon_size) tip=['NUIFPRollLeftHand',tip.info] cmd=command.copy('image.mdl(\uEB88,12)'))
        item(image=image.mdl(\uEB89,default_icon_size) tip=['NUIFPRollLeftAction',tip.info] cmd=command.copy('image.mdl(\uEB89,12)'))

        item(image=image.mdl(\uEB8A,default_icon_size) tip=['NUIFPPressHand',tip.info] cmd=command.copy('image.mdl(\uEB8A,12)') col)
        item(image=image.mdl(\uEB8B,default_icon_size) tip=['NUIFPPressAction',tip.info] cmd=command.copy('image.mdl(\uEB8B,12)'))
        item(image=image.mdl(\uEB8C,default_icon_size) tip=['NUIFPPressRepeatHand',tip.info] cmd=command.copy('image.mdl(\uEB8C,12)'))
        item(image=image.mdl(\uEB8D,default_icon_size) tip=['NUIFPPressRepeatAction',tip.info] cmd=command.copy('image.mdl(\uEB8D,12)'))
        item(image=image.mdl(\uEB90,default_icon_size) tip=['StatusErrorFull',tip.info] cmd=command.copy('image.mdl(\uEB90,12)'))
        item(image=image.mdl(\uEB91,default_icon_size) tip=['TaskViewExpanded',tip.info] cmd=command.copy('image.mdl(\uEB91,12)'))
        item(image=image.mdl(\uEB95,default_icon_size) tip=['Certificate',tip.info] cmd=command.copy('image.mdl(\uEB95,12)'))
        item(image=image.mdl(\uEB96,default_icon_size) tip=['BackSpaceQWERTYLg',tip.info] cmd=command.copy('image.mdl(\uEB96,12)'))
        item(image=image.mdl(\uEB97,default_icon_size) tip=['ReturnKeyLg',tip.info] cmd=command.copy('image.mdl(\uEB97,12)'))
        item(image=image.mdl(\uEB9D,default_icon_size) tip=['FastForward',tip.info] cmd=command.copy('image.mdl(\uEB9D,12)'))
        item(image=image.mdl(\uEB9E,default_icon_size) tip=['Rewind',tip.info] cmd=command.copy('image.mdl(\uEB9E,12)'))
        item(image=image.mdl(\uEB9F,default_icon_size) tip=['Photo2',tip.info] cmd=command.copy('image.mdl(\uEB9F,12)'))
        item(image=image.mdl(\uEBA0,default_icon_size) tip=['MobBattery0',tip.info] cmd=command.copy('image.mdl(\uEBA0,12)'))
        item(image=image.mdl(\uEBA1,default_icon_size) tip=['MobBattery1',tip.info] cmd=command.copy('image.mdl(\uEBA1,12)'))
        item(image=image.mdl(\uEBA2,default_icon_size) tip=['MobBattery2',tip.info] cmd=command.copy('image.mdl(\uEBA2,12)'))
        item(image=image.mdl(\uEBA3,default_icon_size) tip=['MobBattery3',tip.info] cmd=command.copy('image.mdl(\uEBA3,12)'))

        item(image=image.mdl(\uEBA4,default_icon_size) tip=['MobBattery4',tip.info] cmd=command.copy('image.mdl(\uEBA4,12)') col)
        item(image=image.mdl(\uEBA5,default_icon_size) tip=['MobBattery5',tip.info] cmd=command.copy('image.mdl(\uEBA5,12)'))
        item(image=image.mdl(\uEBA6,default_icon_size) tip=['MobBattery6',tip.info] cmd=command.copy('image.mdl(\uEBA6,12)'))
        item(image=image.mdl(\uEBA7,default_icon_size) tip=['MobBattery7',tip.info] cmd=command.copy('image.mdl(\uEBA7,12)'))
        item(image=image.mdl(\uEBA8,default_icon_size) tip=['MobBattery8',tip.info] cmd=command.copy('image.mdl(\uEBA8,12)'))
        item(image=image.mdl(\uEBA9,default_icon_size) tip=['MobBattery9',tip.info] cmd=command.copy('image.mdl(\uEBA9,12)'))
        item(image=image.mdl(\uEBAA,default_icon_size) tip=['MobBattery10',tip.info] cmd=command.copy('image.mdl(\uEBAA,12)'))
        item(image=image.mdl(\uEBAB,default_icon_size) tip=['MobBatteryCharging0',tip.info] cmd=command.copy('image.mdl(\uEBAB,12)'))
        item(image=image.mdl(\uEBAC,default_icon_size) tip=['MobBatteryCharging1',tip.info] cmd=command.copy('image.mdl(\uEBAC,12)'))
        item(image=image.mdl(\uEBAD,default_icon_size) tip=['MobBatteryCharging2',tip.info] cmd=command.copy('image.mdl(\uEBAD,12)'))
        item(image=image.mdl(\uEBAE,default_icon_size) tip=['MobBatteryCharging3',tip.info] cmd=command.copy('image.mdl(\uEBAE,12)'))
        item(image=image.mdl(\uEBAF,default_icon_size) tip=['MobBatteryCharging4',tip.info] cmd=command.copy('image.mdl(\uEBAF,12)'))
        item(image=image.mdl(\uEBB0,default_icon_size) tip=['MobBatteryCharging5',tip.info] cmd=command.copy('image.mdl(\uEBB0,12)'))
        item(image=image.mdl(\uEBB1,default_icon_size) tip=['MobBatteryCharging6',tip.info] cmd=command.copy('image.mdl(\uEBB1,12)'))
        item(image=image.mdl(\uEBB2,default_icon_size) tip=['MobBatteryCharging7',tip.info] cmd=command.copy('image.mdl(\uEBB2,12)'))
        item(image=image.mdl(\uEBB3,default_icon_size) tip=['MobBatteryCharging8',tip.info] cmd=command.copy('image.mdl(\uEBB3,12)'))

        item(image=image.mdl(\uEBB4,default_icon_size) tip=['MobBatteryCharging9',tip.info] cmd=command.copy('image.mdl(\uEBB4,12)') col)
        item(image=image.mdl(\uEBB5,default_icon_size) tip=['MobBatteryCharging10',tip.info] cmd=command.copy('image.mdl(\uEBB5,12)'))
        item(image=image.mdl(\uEBB6,default_icon_size) tip=['MobBatterySaver0',tip.info] cmd=command.copy('image.mdl(\uEBB6,12)'))
        item(image=image.mdl(\uEBB7,default_icon_size) tip=['MobBatterySaver1',tip.info] cmd=command.copy('image.mdl(\uEBB7,12)'))
        item(image=image.mdl(\uEBB8,default_icon_size) tip=['MobBatterySaver2',tip.info] cmd=command.copy('image.mdl(\uEBB8,12)'))
        item(image=image.mdl(\uEBB9,default_icon_size) tip=['MobBatterySaver3',tip.info] cmd=command.copy('image.mdl(\uEBB9,12)'))
        item(image=image.mdl(\uEBBA,default_icon_size) tip=['MobBatterySaver4',tip.info] cmd=command.copy('image.mdl(\uEBBA,12)'))
        item(image=image.mdl(\uEBBB,default_icon_size) tip=['MobBatterySaver5',tip.info] cmd=command.copy('image.mdl(\uEBBB,12)'))
        item(image=image.mdl(\uEBBC,default_icon_size) tip=['MobBatterySaver6',tip.info] cmd=command.copy('image.mdl(\uEBBC,12)'))
        item(image=image.mdl(\uEBBD,default_icon_size) tip=['MobBatterySaver7',tip.info] cmd=command.copy('image.mdl(\uEBBD,12)'))
        item(image=image.mdl(\uEBBE,default_icon_size) tip=['MobBatterySaver8',tip.info] cmd=command.copy('image.mdl(\uEBBE,12)'))
        item(image=image.mdl(\uEBBF,default_icon_size) tip=['MobBatterySaver9',tip.info] cmd=command.copy('image.mdl(\uEBBF,12)'))
        item(image=image.mdl(\uEBC0,default_icon_size) tip=['MobBatterySaver10',tip.info] cmd=command.copy('image.mdl(\uEBC0,12)'))
        item(image=image.mdl(\uEBC3,default_icon_size) tip=['DictionaryCloud',tip.info] cmd=command.copy('image.mdl(\uEBC3,12)'))
        item(image=image.mdl(\uEBC4,default_icon_size) tip=['ResetDrive',tip.info] cmd=command.copy('image.mdl(\uEBC4,12)'))
        item(image=image.mdl(\uEBC5,default_icon_size) tip=['VolumeBars',tip.info] cmd=command.copy('image.mdl(\uEBC5,12)'))

        item(image=image.mdl(\uEBC6,default_icon_size) tip=['Project',tip.info] cmd=command.copy('image.mdl(\uEBC6,12)') col)
        item(image=image.mdl(\uEBD2,default_icon_size) tip=['AdjustHologram',tip.info] cmd=command.copy('image.mdl(\uEBD2,12)'))
        item(image=image.mdl(\uEBD4,default_icon_size) tip=['WifiCallBars',tip.info] cmd=command.copy('image.mdl(\uEBD4,12)'))
        item(image=image.mdl(\uEBD5,default_icon_size) tip=['WifiCall0',tip.info] cmd=command.copy('image.mdl(\uEBD5,12)'))
        item(image=image.mdl(\uEBD6,default_icon_size) tip=['WifiCall1',tip.info] cmd=command.copy('image.mdl(\uEBD6,12)'))
        item(image=image.mdl(\uEBD7,default_icon_size) tip=['WifiCall2',tip.info] cmd=command.copy('image.mdl(\uEBD7,12)'))
        item(image=image.mdl(\uEBD8,default_icon_size) tip=['WifiCall3',tip.info] cmd=command.copy('image.mdl(\uEBD8,12)'))
        item(image=image.mdl(\uEBD9,default_icon_size) tip=['WifiCall4',tip.info] cmd=command.copy('image.mdl(\uEBD9,12)'))
        item(image=image.mdl(\uEBDA,default_icon_size) tip=['Family',tip.info] cmd=command.copy('image.mdl(\uEBDA,12)'))
        item(image=image.mdl(\uEBDB,default_icon_size) tip=['LockFeedback',tip.info] cmd=command.copy('image.mdl(\uEBDB,12)'))
        item(image=image.mdl(\uEBDE,default_icon_size) tip=['DeviceDiscovery',tip.info] cmd=command.copy('image.mdl(\uEBDE,12)'))
        item(image=image.mdl(\uEBE6,default_icon_size) tip=['WindDirection',tip.info] cmd=command.copy('image.mdl(\uEBE6,12)'))
        item(image=image.mdl(\uEBE7,default_icon_size) tip=['RightArrowKeyTime0',tip.info] cmd=command.copy('image.mdl(\uEBE7,12)'))
        item(image=image.mdl(\uEBE8,default_icon_size) tip=['Bug',tip.info] cmd=command.copy('image.mdl(\uEBE8,12)'))
        item(image=image.mdl(\uEBFC,default_icon_size) tip=['TabletMode',tip.info] cmd=command.copy('image.mdl(\uEBFC,12)'))
        item(image=image.mdl(\uEBFD,default_icon_size) tip=['StatusCircleLeft',tip.info] cmd=command.copy('image.mdl(\uEBFD,12)'))
    }

    menu(title='MDL2 #4')
    {
        item(image=image.mdl(\uEBFE,default_icon_size) tip=['StatusTriangleLeft',tip.info] cmd=command.copy('image.mdl(\uEBFE,12)'))
        item(image=image.mdl(\uEBFF,default_icon_size) tip=['StatusErrorLeft',tip.info] cmd=command.copy('image.mdl(\uEBFF,12)'))
        item(image=image.mdl(\uEC00,default_icon_size) tip=['StatusWarningLeft',tip.info] cmd=command.copy('image.mdl(\uEC00,12)'))
        item(image=image.mdl(\uEC02,default_icon_size) tip=['MobBatteryUnknown',tip.info] cmd=command.copy('image.mdl(\uEC02,12)'))
        item(image=image.mdl(\uEC05,default_icon_size) tip=['NetworkTower',tip.info] cmd=command.copy('image.mdl(\uEC05,12)'))
        item(image=image.mdl(\uEC06,default_icon_size) tip=['CityNext',tip.info] cmd=command.copy('image.mdl(\uEC06,12)'))
        item(image=image.mdl(\uEC07,default_icon_size) tip=['CityNext2',tip.info] cmd=command.copy('image.mdl(\uEC07,12)'))
        item(image=image.mdl(\uEC08,default_icon_size) tip=['Courthouse',tip.info] cmd=command.copy('image.mdl(\uEC08,12)'))
        item(image=image.mdl(\uEC09,default_icon_size) tip=['Groceries',tip.info] cmd=command.copy('image.mdl(\uEC09,12)'))
        item(image=image.mdl(\uEC0A,default_icon_size) tip=['Sustainable',tip.info] cmd=command.copy('image.mdl(\uEC0A,12)'))
        item(image=image.mdl(\uEC0B,default_icon_size) tip=['BuildingEnergy',tip.info] cmd=command.copy('image.mdl(\uEC0B,12)'))
        item(image=image.mdl(\uEC11,default_icon_size) tip=['ToggleFilled',tip.info] cmd=command.copy('image.mdl(\uEC11,12)'))
        item(image=image.mdl(\uEC12,default_icon_size) tip=['ToggleBorder',tip.info] cmd=command.copy('image.mdl(\uEC12,12)'))
        item(image=image.mdl(\uEC13,default_icon_size) tip=['SliderThumb',tip.info] cmd=command.copy('image.mdl(\uEC13,12)'))
        item(image=image.mdl(\uEC14,default_icon_size) tip=['ToggleThumb',tip.info] cmd=command.copy('image.mdl(\uEC14,12)'))
        item(image=image.mdl(\uEC15,default_icon_size) tip=['MiracastLogoSmall',tip.info] cmd=command.copy('image.mdl(\uEC15,12)'))

        item(image=image.mdl(\uEC16,default_icon_size) tip=['MiracastLogoLarge',tip.info] cmd=command.copy('image.mdl(\uEC16,12)') col)
        item(image=image.mdl(\uEC19,default_icon_size) tip=['PLAP',tip.info] cmd=command.copy('image.mdl(\uEC19,12)'))
        item(image=image.mdl(\uEC1B,default_icon_size) tip=['Badge',tip.info] cmd=command.copy('image.mdl(\uEC1B,12)'))
        item(image=image.mdl(\uEC1E,default_icon_size) tip=['SignalRoaming',tip.info] cmd=command.copy('image.mdl(\uEC1E,12)'))
        item(image=image.mdl(\uEC20,default_icon_size) tip=['MobileLocked',tip.info] cmd=command.copy('image.mdl(\uEC20,12)'))
        item(image=image.mdl(\uEC24,default_icon_size) tip=['InsiderHubApp',tip.info] cmd=command.copy('image.mdl(\uEC24,12)'))
        item(image=image.mdl(\uEC25,default_icon_size) tip=['PersonalFolder',tip.info] cmd=command.copy('image.mdl(\uEC25,12)'))
        item(image=image.mdl(\uEC26,default_icon_size) tip=['HomeGroup',tip.info] cmd=command.copy('image.mdl(\uEC26,12)'))
        item(image=image.mdl(\uEC27,default_icon_size) tip=['MyNetwork',tip.info] cmd=command.copy('image.mdl(\uEC27,12)'))
        item(image=image.mdl(\uEC31,default_icon_size) tip=['KeyboardFull',tip.info] cmd=command.copy('image.mdl(\uEC31,12)'))
        item(image=image.mdl(\uEC32,default_icon_size) tip=['Cafe',tip.info] cmd=command.copy('image.mdl(\uEC32,12)'))
        item(image=image.mdl(\uEC37,default_icon_size) tip=['MobSignal1',tip.info] cmd=command.copy('image.mdl(\uEC37,12)'))
        item(image=image.mdl(\uEC38,default_icon_size) tip=['MobSignal2',tip.info] cmd=command.copy('image.mdl(\uEC38,12)'))
        item(image=image.mdl(\uEC39,default_icon_size) tip=['MobSignal3',tip.info] cmd=command.copy('image.mdl(\uEC39,12)'))
        item(image=image.mdl(\uEC3A,default_icon_size) tip=['MobSignal4',tip.info] cmd=command.copy('image.mdl(\uEC3A,12)'))
        item(image=image.mdl(\uEC3B,default_icon_size) tip=['MobSignal5',tip.info] cmd=command.copy('image.mdl(\uEC3B,12)'))

        item(image=image.mdl(\uEC3C,default_icon_size) tip=['MobWifi1',tip.info] cmd=command.copy('image.mdl(\uEC3C,12)') col)
        item(image=image.mdl(\uEC3D,default_icon_size) tip=['MobWifi2',tip.info] cmd=command.copy('image.mdl(\uEC3D,12)'))
        item(image=image.mdl(\uEC3E,default_icon_size) tip=['MobWifi3',tip.info] cmd=command.copy('image.mdl(\uEC3E,12)'))
        item(image=image.mdl(\uEC3F,default_icon_size) tip=['MobWifi4',tip.info] cmd=command.copy('image.mdl(\uEC3F,12)'))
        item(image=image.mdl(\uEC40,default_icon_size) tip=['MobAirplane',tip.info] cmd=command.copy('image.mdl(\uEC40,12)'))
        item(image=image.mdl(\uEC41,default_icon_size) tip=['MobBluetooth',tip.info] cmd=command.copy('image.mdl(\uEC41,12)'))
        item(image=image.mdl(\uEC42,default_icon_size) tip=['MobActionCenter',tip.info] cmd=command.copy('image.mdl(\uEC42,12)'))
        item(image=image.mdl(\uEC43,default_icon_size) tip=['MobLocation',tip.info] cmd=command.copy('image.mdl(\uEC43,12)'))
        item(image=image.mdl(\uEC44,default_icon_size) tip=['MobWifiHotspot',tip.info] cmd=command.copy('image.mdl(\uEC44,12)'))
        item(image=image.mdl(\uEC45,default_icon_size) tip=['LanguageJpn',tip.info] cmd=command.copy('image.mdl(\uEC45,12)'))
        item(image=image.mdl(\uEC46,default_icon_size) tip=['MobQuietHours',tip.info] cmd=command.copy('image.mdl(\uEC46,12)'))
        item(image=image.mdl(\uEC47,default_icon_size) tip=['MobDrivingMode',tip.info] cmd=command.copy('image.mdl(\uEC47,12)'))
        item(image=image.mdl(\uEC48,default_icon_size) tip=['SpeedOff',tip.info] cmd=command.copy('image.mdl(\uEC48,12)'))
        item(image=image.mdl(\uEC49,default_icon_size) tip=['SpeedMedium',tip.info] cmd=command.copy('image.mdl(\uEC49,12)'))
        item(image=image.mdl(\uEC4A,default_icon_size) tip=['SpeedHigh',tip.info] cmd=command.copy('image.mdl(\uEC4A,12)'))
        item(image=image.mdl(\uEC4E,default_icon_size) tip=['ThisPC',tip.info] cmd=command.copy('image.mdl(\uEC4E,12)'))

        item(image=image.mdl(\uEC4F,default_icon_size) tip=['MusicNote',tip.info] cmd=command.copy('image.mdl(\uEC4F,12)') col)
        item(image=image.mdl(\uEC50,default_icon_size) tip=['FileExplorer',tip.info] cmd=command.copy('image.mdl(\uEC50,12)'))
        item(image=image.mdl(\uEC51,default_icon_size) tip=['FileExplorerApp',tip.info] cmd=command.copy('image.mdl(\uEC51,12)'))
        item(image=image.mdl(\uEC52,default_icon_size) tip=['LeftArrowKeyTime0',tip.info] cmd=command.copy('image.mdl(\uEC52,12)'))
        item(image=image.mdl(\uEC54,default_icon_size) tip=['MicOff',tip.info] cmd=command.copy('image.mdl(\uEC54,12)'))
        item(image=image.mdl(\uEC55,default_icon_size) tip=['MicSleep',tip.info] cmd=command.copy('image.mdl(\uEC55,12)'))
        item(image=image.mdl(\uEC56,default_icon_size) tip=['MicError',tip.info] cmd=command.copy('image.mdl(\uEC56,12)'))
        item(image=image.mdl(\uEC57,default_icon_size) tip=['PlaybackRate1x',tip.info] cmd=command.copy('image.mdl(\uEC57,12)'))
        item(image=image.mdl(\uEC58,default_icon_size) tip=['PlaybackRateOther',tip.info] cmd=command.copy('image.mdl(\uEC58,12)'))
        item(image=image.mdl(\uEC59,default_icon_size) tip=['CashDrawer',tip.info] cmd=command.copy('image.mdl(\uEC59,12)'))
        item(image=image.mdl(\uEC5A,default_icon_size) tip=['BarcodeScanner',tip.info] cmd=command.copy('image.mdl(\uEC5A,12)'))
        item(image=image.mdl(\uEC5B,default_icon_size) tip=['ReceiptPrinter',tip.info] cmd=command.copy('image.mdl(\uEC5B,12)'))
        item(image=image.mdl(\uEC5C,default_icon_size) tip=['MagStripeReader',tip.info] cmd=command.copy('image.mdl(\uEC5C,12)'))
        item(image=image.mdl(\uEC61,default_icon_size) tip=['CompletedSolid',tip.info] cmd=command.copy('image.mdl(\uEC61,12)'))
        item(image=image.mdl(\uEC64,default_icon_size) tip=['CompanionApp',tip.info] cmd=command.copy('image.mdl(\uEC64,12)'))
        item(image=image.mdl(\uEC6C,default_icon_size) tip=['Favicon2',tip.info] cmd=command.copy('image.mdl(\uEC6C,12)'))

        item(image=image.mdl(\uEC6D,default_icon_size) tip=['SwipeRevealArt',tip.info] cmd=command.copy('image.mdl(\uEC6D,12)') col)
        item(image=image.mdl(\uEC71,default_icon_size) tip=['MicOn',tip.info] cmd=command.copy('image.mdl(\uEC71,12)'))
        item(image=image.mdl(\uEC72,default_icon_size) tip=['MicClipping',tip.info] cmd=command.copy('image.mdl(\uEC72,12)'))
        item(image=image.mdl(\uEC74,default_icon_size) tip=['TabletSelected',tip.info] cmd=command.copy('image.mdl(\uEC74,12)'))
        item(image=image.mdl(\uEC75,default_icon_size) tip=['MobileSelected',tip.info] cmd=command.copy('image.mdl(\uEC75,12)'))
        item(image=image.mdl(\uEC76,default_icon_size) tip=['LaptopSelected',tip.info] cmd=command.copy('image.mdl(\uEC76,12)'))
        item(image=image.mdl(\uEC77,default_icon_size) tip=['TVMonitorSelected',tip.info] cmd=command.copy('image.mdl(\uEC77,12)'))
        item(image=image.mdl(\uEC7A,default_icon_size) tip=['DeveloperTools',tip.info] cmd=command.copy('image.mdl(\uEC7A,12)'))
        item(image=image.mdl(\uEC7E,default_icon_size) tip=['MobCallForwarding',tip.info] cmd=command.copy('image.mdl(\uEC7E,12)'))
        item(image=image.mdl(\uEC7F,default_icon_size) tip=['MobCallForwardingMirrored',tip.info] cmd=command.copy('image.mdl(\uEC7F,12)'))
        item(image=image.mdl(\uEC80,default_icon_size) tip=['BodyCam',tip.info] cmd=command.copy('image.mdl(\uEC80,12)'))
        item(image=image.mdl(\uEC81,default_icon_size) tip=['PoliceCar',tip.info] cmd=command.copy('image.mdl(\uEC81,12)'))
        item(image=image.mdl(\uEC87,default_icon_size) tip=['Draw',tip.info] cmd=command.copy('image.mdl(\uEC87,12)'))
        item(image=image.mdl(\uEC88,default_icon_size) tip=['DrawSolid',tip.info] cmd=command.copy('image.mdl(\uEC88,12)'))
        item(image=image.mdl(\uEC8A,default_icon_size) tip=['LowerBrightness',tip.info] cmd=command.copy('image.mdl(\uEC8A,12)'))
        item(image=image.mdl(\uEC8F,default_icon_size) tip=['ScrollUpDown',tip.info] cmd=command.copy('image.mdl(\uEC8F,12)'))

        item(image=image.mdl(\uEC92,default_icon_size) tip=['DateTime',tip.info] cmd=command.copy('image.mdl(\uEC92,12)') col)
        item(image=image.mdl(\uECA5,default_icon_size) tip=['Tiles',tip.info] cmd=command.copy('image.mdl(\uECA5,12)'))
        item(image=image.mdl(\uECA7,default_icon_size) tip=['PartyLeader',tip.info] cmd=command.copy('image.mdl(\uECA7,12)'))
        item(image=image.mdl(\uECAA,default_icon_size) tip=['AppIconDefault',tip.info] cmd=command.copy('image.mdl(\uECAA,12)'))
        item(image=image.mdl(\uECAD,default_icon_size) tip=['Calories',tip.info] cmd=command.copy('image.mdl(\uECAD,12)'))
        item(image=image.mdl(\uECB9,default_icon_size) tip=['BandBattery0',tip.info] cmd=command.copy('image.mdl(\uECB9,12)'))
        item(image=image.mdl(\uECBA,default_icon_size) tip=['BandBattery1',tip.info] cmd=command.copy('image.mdl(\uECBA,12)'))
        item(image=image.mdl(\uECBB,default_icon_size) tip=['BandBattery2',tip.info] cmd=command.copy('image.mdl(\uECBB,12)'))
        item(image=image.mdl(\uECBC,default_icon_size) tip=['BandBattery3',tip.info] cmd=command.copy('image.mdl(\uECBC,12)'))
        item(image=image.mdl(\uECBD,default_icon_size) tip=['BandBattery4',tip.info] cmd=command.copy('image.mdl(\uECBD,12)'))
        item(image=image.mdl(\uECBE,default_icon_size) tip=['BandBattery5',tip.info] cmd=command.copy('image.mdl(\uECBE,12)'))
        item(image=image.mdl(\uECBF,default_icon_size) tip=['BandBattery6',tip.info] cmd=command.copy('image.mdl(\uECBF,12)'))
        item(image=image.mdl(\uECC4,default_icon_size) tip=['AddSurfaceHub',tip.info] cmd=command.copy('image.mdl(\uECC4,12)'))
        item(image=image.mdl(\uECC5,default_icon_size) tip=['DevUpdate',tip.info] cmd=command.copy('image.mdl(\uECC5,12)'))
        item(image=image.mdl(\uECC6,default_icon_size) tip=['Unit',tip.info] cmd=command.copy('image.mdl(\uECC6,12)'))
        item(image=image.mdl(\uECC8,default_icon_size) tip=['AddTo',tip.info] cmd=command.copy('image.mdl(\uECC8,12)'))

        item(image=image.mdl(\uECC9,default_icon_size) tip=['RemoveFrom',tip.info] cmd=command.copy('image.mdl(\uECC9,12)') col)
        item(image=image.mdl(\uECCA,default_icon_size) tip=['RadioBtnOff',tip.info] cmd=command.copy('image.mdl(\uECCA,12)'))
        item(image=image.mdl(\uECCB,default_icon_size) tip=['RadioBtnOn',tip.info] cmd=command.copy('image.mdl(\uECCB,12)'))
        item(image=image.mdl(\uECCC,default_icon_size) tip=['RadioBullet2',tip.info] cmd=command.copy('image.mdl(\uECCC,12)'))
        item(image=image.mdl(\uECCD,default_icon_size) tip=['ExploreContent',tip.info] cmd=command.copy('image.mdl(\uECCD,12)'))
        item(image=image.mdl(\uECE4,default_icon_size) tip=['Blocked2',tip.info] cmd=command.copy('image.mdl(\uECE4,12)'))
        item(image=image.mdl(\uECE7,default_icon_size) tip=['ScrollMode',tip.info] cmd=command.copy('image.mdl(\uECE7,12)'))
        item(image=image.mdl(\uECE8,default_icon_size) tip=['ZoomMode',tip.info] cmd=command.copy('image.mdl(\uECE8,12)'))
        item(image=image.mdl(\uECE9,default_icon_size) tip=['PanMode',tip.info] cmd=command.copy('image.mdl(\uECE9,12)'))
        item(image=image.mdl(\uECF0,default_icon_size) tip=['WiredUSB',tip.info] cmd=command.copy('image.mdl(\uECF0,12)'))
        item(image=image.mdl(\uECF1,default_icon_size) tip=['WirelessUSB',tip.info] cmd=command.copy('image.mdl(\uECF1,12)'))
        item(image=image.mdl(\uECF3,default_icon_size) tip=['USBSafeConnect',tip.info] cmd=command.copy('image.mdl(\uECF3,12)'))
        item(image=image.mdl(\uED0C,default_icon_size) tip=['ActionCenterNotificationMirrored',tip.info] cmd=command.copy('image.mdl(\uED0C,12)'))
        item(image=image.mdl(\uED0D,default_icon_size) tip=['ActionCenterMirrored',tip.info] cmd=command.copy('image.mdl(\uED0D,12)'))
        item(image=image.mdl(\uED0E,default_icon_size) tip=['SubscriptionAdd',tip.info] cmd=command.copy('image.mdl(\uED0E,12)'))
        item(image=image.mdl(\uED10,default_icon_size) tip=['ResetDevice',tip.info] cmd=command.copy('image.mdl(\uED10,12)'))

        item(image=image.mdl(\uED11,default_icon_size) tip=['SubscriptionAddMirrored',tip.info] cmd=command.copy('image.mdl(\uED11,12)') col)
        item(image=image.mdl(\uED14,default_icon_size) tip=['QRCode',tip.info] cmd=command.copy('image.mdl(\uED14,12)'))
        item(image=image.mdl(\uED15,default_icon_size) tip=['Feedback',tip.info] cmd=command.copy('image.mdl(\uED15,12)'))
        item(image=image.mdl(\uED1E,default_icon_size) tip=['Subtitles',tip.info] cmd=command.copy('image.mdl(\uED1E,12)'))
        item(image=image.mdl(\uED1F,default_icon_size) tip=['SubtitlesAudio',tip.info] cmd=command.copy('image.mdl(\uED1F,12)'))
        item(image=image.mdl(\uED25,default_icon_size) tip=['OpenFolderHorizontal',tip.info] cmd=command.copy('image.mdl(\uED25,12)'))
        item(image=image.mdl(\uED28,default_icon_size) tip=['CalendarMirrored',tip.info] cmd=command.copy('image.mdl(\uED28,12)'))
        item(image=image.mdl(\uED2A,default_icon_size) tip=['MobeSIM',tip.info] cmd=command.copy('image.mdl(\uED2A,12)'))
        item(image=image.mdl(\uED2B,default_icon_size) tip=['MobeSIMNoProfile',tip.info] cmd=command.copy('image.mdl(\uED2B,12)'))
        item(image=image.mdl(\uED2C,default_icon_size) tip=['MobeSIMLocked',tip.info] cmd=command.copy('image.mdl(\uED2C,12)'))
        item(image=image.mdl(\uED2D,default_icon_size) tip=['MobeSIMBusy',tip.info] cmd=command.copy('image.mdl(\uED2D,12)'))
        item(image=image.mdl(\uED2E,default_icon_size) tip=['SignalError',tip.info] cmd=command.copy('image.mdl(\uED2E,12)'))
        item(image=image.mdl(\uED2F,default_icon_size) tip=['StreamingEnterprise',tip.info] cmd=command.copy('image.mdl(\uED2F,12)'))
        item(image=image.mdl(\uED30,default_icon_size) tip=['Headphone0',tip.info] cmd=command.copy('image.mdl(\uED30,12)'))
        item(image=image.mdl(\uED31,default_icon_size) tip=['Headphone1',tip.info] cmd=command.copy('image.mdl(\uED31,12)'))
        item(image=image.mdl(\uED32,default_icon_size) tip=['Headphone2',tip.info] cmd=command.copy('image.mdl(\uED32,12)'))

        item(image=image.mdl(\uED33,default_icon_size) tip=['Headphone3',tip.info] cmd=command.copy('image.mdl(\uED33,12)') col)
        item(image=image.mdl(\uED35,default_icon_size) tip=['Apps',tip.info] cmd=command.copy('image.mdl(\uED35,12)'))
        item(image=image.mdl(\uED39,default_icon_size) tip=['KeyboardBrightness',tip.info] cmd=command.copy('image.mdl(\uED39,12)'))
        item(image=image.mdl(\uED3A,default_icon_size) tip=['KeyboardLowerBrightness',tip.info] cmd=command.copy('image.mdl(\uED3A,12)'))
        item(image=image.mdl(\uED3C,default_icon_size) tip=['SkipBack10',tip.info] cmd=command.copy('image.mdl(\uED3C,12)'))
        item(image=image.mdl(\uED3D,default_icon_size) tip=['SkipForward30',tip.info] cmd=command.copy('image.mdl(\uED3D,12)'))
        item(image=image.mdl(\uED41,default_icon_size) tip=['TreeFolderFolder',tip.info] cmd=command.copy('image.mdl(\uED41,12)'))
        item(image=image.mdl(\uED42,default_icon_size) tip=['TreeFolderFolderFill',tip.info] cmd=command.copy('image.mdl(\uED42,12)'))
        item(image=image.mdl(\uED43,default_icon_size) tip=['TreeFolderFolderOpen',tip.info] cmd=command.copy('image.mdl(\uED43,12)'))
        item(image=image.mdl(\uED44,default_icon_size) tip=['TreeFolderFolderOpenFill',tip.info] cmd=command.copy('image.mdl(\uED44,12)'))
        item(image=image.mdl(\uED47,default_icon_size) tip=['MultimediaDMP',tip.info] cmd=command.copy('image.mdl(\uED47,12)'))
        item(image=image.mdl(\uED4C,default_icon_size) tip=['KeyboardOneHanded',tip.info] cmd=command.copy('image.mdl(\uED4C,12)'))
        item(image=image.mdl(\uED4D,default_icon_size) tip=['Narrator',tip.info] cmd=command.copy('image.mdl(\uED4D,12)'))
        item(image=image.mdl(\uED53,default_icon_size) tip=['EmojiTabPeople',tip.info] cmd=command.copy('image.mdl(\uED53,12)'))
        item(image=image.mdl(\uED54,default_icon_size) tip=['EmojiTabSmilesAnimals',tip.info] cmd=command.copy('image.mdl(\uED54,12)'))
        item(image=image.mdl(\uED55,default_icon_size) tip=['EmojiTabCelebrationObjects',tip.info] cmd=command.copy('image.mdl(\uED55,12)'))

        item(image=image.mdl(\uED56,default_icon_size) tip=['EmojiTabFoodPlants',tip.info] cmd=command.copy('image.mdl(\uED56,12)') col)
        item(image=image.mdl(\uED57,default_icon_size) tip=['EmojiTabTransitPlaces',tip.info] cmd=command.copy('image.mdl(\uED57,12)'))
        item(image=image.mdl(\uED58,default_icon_size) tip=['EmojiTabSymbols',tip.info] cmd=command.copy('image.mdl(\uED58,12)'))
        item(image=image.mdl(\uED59,default_icon_size) tip=['EmojiTabTextSmiles',tip.info] cmd=command.copy('image.mdl(\uED59,12)'))
        item(image=image.mdl(\uED5A,default_icon_size) tip=['EmojiTabFavorites',tip.info] cmd=command.copy('image.mdl(\uED5A,12)'))
        item(image=image.mdl(\uED5B,default_icon_size) tip=['EmojiSwatch',tip.info] cmd=command.copy('image.mdl(\uED5B,12)'))
        item(image=image.mdl(\uED5C,default_icon_size) tip=['ConnectApp',tip.info] cmd=command.copy('image.mdl(\uED5C,12)'))
        item(image=image.mdl(\uED5D,default_icon_size) tip=['CompanionDeviceFramework',tip.info] cmd=command.copy('image.mdl(\uED5D,12)'))
        item(image=image.mdl(\uED5E,default_icon_size) tip=['Ruler',tip.info] cmd=command.copy('image.mdl(\uED5E,12)'))
        item(image=image.mdl(\uED5F,default_icon_size) tip=['FingerInking',tip.info] cmd=command.copy('image.mdl(\uED5F,12)'))
        item(image=image.mdl(\uED60,default_icon_size) tip=['StrokeErase',tip.info] cmd=command.copy('image.mdl(\uED60,12)'))
        item(image=image.mdl(\uED61,default_icon_size) tip=['PointErase',tip.info] cmd=command.copy('image.mdl(\uED61,12)'))
        item(image=image.mdl(\uED62,default_icon_size) tip=['ClearAllInk',tip.info] cmd=command.copy('image.mdl(\uED62,12)'))
        item(image=image.mdl(\uED63,default_icon_size) tip=['Pencil',tip.info] cmd=command.copy('image.mdl(\uED63,12)'))
        item(image=image.mdl(\uED64,default_icon_size) tip=['Marker',tip.info] cmd=command.copy('image.mdl(\uED64,12)'))
        item(image=image.mdl(\uED65,default_icon_size) tip=['InkingCaret',tip.info] cmd=command.copy('image.mdl(\uED65,12)'))

        item(image=image.mdl(\uED66,default_icon_size) tip=['InkingColorOutline',tip.info] cmd=command.copy('image.mdl(\uED66,12)') col)
        item(image=image.mdl(\uED67,default_icon_size) tip=['InkingColorFill',tip.info] cmd=command.copy('image.mdl(\uED67,12)'))
        item(image=image.mdl(\uEDA2,default_icon_size) tip=['HardDrive',tip.info] cmd=command.copy('image.mdl(\uEDA2,12)'))
        item(image=image.mdl(\uEDA3,default_icon_size) tip=['NetworkAdapter',tip.info] cmd=command.copy('image.mdl(\uEDA3,12)'))
        item(image=image.mdl(\uEDA4,default_icon_size) tip=['Touchscreen',tip.info] cmd=command.copy('image.mdl(\uEDA4,12)'))
        item(image=image.mdl(\uEDA5,default_icon_size) tip=['NetworkPrinter',tip.info] cmd=command.copy('image.mdl(\uEDA5,12)'))
        item(image=image.mdl(\uEDA6,default_icon_size) tip=['CloudPrinter',tip.info] cmd=command.copy('image.mdl(\uEDA6,12)'))
        item(image=image.mdl(\uEDA7,default_icon_size) tip=['KeyboardShortcut',tip.info] cmd=command.copy('image.mdl(\uEDA7,12)'))
        item(image=image.mdl(\uEDA8,default_icon_size) tip=['BrushSize',tip.info] cmd=command.copy('image.mdl(\uEDA8,12)'))
        item(image=image.mdl(\uEDA9,default_icon_size) tip=['NarratorForward',tip.info] cmd=command.copy('image.mdl(\uEDA9,12)'))
        item(image=image.mdl(\uEDAA,default_icon_size) tip=['NarratorForwardMirrored',tip.info] cmd=command.copy('image.mdl(\uEDAA,12)'))
        item(image=image.mdl(\uEDAB,default_icon_size) tip=['SyncBadge12',tip.info] cmd=command.copy('image.mdl(\uEDAB,12)'))
        item(image=image.mdl(\uEDAC,default_icon_size) tip=['RingerBadge12',tip.info] cmd=command.copy('image.mdl(\uEDAC,12)'))
        item(image=image.mdl(\uEDAD,default_icon_size) tip=['AsteriskBadge12',tip.info] cmd=command.copy('image.mdl(\uEDAD,12)'))
        item(image=image.mdl(\uEDAE,default_icon_size) tip=['ErrorBadge12',tip.info] cmd=command.copy('image.mdl(\uEDAE,12)'))
        item(image=image.mdl(\uEDAF,default_icon_size) tip=['CircleRingBadge12',tip.info] cmd=command.copy('image.mdl(\uEDAF,12)'))

        item(image=image.mdl(\uEDB0,default_icon_size) tip=['CircleFillBadge12',tip.info] cmd=command.copy('image.mdl(\uEDB0,12)') col)
        item(image=image.mdl(\uEDB1,default_icon_size) tip=['ImportantBadge12',tip.info] cmd=command.copy('image.mdl(\uEDB1,12)'))
        item(image=image.mdl(\uEDB3,default_icon_size) tip=['MailBadge12',tip.info] cmd=command.copy('image.mdl(\uEDB3,12)'))
        item(image=image.mdl(\uEDB4,default_icon_size) tip=['PauseBadge12',tip.info] cmd=command.copy('image.mdl(\uEDB4,12)'))
        item(image=image.mdl(\uEDB5,default_icon_size) tip=['PlayBadge12',tip.info] cmd=command.copy('image.mdl(\uEDB5,12)'))
        item(image=image.mdl(\uEDC6,default_icon_size) tip=['PenWorkspace',tip.info] cmd=command.copy('image.mdl(\uEDC6,12)'))
        item(image=image.mdl(\uEDD6,default_icon_size) tip=['CaretRight8',tip.info] cmd=command.copy('image.mdl(\uEDD6,12)'))
        item(image=image.mdl(\uEDD9,default_icon_size) tip=['CaretLeftSolid8',tip.info] cmd=command.copy('image.mdl(\uEDD9,12)'))
        item(image=image.mdl(\uEDDA,default_icon_size) tip=['CaretRightSolid8',tip.info] cmd=command.copy('image.mdl(\uEDDA,12)'))
        item(image=image.mdl(\uEDDB,default_icon_size) tip=['CaretUpSolid8',tip.info] cmd=command.copy('image.mdl(\uEDDB,12)'))
        item(image=image.mdl(\uEDDC,default_icon_size) tip=['CaretDownSolid8',tip.info] cmd=command.copy('image.mdl(\uEDDC,12)'))
        item(image=image.mdl(\uEDE0,default_icon_size) tip=['Strikethrough',tip.info] cmd=command.copy('image.mdl(\uEDE0,12)'))
        item(image=image.mdl(\uEDE1,default_icon_size) tip=['Export',tip.info] cmd=command.copy('image.mdl(\uEDE1,12)'))
        item(image=image.mdl(\uEDE2,default_icon_size) tip=['ExportMirrored',tip.info] cmd=command.copy('image.mdl(\uEDE2,12)'))
        item(image=image.mdl(\uEDE3,default_icon_size) tip=['ButtonMenu',tip.info] cmd=command.copy('image.mdl(\uEDE3,12)'))
        item(image=image.mdl(\uEDE4,default_icon_size) tip=['CloudSearch',tip.info] cmd=command.copy('image.mdl(\uEDE4,12)'))

        item(image=image.mdl(\uEDE5,default_icon_size) tip=['PinyinIMELogo',tip.info] cmd=command.copy('image.mdl(\uEDE5,12)') col)
        item(image=image.mdl(\uEDFB,default_icon_size) tip=['CalligraphyPen',tip.info] cmd=command.copy('image.mdl(\uEDFB,12)'))
        item(image=image.mdl(\uEE35,default_icon_size) tip=['ReplyMirrored',tip.info] cmd=command.copy('image.mdl(\uEE35,12)'))
        item(image=image.mdl(\uEE3F,default_icon_size) tip=['LockscreenDesktop',tip.info] cmd=command.copy('image.mdl(\uEE3F,12)'))
        item(image=image.mdl(\uEE40,default_icon_size) tip=['TaskViewSettings',tip.info] cmd=command.copy('image.mdl(\uEE40,12)'))
        item(image=image.mdl(\uEE47,default_icon_size) tip=['MiniExpand2Mirrored',tip.info] cmd=command.copy('image.mdl(\uEE47,12)'))
        item(image=image.mdl(\uEE49,default_icon_size) tip=['MiniContract2Mirrored',tip.info] cmd=command.copy('image.mdl(\uEE49,12)'))
        item(image=image.mdl(\uEE4A,default_icon_size) tip=['Play36',tip.info] cmd=command.copy('image.mdl(\uEE4A,12)'))
        item(image=image.mdl(\uEE56,default_icon_size) tip=['PenPalette',tip.info] cmd=command.copy('image.mdl(\uEE56,12)'))
        item(image=image.mdl(\uEE57,default_icon_size) tip=['GuestUser',tip.info] cmd=command.copy('image.mdl(\uEE57,12)'))
        item(image=image.mdl(\uEE63,default_icon_size) tip=['SettingsBattery',tip.info] cmd=command.copy('image.mdl(\uEE63,12)'))
        item(image=image.mdl(\uEE64,default_icon_size) tip=['TaskbarPhone',tip.info] cmd=command.copy('image.mdl(\uEE64,12)'))
        item(image=image.mdl(\uEE65,default_icon_size) tip=['LockScreenGlance',tip.info] cmd=command.copy('image.mdl(\uEE65,12)'))
        item(image=image.mdl(\uEE6F,default_icon_size) tip=['GenericScan',tip.info] cmd=command.copy('image.mdl(\uEE6F,12)'))
        item(image=image.mdl(\uEE71,default_icon_size) tip=['ImageExport',tip.info] cmd=command.copy('image.mdl(\uEE71,12)'))
        item(image=image.mdl(\uEE77,default_icon_size) tip=['WifiEthernet',tip.info] cmd=command.copy('image.mdl(\uEE77,12)'))

        item(image=image.mdl(\uEE79,default_icon_size) tip=['ActionCenterQuiet',tip.info] cmd=command.copy('image.mdl(\uEE79,12)') col)
        item(image=image.mdl(\uEE7A,default_icon_size) tip=['ActionCenterQuietNotification',tip.info] cmd=command.copy('image.mdl(\uEE7A,12)'))
        item(image=image.mdl(\uEE92,default_icon_size) tip=['TrackersMirrored',tip.info] cmd=command.copy('image.mdl(\uEE92,12)'))
        item(image=image.mdl(\uEE93,default_icon_size) tip=['DateTimeMirrored',tip.info] cmd=command.copy('image.mdl(\uEE93,12)'))
        item(image=image.mdl(\uEE94,default_icon_size) tip=['Wheel',tip.info] cmd=command.copy('image.mdl(\uEE94,12)'))
        item(image=image.mdl(\uEEA3,default_icon_size) tip=['VirtualMachineGroup',tip.info] cmd=command.copy('image.mdl(\uEEA3,12)'))
        item(image=image.mdl(\uEECA,default_icon_size) tip=['ButtonView2',tip.info] cmd=command.copy('image.mdl(\uEECA,12)'))
        item(image=image.mdl(\uEF15,default_icon_size) tip=['PenWorkspaceMirrored',tip.info] cmd=command.copy('image.mdl(\uEF15,12)'))
        item(image=image.mdl(\uEF16,default_icon_size) tip=['PenPaletteMirrored',tip.info] cmd=command.copy('image.mdl(\uEF16,12)'))
        item(image=image.mdl(\uEF17,default_icon_size) tip=['StrokeEraseMirrored',tip.info] cmd=command.copy('image.mdl(\uEF17,12)'))
        item(image=image.mdl(\uEF18,default_icon_size) tip=['PointEraseMirrored',tip.info] cmd=command.copy('image.mdl(\uEF18,12)'))
        item(image=image.mdl(\uEF19,default_icon_size) tip=['ClearAllInkMirrored',tip.info] cmd=command.copy('image.mdl(\uEF19,12)'))
        item(image=image.mdl(\uEF1F,default_icon_size) tip=['BackgroundToggle',tip.info] cmd=command.copy('image.mdl(\uEF1F,12)'))
        item(image=image.mdl(\uEF20,default_icon_size) tip=['Marquee',tip.info] cmd=command.copy('image.mdl(\uEF20,12)'))
        item(image=image.mdl(\uEF2C,default_icon_size) tip=['ChromeCloseContrast',tip.info] cmd=command.copy('image.mdl(\uEF2C,12)'))
        item(image=image.mdl(\uEF2D,default_icon_size) tip=['ChromeMinimizeContrast',tip.info] cmd=command.copy('image.mdl(\uEF2D,12)'))

        item(image=image.mdl(\uEF2E,default_icon_size) tip=['ChromeMaximizeContrast',tip.info] cmd=command.copy('image.mdl(\uEF2E,12)') col)
        item(image=image.mdl(\uEF2F,default_icon_size) tip=['ChromeRestoreContrast',tip.info] cmd=command.copy('image.mdl(\uEF2F,12)'))
        item(image=image.mdl(\uEF31,default_icon_size) tip=['TrafficLight',tip.info] cmd=command.copy('image.mdl(\uEF31,12)'))
        item(image=image.mdl(\uEF3B,default_icon_size) tip=['Replay',tip.info] cmd=command.copy('image.mdl(\uEF3B,12)'))
        item(image=image.mdl(\uEF3C,default_icon_size) tip=['Eyedropper',tip.info] cmd=command.copy('image.mdl(\uEF3C,12)'))
        item(image=image.mdl(\uEF3D,default_icon_size) tip=['LineDisplay',tip.info] cmd=command.copy('image.mdl(\uEF3D,12)'))
        item(image=image.mdl(\uEF3E,default_icon_size) tip=['PINPad',tip.info] cmd=command.copy('image.mdl(\uEF3E,12)'))
        item(image=image.mdl(\uEF3F,default_icon_size) tip=['SignatureCapture',tip.info] cmd=command.copy('image.mdl(\uEF3F,12)'))
        item(image=image.mdl(\uEF40,default_icon_size) tip=['ChipCardCreditCardReader',tip.info] cmd=command.copy('image.mdl(\uEF40,12)'))
        item(image=image.mdl(\uEF58,default_icon_size) tip=['PlayerSettings',tip.info] cmd=command.copy('image.mdl(\uEF58,12)'))
        item(image=image.mdl(\uEF6B,default_icon_size) tip=['LandscapeOrientation',tip.info] cmd=command.copy('image.mdl(\uEF6B,12)'))
        item(image=image.mdl(\uEF90,default_icon_size) tip=['Flow',tip.info] cmd=command.copy('image.mdl(\uEF90,12)'))
        item(image=image.mdl(\uEFA5,default_icon_size) tip=['Touchpad',tip.info] cmd=command.copy('image.mdl(\uEFA5,12)'))
        item(image=image.mdl(\uEFA9,default_icon_size) tip=['Speech',tip.info] cmd=command.copy('image.mdl(\uEFA9,12)'))
        item(image=image.mdl(\uF000,default_icon_size) tip=['KnowledgeArticle',tip.info] cmd=command.copy('image.mdl(\uF000,12)'))
        item(image=image.mdl(\uF003,default_icon_size) tip=['Relationship',tip.info] cmd=command.copy('image.mdl(\uF003,12)'))

        item(image=image.mdl(\uF080,default_icon_size) tip=['DefaultAPN',tip.info] cmd=command.copy('image.mdl(\uF080,12)') col)
        item(image=image.mdl(\uF081,default_icon_size) tip=['UserAPN',tip.info] cmd=command.copy('image.mdl(\uF081,12)'))
        item(image=image.mdl(\uF085,default_icon_size) tip=['DoublePinyin',tip.info] cmd=command.copy('image.mdl(\uF085,12)'))
        item(image=image.mdl(\uF08C,default_icon_size) tip=['BlueLight',tip.info] cmd=command.copy('image.mdl(\uF08C,12)'))
        item(image=image.mdl(\uF093,default_icon_size) tip=['ButtonA',tip.info] cmd=command.copy('image.mdl(\uF093,12)'))
        item(image=image.mdl(\uF094,default_icon_size) tip=['ButtonB',tip.info] cmd=command.copy('image.mdl(\uF094,12)'))
        item(image=image.mdl(\uF095,default_icon_size) tip=['ButtonY',tip.info] cmd=command.copy('image.mdl(\uF095,12)'))
        item(image=image.mdl(\uF096,default_icon_size) tip=['ButtonX',tip.info] cmd=command.copy('image.mdl(\uF096,12)'))
        item(image=image.mdl(\uF0AD,default_icon_size) tip=['ArrowUp8',tip.info] cmd=command.copy('image.mdl(\uF0AD,12)'))
        item(image=image.mdl(\uF0AE,default_icon_size) tip=['ArrowDown8',tip.info] cmd=command.copy('image.mdl(\uF0AE,12)'))
        item(image=image.mdl(\uF0AF,default_icon_size) tip=['ArrowRight8',tip.info] cmd=command.copy('image.mdl(\uF0AF,12)'))
        item(image=image.mdl(\uF0B0,default_icon_size) tip=['ArrowLeft8',tip.info] cmd=command.copy('image.mdl(\uF0B0,12)'))
        item(image=image.mdl(\uF0B2,default_icon_size) tip=['QuarentinedItems',tip.info] cmd=command.copy('image.mdl(\uF0B2,12)'))
        item(image=image.mdl(\uF0B3,default_icon_size) tip=['QuarentinedItemsMirrored',tip.info] cmd=command.copy('image.mdl(\uF0B3,12)'))
        item(image=image.mdl(\uF0B4,default_icon_size) tip=['Protractor',tip.info] cmd=command.copy('image.mdl(\uF0B4,12)'))
        item(image=image.mdl(\uF0B5,default_icon_size) tip=['ChecklistMirrored',tip.info] cmd=command.copy('image.mdl(\uF0B5,12)'))
    }

    menu(title='MDL2 #5')
    {
        item(image=image.mdl(\uF0B6,default_icon_size) tip=['StatusCircle7',tip.info] cmd=command.copy('image.mdl(\uF0B6,12)'))
        item(image=image.mdl(\uF0B7,default_icon_size) tip=['StatusCheckmark7',tip.info] cmd=command.copy('image.mdl(\uF0B7,12)'))
        item(image=image.mdl(\uF0B8,default_icon_size) tip=['StatusErrorCircle7',tip.info] cmd=command.copy('image.mdl(\uF0B8,12)'))
        item(image=image.mdl(\uF0B9,default_icon_size) tip=['Connected',tip.info] cmd=command.copy('image.mdl(\uF0B9,12)'))
        item(image=image.mdl(\uF0C6,default_icon_size) tip=['PencilFill',tip.info] cmd=command.copy('image.mdl(\uF0C6,12)'))
        item(image=image.mdl(\uF0C7,default_icon_size) tip=['CalligraphyFill',tip.info] cmd=command.copy('image.mdl(\uF0C7,12)'))
        item(image=image.mdl(\uF0CA,default_icon_size) tip=['QuarterStarLeft',tip.info] cmd=command.copy('image.mdl(\uF0CA,12)'))
        item(image=image.mdl(\uF0CB,default_icon_size) tip=['QuarterStarRight',tip.info] cmd=command.copy('image.mdl(\uF0CB,12)'))
        item(image=image.mdl(\uF0CC,default_icon_size) tip=['ThreeQuarterStarLeft',tip.info] cmd=command.copy('image.mdl(\uF0CC,12)'))
        item(image=image.mdl(\uF0CD,default_icon_size) tip=['ThreeQuarterStarRight',tip.info] cmd=command.copy('image.mdl(\uF0CD,12)'))
        item(image=image.mdl(\uF0CE,default_icon_size) tip=['QuietHoursBadge12',tip.info] cmd=command.copy('image.mdl(\uF0CE,12)'))
        item(image=image.mdl(\uF0D2,default_icon_size) tip=['BackMirrored',tip.info] cmd=command.copy('image.mdl(\uF0D2,12)'))
        item(image=image.mdl(\uF0D3,default_icon_size) tip=['ForwardMirrored',tip.info] cmd=command.copy('image.mdl(\uF0D3,12)'))
        item(image=image.mdl(\uF0D5,default_icon_size) tip=['ChromeBackContrast',tip.info] cmd=command.copy('image.mdl(\uF0D5,12)'))
        item(image=image.mdl(\uF0D6,default_icon_size) tip=['ChromeBackContrastMirrored',tip.info] cmd=command.copy('image.mdl(\uF0D6,12)'))
        item(image=image.mdl(\uF0D7,default_icon_size) tip=['ChromeBackToWindowContrast',tip.info] cmd=command.copy('image.mdl(\uF0D7,12)'))

        item(image=image.mdl(\uF0D8,default_icon_size) tip=['ChromeFullScreenContrast',tip.info] cmd=command.copy('image.mdl(\uF0D8,12)') col)
        item(image=image.mdl(\uF0E2,default_icon_size) tip=['GridView',tip.info] cmd=command.copy('image.mdl(\uF0E2,12)'))
        item(image=image.mdl(\uF0E3,default_icon_size) tip=['ClipboardList',tip.info] cmd=command.copy('image.mdl(\uF0E3,12)'))
        item(image=image.mdl(\uF0E4,default_icon_size) tip=['ClipboardListMirrored',tip.info] cmd=command.copy('image.mdl(\uF0E4,12)'))
        item(image=image.mdl(\uF0E5,default_icon_size) tip=['OutlineQuarterStarLeft',tip.info] cmd=command.copy('image.mdl(\uF0E5,12)'))
        item(image=image.mdl(\uF0E6,default_icon_size) tip=['OutlineQuarterStarRight',tip.info] cmd=command.copy('image.mdl(\uF0E6,12)'))
        item(image=image.mdl(\uF0E7,default_icon_size) tip=['OutlineHalfStarLeft',tip.info] cmd=command.copy('image.mdl(\uF0E7,12)'))
        item(image=image.mdl(\uF0E8,default_icon_size) tip=['OutlineHalfStarRight',tip.info] cmd=command.copy('image.mdl(\uF0E8,12)'))
        item(image=image.mdl(\uF0E9,default_icon_size) tip=['OutlineThreeQuarterStarLeft',tip.info] cmd=command.copy('image.mdl(\uF0E9,12)'))
        item(image=image.mdl(\uF0EA,default_icon_size) tip=['OutlineThreeQuarterStarRight',tip.info] cmd=command.copy('image.mdl(\uF0EA,12)'))
        item(image=image.mdl(\uF0EB,default_icon_size) tip=['SpatialVolume0',tip.info] cmd=command.copy('image.mdl(\uF0EB,12)'))
        item(image=image.mdl(\uF0EC,default_icon_size) tip=['SpatialVolume1',tip.info] cmd=command.copy('image.mdl(\uF0EC,12)'))
        item(image=image.mdl(\uF0ED,default_icon_size) tip=['SpatialVolume2',tip.info] cmd=command.copy('image.mdl(\uF0ED,12)'))
        item(image=image.mdl(\uF0EE,default_icon_size) tip=['SpatialVolume3',tip.info] cmd=command.copy('image.mdl(\uF0EE,12)'))
        item(image=image.mdl(\uF0EF,default_icon_size) tip=['ApplicationGuard',tip.info] cmd=command.copy('image.mdl(\uF0EF,12)'))
        item(image=image.mdl(\uF0F7,default_icon_size) tip=['OutlineStarLeftHalf',tip.info] cmd=command.copy('image.mdl(\uF0F7,12)'))

        item(image=image.mdl(\uF0F8,default_icon_size) tip=['OutlineStarRightHalf',tip.info] cmd=command.copy('image.mdl(\uF0F8,12)') col)
        item(image=image.mdl(\uF0F9,default_icon_size) tip=['ChromeAnnotateContrast',tip.info] cmd=command.copy('image.mdl(\uF0F9,12)'))
        item(image=image.mdl(\uF0FB,default_icon_size) tip=['DefenderBadge12',tip.info] cmd=command.copy('image.mdl(\uF0FB,12)'))
        item(image=image.mdl(\uF103,default_icon_size) tip=['DetachablePC',tip.info] cmd=command.copy('image.mdl(\uF103,12)'))
        item(image=image.mdl(\uF108,default_icon_size) tip=['LeftStick',tip.info] cmd=command.copy('image.mdl(\uF108,12)'))
        item(image=image.mdl(\uF109,default_icon_size) tip=['RightStick',tip.info] cmd=command.copy('image.mdl(\uF109,12)'))
        item(image=image.mdl(\uF10A,default_icon_size) tip=['TriggerLeft',tip.info] cmd=command.copy('image.mdl(\uF10A,12)'))
        item(image=image.mdl(\uF10B,default_icon_size) tip=['TriggerRight',tip.info] cmd=command.copy('image.mdl(\uF10B,12)'))
        item(image=image.mdl(\uF10C,default_icon_size) tip=['BumperLeft',tip.info] cmd=command.copy('image.mdl(\uF10C,12)'))
        item(image=image.mdl(\uF10D,default_icon_size) tip=['BumperRight',tip.info] cmd=command.copy('image.mdl(\uF10D,12)'))
        item(image=image.mdl(\uF10E,default_icon_size) tip=['Dpad',tip.info] cmd=command.copy('image.mdl(\uF10E,12)'))
        item(image=image.mdl(\uF110,default_icon_size) tip=['EnglishPunctuation',tip.info] cmd=command.copy('image.mdl(\uF110,12)'))
        item(image=image.mdl(\uF111,default_icon_size) tip=['ChinesePunctuation',tip.info] cmd=command.copy('image.mdl(\uF111,12)'))
        item(image=image.mdl(\uF119,default_icon_size) tip=['HMD',tip.info] cmd=command.copy('image.mdl(\uF119,12)'))
        item(image=image.mdl(\uF11B,default_icon_size) tip=['CtrlSpatialRight',tip.info] cmd=command.copy('image.mdl(\uF11B,12)'))
        item(image=image.mdl(\uF126,default_icon_size) tip=['PaginationDotOutline10',tip.info] cmd=command.copy('image.mdl(\uF126,12)'))

        item(image=image.mdl(\uF127,default_icon_size) tip=['PaginationDotSolid10',tip.info] cmd=command.copy('image.mdl(\uF127,12)') col)
        item(image=image.mdl(\uF128,default_icon_size) tip=['StrokeErase2',tip.info] cmd=command.copy('image.mdl(\uF128,12)'))
        item(image=image.mdl(\uF129,default_icon_size) tip=['SmallErase',tip.info] cmd=command.copy('image.mdl(\uF129,12)'))
        item(image=image.mdl(\uF12A,default_icon_size) tip=['LargeErase',tip.info] cmd=command.copy('image.mdl(\uF12A,12)'))
        item(image=image.mdl(\uF12B,default_icon_size) tip=['FolderHorizontal',tip.info] cmd=command.copy('image.mdl(\uF12B,12)'))
        item(image=image.mdl(\uF12E,default_icon_size) tip=['MicrophoneListening',tip.info] cmd=command.copy('image.mdl(\uF12E,12)'))
        item(image=image.mdl(\uF12F,default_icon_size) tip=['StatusExclamationCircle7',tip.info] cmd=command.copy('image.mdl(\uF12F,12)'))
        item(image=image.mdl(\uF131,default_icon_size) tip=['Video360',tip.info] cmd=command.copy('image.mdl(\uF131,12)'))
        item(image=image.mdl(\uF133,default_icon_size) tip=['GiftboxOpen',tip.info] cmd=command.copy('image.mdl(\uF133,12)'))
        item(image=image.mdl(\uF136,default_icon_size) tip=['StatusCircleOuter',tip.info] cmd=command.copy('image.mdl(\uF136,12)'))
        item(image=image.mdl(\uF137,default_icon_size) tip=['StatusCircleInner',tip.info] cmd=command.copy('image.mdl(\uF137,12)'))
        item(image=image.mdl(\uF138,default_icon_size) tip=['StatusCircleRing',tip.info] cmd=command.copy('image.mdl(\uF138,12)'))
        item(image=image.mdl(\uF139,default_icon_size) tip=['StatusTriangleOuter',tip.info] cmd=command.copy('image.mdl(\uF139,12)'))
        item(image=image.mdl(\uF13A,default_icon_size) tip=['StatusTriangleInner',tip.info] cmd=command.copy('image.mdl(\uF13A,12)'))
        item(image=image.mdl(\uF13B,default_icon_size) tip=['StatusTriangleExclamation',tip.info] cmd=command.copy('image.mdl(\uF13B,12)'))
        item(image=image.mdl(\uF13C,default_icon_size) tip=['StatusCircleExclamation',tip.info] cmd=command.copy('image.mdl(\uF13C,12)'))

        item(image=image.mdl(\uF13D,default_icon_size) tip=['StatusCircleErrorX',tip.info] cmd=command.copy('image.mdl(\uF13D,12)') col)
        item(image=image.mdl(\uF13E,default_icon_size) tip=['StatusCircleCheckmark',tip.info] cmd=command.copy('image.mdl(\uF13E,12)'))
        item(image=image.mdl(\uF13F,default_icon_size) tip=['StatusCircleInfo',tip.info] cmd=command.copy('image.mdl(\uF13F,12)'))
        item(image=image.mdl(\uF140,default_icon_size) tip=['StatusCircleBlock',tip.info] cmd=command.copy('image.mdl(\uF140,12)'))
        item(image=image.mdl(\uF141,default_icon_size) tip=['StatusCircleBlock2',tip.info] cmd=command.copy('image.mdl(\uF141,12)'))
        item(image=image.mdl(\uF142,default_icon_size) tip=['StatusCircleQuestionMark',tip.info] cmd=command.copy('image.mdl(\uF142,12)'))
        item(image=image.mdl(\uF143,default_icon_size) tip=['StatusCircleSync',tip.info] cmd=command.copy('image.mdl(\uF143,12)'))
        item(image=image.mdl(\uF146,default_icon_size) tip=['Dial1',tip.info] cmd=command.copy('image.mdl(\uF146,12)'))
        item(image=image.mdl(\uF147,default_icon_size) tip=['Dial2',tip.info] cmd=command.copy('image.mdl(\uF147,12)'))
        item(image=image.mdl(\uF148,default_icon_size) tip=['Dial3',tip.info] cmd=command.copy('image.mdl(\uF148,12)'))
        item(image=image.mdl(\uF149,default_icon_size) tip=['Dial4',tip.info] cmd=command.copy('image.mdl(\uF149,12)'))
        item(image=image.mdl(\uF14A,default_icon_size) tip=['Dial5',tip.info] cmd=command.copy('image.mdl(\uF14A,12)'))
        item(image=image.mdl(\uF14B,default_icon_size) tip=['Dial6',tip.info] cmd=command.copy('image.mdl(\uF14B,12)'))
        item(image=image.mdl(\uF14C,default_icon_size) tip=['Dial7',tip.info] cmd=command.copy('image.mdl(\uF14C,12)'))
        item(image=image.mdl(\uF14D,default_icon_size) tip=['Dial8',tip.info] cmd=command.copy('image.mdl(\uF14D,12)'))
        item(image=image.mdl(\uF14E,default_icon_size) tip=['Dial9',tip.info] cmd=command.copy('image.mdl(\uF14E,12)'))

        item(image=image.mdl(\uF14F,default_icon_size) tip=['Dial10',tip.info] cmd=command.copy('image.mdl(\uF14F,12)') col)
        item(image=image.mdl(\uF150,default_icon_size) tip=['Dial11',tip.info] cmd=command.copy('image.mdl(\uF150,12)'))
        item(image=image.mdl(\uF151,default_icon_size) tip=['Dial12',tip.info] cmd=command.copy('image.mdl(\uF151,12)'))
        item(image=image.mdl(\uF152,default_icon_size) tip=['Dial13',tip.info] cmd=command.copy('image.mdl(\uF152,12)'))
        item(image=image.mdl(\uF153,default_icon_size) tip=['Dial14',tip.info] cmd=command.copy('image.mdl(\uF153,12)'))
        item(image=image.mdl(\uF154,default_icon_size) tip=['Dial15',tip.info] cmd=command.copy('image.mdl(\uF154,12)'))
        item(image=image.mdl(\uF155,default_icon_size) tip=['Dial16',tip.info] cmd=command.copy('image.mdl(\uF155,12)'))
        item(image=image.mdl(\uF156,default_icon_size) tip=['DialShape1',tip.info] cmd=command.copy('image.mdl(\uF156,12)'))
        item(image=image.mdl(\uF157,default_icon_size) tip=['DialShape2',tip.info] cmd=command.copy('image.mdl(\uF157,12)'))
        item(image=image.mdl(\uF158,default_icon_size) tip=['DialShape3',tip.info] cmd=command.copy('image.mdl(\uF158,12)'))
        item(image=image.mdl(\uF159,default_icon_size) tip=['DialShape4',tip.info] cmd=command.copy('image.mdl(\uF159,12)'))
        item(image=image.mdl(\uF161,default_icon_size) tip=['TollSolid',tip.info] cmd=command.copy('image.mdl(\uF161,12)'))
        item(image=image.mdl(\uF163,default_icon_size) tip=['TrafficCongestionSolid',tip.info] cmd=command.copy('image.mdl(\uF163,12)'))
        item(image=image.mdl(\uF164,default_icon_size) tip=['ExploreContentSingle',tip.info] cmd=command.copy('image.mdl(\uF164,12)'))
        item(image=image.mdl(\uF165,default_icon_size) tip=['CollapseContent',tip.info] cmd=command.copy('image.mdl(\uF165,12)'))
        item(image=image.mdl(\uF166,default_icon_size) tip=['CollapseContentSingle',tip.info] cmd=command.copy('image.mdl(\uF166,12)'))

        item(image=image.mdl(\uF167,default_icon_size) tip=['InfoSolid',tip.info] cmd=command.copy('image.mdl(\uF167,12)') col)
        item(image=image.mdl(\uF168,default_icon_size) tip=['GroupList',tip.info] cmd=command.copy('image.mdl(\uF168,12)'))
        item(image=image.mdl(\uF169,default_icon_size) tip=['CaretBottomRightSolidCenter8',tip.info] cmd=command.copy('image.mdl(\uF169,12)'))
        item(image=image.mdl(\uF16A,default_icon_size) tip=['ProgressRingDots',tip.info] cmd=command.copy('image.mdl(\uF16A,12)'))
        item(image=image.mdl(\uF16B,default_icon_size) tip=['Checkbox14',tip.info] cmd=command.copy('image.mdl(\uF16B,12)'))
        item(image=image.mdl(\uF16C,default_icon_size) tip=['CheckboxComposite14',tip.info] cmd=command.copy('image.mdl(\uF16C,12)'))
        item(image=image.mdl(\uF16D,default_icon_size) tip=['CheckboxIndeterminateCombo14',tip.info] cmd=command.copy('image.mdl(\uF16D,12)'))
        item(image=image.mdl(\uF16E,default_icon_size) tip=['CheckboxIndeterminateCombo',tip.info] cmd=command.copy('image.mdl(\uF16E,12)'))
        item(image=image.mdl(\uF175,default_icon_size) tip=['StatusPause7',tip.info] cmd=command.copy('image.mdl(\uF175,12)'))
        item(image=image.mdl(\uF17F,default_icon_size) tip=['CharacterAppearance',tip.info] cmd=command.copy('image.mdl(\uF17F,12)'))
        item(image=image.mdl(\uF180,default_icon_size) tip=['Lexicon',tip.info] cmd=command.copy('image.mdl(\uF180,12)'))
        item(image=image.mdl(\uF182,default_icon_size) tip=['ScreenTime',tip.info] cmd=command.copy('image.mdl(\uF182,12)'))
        item(image=image.mdl(\uF191,default_icon_size) tip=['HeadlessDevice',tip.info] cmd=command.copy('image.mdl(\uF191,12)'))
        item(image=image.mdl(\uF193,default_icon_size) tip=['NetworkSharing',tip.info] cmd=command.copy('image.mdl(\uF193,12)'))
        item(image=image.mdl(\uF19D,default_icon_size) tip=['EyeGaze',tip.info] cmd=command.copy('image.mdl(\uF19D,12)'))
        item(image=image.mdl(\uF19E,default_icon_size) tip=['ToggleLeft',tip.info] cmd=command.copy('image.mdl(\uF19E,12)'))

        item(image=image.mdl(\uF19F,default_icon_size) tip=['ToggleRight',tip.info] cmd=command.copy('image.mdl(\uF19F,12)') col)
        item(image=image.mdl(\uF1AD,default_icon_size) tip=['WindowsInsider',tip.info] cmd=command.copy('image.mdl(\uF1AD,12)'))
        item(image=image.mdl(\uF1CB,default_icon_size) tip=['ChromeSwitch',tip.info] cmd=command.copy('image.mdl(\uF1CB,12)'))
        item(image=image.mdl(\uF1CC,default_icon_size) tip=['ChromeSwitchContast',tip.info] cmd=command.copy('image.mdl(\uF1CC,12)'))
        item(image=image.mdl(\uF1D8,default_icon_size) tip=['StatusCheckmark',tip.info] cmd=command.copy('image.mdl(\uF1D8,12)'))
        item(image=image.mdl(\uF1D9,default_icon_size) tip=['StatusCheckmarkLeft',tip.info] cmd=command.copy('image.mdl(\uF1D9,12)'))
        item(image=image.mdl(\uF20C,default_icon_size) tip=['KeyboardLeftAligned',tip.info] cmd=command.copy('image.mdl(\uF20C,12)'))
        item(image=image.mdl(\uF20D,default_icon_size) tip=['KeyboardRightAligned',tip.info] cmd=command.copy('image.mdl(\uF20D,12)'))
        item(image=image.mdl(\uF210,default_icon_size) tip=['KeyboardSettings',tip.info] cmd=command.copy('image.mdl(\uF210,12)'))
        item(image=image.mdl(\uF211,default_icon_size) tip=['NetworkPhysical',tip.info] cmd=command.copy('image.mdl(\uF211,12)'))
        item(image=image.mdl(\uF22C,default_icon_size) tip=['IOT',tip.info] cmd=command.copy('image.mdl(\uF22C,12)'))
        item(image=image.mdl(\uF22E,default_icon_size) tip=['UnknownMirrored',tip.info] cmd=command.copy('image.mdl(\uF22E,12)'))
        item(image=image.mdl(\uF246,default_icon_size) tip=['ViewDashboard',tip.info] cmd=command.copy('image.mdl(\uF246,12)'))
        item(image=image.mdl(\uF259,default_icon_size) tip=['ExploitProtectionSettings',tip.info] cmd=command.copy('image.mdl(\uF259,12)'))
        item(image=image.mdl(\uF260,default_icon_size) tip=['KeyboardNarrow',tip.info] cmd=command.copy('image.mdl(\uF260,12)'))
        item(image=image.mdl(\uF261,default_icon_size) tip=['Keyboard12Key',tip.info] cmd=command.copy('image.mdl(\uF261,12)'))

        item(image=image.mdl(\uF26B,default_icon_size) tip=['KeyboardDock',tip.info] cmd=command.copy('image.mdl(\uF26B,12)') col)
        item(image=image.mdl(\uF26C,default_icon_size) tip=['KeyboardUndock',tip.info] cmd=command.copy('image.mdl(\uF26C,12)'))
        item(image=image.mdl(\uF26D,default_icon_size) tip=['KeyboardLeftDock',tip.info] cmd=command.copy('image.mdl(\uF26D,12)'))
        item(image=image.mdl(\uF26E,default_icon_size) tip=['KeyboardRightDock',tip.info] cmd=command.copy('image.mdl(\uF26E,12)'))
        item(image=image.mdl(\uF270,default_icon_size) tip=['Ear',tip.info] cmd=command.copy('image.mdl(\uF270,12)'))
        item(image=image.mdl(\uF271,default_icon_size) tip=['PointerHand',tip.info] cmd=command.copy('image.mdl(\uF271,12)'))
        item(image=image.mdl(\uF272,default_icon_size) tip=['Bullseye',tip.info] cmd=command.copy('image.mdl(\uF272,12)'))
        item(image=image.mdl(\uF2B7,default_icon_size) tip=['LocaleLanguage',tip.info] cmd=command.copy('image.mdl(\uF2B7,12)'))
        item(image=image.mdl(\uF32A,default_icon_size) tip=['PassiveAuthentication',tip.info] cmd=command.copy('image.mdl(\uF32A,12)'))
        item(image=image.mdl(\uF354,default_icon_size) tip=['ColorSolid',tip.info] cmd=command.copy('image.mdl(\uF354,12)'))
        item(image=image.mdl(\uF384,default_icon_size) tip=['NetworkOffline',tip.info] cmd=command.copy('image.mdl(\uF384,12)'))
        item(image=image.mdl(\uF385,default_icon_size) tip=['NetworkConnected',tip.info] cmd=command.copy('image.mdl(\uF385,12)'))
        item(image=image.mdl(\uF386,default_icon_size) tip=['NetworkConnectedCheckmark',tip.info] cmd=command.copy('image.mdl(\uF386,12)'))
        item(image=image.mdl(\uF3B1,default_icon_size) tip=['SignOut',tip.info] cmd=command.copy('image.mdl(\uF3B1,12)'))
        item(image=image.mdl(\uF3CC,default_icon_size) tip=['StatusInfo',tip.info] cmd=command.copy('image.mdl(\uF3CC,12)'))
        item(image=image.mdl(\uF3CD,default_icon_size) tip=['StatusInfoLeft',tip.info] cmd=command.copy('image.mdl(\uF3CD,12)'))

        item(image=image.mdl(\uF3E2,default_icon_size) tip=['NearbySharing',tip.info] cmd=command.copy('image.mdl(\uF3E2,12)') col)
        item(image=image.mdl(\uF3E7,default_icon_size) tip=['CtrlSpatialLeft',tip.info] cmd=command.copy('image.mdl(\uF3E7,12)'))
        item(image=image.mdl(\uF404,default_icon_size) tip=['InteractiveDashboard',tip.info] cmd=command.copy('image.mdl(\uF404,12)'))
        item(image=image.mdl(\uF406,default_icon_size) tip=['ClippingTool',tip.info] cmd=command.copy('image.mdl(\uF406,12)'))
        item(image=image.mdl(\uF407,default_icon_size) tip=['RectangularClipping',tip.info] cmd=command.copy('image.mdl(\uF407,12)'))
        item(image=image.mdl(\uF408,default_icon_size) tip=['FreeFormClipping',tip.info] cmd=command.copy('image.mdl(\uF408,12)'))
        item(image=image.mdl(\uF413,default_icon_size) tip=['CopyTo',tip.info] cmd=command.copy('image.mdl(\uF413,12)'))
        item(image=image.mdl(\uF439,default_icon_size) tip=['DynamicLock',tip.info] cmd=command.copy('image.mdl(\uF439,12)'))
        item(image=image.mdl(\uF45E,default_icon_size) tip=['PenTips',tip.info] cmd=command.copy('image.mdl(\uF45E,12)'))
        item(image=image.mdl(\uF45F,default_icon_size) tip=['PenTipsMirrored',tip.info] cmd=command.copy('image.mdl(\uF45F,12)'))
        item(image=image.mdl(\uF460,default_icon_size) tip=['HWPJoin',tip.info] cmd=command.copy('image.mdl(\uF460,12)'))
        item(image=image.mdl(\uF461,default_icon_size) tip=['HWPInsert',tip.info] cmd=command.copy('image.mdl(\uF461,12)'))
        item(image=image.mdl(\uF462,default_icon_size) tip=['HWPStrikeThrough',tip.info] cmd=command.copy('image.mdl(\uF462,12)'))
        item(image=image.mdl(\uF463,default_icon_size) tip=['HWPScratchOut',tip.info] cmd=command.copy('image.mdl(\uF463,12)'))
        item(image=image.mdl(\uF464,default_icon_size) tip=['HWPSplit',tip.info] cmd=command.copy('image.mdl(\uF464,12)'))
        item(image=image.mdl(\uF465,default_icon_size) tip=['HWPNewLine',tip.info] cmd=command.copy('image.mdl(\uF465,12)'))

        item(image=image.mdl(\uF466,default_icon_size) tip=['HWPOverwrite',tip.info] cmd=command.copy('image.mdl(\uF466,12)') col)
        item(image=image.mdl(\uF473,default_icon_size) tip=['MobWifiWarning1',tip.info] cmd=command.copy('image.mdl(\uF473,12)'))
        item(image=image.mdl(\uF474,default_icon_size) tip=['MobWifiWarning2',tip.info] cmd=command.copy('image.mdl(\uF474,12)'))
        item(image=image.mdl(\uF475,default_icon_size) tip=['MobWifiWarning3',tip.info] cmd=command.copy('image.mdl(\uF475,12)'))
        item(image=image.mdl(\uF476,default_icon_size) tip=['MobWifiWarning4',tip.info] cmd=command.copy('image.mdl(\uF476,12)'))
        item(image=image.mdl(\uF49A,default_icon_size) tip=['Globe2',tip.info] cmd=command.copy('image.mdl(\uF49A,12)'))
        item(image=image.mdl(\uF4A5,default_icon_size) tip=['SpecialEffectSize',tip.info] cmd=command.copy('image.mdl(\uF4A5,12)'))
        item(image=image.mdl(\uF4A9,default_icon_size) tip=['GIF',tip.info] cmd=command.copy('image.mdl(\uF4A9,12)'))
        item(image=image.mdl(\uF4AA,default_icon_size) tip=['Sticker2',tip.info] cmd=command.copy('image.mdl(\uF4AA,12)'))
        item(image=image.mdl(\uF4BE,default_icon_size) tip=['SurfaceHubSelected',tip.info] cmd=command.copy('image.mdl(\uF4BE,12)'))
        item(image=image.mdl(\uF4BF,default_icon_size) tip=['HoloLensSelected',tip.info] cmd=command.copy('image.mdl(\uF4BF,12)'))
        item(image=image.mdl(\uF4C0,default_icon_size) tip=['Earbud',tip.info] cmd=command.copy('image.mdl(\uF4C0,12)'))
        item(image=image.mdl(\uF4C3,default_icon_size) tip=['MixVolumes',tip.info] cmd=command.copy('image.mdl(\uF4C3,12)'))
        item(image=image.mdl(\uF540,default_icon_size) tip=['Safe',tip.info] cmd=command.copy('image.mdl(\uF540,12)'))
        item(image=image.mdl(\uF552,default_icon_size) tip=['LaptopSecure',tip.info] cmd=command.copy('image.mdl(\uF552,12)'))
        item(image=image.mdl(\uF56D,default_icon_size) tip=['PrintDefault',tip.info] cmd=command.copy('image.mdl(\uF56D,12)'))

        item(image=image.mdl(\uF56E,default_icon_size) tip=['PageMirrored',tip.info] cmd=command.copy('image.mdl(\uF56E,12)') col)
        item(image=image.mdl(\uF56F,default_icon_size) tip=['LandscapeOrientationMirrored',tip.info] cmd=command.copy('image.mdl(\uF56F,12)'))
        item(image=image.mdl(\uF570,default_icon_size) tip=['ColorOff',tip.info] cmd=command.copy('image.mdl(\uF570,12)'))
        item(image=image.mdl(\uF571,default_icon_size) tip=['PrintAllPages',tip.info] cmd=command.copy('image.mdl(\uF571,12)'))
        item(image=image.mdl(\uF572,default_icon_size) tip=['PrintCustomRange',tip.info] cmd=command.copy('image.mdl(\uF572,12)'))
        item(image=image.mdl(\uF573,default_icon_size) tip=['PageMarginPortraitNarrow',tip.info] cmd=command.copy('image.mdl(\uF573,12)'))
        item(image=image.mdl(\uF574,default_icon_size) tip=['PageMarginPortraitNormal',tip.info] cmd=command.copy('image.mdl(\uF574,12)'))
        item(image=image.mdl(\uF575,default_icon_size) tip=['PageMarginPortraitModerate',tip.info] cmd=command.copy('image.mdl(\uF575,12)'))
        item(image=image.mdl(\uF576,default_icon_size) tip=['PageMarginPortraitWide',tip.info] cmd=command.copy('image.mdl(\uF576,12)'))
        item(image=image.mdl(\uF577,default_icon_size) tip=['PageMarginLandscapeNarrow',tip.info] cmd=command.copy('image.mdl(\uF577,12)'))
        item(image=image.mdl(\uF578,default_icon_size) tip=['PageMarginLandscapeNormal',tip.info] cmd=command.copy('image.mdl(\uF578,12)'))
        item(image=image.mdl(\uF579,default_icon_size) tip=['PageMarginLandscapeModerate',tip.info] cmd=command.copy('image.mdl(\uF579,12)'))
        item(image=image.mdl(\uF57A,default_icon_size) tip=['PageMarginLandscapeWide',tip.info] cmd=command.copy('image.mdl(\uF57A,12)'))
        item(image=image.mdl(\uF57B,default_icon_size) tip=['CollateLandscape',tip.info] cmd=command.copy('image.mdl(\uF57B,12)'))
        item(image=image.mdl(\uF57C,default_icon_size) tip=['CollatePortrait',tip.info] cmd=command.copy('image.mdl(\uF57C,12)'))
        item(image=image.mdl(\uF57D,default_icon_size) tip=['CollatePortraitSeparated',tip.info] cmd=command.copy('image.mdl(\uF57D,12)'))

        item(image=image.mdl(\uF57E,default_icon_size) tip=['DuplexLandscapeOneSided',tip.info] cmd=command.copy('image.mdl(\uF57E,12)') col)
        item(image=image.mdl(\uF57F,default_icon_size) tip=['DuplexLandscapeOneSidedMirrored',tip.info] cmd=command.copy('image.mdl(\uF57F,12)'))
        item(image=image.mdl(\uF580,default_icon_size) tip=['DuplexLandscapeTwoSidedLongEdge',tip.info] cmd=command.copy('image.mdl(\uF580,12)'))
        item(image=image.mdl(\uF581,default_icon_size) tip=['DuplexLandscapeTwoSidedLongEdgeMirrored',tip.info] cmd=command.copy('image.mdl(\uF581,12)'))
        item(image=image.mdl(\uF582,default_icon_size) tip=['DuplexLandscapeTwoSidedShortEdge',tip.info] cmd=command.copy('image.mdl(\uF582,12)'))
        item(image=image.mdl(\uF583,default_icon_size) tip=['DuplexLandscapeTwoSidedShortEdgeMirrored',tip.info] cmd=command.copy('image.mdl(\uF583,12)'))
        item(image=image.mdl(\uF584,default_icon_size) tip=['DuplexPortraitOneSided',tip.info] cmd=command.copy('image.mdl(\uF584,12)'))
        item(image=image.mdl(\uF585,default_icon_size) tip=['DuplexPortraitOneSidedMirrored',tip.info] cmd=command.copy('image.mdl(\uF585,12)'))
        item(image=image.mdl(\uF586,default_icon_size) tip=['DuplexPortraitTwoSidedLongEdge',tip.info] cmd=command.copy('image.mdl(\uF586,12)'))
        item(image=image.mdl(\uF587,default_icon_size) tip=['DuplexPortraitTwoSidedLongEdgeMirrored',tip.info] cmd=command.copy('image.mdl(\uF587,12)'))
        item(image=image.mdl(\uF588,default_icon_size) tip=['DuplexPortraitTwoSidedShortEdge',tip.info] cmd=command.copy('image.mdl(\uF588,12)'))
        item(image=image.mdl(\uF589,default_icon_size) tip=['DuplexPortraitTwoSidedShortEdgeMirrored',tip.info] cmd=command.copy('image.mdl(\uF589,12)'))
        item(image=image.mdl(\uF58A,default_icon_size) tip=['PPSOneLandscape',tip.info] cmd=command.copy('image.mdl(\uF58A,12)'))
        item(image=image.mdl(\uF58B,default_icon_size) tip=['PPSTwoLandscape',tip.info] cmd=command.copy('image.mdl(\uF58B,12)'))
        item(image=image.mdl(\uF58C,default_icon_size) tip=['PPSTwoPortrait',tip.info] cmd=command.copy('image.mdl(\uF58C,12)'))
        item(image=image.mdl(\uF58D,default_icon_size) tip=['PPSFourLandscape',tip.info] cmd=command.copy('image.mdl(\uF58D,12)'))

        item(image=image.mdl(\uF58E,default_icon_size) tip=['PPSFourPortrait',tip.info] cmd=command.copy('image.mdl(\uF58E,12)') col)
        item(image=image.mdl(\uF58F,default_icon_size) tip=['HolePunchOff',tip.info] cmd=command.copy('image.mdl(\uF58F,12)'))
        item(image=image.mdl(\uF590,default_icon_size) tip=['HolePunchPortraitLeft',tip.info] cmd=command.copy('image.mdl(\uF590,12)'))
        item(image=image.mdl(\uF591,default_icon_size) tip=['HolePunchPortraitRight',tip.info] cmd=command.copy('image.mdl(\uF591,12)'))
        item(image=image.mdl(\uF592,default_icon_size) tip=['HolePunchPortraitTop',tip.info] cmd=command.copy('image.mdl(\uF592,12)'))
        item(image=image.mdl(\uF593,default_icon_size) tip=['HolePunchPortraitBottom',tip.info] cmd=command.copy('image.mdl(\uF593,12)'))
        item(image=image.mdl(\uF594,default_icon_size) tip=['HolePunchLandscapeLeft',tip.info] cmd=command.copy('image.mdl(\uF594,12)'))
        item(image=image.mdl(\uF595,default_icon_size) tip=['HolePunchLandscapeRight',tip.info] cmd=command.copy('image.mdl(\uF595,12)'))
        item(image=image.mdl(\uF596,default_icon_size) tip=['HolePunchLandscapeTop',tip.info] cmd=command.copy('image.mdl(\uF596,12)'))
        item(image=image.mdl(\uF597,default_icon_size) tip=['HolePunchLandscapeBottom',tip.info] cmd=command.copy('image.mdl(\uF597,12)'))
        item(image=image.mdl(\uF598,default_icon_size) tip=['StaplingOff',tip.info] cmd=command.copy('image.mdl(\uF598,12)'))
        item(image=image.mdl(\uF599,default_icon_size) tip=['StaplingPortraitTopLeft',tip.info] cmd=command.copy('image.mdl(\uF599,12)'))
        item(image=image.mdl(\uF59A,default_icon_size) tip=['StaplingPortraitTopRight',tip.info] cmd=command.copy('image.mdl(\uF59A,12)'))
        item(image=image.mdl(\uF59B,default_icon_size) tip=['StaplingPortraitBottomRight',tip.info] cmd=command.copy('image.mdl(\uF59B,12)'))
        item(image=image.mdl(\uF59C,default_icon_size) tip=['StaplingPortraitTwoLeft',tip.info] cmd=command.copy('image.mdl(\uF59C,12)'))
        item(image=image.mdl(\uF59D,default_icon_size) tip=['StaplingPortraitTwoRight',tip.info] cmd=command.copy('image.mdl(\uF59D,12)'))

        item(image=image.mdl(\uF59E,default_icon_size) tip=['StaplingPortraitTwoTop',tip.info] cmd=command.copy('image.mdl(\uF59E,12)') col)
        item(image=image.mdl(\uF59F,default_icon_size) tip=['StaplingPortraitTwoBottom',tip.info] cmd=command.copy('image.mdl(\uF59F,12)'))
        item(image=image.mdl(\uF5A0,default_icon_size) tip=['StaplingPortraitBookBinding',tip.info] cmd=command.copy('image.mdl(\uF5A0,12)'))
        item(image=image.mdl(\uF5A1,default_icon_size) tip=['StaplingLandscapeTopLeft',tip.info] cmd=command.copy('image.mdl(\uF5A1,12)'))
        item(image=image.mdl(\uF5A2,default_icon_size) tip=['StaplingLandscapeTopRight',tip.info] cmd=command.copy('image.mdl(\uF5A2,12)'))
        item(image=image.mdl(\uF5A3,default_icon_size) tip=['StaplingLandscapeBottomLeft',tip.info] cmd=command.copy('image.mdl(\uF5A3,12)'))
        item(image=image.mdl(\uF5A4,default_icon_size) tip=['StaplingLandscapeBottomRight',tip.info] cmd=command.copy('image.mdl(\uF5A4,12)'))
        item(image=image.mdl(\uF5A5,default_icon_size) tip=['StaplingLandscapeTwoLeft',tip.info] cmd=command.copy('image.mdl(\uF5A5,12)'))
        item(image=image.mdl(\uF5A6,default_icon_size) tip=['StaplingLandscapeTwoRight',tip.info] cmd=command.copy('image.mdl(\uF5A6,12)'))
        item(image=image.mdl(\uF5A7,default_icon_size) tip=['StaplingLandscapeTwoTop',tip.info] cmd=command.copy('image.mdl(\uF5A7,12)'))
        item(image=image.mdl(\uF5A8,default_icon_size) tip=['StaplingLandscapeTwoBottom',tip.info] cmd=command.copy('image.mdl(\uF5A8,12)'))
        item(image=image.mdl(\uF5A9,default_icon_size) tip=['StaplingLandscapeBookBinding',tip.info] cmd=command.copy('image.mdl(\uF5A9,12)'))
        item(image=image.mdl(\uF5AA,default_icon_size) tip=['StatusDataTransferRoaming',tip.info] cmd=command.copy('image.mdl(\uF5AA,12)'))
        item(image=image.mdl(\uF5AB,default_icon_size) tip=['MobSIMError',tip.info] cmd=command.copy('image.mdl(\uF5AB,12)'))
        item(image=image.mdl(\uF5AC,default_icon_size) tip=['CollateLandscapeSeparated',tip.info] cmd=command.copy('image.mdl(\uF5AC,12)'))
        item(image=image.mdl(\uF5AD,default_icon_size) tip=['PPSOnePortrait',tip.info] cmd=command.copy('image.mdl(\uF5AD,12)'))

        item(image=image.mdl(\uF5AE,default_icon_size) tip=['StaplingPortraitBottomLeft',tip.info] cmd=command.copy('image.mdl(\uF5AE,12)') col)
        item(image=image.mdl(\uF5B0,default_icon_size) tip=['PlaySolid',tip.info] cmd=command.copy('image.mdl(\uF5B0,12)'))
        item(image=image.mdl(\uF5E7,default_icon_size) tip=['RepeatOff',tip.info] cmd=command.copy('image.mdl(\uF5E7,12)'))
        item(image=image.mdl(\uF5ED,default_icon_size) tip=['Set',tip.info] cmd=command.copy('image.mdl(\uF5ED,12)'))
        item(image=image.mdl(\uF5EE,default_icon_size) tip=['SetSolid',tip.info] cmd=command.copy('image.mdl(\uF5EE,12)'))
        item(image=image.mdl(\uF5EF,default_icon_size) tip=['FuzzyReading',tip.info] cmd=command.copy('image.mdl(\uF5EF,12)'))
        item(image=image.mdl(\uF5F2,default_icon_size) tip=['VerticalBattery0',tip.info] cmd=command.copy('image.mdl(\uF5F2,12)'))
        item(image=image.mdl(\uF5F3,default_icon_size) tip=['VerticalBattery1',tip.info] cmd=command.copy('image.mdl(\uF5F3,12)'))
        item(image=image.mdl(\uF5F4,default_icon_size) tip=['VerticalBattery2',tip.info] cmd=command.copy('image.mdl(\uF5F4,12)'))
        item(image=image.mdl(\uF5F5,default_icon_size) tip=['VerticalBattery3',tip.info] cmd=command.copy('image.mdl(\uF5F5,12)'))
        item(image=image.mdl(\uF5F6,default_icon_size) tip=['VerticalBattery4',tip.info] cmd=command.copy('image.mdl(\uF5F6,12)'))
        item(image=image.mdl(\uF5F7,default_icon_size) tip=['VerticalBattery5',tip.info] cmd=command.copy('image.mdl(\uF5F7,12)'))
        item(image=image.mdl(\uF5F8,default_icon_size) tip=['VerticalBattery6',tip.info] cmd=command.copy('image.mdl(\uF5F8,12)'))
        item(image=image.mdl(\uF5F9,default_icon_size) tip=['VerticalBattery7',tip.info] cmd=command.copy('image.mdl(\uF5F9,12)'))
        item(image=image.mdl(\uF5FA,default_icon_size) tip=['VerticalBattery8',tip.info] cmd=command.copy('image.mdl(\uF5FA,12)'))
        item(image=image.mdl(\uF5FB,default_icon_size) tip=['VerticalBattery9',tip.info] cmd=command.copy('image.mdl(\uF5FB,12)'))
    }
