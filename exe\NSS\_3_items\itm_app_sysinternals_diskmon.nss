
//
$APP_USER_DISKMON_DIR = '@user.desktop\my\flow\home\__GOTO__\Apps\grp_sysinternalssuite\app_diskmon\exe'
$APP_USER_DISKMON_EXE = '@APP_USER_DISKMON_DIR\Diskmon64.exe'
$APP_USER_DISKMON_TIP = "..."+str.trimstart('@APP_USER_DISKMON_EXE','@app.dir')

// -> Diskmon
item(
    title=":  &Diskmon"
    keys="exe"
    type='Taskbar|File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    //
    image=APP_USER_DISKMON_EXE
    tip=[APP_USER_DISKMON_TIP,TIP3,0.5]
    //
    admin=(keys.rbutton() OR keys.lbutton())
    cmd=if(KEYS_EXE_OPEN_EXE,(APP_USER_DISKMON_EXE))
    args='/AcceptEula'
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_DISKMON_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_DISKMON_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_DISKMON_DIR')),
    }
)
