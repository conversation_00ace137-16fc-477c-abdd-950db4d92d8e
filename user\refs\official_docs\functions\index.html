﻿<h4>Functions</h4>
<p>The functions and variables have unique titles described by fully qualified names that indicate a logical hierarchy.</p>
<p><b>Shell</b> has a number of functions and variables built into it that are always available. They are listed here in alphabetical order.</p>

<section id="indexof" class="my-5">
	<h5>indexof</h5>
	<p>Find the position of a menu item.</p>
	<p>Syntax</p>
	<code>indexof(expression[, default position])</code>
</section>

<section id="if" class="my-5">
	<h5>if</h5>
	<p>Conditionally executes another statement.</p>
	<p>Syntax</p>
	<code>if(condition-expression)</code><br><br>
	<code>if(condition-expression, true-expression)</code><br><br>
	<code>if(condition-expression, true-expression, false-expression)</code>
</section>

<section id="null" class="my-5">
	<h5>null</h5>
	<p>Returns a null value.</p>
	<p>Syntax</p>
	<code>null(expression)</code><br><br>
</section>

<section id="length" class="my-5">
	<h5>length (len)</h5>
	<p>Returns length of string and array type</p>
	<p>Syntax</p>
	<code>length(expression)</code><br><br>
</section>

<section id="quote" class="my-5">
	<h5>quote</h5>
	<p>Returns text with a double quotation mark.</p>
	<p>Syntax</p>
	<code>quote(expression)</code>
</section>

<section id="char" class="my-5">
	<h5>char</h5>
	<p>Returns the value of the numeric parameter to a character.</p>
	<p>Syntax</p>
	<code>char(numeric-expression)</code>
</section>

<section id="var" class="my-5">
	<h5>var</h5>
	<p>Returns the value of the passed variable</p>
	<p>Syntax</p>
	<code>var(expression)</code>
</section>

<section id="tohex" class="my-5">
	<h5>tohex</h5>
	<p>Converting the value of the passed numeric expression to hexadecimal string.</p>
	<p>Syntax</p>
	<code>tohex(numeric-expression)</code>
</section>

<section id="equal" class="my-5">
	<h5>equal</h5>
	<p>Returns <code>true</code> if the parameters passed are equal.</p>
	<p>Syntax</p>
	<code>equal(expression-1, expression-2)</code>
</section>

<section id="not" class="my-5">
	<h5>not</h5>
	<p>Returns <code>true</code> if the parameters passed are not equal.</p>
	<p>Syntax</p>
	<code>not(expression-1, expression-2)</code>
</section>

<section id="greater" class="my-5">
	<h5>greater</h5>
	<p>Returns <code>true</code> if the first parameter is greater than the second parameter.</p>
	<p>Syntax</p>
	<code>greater(expression-1, expression-2)</code>
</section>

<section id="less" class="my-5">
	<h5>less</h5>
	<p>Returns <code>true</code> if the first parameter is less than the second parameter.</p>
	<p>Syntax</p>
	<code>less(expression-1, expression-2)</code>
</section>

<section id="shl" class="my-5">
	<h5>shl</h5>
	<p>bitwise left shift.</p>
	<p>Syntax</p>
	<code>shl(shift-expression, additive-expression)</code>
</section>

<section id="shr" class="my-5">
	<h5>shr</h5>
	<p>bitwise right shift.</p>
	<p>Syntax</p>
	<code>shr(shift-expression, additive-expression)</code>
</section>

<section id="cmd-visibility" class="my-5">
	<h5>cmd visibility enumerations</h5>
	<p>The flags that specify how an application is to be displayed when it is opened.</p>
	<pre><code>cmd.hidden
cmd.show
cmd.visible
cmd.normal
cmd.maximized
cmd.minimized</code></pre>
</section>

<section id="selection-modes" class="my-5">
	<h5>Selection mode enumerations</h5>
	<p>Syntax</p>
	<pre><code>mode.none
mode.single
mode.multiple
mode.multiunique
mode.multisingle
mode.multi</code></pre>
</section>

<section id="selection-types" class="my-5">
	<h5>Selection type enumerations</h5>
	<p>Syntax</p>
	<pre><code>type.desktop
type.directory(dir)
type.drive
type.dvd
type.file
type.fixed
type.namespace
type.remote
type.unknown
type.usb
type.vhd</code></pre>
</section>

<h5 id="keywords">Keywords</h5>
<pre><code>null
bool
true
false
auto
none
default</code>
</pre>

<div class="is-unstyled mt-5"><a href="/docs/functions/app">app</a> | <a href="/docs/functions/color">color</a> | <a href="/docs/functions/image">image</a> | <a href="/docs/functions/io">io</a> | <a href="/docs/functions/key">key</a> | <a href="/docs/functions/msg">msg</a> | <a href="/docs/functions/path">path</a> | <a href="/docs/functions/reg">reg</a> | <a href="/docs/functions/sel">sel</a> | <a href="/docs/functions/str">str</a> | <a href="/docs/functions/sys">sys</a> | <a href="/docs/functions/user">user</a>| <a href="/docs/functions/input">input</a>
| <a href="/docs/functions/ini">ini</a> | <a href="/docs/functions/clipboard">clipboard</a>
</div>

