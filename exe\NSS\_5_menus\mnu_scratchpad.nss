
// menu(title='&Scratchpad @"\t"' type='Taskbar|File|Desktop|Dir|Drive|Back.Dir|Back.Drive' image=[E0E8,SOFT] image-sel=[E0E8,WHITE] sep='None') {
menu(title="Scratchpad" type='Taskbar|File|Desktop|Dir|Drive|Back.Dir|Back.Drive' image=[E0E8,WHITE1] image-sel=[E0E8,WHITE] sep='None') {

    // user: files
    // =============================================================================
    //
    separator()
    item(column vis='Static' title="STICKY" image=[E00A,DARK])
    separator()

    $stickynotesmd = path.full('@user.desktop\my\flow\home\__GOTO__\Scripts\Python\Py_Cli_Utils\py__ProjectGenerator\src\templates\prompts\unsorted_gpt_prompts\STICKY_SCRATCHPAD.md')
    item(
        keys="2025.04.07"
        type='Taskbar|File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
        image=[E10A,BLUE]
        image-sel='@user.desktop\my\flow\home\__GOTO__\Apps\app_sublimetext\exe\sublime_text.exe'
        //
        title=path.name(stickynotesmd)
        tip=[stickynotesmd,TIP5,0.5]
        commands{
            cmd=if(KEYS_DIR_COPY,clipboard.set(stickynotesmd)),
            cmd=if(KEYS_DIR_GOTO,command.navigate(path.parent(stickynotesmd))),
            cmd=if(KEYS_EXE_OPEN_DIR,(path.parent(stickynotesmd))),
            cmd=if(KEYS_EXE_OPEN_EXE,stickynotesmd),
        }
    )
    $stickynotes = path.full('@user.desktop\my\flow\home\__GOTO__\Scripts\Python\Py_Cli_Utils\py__ProjectGenerator\src\templates\prompts\unsorted_gpt_prompts\STICKY_NOTES.py')
    item(
        keys="2024.12.09"
        type='Taskbar|File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
        image=[E10A,WHITE]
        image-sel='@user.desktop\my\flow\home\__GOTO__\Apps\app_sublimetext\exe\sublime_text.exe'
        //
        title=path.name(stickynotes)
        tip=[stickynotes,TIP5,0.5]
        commands{
            cmd=if(KEYS_DIR_COPY,clipboard.set(stickynotes)),
            cmd=if(KEYS_DIR_GOTO,command.navigate(path.parent(stickynotes))),
            cmd=if(KEYS_EXE_OPEN_DIR,(path.parent(stickynotes))),
            cmd=if(KEYS_EXE_OPEN_EXE,stickynotes),
        }
    )
    $stickylog = path.full('@user.desktop\my\flow\home\__GOTO__\Scripts\Python\Py_Cli_Utils\py__ProjectGenerator\src\templates\prompts\unsorted_gpt_prompts\STICKY_LOG.py')
    item(
        keys="2024.12.09"
        type='Taskbar|File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
        image=[E10A,SOFT]
        image-sel='@user.desktop\my\flow\home\__GOTO__\Apps\app_sublimetext\exe\sublime_text.exe'
        //
        title=path.name(stickylog)
        tip=[stickylog,TIP5,0.5]
        commands{
            cmd=if(KEYS_DIR_COPY,clipboard.set(stickylog)),
            cmd=if(KEYS_DIR_GOTO,command.navigate(path.parent(stickylog))),
            cmd=if(KEYS_EXE_OPEN_DIR,(path.parent(stickylog))),
            cmd=if(KEYS_EXE_OPEN_EXE,stickylog),
        }
    )

    // dirs
    // =============================================================================
    separator()
    item(/*column*/ vis='Static' title="Folders" image=[E00A,DARK])
    // item(column vis='Static' title="Frequent Dirs" image=[E00A,DARK])
    separator()

    //
    $blender_tutorials = path.full('@user.desktop\my\flow\home\__GOTO__\Apps\app_blender\user\blender_tutorials')
    item(
        title="&blender_tutorials"
        keys="2024.10.18"
        type='Taskbar|File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
        image=[E0E7,SOFT] image-sel=[E0E8,WHITE]
        //
        commands{
            cmd=if(KEYS_DIR_COPY,clipboard.set('@blender_tutorials')),
            cmd=if(KEYS_DIR_GOTO,command.navigate('@blender_tutorials')),
            cmd=if(KEYS_DIR_OPEN,('@blender_tutorials')),
        }
        tip=['@blender_tutorials',TIP5,0.5]
    )

    $DIR_3DSMAX_LOCALAPPDATA = path.full('@user.localappdata/Autodesk/3dsMax/2025 - 64bit')
    item(title="3dsMax.&localappdata"
        keys="/"
        image=[E0E8,GREY]
        image-sel=[E0E8,GREEN]
        tip=['@DIR_3DSMAX_LOCALAPPDATA','@TIP1',0.0]
        commands{
            cmd=if('@KEYS_DIR_COPY',clipboard.set('@DIR_3DSMAX_LOCALAPPDATA')),
            cmd=if('@KEYS_DIR_GOTO',command.navigate('@DIR_3DSMAX_LOCALAPPDATA')),
            cmd=if('@KEYS_DIR_OPEN',('@DIR_3DSMAX_LOCALAPPDATA')),
        }
    )

    $DIR_3DSMAX_3DPRINT = path.full('@user.desktop\PRJ\NAS_WORKFLOW\_3D\3D_PRINT')
    item(title="3D PRINT"
        keys="/"
        image=[E0E8,GREY]
        image-sel=[E0E8,GREEN]
        tip=['@DIR_3DSMAX_3DPRINT','@TIP1',0.0]
        commands{
            cmd=if('@KEYS_DIR_COPY',clipboard.set('@DIR_3DSMAX_3DPRINT')),
            cmd=if('@KEYS_DIR_GOTO',command.navigate('@DIR_3DSMAX_3DPRINT')),
            cmd=if('@KEYS_DIR_OPEN',('@DIR_3DSMAX_3DPRINT')),
        }
    )

    $DIR_GOOGLEDRIVE = path.full('G:\My Drive')
    item(title="GoogleDrive"
        keys="/"
        image=[E0E8,GREY]
        image-sel='@user.desktop\my\flow\home\__GOTO__\Apps\app_googledrive\versions\GoogleDriveSetup.exe'
        tip=['@DIR_GOOGLEDRIVE','@TIP1',0.0]
        commands{
            cmd=if('@KEYS_DIR_COPY',clipboard.set('@DIR_GOOGLEDRIVE')),
            cmd=if('@KEYS_DIR_GOTO',command.navigate('@DIR_GOOGLEDRIVE')),
            cmd=if('@KEYS_DIR_OPEN',('@DIR_GOOGLEDRIVE')),
        }
    )
    separator()

    $DIR_RINGERIKELANDSKAPV2 = path.full('@user.desktop\my\flow\home\__GOTO__\Projects\prj_ringerikelandskap\prj\rlweb_v2')
    item(title="RingerikeLandskap: rl-website-v2"
        keys="/"
        image=[E0E8,GREY]
        image-sel=[E0E8,WHITE]
        tip=['@DIR_RINGERIKELANDSKAPV2','@TIP1',0.0]
        commands{
            cmd=if('@KEYS_DIR_COPY',clipboard.set('@DIR_RINGERIKELANDSKAPV2')),
            cmd=if('@KEYS_DIR_GOTO',command.navigate('@DIR_RINGERIKELANDSKAPV2')),
            cmd=if('@KEYS_DIR_OPEN',('@DIR_RINGERIKELANDSKAPV2')),
        }
    )




    // user: files
    // =============================================================================
    //
    separator()
    item(/*column*/ vis='Static' title="Files" image=[E00A,DARK])
    separator()

    $subpromptsnotes = path.full('@user.desktop\my\flow\home\__GOTO__\Scripts\Python\Py_Cli_Utils\py__ProjectGenerator\src\templates\prompts\unsorted_gpt_prompts\sub-prompts-notes.py')
    item(
        keys="2024.12.09"
        type='Taskbar|File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
        image=[E10A,BLUE]
        image-sel='@user.desktop\my\flow\home\__GOTO__\Apps\app_sublimetext\exe\sublime_text.exe'
        //
        title=path.name(subpromptsnotes)
        tip=[subpromptsnotes,TIP5,0.5]
        commands{
            cmd=if(KEYS_DIR_COPY,clipboard.set(subpromptsnotes)),
            cmd=if(KEYS_DIR_GOTO,command.navigate(path.parent(subpromptsnotes))),
            cmd=if(KEYS_EXE_OPEN_DIR,(path.parent(subpromptsnotes))),
            cmd=if(KEYS_EXE_OPEN_EXE,subpromptsnotes),
        }
    )
    $kucisubpromptsnotes = path.full('@user.desktop\my\flow\home\__GOTO__\Scripts\Python\Py_Cli_Utils\py__ProjectGenerator\src\templates\prompts\unsorted_gpt_prompts\kuci-sub-prompts-notes.md')
    item(
        keys="2024.12.09"
        type='Taskbar|File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
        image=[E10A,PINK]
        image-sel='@user.desktop\my\flow\home\__GOTO__\Apps\app_sublimetext\exe\sublime_text.exe'
        //
        title=path.name(kucisubpromptsnotes)
        tip=[kucisubpromptsnotes,TIP5,0.5]
        commands{
            cmd=if(KEYS_DIR_COPY,clipboard.set(kucisubpromptsnotes)),
            cmd=if(KEYS_DIR_GOTO,command.navigate(path.parent(kucisubpromptsnotes))),
            cmd=if(KEYS_EXE_OPEN_DIR,(path.parent(kucisubpromptsnotes))),
            cmd=if(KEYS_EXE_OPEN_EXE,kucisubpromptsnotes),
        }
    )

    $subpromptsnotesunsorted = path.full('@user.desktop\my\flow\home\__GOTO__\Scripts\Python\Py_Cli_Utils\py__ProjectGenerator\src\templates\prompts\unsorted_gpt_prompts\sub-prompts-notes-unsorted.py')
    item(
        keys="2024.12.09"
        type='Taskbar|File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
        image=[E10A,PURPLE]
        image-sel='@user.desktop\my\flow\home\__GOTO__\Apps\app_sublimetext\exe\sublime_text.exe'
        //
        title=path.name(subpromptsnotesunsorted)
        tip=[subpromptsnotesunsorted,TIP5,0.5]
        commands{
            cmd=if(KEYS_DIR_COPY,clipboard.set(subpromptsnotesunsorted)),
            cmd=if(KEYS_DIR_GOTO,command.navigate(path.parent(subpromptsnotesunsorted))),
            cmd=if(KEYS_EXE_OPEN_DIR,(path.parent(subpromptsnotesunsorted))),
            cmd=if(KEYS_EXE_OPEN_EXE,subpromptsnotesunsorted),
        }
    )
    $unsortedprompts = path.full('@user.desktop\my\flow\home\__GOTO__\Scripts\Python\Py_Cli_Utils\py__ProjectGenerator\src\templates\prompts\unsorted_gpt_prompts\unsorted-prompts.md')
    item(
        keys="2024.12.09"
        type='Taskbar|File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
        image=[E10A,PURPLE]
        image-sel='@user.desktop\my\flow\home\__GOTO__\Apps\app_sublimetext\exe\sublime_text.exe'
        //
        title=path.name(unsortedprompts)
        tip=[unsortedprompts,TIP5,0.5]
        commands{
            cmd=if(KEYS_DIR_COPY,clipboard.set(unsortedprompts)),
            cmd=if(KEYS_DIR_GOTO,command.navigate(path.parent(unsortedprompts))),
            cmd=if(KEYS_EXE_OPEN_DIR,(path.parent(unsortedprompts))),
            cmd=if(KEYS_EXE_OPEN_EXE,unsortedprompts),
        }
    )


    // user: sublimeprojects
    // =============================================================================
    separator()
    item(/*column*/ vis='Static' title="Sublime Projects" image=[E00A,DARK])
    separator()

    //
    $sublimeprj_shell = path.full('@user.desktop\my\flow\home\__SHELL__\__SHELL__.sublime-project')
    item(
        keys="2024.10.18"
        type='Taskbar|File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
        image='@user.desktop\my\flow\home\__GOTO__\Apps\app_sublimetext\exe\sublime_text.exe'
        //
        title=path.name(sublimeprj_shell)
        tip=[sublimeprj_shell,TIP5,0.5]
        commands{
            cmd=if(KEYS_DIR_COPY,clipboard.set(sublimeprj_shell)),
            cmd=if(KEYS_DIR_GOTO,command.navigate(path.parent(sublimeprj_shell))),
            cmd=if(KEYS_EXE_OPEN_DIR,(path.parent(sublimeprj_shell))),
            cmd=if(KEYS_EXE_OPEN_EXE,sublimeprj_shell),
        }
    )



    //
    $sublimeprj_ai = path.full('@user.desktop\my\flow\home\__GOTO__\Scripts\Python\Py_Ai_Utils\ai\ai.sublime-project')
    item(
        keys="14.01.2025"
        type='Taskbar|File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
        image='@user.desktop\my\flow\home\__GOTO__\Apps\app_sublimetext\exe\sublime_text.exe'
        //
        title=path.name(sublimeprj_ai)
        tip=[sublimeprj_ai,TIP5,0.5]
        commands{
            cmd=if(KEYS_DIR_COPY,clipboard.set(sublimeprj_ai)),
            cmd=if(KEYS_DIR_GOTO,command.navigate(path.parent(sublimeprj_ai))),
            cmd=if(KEYS_EXE_OPEN_DIR,(path.parent(sublimeprj_ai))),
            cmd=if(KEYS_EXE_OPEN_EXE,sublimeprj_ai),
        }
    )



    //
    $sublimeprj_perspektiv = path.full('@user.desktop\PRJ\ANDROID_CLOUD\prj__Perspektiv\prj__Perspektiv.sublime-project')
    item(
        keys="14.01.2025"
        type='Taskbar|File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
        image='@user.desktop\my\flow\home\__GOTO__\Apps\app_sublimetext\exe\sublime_text.exe'
        //
        title=path.name(sublimeprj_perspektiv)
        tip=[sublimeprj_perspektiv,TIP5,0.5]
        commands{
            cmd=if(KEYS_DIR_COPY,clipboard.set(sublimeprj_perspektiv)),
            cmd=if(KEYS_DIR_GOTO,command.navigate(path.parent(sublimeprj_perspektiv))),
            cmd=if(KEYS_EXE_OPEN_DIR,(path.parent(sublimeprj_perspektiv))),
            cmd=if(KEYS_EXE_OPEN_EXE,sublimeprj_perspektiv),
        }
    )

    //
    $sublimeprj_ringerikelandskap = path.full('@user.desktop\my\flow\home\__GOTO__\Personal\Ringerike Landskap\Ringerike Landskap.sublime-project')
    item(
        keys="2024.10.18"
        type='Taskbar|File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
        image='@user.desktop\my\flow\home\__GOTO__\Apps\app_sublimetext\exe\sublime_text.exe'
        //
        title=path.name(sublimeprj_ringerikelandskap)
        tip=[sublimeprj_ringerikelandskap,TIP5,0.5]
        commands{
            cmd=if(KEYS_DIR_COPY,clipboard.set(sublimeprj_ringerikelandskap)),
            cmd=if(KEYS_DIR_GOTO,command.navigate(path.parent(sublimeprj_ringerikelandskap))),
            cmd=if(KEYS_EXE_OPEN_DIR,(path.parent(sublimeprj_ringerikelandskap))),
            cmd=if(KEYS_EXE_OPEN_EXE,sublimeprj_ringerikelandskap),
        }
    )

    //
    $sublimeprj_blenderscripts = path.full('@user.desktop\my\flow\home\__GOTO__\Apps\app_blender\user\blender_scripts_dir\blender_scripts_dir.sublime-project')
    item(
        keys="2024.10.18"
        type='Taskbar|File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
        image='@user.desktop\my\flow\home\__GOTO__\Apps\app_sublimetext\exe\sublime_text.exe'
        //
        title=path.name(sublimeprj_blenderscripts)
        tip=[sublimeprj_blenderscripts,TIP5,0.5]
        commands{
            cmd=if(KEYS_DIR_COPY,clipboard.set(sublimeprj_blenderscripts)),
            cmd=if(KEYS_DIR_GOTO,command.navigate(path.parent(sublimeprj_blenderscripts))),
            cmd=if(KEYS_EXE_OPEN_DIR,(path.parent(sublimeprj_blenderscripts))),
            cmd=if(KEYS_EXE_OPEN_EXE,sublimeprj_blenderscripts),
        }
    )

    //
    $py__ProjectGenerator = path.full('@user.desktop\my\flow\home\__GOTO__\Scripts\Python\Py_Cli_Utils\py__ProjectGenerator\py__ProjectGenerator.sublime-project')
    item(
        keys="2024.10.18"
        type='Taskbar|File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
        image='@user.desktop\my\flow\home\__GOTO__\Apps\app_sublimetext\exe\sublime_text.exe'
        //
        title=path.name(py__ProjectGenerator)
        tip=[py__ProjectGenerator,TIP5,0.5]
        commands{
            cmd=if(KEYS_DIR_COPY,clipboard.set(py__ProjectGenerator)),
            cmd=if(KEYS_DIR_GOTO,command.navigate(path.parent(py__ProjectGenerator))),
            cmd=if(KEYS_EXE_OPEN_DIR,(path.parent(py__ProjectGenerator))),
            cmd=if(KEYS_EXE_OPEN_EXE,py__ProjectGenerator),
        }
    )

    //
    $py__MyDataRetriever = path.full('@user.desktop\my\flow\home\__GOTO__\Scripts\Python\Py_Cli_Utils\py__MyDataRetriever\py__MyDataRetriever.sublime-project')
    item(
        keys="2024.11.16"
        type='Taskbar|File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
        image='@user.desktop\my\flow\home\__GOTO__\Apps\app_sublimetext\exe\sublime_text.exe'
        //
        title=path.name(py__MyDataRetriever)
        tip=[py__MyDataRetriever,TIP5,0.5]
        commands{
            cmd=if(KEYS_DIR_COPY,clipboard.set(py__MyDataRetriever)),
            cmd=if(KEYS_DIR_GOTO,command.navigate(path.parent(py__MyDataRetriever))),
            cmd=if(KEYS_EXE_OPEN_DIR,(path.parent(py__MyDataRetriever))),
            cmd=if(KEYS_EXE_OPEN_EXE,py__MyDataRetriever),
        }
    )

    // todo
    // =============================================================================
    separator()
    item(column vis='Static' title="Todo" image=[E00A,RED])
    separator()

    // sublimeprojects
    // =============================================================================
    separator()
    item(/*column*/ vis='Static' title="Projects Todo" image=[E00A,RED_SOFT])
    separator()

    //
    $rssfeedgeneratorprj = path.full('@user.desktop\my\flow\home\__GOTO__\Scripts\Python\Py_Utils_Todo\RSSFeedGenerator\RSSFeedGenerator.sublime-project')
    item(
        keys="2024.10.20"
        type='Taskbar|File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
        image='@user.desktop\my\flow\home\__GOTO__\Apps\app_sublimetext\exe\sublime_text.exe'
        //
        title=path.name(rssfeedgeneratorprj)
        tip=[rssfeedgeneratorprj,TIP5,0.5]
        commands{
            cmd=if(KEYS_DIR_COPY,clipboard.set(rssfeedgeneratorprj)),
            cmd=if(KEYS_DIR_GOTO,command.navigate(path.parent(rssfeedgeneratorprj))),
            cmd=if(KEYS_EXE_OPEN_DIR,(path.parent(rssfeedgeneratorprj))),
            cmd=if(KEYS_EXE_OPEN_EXE,rssfeedgeneratorprj),
        }
    )

}
