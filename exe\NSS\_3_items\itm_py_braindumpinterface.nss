
//
$BRAINDUMPS_OUTPUT_DIR = '@user.desktop\my\flow\home\__GOTO__\Scripts\Python\Py_Cli_Utils\py__BraindumpInterface'

$PY_BRAINDUMPINTERFACE_DIR = '@user.desktop\my\flow\home\__GOTO__\Scripts\Python\Py_Cli_Utils\py__BraindumpInterface'
$PY_BRAINDUMPINTERFACE_EXE = '@PY_BRAINDUMPINTERFACE_DIR\venv\Scripts\python.exe'
$PY_BRAINDUMPINTERFACE_APP = '@PY_BRAINDUMPINTERFACE_DIR\src\markdown_generator.py'


// Context: Taskbar
$PY_BRAINDUMPINTERFACE_MARKDOWN = '-d "@BRAINDUMPS_OUTPUT_DIR\dumps" -s "braindump" -o'
item(
    title="dump: &markdown"
    keys="Braindump"
    type='Taskbar'
    //
    image=[E17C,BLUE3]
    image-sel=[<PERSON>17<PERSON>,<PERSON><PERSON><PERSON>]
    //
    admin=keys.rbutton()
    cmd=if(KEYS_EXE_OPEN_EXE,('"@PY_BRAINDUMPINTERFACE_EXE"'))
    args='"@PY_BRAINDUMPINTERFACE_APP" @PY_BRAINDUMPINTERFACE_MARKDOWN'
    tip=['"@PY_BRAINDUMPINTERFACE_APP" @PY_BRAINDUMPINTERFACE_MARKDOWN',TIP3,0.75]
    //
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@PY_BRAINDUMPINTERFACE_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@PY_BRAINDUMPINTERFACE_APP')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@PY_BRAINDUMPINTERFACE_DIR')),
    }
)

$PY_BRAINDUMPINTERFACE_PYTHON = '-d "@BRAINDUMPS_OUTPUT_DIR\dumps" -s "py" -o'
item(
    title="dump: &python"
    keys="Braindump"
    type='Taskbar'
    //
    image=[E17C,ORANGE]
    image-sel=[E17C,GREEN]
    //
    admin=keys.rbutton()
    cmd=if(KEYS_EXE_OPEN_EXE,('"@PY_BRAINDUMPINTERFACE_EXE"'))
    args='"@PY_BRAINDUMPINTERFACE_APP" @PY_BRAINDUMPINTERFACE_PYTHON'
    tip=['"@PY_BRAINDUMPINTERFACE_APP" @PY_BRAINDUMPINTERFACE_PYTHON',TIP3,0.75]
    //
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@PY_BRAINDUMPINTERFACE_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@PY_BRAINDUMPINTERFACE_APP')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@PY_BRAINDUMPINTERFACE_DIR')),
    }
)



// // Context: Explorer
// $PY_BRAINDUMPINTERFACE_EXPLORER = '"@sel.dir" --prompt'
// item(
//     title="* &BraindumpINTERFACE"
//     keys="py"
//     type='File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
//     //
//     image=[E17C,BLUE3] image-sel=[E17C,PURPLE]
//     tip=['"@PY_BRAINDUMPINTERFACE_APP" @PY_BRAINDUMPINTERFACE_EXPLORER',TIP3,0.75]
//     //
//     admin=keys.rbutton()
//     cmd=if(KEYS_EXE_OPEN_EXE,('"@PY_BRAINDUMPINTERFACE_EXE"'))
//     args='"@PY_BRAINDUMPINTERFACE_APP" @PY_BRAINDUMPINTERFACE_EXPLORER'
//     //
//     commands{
//         cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@PY_BRAINDUMPINTERFACE_DIR')),
//         cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@PY_BRAINDUMPINTERFACE_APP')),
//         cmd=if(KEYS_EXE_OPEN_DIR,('@PY_BRAINDUMPINTERFACE_DIR')),
//     }
// )
