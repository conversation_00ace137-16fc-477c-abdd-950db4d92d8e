
// docs: `...`


/*
    :: predefined colors

    :: usage: `image=["\uE23B",LOWKEY] image-sel=["\uE23B",HOVER]`
*/


$WHITE1 = #B4BFE4


// -> generalized colors
$LOWKEY        = #E0D9EA
$SOFT          = #8D8199
$DARK          = #46466F

// -> categorized colors
$HOVER         = #FFFA00

// -> specific colors
$BLUE1         = #1A377D
$BLUE2         = #285CF1
$BLUE3         = #3893FF
$BLUE4         = #75B4FF

$BLUE          = #3893FF
// $BLUE_DARK     = #285CF1
//
$WHITE         = #FFFFFF
$BLUE2         = #34B6FF80
//
$RED           = #FF1C1A
$RED_SOFT      = #FF4642
$PINK_BRIGHT   = #FF4DAE
$PINK          = #FF00FF
$PURPLE        = #A457FF
$PURPLE1       = #DC4DFF
$PURPLE2       = #A457FF80
//
$BLACK         = #000000
//
$GREEN         = #39C65A
$GREEN2        = #34FF6580
//
$YELLOW_BRIGHT = #FFFA00
$YELLOW        = #FFD420
$YELLOW2        = #FFD42080
$ORANGE        = #FF904D
$ORANGE2       = #FF904D80

//
$PINK2         = #FF00FF60
$RED2          = #F7A1A1B3
$GREY          = #717482

// -> new

// $YELLOW1 = #FFFA00 // bright
// $YELLOW2 = #FFD420 // normal
// $YELLOW3 = #D6B426 // subtle/soft
