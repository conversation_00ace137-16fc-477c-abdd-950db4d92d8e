@ECHO OFF
SETLOCAL ENABLEDELAYEDEXPANSION
IF EXIST "%~1" (CD /D "%~1") ELSE (CD /D "%~dp0")

@echo off

:: set "file_extension=.gitignore"
:: set "program_path=C:\Users\<USER>\Desktop\PRJ\GIT_WORKFLOW\APPS\app_nfopad\app\NFOPad.exe"
:: :: assoc %file_extension%=Textfile
:: :: ftype Textfile="C:\Users\<USER>\Desktop\PRJ\GIT_WORKFLOW\APPS\app_nfopad\app\NFOPad.exe" "%%1"
:: :: exit /b 0
:: REM Set default program for .gitignore files
:: reg add HKCU\Software\Classes\%file_extension% /f /ve /d Textfile > nul
:: reg add HKCU\Software\Classes\%file_extension%\Shell\Open\Command /f /ve /d "\"%program_path%\" \"%%1\"" > nul
:: echo Default program for %file_extension% set to %program_path%
:: exit /b 0



:: set PythonDIR=C:\Python27
:: set PATH=%PythonDIR%;%PythonDIR%\Scripts;%PATH%
:: set PYTHONPATH=%PythonDIR%\Lib;%PythonDIR%\Lib\site-packages;%PythonDIR%\DLLs;
:: set PATHEXT=%PATHEXT%;.PY;.PYW
:: assoc .py=Python.File>NUL
:: assoc .pyw=PythonW.File>NUL
:: ftype Python.File="%PythonDIR%\python.exe" %%1 %%*>NUL
:: ftype PythonW.File="%PythonDIR%\pythonw.exe" %%1 %%*>NUL
:: pause



@echo off
REM Set the path to NFOPad executable
set "NFOPadExe=C:\Users\<USER>\Desktop\PRJ\GIT_WORKFLOW\APPS\exe_winmerge\app\WinMerge\WinMergeU.exe"

REM Associate .nfo files with NFOPad
assoc .nfo=NFOpad.File

REM Set the file type and command to open .nfo files with NFOPad
ftype NFOpad.File="%NFOPadExe%" "%%1"

reg add HKCU\Software\Classes\.nfo /f /ve /d Textfile > nul
reg add HKCU\Software\Classes\.nfo\Shell\Open\Command /f /ve /d "\"%NFOPadExe%\" \"%%1\"" > nul

:: echo File associations updated successfully.
pause
