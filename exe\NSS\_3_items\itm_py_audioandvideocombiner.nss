
//
$PY_AUDIOANDVIDEOCOMBINER_DIR = '@user.desktop\my\flow\home\__GOTO__\Scripts\Python\Py_Cli_Utils\py__AudioAndVideoCombiner'
$PY_AUDIOANDVIDEOCOMBINER_EXE = '@PY_AUDIOANDVIDEOCOMBINER_DIR\venv\Scripts\python.exe'
$PY_AUDIOANDVIDEOCOMBINER_APP = '@PY_AUDIOANDVIDEOCOMBINER_DIR\main.py'
//

// Context: Explorer
$PY_AUDIOANDVIDEOCOMBINER_ARGS = '"@sel.file" --prompt'
item(
    title="&AudioAndVideoCombiner"
    keys="py"
    type='File'
    where=str.equals(sel.file.ext,[".m4a",".webm",".wmv",".wav",".f4v",".mov",".mkv",".mp4"])
    //
    image=[E17C,GREY] image-sel=[E17<PERSON>,PURPLE]
    tip=['"@PY_AUDIOANDVIDEOCOMBINER_APP" @PY_AUDIOANDVIDEOCOMBINER_ARGS',TIP3,0.75]
    //
    admin=keys.rbutton()
    cmd=if(KEYS_EXE_OPEN_EXE,('"@PY_AUDIOANDVIDEOCOMBINER_EXE"'))
    args='"@PY_AUDIOANDVIDEOCOMBINER_APP" @PY_AUDIOANDVIDEOCOMBINER_ARGS'
    //
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@PY_AUDIOANDVIDEOCOMBINER_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@PY_AUDIOANDVIDEOCOMBINER_APP')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@PY_AUDIOANDVIDEOCOMBINER_DIR')),
    }
)

// Context: Titlebar|Taskbar
$PY_AUDIOANDVIDEOCOMBINER_TASKBAR = '--prompt'
item(
    title="&AudioAndVideoCombiner"
    keys="py"
    type='Titlebar|Taskbar'
    //
    image=[E17C,GREY] image-sel=[E17C,PURPLE]
    tip=['"@PY_AUDIOANDVIDEOCOMBINER_APP" @PY_AUDIOANDVIDEOCOMBINER_TASKBAR',TIP3,0.75]
    //
    admin=keys.rbutton()
    args='"@PY_AUDIOANDVIDEOCOMBINER_APP" @PY_AUDIOANDVIDEOCOMBINER_TASKBAR'
    cmd=if(KEYS_EXE_OPEN_EXE,('"@PY_AUDIOANDVIDEOCOMBINER_EXE"'))
    //
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@PY_AUDIOANDVIDEOCOMBINER_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@PY_AUDIOANDVIDEOCOMBINER_APP')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@PY_AUDIOANDVIDEOCOMBINER_DIR')),
    }
)
