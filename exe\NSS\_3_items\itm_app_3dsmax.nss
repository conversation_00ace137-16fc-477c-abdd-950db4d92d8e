
//
$APP_3DSMAX_DIR = '@sys.prog\Autodesk\3ds Max 2025'
$APP_3DSMAX_EXE = '@APP_3DSMAX_DIR\3dsmax.exe'
$APP_3DSMAX_TIP = "..."+str.trimstart('@APP_3DSMAX_EXE','@app.dir')
//
$APP_3DSMAX_DIR_CFG = '@user.localappdata\Autodesk\3dsMax\2025 - 64bit'
$APP_3DSMAX_DIR_NSS = '@app.dir\NSS\_3_items\user_apps'
$APP_3DSMAX_DIR_SRC = '@user.desktop\my\flow\home\__GOTO__\Apps\app_3dsmax'



// Context: File
item(
    title  = ":  &3dsMax"
    keys   = "exe"
    type   = 'File'
    args   = '"@sel.file"'
    where  = path.exists(APP_3DSMAX_EXE) && str.equals(sel.file.ext,[".max"])
    //
    image  = APP_3DSMAX_EXE
    tip    = [APP_3DSMAX_TIP,TIP3,1.0]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_3DSMAX_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_3DSMAX_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_3DSMAX_EXE')),
        cmd=if(KEYS_EXE_GOTO_CFG,('@APP_3DSMAX_DIR_CFG')),
        cmd=if(KEYS_EXE_GOTO_NSS,('@APP_3DSMAX_DIR_NSS')),
        cmd=if(KEYS_EXE_GOTO_SRC,('@APP_3DSMAX_DIR_SRC')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_3DSMAX_DIR')),
    }
)
// Context: Directory
item(
    title  = ":  &3dsMax"
    keys   = "exe"
    type   = 'Desktop|Dir|Drive|Back.Dir|Back.Drive'
    args   = '--local="@sel.dir"'
    where  = path.exists(APP_3DSMAX_EXE)
    //
    image  = APP_3DSMAX_EXE
    tip    = [APP_3DSMAX_TIP,TIP3,1.0]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_3DSMAX_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_3DSMAX_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_3DSMAX_EXE')),
        cmd=if(KEYS_EXE_GOTO_CFG,('@APP_3DSMAX_DIR_CFG')),
        cmd=if(KEYS_EXE_GOTO_NSS,('@APP_3DSMAX_DIR_NSS')),
        cmd=if(KEYS_EXE_GOTO_SRC,('@APP_3DSMAX_DIR_SRC')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_3DSMAX_DIR')),
    }
)
// Context: Taskbar
item(
    title  = ":  &3dsMax"
    keys   = "exe"
    type   = 'Taskbar'
    args   = ''
    where  = path.exists(APP_3DSMAX_EXE)
    //
    image  = APP_3DSMAX_EXE
    tip    = [APP_3DSMAX_TIP,TIP3,1.0]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_3DSMAX_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_3DSMAX_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_3DSMAX_EXE')),
        cmd=if(KEYS_EXE_GOTO_CFG,('@APP_3DSMAX_DIR_CFG')),
        cmd=if(KEYS_EXE_GOTO_NSS,('@APP_3DSMAX_DIR_NSS')),
        cmd=if(KEYS_EXE_GOTO_SRC,('@APP_3DSMAX_DIR_SRC')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_3DSMAX_DIR')),
    }
)

// //
// $APP_USER_3DSMAX_DIR = '@user.desktop\my\flow\home\__GOTO__\Apps\app_3dsmax'
// $APP_USER_3DSMAX_EXE = '@APP_USER_3DSMAX_DIR\Bulk Rename Utility.exe'
// $APP_USER_3DSMAX_TIP = "..."+str.trimstart('@APP_USER_3DSMAX_EXE','@app.dir')

// // Context: Explorer
// item(
//     title  = ":  &3dsMax"
//     keys   = "exe"
//     type   = 'File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
//     args   = '"@sel.dir"'
//     //
//     image  = APP_USER_3DSMAX_EXE
//     tip    = [APP_USER_3DSMAX_TIP,TIP3,0.8]
//     //
//     admin  = keys.rbutton()
//     cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_USER_3DSMAX_EXE"'))
//     commands{
//         cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_3DSMAX_DIR')),
//         cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_3DSMAX_EXE')),
//         cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_3DSMAX_DIR')),
//     }
// )
// // Context: Taskbar
// item(
//     title  = ":  &3dsMax"
//     keys   = "exe"
//     type   = 'Taskbar'
//     args   = '@user.desktop'
//     //
//     image  = APP_USER_3DSMAX_EXE
//     tip    = [APP_USER_3DSMAX_TIP,TIP3,0.8]
//     //
//     admin  = keys.rbutton()
//     cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_USER_3DSMAX_EXE"'))
//     commands{
//         cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_3DSMAX_DIR')),
//         cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_3DSMAX_EXE')),
//         cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_3DSMAX_DIR')),
//     }
// )
