
//
$PY_YOUTUBEPLAYLISTMANAGER_DIR = '@user.desktop\my\flow\home\__GOTO__\Scripts\Python\Py_Cli_Utils\py__YoutubePlaylistManager'
$PY_YOUTUBEPLAYLISTMANAGER_EXE = '@PY_YOUTUBEPLAYLISTMANAGER_DIR\venv\Scripts\python.exe'
$PY_YOUTUBEPLAYLISTMANAGER_APP = '@PY_YOUTUBEPLAYLISTMANAGER_DIR\main.py'

// Context: Explorer
$PY_YOUTUBEPLAYLISTMANAGER_EXPLORER = '--prompt'
item(
    title="&YoutubePlaylistManager"
    keys="py"
    type='File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    //
    image=[E17C,GREY] image-sel=[E17C,PURPLE]
    tip=['"@PY_YOUTUBEPLAYLISTMANAGER_APP" @PY_YOUTUBEPLAYLISTMANAGER_EXPLORER',TIP3,0.75]
    //
    admin=keys.rbutton()
    cmd=if(KEYS_EXE_OPEN_EXE,('"@PY_YOUTUBEPLAYLISTMANAGER_EXE"'))
    args='"@PY_YOUTUBEPLAYLISTMANAGER_APP" @PY_YOUTUBEPLAYLISTMANAGER_EXPLORER'
    //
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@PY_YOUTUBEPLAYLISTMANAGER_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@PY_YOUTUBEPLAYLISTMANAGER_APP')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@PY_YOUTUBEPLAYLISTMANAGER_DIR')),
    }
)
// Context: Taskbar
$PY_YOUTUBEPLAYLISTMANAGER_TASKBAR = '--prompt'
item(
    title="&YoutubePlaylistManager"
    keys="py"
    type='Taskbar'
    //
    image=[E17C,GREY] image-sel=[E17C,PURPLE]
    tip=['"@PY_YOUTUBEPLAYLISTMANAGER_APP" @PY_YOUTUBEPLAYLISTMANAGER_TASKBAR',TIP3,0.75]
    //
    admin=keys.rbutton()
    args='"@PY_YOUTUBEPLAYLISTMANAGER_APP" @PY_YOUTUBEPLAYLISTMANAGER_TASKBAR'
    cmd=if(KEYS_EXE_OPEN_EXE,('"@PY_YOUTUBEPLAYLISTMANAGER_EXE"'))
    //
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@PY_YOUTUBEPLAYLISTMANAGER_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@PY_YOUTUBEPLAYLISTMANAGER_APP')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@PY_YOUTUBEPLAYLISTMANAGER_DIR')),
    }
)
