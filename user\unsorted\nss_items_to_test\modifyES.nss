// modify items
// Remove items by identifiers
/*modify(mode=mode.multiple
	where=this.name=="Restaurar versiones anteriores" ||this.name=="Transmitir en dispositivo" 
	vis=vis.remove)*/
//variante 1
/*modify(mode=mode.multiple
	where=this.name("Restaurar versiones anteriores", "Transmitir en dispositivo")
	vis=vis.remove)*/
//variante 2	
modify(mode=mode.multiple
	where=str.equals(this.name, ["Restaurar versiones anteriores", "Transmitir en dispositivo"])
	vis=vis.remove)

/*modify(type="recyclebin" where=window.is_desktop and this.name=="Vaciar Papelera de reciclaje" pos=1 sep) // NO FUNCIONA. En inglés tampoco*/

// Menu "Pin/Unpin", creado en shell.nss > menu(mode="multiple" title="Pin/Unpin" image=icon.pin) { }
modify(find="Desanclar*" pos="bottom" menu="Pin/Unpin")
modify(find="Anclar*" pos="top" menu="Pin/Unpin")

// Menu "File Manage", creado en shell.nss > import 'imports/file-manage.nss'
modify(where=this.name=='Copiar como ruta de acceso' menu="file manage")
modify(type="dir.back|drive.back" where=this.name=="Personalizar esta carpeta" pos=1 sep="top" menu="file manage")

modify(where=str.equals(this.name, ["Abrir en Terminal", "Abrir la ventana de PowerShell aquí"])
	pos="bottom" menu="Terminal")

// Menu "More options"(title.more_options), created in shell.nss > menu(mode="multiple" title=title.more_options image=icon.more_options) { }
modify(mode=mode.multiple
    where=str.equals(this.name,
    [
        "Send to",
        "Share",
        "Create shortcut",
        "Set as desktop background",
        "Rotate left",
        "Rotate right",
        "Connect to network drive",
        "Disconnect network drive",
        "Add a network location",
        "Format",
        "Eject",
        "Grant access to",
        "Include in library",
        "Print",
        "Print" // Included because PDF Xchange Viewer displays options in English.
    ])
    pos=1 menu=title.more_options)
