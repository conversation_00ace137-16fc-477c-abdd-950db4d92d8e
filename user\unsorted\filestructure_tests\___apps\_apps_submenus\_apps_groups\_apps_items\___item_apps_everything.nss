
/* item: singles/apps/everything */

//
$APP_EVERYTHING_DIR = '@app.dir\PORTAL\APPS\app_everything\exe'
$APP_EVERYTHING_EXE = '@APP_EVERYTHING_DIR\Everything64.exe'
$APP_EVERYTHING_TIP = "..."+str.trimstart('@APP_EVERYTHING_EXE','@app.dir')

// Context: Explorer
item(
    title  = ":  &Everything"
    keys   = "exe"
    type   = 'File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    args   = '-search "!ext:lnk hide: focus: dm:last2days " -filter " ├─ Modified: Any Time" -explore "@sel.dir"'
    //
    image  = APP_EVERYTHING_EXE
    tip    = [APP_EVERYTHING_TIP,TIP3,1.0]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_EVERYTHING_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_EVERYTHING_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_EVERYTHING_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_EVERYTHING_DIR')),
    }
)
// Context: Taskbar
item(
    title  = ":  &Everything"
    keys   = "exe"
    type   = 'Taskbar'
    args   = '-search "!ext:lnk hide: focus: dm:last2days " -filter " ├─ Modified: Any Time"'
    //
    image  = APP_EVERYTHING_EXE
    tip    = [APP_EVERYTHING_TIP,TIP3,1.0]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_EVERYTHING_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_EVERYTHING_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_EVERYTHING_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_EVERYTHING_DIR')),
    }
)
