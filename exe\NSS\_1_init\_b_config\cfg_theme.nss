﻿
// docs: `https://nilesoft.org/docs/configuration/themes`


theme {
    // `[auto, classic, white, black, modern]`
    name = "modern"
    // `[auto, compact, small, medium, large, wide]`
    view = view.small
    // `[true, false]`
    dark = true
    //
    background {
        opacity = 95
        color = #0B1127
        effect = auto
    }
    //
    item {
        opacity = 100
        radius = 0
        prefix = 1
        text {
            normal = #B4BFE4
            select = #FFFFFF
            normal-disabled = #7C839E
            select-disabled = #8B93B1
        }
        back {
            select = #3AC9B326
            select-disabled = #171820
        }
    }
    //
    symbol {
        normal = #89B4FA
        select = #89B4FA
        normal-disabled = #A6ADC8
        select-disabled = #A6ADC8
    }
    //
    image {
        enabled = true
        color = [#CDD6F4, #89B4FA, #1E1E2E]
        gap = 13
        align = 2
    }
    //
    separator {
        size = 1
        color = #313244
    }
    //
    border {
        enabled = true
        size = 1
        color = #89B4FA
        opacity = 40
        radius = 1
    }
    //
    shadow {
        enabled = true
        size = 5
        opacity = 5
        color = #0D0D15
    }
    //
    tip {
        enabled = true
        opacity = 65
        radius = 0
        padding = [8, 4, 8, 4]
        primary = TIP1_COLOR
        info = TIP2_COLOR
        success = TIP3_COLOR
        warning = TIP4_COLOR
        danger = TIP5_COLOR
    }
}