
//
$APP_USER_BULKRENAMEUTILITY_DIR = '@user.desktop\my\flow\home\__GOTO__\Apps'
$APP_USER_BULKRENAMEUTILITY_DIR = '@user.desktop\my\flow\home\__GOTO__\Apps\app_bulkrenameutility\exe\64-bit'
$APP_USER_BULKRENAMEUTILITY_EXE = '@APP_USER_BULKRENAMEUTILITY_DIR\Bulk Rename Utility.exe'
$APP_USER_BULKRENAMEUTILITY_TIP = "..."+str.trimstart('@APP_USER_BULKRENAMEUTILITY_EXE','@app.dir')

// Context: Explorer
item(
    title  = ":  &Bulk-Rename-Utility"
    keys   = "exe"
    type   = 'File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    args   = '"@sel.dir"'
    //
    image  = APP_USER_BULKRENAMEUTILITY_EXE
    tip    = [APP_USER_BULKRENAMEUTILITY_TIP,TIP3,0.8]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_USER_BULKRENAMEUTILITY_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_BULKRENAMEUTILITY_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_BULKRENAMEUTILITY_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_BULKRENAMEUTILITY_DIR')),
    }
)
// Context: Taskbar
item(
    title  = ":  &Bulk-Rename-Utility"
    keys   = "exe"
    type   = 'Taskbar'
    args   = '@user.desktop'
    //
    image  = APP_USER_BULKRENAMEUTILITY_EXE
    tip    = [APP_USER_BULKRENAMEUTILITY_TIP,TIP3,0.8]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_USER_BULKRENAMEUTILITY_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_BULKRENAMEUTILITY_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_BULKRENAMEUTILITY_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_BULKRENAMEUTILITY_DIR')),
    }
)
