﻿// ----------------------------------------------------------------------------
// GENERAL
// ----------------------------------------------------------------------------
// -> Settings
settings {
	priority=1
	exclude.where = !process.is_explorer
	showdelay = 200
	modify.remove.duplicate=1
	tip.enabled=true
}
// -> Icon Size
$icn_size_min = 11
$icn_size_mid = 12
$icn_size_max = 13

// ----------------------------------------------------------------------------
// COLORS
// ----------------------------------------------------------------------------
$clr_subtle = #4e4259
$clr_grey   = #717482
$clr_white  = #ffffff
$clr_blue   = #22a7f2
$clr_green  = #39c65a
$clr_orange = #ff904d
$clr_purple = #9133ff
$clr_red    = #ff1c1a

// ----------------------------------------------------------------------------
// REMOVE
// ----------------------------------------------------------------------------
// -> Default
remove(where=this.id==id.copy_path)
remove(where=this.id==id.copy_as_path)
remove(where=this.id==id.content)
// -> Custom
remove(where=regex.match(this.name, ".*NordVPN.*"))
remove(where=regex.match(this.name, ".*Onedrive.*"))
remove(where=regex.match(this.name, ".*Dropbox.*"))
remove(where=regex.match(this.name, ".*Sync or Backup.*"))
remove(where=regex.match(this.name, ".*Add to VLC Media Player.*"))
remove(where=regex.match(this.name, ".*Enqueue in Winamp.*"))
remove(where=regex.match(this.name, ".*Send a copy.*"))
remove(where=regex.match(this.name, ".*Open archive.*"))


// ----------------------------------------------------------------------------
// APPLICATION PATHS
// ----------------------------------------------------------------------------
// -> Default
$AppPath_Edge        = '@sys.prog32\Microsoft\Edge\Application\msedge.exe'
// -> Custom
$AppPath_BulkRename  = '@sys.prog\Bulk Rename Utility\Bulk Rename Utility.exe'
$AppPath_SublimeText = '@sys.prog\Sublime Text\sublime_text.exe'
$AppPath_Photoshop   = '@sys.prog\Adobe\Adobe Photoshop 2023\Photoshop.exe'
$AppPath_NotepadPlus = '@sys.prog\Notepad++\notepad++.exe'
$AppPath_VSCode      = '@sys.prog\Microsoft VS Code\Code.exe'
$AppPath_WinMerge    = '@sys.prog\WinMerge\WinMergeU.exe'
$AppPath_Chrome      = '@sys.prog\Google\Chrome\Application\chrome.exe'
$AppPath_Everything  = '@sys.prog\Everything 1.5a\Everything64.exe'

// ----------------------------------------------------------------------------
// APPLICATION ICONS
// ----------------------------------------------------------------------------
// -> Default
modify(image=image.fluent("\uE77B", icn_size_mid, clr_blue) where=this.id==id.run_as_administrator)
modify(image=image.fluent("\uE8E5", icn_size_mid, clr_blue) where=this.id==id.open)
modify(image=image.fluent("\uE7AC", icn_size_mid, clr_blue) where=this.id==id.open_with)
modify(image=image.fluent("\uE8A0", icn_size_mid, clr_blue) where=this.id==id.open_in_new_tab)
modify(image=image.fluent("\uE838", icn_size_mid, clr_blue) where=this.id==id.open_in_new_window)
modify(image=image.fluent("\uE81D", icn_size_mid, clr_blue) where=this.id==id.open_file_location)
modify(image=image.fluent("\uE81D", icn_size_mid, clr_blue) where=this.id==id.open_folder_location)
// -> Custom
modify(image=($AppPath_WinMerge)     where=regex.match(this.name, ".*Compare.*"))
modify(image=($AppPath_SublimeText)  where=regex.match(this.name, ".*Sublime.*"))
modify(image=($AppPath_Everything)   where=regex.match(this.name, ".*SearchEverything.*"))
modify(image=($AppPath_NotepadPlus)  where=regex.match(this.name, ".*Notepad\\+\\+.*"))



// ----------------------------------------------------------------------------
// MOVE THE EXISTING NON-DEFAULT MENUITEMS INTO A NEW SUBMENU
// ----------------------------------------------------------------------------
menu(type='~taskbar' title='ORG' pos='top' sep='bottom' image=["\uE1B8", clr_subtle]) {}
modify(
    where=!this.id(
		// -> Basic Operations
		id.delete, id.edit, id.new, id.new_folder, id.new_item, id.open,
		id.open_with, id.properties, id.rename,

		// -> Clipboard Operations
		id.copy_as_path, id.copy_path, id.copy, id.cut, id.paste,
		id.paste_shortcut,

		// -> Movement and Location
		id.copy_here, id.copy_to, id.copy_to_folder, id.move_here, id.move_to,
		id.move_to_folder, id.open_file_location, id.open_folder_location,

		// -> Advanced File Operations
		id.compressed, id.create_shortcut, id.create_shortcuts_here,
		id.extract_all, id.extract_to, id.restore_previous_versions,

		// -> Icon and Display Settings
		id.extra_large_icons, id.large_icons, id.medium_icons, id.small_icons,
		id.list, id.details, id.tiles, id.content,

		// -> Organization and Sorting
		id.arrange_by, id.group_by, id.sort_by,

		// -> Icon Management
		id.align_icons_to_grid, id.auto_arrange_icons,

		// -> Visibility and Customization
		id.customize_notification_icons, id.customize_this_folder,
		id.show_cortana_button, id.show_desktop_icons, id.show_file_extensions,
		id.show_hidden_files, id.show_libraries, id.show_network,
		id.show_people_on_the_taskbar, id.show_task_view_button,
		id.show_touch_keyboard_button, id.show_touchpad_button,

		// -> System Tools
		id.adjust_date_time, id.control_panel, id.device_manager,
		id.display_settings, id.file_explorer, id.folder_options,
		id.power_options, id.settings, id.task_manager, id.taskbar_settings,

		// -> Personalization
		id.desktop, id.options, id.personalize,

		// -> Network Operations
		id.cast_to_device, id.disconnect, id.disconnect_network_drive,
		id.map_as_drive, id.map_network_drive,

		// -> Sharing and Accessibility
		id.give_access_to, id.make_available_offline, id.make_available_online,
		id.share, id.share_with,

		// -> Command Line Tools
		id.command_prompt, id.open_command_prompt, id.open_command_window_here,
		id.open_powershell_window_here, id.open_windows_powershell,

		// -> System Utilities
		id.cleanup, id.refresh, id.run, id.run_as_administrator,
		id.run_as_another_user, id.search, id.troubleshoot_compatibility,

		// -> Security Tools
		id.install, id.manage, id.turn_off_bitlocker, id.turn_on_bitlocker,

		// -> Device Operations
		id.autoplay, id.eject, id.erase_this_disc, id.mount,

		// -> Media Actions
		id.play, id.print,

		// -> Window Arrangement
		id.cascade_windows, id.show_windows_side_by_side,
		id.show_windows_stacked,

		// -> Taskbar Management
		id.lock_all_taskbars, id.lock_the_taskbar,

		// -> Windows Features
		id.cortana, id.news_and_interests, id.send_to, id.store,

		// -> General
		id.add_a_network_location, id.cancel, id.collapse,
		id.collapse_all_groups, id.collapse_group, id.configure,
		id.empty_recycle_bin, id.exit_explorer, id.expand, id.expand_all_groups,
		id.expand_group, id.format, id.include_in_library,
		id.insert_unicode_control_character, id.merge, id.more_options,
		id.next_desktop_background, id.open_as_portable, id.open_autoplay,
		id.open_in_new_process, id.open_in_new_tab, id.open_in_new_window,
		id.open_new_tab, id.open_new_window,
		id.pin_current_folder_to_quick_access, id.pin_to_quick_access,
		id.pin_to_start, id.pin_to_taskbar, id.preview, id.reconversion,
		id.redo, id.remove_from_quick_access, id.remove_properties, id.restore,
		id.restore_default_libraries, id.rotate_left, id.rotate_right,
		id.select_all, id.set_as_desktop_background,
		id.set_as_desktop_wallpaper, id.shield, id.show_pen_button,
		id.show_the_desktop, id.show_this_pc, id.undo,
		id.unpin_from_quick_access, id.unpin_from_start, id.unpin_from_taskbar,
		id.view
    ) menu='ORG'
)


// ----------------------------------------------------------------------------
// HIDE SPECIFIC MENUITEMS
// ----------------------------------------------------------------------------
remove(where=this.id==id.copy_path)
remove(where=this.id==id.copy_as_path)
remove(where=this.id==id.content)

// ----------------------------------------------------------------------------
// SUBMENU: OPEN
// ----------------------------------------------------------------------------
// -> Menu
$OPEN_MenuName = "Open"
$OPEN_MenuMode = "*"
$OPEN_MenuIcon = image.fluent("\uE7B5", icn_size_mid, clr_white)
// -> Items

// -> Creation
menu(type='~taskbar' title=OPEN_MenuName pos=indexof('ORG', 1) mode=OPEN_MenuMode image=OPEN_MenuIcon) {}
modify(where=this.id==id.run_as_administrator image=image.fluent("\uE77B", icn_size_mid, clr_blue) menu=OPEN_MenuName)
modify(where=this.id==id.open image=image.fluent("\uE8E5", icn_size_mid, clr_blue) menu=OPEN_MenuName)
modify(where=this.id==id.open_with image=image.fluent("\uE7AC", icn_size_mid, clr_blue) menu=OPEN_MenuName)
modify(where=this.id==id.open_in_new_tab image=image.fluent("\uE8A0", icn_size_mid, clr_blue) menu=OPEN_MenuName)
modify(where=this.id==id.open_in_new_window image=image.fluent("\uE838", icn_size_mid, clr_blue) menu=OPEN_MenuName)
modify(where=this.id==id.open_file_location image=image.fluent("\uE81D", icn_size_mid, clr_blue) menu=OPEN_MenuName)
modify(where=this.id==id.open_folder_location image=image.fluent("\uE81D", icn_size_mid, clr_blue) menu=OPEN_MenuName)

// ----------------------------------------------------------------------------
// COMMAND ARGUMENTS
// ----------------------------------------------------------------------------

// ----------------------------------------------------------------------------
// SUBMENU: CLIPBOARD ACTIONS
// ----------------------------------------------------------------------------
//
// -> Menu
// --------------
$MENU_TITLE_copy = "Copy"
$MENU_MODE_copy  = "none|single|multi_unique|multi_single|multiple"
$MENU_ICON_copy  = image.fluent("\uE77F", icn_size_mid, clr_white)
//
// -> Items
// --------------
$ITEM_TITLE_copy_path     = "Copy path"
$ITEM_ICON_copy_path      = image.fluent("\uE8C8", icn_size_mid, clr_green)
$ITEM_CMDS_copy_path      = ['powershell', '-Command @sel("\\\"",",") | % { [System.IO.Path]::GetFullPath($_) } | Set-Clipboard']
//
$ITEM_TITLE_copy_location = "Copy location"
$ITEM_ICON_copy_location  = image.mdl("\uED43", icn_size_mid, clr_green)
$ITEM_CMDS_copy_location  = ['powershell', '-Command @sel("\\\"",",") | % { [System.IO.Path]::GetDirectoryName($_) } | Set-Clipboard']
//
$ITEM_TITLE_copy_filename = "Copy filename"
$ITEM_ICON_copy_filename  = image.fluent("\uE729", icn_size_mid, clr_green)
$ITEM_CMDS_copy_filename  = ['powershell', '-Command @sel("\\\"",",") | % { [System.IO.Path]::GetFileName($_) } | Set-Clipboard']
//
$ITEM_TITLE_copy_name     = "Copy name"
$ITEM_ICON_copy_name      = image.fluent("\uE7C3", icn_size_mid, clr_green)
$ITEM_CMDS_copy_name      = ['powershell', '-Command @sel("\\\"",",") | % { [System.IO.Path]::GetFileNameWithoutExtension($_) } | Set-Clipboard']
//
$ITEM_TITLE_copy_content  = "Copy content"
$ITEM_ICON_copy_content   = image.fluent("\uE8E4", icn_size_mid, clr_green)
$ITEM_CMDS_copy_content   = ['powershell', '-Command @sel("\\\"",",") | % { Get-Content $_ -Raw } | Set-Clipboard']
//
$ITEM_TITLE_copy_contents = "Copy contents"
$ITEM_ICON_copy_contents  = image.fluent("\uED28", icn_size_mid, clr_green)
$ITEM_CMDS_copy_contents  = ['powershell', '-Command Set-Clipboard -Path (@sel("\\\"",",") | % { Get-ChildItem $_ -Force | % { $_.FullName } })']
//
// -> Create
// --------------
menu(title=MENU_TITLE_copy type='~taskbar' pos=indexof('ORG', 2) mode=MENU_MODE_copy image=MENU_ICON_copy) {
    item(title=ITEM_TITLE_copy_path
        cmd=ITEM_CMDS_copy_path[0]
        args=ITEM_CMDS_copy_path[1]
        window=hidden
        image=ITEM_ICON_copy_path)
    item(title=ITEM_TITLE_copy_location
        cmd=ITEM_CMDS_copy_location[0]
        args=ITEM_CMDS_copy_location[1]
        window=hidden
        image=ITEM_ICON_copy_location)
    item(title=ITEM_TITLE_copy_filename
        cmd=ITEM_CMDS_copy_filename[0]
        args=ITEM_CMDS_copy_filename[1]
        window=hidden
        image=ITEM_ICON_copy_filename)
    item(title=ITEM_TITLE_copy_name
        cmd=ITEM_CMDS_copy_name[0]
        args=ITEM_CMDS_copy_name[1]
        window=hidden
        image=ITEM_ICON_copy_name)
    item(title=ITEM_TITLE_copy_content type='file' mode='multi_unique'
        cmd=ITEM_CMDS_copy_content[0]
        args=ITEM_CMDS_copy_content[1]
        window=hidden
        image=ITEM_ICON_copy_content)
    item(title=ITEM_TITLE_copy_contents type='~file|dir|back.dir|drive|back.drive' mode='multiple'
        cmd=ITEM_CMDS_copy_contents[0]
        args=ITEM_CMDS_copy_contents[1]
        window=hidden
        image=ITEM_ICON_copy_contents)
}


item(type='dir|back.dir' title='Bulk Rename Here' image cmd=AppPath_BulkRename args=sel.path.quote)
// item(type='file' title='Bulk Rename Here' image cmd=AppPath_BulkRename args=@sel(true, '|'))
// item(type='file'  mode='multiple' title='Open File(s) with Bulk and select them' image cmd='C:\Program Files\Bulk Rename Utility\Bulk Rename Utility.exe' args='"@sel(false, '|')"' )

// File
menu(type='~taskbar' title="File" pos=indexof('ORG', 3) image=image.fluent("\uEC6C", icn_size_mid, clr_white)) {}
modify(where=this.id==id.rename image=image.fluent("\uE8AC", icn_size_mid, clr_orange) menu="File")
modify(where=this.id==id.edit image=image.fluent("\uE70F", icn_size_mid, clr_orange) menu="File")
modify(where=this.id==id.delete image=image.fluent(\uE74D, icn_size_mid, clr_orange) menu="File")


// Misc
menu(type='~taskbar' title="Misc" pos=indexof('ORG', 4) sep="after" image=image.fluent("\uE712", icn_size_mid, clr_grey)) {}
modify(where=this.id==id.print image=image.fluent("\uE749", icn_size_mid, clr_grey) menu="Misc")
modify(where=this.id==id.create_shortcut image=image.fluent("\uE71B", icn_size_mid, clr_grey) menu="Misc")
modify(where=this.id==id.merge image=image.fluent("\uE8AB", icn_size_mid, clr_grey) menu="Misc")
modify(where=this.id==id.preview image=image.fluent("\uE8FF", icn_size_mid, clr_grey) menu="Misc")
modify(where=this.id==id.pin_current_folder_to_quick_access image=image.fluent("\uE718", icn_size_mid, clr_grey) menu="Misc")
modify(where=this.id==id.pin_to_quick_access image=image.fluent("\uE718", icn_size_mid, clr_grey) menu="Misc")
modify(where=this.id==id.pin_to_start image=image.fluent("\uE718", icn_size_mid, clr_grey) menu="Misc")
modify(where=this.id==id.pin_to_taskbar image=image.fluent("\uE718", icn_size_mid, clr_grey) menu="Misc")
modify(where=this.id==id.unpin_from_quick_access image=image.fluent("\uE718", icn_size_mid, clr_grey) menu="Misc")
modify(where=this.id==id.unpin_from_start image=image.fluent("\uE718", icn_size_mid, clr_grey) menu="Misc")
modify(where=this.id==id.unpin_from_taskbar image=image.fluent("\uE718", icn_size_mid, clr_grey) menu="Misc")
modify(where=this.id==id.turn_on_bitlocker image=image.fluent("\uE785", icn_size_mid, clr_grey) menu="Misc")
modify(where=this.id==id.format image=image.fluent("\uE8CE", icn_size_mid, clr_grey) menu="Misc")
modify(where=this.id==id.expand image=image.fluent("\uE976", icn_size_mid, clr_grey) menu="Misc")


// $segoe_icon_size=13

// menu(title='Copy' type='file|dir|back.dir|drive|back.drive' mode='multiple' image=icon.copy){
//     item(title='Copy path' cmd='powershell' args='-Command @sel("\\\"",",") | % { [System.IO.Path]::GetFullPath($_) } | Set-Clipboard' window=hidden image=image.fluent(\uE74E,segoe_icon_size))
//     item(title='Copy location' cmd='powershell' args='-Command @sel("\\\"",",") | % { [System.IO.Path]::GetDirectoryName($_) } | Set-Clipboard' window=hidden image=image.mdl(\uED43,segoe_icon_size))
//     item(title='Copy filename' cmd='powershell' args='-Command @sel("\\\"",",") | % { [System.IO.Path]::GetFileName($_) } | Set-Clipboard' window=hidden image=image.fluent(\uE729,segoe_icon_size))
//     item(title='Copy name' cmd='powershell' args='-Command @sel("\\\"",",") | % { [System.IO.Path]::GetFileNameWithoutExtension($_) } | Set-Clipboard' window=hidden image=image.fluent(\uE7C3,segoe_icon_size))
//     item(title='Copy contents' type='file' mode='multi_unique' cmd='powershell' args='-Command @sel("\\\"",",") | % { Get-Content $_ -Raw } | Set-Clipboard' window=hidden image=image.fluent(\uE8E4,segoe_icon_size))
//     item(title='Copy contents' type='~file|dir|back.dir|drive|back.drive' mode='multiple' cmd='powershell' args='-Command Set-Clipboard -Path (@sel("\\\"",",") | % { Get-ChildItem $_ -Force | % { $_.FullName } })' window=hidden image=image.fluent(\uED28,segoe_icon_size))
// }


// modify(where=this.id==id.merge image=image.fluent("\uF460", icn_size_mid, #717482) menu="Misc")
// modify(where=this.id==id.merge image=image.fluent("\uEA3C", icn_size_mid, #717482) menu="Misc")
// modify(where=this.id==id.edit image=image.fluent("\uE70F", icn_size_mid, #313f7b) menu="Misc")
// modify(where=this.id==id.delete image=image.fluent(\uE74D, icn_size_mid, #313f7b) menu="Misc")




// Root
modify(where=this.id==id.copy pos=5 image=image.fluent("\uE8C8", icn_size_max, clr_blue))
// modify(where=this.id==id.cut pos=6 image=image.fluent("\uE8C6", icn_size_max, clr_red))
modify(where=this.id==id.cut pos=6 image=image.fluent("\uE8C6", icn_size_max, clr_purple))
modify(where=this.id==id.paste pos=7 image=image.fluent("\uE77F", icn_size_max, clr_green))

// Properties
modify(where=this.id==id.properties sep="before" pos="end" image=image.fluent("\uE7BA", icn_size_max, #717482))


$reg_hidden = 'HKCU\SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer\Advanced'
$is_hidden = reg.get(reg_hidden,'Hidden')==2
item(
	type="desktop|back"
	image=["\uE7B3" ,"Segoe Fluent Icons"]
	title='Show hidden files'
	menu='view'
	checked=!is_hidden tip='Show or hide the files and folders that are marked as hidden.'
	commands {
	    cmd=reg.set(reg_hidden, 'Hidden', @if(is_hidden,1,2), reg.dword),
	    cmd=command.refresh,
	    cmd='ie4uinit' args='-show'
	}
)


// #f76e6e
// // Run as administrator
// modify(
// 	where=this.id==id.run_as_administrator
// 	pos=indexof('ORG', 1)
// 	image=image.fluent("\uE77B", icn_size_mid, #f1a374)
// 	title="Run as administrator \t –"
// )
// // Edit
// modify(
// 	where=this.id==id.edit
// 	pos=indexof('ORG', 2)
// 	image=image.fluent("\uE8E5", icn_size_mid, #f1a374)
// 	title="Edit \t –"
// )
// // Open
// modify(
// 	where=this.id==id.open
// 	pos=indexof('ORG', 3)
// 	image=image.fluent("\uE8E5", icn_size_mid, #22A7F2)
// 	title="Open \t –"
// )
// modify(
// 	where=this.id==id.open_with
// 	pos=indexof('ORG', 4)
// 	image=image.fluent("\uE7AC", icn_size_mid, #22A7F2)
// 	title="Open With"
// )
// modify(
// 	where=this.id==id.open_in_new_tab
// 	pos=indexof('ORG', 5)
// 	image=[icon.open_in_new_tab, #22A7F2]
// 	title="Open in new tab \t –"
// )
// modify(
// 	where=this.id==id.open_in_new_window
// 	pos=indexof('ORG', 6)
// 	image=[icon.open_in_new_window, #22A7F2]
// 	title="Open in new window \t –"
// )
// modify(type='file' where=this.id==id.open_with pos=indexof('ORG', 2) image=image.fluent(\uE7AC, 12) title="Open With" separator="none")
// separator pos=indexof('ORG', 3)
// ----------------------------------------------------------------------------
// IMPORT: Initialize images/icons
// ----------------------------------------------------------------------------
import 'imports/shell_themes/theme_blue.nss'
import 'imports/images.nss'

import 'imports/shell_menus/menu_taskbar.nss'
import 'imports/shell_menus_external/shell-icons.nss'
