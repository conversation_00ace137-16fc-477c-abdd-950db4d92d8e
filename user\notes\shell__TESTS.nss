// # =============================================================================
when using clsid's, they can only be used to remove items (not modify them)
```JS
    // #a (works)
    remove(clsid='{4E716236-AA30-4C65-B225-D68BBA81E9C2}')

    // #b (doesnt work)
    $winmerge_clsid = '{4E716236-AA30-4C65-B225-D68BBA81E9C2}'
    remove(clsid=winmerge_clsid)
```


// # =============================================================================
item(title='test regex.match' cmd=msg(regex.match(sel.path, '(.*)?\.exe``
item(title='test regex.match' cmd=msg(regex.match(sel.path, '(.*)?\.exe``#x27;)))

$matches = regex.matches('this subject has a submarine as a subsequence','\b(sub)(<a style="text-decoration:none;color:rgb(0, 168, 252);cursor:pointer !important" href="" target="_blank" alt="link-to" rel="noreferrer" title=")" data-reactroot="">^ </a>
item(title='test regex.matches' cmd=msg(str.join('found matches: @length(matches)', "", matches,"\n")))



// # =============================================================================
    item(vis=@key.shift() title=title.exit_explorer cmd=command.restart_explorer)
    item(title='Subscene' image=[\uE270, #22A7F2] pos="middle" cmd='chrome' args='"https://subscene.com/subtitles/searchbytitle"')
    item(title='qBittorrent' image=[\uE0CA, #22A7F2] pos="middle" cmd='cmd.exe' args='START /MAX CMD /C "C:\Program Files\qBittorrent\qbittorrent.exe"')
    separator



// # =============================================================================



theme {
    image {
        glyph="Segoe Fluent Icons"
        scale=true
        gap=10
        color=[light, accent, dark]
    }
}



settings {
    tip {
        enabled=true
        opacity=100
        padding=[14,6,14,8]
        width=400
        radius=1
        time=1.5

        default=[#d4d4d4, #232323]
        info=[#737373, light]
        primary=[accent, light]
        success=[#28a745, light]
        warning=[#ffc107, #343a40]
        danger=[#c24e4e, light]
    }
}




Use "Segoe Fluent Icons" with "lowercase glyph codes" for bigger badge
(e.g.: image=[[\uf83d, #ffffff],[\uf83f, #ff6a00], "Segoe Fluent Icons"])

Use "Segoe MDL2 Assets" with "uppercase glyph codes" for smaller badge
(e.g.: image=[[\uF83D, #ffffff],[\uF83F, #ff6a00], "Segoe MDL2 Assets"])


// # =============================================================================
// # 12.10.2023 - Kl.15:40: namespaces

shell
{
    // static items

    // Delete items by identifiers
    item(mode=mode.multiple
        where=this.id(id.restore_previous_versions,id.cast_to_device)
        vis=vis.remove)

    item(type='recyclebin' where=window.is_desktop and this.id==id.empty_recycle_bin pos=1 sep)
    item(type='back' find=['shortcut', '/new'] vis=vis.remove)
    item(find='unpin' pos=pos.bottom menu="Pin//Unpin")
    item(find='pin' pos=pos.top menu="Pin//Unpin")
    item(where=this.id==id.copy_as_path menu='file manage')
    item(type='dir.back|drive.back' where=this.id==id.customize_this_folder pos=1 sep='top' menu='file manage')
    item(find='open in terminal*' pos=pos.bottom sep menu='Terminal')
    item(find='open with visual studio' pos=1 menu='develop/editors')
    //Move and organize
    //item(mode=mode.multiple find='scan with' menu=title.more_options)
    item(mode=mode.multiple
        where=this.id(id.send_to,id.share,id.create_shortcut,id.set_as_desktop_background,id.rotate_left,
                        id.rotate_right, id.map_network_drive,id.disconnect_network_drive,id.format, id.eject,
                        id.give_access_to,id.include_in_library,id.print)
        pos=1 menu=title.more_options)
}