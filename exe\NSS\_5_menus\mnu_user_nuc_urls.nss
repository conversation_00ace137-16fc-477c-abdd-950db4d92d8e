
menu(title='&URLs@"\tNUC"' type='Taskbar|Desktop' image=[E09B,WHITE1] image-sel=[E09B,HOVER] sep='Bottom') {

    // Websites
    // =======================================================
    item(column vis='Static' image=[E00A,DARK])
    item(title="Websites" keys="" image=[E25E,DARK] tip=['@KEYS_URL_TIP',TIP1,0.0] sep='Both' )


    //
    $URL_IPTORRENTS = ['IPTorrents@"\t"',"https://iptorrents.me/t"]
    item(
        title="&"+'@URL_IPTORRENTS[0]'
        image='@app.dir\LIB\png\ico_iptorrents.png'
        image-sel=[E09B,PINK]
        tip=['@URL_IPTORRENTS[1]',TIP3,0.0]
        commands{
            cmd=if('@KEYS_URL_COPY',clipboard.set('@URL_IPTORRENTS[1]')),
            cmd=if('@KEYS_URL_OPEN',('@URL_IPTORRENTS[1]')),
        }
    )
    $URL_IMDB = ['IMDb@"\t"',"https://imdb.com/what-to-watch/?ref_=nv_watch"]
    item(
        title="&"+'@URL_IMDB[0]'
        image='@app.dir\LIB\png\ico_imdb.png'
        image-sel=[E09B,PINK]
        tip=['@URL_IMDB[1]',TIP3,0.0]
        commands{
            cmd=if('@KEYS_URL_COPY',clipboard.set('@URL_IMDB[1]')),
            cmd=if('@KEYS_URL_OPEN',('@URL_IMDB[1]')),
        }
    )
    $URL_SIDEREEL = ['SideReel@"\t"',"https://sidereel.com/"]
    item(
        title="&"+'@URL_SIDEREEL[0]'
        image='@app.dir\LIB\png\ico_sidereel.png'
        image-sel=[E09B,PINK]
        tip=['@URL_SIDEREEL[1]',TIP3,0.0]
        commands{
            cmd=if('@KEYS_URL_COPY',clipboard.set('@URL_SIDEREEL[1]')),
            cmd=if('@KEYS_URL_OPEN',('@URL_SIDEREEL[1]')),
        }
    )
    $URL_YOUTUBE = ['YouTube@"\t"',"https://youtube.com/"]
    item(
        title="&"+'@URL_YOUTUBE[0]'
        image='@app.dir\LIB\png\ico_youtube.png'
        image-sel=[E09B,PINK]
        tip=['@URL_YOUTUBE[1]',TIP3,0.0]
        commands{
            cmd=if('@KEYS_URL_COPY',clipboard.set('@URL_YOUTUBE[1]')),
            cmd=if('@KEYS_URL_OPEN',('@URL_YOUTUBE[1]')),
        }
    )

    // Streaming
    // =======================================================
    item(column vis='Static' image=[E00A,DARK])
    item(title="Streaming" keys="" image=[E153,DARK] tip=['@KEYS_URL_TIP',TIP1,0.0] sep='Both' )

    //
    $URL_NETFLIX = ['Netflix@"\t"',"https://netflix.com/"]
    item(
        title="&"+'@URL_NETFLIX[0]'
        image='@app.dir\LIB\png\ico_netflix.png'
        image-sel=[E09B,PINK]
        tip=['@URL_NETFLIX[1]',TIP3,0.0]
        commands{
            cmd=if('@KEYS_URL_COPY',clipboard.set('@URL_NETFLIX[1]')),
            cmd=if('@KEYS_URL_OPEN',('@URL_NETFLIX[1]')),
        }
    )
    $URL_NRKTV = ['NRK TV@"\t"',"https://tv.nrk.no/"]
    item(
        title="&"+'@URL_NRKTV[0]'
        image='@app.dir\LIB\png\ico_nrktv.png'
        image-sel=[E09B,PINK]
        tip=['@URL_NRKTV[1]',TIP3,0.0]
        commands{
            cmd=if('@KEYS_URL_COPY',clipboard.set('@URL_NRKTV[1]')),
            cmd=if('@KEYS_URL_OPEN',('@URL_NRKTV[1]')),
        }
    )
    $URL_HBOMAX = ['HBO Max@"\t"',"https://www.max.com/no/en"]
    item(
        title="&"+'@URL_HBOMAX[0]'
        image='@app.dir\LIB\png\ico_hbomax.png'
        image-sel=[E09B,PINK]
        tip=['@URL_HBOMAX[1]',TIP3,0.0]
        commands{
            cmd=if('@KEYS_URL_COPY',clipboard.set('@URL_HBOMAX[1]')),
            cmd=if('@KEYS_URL_OPEN',('@URL_HBOMAX[1]')),
        }
    )

}