
$NSS = '@app.dir/NSS/'
$LIB = '@app.dir/LIB/'

// ---

import 'NSS/_1_init/_a_constants/def_bools.nss'    // system checks
import 'NSS/_1_init/_a_constants/def_colors.nss'   // predefined colors
import 'NSS/_1_init/_a_constants/def_icons.nss'    // predefined icons
import 'NSS/_1_init/_a_constants/def_keys.nss'     // multifunctional key-kombinations
import 'NSS/_1_init/_a_constants/def_paths.nss'    // system paths
import 'NSS/_1_init/_a_constants/def_tooltips.nss' // tooltip styling
import 'NSS/_1_init/_a_constants/def_uris.nss'     // system shortcuts
// ---
// import 'NSS/_1_init/_b_variables/var_commands_cmd.nss'
// import 'NSS/_1_init/_b_variables/var_commands_ps1.nss'
// import 'NSS/_1_init/_b_variables/var_commands_py.nss'
// import 'NSS/_1_init/_b_variables/var_directories.nss'
// import 'NSS/_1_init/_b_variables/var_executables.nss'
// ---
import 'NSS/_1_init/_b_config/cfg_settings.nss'
import 'NSS/_1_init/_b_config/cfg_theme.nss'
// ---
import 'NSS/_1_init/_c_overrides/mod_icons.nss'
import 'NSS/_1_init/_c_overrides/mod_positions.nss'
import 'NSS/_1_init/_c_overrides/mod_visibility.nss'
// ---
import 'NSS/_1_init/_d_cleanup/cln_desktop.nss'
import 'NSS/_1_init/_d_cleanup/cln_explorer.nss'
import 'NSS/_1_init/_d_cleanup/cln_taskbar.nss'


// ----------------------------------------------------------------------------

// setup: load context menus
import 'NSS/_6_contexts/ctx_desktop.nss'
// import 'NSS/_6_contexts/ctx_everything64.nss'
import 'NSS/_6_contexts/ctx_explorer.nss'
import 'NSS/_6_contexts/ctx_taskbar.nss'
// import 'NSS/_6_contexts/ctx_taskbar_nuc.nss'
import 'NSS/_6_contexts/ctx_titlebar.nss'

// // icons
// import 'NSS/unsorted/shell_menus_external/custom-icons.nss'
// import 'NSS/unsorted/shell_menus_external/custom-icons2.nss'
