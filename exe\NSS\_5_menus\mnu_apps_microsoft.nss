
/* menu */

menu(title="&Microsoft" image=[E1B5,LOWKEY] image-sel=[E1B5,HOVER] sep='None')
{

    //
    item(column vis='Static' image=[E00A,DARK])
    // =============================================================================
    //
    item(title="settings" image=[E0EE,GREEN] vis='Static' sep )
    // -----------------------------------------------------------------------------

    // item(title='&System-Configuration@"\t"settings'  admin=if_key image=[E0F4,GREY] image-sel=[E0F4,HOVER] cmd='@exe_sys_conf'     tip='@exe_sys_conf')

    //
    item(title='&Audio-Device@"\t"settings'          admin=if_key image=[E0F3,GREY] image-sel=[E0F3,HOVER] cmd='@exe_sound'        tip='@exe_sound')
    item(title='&Background@"\t"settings'            admin=if_key image=[E0F3,GREY] image-sel=[E0F3,HOVER] cmd='@exe_control'      tip='@exe_control'       args='desktop')
    item(title='&Keyboard-Properties@"\t"settings'   admin=if_key image=[E0F3,GREY] image-sel=[E0F3,HOVER] cmd='@exe_control'      tip='@exe_control'       args='keyboard')
    item(title='&Language Bar Options@"\t"settings'  admin=if_key image=[E0F4,GREY] image-sel=[E0F4,HOVER] cmd='rundll32.exe'      tip='@exe_languagebar'   args='Shell32.dll,Control_RunDLL input.dll,,{C07337D3-DB2C-4D0B-9A93-B722A6C106E2}')
    item(title='&Regional-Settings@"\t"settings'     admin=if_key image=[E0F3,GREY] image-sel=[E0F3,HOVER] cmd='@exe_region'       tip='@exe_region')
    item(title='&System-Configuration@"\t"settings'  admin=if_key image=[E0F4,GREY] image-sel=[E0F4,HOVER] cmd='@exe_sys_conf'     tip='@exe_sys_conf')
    item(title='&System-Properties@"\t"settings'     admin=if_key image=[E0F3,GREY] image-sel=[E0F3,HOVER] cmd='@exe_sys_props'    tip='@exe_sys_props')
    item(title='&Taskbar-Configuration@"\t"settings' admin=if_key image=[E0F4,GREY] image-sel=[E0F4,HOVER] cmd='@exe_taskbar_conf' tip='@exe_taskbar_conf')

    //
    item(title="manage" image=[E194,ORANGE] vis='Static' sep )
    // -----------------------------------------------------------------------------
    //
    item(title='&Computer-Management@"\t"manage'   admin=if_key image=[E0D1,GREY] image-sel=[E0D1,HOVER] cmd='@exe_comp_mgmt'    tip='@exe_comp_mgmt')
    item(title='&Control-Panel@"\t"manage'         admin=if_key image=[E1D9,GREY] image-sel=[E1D9,HOVER] cmd='@exe_control'      tip='@exe_control')
    item(title='&Device-Manager@"\t"manage'        admin=if_key image=[E0D1,GREY] image-sel=[E0D1,HOVER] cmd='@exe_dev_mgmt'     tip='@exe_dev_mgmt')
    item(title='&Disk-Manager@"\t"manage'          admin=if_key image=[E0D1,GREY] image-sel=[E0D1,HOVER] cmd='@exe_disk_mgmt'    tip='@exe_disk_mgmt')
    item(title='&Network-Adapters@"\t"manage'      admin=if_key image=[E1D9,GREY] image-sel=[E1D9,HOVER] cmd='@exe_net_adapters' tip='@exe_net_adapters')
    item(title='&Power-Options@"\t"manage'         admin=if_key image=[E1D9,GREY] image-sel=[E1D9,HOVER] cmd='@exe_power_opts'   tip='@exe_power_opts')
    item(title='&Print-Management@"\t"manage'      admin=if_key image=[E0D1,GREY] image-sel=[E0D1,HOVER] cmd='@exe_print_mgmt'   tip='@exe_print_mgmt')
    item(title='&Programs-and-Features@"\t"manage' admin=if_key image=[E0D1,GREY] image-sel=[E0D1,HOVER] cmd='@exe_programs'     tip='@exe_programs')
    item(title='&Screen-Resolution@"\t"manage'     admin=if_key image=[E0D1,GREY] image-sel=[E0D1,HOVER] cmd='@exe_display'      tip='@exe_display')
    item(title='&Shared-Folders@"\t"manage'        admin=if_key image=[E1D9,GREY] image-sel=[E1D9,HOVER] cmd='@exe_shared_fldrs' tip='@exe_shared_fldrs')
    item(title='&User-Management@"\t"manage'       admin=if_key image=[E0D1,GREY] image-sel=[E0D1,HOVER] cmd='@exe_user_mgmt'    tip='@exe_user_mgmt')


    //
    item(column vis='Static' image=[E00A,DARK])
    // =============================================================================
    //
    item(title="utilities" image=[E0BE,BLUE] vis='Static' sep )
    // -----------------------------------------------------------------------------
    item(title='&Event-Viewer@"\t"utilities'        admin=if_key image=[E159,GREY]       image-sel=[E159,HOVER]      cmd='@exe_event_vwr'         tip='@exe_event_vwr')
    item(title='&Performance-Monitor@"\t"utilities' admin=if_key image=[E159,GREY]       image-sel=[E159,HOVER]      cmd='@exe_perf_mon'          tip='@exe_perf_mon')
    item(title='&Resource-Monitor@"\t"utilities'    admin=if_key image=[E159,GREY]       image-sel=[E159,HOVER]      cmd='@exe_res_mon'           tip='@exe_res_mon')
    item(title='&Services@"\t"utilities'            admin=if_key image=[E159,GREY]       image-sel=[E159,HOVER]      cmd='@exe_services'          tip='@exe_services')
    item(title='&Task-Scheduler@"\t"utilities'      admin=if_key image=[E159,GREY]       image-sel=[E159,HOVER]      cmd='@exe_task_schd'         tip='@exe_task_schd')
    item(title='&Windows-Defender@"\t"utilities'    admin=if_key image=[E1B5,GREY]       image-sel=[E1B5,HOVER]      cmd='@exe_defender'          tip='@exe_defender')
    item(title='&Windows-Firewall@"\t"utilities'    admin=if_key image=[E1B5,GREY]       image-sel=[E1B5,HOVER]      cmd='@exe_firewall'          tip='@exe_firewall')
    item(title=" " vis='Static' )

    //
    item(title="applications" image=[E142,PURPLE] vis='Static' sep )
    // -----------------------------------------------------------------------------
    // item(title="&Calculator"          admin=if_key image=[E1E7,LOWKEY]     image-sel=[E1E7,HOVER]      cmd='@exe_calc'              tip='@exe_calc')
    // item(title="&Character-Map"       admin=if_key image='@exe_charmap'    image-sel='@exe_charmap'    cmd='@exe_charmap'           tip='@exe_charmap')
    // item(title="&Ms-Paint"            admin=if_key image=[E116,GREY]       image-sel=[E116,HOVER]      cmd='@exe_mspaint'           tip='@exe_mspaint')
    // item(title="&Notepad"             admin=if_key image='@exe_notepad'    image-sel='@exe_notepad'    cmd='@exe_notepad'           tip='@exe_notepad')
    // item(title="&OnScreen-Keyboard"   admin=if_key image='@exe_osk'        image-sel='@exe_osk'        cmd='@exe_osk'               tip='@exe_osk')
    // item(title="&Registry-Editor"     admin=if_key image='@exe_regedit'    image-sel='@exe_regedit'    cmd='@exe_regedit'           tip='@exe_regedit')
    // item(title="&Windows-Magnifier"   admin=if_key image='@exe_magnify'    image-sel='@exe_magnify'    cmd='@exe_magnify'           tip='@exe_magnify')
    import '@app.dir/NSS/_3_items/itm_app_sys_calc.nss'
    import '@app.dir/NSS/_3_items/itm_app_sys_cmd.nss'
    import '@app.dir/NSS/_3_items/itm_app_sys_magnify.nss'
    import '@app.dir/NSS/_3_items/itm_app_sys_mspaint.nss'
    import '@app.dir/NSS/_3_items/itm_app_sys_notepad.nss'
    import '@app.dir/NSS/_3_items/itm_app_sys_osk.nss'
    import '@app.dir/NSS/_3_items/itm_app_sys_powershell.nss'
    import '@app.dir/NSS/_3_items/itm_app_sys_powershellise.nss'
    import '@app.dir/NSS/_3_items/itm_app_sys_regedit.nss'

    //
    item(title="terminals" image=[E26E,PINK] vis='Static' sep='Both' )
    // -----------------------------------------------------------------------------
    item(title='&Command-Prompt@"\t"terminals'      admin=if_key image='@exe_cmd'        image-sel='@exe_cmd'        cmd-line='@cmd_terminal_def' tip=TIP_ADMIN)
    item(title='&PowerShell-ISE@"\t"terminals'      admin=if_key image='@exe_ise'        image-sel='@exe_ise'        cmd='@exe_ise'               tip=TIP_ADMIN)
    item(title='&PowerShell@"\t"terminals'          admin=if_key image='@exe_powershell' image-sel='@exe_powershell' cmd-ps='@ps1_terminal_dsk'   tip=TIP_ADMIN)



}
