
//
$PY_CONSOLIDATELOGS_DIR = '@user.desktop\my\flow\home\__GOTO__\Scripts\Python\Py_Cli_Utils\py__ConsolidateLogs'
$PY_CONSOLIDATELOGS_EXE = '@PY_CONSOLIDATELOGS_DIR\venv\Scripts\python.exe'
$PY_CONSOLIDATELOGS_APP = '@PY_CONSOLIDATELOGS_DIR\src\consolidate_vscode_logs.py'
//
// $CMD_GIT_ADD_SELECTION_F_V = '/K (CD /D "@sel.dir") && (git add @BATCH_SEL_AS_STRING -f) && (@BATCH_EXIT_WITH_MSG) && PAUSE'

// Context: Explorer
$PY_CONSOLIDATELOGS_EXPLORER = '-d "@sel.dir" --prompt'
item(
    title="&ConsolidateLogs"
    keys="py"
    type='File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    //
    image=[E17<PERSON>,GREY] image-sel=[E17<PERSON>,PURPLE]
    tip=['"@PY_CONSOLIDATELOGS_APP" @PY_CONSOLIDATELOGS_EXPLORER',TIP3,0.75]
    //
    admin=keys.rbutton()
    cmd=if(KEYS_EXE_OPEN_EXE,('"@PY_CONSOLIDATELOGS_EXE"'))
    args='"@PY_CONSOLIDATELOGS_APP" @PY_CONSOLIDATELOGS_EXPLORER'
    //
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@PY_CONSOLIDATELOGS_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@PY_CONSOLIDATELOGS_APP')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@PY_CONSOLIDATELOGS_DIR')),
    }
)
// Context: Taskbar
$PY_CONSOLIDATELOGS_TASKBAR = ' --prompt'
// $PY_CONSOLIDATELOGS_TASKBAR = '-d "@PY_CONSOLIDATELOGS_DIR\src\consolidate_vscode_logs.py" --prompt'
item(
    title="&ConsolidateLogs"
    keys="py"
    type='Taskbar'
    //
    image=[E17C,GREY]
    image-sel=[E17C,PURPLE]
    //
    admin=keys.rbutton()
    cmd=if(KEYS_EXE_OPEN_EXE,('"@PY_CONSOLIDATELOGS_EXE"'))
    args='"@PY_CONSOLIDATELOGS_APP" @PY_CONSOLIDATELOGS_TASKBAR'
    tip=['"@PY_CONSOLIDATELOGS_APP" @PY_CONSOLIDATELOGS_TASKBAR',TIP3,0.75]
    //
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@PY_CONSOLIDATELOGS_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@PY_CONSOLIDATELOGS_APP')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@PY_CONSOLIDATELOGS_DIR')),
    }
)
