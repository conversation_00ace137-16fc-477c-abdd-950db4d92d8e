
//
$APP_GPT4ALL_DIR = '@user.desktop\my\flow\home\__GOTO__\Apps\app_gpt4all\exe\bin'
$APP_GPT4ALL_EXE = '@APP_GPT4ALL_DIR\chat.exe'
$APP_GPT4ALL_TIP = "..."+str.trimstart('@APP_GPT4ALL_EXE','@app.dir')
//

$APP_GPT4ALL_DIR_CFG = '@user.appdata\nomic.ai'
$APP_GPT4ALL_DIR_NSS = '@app.dir\NSS\_3_items\user_apps'
$APP_GPT4ALL_DIR_SRC = '@user.desktop\my\flow\home\__GOTO__\Apps\app_gpt4all'


// Context: Taskbar
item(
    title  = ":  &GPT4ALL"
    keys   = "exe"
    type   = 'Taskbar'
    args   = ''
    //
    image  = APP_GPT4ALL_EXE
    tip    = [APP_GPT4ALL_TIP,TIP3,1.0]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_GPT4ALL_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_GPT4ALL_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_GPT4ALL_EXE')),
        cmd=if(KEYS_EXE_GOTO_CFG,('@APP_GPT4ALL_DIR_CFG')),
        cmd=if(KEYS_EXE_GOTO_NSS,('@APP_GPT4ALL_DIR_NSS')),
        cmd=if(KEYS_EXE_GOTO_SRC,('@APP_GPT4ALL_DIR_SRC')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_GPT4ALL_DIR')),
    }
)

