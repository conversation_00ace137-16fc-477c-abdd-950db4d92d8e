
/* item: procexp64.exe */

// @app.dir/NSS/_step_2_setup/___user/apps/_1_items/app_procexp.nss
//
$APP_PROCEXP_DIR = '@app.dir\PORTAL\APPS\grp_sysinternalssuite\app_procexp\exe'
$APP_PROCEXP_EXE = '@APP_PROCEXP_DIR\procexp64.exe'
$APP_PROCEXP_TIP = "..."+str.trimstart('@APP_PROCEXP_EXE','@app.dir')
//
item(
    title  = ":  &Procexp64"
    keys   = "exe"
    type   = 'Taskbar|File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    args   = ''
    //
    image  = APP_PROCEXP_EXE
    tip    = [APP_PROCEXP_TIP,TIP3,1.0]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_PROCEXP_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_PROCEXP_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_PROCEXP_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_PROCEXP_DIR')),
    }
)
