
// //
// $APP_USER_SONICVISUALISER_DIR = '@user.desktop\my\flow\home\__GOTO__\Apps\app_sonicvisualiser\exe\bin'
// $APP_USER_SONICVISUALISER_EXE = '@APP_USER_SONICVISUALISER_DIR\SONICVISUALISER.exe'
// $APP_USER_SONICVISUALISER_TIP = "..."+str.trimstart('@APP_USER_SONICVISUALISER_EXE','@app.dir')

// // context: directory
// item(
//     title  = ":  &SonicVisualiser"
//     keys   = "exe"
//     type   = 'File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
//     find   = '.ts|.mp4|.mkv|.webm|.mov|.wmv|.avi|.flv|.mpg'
//     args   = '"@sel.file"'
//     //
//     image  = APP_USER_SONICVISUALISER_EXE
//     tip    = [APP_USER_SONICVISUALISER_TIP,TIP3,0.8]
//     //
//     admin  = keys.rbutton()
//     cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_USER_SONICVISUALISER_EXE"'))
//     commands{
//         cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_SONICVISUALISER_DIR')),
//         cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_SONICVISUALISER_EXE')),
//         cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_SONICVISUALISER_DIR')),
//     }
// )
// // Context: Taskbar
// item(
//     title  = ":  &SonicVisualiser"
//     keys   = "exe"
//     type   = 'Taskbar'
//     args   = ''
//     //
//     image  = APP_USER_SONICVISUALISER_EXE
//     tip    = [APP_USER_SONICVISUALISER_TIP,TIP3,0.8]
//     //
//     admin  = keys.rbutton()
//     cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_USER_SONICVISUALISER_EXE"'))
//     commands{
//         cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_SONICVISUALISER_DIR')),
//         cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_SONICVISUALISER_EXE')),
//         cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_SONICVISUALISER_DIR')),
//     }
// )



//
$APP_SONICVISUALISER_DIR = '@user.desktop\my\flow\home\__GOTO__\Apps\app_sonicvisualiser\exe'
$APP_SONICVISUALISER_EXE = '@APP_SONICVISUALISER_DIR\Sonic Visualiser.exe'
$APP_SONICVISUALISER_TIP = "..."+str.trimstart('@APP_SONICVISUALISER_EXE','@app.dir')
//
$APP_SONICVISUALISER_DIR_CFG = '@user.appdata\sonic-visualiser'
$APP_SONICVISUALISER_DIR_NSS = '@app.dir\NSS\_3_items\user_apps'
$APP_SONICVISUALISER_DIR_SRC = '@user.desktop\my\flow\home\__GOTO__\Apps\app_sonicvisualiser'

// Context: File
item(
    title  = ":  &Sonic Visualiser"
    keys   = "exe"
    type   = 'File'
    args   = '"@sel.file"'
    where  = str.equals(sel.file.ext,[".aac",".flac",".m4a",".mp3",".ogg",".wav",".wma"])
    //
    image  = APP_SONICVISUALISER_EXE
    tip    = [APP_SONICVISUALISER_TIP,TIP3,1.0]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_SONICVISUALISER_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_SONICVISUALISER_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_SONICVISUALISER_EXE')),
        cmd=if(KEYS_EXE_GOTO_CFG,('@APP_SONICVISUALISER_DIR_CFG')),
        cmd=if(KEYS_EXE_GOTO_NSS,('@APP_SONICVISUALISER_DIR_NSS')),
        cmd=if(KEYS_EXE_GOTO_SRC,('@APP_SONICVISUALISER_DIR_SRC')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_SONICVISUALISER_DIR')),
    }
)
// Context: Directory
item(
    title  = ":  &Sonic Visualiser"
    keys   = "exe"
    type   = 'Desktop|Dir|Drive|Back.Dir|Back.Drive'
    args   = '--local="@sel.dir"'
    //
    image  = APP_SONICVISUALISER_EXE
    tip    = [APP_SONICVISUALISER_TIP,TIP3,1.0]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_SONICVISUALISER_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_SONICVISUALISER_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_SONICVISUALISER_EXE')),
        cmd=if(KEYS_EXE_GOTO_CFG,('@APP_SONICVISUALISER_DIR_CFG')),
        cmd=if(KEYS_EXE_GOTO_NSS,('@APP_SONICVISUALISER_DIR_NSS')),
        cmd=if(KEYS_EXE_GOTO_SRC,('@APP_SONICVISUALISER_DIR_SRC')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_SONICVISUALISER_DIR')),
    }
)
// Context: Taskbar
item(
    title  = ":  &Sonic Visualiser"
    keys   = "exe"
    type   = 'Taskbar'
    args   = ''
    //
    image  = APP_SONICVISUALISER_EXE
    tip    = [APP_SONICVISUALISER_TIP,TIP3,1.0]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_SONICVISUALISER_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_SONICVISUALISER_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_SONICVISUALISER_EXE')),
        cmd=if(KEYS_EXE_GOTO_CFG,('@APP_SONICVISUALISER_DIR_CFG')),
        cmd=if(KEYS_EXE_GOTO_NSS,('@APP_SONICVISUALISER_DIR_NSS')),
        cmd=if(KEYS_EXE_GOTO_SRC,('@APP_SONICVISUALISER_DIR_SRC')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_SONICVISUALISER_DIR')),
    }
)

