
// :: update visibility


// --- /
modify(vis='Hidden' type='*' in='/' where=(
        this.id(
            id.autoplay,
            id.cast_to_device,
            id.copy_as_path,
            id.copy_path,
            id.cortana,
            // id.disconnect_network_drive,
            id.give_access_to,
            id.include_in_library,
            id.make_available_offline,
            id.make_available_online,
            id.move,
            id.new_item,
            id.news_and_interests,
            id.open_as_portable,
            id.restore,
            id.show_cortana_button,
            id.show_people_on_the_taskbar,
            id.restore_previous_versions,
            id.size)))


// --- /
modify(vis='Hidden' type='*' in='/' where=(
        // ---
        str.contains(this.name, "Clipchamp") ||
        str.contains(this.name, "Dropbox") ||
        str.contains(this.name, "NordVPN") ||
        str.contains(this.name, "Onedrive") ||
        str.contains(this.name, "Send a copy") ||
        str.contains(this.name, "Sync and Backup") ||
        str.contains(this.name, "VLC Media Player") ||
        // ---
        str.equals(this.name, [
                "Add to VLC Media Player",
                "Back up to Dropbox",
                "Dropbox",
                "Enqueue in Winamp",
                "Move to Dropbox",
                "Move to Onedrive",
                "News and interests",
                "Open archive",
                "Open Command Prompt Here",
                "Open in Terminal",
                "Sync or Backup",
                "Windows Ink Workspace"])))


// --- /View
modify(vis='Hidden' type='*' in='/View' where=(
        this.id(
            id.list,
            id.tiles,
            id.content)))


// --- /New
modify(vis='Hidden' type='*' in='/New' where=(
        !str.equals(this.name, [
                "Folder",
                "Shortcut",
                "Text Document",
                "AutoHotkey"])))



// /* OLD */
// /* win10 */
// // -> hide by id
// remove(type='*' in='/View' where=sys.is10() & this.id(id.content, id.list, id.tiles))
// remove(type='*' in='/'
    //     where=sys.is10() & this.id(
        //         id.cortana, id.news_and_interests, id.show_cortana_button,
        //         id.show_people_on_the_taskbar,
        //         id.autoplay, id.cast_to_device,
        //         id.new_item, id.open_as_portable,
        //         id.copy_as_path, id.copy_path, id.give_access_to, id.include_in_library,
        //         id.make_available_offline, id.make_available_online,
        //         id.restore_previous_versions
        //     )
    // )
// // -> hide by name
// remove(type='*' in='/New' where=sys.is10() & (!regex.match(this.name, "^Folder$|^Shortcut$|^Text.*|.*AutoHotkey.*")))
// remove(type='*' in='/' where=sys.is10() find='Add to VLC Media Player|Dropbox|Enqueue in Winamp|News and interests|NordVPN|Onedrive|Open archive|Send a copy|Sync or Backup|Windows Ink Workspa')

// /* OLD */
// /* win11 */
// // EXTREMELY SLOW ON WINDOWS 10
// // -> hide by id
// remove(type='*' in='/View' where=sys.is11() & this.id(id.content))
// remove(type='*' in='/View' where=sys.is11() & this.id(id.list))
// remove(type='*' in='/View' where=sys.is11() & this.id(id.tiles))
// //
// remove(type='*' in='/' where=sys.is11() & this.id(id.cortana))
// remove(type='*' in='/' where=sys.is11() & this.id(id.news_and_interests))
// remove(type='*' in='/' where=sys.is11() & this.id(id.show_cortana_button))
// remove(type='*' in='/' where=sys.is11() & this.id(id.show_people_on_the_taskbar))
// //
// remove(type='*' in='/' where=sys.is11() & this.id(id.autoplay))
// remove(type='*' in='/' where=sys.is11() & this.id(id.cast_to_device))
// remove(type='*' in='/' where=sys.is11() & this.id(id.disconnect_network_drive))
// remove(type='*' in='/' where=sys.is11() & this.id(id.new_item))
// remove(type='*' in='/' where=sys.is11() & this.id(id.open_as_portable))
// //
// remove(type='*' in='/' where=sys.is11() & this.id(id.copy_as_path))
// remove(type='*' in='/' where=sys.is11() & this.id(id.copy_path))
// remove(type='*' in='/' where=sys.is11() & this.id(id.give_access_to))
// remove(type='*' in='/' where=sys.is11() & this.id(id.include_in_library))
// remove(type='*' in='/' where=sys.is11() & this.id(id.make_available_offline))
// remove(type='*' in='/' where=sys.is11() & this.id(id.make_available_online))
// remove(type='*' in='/' where=sys.is11() & this.id(id.restore_previous_versions))
// // -> hide by name
// remove(type='*' in='*' where=sys.is11() find='Add to VLC Media Player')
// remove(type='*' in='*' where=sys.is11() find='Dropbox')
// remove(type='*' in='*' where=sys.is11() find='Enqueue in Winamp')
// remove(type='*' in='*' where=sys.is11() find='News and interests')
// remove(type='*' in='*' where=sys.is11() find='NordVPN')
// remove(type='*' in='*' where=sys.is11() find='Onedrive')
// remove(type='*' in='*' where=sys.is11() find='Open archive')
// remove(type='*' in='*' where=sys.is11() find='Send a copy')
// remove(type='*' in='*' where=sys.is11() find='Sync or Backup')
// remove(type='*' in='*' where=sys.is11() find='Windows Ink Workspa')
