
//
menu(title="&Create" type='Desktop|Back.Dir|Back.Drive|File' image=[E1D2,BLUE] image-sel=[E1D2,HOVER] sep='None') {

    //
    $STR_TIME = sys.datetime("H.M")
    // $STR_DATE = sys.datetime("d.m.y")
    $STR_DATE = sys.datetime("y.m.d")
    // $STR_DATETIME = sys.datetime("d.m.y - H.M - ")
    $STR_DATETIME = sys.datetime("y.m.d-kl.H.M--")
    //
    $STR_SEPARATOR = "============================================================================="
    $STR_LINEBREAK = "\n"

    //
    $STR_CONTENT_BAT = "@ECHO OFF\nSETLOCAL ENABLEDELAYEDEXPANSION\nIF EXIST \"%~1\" (CD /D \"%~1\") ELSE (CD /D \"%~dp0\")"
    //
    $STR_CONTENT_PY = '# @STR_SEPARATOR'+STR_LINEBREAK+'# [@STR_TIME] - @STR_DATE'+STR_LINEBREAK

    //
    item(title="&Directory" keys="/"  image=[E0E7,GREY]  image-sel=[E0E7,HOVER] cmd=io.dir.create('@(STR_DATETIME)'))
    separator()

    //
    //
    //
    item(title="&bat"  keys="*.*" image=[E0AC,WHITE] image-sel=[E0AC,HOVER] cmd=io.file.create('@(STR_DATETIME).bat','@STR_CONTENT_BAT',1))
    item(title="&py"   keys="*.*" image=[E230,WHITE] image-sel=[E230,HOVER] cmd=io.file.create('@(STR_DATETIME).py','@STR_CONTENT_PY',1))
    item(title="&todo" keys="*.*" image=[E230,WHITE] image-sel=[E230,HOVER] cmd=io.file.create('@(STR_DATETIME)__todo.py','@STR_DATETIME',1))
    item(title="&txt"  keys="*.*" image=[E17A,WHITE] image-sel=[E17A,HOVER] cmd=io.file.create('@(STR_DATETIME).txt','@STR_DATETIME',1))
    separator()

    // item(title="Sync" image=[E09D,BLUE] vis='Static' sep='both' )
    import '@app.dir/NSS/_3_items/itm_py_markdowngenerator.nss'
    import '@app.dir/NSS/_3_items/itm_py_projectgenerator.nss'
    import '@app.dir/NSS/_3_items/itm_py_urlgenerator.nss'
    import '@app.dir/NSS/_3_items/itm_py_youtubedownloader.nss'
    separator()

    //
    $CONTENT_GITIGNORE = "# everything except .gitignore\n# *\n# !.gitignore\n\n# files without extension\n# *\n# !*/\n# !*.*"
    //
    item(title="__init__.py"       keys="" image=[E230,WHITE] image-sel=[E230,HOVER] cmd=io.file.create('__init__.py',"",1))
    item(title=".&gitignore"       keys="" image=[E22C,WHITE] image-sel=[E22C,HOVER] cmd=io.file.create('.gitignore','@CONTENT_GITIGNORE',1))
    item(title="&requirements.txt" keys="" image=[E17A,WHITE] image-sel=[E17A,HOVER] cmd=io.file.create('requirements.txt',"",1))
    separator()

    // (
        // # dirs: python
        // **/.env/
        // **/.venv/
        // **/__pycache__/
        // **/env/
        // **/venv/

        // # dirs: logs and temp
        // **/build/
        // **/cache/
        // **/dist/
        // **/logs/
        // **/temp/
        // **/tmp/

        // # files: workflow
        // *.bak
        // *.blend1
        // *.ini.bak
        // *.ldb
        // *.log
        // *.prv.ppk
        // *.prv.pub
        // *.pyc
        // *.pyo
        // *.sublime-workspace
        // *.sublime_session
        // *.swp
        // *.tmp
        // .DS_Store

        // # files
        // **/quit.blend
        // **/Run History-1.5a.csv
        // **/Search History-1.5a.csv
        // **/Session-1.5a.backup.json
        // **/Session-1.5a.json
    // )

}
