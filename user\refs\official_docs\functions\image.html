<h5>IMAGE (ICON)</h5>
<p>The image namespace contains functions that return an icon and are only assigned to the <code>image</code> property
</p>
<section id="image.glyph" class="my-5">
	<h5>image.glyph</h5>
	<p>Return font icon with color and size option.</p>
	<p>Syntax</p>
	<code>image.glyph(0xE00B)</code><br>
	<code>image.glyph(\uE00B)</code><br>
	<code>image.glyph("\uE00B")</code><br>
	<code>image.glyph("\xE00B")</code><br>
	<br>
	<code>image.glyph(0xE00B, #0000ff)</code><br>
	<code>image.glyph(0xE00B, #0000ff, 10)</code><br>
	<code>image.glyph(0xE00B, #0000ff, 12, "Segoe MDL2 Assets")</code><br>
</section>
<section id="image.res" class="my-5">
	<h5>image.res</h5>
	<p>Returns an icon through a path or through resources.</p>
	<p>Syntax</p>
	<code>image.res(path)</code><br>
	<code>image.res(path, index)</code><br>
	<code>image.res(path, -id)</code><br>
	<br>
	<code>image(path)</code><br>
	<code>image(path, index)</code><br>
	<code>image(path, -id)</code><br>
</section>
<section id="image.svg," class="my-5">
	<h5>image.svg, image.svgf</h5>
	<p>Returns an svg image via a string or from a file path.</p>
	<p>Syntax</p>
	<code>image.svg('&lt;svg viewBox=&quot;0 0 100 100&quot;&gt;&lt;path fill=&quot;red&quot; d=&quot;M0 0 L 100 0 L50
		100 Z&quot;/&gt;&lt;/svg&gt;')</code><br>
	<code>image.svgf(path)</code><br>
</section>
<section id="image.rect" class="my-5">
	<h5>image.rect</h5>
	<p>Returns a colored rectangle icon of an optional size.</p>
	<p>Syntax</p>
	<code>image.rect(#0000ff)</code><br>
	<code>image.rect(#0000ff, 10)</code><br>
</section>

<section id="image.segoe" class="my-5">
	<h5>image.segoe</h5>
	<p>Returns a glyph from "Segoe Fluent Icons" if present then "Segoe MDL2 Assets".</p>
	<p>Syntax</p>
	<code>image.segoe(\uxxxx)</code><br>
	<code>image.segoe(\uxxxx, 10)</code><br>
</section>

<section id="image.fluent" class="my-5">
	<h5>image.fluent</h5>
	<p>Returns a glyph from "Segoe Fluent Icons" with optional size.</p>
	<p>Syntax</p>
	<code>image.fluent(\uxxxx)</code><br>
	<code>image.fluent(\uxxxx, 10)</code><br>
</section>

<section id="image.mdl" class="my-5">
	<h5>image.mdl</h5>
	<p>Returns a glyph from "Segoe MDL2 Assets" with optional size.</p>
	<p>Syntax</p>
	<code>image.mdl(\uxxxx)</code><br>
	<code>image.mdl(\uxxxx, 10)</code><br>
</section>

<section id="image.default" class="my-5">
	<h5>image.default</h5>
	<p></p>
	<p>Syntax</p>
	<code>image.default</code>
</section>

<section id="icon.box" class="my-5">
	<h5>icon.box</h5>
	<p></p>
	<p>Syntax</p>
	<code>icon.box([["path to file"], [index]])</code>
</section>
