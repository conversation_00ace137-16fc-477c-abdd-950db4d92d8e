
//
$APP_USER_REGISTRYCHANGESVIEW_DIR = '@user.desktop\my\flow\home\__GOTO__\Apps\grp_nirsoft\app_registrychangesview\exe'
$APP_USER_REGISTRYCHANGESVIEW_EXE = '@APP_USER_REGISTRYCHANGESVIEW_DIR\RegistryChangesView.exe'
$APP_USER_REGISTRYCHANGESVIEW_TIP = "..."+str.trimstart('@APP_USER_REGISTRYCHANGESVIEW_EXE','@app.dir')

//
item(title="&RegistryChangesView"
    keys="exe"
    type='Taskbar|File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    //
    image=APP_USER_REGISTRYCHANGESVIEW_EXE
    tip=[APP_USER_REGISTRYCHANGESVIEW_TIP,TIP3,0.5]
    //
    admin=keys.rbutton()
    cmd=if(KEYS_EXE_OPEN_EXE,(APP_USER_REGISTRYCHANGESVIEW_EXE))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set(APP_USER_REGISTRYCHANGESVIEW_DIR)),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set(APP_USER_REGISTRYCHANGESVIEW_EXE)),
        cmd=if(KEYS_EXE_OPEN_DIR,(APP_USER_REGISTRYCHANGESVIEW_DIR)),
    }
)

