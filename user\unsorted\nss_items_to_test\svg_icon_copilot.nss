// from shell: import 'imports/custom/svg_icon_copilot.nss'

$svg_copilot=image.svg('<svg width="100" height="100" viewBox="0 0 44 40">
  <defs>
    <radialGradient id="a" cx="124.98" cy="475.38" r="1" gradientTransform="matrix(-10.96 -13.39 -12.59 10.31 7758.88 -2885.53)" gradientUnits="userSpaceOnUse">
      <stop offset=".1" stop-color="#00aeff"/>
      <stop offset=".77" stop-color="#2253ce"/>
      <stop offset="1" stop-color="#0736c4"/>
    </radialGradient>
    <radialGradient id="b" cx="145.65" cy="495.32" r="1" gradientTransform="matrix(9.88 12.57 12.2 -9.58 -7103.18 3268.69)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#ffb657"/>
      <stop offset=".63" stop-color="#ff5f3d"/>
      <stop offset=".92" stop-color="#c02b3c"/>
    </radialGradient>
    <radialGradient id="e" cx="133.87" cy="489.99" r="1" gradientTransform="matrix(-12.67 36.24 43.41 15.18 -19166.53 -11956.63)" gradientUnits="userSpaceOnUse">
      <stop offset=".07" stop-color="#8c48ff"/>
      <stop offset=".5" stop-color="#f2598a"/>
      <stop offset=".9" stop-color="#ffb152"/>
    </radialGradient>
    <linearGradient id="c" x1="381.02" y1="442.72" x2="383.31" y2="416.24" gradientTransform="matrix(1 0 0 -1 0 770)" gradientUnits="userSpaceOnUse">
      <stop offset=".16" stop-color="#0d91e1"/>
      <stop offset=".49" stop-color="#52b471"/>
      <stop offset=".65" stop-color="#98bd42"/>
      <stop offset=".94" stop-color="#ffc800"/>
    </linearGradient>
    <linearGradient id="d" x1="381.24" y1="446.03" x2="382.49" y2="417.15" gradientTransform="matrix(1 0 0 -1 0 770)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#3dcbff"/>
      <stop offset=".25" stop-color="#0588f7" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="f" x1="398.29" y1="436.68" x2="398.27" y2="428.81" gradientTransform="matrix(1 0 0 -1 0 770)" gradientUnits="userSpaceOnUse">
      <stop offset=".06" stop-color="#f8adfa"/>
      <stop offset=".71" stop-color="#a86edd" stop-opacity="0"/>
    </linearGradient>
  </defs>
  <path d="M400.14 327.33a4.63 4.63 0 0 0-4.44-3.33h-1.35a4.63 4.63 0 0 0-4.55 3.79l-2.31 12.61.57-2a4.63 4.63 0 0 1 4.44-3.33h7.85l3.29 1.28 3.18-1.28h-.93a4.63 4.63 0 0 1-4.44-3.32Z" transform="translate(-368 -324)" style="fill:url(#a)"/>
  <path d="M380.33 360.66a4.63 4.63 0 0 0 4.45 3.34h2.87a4.63 4.63 0 0 0 4.63-4.51l.31-12.16-.65 2.23a4.63 4.63 0 0 1-4.44 3.33h-7.92l-2.82-1.53-3.06 1.53h.91a4.63 4.63 0 0 1 4.45 3.34Z" transform="translate(-368 -324)" style="fill:url(#b)"/>
  <path d="M395.5 324h-16c-4.58 0-7.33 6.06-9.17 12.11-2.17 7.18-5 16.77 3.21 16.77h6.93a4.63 4.63 0 0 0 4.46-3.36c1.2-4.21 3.31-11.56 5-17.16.84-2.84 1.54-5.28 2.62-6.8a3.62 3.62 0 0 1 2.95-1.56Z" transform="translate(-368 -324)" style="fill:url(#c)"/>
  <path d="M395.5 324h-16c-4.58 0-7.33 6.06-9.17 12.11-2.17 7.18-5 16.77 3.21 16.77h6.93a4.63 4.63 0 0 0 4.46-3.36c1.2-4.21 3.31-11.56 5-17.16.84-2.84 1.54-5.28 2.62-6.8a3.62 3.62 0 0 1 2.95-1.56Z" transform="translate(-368 -324)" style="fill:url(#d)"/>
  <path d="M384.5 364h16c4.58 0 7.33-6.06 9.17-12.12 2.17-7.18 5-16.77-3.21-16.77h-6.93a4.63 4.63 0 0 0-4.46 3.36c-1.2 4.21-3.31 11.56-5 17.16-.84 2.84-1.54 5.29-2.62 6.81a3.62 3.62 0 0 1-2.95 1.56Z" transform="translate(-368 -324)" style="fill:url(#e)"/>
  <path d="M384.5 364h16c4.58 0 7.33-6.06 9.17-12.12 2.17-7.18 5-16.77-3.21-16.77h-6.93a4.63 4.63 0 0 0-4.46 3.36c-1.2 4.21-3.31 11.56-5 17.16-.84 2.84-1.54 5.29-2.62 6.81a3.62 3.62 0 0 1-2.95 1.56Z" transform="translate(-368 -324)" style="fill:url(#f)"/>
</svg>')


item(type='desktop' title='Copilot' sep='both' pos='bottom' image=svg_copilot cmd='microsoft-edge:///?ux=copilot&tcp=1&source=taskbar')