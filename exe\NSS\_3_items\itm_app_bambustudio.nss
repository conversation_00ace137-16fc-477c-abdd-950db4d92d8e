
//
$APP_BAMBUSTUDIO_DIR = '@sys.prog\Bambu Studio'
$APP_BAMBUSTUDIO_EXE = '@APP_BAMBUSTUDIO_DIR\bambu-studio.exe'
$APP_BAMBUSTUDIO_TIP = "..."+str.trimstart('@APP_BAMBUSTUDIO_EXE','@app.dir')
//
$APP_BAMBUSTUDIO_NSS = '@app.dir\NSS\_3_items\user_apps'
$APP_BAMBUSTUDIO_SRC = '@user.desktop\my\flow\home\__GOTO__\Apps\app_bambustudio'


// Context: File
item(
    title  = ":  &Bambu Studio"
    keys   = "exe"
    type   = 'File'
    args   = '"@sel.file"'
    where  = str.equals(sel.file.ext,[".max"])
    //
    image  = APP_BAMBUSTUDIO_EXE
    tip    = [APP_BAMBUSTUDIO_TIP,TIP3,1.0]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_BAMBUSTUDIO_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_BAMBUSTUDIO_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_BAMBUSTUDIO_EXE')),
        cmd=if(KEYS_EXE_GOTO_NSS,('@APP_BAMBUSTUDIO_NSS')),
        cmd=if(KEYS_EXE_GOTO_SRC,('@APP_BAMBUSTUDIO_SRC')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_BAMBUSTUDIO_DIR')),
    }
)
// Context: Directory
item(
    title  = ":  &Bambu Studio"
    keys   = "exe"
    type   = 'Desktop|Dir|Drive|Back.Dir|Back.Drive'
    args   = '--local="@sel.dir"'
    //
    image  = APP_BAMBUSTUDIO_EXE
    tip    = [APP_BAMBUSTUDIO_TIP,TIP3,1.0]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_BAMBUSTUDIO_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_BAMBUSTUDIO_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_BAMBUSTUDIO_EXE')),
        cmd=if(KEYS_EXE_GOTO_NSS,('@APP_BAMBUSTUDIO_NSS')),
        cmd=if(KEYS_EXE_GOTO_SRC,('@APP_BAMBUSTUDIO_SRC')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_BAMBUSTUDIO_DIR')),
    }
)
// Context: Taskbar
item(
    title  = ":  &Bambu Studio"
    keys   = "exe"
    type   = 'Taskbar'
    args   = ''
    //
    image  = APP_BAMBUSTUDIO_EXE
    tip    = [APP_BAMBUSTUDIO_TIP,TIP3,1.0]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_BAMBUSTUDIO_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_BAMBUSTUDIO_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_BAMBUSTUDIO_EXE')),
        cmd=if(KEYS_EXE_GOTO_NSS,('@APP_BAMBUSTUDIO_NSS')),
        cmd=if(KEYS_EXE_GOTO_SRC,('@APP_BAMBUSTUDIO_SRC')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_BAMBUSTUDIO_DIR')),
    }
)

