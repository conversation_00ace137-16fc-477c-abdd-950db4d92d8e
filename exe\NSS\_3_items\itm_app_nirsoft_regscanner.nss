
//
$APP_USER_REGSCANNER_DIR = '@user.desktop\my\flow\home\__GOTO__\Apps\grp_nirsoft\app_regscanner\exe'
$APP_USER_REGSCANNER_EXE = '@APP_USER_REGSCANNER_DIR\RegScanner.exe'
$APP_USER_REGSCANNER_TIP = "..."+str.trimstart('@APP_USER_REGSCANNER_EXE','@app.dir')

// -> RegScanner
item(title="&RegScanner"
    keys="exe"
    type='Taskbar|File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    //
    image=APP_USER_REGSCANNER_EXE
    tip=[APP_USER_REGSCANNER_TIP,TIP3,0.5]
    //
    admin=keys.rbutton()
    cmd=if(KEYS_EXE_OPEN_EXE,(APP_USER_REGSCANNER_EXE))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set(APP_USER_REGSCANNER_DIR)),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set(APP_USER_REGSCANNER_EXE)),
        cmd=if(KEYS_EXE_OPEN_DIR,(APP_USER_REGSCANNER_DIR)),
    }
)

