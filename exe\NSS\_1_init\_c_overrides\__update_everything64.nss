
//
$IF_EVERYTHING64_OPEN                  = process.name=="Everything64" && str.equals(this.name,   "Open")
$IF_EVERYTHING64_OPEN_PATH             = process.name=="Everything64" && str.equals(this.name,   "Open Path")
$IF_EVERYTHING64_OPEN_WITH             = process.name=="Everything64" && str.equals(this.name,   "Open With")
$IF_EVERYTHING64_COPY_NAME             = process.name=="Everything64" && str.equals(this.name,   "Copy Name")
$IF_EVERYTHING64_COPY_FULL_PATH        = process.name=="Everything64" && str.equals(this.name,   "Copy Full Path")
$IF_EVERYTHING64_COPY_DATE_MODIFIED    = process.name=="Everything64" && str.equals(this.name,   "Copy Date Modified")
$IF_EVERYTHING64_COPY_EXTENSION        = process.name=="Everything64" && str.equals(this.name,   "Copy Extension")
$IF_EVERYTHING64_COPY_SIZE             = process.name=="Everything64" && str.equals(this.name,   "Copy Size")
$IF_EVERYTHING64_COPY_TYPE             = process.name=="Everything64" && str.equals(this.name,   "Copy Type")
$IF_EVERYTHING64_SET_RUN_COUNT         = process.name=="Everything64" && str.equals(this.name,   "Set Run Count")
$IF_EVERYTHING64_PIN_TO                = process.name=="Everything64" && str.contains(this.name, "Pin to")
$IF_EVERYTHING64_EXPLORE_IN_EVERYTHING = process.name=="Everything64" && str.equals(this.name,   "Explore in Everything")
$IF_EVERYTHING64_EDIT_IN_NOTEPAD       = process.name=="Everything64" && str.equals(this.name,   "Edit in Notepad")

//
modify(type='*' image=[E122,WHITE]  where='@IF_EVERYTHING64_OPEN'               sep='None')
modify(type='*' image=[E16A,WHITE]  where='@IF_EVERYTHING64_OPEN_PATH'          sep='None')
modify(type='*' image=[E201,WHITE]  where='@IF_EVERYTHING64_OPEN_WITH'          sep='Both'  pos=indexof('Open-Path',1))
//
modify(type='*' image=[E10E,BLUE]   where='@IF_EVERYTHING64_COPY_NAME'          sep='None')
modify(type='*' image=[E114,BLUE]   where='@IF_EVERYTHING64_COPY_FULL_PATH'     sep='None')
//
modify(type='*' image=[E10E,GREEN]  where='@IF_EVERYTHING64_COPY_DATE_MODIFIED' sep='None')
modify(type='*' image=[E10E,GREEN]  where='@IF_EVERYTHING64_COPY_EXTENSION'     sep='None')
modify(type='*' image=[E10E,GREEN]  where='@IF_EVERYTHING64_COPY_EXTENSION'     sep='None')
modify(type='*' image=[E10E,GREEN]  where='@IF_EVERYTHING64_COPY_SIZE'          sep='None')
modify(type='*' image=[E10E,GREEN]  where='@IF_EVERYTHING64_COPY_TYPE'          sep='None')
//
modify(type='*' image=[E167,ORANGE] where='@IF_EVERYTHING64_SET_RUN_COUNT'      sep='Both')
modify(type='*' image=[E0C9,PURPLE] where='@IF_EVERYTHING64_PIN_TO'             sep='None'  pos=indexof('Set-Run-Count',1))

// -> visibility overrides
modify(type='*' image=exe_everything where='@IF_EVERYTHING64_EXPLORE_IN_EVERYTHING' vis='Hidden')