item(mode="single" type='file' where=sel.file.len != sel.file.title.len title='Copy "'+@sel.file.title+'"' image=[\uE055, #4d8e4a] cmd=command.copy(sel.file.title))
item(mode="single" title='Copy "'+@sel.path+'"' tip=sel.path image=[\uE09B, #4d8e4a] cmd=command.copy(sel.path))
item(mode="single" where=@sel.parent.len>3 title='Copy "'+sel.parent+'"' image=[\uE0E7, #4d8e4a] cmd=@command.copy(sel.parent))
separator
