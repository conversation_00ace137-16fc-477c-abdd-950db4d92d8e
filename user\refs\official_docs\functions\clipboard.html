﻿<h5 id="clipboard">CLIPBOARD</h5>
<br>
<p>Clipboard handling functions.</p>
<br>

<section id="clipboard.get" class="my-5">
	<h5>clipboard.get</h5>
	<p>Returns the value of the stored clipboard.</p>
	<p>Syntax</p>
	<code>clipboard.get</code>
</section>

<section id="clipboard.set" class="my-5">
	<h5>clipboard.set</h5>
	<p>Store a value in the clipboard.</p>
	<p>Syntax</p>
	<code>clipboard.set("Hello world!")</code>
</section>

<section id="clipboard.length" class="my-5">
	<h5>clipboard.length</h5>
	<p>Returns the length of the value stored in the clipboard.</p>
	<p>Syntax</p>
	<code>clipboard.length</code>
</section>

<section id="clipboard.is_empty" class="my-5">
	<h5>clipboard.is_empty</h5>
	<p>Verifies that there is a value stored in the clipboard.</p>
	<p>Syntax</p>
	<code>clipboard.is_empty</code>
</section>

<section id="clipboard.empty" class="my-5">
	<h5>clipboard.empty</h5>
	<p>Empty values stored in the clipboard.</p>
	<p>Syntax</p>
	<code>clipboard.empty</code>
</section>
