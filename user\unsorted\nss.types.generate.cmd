@ECHO OFF
SETLOCAL

SET "SOURCEFILE=.nss"

copy "%SOURCEFILE%" "%cd%\.nss"
copy "%SOURCEFILE%" "%cd%\file.nss"
copy "%SOURCEFILE%" "%cd%\directory.nss"
copy "%SOURCEFILE%" "%cd%\dir.nss"
copy "%SOURCEFILE%" "%cd%\drive.nss"
copy "%SOURCEFILE%" "%cd%\usb.nss"
copy "%SOURCEFILE%" "%cd%\dvd.nss"
copy "%SOURCEFILE%" "%cd%\fixed.nss"
copy "%SOURCEFILE%" "%cd%\vhd.nss"
copy "%SOURCEFILE%" "%cd%\removable.nss"
copy "%SOURCEFILE%" "%cd%\remote.nss"
copy "%SOURCEFILE%" "%cd%\back.nss"
copy "%SOURCEFILE%" "%cd%\desktop.nss"
copy "%SOURCEFILE%" "%cd%\namespace.nss"
copy "%SOURCEFILE%" "%cd%\computer.nss"
copy "%SOURCEFILE%" "%cd%\recyclebin.nss"
copy "%SOURCEFILE%" "%cd%\taskbar.nss"
pause

















