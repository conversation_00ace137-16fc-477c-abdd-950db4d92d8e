// // Hide default menu
// modify(find='7-Zip' vis=hidden)

// Hide by CLSID: 7zip
remove(clsid='{23170F69-40C1-278A-1000-000100020000}' where=!this.isuwp)

//
$exe_7zipC='@user.desktop/my/flow/home/<USER>/Apps/app_7zip/exe/App/7-Zip64/7z.exe'
$exe_7zipG='@user.desktop/my/flow/home/<USER>/Apps/app_7zip/exe/App/7-Zip64/7zG.exe'
$exe_7zipA='@user.desktop/my/flow/home/<USER>/Apps/app_7zip/exe/App/7-Zip64/7zFM.exe'
//
$ext_formats = '7z|xz|bz2|gz|tar|zip|wim'
$ext_archive = 'apfs|ar|arj|cab|chm|cpio|cramfs|dmg|ext|fat|gpt|hfs|ihex|iso|lzh|lzma|mbr|msi|nsis|ntfs|qcow2|rar|rpm|squashfs|udf|uefi|vdi|vhd|vhdx|vmdk|xar|z'
$ext_checksum = 'sha256|sha512|sha224|sha384|sha1|sha|md5|crc32|crc64|asc|cksum'
$exe_textfile = 'txt|log|cfg|c|cpp|java|py|html|xml|ini|conf|yaml|json|bat|sh|ps1|csv|doc'

// image=[E0AA,WHITE1] image-sel=[E0AA,HOVER]
// image=[E1A4,WHITE] image-sel=[E1A4,HOVER]
// Main
menu(title="App :  &7-Zip" mode='multiple'  type='file|dir|drive|back' image='@exe_7zipG') {
    $is_se7z1=if(keys.shift(), '', str.replace('."'+ext_formats+'|'+ext_archive+'"', '|', '"|."'))
    $is_se7z2=if(keys.shift(), '', str.replace('."'+ext_checksum+'"', '|', '"|."'))
    $is_se7z3=str.replace('."'+exe_textfile+'"', '|', '"|."')
    item(title='Browse with 7-Zip...'   mode='single' type='dir|drive|back'     image=inherit cmd=exe_7zipA args=sel(true))
    item(title='Open with 7-Zip...'     mode='single' type='file' find=is_se7z1 image=inherit cmd=exe_7zipA args=sel(true))
    menu(title='Open like...'      mode='single' type='file' where=keys.shift() image=inherit) {
        item(title='*'      tip='Opens only one top level archive' cmd=exe_7zipA   args='@sel(true) -t*')
        item(title='#'      tip='Opens file in Parser mode, and ignores full archives.' cmd=exe_7zipA   args='@sel(true) -t#')
        item(title='#:e'    tip='Opens file in Parser mode and checks all byte positions as start of archive.' cmd=exe_7zipA   args='@sel(true) -t#:e')
        item(title='7z'     cmd=exe_7zipA   args='@sel(true) -t7z')
        item(title='zip'    cmd=exe_7zipA   args='@sel(true) -tzip')
        item(title='cab'    cmd=exe_7zipA   args='@sel(true) -tcab')
        item(title='rar'    cmd=exe_7zipA   args='@sel(true) -trar')
    }

    $sel_air=str.replace('"-air!@sel(false, '" "-air!')#', '" "-air#', '" -an')
    menu(title='Extract...' type='file' find=is_se7z1 image=[E1A4,WHITE] image-sel=[E1A4,HOVER] expanded=1) {
        separator()

        item(title='Extract...@"\t"with manager' image=inherit cmd=exe_7zipG args='x @sel_air -ad -o@if(sel.count>1, '*\', '@sel.path.title\')')
        menu(title='Extract...' where=keys.shift() image=inherit) {
            item(title='Extract files...@"\t"single' mode='single' image=inherit cmd=exe_7zipG args='x @sel(true) -<EMAIL>\ -ad')
            item(title='Extract to "@sel.title\", delete' mode='single' image=inherit commands {
                    cmd=exe_7zipG args='x @sel(true) -o*\' wait = 1,
                    cmd=io.delete(sel) wait = 1,
                    cmd=command.refresh
                    }
            )
            item(title='Extract to "@sel.title\", ask to delete' mode='single' image=inherit commands {
                    cmd=exe_7zipG args='x @sel(true) -o*\' wait = 1,
                    cmd=if(msg("Are you sure you want to delete the archive file?","NileSoft Shell", msg.warning | msg.yesno)==msg.idyes, io.delete(sel)) wait = 1,
                    cmd=command.refresh}
                )
        }
        item(title='Extract Here' image=inherit cmd=exe_7zipG args='x @sel_air -spe')
        item(title='Extract'+if(sel.count==1, ' to "@sel.title\"', ' each archive to separate folder') image=inherit cmd=exe_7zipG args='x @sel_air -o*\ -spe') }
    menu(title='Archive...' type='file|dir|drive|back' image=[E1A4,WHITE] image-sel=[E1A4,HOVER] expanded=1) {
        separator()

        item(title='Add to...@"\t"with manager' image=inherit cmd=exe_7zipG args='a @(sel.title) -ad -sae -- @sel(true)')
        menu(title='Add to...' where=keys.shift() image=inherit) {
            item(title='Add to...@"\t"SHIFT to mail' image=inherit cmd=exe_7zipG args='a @(sel.title) -ad @if(keys.shift(), '-seml.') -sae -- @sel(true)')
            item(title='Add to "@(sel.title).7z"@"\t"SHIFT to mail' image=inherit cmd=exe_7zipG args='a @(sel.title).7z -t7z  @if(keys.shift(), '-seml.') -sae -- @sel(true)')
            item(title='Add to "@(sel.title).zip"@"\t"SHIFT to mail' image=inherit cmd=exe_7zipG args='a @(sel.title).zip -tzip @if(keys.shift(), '-seml.') -sae -- @sel(true)')
        }

        item(title='Add to "@(sel.title).7z"@"\t"SHIFT to .zip' image=inherit cmd=exe_7zipG args='a @(sel.title)@if(!keys.shift(), '.7z -t7z', '.zip -tzip') -sae -- @sel(true)')
        item(title='Add to "@(sel.title).ppmd.7z" @"\t"SHIFT to .zip' find=is_se7z3 tip='PPMd compression is particularly effective for compressing text files that have a lot of repetitive patterns and structured content' image=inherit cmd=exe_7zipG args='a @(sel.title).ppmd@if(!keys.shift(), '.7z -t7z', '.zip -tzip') -m0=PPMd -sae -- @sel(true)')
        item(title='Add to "@(sel.title).sfx.exe"@"\t"SHIFT no GUI' image=inherit cmd=exe_7zipG args='a @(sel.title).sfx.exe @if(!keys.shift(), '-sfx7z.sfx', '-sfx7zCon.sfx') -sae -- @sel(true)')
        item(title='Generate a file checksum ' image=inherit cmd=exe_7zipG args='a @(sel.name).sha256 -thash -sae -- @sel(true)')
    }
    menu(title='Test...' type='file' image=[E1A4,WHITE] image-sel=[E1A4,HOVER] expanded=1) {
        separator()

        item(title='Test archive@if(sel.count>1,'s')'  find=is_se7z1 image=inherit cmd=exe_7zipG args='t @sel_air')
        item(title='Test and list...' find=is_se7z1 image=inherit tip='Test the integrity of the archive and list the files afterwards.' cmd-line='/k @path.short(exe_7zipC) l @sel_air & pause & exit')
        item(title='Test Checksum' find=is_se7z2 image=inherit cmd=exe_7zipG args='t @sel_air -thash')

        menu(title='Checksum' type='file|dir' where=keys.shift() image=inherit) {
            item(title='CRC 32'     cmd=exe_7zipG args='h -scrcCRC32  @sel(true)')
            item(title='CRC 64'     cmd=exe_7zipG args='h -scrcCRC64  @sel(true)')
            item(title='SHA 1'      cmd=exe_7zipG args='h -scrcSHA1   @sel(true)')
            item(title='SHA 256'    cmd=exe_7zipG args='h -scrcSHA256 @sel(true)')
            item(title='*'          cmd=exe_7zipG args='h -scrc*      @sel(true)')
        }
    }

    menu(title='7-Zip Info' type='file|dir|drive|back' where=key.shift() sep='before' image=[E1A4,WHITE] image-sel=[E1A4,HOVER]) {
        item(title='Homepage...' cmd='https://7-zip.org/')
        item(title='Documentation...' cmd=path.combine(sys.prog,'7-Zip','7-zip.chm'))
        item(title='Command Line Version User@"'"s Guide...' cmd='https://7-zip.opensource.jp/chm/cmdline/')
        item(title='Supported Formats Info...' cmd-line='/k @path.short(exe_7zipC) i & pause & exit')
    }
}

/*
    PPMd compression is particularly effective for compressing text files that have a lot of repetitive patterns and structured content. Some common file types and extensions for which PPMd compression can be beneficial include:
    Plain Text Files: Regular text files that contain human-readable text without any formatting. These can include files with extensions like
    .txt, .log, .cfg, etc.
    Source Code Files: Programming source code files, which often have a lot of repeated keywords, symbols, and structures. Examples include
    .c, .cpp, .java, .py, etc.
    Markup Languages: Text-based markup languages that define structure and formatting, such as HTML, XML, LaTeX, etc. Examples include
    .html, .xml, .tex, etc.
    Configuration Files: Various configuration files used by software applications or operating systems. Examples include
    .ini, .conf, .yaml, .json, etc.
    Script Files: Script files used for automation or scripting purposes. Examples include
    .bat, .sh, .ps1, etc.
    Data Files: Certain types of data files that contain structured information. Examples include
    .csv, .tsv, .json, etc.
    Documentation Files: Documentation files that contain technical documentation, manuals, or other explanatory text. Examples include
    .doc, .pdf, .md, etc. (though these may contain non-text elements as well).
    Log Files: Log files generated by software applications to record events or actions. Examples include
    .log, .event, etc.
    Configuration Files: Files that store configuration settings for applications or systems. Examples include
    .config, .cfg, .ini, etc.
    It's important to note that while PPMd compression can be effective for these types of files, the actual compression ratio achieved depends on the specific content and patterns within each file. Not all text files will benefit equally from PPMd compression, and in some cases, other compression algorithms may perform better.
*/
