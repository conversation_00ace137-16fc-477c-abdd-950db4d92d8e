$segoe_icon_size=13

menu(title='Path' type='dir|back.dir|drive|back.drive' mode='multiple' image=image.fluent(\uE712,segoe_icon_size)){
        item(title='Append to system path' cmd='powershell' args='-Command @sel("\\\"",",") | ? { $key = [Microsoft.Win32.Registry]::LocalMachine.OpenSubKey(\"System\\CurrentControlSet\\Control\\Session Manager\\Environment\", $true); $path = $key.GetValue(\"Path\", $null, \"DoNotExpandEnvironmentNames\"); $path -inotlike \"*$_*\" } | & { $key.SetValue(\"Path\", (, $path.Trim(\";\") + $input) -join \";\", \"ExpandString\"); $key.Dispose() }' admin window=hidden image=image.fluent(\uEA52,segoe_icon_size))
        item(title='Prepend to system path' cmd='powershell' args='-Command @sel("\\\"",",") | ? { $key = [Microsoft.Win32.Registry]::LocalMachine.OpenSubKey(\"System\\CurrentControlSet\\Control\\Session Manager\\Environment\", $true); $path = $key.GetValue(\"Path\", $null, \"DoNotExpandEnvironmentNames\"); $path -inotlike \"*$_*\" } | & { $key.SetValue(\"Path\", ($input + $path.Trim(\";\")) -join \";\", \"ExpandString\"); $key.Dispose() }' admin window=hidden image=image.fluent(\uE8B5,segoe_icon_size))
        item(title='Append to user path' cmd='powershell' args='-Command @sel("\\\"",",") | ? { $key = [Microsoft.Win32.Registry]::CurrentUser.OpenSubKey(\"Environment\", $true); $path = $key.GetValue(\"Path\", $null, \"DoNotExpandEnvironmentNames\"); $path -inotlike \"*$_*\" } | & { $key.SetValue(\"Path\", (, $path.Trim(\";\") + $input) -join \";\", \"ExpandString\"); $key.Dispose() }' admin window=hidden image=image.fluent(\uEA53,segoe_icon_size))
        item(title='Prepend to user path' cmd='powershell' args='-Command @sel("\\\"",",") | ? { $key = [Microsoft.Win32.Registry]::CurrentUser.OpenSubKey(\"Environment\", $true); $path = $key.GetValue(\"Path\", $null, \"DoNotExpandEnvironmentNames\"); $path -inotlike \"*$_*\" } | & { $key.SetValue(\"Path\", ($input + $path.Trim(\";\")) -join \";\", \"ExpandString\"); $key.Dispose() }' admin window=hidden image=image.fluent(\uE8B6,segoe_icon_size))
}