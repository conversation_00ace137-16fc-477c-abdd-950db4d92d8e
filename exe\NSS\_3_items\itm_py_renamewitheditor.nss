
//
$PY_RENAMEWITHEDITOR_DIR = '@user.desktop\my\flow\home\__GOTO__\Scripts\Python\Py_Cli_Utils\py__RenameWithEditor'
$PY_RENAMEWITHEDITOR_EXE = '@PY_RENAMEWITHEDITOR_DIR\venv\Scripts\python.exe'
$PY_RENAMEWITHEDITOR_APP = '@PY_RENAMEWITHEDITOR_DIR\src\main.py'
//
// $CMD_GIT_ADD_SELECTION_F_V = '/K (CD /D "@sel.dir") && (git add @BATCH_SEL_AS_STRING -f) && (@BATCH_EXIT_WITH_MSG) && PAUSE'

// Context: Explorer
$PY_RENAMEWITHEDITOR_EXPLORER = '-d "@sel.dir" --prompt'
item(
    title="&RenameWithEditor"
    keys="py"
    type='File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    //
    image=[E17C,GREY] image-sel=[E17<PERSON>,PURPLE]
    tip=['"@PY_RENAMEWITHEDITOR_APP" @PY_RENAMEWITHEDITOR_EXPLORER',TIP3,0.75]
    //
    admin=keys.rbutton()
    cmd=if(KEYS_EXE_OPEN_EXE,('"@PY_RENAMEWITHEDITOR_EXE"'))
    args='"@PY_RENAMEWITHEDITOR_APP" @PY_RENAMEWITHEDITOR_EXPLORER'
    //
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@PY_RENAMEWITHEDITOR_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@PY_RENAMEWITHEDITOR_APP')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@PY_RENAMEWITHEDITOR_DIR')),
    }
)
// Context: Taskbar
$PY_RENAMEWITHEDITOR_TASKBAR = '-d "@user.desktop" --prompt'
item(
    title="&RenameWithEditor"
    keys="py"
    type='Taskbar'
    //
    image=[E17C,GREY]
    image-sel=[E17C,PURPLE]
    //
    admin=keys.rbutton()
    cmd=if(KEYS_EXE_OPEN_EXE,('"@PY_RENAMEWITHEDITOR_EXE"'))
    args='"@PY_RENAMEWITHEDITOR_APP" @PY_RENAMEWITHEDITOR_TASKBAR'
    tip=['"@PY_RENAMEWITHEDITOR_APP" @PY_RENAMEWITHEDITOR_TASKBAR',TIP3,0.75]
    //
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@PY_RENAMEWITHEDITOR_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@PY_RENAMEWITHEDITOR_APP')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@PY_RENAMEWITHEDITOR_DIR')),
    }
)
