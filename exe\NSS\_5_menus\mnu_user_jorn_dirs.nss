
//
menu(title='Jorn : &Work@"\t"NAS' type='Taskbar|File|Desktop|Dir|Drive|Back.Dir|Back.Drive' image=[E22C,WHITE1] image-sel=[E22C,ORANGE] sep='None') {
    // item(column vis='Static' image=[E00A,DARK])
    import '@app.dir/NSS/_4_groups/grp_jorn_dirs_nas_work.nss'
    separator()
}

//
menu(title='Jorn : &NAS@"\t"NAS' type='Taskbar|File|Desktop|Dir|Drive|Back.Dir|Back.Drive' image=[E120,WHITE1] image-sel=[E120,BLUE] sep='None') {
    item(column vis='Static' image=[E00A,DARK])
    import '@app.dir/NSS/_4_groups/grp_jorn_dirs_nas_workflow.nss'
    separator()

    //
    item(column vis='Static' image=[E00A,DARK])
    import '@app.dir/NSS/_4_groups/grp_jorn_dirs_nas_cloud.nss'
    separator()

    //
    item(column vis='Static' image=[E00A,DARK])
    import '@app.dir/NSS/_4_groups/grp_jorn_dirs_nas_android.nss'
    separator()

    //
    item(column vis='Static' image=[E00A,DARK])
    import '@app.dir/NSS/_4_groups/grp_jorn_dirs_nas_networkdrives.nss'
    separator()
}

//
menu(title='Jorn : &Flow@"\t"NAS' type='Taskbar|File|Desktop|Dir|Drive|Back.Dir|Back.Drive' image=[E121,WHITE1] image-sel=[E121,GREEN] sep='None') {
    item(column vis='Static' image=[E00A,DARK])
    import '@app.dir/NSS/_4_groups/grp_jorn_dirs_nas_flow.nss'
    separator()
    import '@app.dir/NSS/_4_groups/grp_jorn_dirs_DESKTOP-8058SHG_flow.nss'
}




// //
// menu(title='Goto : &Portal@"\t"Jorn' type='Taskbar|File|Desktop|Dir|Drive|Back.Dir|Back.Drive' image=[E22C,WHITE1] image-sel=[E22C,GREEN] sep='None') {
//     // item(column vis='Static' image=[E00A,DARK])
//     import '@app.dir/NSS/_4_groups/grp_jorn_dirs_git_portal.nss'
//     separator()
// }
