
//
$APP_USER_LOSSLESSCUT_DIR = '@user.desktop\my\flow\home\__GOTO__\Apps\app_losslesscut\exe'
$APP_USER_LOSSLESSCUT_EXE = '@APP_USER_LOSSLESSCUT_DIR\LosslessCut.exe'
$APP_USER_LOSSLESSCUT_TIP = "..."+str.trimstart('@APP_USER_LOSSLESSCUT_EXE','@app.dir')

// context: directory
item(
    title  = ":  &LosslessCut"
    keys   = "exe"
    type   = 'File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    find   = '.ts|.mp4|.mkv|.webm|.mov|.wmv|.avi|.flv|.mpg'
    args   = '"@sel.file"'
    //
    image  = APP_USER_LOSSLESSCUT_EXE
    tip    = [APP_USER_LOSSLESSCUT_TIP,TIP3,0.8]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_USER_LOSSLESSCUT_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_LOSSLESSCUT_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_LOSSLESSCUT_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_LOSSLESSCUT_DIR')),
    }
)
// Context: Taskbar
item(
    title  = ":  &LosslessCut"
    keys   = "exe"
    type   = 'Taskbar'
    args   = ''
    //
    image  = APP_USER_LOSSLESSCUT_EXE
    tip    = [APP_USER_LOSSLESSCUT_TIP,TIP3,0.8]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_USER_LOSSLESSCUT_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_LOSSLESSCUT_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_LOSSLESSCUT_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_LOSSLESSCUT_DIR')),
    }
)
