$nilesoft_icon_size=25
$segoe_icon_size=13

menu(type='taskbar' title='Icon Viewer' image=icon.search)
{
	menu(title='Windows')
	{
		item(image=icon.edit title=' ' tip=['edit',tip.info] cmd=command.copy('icon.edit') col)
		item(image=icon.personalize title=' ' tip=['personalize',tip.info] cmd=command.copy('icon.personalize'))
		item(image=icon.cleanup title=' ' tip=['cleanup',tip.info] cmd=command.copy('icon.cleanup'))
		item(image=icon.paste title=' ' tip=['paste',tip.info] cmd=command.copy('icon.paste'))
		item(image=icon.paste_shortcut title=' ' tip=['paste_shortcut',tip.info] cmd=command.copy('icon.paste_shortcut'))
		item(image=icon.rename title=' ' tip=['rename',tip.info] cmd=command.copy('icon.rename'))
		item(image=icon.show_file_extensions title=' ' tip=['show_file_extensions',tip.info] cmd=command.copy('icon.show_file_extensions'))
		item(image=icon.preview title=' ' tip=['preview',tip.info] cmd=command.copy('icon.preview'))
		item(image=icon.copy title=' ' tip=['copy',tip.info] cmd=command.copy('icon.copy'))
		item(image=icon.copy_here title=' ' tip=['copy_here',tip.info] cmd=command.copy('icon.copy_here'))

		item(image=icon.copy_path title=' ' tip=['copy_path',tip.info] cmd=command.copy('icon.copy_path') col)
		item(image=icon.cut title=' ' tip=['cut',tip.info] cmd=command.copy('icon.cut'))
		item(image=icon.delete title=' ' tip=['delete',tip.info] cmd=command.copy('icon.delete'))
		item(image=icon.send_to title=' ' tip=['send_to',tip.info] cmd=command.copy('icon.send_to'))
		item(image=icon.include_in_library title=' ' tip=['include_in_library',tip.info] cmd=command.copy('icon.include_in_library'))
		item(image=icon.open_file_location title=' ' tip=['open_file_location',tip.info] cmd=command.copy('icon.open_file_location'))
		item(image=icon.new title=' ' tip=['new',tip.info] cmd=command.copy('icon.new'))
		item(image=icon.new_file title=' ' tip=['new_file',tip.info] cmd=command.copy('icon.new_file'))
		item(image=icon.new_folder title=' ' tip=['new_folder',tip.info] cmd=command.copy('icon.new_folder'))
		item(image=icon.move_to title=' ' tip=['move_to',tip.info] cmd=command.copy('icon.move_to'))

		item(image=icon.compressed title=' ' tip=['compressed',tip.info] cmd=command.copy('icon.compressed') col)
		item(image=icon.create_shortcut title=' ' tip=['create_shortcut',tip.info] cmd=command.copy('icon.create_shortcut'))
		item(image=icon.open_in_new_tab title=' ' tip=['open_in_new_tab',tip.info] cmd=command.copy('icon.open_in_new_tab'))
		item(image=icon.open_in_new_window title=' ' tip=['open_in_new_window',tip.info] cmd=command.copy('icon.open_in_new_window'))
		item(image=icon.more_options title=' ' tip=['more_options',tip.info] cmd=command.copy('icon.more_options'))
		item(image=icon.share title=' ' tip=['share',tip.info] cmd=command.copy('icon.share'))
		item(image=icon.open_with title=' ' tip=['open_with',tip.info] cmd=command.copy('icon.open_with'))
		item(image=icon.expand title=' ' tip=['expand',tip.info] cmd=command.copy('icon.expand'))
		item(image=icon.expand_group title=' ' tip=['expand_group',tip.info] cmd=command.copy('icon.expand_group'))
		item(image=icon.collapse title=' ' tip=['collapse',tip.info] cmd=command.copy('icon.collapse'))

		item(image=icon.collapse_group title=' ' tip=['collapse_group',tip.info] cmd=command.copy('icon.collapse_group') col)
		item(image=icon.search title=' ' tip=['search',tip.info] cmd=command.copy('icon.search'))
		item(image=icon.restore title=' ' tip=['restore',tip.info] cmd=command.copy('icon.restore'))
		item(image=icon.undo title=' ' tip=['undo',tip.info] cmd=command.copy('icon.undo'))
		item(image=icon.redo title=' ' tip=['redo',tip.info] cmd=command.copy('icon.redo'))
		item(image=icon.reconversion title=' ' tip=['reconversion',tip.info] cmd=command.copy('icon.reconversion'))
		item(image=icon.refresh title=' ' tip=['refresh',tip.info] cmd=command.copy('icon.refresh'))
		item(image=icon.autoplay title=' ' tip=['autoplay',tip.info] cmd=command.copy('icon.autoplay'))
		item(image=icon.restore_previous_versions title=' ' tip=['restore_previous_versions',tip.info] cmd=command.copy('icon.restore_previous_versions'))
		item(image=icon.rotate_left title=' ' tip=['rotate_left',tip.info] cmd=command.copy('icon.rotate_left'))

		item(image=icon.rotate_right title=' ' tip=['rotate_right',tip.info] cmd=command.copy('icon.rotate_right') col)
		item(image=icon.eject title=' ' tip=['eject',tip.info] cmd=command.copy('icon.eject'))
		item(image=icon.add_to_favorites title=' ' tip=['add_to_favorites',tip.info] cmd=command.copy('icon.add_to_favorites'))
		item(image=icon.cancel title=' ' tip=['cancel',tip.info] cmd=command.copy('icon.cancel'))
		item(image=icon.install title=' ' tip=['install',tip.info] cmd=command.copy('icon.install'))
		item(image=icon.sort_by title=' ' tip=['sort_by',tip.info] cmd=command.copy('icon.sort_by'))
		item(image=icon.pin title=' ' tip=['pin',tip.info] cmd=command.copy('icon.pin'))
		item(image=icon.unpin_from_start title=' ' tip=['unpin_from_start',tip.info] cmd=command.copy('icon.unpin_from_start'))
		item(image=icon.select_all title=' ' tip=['select_all',tip.info] cmd=command.copy('icon.select_all'))
		item(image=icon.invert_selection title=' ' tip=['invert_selection',tip.info] cmd=command.copy('icon.invert_selection'))

		item(image=icon.select_none title=' ' tip=['select_none',tip.info] cmd=command.copy('icon.select_none') col)
		item(image=icon.merge title=' ' tip=['merge',tip.info] cmd=command.copy('icon.merge'))
		item(image=icon.show_task_view_button title=' ' tip=['show_task_view_button',tip.info] cmd=command.copy('icon.show_task_view_button'))
		item(image=icon.play title=' ' tip=['play',tip.info] cmd=command.copy('icon.play'))
		item(image=icon.properties title=' ' tip=['properties',tip.info] cmd=command.copy('icon.properties'))
		item(image=icon.control_panel title=' ' tip=['control_panel',tip.info] cmd=command.copy('icon.control_panel'))
		item(image=icon.settings title=' ' tip=['settings',tip.info] cmd=command.copy('icon.settings'))
		item(image=icon.display_settings title=' ' tip=['display_settings',tip.info] cmd=command.copy('icon.display_settings'))
		item(image=icon.task_manager title=' ' tip=['task_manager',tip.info] cmd=command.copy('icon.task_manager'))
		item(image=icon.command_prompt title=' ' tip=['command_prompt',tip.info] cmd=command.copy('icon.command_prompt'))

		item(image=icon.adjust_date_time title=' ' tip=['adjust_date_time',tip.info] cmd=command.copy('icon.adjust_date_time') col)
		item(image=icon.manage title=' ' tip=['manage',tip.info] cmd=command.copy('icon.manage'))
		item(image=icon.open_windows_powershell title=' ' tip=['open_windows_powershell',tip.info] cmd=command.copy('icon.open_windows_powershell'))
		item(image=icon.run_as_administrator title=' ' tip=['run_as_administrator',tip.info] cmd=command.copy('icon.run_as_administrator'))
		item(image=icon.troubleshoot_compatibility title=' ' tip=['troubleshoot_compatibility',tip.info] cmd=command.copy('icon.troubleshoot_compatibility'))
		item(image=icon.file_explorer title=' ' tip=['file_explorer',tip.info] cmd=command.copy('icon.file_explorer'))
		item(image=icon.exit_explorer title=' ' tip=['exit_explorer',tip.info] cmd=command.copy('icon.exit_explorer'))
		item(image=icon.device_manager title=' ' tip=['device_manager',tip.info] cmd=command.copy('icon.device_manager'))
		item(image=icon.add_a_network_location title=' ' tip=['add_a_network_location',tip.info] cmd=command.copy('icon.add_a_network_location'))
		item(image=icon.map_network_drive title=' ' tip=['map_network_drive',tip.info] cmd=command.copy('icon.map_network_drive'))

		item(image=icon.disconnect_network_drive title=' ' tip=['disconnect_network_drive',tip.info] cmd=command.copy('icon.disconnect_network_drive') col)
		item(image=icon.format title=' ' tip=['format',tip.info] cmd=command.copy('icon.format'))
		item(image=icon.turn_on_bitlocker title=' ' tip=['turn_on_bitlocker',tip.info] cmd=command.copy('icon.turn_on_bitlocker'))
		item(image=icon.mount title=' ' tip=['mount',tip.info] cmd=command.copy('icon.mount'))
		item(image=icon.make_available_online title=' ' tip=['make_available_online',tip.info] cmd=command.copy('icon.make_available_online'))
		item(image=icon.make_available_offline title=' ' tip=['make_available_offline',tip.info] cmd=command.copy('icon.make_available_offline'))
		item(image=icon.give_access_to title=' ' tip=['give_access_to',tip.info] cmd=command.copy('icon.give_access_to'))
		item(image=icon.show_people_on_the_taskbar title=' ' tip=['show_people_on_the_taskbar',tip.info] cmd=command.copy('icon.show_people_on_the_taskbar'))
		item(image=icon.set_as_desktop_wallpaper title=' ' tip=['set_as_desktop_wallpaper',tip.info] cmd=command.copy('icon.set_as_desktop_wallpaper'))
		item(image=icon.next_desktop_background title=' ' tip=['next_desktop_background',tip.info] cmd=command.copy('icon.next_desktop_background'))

		item(image=icon.align_icons_to_grid title=' ' tip=['align_icons_to_grid',tip.info] cmd=command.copy('icon.align_icons_to_grid') col)
		item(image=icon.auto_arrange_icons title=' ' tip=['auto_arrange_icons',tip.info] cmd=command.copy('icon.auto_arrange_icons'))
		item(image=icon.extra_large_icons title=' ' tip=['extra_large_icons',tip.info] cmd=command.copy('icon.extra_large_icons'))
		item(image=icon.large_icons title=' ' tip=['large_icons',tip.info] cmd=command.copy('icon.large_icons'))
		item(image=icon.medium_icons title=' ' tip=['medium_icons',tip.info] cmd=command.copy('icon.medium_icons'))
		item(image=icon.details title=' ' tip=['details',tip.info] cmd=command.copy('icon.details'))
		item(image=icon.list title=' ' tip=['list',tip.info] cmd=command.copy('icon.list'))
		item(image=icon.content title=' ' tip=['content',tip.info] cmd=command.copy('icon.content'))
		item(image=icon.tiles title=' ' tip=['tiles',tip.info] cmd=command.copy('icon.tiles'))
		item(image=icon.customize_this_folder title=' ' tip=['customize_this_folder',tip.info] cmd=command.copy('icon.customize_this_folder'))

		item(image=icon.group_by title=' ' tip=['group_by',tip.info] cmd=command.copy('icon.group_by') col)
		item(image=icon.desktop title=' ' tip=['desktop',tip.info] cmd=command.copy('icon.desktop'))
		item(image=icon.show_touch_keyboard_button title=' ' tip=['show_touch_keyboard_button',tip.info] cmd=command.copy('icon.show_touch_keyboard_button'))
		item(image=icon.show_touchpad_button title=' ' tip=['show_touchpad_button',tip.info] cmd=command.copy('icon.show_touchpad_button'))
		item(image=icon.print title=' ' tip=['print',tip.info] cmd=command.copy('icon.print'))
		item(image=icon.customize_notification_icons title=' ' tip=['customize_notification_icons',tip.info] cmd=command.copy('icon.customize_notification_icons'))
		item(image=icon.insert_unicode_control_character title=' ' tip=['insert_unicode_control_character',tip.info] cmd=command.copy('icon.insert_unicode_control_character'))
		item(image=icon.cortana title=' ' tip=['cortana',tip.info] cmd=command.copy('icon.cortana'))
		item(image=icon.store title=' ' tip=['store',tip.info] cmd=command.copy('icon.store'))
	}

	menu(title='Nilesoft Shell SVG')
	{
		item(image=icon.edit title=' ' tip=['edit',tip.info] cmd=command.copy('icon.edit'))
		item(image=icon.personalize title=' ' tip=['personalize',tip.info] cmd=command.copy('icon.personalize'))
		item(image=icon.cleanup title=' ' tip=['cleanup',tip.info] cmd=command.copy('icon.cleanup'))
		item(image=icon.paste title=' ' tip=['paste',tip.info] cmd=command.copy('icon.paste'))
		item(image=icon.rename title=' ' tip=['rename',tip.info] cmd=command.copy('icon.rename'))
		item(image=icon.show_file_extensions title=' ' tip=['show_file_extensions',tip.info] cmd=command.copy('icon.show_file_extensions'))
		item(image=icon.copy title=' ' tip=['copy',tip.info] cmd=command.copy('icon.copy'))
		item(image=icon.cut title=' ' tip=['cut',tip.info] cmd=command.copy('icon.cut'))
		item(image=icon.remove_from_favorites title=' ' tip=['remove_from_favorites',tip.info] cmd=command.copy('icon.remove_from_favorites'))
		item(image=icon.delete title=' ' tip=['delete',tip.info] cmd=command.copy('icon.delete'))

		item(image=icon.open_folder title=' ' tip=['open_folder',tip.info] cmd=command.copy('icon.open_folder') col)
		item(image=icon.send_to title=' ' tip=['send_to',tip.info] cmd=command.copy('icon.send_to'))
		item(image=icon.new_file title=' ' tip=['new_file',tip.info] cmd=command.copy('icon.new_file'))
		item(image=icon.new_folder title=' ' tip=['new_folder',tip.info] cmd=command.copy('icon.new_folder'))
		item(image=icon.move_to title=' ' tip=['move_to',tip.info] cmd=command.copy('icon.move_to'))
		item(image=icon.compressed title=' ' tip=['compressed',tip.info] cmd=command.copy('icon.compressed'))
		item(image=icon.create_shortcut title=' ' tip=['create_shortcut',tip.info] cmd=command.copy('icon.create_shortcut'))
		item(image=icon.open_new_tab title=' ' tip=['open_new_tab',tip.info] cmd=command.copy('icon.open_new_tab'))
		item(image=icon.open_spot_light title=' ' tip=['open_spot_light',tip.info] cmd=command.copy('icon.open_spot_light'))
		item(image=icon.open_new_window title=' ' tip=['open_new_window',tip.info] cmd=command.copy('icon.open_new_window'))

		item(image=icon.more_options title=' ' tip=['more_options',tip.info] cmd=command.copy('icon.more_options') col)
		item(image=icon.share title=' ' tip=['share',tip.info] cmd=command.copy('icon.share'))
		item(image=icon.open_with title=' ' tip=['open_with',tip.info] cmd=command.copy('icon.open_with'))
		item(image=icon.expand_all title=' ' tip=['expand_all',tip.info] cmd=command.copy('icon.expand_all'))
		item(image=icon.collapse title=' ' tip=['collapse',tip.info] cmd=command.copy('icon.collapse'))
		item(image=icon.collapse_all title=' ' tip=['collapse_all',tip.info] cmd=command.copy('icon.collapse_all'))
		item(image=icon.filter title=' ' tip=['filter',tip.info] cmd=command.copy('icon.filter'))
		item(image=icon.undo title=' ' tip=['undo',tip.info] cmd=command.copy('icon.undo'))
		item(image=icon.restore_previous_versions title=' ' tip=['restore_previous_versions',tip.info] cmd=command.copy('icon.restore_previous_versions'))
		item(image=icon.burn_disc_image title=' ' tip=['burn_disc_image',tip.info] cmd=command.copy('icon.burn_disc_image'))

		item(image=icon.rotate_left title=' ' tip=['rotate_left',tip.info] cmd=command.copy('icon.rotate_left') col)
		item(image=icon.rotate_right title=' ' tip=['rotate_right',tip.info] cmd=command.copy('icon.rotate_right'))
		item(image=icon.eject title=' ' tip=['eject',tip.info] cmd=command.copy('icon.eject'))
		item(image=icon.close title=' ' tip=['close',tip.info] cmd=command.copy('icon.close'))
		item(image=icon.install title=' ' tip=['install',tip.info] cmd=command.copy('icon.install'))
		item(image=icon.sort_by title=' ' tip=['sort_by',tip.info] cmd=command.copy('icon.sort_by'))
		item(image=icon.pin title=' ' tip=['pin',tip.info] cmd=command.copy('icon.pin'))
		item(image=icon.unpin title=' ' tip=['unpin',tip.info] cmd=command.copy('icon.unpin'))
		item(image=icon.invert_selection title=' ' tip=['invert_selection',tip.info] cmd=command.copy('icon.invert_selection'))
		item(image=icon.select_none title=' ' tip=['select_none',tip.info] cmd=command.copy('icon.select_none'))

		item(image=icon.properties title=' ' tip=['properties',tip.info] cmd=command.copy('icon.properties') col)
		item(image=icon.file_explorer_options title=' ' tip=['file_explorer_options',tip.info] cmd=command.copy('icon.file_explorer_options'))
		item(image=icon.display_settings title=' ' tip=['display_settings',tip.info] cmd=command.copy('icon.display_settings'))
		item(image=icon.pc title=' ' tip=['pc',tip.info] cmd=command.copy('icon.pc'))
		item(image=icon.command_prompt title=' ' tip=['command_prompt',tip.info] cmd=command.copy('icon.command_prompt'))
		item(image=icon.manage title=' ' tip=['manage',tip.info] cmd=command.copy('icon.manage'))
		item(image=icon.run_with_powershell title=' ' tip=['run_with_powershell',tip.info] cmd=command.copy('icon.run_with_powershell'))
		item(image=icon.run_as_administrator title=' ' tip=['run_as_administrator',tip.info] cmd=command.copy('icon.run_as_administrator'))
		item(image=icon.run_as_different_user title=' ' tip=['run_as_different_user',tip.info] cmd=command.copy('icon.run_as_different_user'))
		item(image=icon.file_explorer title=' ' tip=['file_explorer',tip.info] cmd=command.copy('icon.file_explorer'))

		item(image=icon.format title=' ' tip=['format',tip.info] cmd=command.copy('icon.format') col)
		item(image=icon.turn_on_bitlocker title=' ' tip=['turn_on_bitlocker',tip.info] cmd=command.copy('icon.turn_on_bitlocker'))
		item(image=icon.mount title=' ' tip=['mount',tip.info] cmd=command.copy('icon.mount'))
		item(image=icon.make_available_offline title=' ' tip=['make_available_offline',tip.info] cmd=command.copy('icon.make_available_offline'))
		item(image=icon.set_as_desktop_wallpaper title=' ' tip=['set_as_desktop_wallpaper',tip.info] cmd=command.copy('icon.set_as_desktop_wallpaper'))
		item(image=icon.next_desktop_background title=' ' tip=['next_desktop_background',tip.info] cmd=command.copy('icon.next_desktop_background'))
		item(image=icon.disk_management title=' ' tip=['disk_management',tip.info] cmd=command.copy('icon.disk_management'))
		item(image=icon.align_icons_to_grid title=' ' tip=['align_icons_to_grid',tip.info] cmd=command.copy('icon.align_icons_to_grid'))
		item(image=icon.auto_arrange_icons title=' ' tip=['auto_arrange_icons',tip.info] cmd=command.copy('icon.auto_arrange_icons'))
		item(image=icon.large_icons title=' ' tip=['large_icons',tip.info] cmd=command.copy('icon.large_icons'))

		item(image=icon.small_icons title=' ' tip=['small_icons',tip.info] cmd=command.copy('icon.small_icons') col)
		item(image=icon.view title=' ' tip=['view',tip.info] cmd=command.copy('icon.view'))
		item(image=icon.details title=' ' tip=['details',tip.info] cmd=command.copy('icon.details'))
		item(image=icon.list title=' ' tip=['list',tip.info] cmd=command.copy('icon.list'))
		item(image=icon.tiles title=' ' tip=['tiles',tip.info] cmd=command.copy('icon.tiles'))
		item(image=icon.customize_this_folder title=' ' tip=['customize_this_folder',tip.info] cmd=command.copy('icon.customize_this_folder'))
		item(image=icon.group_by title=' ' tip=['group_by',tip.info] cmd=command.copy('icon.group_by'))
		item(image=icon.desktop title=' ' tip=['desktop',tip.info] cmd=command.copy('icon.desktop'))
		item(image=icon.print title=' ' tip=['print',tip.info] cmd=command.copy('icon.print'))
		item(image=icon.show_hidden_files title=' ' tip=['show_hidden_files',tip.info] cmd=command.copy('icon.show_hidden_files'))

		item(image=icon.view2 title=' ' tip=['view2',tip.info] cmd=command.copy('icon.view2') col)
		item(image=icon.reddit title=' ' tip=['reddit',tip.info] cmd=command.copy('icon.reddit'))
		item(image=icon.nvidia title=' ' tip=['nvidia',tip.info] cmd=command.copy('icon.nvidia'))
	}

	separator

	menu(title='Nilesoft Shell Glyph #1')
	{
		item(image=image.glyph(\uE001,nilesoft_icon_size) tip=['E001',tip.info] cmd=command.copy('image.glyph(\uE001,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE002,nilesoft_icon_size) tip=['E002',tip.info] cmd=command.copy('image.glyph(\uE002,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE003,nilesoft_icon_size) tip=['E003',tip.info] cmd=command.copy('image.glyph(\uE003,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE004,nilesoft_icon_size) tip=['E004',tip.info] cmd=command.copy('image.glyph(\uE004,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE005,nilesoft_icon_size) tip=['E005',tip.info] cmd=command.copy('image.glyph(\uE005,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE006,nilesoft_icon_size) tip=['E006',tip.info] cmd=command.copy('image.glyph(\uE006,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE007,nilesoft_icon_size) tip=['E007',tip.info] cmd=command.copy('image.glyph(\uE007,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE008,nilesoft_icon_size) tip=['E008',tip.info] cmd=command.copy('image.glyph(\uE008,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE009,nilesoft_icon_size) tip=['E009',tip.info] cmd=command.copy('image.glyph(\uE009,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE00A,nilesoft_icon_size) tip=['E00A',tip.info] cmd=command.copy('image.glyph(\uE00A,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE00B,nilesoft_icon_size) tip=['E00B',tip.info] cmd=command.copy('image.glyph(\uE00B,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE00C,nilesoft_icon_size) tip=['E00C',tip.info] cmd=command.copy('image.glyph(\uE00C,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE00D,nilesoft_icon_size) tip=['E00D',tip.info] cmd=command.copy('image.glyph(\uE00D,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE00E,nilesoft_icon_size) tip=['E00E',tip.info] cmd=command.copy('image.glyph(\uE00E,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE00F,nilesoft_icon_size) tip=['E00F',tip.info] cmd=command.copy('image.glyph(\uE00F,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE010,nilesoft_icon_size) tip=['E010',tip.info] cmd=command.copy('image.glyph(\uE010,@nilesoft_icon_size)'))

		item(image=image.glyph(\uE011,nilesoft_icon_size) tip=['E011',tip.info] cmd=command.copy('image.glyph(\uE011,@nilesoft_icon_size)') col)
		item(image=image.glyph(\uE012,nilesoft_icon_size) tip=['E012',tip.info] cmd=command.copy('image.glyph(\uE012,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE013,nilesoft_icon_size) tip=['E013',tip.info] cmd=command.copy('image.glyph(\uE013,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE014,nilesoft_icon_size) tip=['E014',tip.info] cmd=command.copy('image.glyph(\uE014,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE015,nilesoft_icon_size) tip=['E015',tip.info] cmd=command.copy('image.glyph(\uE015,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE016,nilesoft_icon_size) tip=['E016',tip.info] cmd=command.copy('image.glyph(\uE016,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE017,nilesoft_icon_size) tip=['E017',tip.info] cmd=command.copy('image.glyph(\uE017,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE018,nilesoft_icon_size) tip=['E018',tip.info] cmd=command.copy('image.glyph(\uE018,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE019,nilesoft_icon_size) tip=['E019',tip.info] cmd=command.copy('image.glyph(\uE019,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE01A,nilesoft_icon_size) tip=['E01A',tip.info] cmd=command.copy('image.glyph(\uE01A,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE01B,nilesoft_icon_size) tip=['E01B',tip.info] cmd=command.copy('image.glyph(\uE01B,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE01C,nilesoft_icon_size) tip=['E01C',tip.info] cmd=command.copy('image.glyph(\uE01C,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE01D,nilesoft_icon_size) tip=['E01D',tip.info] cmd=command.copy('image.glyph(\uE01D,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE01E,nilesoft_icon_size) tip=['E01E',tip.info] cmd=command.copy('image.glyph(\uE01E,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE01F,nilesoft_icon_size) tip=['E01F',tip.info] cmd=command.copy('image.glyph(\uE01F,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE020,nilesoft_icon_size) tip=['E020',tip.info] cmd=command.copy('image.glyph(\uE020,@nilesoft_icon_size)'))

		item(image=image.glyph(\uE021,nilesoft_icon_size) tip=['E021',tip.info] cmd=command.copy('image.glyph(\uE021,@nilesoft_icon_size)') col)
		item(image=image.glyph(\uE022,nilesoft_icon_size) tip=['E022',tip.info] cmd=command.copy('image.glyph(\uE022,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE023,nilesoft_icon_size) tip=['E023',tip.info] cmd=command.copy('image.glyph(\uE023,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE024,nilesoft_icon_size) tip=['E024',tip.info] cmd=command.copy('image.glyph(\uE024,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE025,nilesoft_icon_size) tip=['E025',tip.info] cmd=command.copy('image.glyph(\uE025,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE026,nilesoft_icon_size) tip=['E026',tip.info] cmd=command.copy('image.glyph(\uE026,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE027,nilesoft_icon_size) tip=['E027',tip.info] cmd=command.copy('image.glyph(\uE027,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE028,nilesoft_icon_size) tip=['E028',tip.info] cmd=command.copy('image.glyph(\uE028,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE029,nilesoft_icon_size) tip=['E029',tip.info] cmd=command.copy('image.glyph(\uE029,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE02A,nilesoft_icon_size) tip=['E02A',tip.info] cmd=command.copy('image.glyph(\uE02A,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE02B,nilesoft_icon_size) tip=['E02B',tip.info] cmd=command.copy('image.glyph(\uE02B,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE02C,nilesoft_icon_size) tip=['E02C',tip.info] cmd=command.copy('image.glyph(\uE02C,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE02D,nilesoft_icon_size) tip=['E02D',tip.info] cmd=command.copy('image.glyph(\uE02D,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE02E,nilesoft_icon_size) tip=['E02E',tip.info] cmd=command.copy('image.glyph(\uE02E,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE02F,nilesoft_icon_size) tip=['E02F',tip.info] cmd=command.copy('image.glyph(\uE02F,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE030,nilesoft_icon_size) tip=['E030',tip.info] cmd=command.copy('image.glyph(\uE030,@nilesoft_icon_size)'))

		item(image=image.glyph(\uE031,nilesoft_icon_size) tip=['E031',tip.info] cmd=command.copy('image.glyph(\uE031,@nilesoft_icon_size)') col)
		item(image=image.glyph(\uE032,nilesoft_icon_size) tip=['E032',tip.info] cmd=command.copy('image.glyph(\uE032,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE033,nilesoft_icon_size) tip=['E033',tip.info] cmd=command.copy('image.glyph(\uE033,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE034,nilesoft_icon_size) tip=['E034',tip.info] cmd=command.copy('image.glyph(\uE034,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE035,nilesoft_icon_size) tip=['E035',tip.info] cmd=command.copy('image.glyph(\uE035,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE036,nilesoft_icon_size) tip=['E036',tip.info] cmd=command.copy('image.glyph(\uE036,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE037,nilesoft_icon_size) tip=['E037',tip.info] cmd=command.copy('image.glyph(\uE037,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE038,nilesoft_icon_size) tip=['E038',tip.info] cmd=command.copy('image.glyph(\uE038,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE039,nilesoft_icon_size) tip=['E039',tip.info] cmd=command.copy('image.glyph(\uE039,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE03A,nilesoft_icon_size) tip=['E03A',tip.info] cmd=command.copy('image.glyph(\uE03A,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE03B,nilesoft_icon_size) tip=['E03B',tip.info] cmd=command.copy('image.glyph(\uE03B,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE03C,nilesoft_icon_size) tip=['E03C',tip.info] cmd=command.copy('image.glyph(\uE03C,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE03D,nilesoft_icon_size) tip=['E03D',tip.info] cmd=command.copy('image.glyph(\uE03D,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE03E,nilesoft_icon_size) tip=['E03E',tip.info] cmd=command.copy('image.glyph(\uE03E,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE03F,nilesoft_icon_size) tip=['E03F',tip.info] cmd=command.copy('image.glyph(\uE03F,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE040,nilesoft_icon_size) tip=['E040',tip.info] cmd=command.copy('image.glyph(\uE040,@nilesoft_icon_size)'))

		item(image=image.glyph(\uE041,nilesoft_icon_size) tip=['E041',tip.info] cmd=command.copy('image.glyph(\uE041,@nilesoft_icon_size)') col)
		item(image=image.glyph(\uE042,nilesoft_icon_size) tip=['E042',tip.info] cmd=command.copy('image.glyph(\uE042,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE043,nilesoft_icon_size) tip=['E043',tip.info] cmd=command.copy('image.glyph(\uE043,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE044,nilesoft_icon_size) tip=['E044',tip.info] cmd=command.copy('image.glyph(\uE044,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE045,nilesoft_icon_size) tip=['E045',tip.info] cmd=command.copy('image.glyph(\uE045,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE046,nilesoft_icon_size) tip=['E046',tip.info] cmd=command.copy('image.glyph(\uE046,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE047,nilesoft_icon_size) tip=['E047',tip.info] cmd=command.copy('image.glyph(\uE047,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE048,nilesoft_icon_size) tip=['E048',tip.info] cmd=command.copy('image.glyph(\uE048,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE049,nilesoft_icon_size) tip=['E049',tip.info] cmd=command.copy('image.glyph(\uE049,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE04A,nilesoft_icon_size) tip=['E04A',tip.info] cmd=command.copy('image.glyph(\uE04A,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE04B,nilesoft_icon_size) tip=['E04B',tip.info] cmd=command.copy('image.glyph(\uE04B,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE04C,nilesoft_icon_size) tip=['E04C',tip.info] cmd=command.copy('image.glyph(\uE04C,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE04D,nilesoft_icon_size) tip=['E04D',tip.info] cmd=command.copy('image.glyph(\uE04D,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE04E,nilesoft_icon_size) tip=['E04E',tip.info] cmd=command.copy('image.glyph(\uE04E,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE04F,nilesoft_icon_size) tip=['E04F',tip.info] cmd=command.copy('image.glyph(\uE04F,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE050,nilesoft_icon_size) tip=['E050',tip.info] cmd=command.copy('image.glyph(\uE050,@nilesoft_icon_size)'))

		item(image=image.glyph(\uE051,nilesoft_icon_size) tip=['E051',tip.info] cmd=command.copy('image.glyph(\uE051,@nilesoft_icon_size)') col)
		item(image=image.glyph(\uE052,nilesoft_icon_size) tip=['E052',tip.info] cmd=command.copy('image.glyph(\uE052,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE053,nilesoft_icon_size) tip=['E053',tip.info] cmd=command.copy('image.glyph(\uE053,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE054,nilesoft_icon_size) tip=['E054',tip.info] cmd=command.copy('image.glyph(\uE054,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE055,nilesoft_icon_size) tip=['E055',tip.info] cmd=command.copy('image.glyph(\uE055,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE056,nilesoft_icon_size) tip=['E056',tip.info] cmd=command.copy('image.glyph(\uE056,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE057,nilesoft_icon_size) tip=['E057',tip.info] cmd=command.copy('image.glyph(\uE057,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE058,nilesoft_icon_size) tip=['E058',tip.info] cmd=command.copy('image.glyph(\uE058,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE059,nilesoft_icon_size) tip=['E059',tip.info] cmd=command.copy('image.glyph(\uE059,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE05A,nilesoft_icon_size) tip=['E05A',tip.info] cmd=command.copy('image.glyph(\uE05A,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE05B,nilesoft_icon_size) tip=['E05B',tip.info] cmd=command.copy('image.glyph(\uE05B,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE05C,nilesoft_icon_size) tip=['E05C',tip.info] cmd=command.copy('image.glyph(\uE05C,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE05D,nilesoft_icon_size) tip=['E05D',tip.info] cmd=command.copy('image.glyph(\uE05D,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE05E,nilesoft_icon_size) tip=['E05E',tip.info] cmd=command.copy('image.glyph(\uE05E,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE05F,nilesoft_icon_size) tip=['E05F',tip.info] cmd=command.copy('image.glyph(\uE05F,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE060,nilesoft_icon_size) tip=['E060',tip.info] cmd=command.copy('image.glyph(\uE060,@nilesoft_icon_size)'))

		item(image=image.glyph(\uE061,nilesoft_icon_size) tip=['E061',tip.info] cmd=command.copy('image.glyph(\uE061,@nilesoft_icon_size)') col)
		item(image=image.glyph(\uE062,nilesoft_icon_size) tip=['E062',tip.info] cmd=command.copy('image.glyph(\uE062,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE063,nilesoft_icon_size) tip=['E063',tip.info] cmd=command.copy('image.glyph(\uE063,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE064,nilesoft_icon_size) tip=['E064',tip.info] cmd=command.copy('image.glyph(\uE064,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE065,nilesoft_icon_size) tip=['E065',tip.info] cmd=command.copy('image.glyph(\uE065,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE066,nilesoft_icon_size) tip=['E066',tip.info] cmd=command.copy('image.glyph(\uE066,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE067,nilesoft_icon_size) tip=['E067',tip.info] cmd=command.copy('image.glyph(\uE067,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE068,nilesoft_icon_size) tip=['E068',tip.info] cmd=command.copy('image.glyph(\uE068,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE069,nilesoft_icon_size) tip=['E069',tip.info] cmd=command.copy('image.glyph(\uE069,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE06A,nilesoft_icon_size) tip=['E06A',tip.info] cmd=command.copy('image.glyph(\uE06A,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE06B,nilesoft_icon_size) tip=['E06B',tip.info] cmd=command.copy('image.glyph(\uE06B,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE06C,nilesoft_icon_size) tip=['E06C',tip.info] cmd=command.copy('image.glyph(\uE06C,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE06D,nilesoft_icon_size) tip=['E06D',tip.info] cmd=command.copy('image.glyph(\uE06D,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE06E,nilesoft_icon_size) tip=['E06E',tip.info] cmd=command.copy('image.glyph(\uE06E,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE06F,nilesoft_icon_size) tip=['E06F',tip.info] cmd=command.copy('image.glyph(\uE06F,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE070,nilesoft_icon_size) tip=['E070',tip.info] cmd=command.copy('image.glyph(\uE070,@nilesoft_icon_size)'))

		item(image=image.glyph(\uE071,nilesoft_icon_size) tip=['E071',tip.info] cmd=command.copy('image.glyph(\uE071,@nilesoft_icon_size)') col)
		item(image=image.glyph(\uE072,nilesoft_icon_size) tip=['E072',tip.info] cmd=command.copy('image.glyph(\uE072,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE073,nilesoft_icon_size) tip=['E073',tip.info] cmd=command.copy('image.glyph(\uE073,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE074,nilesoft_icon_size) tip=['E074',tip.info] cmd=command.copy('image.glyph(\uE074,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE075,nilesoft_icon_size) tip=['E075',tip.info] cmd=command.copy('image.glyph(\uE075,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE076,nilesoft_icon_size) tip=['E076',tip.info] cmd=command.copy('image.glyph(\uE076,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE077,nilesoft_icon_size) tip=['E077',tip.info] cmd=command.copy('image.glyph(\uE077,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE078,nilesoft_icon_size) tip=['E078',tip.info] cmd=command.copy('image.glyph(\uE078,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE079,nilesoft_icon_size) tip=['E079',tip.info] cmd=command.copy('image.glyph(\uE079,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE07A,nilesoft_icon_size) tip=['E07A',tip.info] cmd=command.copy('image.glyph(\uE07A,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE07B,nilesoft_icon_size) tip=['E07B',tip.info] cmd=command.copy('image.glyph(\uE07B,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE07C,nilesoft_icon_size) tip=['E07C',tip.info] cmd=command.copy('image.glyph(\uE07C,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE07D,nilesoft_icon_size) tip=['E07D',tip.info] cmd=command.copy('image.glyph(\uE07D,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE07E,nilesoft_icon_size) tip=['E07E',tip.info] cmd=command.copy('image.glyph(\uE07E,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE07F,nilesoft_icon_size) tip=['E07F',tip.info] cmd=command.copy('image.glyph(\uE07F,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE080,nilesoft_icon_size) tip=['E080',tip.info] cmd=command.copy('image.glyph(\uE080,@nilesoft_icon_size)'))

		item(image=image.glyph(\uE081,nilesoft_icon_size) tip=['E081',tip.info] cmd=command.copy('image.glyph(\uE081,@nilesoft_icon_size)') col)
		item(image=image.glyph(\uE082,nilesoft_icon_size) tip=['E082',tip.info] cmd=command.copy('image.glyph(\uE082,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE083,nilesoft_icon_size) tip=['E083',tip.info] cmd=command.copy('image.glyph(\uE083,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE084,nilesoft_icon_size) tip=['E084',tip.info] cmd=command.copy('image.glyph(\uE084,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE085,nilesoft_icon_size) tip=['E085',tip.info] cmd=command.copy('image.glyph(\uE085,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE086,nilesoft_icon_size) tip=['E086',tip.info] cmd=command.copy('image.glyph(\uE086,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE087,nilesoft_icon_size) tip=['E087',tip.info] cmd=command.copy('image.glyph(\uE087,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE088,nilesoft_icon_size) tip=['E088',tip.info] cmd=command.copy('image.glyph(\uE088,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE089,nilesoft_icon_size) tip=['E089',tip.info] cmd=command.copy('image.glyph(\uE089,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE08A,nilesoft_icon_size) tip=['E08A',tip.info] cmd=command.copy('image.glyph(\uE08A,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE08B,nilesoft_icon_size) tip=['E08B',tip.info] cmd=command.copy('image.glyph(\uE08B,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE08C,nilesoft_icon_size) tip=['E08C',tip.info] cmd=command.copy('image.glyph(\uE08C,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE08D,nilesoft_icon_size) tip=['E08D',tip.info] cmd=command.copy('image.glyph(\uE08D,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE08E,nilesoft_icon_size) tip=['E08E',tip.info] cmd=command.copy('image.glyph(\uE08E,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE08F,nilesoft_icon_size) tip=['E08F',tip.info] cmd=command.copy('image.glyph(\uE08F,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE090,nilesoft_icon_size) tip=['E090',tip.info] cmd=command.copy('image.glyph(\uE090,@nilesoft_icon_size)'))

		item(image=image.glyph(\uE091,nilesoft_icon_size) tip=['E091',tip.info] cmd=command.copy('image.glyph(\uE091,@nilesoft_icon_size)') col)
		item(image=image.glyph(\uE092,nilesoft_icon_size) tip=['E092',tip.info] cmd=command.copy('image.glyph(\uE092,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE093,nilesoft_icon_size) tip=['E093',tip.info] cmd=command.copy('image.glyph(\uE093,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE094,nilesoft_icon_size) tip=['E094',tip.info] cmd=command.copy('image.glyph(\uE094,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE095,nilesoft_icon_size) tip=['E095',tip.info] cmd=command.copy('image.glyph(\uE095,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE096,nilesoft_icon_size) tip=['E096',tip.info] cmd=command.copy('image.glyph(\uE096,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE097,nilesoft_icon_size) tip=['E097',tip.info] cmd=command.copy('image.glyph(\uE097,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE098,nilesoft_icon_size) tip=['E098',tip.info] cmd=command.copy('image.glyph(\uE098,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE099,nilesoft_icon_size) tip=['E099',tip.info] cmd=command.copy('image.glyph(\uE099,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE09A,nilesoft_icon_size) tip=['E09A',tip.info] cmd=command.copy('image.glyph(\uE09A,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE09B,nilesoft_icon_size) tip=['E09B',tip.info] cmd=command.copy('image.glyph(\uE09B,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE09C,nilesoft_icon_size) tip=['E09C',tip.info] cmd=command.copy('image.glyph(\uE09C,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE09D,nilesoft_icon_size) tip=['E09D',tip.info] cmd=command.copy('image.glyph(\uE09D,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE09E,nilesoft_icon_size) tip=['E09E',tip.info] cmd=command.copy('image.glyph(\uE09E,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE09F,nilesoft_icon_size) tip=['E09F',tip.info] cmd=command.copy('image.glyph(\uE09F,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE0A0,nilesoft_icon_size) tip=['E0A0',tip.info] cmd=command.copy('image.glyph(\uE0A0,@nilesoft_icon_size)'))

		item(image=image.glyph(\uE0A1,nilesoft_icon_size) tip=['E0A1',tip.info] cmd=command.copy('image.glyph(\uE0A1,@nilesoft_icon_size)') col)
		item(image=image.glyph(\uE0A2,nilesoft_icon_size) tip=['E0A2',tip.info] cmd=command.copy('image.glyph(\uE0A2,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE0A3,nilesoft_icon_size) tip=['E0A3',tip.info] cmd=command.copy('image.glyph(\uE0A3,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE0A4,nilesoft_icon_size) tip=['E0A4',tip.info] cmd=command.copy('image.glyph(\uE0A4,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE0A5,nilesoft_icon_size) tip=['E0A5',tip.info] cmd=command.copy('image.glyph(\uE0A5,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE0A6,nilesoft_icon_size) tip=['E0A6',tip.info] cmd=command.copy('image.glyph(\uE0A6,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE0A7,nilesoft_icon_size) tip=['E0A7',tip.info] cmd=command.copy('image.glyph(\uE0A7,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE0A8,nilesoft_icon_size) tip=['E0A8',tip.info] cmd=command.copy('image.glyph(\uE0A8,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE0A9,nilesoft_icon_size) tip=['E0A9',tip.info] cmd=command.copy('image.glyph(\uE0A9,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE0AA,nilesoft_icon_size) tip=['E0AA',tip.info] cmd=command.copy('image.glyph(\uE0AA,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE0AB,nilesoft_icon_size) tip=['E0AB',tip.info] cmd=command.copy('image.glyph(\uE0AB,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE0AC,nilesoft_icon_size) tip=['E0AC',tip.info] cmd=command.copy('image.glyph(\uE0AC,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE0AD,nilesoft_icon_size) tip=['E0AD',tip.info] cmd=command.copy('image.glyph(\uE0AD,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE0AE,nilesoft_icon_size) tip=['E0AE',tip.info] cmd=command.copy('image.glyph(\uE0AE,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE0AF,nilesoft_icon_size) tip=['E0AF',tip.info] cmd=command.copy('image.glyph(\uE0AF,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE0B0,nilesoft_icon_size) tip=['E0B0',tip.info] cmd=command.copy('image.glyph(\uE0B0,@nilesoft_icon_size)'))

		item(image=image.glyph(\uE0B1,nilesoft_icon_size) tip=['E0B1',tip.info] cmd=command.copy('image.glyph(\uE0B1,@nilesoft_icon_size)') col)
		item(image=image.glyph(\uE0B2,nilesoft_icon_size) tip=['E0B2',tip.info] cmd=command.copy('image.glyph(\uE0B2,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE0B3,nilesoft_icon_size) tip=['E0B3',tip.info] cmd=command.copy('image.glyph(\uE0B3,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE0B4,nilesoft_icon_size) tip=['E0B4',tip.info] cmd=command.copy('image.glyph(\uE0B4,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE0B5,nilesoft_icon_size) tip=['E0B5',tip.info] cmd=command.copy('image.glyph(\uE0B5,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE0B6,nilesoft_icon_size) tip=['E0B6',tip.info] cmd=command.copy('image.glyph(\uE0B6,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE0B7,nilesoft_icon_size) tip=['E0B7',tip.info] cmd=command.copy('image.glyph(\uE0B7,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE0B8,nilesoft_icon_size) tip=['E0B8',tip.info] cmd=command.copy('image.glyph(\uE0B8,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE0B9,nilesoft_icon_size) tip=['E0B9',tip.info] cmd=command.copy('image.glyph(\uE0B9,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE0BA,nilesoft_icon_size) tip=['E0BA',tip.info] cmd=command.copy('image.glyph(\uE0BA,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE0BB,nilesoft_icon_size) tip=['E0BB',tip.info] cmd=command.copy('image.glyph(\uE0BB,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE0BC,nilesoft_icon_size) tip=['E0BC',tip.info] cmd=command.copy('image.glyph(\uE0BC,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE0BD,nilesoft_icon_size) tip=['E0BD',tip.info] cmd=command.copy('image.glyph(\uE0BD,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE0BE,nilesoft_icon_size) tip=['E0BE',tip.info] cmd=command.copy('image.glyph(\uE0BE,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE0BF,nilesoft_icon_size) tip=['E0BF',tip.info] cmd=command.copy('image.glyph(\uE0BF,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE0C0,nilesoft_icon_size) tip=['E0C0',tip.info] cmd=command.copy('image.glyph(\uE0C0,@nilesoft_icon_size)'))

		item(image=image.glyph(\uE0C1,nilesoft_icon_size) tip=['E0C1',tip.info] cmd=command.copy('image.glyph(\uE0C1,@nilesoft_icon_size)') col)
		item(image=image.glyph(\uE0C2,nilesoft_icon_size) tip=['E0C2',tip.info] cmd=command.copy('image.glyph(\uE0C2,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE0C3,nilesoft_icon_size) tip=['E0C3',tip.info] cmd=command.copy('image.glyph(\uE0C3,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE0C4,nilesoft_icon_size) tip=['E0C4',tip.info] cmd=command.copy('image.glyph(\uE0C4,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE0C5,nilesoft_icon_size) tip=['E0C5',tip.info] cmd=command.copy('image.glyph(\uE0C5,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE0C6,nilesoft_icon_size) tip=['E0C6',tip.info] cmd=command.copy('image.glyph(\uE0C6,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE0C7,nilesoft_icon_size) tip=['E0C7',tip.info] cmd=command.copy('image.glyph(\uE0C7,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE0C8,nilesoft_icon_size) tip=['E0C8',tip.info] cmd=command.copy('image.glyph(\uE0C8,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE0C9,nilesoft_icon_size) tip=['E0C9',tip.info] cmd=command.copy('image.glyph(\uE0C9,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE0CA,nilesoft_icon_size) tip=['E0CA',tip.info] cmd=command.copy('image.glyph(\uE0CA,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE0CB,nilesoft_icon_size) tip=['E0CB',tip.info] cmd=command.copy('image.glyph(\uE0CB,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE0CC,nilesoft_icon_size) tip=['E0CC',tip.info] cmd=command.copy('image.glyph(\uE0CC,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE0CD,nilesoft_icon_size) tip=['E0CD',tip.info] cmd=command.copy('image.glyph(\uE0CD,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE0CE,nilesoft_icon_size) tip=['E0CE',tip.info] cmd=command.copy('image.glyph(\uE0CE,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE0CF,nilesoft_icon_size) tip=['E0CF',tip.info] cmd=command.copy('image.glyph(\uE0CF,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE0D0,nilesoft_icon_size) tip=['E0D0',tip.info] cmd=command.copy('image.glyph(\uE0D0,@nilesoft_icon_size)'))

		item(image=image.glyph(\uE0D1,nilesoft_icon_size) tip=['E0D1',tip.info] cmd=command.copy('image.glyph(\uE0D1,@nilesoft_icon_size)') col)
		item(image=image.glyph(\uE0D2,nilesoft_icon_size) tip=['E0D2',tip.info] cmd=command.copy('image.glyph(\uE0D2,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE0D3,nilesoft_icon_size) tip=['E0D3',tip.info] cmd=command.copy('image.glyph(\uE0D3,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE0D4,nilesoft_icon_size) tip=['E0D4',tip.info] cmd=command.copy('image.glyph(\uE0D4,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE0D5,nilesoft_icon_size) tip=['E0D5',tip.info] cmd=command.copy('image.glyph(\uE0D5,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE0D6,nilesoft_icon_size) tip=['E0D6',tip.info] cmd=command.copy('image.glyph(\uE0D6,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE0D7,nilesoft_icon_size) tip=['E0D7',tip.info] cmd=command.copy('image.glyph(\uE0D7,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE0D8,nilesoft_icon_size) tip=['E0D8',tip.info] cmd=command.copy('image.glyph(\uE0D8,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE0D9,nilesoft_icon_size) tip=['E0D9',tip.info] cmd=command.copy('image.glyph(\uE0D9,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE0DA,nilesoft_icon_size) tip=['E0DA',tip.info] cmd=command.copy('image.glyph(\uE0DA,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE0DB,nilesoft_icon_size) tip=['E0DB',tip.info] cmd=command.copy('image.glyph(\uE0DB,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE0DC,nilesoft_icon_size) tip=['E0DC',tip.info] cmd=command.copy('image.glyph(\uE0DC,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE0DD,nilesoft_icon_size) tip=['E0DD',tip.info] cmd=command.copy('image.glyph(\uE0DD,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE0DE,nilesoft_icon_size) tip=['E0DE',tip.info] cmd=command.copy('image.glyph(\uE0DE,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE0DF,nilesoft_icon_size) tip=['E0DF',tip.info] cmd=command.copy('image.glyph(\uE0DF,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE0E0,nilesoft_icon_size) tip=['E0E0',tip.info] cmd=command.copy('image.glyph(\uE0E0,@nilesoft_icon_size)'))

		item(image=image.glyph(\uE0E1,nilesoft_icon_size) tip=['E0E1',tip.info] cmd=command.copy('image.glyph(\uE0E1,@nilesoft_icon_size)') col)
		item(image=image.glyph(\uE0E2,nilesoft_icon_size) tip=['E0E2',tip.info] cmd=command.copy('image.glyph(\uE0E2,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE0E3,nilesoft_icon_size) tip=['E0E3',tip.info] cmd=command.copy('image.glyph(\uE0E3,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE0E4,nilesoft_icon_size) tip=['E0E4',tip.info] cmd=command.copy('image.glyph(\uE0E4,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE0E5,nilesoft_icon_size) tip=['E0E5',tip.info] cmd=command.copy('image.glyph(\uE0E5,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE0E6,nilesoft_icon_size) tip=['E0E6',tip.info] cmd=command.copy('image.glyph(\uE0E6,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE0E7,nilesoft_icon_size) tip=['E0E7',tip.info] cmd=command.copy('image.glyph(\uE0E7,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE0E8,nilesoft_icon_size) tip=['E0E8',tip.info] cmd=command.copy('image.glyph(\uE0E8,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE0E9,nilesoft_icon_size) tip=['E0E9',tip.info] cmd=command.copy('image.glyph(\uE0E9,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE0EA,nilesoft_icon_size) tip=['E0EA',tip.info] cmd=command.copy('image.glyph(\uE0EA,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE0EB,nilesoft_icon_size) tip=['E0EB',tip.info] cmd=command.copy('image.glyph(\uE0EB,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE0EC,nilesoft_icon_size) tip=['E0EC',tip.info] cmd=command.copy('image.glyph(\uE0EC,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE0ED,nilesoft_icon_size) tip=['E0ED',tip.info] cmd=command.copy('image.glyph(\uE0ED,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE0EE,nilesoft_icon_size) tip=['E0EE',tip.info] cmd=command.copy('image.glyph(\uE0EE,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE0EF,nilesoft_icon_size) tip=['E0EF',tip.info] cmd=command.copy('image.glyph(\uE0EF,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE0F0,nilesoft_icon_size) tip=['E0F0',tip.info] cmd=command.copy('image.glyph(\uE0F0,@nilesoft_icon_size)'))

		item(image=image.glyph(\uE0F1,nilesoft_icon_size) tip=['E0F1',tip.info] cmd=command.copy('image.glyph(\uE0F1,@nilesoft_icon_size)') col)
		item(image=image.glyph(\uE0F2,nilesoft_icon_size) tip=['E0F2',tip.info] cmd=command.copy('image.glyph(\uE0F2,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE0F3,nilesoft_icon_size) tip=['E0F3',tip.info] cmd=command.copy('image.glyph(\uE0F3,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE0F4,nilesoft_icon_size) tip=['E0F4',tip.info] cmd=command.copy('image.glyph(\uE0F4,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE0F5,nilesoft_icon_size) tip=['E0F5',tip.info] cmd=command.copy('image.glyph(\uE0F5,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE0F6,nilesoft_icon_size) tip=['E0F6',tip.info] cmd=command.copy('image.glyph(\uE0F6,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE0F7,nilesoft_icon_size) tip=['E0F7',tip.info] cmd=command.copy('image.glyph(\uE0F7,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE0F8,nilesoft_icon_size) tip=['E0F8',tip.info] cmd=command.copy('image.glyph(\uE0F8,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE0F9,nilesoft_icon_size) tip=['E0F9',tip.info] cmd=command.copy('image.glyph(\uE0F9,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE0FA,nilesoft_icon_size) tip=['E0FA',tip.info] cmd=command.copy('image.glyph(\uE0FA,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE0FB,nilesoft_icon_size) tip=['E0FB',tip.info] cmd=command.copy('image.glyph(\uE0FB,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE0FC,nilesoft_icon_size) tip=['E0FC',tip.info] cmd=command.copy('image.glyph(\uE0FC,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE0FD,nilesoft_icon_size) tip=['E0FD',tip.info] cmd=command.copy('image.glyph(\uE0FD,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE0FE,nilesoft_icon_size) tip=['E0FE',tip.info] cmd=command.copy('image.glyph(\uE0FE,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE0FF,nilesoft_icon_size) tip=['E0FF',tip.info] cmd=command.copy('image.glyph(\uE0FF,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE100,nilesoft_icon_size) tip=['E100',tip.info] cmd=command.copy('image.glyph(\uE100,@nilesoft_icon_size)'))
	}

	menu(title='Nilesoft Shell Glyph #2')
	{
		item(image=image.glyph(\uE101,nilesoft_icon_size) tip=['E101',tip.info] cmd=command.copy('image.glyph(\uE101,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE102,nilesoft_icon_size) tip=['E102',tip.info] cmd=command.copy('image.glyph(\uE102,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE103,nilesoft_icon_size) tip=['E103',tip.info] cmd=command.copy('image.glyph(\uE103,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE104,nilesoft_icon_size) tip=['E104',tip.info] cmd=command.copy('image.glyph(\uE104,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE105,nilesoft_icon_size) tip=['E105',tip.info] cmd=command.copy('image.glyph(\uE105,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE106,nilesoft_icon_size) tip=['E106',tip.info] cmd=command.copy('image.glyph(\uE106,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE107,nilesoft_icon_size) tip=['E107',tip.info] cmd=command.copy('image.glyph(\uE107,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE108,nilesoft_icon_size) tip=['E108',tip.info] cmd=command.copy('image.glyph(\uE108,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE109,nilesoft_icon_size) tip=['E109',tip.info] cmd=command.copy('image.glyph(\uE109,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE10A,nilesoft_icon_size) tip=['E10A',tip.info] cmd=command.copy('image.glyph(\uE10A,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE10B,nilesoft_icon_size) tip=['E10B',tip.info] cmd=command.copy('image.glyph(\uE10B,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE10C,nilesoft_icon_size) tip=['E10C',tip.info] cmd=command.copy('image.glyph(\uE10C,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE10D,nilesoft_icon_size) tip=['E10D',tip.info] cmd=command.copy('image.glyph(\uE10D,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE10E,nilesoft_icon_size) tip=['E10E',tip.info] cmd=command.copy('image.glyph(\uE10E,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE10F,nilesoft_icon_size) tip=['E10F',tip.info] cmd=command.copy('image.glyph(\uE10F,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE110,nilesoft_icon_size) tip=['E110',tip.info] cmd=command.copy('image.glyph(\uE110,@nilesoft_icon_size)'))

		item(image=image.glyph(\uE111,nilesoft_icon_size) tip=['E111',tip.info] cmd=command.copy('image.glyph(\uE111,@nilesoft_icon_size)') col)
		item(image=image.glyph(\uE112,nilesoft_icon_size) tip=['E112',tip.info] cmd=command.copy('image.glyph(\uE112,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE113,nilesoft_icon_size) tip=['E113',tip.info] cmd=command.copy('image.glyph(\uE113,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE114,nilesoft_icon_size) tip=['E114',tip.info] cmd=command.copy('image.glyph(\uE114,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE115,nilesoft_icon_size) tip=['E115',tip.info] cmd=command.copy('image.glyph(\uE115,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE116,nilesoft_icon_size) tip=['E116',tip.info] cmd=command.copy('image.glyph(\uE116,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE117,nilesoft_icon_size) tip=['E117',tip.info] cmd=command.copy('image.glyph(\uE117,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE118,nilesoft_icon_size) tip=['E118',tip.info] cmd=command.copy('image.glyph(\uE118,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE119,nilesoft_icon_size) tip=['E119',tip.info] cmd=command.copy('image.glyph(\uE119,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE11A,nilesoft_icon_size) tip=['E11A',tip.info] cmd=command.copy('image.glyph(\uE11A,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE11B,nilesoft_icon_size) tip=['E11B',tip.info] cmd=command.copy('image.glyph(\uE11B,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE11C,nilesoft_icon_size) tip=['E11C',tip.info] cmd=command.copy('image.glyph(\uE11C,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE11D,nilesoft_icon_size) tip=['E11D',tip.info] cmd=command.copy('image.glyph(\uE11D,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE11E,nilesoft_icon_size) tip=['E11E',tip.info] cmd=command.copy('image.glyph(\uE11E,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE11F,nilesoft_icon_size) tip=['E11F',tip.info] cmd=command.copy('image.glyph(\uE11F,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE120,nilesoft_icon_size) tip=['E120',tip.info] cmd=command.copy('image.glyph(\uE120,@nilesoft_icon_size)'))

		item(image=image.glyph(\uE121,nilesoft_icon_size) tip=['E121',tip.info] cmd=command.copy('image.glyph(\uE121,@nilesoft_icon_size)') col)
		item(image=image.glyph(\uE122,nilesoft_icon_size) tip=['E122',tip.info] cmd=command.copy('image.glyph(\uE122,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE123,nilesoft_icon_size) tip=['E123',tip.info] cmd=command.copy('image.glyph(\uE123,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE124,nilesoft_icon_size) tip=['E124',tip.info] cmd=command.copy('image.glyph(\uE124,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE125,nilesoft_icon_size) tip=['E125',tip.info] cmd=command.copy('image.glyph(\uE125,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE126,nilesoft_icon_size) tip=['E126',tip.info] cmd=command.copy('image.glyph(\uE126,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE127,nilesoft_icon_size) tip=['E127',tip.info] cmd=command.copy('image.glyph(\uE127,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE128,nilesoft_icon_size) tip=['E128',tip.info] cmd=command.copy('image.glyph(\uE128,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE129,nilesoft_icon_size) tip=['E129',tip.info] cmd=command.copy('image.glyph(\uE129,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE12A,nilesoft_icon_size) tip=['E12A',tip.info] cmd=command.copy('image.glyph(\uE12A,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE12B,nilesoft_icon_size) tip=['E12B',tip.info] cmd=command.copy('image.glyph(\uE12B,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE12C,nilesoft_icon_size) tip=['E12C',tip.info] cmd=command.copy('image.glyph(\uE12C,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE12D,nilesoft_icon_size) tip=['E12D',tip.info] cmd=command.copy('image.glyph(\uE12D,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE12E,nilesoft_icon_size) tip=['E12E',tip.info] cmd=command.copy('image.glyph(\uE12E,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE12F,nilesoft_icon_size) tip=['E12F',tip.info] cmd=command.copy('image.glyph(\uE12F,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE130,nilesoft_icon_size) tip=['E130',tip.info] cmd=command.copy('image.glyph(\uE130,@nilesoft_icon_size)'))

		item(image=image.glyph(\uE131,nilesoft_icon_size) tip=['E131',tip.info] cmd=command.copy('image.glyph(\uE131,@nilesoft_icon_size)') col)
		item(image=image.glyph(\uE132,nilesoft_icon_size) tip=['E132',tip.info] cmd=command.copy('image.glyph(\uE132,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE133,nilesoft_icon_size) tip=['E133',tip.info] cmd=command.copy('image.glyph(\uE133,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE134,nilesoft_icon_size) tip=['E134',tip.info] cmd=command.copy('image.glyph(\uE134,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE135,nilesoft_icon_size) tip=['E135',tip.info] cmd=command.copy('image.glyph(\uE135,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE136,nilesoft_icon_size) tip=['E136',tip.info] cmd=command.copy('image.glyph(\uE136,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE137,nilesoft_icon_size) tip=['E137',tip.info] cmd=command.copy('image.glyph(\uE137,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE138,nilesoft_icon_size) tip=['E138',tip.info] cmd=command.copy('image.glyph(\uE138,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE139,nilesoft_icon_size) tip=['E139',tip.info] cmd=command.copy('image.glyph(\uE139,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE13A,nilesoft_icon_size) tip=['E13A',tip.info] cmd=command.copy('image.glyph(\uE13A,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE13B,nilesoft_icon_size) tip=['E13B',tip.info] cmd=command.copy('image.glyph(\uE13B,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE13C,nilesoft_icon_size) tip=['E13C',tip.info] cmd=command.copy('image.glyph(\uE13C,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE13D,nilesoft_icon_size) tip=['E13D',tip.info] cmd=command.copy('image.glyph(\uE13D,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE13E,nilesoft_icon_size) tip=['E13E',tip.info] cmd=command.copy('image.glyph(\uE13E,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE13F,nilesoft_icon_size) tip=['E13F',tip.info] cmd=command.copy('image.glyph(\uE13F,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE140,nilesoft_icon_size) tip=['E140',tip.info] cmd=command.copy('image.glyph(\uE140,@nilesoft_icon_size)'))

		item(image=image.glyph(\uE141,nilesoft_icon_size) tip=['E141',tip.info] cmd=command.copy('image.glyph(\uE141,@nilesoft_icon_size)') col)
		item(image=image.glyph(\uE142,nilesoft_icon_size) tip=['E142',tip.info] cmd=command.copy('image.glyph(\uE142,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE143,nilesoft_icon_size) tip=['E143',tip.info] cmd=command.copy('image.glyph(\uE143,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE144,nilesoft_icon_size) tip=['E144',tip.info] cmd=command.copy('image.glyph(\uE144,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE145,nilesoft_icon_size) tip=['E145',tip.info] cmd=command.copy('image.glyph(\uE145,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE146,nilesoft_icon_size) tip=['E146',tip.info] cmd=command.copy('image.glyph(\uE146,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE147,nilesoft_icon_size) tip=['E147',tip.info] cmd=command.copy('image.glyph(\uE147,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE148,nilesoft_icon_size) tip=['E148',tip.info] cmd=command.copy('image.glyph(\uE148,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE149,nilesoft_icon_size) tip=['E149',tip.info] cmd=command.copy('image.glyph(\uE149,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE14A,nilesoft_icon_size) tip=['E14A',tip.info] cmd=command.copy('image.glyph(\uE14A,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE14B,nilesoft_icon_size) tip=['E14B',tip.info] cmd=command.copy('image.glyph(\uE14B,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE14C,nilesoft_icon_size) tip=['E14C',tip.info] cmd=command.copy('image.glyph(\uE14C,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE14D,nilesoft_icon_size) tip=['E14D',tip.info] cmd=command.copy('image.glyph(\uE14D,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE14E,nilesoft_icon_size) tip=['E14E',tip.info] cmd=command.copy('image.glyph(\uE14E,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE14F,nilesoft_icon_size) tip=['E14F',tip.info] cmd=command.copy('image.glyph(\uE14F,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE150,nilesoft_icon_size) tip=['E150',tip.info] cmd=command.copy('image.glyph(\uE150,@nilesoft_icon_size)'))

		item(image=image.glyph(\uE151,nilesoft_icon_size) tip=['E151',tip.info] cmd=command.copy('image.glyph(\uE151,@nilesoft_icon_size)') col)
		item(image=image.glyph(\uE152,nilesoft_icon_size) tip=['E152',tip.info] cmd=command.copy('image.glyph(\uE152,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE153,nilesoft_icon_size) tip=['E153',tip.info] cmd=command.copy('image.glyph(\uE153,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE154,nilesoft_icon_size) tip=['E154',tip.info] cmd=command.copy('image.glyph(\uE154,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE155,nilesoft_icon_size) tip=['E155',tip.info] cmd=command.copy('image.glyph(\uE155,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE156,nilesoft_icon_size) tip=['E156',tip.info] cmd=command.copy('image.glyph(\uE156,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE157,nilesoft_icon_size) tip=['E157',tip.info] cmd=command.copy('image.glyph(\uE157,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE158,nilesoft_icon_size) tip=['E158',tip.info] cmd=command.copy('image.glyph(\uE158,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE159,nilesoft_icon_size) tip=['E159',tip.info] cmd=command.copy('image.glyph(\uE159,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE15A,nilesoft_icon_size) tip=['E15A',tip.info] cmd=command.copy('image.glyph(\uE15A,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE15B,nilesoft_icon_size) tip=['E15B',tip.info] cmd=command.copy('image.glyph(\uE15B,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE15C,nilesoft_icon_size) tip=['E15C',tip.info] cmd=command.copy('image.glyph(\uE15C,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE15D,nilesoft_icon_size) tip=['E15D',tip.info] cmd=command.copy('image.glyph(\uE15D,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE15E,nilesoft_icon_size) tip=['E15E',tip.info] cmd=command.copy('image.glyph(\uE15E,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE15F,nilesoft_icon_size) tip=['E15F',tip.info] cmd=command.copy('image.glyph(\uE15F,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE160,nilesoft_icon_size) tip=['E160',tip.info] cmd=command.copy('image.glyph(\uE160,@nilesoft_icon_size)'))

		item(image=image.glyph(\uE161,nilesoft_icon_size) tip=['E161',tip.info] cmd=command.copy('image.glyph(\uE161,@nilesoft_icon_size)') col)
		item(image=image.glyph(\uE162,nilesoft_icon_size) tip=['E162',tip.info] cmd=command.copy('image.glyph(\uE162,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE163,nilesoft_icon_size) tip=['E163',tip.info] cmd=command.copy('image.glyph(\uE163,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE164,nilesoft_icon_size) tip=['E164',tip.info] cmd=command.copy('image.glyph(\uE164,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE165,nilesoft_icon_size) tip=['E165',tip.info] cmd=command.copy('image.glyph(\uE165,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE166,nilesoft_icon_size) tip=['E166',tip.info] cmd=command.copy('image.glyph(\uE166,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE167,nilesoft_icon_size) tip=['E167',tip.info] cmd=command.copy('image.glyph(\uE167,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE168,nilesoft_icon_size) tip=['E168',tip.info] cmd=command.copy('image.glyph(\uE168,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE169,nilesoft_icon_size) tip=['E169',tip.info] cmd=command.copy('image.glyph(\uE169,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE16A,nilesoft_icon_size) tip=['E16A',tip.info] cmd=command.copy('image.glyph(\uE16A,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE16B,nilesoft_icon_size) tip=['E16B',tip.info] cmd=command.copy('image.glyph(\uE16B,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE16C,nilesoft_icon_size) tip=['E16C',tip.info] cmd=command.copy('image.glyph(\uE16C,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE16D,nilesoft_icon_size) tip=['E16D',tip.info] cmd=command.copy('image.glyph(\uE16D,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE16E,nilesoft_icon_size) tip=['E16E',tip.info] cmd=command.copy('image.glyph(\uE16E,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE16F,nilesoft_icon_size) tip=['E16F',tip.info] cmd=command.copy('image.glyph(\uE16F,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE170,nilesoft_icon_size) tip=['E170',tip.info] cmd=command.copy('image.glyph(\uE170,@nilesoft_icon_size)'))

		item(image=image.glyph(\uE171,nilesoft_icon_size) tip=['E171',tip.info] cmd=command.copy('image.glyph(\uE171,@nilesoft_icon_size)') col)
		item(image=image.glyph(\uE172,nilesoft_icon_size) tip=['E172',tip.info] cmd=command.copy('image.glyph(\uE172,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE173,nilesoft_icon_size) tip=['E173',tip.info] cmd=command.copy('image.glyph(\uE173,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE174,nilesoft_icon_size) tip=['E174',tip.info] cmd=command.copy('image.glyph(\uE174,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE175,nilesoft_icon_size) tip=['E175',tip.info] cmd=command.copy('image.glyph(\uE175,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE176,nilesoft_icon_size) tip=['E176',tip.info] cmd=command.copy('image.glyph(\uE176,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE177,nilesoft_icon_size) tip=['E177',tip.info] cmd=command.copy('image.glyph(\uE177,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE178,nilesoft_icon_size) tip=['E178',tip.info] cmd=command.copy('image.glyph(\uE178,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE179,nilesoft_icon_size) tip=['E179',tip.info] cmd=command.copy('image.glyph(\uE179,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE17A,nilesoft_icon_size) tip=['E17A',tip.info] cmd=command.copy('image.glyph(\uE17A,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE17B,nilesoft_icon_size) tip=['E17B',tip.info] cmd=command.copy('image.glyph(\uE17B,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE17C,nilesoft_icon_size) tip=['E17C',tip.info] cmd=command.copy('image.glyph(\uE17C,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE17D,nilesoft_icon_size) tip=['E17D',tip.info] cmd=command.copy('image.glyph(\uE17D,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE17E,nilesoft_icon_size) tip=['E17E',tip.info] cmd=command.copy('image.glyph(\uE17E,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE17F,nilesoft_icon_size) tip=['E17F',tip.info] cmd=command.copy('image.glyph(\uE17F,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE180,nilesoft_icon_size) tip=['E180',tip.info] cmd=command.copy('image.glyph(\uE180,@nilesoft_icon_size)'))

		item(image=image.glyph(\uE181,nilesoft_icon_size) tip=['E181',tip.info] cmd=command.copy('image.glyph(\uE181,@nilesoft_icon_size)') col)
		item(image=image.glyph(\uE182,nilesoft_icon_size) tip=['E182',tip.info] cmd=command.copy('image.glyph(\uE182,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE183,nilesoft_icon_size) tip=['E183',tip.info] cmd=command.copy('image.glyph(\uE183,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE184,nilesoft_icon_size) tip=['E184',tip.info] cmd=command.copy('image.glyph(\uE184,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE185,nilesoft_icon_size) tip=['E185',tip.info] cmd=command.copy('image.glyph(\uE185,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE186,nilesoft_icon_size) tip=['E186',tip.info] cmd=command.copy('image.glyph(\uE186,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE187,nilesoft_icon_size) tip=['E187',tip.info] cmd=command.copy('image.glyph(\uE187,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE188,nilesoft_icon_size) tip=['E188',tip.info] cmd=command.copy('image.glyph(\uE188,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE189,nilesoft_icon_size) tip=['E189',tip.info] cmd=command.copy('image.glyph(\uE189,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE18A,nilesoft_icon_size) tip=['E18A',tip.info] cmd=command.copy('image.glyph(\uE18A,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE18B,nilesoft_icon_size) tip=['E18B',tip.info] cmd=command.copy('image.glyph(\uE18B,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE18C,nilesoft_icon_size) tip=['E18C',tip.info] cmd=command.copy('image.glyph(\uE18C,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE18D,nilesoft_icon_size) tip=['E18D',tip.info] cmd=command.copy('image.glyph(\uE18D,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE18E,nilesoft_icon_size) tip=['E18E',tip.info] cmd=command.copy('image.glyph(\uE18E,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE18F,nilesoft_icon_size) tip=['E18F',tip.info] cmd=command.copy('image.glyph(\uE18F,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE190,nilesoft_icon_size) tip=['E190',tip.info] cmd=command.copy('image.glyph(\uE190,@nilesoft_icon_size)'))

		item(image=image.glyph(\uE191,nilesoft_icon_size) tip=['E191',tip.info] cmd=command.copy('image.glyph(\uE191,@nilesoft_icon_size)') col)
		item(image=image.glyph(\uE192,nilesoft_icon_size) tip=['E192',tip.info] cmd=command.copy('image.glyph(\uE192,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE193,nilesoft_icon_size) tip=['E193',tip.info] cmd=command.copy('image.glyph(\uE193,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE194,nilesoft_icon_size) tip=['E194',tip.info] cmd=command.copy('image.glyph(\uE194,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE195,nilesoft_icon_size) tip=['E195',tip.info] cmd=command.copy('image.glyph(\uE195,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE196,nilesoft_icon_size) tip=['E196',tip.info] cmd=command.copy('image.glyph(\uE196,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE197,nilesoft_icon_size) tip=['E197',tip.info] cmd=command.copy('image.glyph(\uE197,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE198,nilesoft_icon_size) tip=['E198',tip.info] cmd=command.copy('image.glyph(\uE198,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE199,nilesoft_icon_size) tip=['E199',tip.info] cmd=command.copy('image.glyph(\uE199,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE19A,nilesoft_icon_size) tip=['E19A',tip.info] cmd=command.copy('image.glyph(\uE19A,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE19B,nilesoft_icon_size) tip=['E19B',tip.info] cmd=command.copy('image.glyph(\uE19B,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE19C,nilesoft_icon_size) tip=['E19C',tip.info] cmd=command.copy('image.glyph(\uE19C,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE19D,nilesoft_icon_size) tip=['E19D',tip.info] cmd=command.copy('image.glyph(\uE19D,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE19E,nilesoft_icon_size) tip=['E19E',tip.info] cmd=command.copy('image.glyph(\uE19E,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE19F,nilesoft_icon_size) tip=['E19F',tip.info] cmd=command.copy('image.glyph(\uE19F,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE1A0,nilesoft_icon_size) tip=['E1A0',tip.info] cmd=command.copy('image.glyph(\uE1A0,@nilesoft_icon_size)'))

		item(image=image.glyph(\uE1A1,nilesoft_icon_size) tip=['E1A1',tip.info] cmd=command.copy('image.glyph(\uE1A1,@nilesoft_icon_size)') col)
		item(image=image.glyph(\uE1A2,nilesoft_icon_size) tip=['E1A2',tip.info] cmd=command.copy('image.glyph(\uE1A2,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE1A3,nilesoft_icon_size) tip=['E1A3',tip.info] cmd=command.copy('image.glyph(\uE1A3,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE1A4,nilesoft_icon_size) tip=['E1A4',tip.info] cmd=command.copy('image.glyph(\uE1A4,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE1A5,nilesoft_icon_size) tip=['E1A5',tip.info] cmd=command.copy('image.glyph(\uE1A5,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE1A6,nilesoft_icon_size) tip=['E1A6',tip.info] cmd=command.copy('image.glyph(\uE1A6,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE1A7,nilesoft_icon_size) tip=['E1A7',tip.info] cmd=command.copy('image.glyph(\uE1A7,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE1A8,nilesoft_icon_size) tip=['E1A8',tip.info] cmd=command.copy('image.glyph(\uE1A8,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE1A9,nilesoft_icon_size) tip=['E1A9',tip.info] cmd=command.copy('image.glyph(\uE1A9,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE1AA,nilesoft_icon_size) tip=['E1AA',tip.info] cmd=command.copy('image.glyph(\uE1AA,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE1AB,nilesoft_icon_size) tip=['E1AB',tip.info] cmd=command.copy('image.glyph(\uE1AB,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE1AC,nilesoft_icon_size) tip=['E1AC',tip.info] cmd=command.copy('image.glyph(\uE1AC,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE1AD,nilesoft_icon_size) tip=['E1AD',tip.info] cmd=command.copy('image.glyph(\uE1AD,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE1AE,nilesoft_icon_size) tip=['E1AE',tip.info] cmd=command.copy('image.glyph(\uE1AE,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE1AF,nilesoft_icon_size) tip=['E1AF',tip.info] cmd=command.copy('image.glyph(\uE1AF,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE1B0,nilesoft_icon_size) tip=['E1B0',tip.info] cmd=command.copy('image.glyph(\uE1B0,@nilesoft_icon_size)'))

		item(image=image.glyph(\uE1B1,nilesoft_icon_size) tip=['E1B1',tip.info] cmd=command.copy('image.glyph(\uE1B1,@nilesoft_icon_size)') col)
		item(image=image.glyph(\uE1B2,nilesoft_icon_size) tip=['E1B2',tip.info] cmd=command.copy('image.glyph(\uE1B2,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE1B3,nilesoft_icon_size) tip=['E1B3',tip.info] cmd=command.copy('image.glyph(\uE1B3,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE1B4,nilesoft_icon_size) tip=['E1B4',tip.info] cmd=command.copy('image.glyph(\uE1B4,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE1B5,nilesoft_icon_size) tip=['E1B5',tip.info] cmd=command.copy('image.glyph(\uE1B5,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE1B6,nilesoft_icon_size) tip=['E1B6',tip.info] cmd=command.copy('image.glyph(\uE1B6,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE1B7,nilesoft_icon_size) tip=['E1B7',tip.info] cmd=command.copy('image.glyph(\uE1B7,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE1B8,nilesoft_icon_size) tip=['E1B8',tip.info] cmd=command.copy('image.glyph(\uE1B8,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE1B9,nilesoft_icon_size) tip=['E1B9',tip.info] cmd=command.copy('image.glyph(\uE1B9,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE1BA,nilesoft_icon_size) tip=['E1BA',tip.info] cmd=command.copy('image.glyph(\uE1BA,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE1BB,nilesoft_icon_size) tip=['E1BB',tip.info] cmd=command.copy('image.glyph(\uE1BB,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE1BC,nilesoft_icon_size) tip=['E1BC',tip.info] cmd=command.copy('image.glyph(\uE1BC,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE1BD,nilesoft_icon_size) tip=['E1BD',tip.info] cmd=command.copy('image.glyph(\uE1BD,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE1BE,nilesoft_icon_size) tip=['E1BE',tip.info] cmd=command.copy('image.glyph(\uE1BE,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE1BF,nilesoft_icon_size) tip=['E1BF',tip.info] cmd=command.copy('image.glyph(\uE1BF,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE1C0,nilesoft_icon_size) tip=['E1C0',tip.info] cmd=command.copy('image.glyph(\uE1C0,@nilesoft_icon_size)'))

		item(image=image.glyph(\uE1C1,nilesoft_icon_size) tip=['E1C1',tip.info] cmd=command.copy('image.glyph(\uE1C1,@nilesoft_icon_size)') col)
		item(image=image.glyph(\uE1C2,nilesoft_icon_size) tip=['E1C2',tip.info] cmd=command.copy('image.glyph(\uE1C2,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE1C3,nilesoft_icon_size) tip=['E1C3',tip.info] cmd=command.copy('image.glyph(\uE1C3,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE1C4,nilesoft_icon_size) tip=['E1C4',tip.info] cmd=command.copy('image.glyph(\uE1C4,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE1C5,nilesoft_icon_size) tip=['E1C5',tip.info] cmd=command.copy('image.glyph(\uE1C5,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE1C6,nilesoft_icon_size) tip=['E1C6',tip.info] cmd=command.copy('image.glyph(\uE1C6,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE1C7,nilesoft_icon_size) tip=['E1C7',tip.info] cmd=command.copy('image.glyph(\uE1C7,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE1C8,nilesoft_icon_size) tip=['E1C8',tip.info] cmd=command.copy('image.glyph(\uE1C8,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE1C9,nilesoft_icon_size) tip=['E1C9',tip.info] cmd=command.copy('image.glyph(\uE1C9,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE1CA,nilesoft_icon_size) tip=['E1CA',tip.info] cmd=command.copy('image.glyph(\uE1CA,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE1CB,nilesoft_icon_size) tip=['E1CB',tip.info] cmd=command.copy('image.glyph(\uE1CB,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE1CC,nilesoft_icon_size) tip=['E1CC',tip.info] cmd=command.copy('image.glyph(\uE1CC,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE1CD,nilesoft_icon_size) tip=['E1CD',tip.info] cmd=command.copy('image.glyph(\uE1CD,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE1CE,nilesoft_icon_size) tip=['E1CE',tip.info] cmd=command.copy('image.glyph(\uE1CE,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE1CF,nilesoft_icon_size) tip=['E1CF',tip.info] cmd=command.copy('image.glyph(\uE1CF,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE1D0,nilesoft_icon_size) tip=['E1D0',tip.info] cmd=command.copy('image.glyph(\uE1D0,@nilesoft_icon_size)'))

		item(image=image.glyph(\uE1D1,nilesoft_icon_size) tip=['E1D1',tip.info] cmd=command.copy('image.glyph(\uE1D1,@nilesoft_icon_size)') col)
		item(image=image.glyph(\uE1D2,nilesoft_icon_size) tip=['E1D2',tip.info] cmd=command.copy('image.glyph(\uE1D2,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE1D3,nilesoft_icon_size) tip=['E1D3',tip.info] cmd=command.copy('image.glyph(\uE1D3,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE1D4,nilesoft_icon_size) tip=['E1D4',tip.info] cmd=command.copy('image.glyph(\uE1D4,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE1D5,nilesoft_icon_size) tip=['E1D5',tip.info] cmd=command.copy('image.glyph(\uE1D5,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE1D6,nilesoft_icon_size) tip=['E1D6',tip.info] cmd=command.copy('image.glyph(\uE1D6,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE1D7,nilesoft_icon_size) tip=['E1D7',tip.info] cmd=command.copy('image.glyph(\uE1D7,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE1D8,nilesoft_icon_size) tip=['E1D8',tip.info] cmd=command.copy('image.glyph(\uE1D8,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE1D9,nilesoft_icon_size) tip=['E1D9',tip.info] cmd=command.copy('image.glyph(\uE1D9,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE1DA,nilesoft_icon_size) tip=['E1DA',tip.info] cmd=command.copy('image.glyph(\uE1DA,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE1DB,nilesoft_icon_size) tip=['E1DB',tip.info] cmd=command.copy('image.glyph(\uE1DB,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE1DC,nilesoft_icon_size) tip=['E1DC',tip.info] cmd=command.copy('image.glyph(\uE1DC,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE1DD,nilesoft_icon_size) tip=['E1DD',tip.info] cmd=command.copy('image.glyph(\uE1DD,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE1DE,nilesoft_icon_size) tip=['E1DE',tip.info] cmd=command.copy('image.glyph(\uE1DE,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE1DF,nilesoft_icon_size) tip=['E1DF',tip.info] cmd=command.copy('image.glyph(\uE1DF,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE1E0,nilesoft_icon_size) tip=['E1E0',tip.info] cmd=command.copy('image.glyph(\uE1E0,@nilesoft_icon_size)'))

		item(image=image.glyph(\uE1E1,nilesoft_icon_size) tip=['E1E1',tip.info] cmd=command.copy('image.glyph(\uE1E1,@nilesoft_icon_size)') col)
		item(image=image.glyph(\uE1E2,nilesoft_icon_size) tip=['E1E2',tip.info] cmd=command.copy('image.glyph(\uE1E2,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE1E3,nilesoft_icon_size) tip=['E1E3',tip.info] cmd=command.copy('image.glyph(\uE1E3,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE1E4,nilesoft_icon_size) tip=['E1E4',tip.info] cmd=command.copy('image.glyph(\uE1E4,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE1E5,nilesoft_icon_size) tip=['E1E5',tip.info] cmd=command.copy('image.glyph(\uE1E5,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE1E6,nilesoft_icon_size) tip=['E1E6',tip.info] cmd=command.copy('image.glyph(\uE1E6,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE1E7,nilesoft_icon_size) tip=['E1E7',tip.info] cmd=command.copy('image.glyph(\uE1E7,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE1E8,nilesoft_icon_size) tip=['E1E8',tip.info] cmd=command.copy('image.glyph(\uE1E8,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE1E9,nilesoft_icon_size) tip=['E1E9',tip.info] cmd=command.copy('image.glyph(\uE1E9,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE1EA,nilesoft_icon_size) tip=['E1EA',tip.info] cmd=command.copy('image.glyph(\uE1EA,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE1EB,nilesoft_icon_size) tip=['E1EB',tip.info] cmd=command.copy('image.glyph(\uE1EB,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE1EC,nilesoft_icon_size) tip=['E1EC',tip.info] cmd=command.copy('image.glyph(\uE1EC,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE1ED,nilesoft_icon_size) tip=['E1ED',tip.info] cmd=command.copy('image.glyph(\uE1ED,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE1EE,nilesoft_icon_size) tip=['E1EE',tip.info] cmd=command.copy('image.glyph(\uE1EE,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE1EF,nilesoft_icon_size) tip=['E1EF',tip.info] cmd=command.copy('image.glyph(\uE1EF,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE1F0,nilesoft_icon_size) tip=['E1F0',tip.info] cmd=command.copy('image.glyph(\uE1F0,@nilesoft_icon_size)'))

		item(image=image.glyph(\uE1F1,nilesoft_icon_size) tip=['E1F1',tip.info] cmd=command.copy('image.glyph(\uE1F1,@nilesoft_icon_size)') col)
		item(image=image.glyph(\uE1F2,nilesoft_icon_size) tip=['E1F2',tip.info] cmd=command.copy('image.glyph(\uE1F2,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE1F3,nilesoft_icon_size) tip=['E1F3',tip.info] cmd=command.copy('image.glyph(\uE1F3,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE1F4,nilesoft_icon_size) tip=['E1F4',tip.info] cmd=command.copy('image.glyph(\uE1F4,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE1F5,nilesoft_icon_size) tip=['E1F5',tip.info] cmd=command.copy('image.glyph(\uE1F5,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE1F6,nilesoft_icon_size) tip=['E1F6',tip.info] cmd=command.copy('image.glyph(\uE1F6,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE1F7,nilesoft_icon_size) tip=['E1F7',tip.info] cmd=command.copy('image.glyph(\uE1F7,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE1F8,nilesoft_icon_size) tip=['E1F8',tip.info] cmd=command.copy('image.glyph(\uE1F8,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE1F9,nilesoft_icon_size) tip=['E1F9',tip.info] cmd=command.copy('image.glyph(\uE1F9,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE1FA,nilesoft_icon_size) tip=['E1FA',tip.info] cmd=command.copy('image.glyph(\uE1FA,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE1FB,nilesoft_icon_size) tip=['E1FB',tip.info] cmd=command.copy('image.glyph(\uE1FB,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE1FC,nilesoft_icon_size) tip=['E1FC',tip.info] cmd=command.copy('image.glyph(\uE1FC,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE1FD,nilesoft_icon_size) tip=['E1FD',tip.info] cmd=command.copy('image.glyph(\uE1FD,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE1FE,nilesoft_icon_size) tip=['E1FE',tip.info] cmd=command.copy('image.glyph(\uE1FE,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE1FF,nilesoft_icon_size) tip=['E1FF',tip.info] cmd=command.copy('image.glyph(\uE1FF,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE200,nilesoft_icon_size) tip=['E200',tip.info] cmd=command.copy('image.glyph(\uE200,@nilesoft_icon_size)'))
	}

	menu(title='Nilesoft Shell Glyph #3')
	{
		item(image=image.glyph(\uE201,nilesoft_icon_size) tip=['E201',tip.info] cmd=command.copy('image.glyph(\uE201,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE202,nilesoft_icon_size) tip=['E202',tip.info] cmd=command.copy('image.glyph(\uE202,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE203,nilesoft_icon_size) tip=['E203',tip.info] cmd=command.copy('image.glyph(\uE203,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE204,nilesoft_icon_size) tip=['E204',tip.info] cmd=command.copy('image.glyph(\uE204,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE205,nilesoft_icon_size) tip=['E205',tip.info] cmd=command.copy('image.glyph(\uE205,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE206,nilesoft_icon_size) tip=['E206',tip.info] cmd=command.copy('image.glyph(\uE206,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE207,nilesoft_icon_size) tip=['E207',tip.info] cmd=command.copy('image.glyph(\uE207,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE208,nilesoft_icon_size) tip=['E208',tip.info] cmd=command.copy('image.glyph(\uE208,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE209,nilesoft_icon_size) tip=['E209',tip.info] cmd=command.copy('image.glyph(\uE209,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE20A,nilesoft_icon_size) tip=['E20A',tip.info] cmd=command.copy('image.glyph(\uE20A,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE20B,nilesoft_icon_size) tip=['E20B',tip.info] cmd=command.copy('image.glyph(\uE20B,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE20C,nilesoft_icon_size) tip=['E20C',tip.info] cmd=command.copy('image.glyph(\uE20C,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE20D,nilesoft_icon_size) tip=['E20D',tip.info] cmd=command.copy('image.glyph(\uE20D,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE20E,nilesoft_icon_size) tip=['E20E',tip.info] cmd=command.copy('image.glyph(\uE20E,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE20F,nilesoft_icon_size) tip=['E20F',tip.info] cmd=command.copy('image.glyph(\uE20F,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE210,nilesoft_icon_size) tip=['E210',tip.info] cmd=command.copy('image.glyph(\uE210,@nilesoft_icon_size)'))

		item(image=image.glyph(\uE211,nilesoft_icon_size) tip=['E211',tip.info] cmd=command.copy('image.glyph(\uE211,@nilesoft_icon_size)') col)
		item(image=image.glyph(\uE212,nilesoft_icon_size) tip=['E212',tip.info] cmd=command.copy('image.glyph(\uE212,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE213,nilesoft_icon_size) tip=['E213',tip.info] cmd=command.copy('image.glyph(\uE213,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE214,nilesoft_icon_size) tip=['E214',tip.info] cmd=command.copy('image.glyph(\uE214,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE215,nilesoft_icon_size) tip=['E215',tip.info] cmd=command.copy('image.glyph(\uE215,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE216,nilesoft_icon_size) tip=['E216',tip.info] cmd=command.copy('image.glyph(\uE216,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE217,nilesoft_icon_size) tip=['E217',tip.info] cmd=command.copy('image.glyph(\uE217,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE218,nilesoft_icon_size) tip=['E218',tip.info] cmd=command.copy('image.glyph(\uE218,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE219,nilesoft_icon_size) tip=['E219',tip.info] cmd=command.copy('image.glyph(\uE219,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE21A,nilesoft_icon_size) tip=['E21A',tip.info] cmd=command.copy('image.glyph(\uE21A,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE21B,nilesoft_icon_size) tip=['E21B',tip.info] cmd=command.copy('image.glyph(\uE21B,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE21C,nilesoft_icon_size) tip=['E21C',tip.info] cmd=command.copy('image.glyph(\uE21C,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE21D,nilesoft_icon_size) tip=['E21D',tip.info] cmd=command.copy('image.glyph(\uE21D,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE21E,nilesoft_icon_size) tip=['E21E',tip.info] cmd=command.copy('image.glyph(\uE21E,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE21F,nilesoft_icon_size) tip=['E21F',tip.info] cmd=command.copy('image.glyph(\uE21F,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE220,nilesoft_icon_size) tip=['E220',tip.info] cmd=command.copy('image.glyph(\uE220,@nilesoft_icon_size)'))

		item(image=image.glyph(\uE221,nilesoft_icon_size) tip=['E221',tip.info] cmd=command.copy('image.glyph(\uE221,@nilesoft_icon_size)') col)
		item(image=image.glyph(\uE222,nilesoft_icon_size) tip=['E222',tip.info] cmd=command.copy('image.glyph(\uE222,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE223,nilesoft_icon_size) tip=['E223',tip.info] cmd=command.copy('image.glyph(\uE223,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE224,nilesoft_icon_size) tip=['E224',tip.info] cmd=command.copy('image.glyph(\uE224,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE225,nilesoft_icon_size) tip=['E225',tip.info] cmd=command.copy('image.glyph(\uE225,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE226,nilesoft_icon_size) tip=['E226',tip.info] cmd=command.copy('image.glyph(\uE226,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE227,nilesoft_icon_size) tip=['E227',tip.info] cmd=command.copy('image.glyph(\uE227,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE228,nilesoft_icon_size) tip=['E228',tip.info] cmd=command.copy('image.glyph(\uE228,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE229,nilesoft_icon_size) tip=['E229',tip.info] cmd=command.copy('image.glyph(\uE229,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE22A,nilesoft_icon_size) tip=['E22A',tip.info] cmd=command.copy('image.glyph(\uE22A,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE22B,nilesoft_icon_size) tip=['E22B',tip.info] cmd=command.copy('image.glyph(\uE22B,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE22C,nilesoft_icon_size) tip=['E22C',tip.info] cmd=command.copy('image.glyph(\uE22C,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE22D,nilesoft_icon_size) tip=['E22D',tip.info] cmd=command.copy('image.glyph(\uE22D,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE22E,nilesoft_icon_size) tip=['E22E',tip.info] cmd=command.copy('image.glyph(\uE22E,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE22F,nilesoft_icon_size) tip=['E22F',tip.info] cmd=command.copy('image.glyph(\uE22F,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE230,nilesoft_icon_size) tip=['E230',tip.info] cmd=command.copy('image.glyph(\uE230,@nilesoft_icon_size)'))

		item(image=image.glyph(\uE231,nilesoft_icon_size) tip=['E231',tip.info] cmd=command.copy('image.glyph(\uE231,@nilesoft_icon_size)') col)
		item(image=image.glyph(\uE232,nilesoft_icon_size) tip=['E232',tip.info] cmd=command.copy('image.glyph(\uE232,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE233,nilesoft_icon_size) tip=['E233',tip.info] cmd=command.copy('image.glyph(\uE233,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE234,nilesoft_icon_size) tip=['E234',tip.info] cmd=command.copy('image.glyph(\uE234,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE235,nilesoft_icon_size) tip=['E235',tip.info] cmd=command.copy('image.glyph(\uE235,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE236,nilesoft_icon_size) tip=['E236',tip.info] cmd=command.copy('image.glyph(\uE236,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE237,nilesoft_icon_size) tip=['E237',tip.info] cmd=command.copy('image.glyph(\uE237,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE238,nilesoft_icon_size) tip=['E238',tip.info] cmd=command.copy('image.glyph(\uE238,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE239,nilesoft_icon_size) tip=['E239',tip.info] cmd=command.copy('image.glyph(\uE239,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE23A,nilesoft_icon_size) tip=['E23A',tip.info] cmd=command.copy('image.glyph(\uE23A,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE23B,nilesoft_icon_size) tip=['E23B',tip.info] cmd=command.copy('image.glyph(\uE23B,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE23C,nilesoft_icon_size) tip=['E23C',tip.info] cmd=command.copy('image.glyph(\uE23C,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE23D,nilesoft_icon_size) tip=['E23D',tip.info] cmd=command.copy('image.glyph(\uE23D,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE23E,nilesoft_icon_size) tip=['E23E',tip.info] cmd=command.copy('image.glyph(\uE23E,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE23F,nilesoft_icon_size) tip=['E23F',tip.info] cmd=command.copy('image.glyph(\uE23F,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE240,nilesoft_icon_size) tip=['E240',tip.info] cmd=command.copy('image.glyph(\uE240,@nilesoft_icon_size)'))

		item(image=image.glyph(\uE241,nilesoft_icon_size) tip=['E241',tip.info] cmd=command.copy('image.glyph(\uE241,@nilesoft_icon_size)') col)
		item(image=image.glyph(\uE242,nilesoft_icon_size) tip=['E242',tip.info] cmd=command.copy('image.glyph(\uE242,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE243,nilesoft_icon_size) tip=['E243',tip.info] cmd=command.copy('image.glyph(\uE243,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE244,nilesoft_icon_size) tip=['E244',tip.info] cmd=command.copy('image.glyph(\uE244,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE245,nilesoft_icon_size) tip=['E245',tip.info] cmd=command.copy('image.glyph(\uE245,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE246,nilesoft_icon_size) tip=['E246',tip.info] cmd=command.copy('image.glyph(\uE246,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE247,nilesoft_icon_size) tip=['E247',tip.info] cmd=command.copy('image.glyph(\uE247,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE248,nilesoft_icon_size) tip=['E248',tip.info] cmd=command.copy('image.glyph(\uE248,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE249,nilesoft_icon_size) tip=['E249',tip.info] cmd=command.copy('image.glyph(\uE249,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE24A,nilesoft_icon_size) tip=['E24A',tip.info] cmd=command.copy('image.glyph(\uE24A,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE24B,nilesoft_icon_size) tip=['E24B',tip.info] cmd=command.copy('image.glyph(\uE24B,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE24C,nilesoft_icon_size) tip=['E24C',tip.info] cmd=command.copy('image.glyph(\uE24C,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE24D,nilesoft_icon_size) tip=['E24D',tip.info] cmd=command.copy('image.glyph(\uE24D,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE24E,nilesoft_icon_size) tip=['E24E',tip.info] cmd=command.copy('image.glyph(\uE24E,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE24F,nilesoft_icon_size) tip=['E24F',tip.info] cmd=command.copy('image.glyph(\uE24F,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE250,nilesoft_icon_size) tip=['E250',tip.info] cmd=command.copy('image.glyph(\uE250,@nilesoft_icon_size)'))

		item(image=image.glyph(\uE251,nilesoft_icon_size) tip=['E251',tip.info] cmd=command.copy('image.glyph(\uE251,@nilesoft_icon_size)') col)
		item(image=image.glyph(\uE252,nilesoft_icon_size) tip=['E252',tip.info] cmd=command.copy('image.glyph(\uE252,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE253,nilesoft_icon_size) tip=['E253',tip.info] cmd=command.copy('image.glyph(\uE253,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE254,nilesoft_icon_size) tip=['E254',tip.info] cmd=command.copy('image.glyph(\uE254,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE255,nilesoft_icon_size) tip=['E255',tip.info] cmd=command.copy('image.glyph(\uE255,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE256,nilesoft_icon_size) tip=['E256',tip.info] cmd=command.copy('image.glyph(\uE256,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE257,nilesoft_icon_size) tip=['E257',tip.info] cmd=command.copy('image.glyph(\uE257,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE258,nilesoft_icon_size) tip=['E258',tip.info] cmd=command.copy('image.glyph(\uE258,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE259,nilesoft_icon_size) tip=['E259',tip.info] cmd=command.copy('image.glyph(\uE259,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE25A,nilesoft_icon_size) tip=['E25A',tip.info] cmd=command.copy('image.glyph(\uE25A,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE25B,nilesoft_icon_size) tip=['E25B',tip.info] cmd=command.copy('image.glyph(\uE25B,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE25C,nilesoft_icon_size) tip=['E25C',tip.info] cmd=command.copy('image.glyph(\uE25C,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE25D,nilesoft_icon_size) tip=['E25D',tip.info] cmd=command.copy('image.glyph(\uE25D,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE25E,nilesoft_icon_size) tip=['E25E',tip.info] cmd=command.copy('image.glyph(\uE25E,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE25F,nilesoft_icon_size) tip=['E25F',tip.info] cmd=command.copy('image.glyph(\uE25F,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE260,nilesoft_icon_size) tip=['E260',tip.info] cmd=command.copy('image.glyph(\uE260,@nilesoft_icon_size)'))

		item(image=image.glyph(\uE261,nilesoft_icon_size) tip=['E261',tip.info] cmd=command.copy('image.glyph(\uE261,@nilesoft_icon_size)') col)
		item(image=image.glyph(\uE262,nilesoft_icon_size) tip=['E262',tip.info] cmd=command.copy('image.glyph(\uE262,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE263,nilesoft_icon_size) tip=['E263',tip.info] cmd=command.copy('image.glyph(\uE263,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE264,nilesoft_icon_size) tip=['E264',tip.info] cmd=command.copy('image.glyph(\uE264,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE265,nilesoft_icon_size) tip=['E265',tip.info] cmd=command.copy('image.glyph(\uE265,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE266,nilesoft_icon_size) tip=['E266',tip.info] cmd=command.copy('image.glyph(\uE266,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE267,nilesoft_icon_size) tip=['E267',tip.info] cmd=command.copy('image.glyph(\uE267,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE268,nilesoft_icon_size) tip=['E268',tip.info] cmd=command.copy('image.glyph(\uE268,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE269,nilesoft_icon_size) tip=['E269',tip.info] cmd=command.copy('image.glyph(\uE269,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE26A,nilesoft_icon_size) tip=['E26A',tip.info] cmd=command.copy('image.glyph(\uE26A,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE26B,nilesoft_icon_size) tip=['E26B',tip.info] cmd=command.copy('image.glyph(\uE26B,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE26C,nilesoft_icon_size) tip=['E26C',tip.info] cmd=command.copy('image.glyph(\uE26C,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE26D,nilesoft_icon_size) tip=['E26D',tip.info] cmd=command.copy('image.glyph(\uE26D,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE26E,nilesoft_icon_size) tip=['E26E',tip.info] cmd=command.copy('image.glyph(\uE26E,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE26F,nilesoft_icon_size) tip=['E26F',tip.info] cmd=command.copy('image.glyph(\uE26F,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE270,nilesoft_icon_size) tip=['E270',tip.info] cmd=command.copy('image.glyph(\uE270,@nilesoft_icon_size)'))

		item(image=image.glyph(\uE271,nilesoft_icon_size) tip=['E271',tip.info] cmd=command.copy('image.glyph(\uE271,@nilesoft_icon_size)') col)
		item(image=image.glyph(\uE272,nilesoft_icon_size) tip=['E272',tip.info] cmd=command.copy('image.glyph(\uE272,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE273,nilesoft_icon_size) tip=['E273',tip.info] cmd=command.copy('image.glyph(\uE273,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE274,nilesoft_icon_size) tip=['E274',tip.info] cmd=command.copy('image.glyph(\uE274,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE275,nilesoft_icon_size) tip=['E275',tip.info] cmd=command.copy('image.glyph(\uE275,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE276,nilesoft_icon_size) tip=['E276',tip.info] cmd=command.copy('image.glyph(\uE276,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE277,nilesoft_icon_size) tip=['E277',tip.info] cmd=command.copy('image.glyph(\uE277,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE278,nilesoft_icon_size) tip=['E278',tip.info] cmd=command.copy('image.glyph(\uE278,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE279,nilesoft_icon_size) tip=['E279',tip.info] cmd=command.copy('image.glyph(\uE279,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE27A,nilesoft_icon_size) tip=['E27A',tip.info] cmd=command.copy('image.glyph(\uE27A,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE27B,nilesoft_icon_size) tip=['E27B',tip.info] cmd=command.copy('image.glyph(\uE27B,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE27C,nilesoft_icon_size) tip=['E27C',tip.info] cmd=command.copy('image.glyph(\uE27C,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE27D,nilesoft_icon_size) tip=['E27D',tip.info] cmd=command.copy('image.glyph(\uE27D,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE27E,nilesoft_icon_size) tip=['E27E',tip.info] cmd=command.copy('image.glyph(\uE27E,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE27F,nilesoft_icon_size) tip=['E27F',tip.info] cmd=command.copy('image.glyph(\uE27F,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE280,nilesoft_icon_size) tip=['E280',tip.info] cmd=command.copy('image.glyph(\uE280,@nilesoft_icon_size)'))

		item(image=image.glyph(\uE281,nilesoft_icon_size) tip=['E281',tip.info] cmd=command.copy('image.glyph(\uE281,@nilesoft_icon_size)') col)
		item(image=image.glyph(\uE282,nilesoft_icon_size) tip=['E282',tip.info] cmd=command.copy('image.glyph(\uE282,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE283,nilesoft_icon_size) tip=['E283',tip.info] cmd=command.copy('image.glyph(\uE283,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE284,nilesoft_icon_size) tip=['E284',tip.info] cmd=command.copy('image.glyph(\uE284,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE285,nilesoft_icon_size) tip=['E285',tip.info] cmd=command.copy('image.glyph(\uE285,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE286,nilesoft_icon_size) tip=['E286',tip.info] cmd=command.copy('image.glyph(\uE286,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE287,nilesoft_icon_size) tip=['E287',tip.info] cmd=command.copy('image.glyph(\uE287,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE288,nilesoft_icon_size) tip=['E288',tip.info] cmd=command.copy('image.glyph(\uE288,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE289,nilesoft_icon_size) tip=['E289',tip.info] cmd=command.copy('image.glyph(\uE289,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE28A,nilesoft_icon_size) tip=['E28A',tip.info] cmd=command.copy('image.glyph(\uE28A,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE28B,nilesoft_icon_size) tip=['E28B',tip.info] cmd=command.copy('image.glyph(\uE28B,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE28C,nilesoft_icon_size) tip=['E28C',tip.info] cmd=command.copy('image.glyph(\uE28C,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE28D,nilesoft_icon_size) tip=['E28D',tip.info] cmd=command.copy('image.glyph(\uE28D,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE28E,nilesoft_icon_size) tip=['E28E',tip.info] cmd=command.copy('image.glyph(\uE28E,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE28F,nilesoft_icon_size) tip=['E28F',tip.info] cmd=command.copy('image.glyph(\uE28F,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE290,nilesoft_icon_size) tip=['E290',tip.info] cmd=command.copy('image.glyph(\uE290,@nilesoft_icon_size)'))

		item(image=image.glyph(\uE291,nilesoft_icon_size) tip=['E291',tip.info] cmd=command.copy('image.glyph(\uE291,@nilesoft_icon_size)') col)
		item(image=image.glyph(\uE292,nilesoft_icon_size) tip=['E292',tip.info] cmd=command.copy('image.glyph(\uE292,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE293,nilesoft_icon_size) tip=['E293',tip.info] cmd=command.copy('image.glyph(\uE293,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE294,nilesoft_icon_size) tip=['E294',tip.info] cmd=command.copy('image.glyph(\uE294,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE295,nilesoft_icon_size) tip=['E295',tip.info] cmd=command.copy('image.glyph(\uE295,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE296,nilesoft_icon_size) tip=['E296',tip.info] cmd=command.copy('image.glyph(\uE296,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE297,nilesoft_icon_size) tip=['E297',tip.info] cmd=command.copy('image.glyph(\uE297,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE298,nilesoft_icon_size) tip=['E298',tip.info] cmd=command.copy('image.glyph(\uE298,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE299,nilesoft_icon_size) tip=['E299',tip.info] cmd=command.copy('image.glyph(\uE299,@nilesoft_icon_size)'))
		item(image=image.glyph(\uE29A,nilesoft_icon_size) tip=['E29A',tip.info] cmd=command.copy('image.glyph(\uE29A,@nilesoft_icon_size)'))
	}

	separator

	menu(title='Fluent #1')
	{
	    item(image=image.fluent(\uE700,segoe_icon_size) tip=['GlobalNavButton',tip.info] cmd=command.copy('image.fluent(\uE700,@segoe_icon_size)'))
	    item(image=image.fluent(\uE701,segoe_icon_size) tip=['Wifi',tip.info] cmd=command.copy('image.fluent(\uE701,@segoe_icon_size)'))
	    item(image=image.fluent(\uE702,segoe_icon_size) tip=['Bluetooth',tip.info] cmd=command.copy('image.fluent(\uE702,@segoe_icon_size)'))
	    item(image=image.fluent(\uE703,segoe_icon_size) tip=['Connect',tip.info] cmd=command.copy('image.fluent(\uE703,@segoe_icon_size)'))
	    item(image=image.fluent(\uE704,segoe_icon_size) tip=['InternetSharing',tip.info] cmd=command.copy('image.fluent(\uE704,@segoe_icon_size)'))
	    item(image=image.fluent(\uE705,segoe_icon_size) tip=['VPN',tip.info] cmd=command.copy('image.fluent(\uE705,@segoe_icon_size)'))
	    item(image=image.fluent(\uE706,segoe_icon_size) tip=['Brightness',tip.info] cmd=command.copy('image.fluent(\uE706,@segoe_icon_size)'))
	    item(image=image.fluent(\uE707,segoe_icon_size) tip=['MapPin',tip.info] cmd=command.copy('image.fluent(\uE707,@segoe_icon_size)'))
	    item(image=image.fluent(\uE708,segoe_icon_size) tip=['QuietHours',tip.info] cmd=command.copy('image.fluent(\uE708,@segoe_icon_size)'))
	    item(image=image.fluent(\uE709,segoe_icon_size) tip=['Airplane',tip.info] cmd=command.copy('image.fluent(\uE709,@segoe_icon_size)'))
	    item(image=image.fluent(\uE70A,segoe_icon_size) tip=['Tablet',tip.info] cmd=command.copy('image.fluent(\uE70A,@segoe_icon_size)'))
	    item(image=image.fluent(\uE70B,segoe_icon_size) tip=['QuickNote',tip.info] cmd=command.copy('image.fluent(\uE70B,@segoe_icon_size)'))
	    item(image=image.fluent(\uE70C,segoe_icon_size) tip=['RememberedDevice',tip.info] cmd=command.copy('image.fluent(\uE70C,@segoe_icon_size)'))
	    item(image=image.fluent(\uE70D,segoe_icon_size) tip=['ChevronDown',tip.info] cmd=command.copy('image.fluent(\uE70D,@segoe_icon_size)'))
	    item(image=image.fluent(\uE70E,segoe_icon_size) tip=['ChevronUp',tip.info] cmd=command.copy('image.fluent(\uE70E,@segoe_icon_size)'))
	    item(image=image.fluent(\uE70F,segoe_icon_size) tip=['Edit',tip.info] cmd=command.copy('image.fluent(\uE70F,@segoe_icon_size)'))

	    item(image=image.fluent(\uE710,segoe_icon_size) tip=['Add',tip.info] cmd=command.copy('image.fluent(\uE710,@segoe_icon_size)') col)
	    item(image=image.fluent(\uE711,segoe_icon_size) tip=['Cancel',tip.info] cmd=command.copy('image.fluent(\uE711,@segoe_icon_size)'))
	    item(image=image.fluent(\uE712,segoe_icon_size) tip=['More',tip.info] cmd=command.copy('image.fluent(\uE712,@segoe_icon_size)'))
	    item(image=image.fluent(\uE713,segoe_icon_size) tip=['Settings',tip.info] cmd=command.copy('image.fluent(\uE713,@segoe_icon_size)'))
	    item(image=image.fluent(\uE714,segoe_icon_size) tip=['Video',tip.info] cmd=command.copy('image.fluent(\uE714,@segoe_icon_size)'))
	    item(image=image.fluent(\uE715,segoe_icon_size) tip=['Mail',tip.info] cmd=command.copy('image.fluent(\uE715,@segoe_icon_size)'))
	    item(image=image.fluent(\uE716,segoe_icon_size) tip=['People',tip.info] cmd=command.copy('image.fluent(\uE716,@segoe_icon_size)'))
	    item(image=image.fluent(\uE717,segoe_icon_size) tip=['Phone',tip.info] cmd=command.copy('image.fluent(\uE717,@segoe_icon_size)'))
	    item(image=image.fluent(\uE718,segoe_icon_size) tip=['Pin',tip.info] cmd=command.copy('image.fluent(\uE718,@segoe_icon_size)'))
	    item(image=image.fluent(\uE719,segoe_icon_size) tip=['Shop',tip.info] cmd=command.copy('image.fluent(\uE719,@segoe_icon_size)'))
	    item(image=image.fluent(\uE71A,segoe_icon_size) tip=['Stop',tip.info] cmd=command.copy('image.fluent(\uE71A,@segoe_icon_size)'))
	    item(image=image.fluent(\uE71B,segoe_icon_size) tip=['Link',tip.info] cmd=command.copy('image.fluent(\uE71B,@segoe_icon_size)'))
	    item(image=image.fluent(\uE71C,segoe_icon_size) tip=['Filter',tip.info] cmd=command.copy('image.fluent(\uE71C,@segoe_icon_size)'))
	    item(image=image.fluent(\uE71D,segoe_icon_size) tip=['AllApps',tip.info] cmd=command.copy('image.fluent(\uE71D,@segoe_icon_size)'))
	    item(image=image.fluent(\uE71E,segoe_icon_size) tip=['Zoom',tip.info] cmd=command.copy('image.fluent(\uE71E,@segoe_icon_size)'))
	    item(image=image.fluent(\uE71F,segoe_icon_size) tip=['ZoomOut',tip.info] cmd=command.copy('image.fluent(\uE71F,@segoe_icon_size)'))

	    item(image=image.fluent(\uE720,segoe_icon_size) tip=['Microphone',tip.info] cmd=command.copy('image.fluent(\uE720,@segoe_icon_size)') col)
	    item(image=image.fluent(\uE721,segoe_icon_size) tip=['Search',tip.info] cmd=command.copy('image.fluent(\uE721,@segoe_icon_size)'))
	    item(image=image.fluent(\uE722,segoe_icon_size) tip=['Camera',tip.info] cmd=command.copy('image.fluent(\uE722,@segoe_icon_size)'))
	    item(image=image.fluent(\uE723,segoe_icon_size) tip=['Attach',tip.info] cmd=command.copy('image.fluent(\uE723,@segoe_icon_size)'))
	    item(image=image.fluent(\uE724,segoe_icon_size) tip=['Send',tip.info] cmd=command.copy('image.fluent(\uE724,@segoe_icon_size)'))
	    item(image=image.fluent(\uE725,segoe_icon_size) tip=['SendFill',tip.info] cmd=command.copy('image.fluent(\uE725,@segoe_icon_size)'))
	    item(image=image.fluent(\uE726,segoe_icon_size) tip=['WalkSolid',tip.info] cmd=command.copy('image.fluent(\uE726,@segoe_icon_size)'))
	    item(image=image.fluent(\uE727,segoe_icon_size) tip=['InPrivate',tip.info] cmd=command.copy('image.fluent(\uE727,@segoe_icon_size)'))
	    item(image=image.fluent(\uE728,segoe_icon_size) tip=['FavoriteList',tip.info] cmd=command.copy('image.fluent(\uE728,@segoe_icon_size)'))
	    item(image=image.fluent(\uE729,segoe_icon_size) tip=['PageSolid',tip.info] cmd=command.copy('image.fluent(\uE729,@segoe_icon_size)'))
	    item(image=image.fluent(\uE72A,segoe_icon_size) tip=['Forward',tip.info] cmd=command.copy('image.fluent(\uE72A,@segoe_icon_size)'))
	    item(image=image.fluent(\uE72B,segoe_icon_size) tip=['Back',tip.info] cmd=command.copy('image.fluent(\uE72B,@segoe_icon_size)'))
	    item(image=image.fluent(\uE72C,segoe_icon_size) tip=['Refresh',tip.info] cmd=command.copy('image.fluent(\uE72C,@segoe_icon_size)'))
	    item(image=image.fluent(\uE72D,segoe_icon_size) tip=['Share',tip.info] cmd=command.copy('image.fluent(\uE72D,@segoe_icon_size)'))
	    item(image=image.fluent(\uE72E,segoe_icon_size) tip=['Lock',tip.info] cmd=command.copy('image.fluent(\uE72E,@segoe_icon_size)'))
	    item(image=image.fluent(\uE730,segoe_icon_size) tip=['ReportHacked',tip.info] cmd=command.copy('image.fluent(\uE730,@segoe_icon_size)'))

	    item(image=image.fluent(\uE731,segoe_icon_size) tip=['EMI',tip.info] cmd=command.copy('image.fluent(\uE731,@segoe_icon_size)') col)
	    item(image=image.fluent(\uE734,segoe_icon_size) tip=['FavoriteStar',tip.info] cmd=command.copy('image.fluent(\uE734,@segoe_icon_size)'))
	    item(image=image.fluent(\uE735,segoe_icon_size) tip=['FavoriteStarFill',tip.info] cmd=command.copy('image.fluent(\uE735,@segoe_icon_size)'))
	    item(image=image.fluent(\uE736,segoe_icon_size) tip=['ReadingMode',tip.info] cmd=command.copy('image.fluent(\uE736,@segoe_icon_size)'))
	    item(image=image.fluent(\uE737,segoe_icon_size) tip=['Favicon',tip.info] cmd=command.copy('image.fluent(\uE737,@segoe_icon_size)'))
	    item(image=image.fluent(\uE738,segoe_icon_size) tip=['Remove',tip.info] cmd=command.copy('image.fluent(\uE738,@segoe_icon_size)'))
	    item(image=image.fluent(\uE739,segoe_icon_size) tip=['Checkbox',tip.info] cmd=command.copy('image.fluent(\uE739,@segoe_icon_size)'))
	    item(image=image.fluent(\uE73A,segoe_icon_size) tip=['CheckboxComposite',tip.info] cmd=command.copy('image.fluent(\uE73A,@segoe_icon_size)'))
	    item(image=image.fluent(\uE73B,segoe_icon_size) tip=['CheckboxFill',tip.info] cmd=command.copy('image.fluent(\uE73B,@segoe_icon_size)'))
	    item(image=image.fluent(\uE73C,segoe_icon_size) tip=['CheckboxIndeterminate',tip.info] cmd=command.copy('image.fluent(\uE73C,@segoe_icon_size)'))
	    item(image=image.fluent(\uE73D,segoe_icon_size) tip=['CheckboxCompositeReversed',tip.info] cmd=command.copy('image.fluent(\uE73D,@segoe_icon_size)'))
	    item(image=image.fluent(\uE73E,segoe_icon_size) tip=['CheckMark',tip.info] cmd=command.copy('image.fluent(\uE73E,@segoe_icon_size)'))
	    item(image=image.fluent(\uE73F,segoe_icon_size) tip=['BackToWindow',tip.info] cmd=command.copy('image.fluent(\uE73F,@segoe_icon_size)'))
	    item(image=image.fluent(\uE740,segoe_icon_size) tip=['FullScreen',tip.info] cmd=command.copy('image.fluent(\uE740,@segoe_icon_size)'))
	    item(image=image.fluent(\uE741,segoe_icon_size) tip=['ResizeTouchLarger',tip.info] cmd=command.copy('image.fluent(\uE741,@segoe_icon_size)'))
	    item(image=image.fluent(\uE742,segoe_icon_size) tip=['ResizeTouchSmaller',tip.info] cmd=command.copy('image.fluent(\uE742,@segoe_icon_size)'))

	    item(image=image.fluent(\uE743,segoe_icon_size) tip=['ResizeMouseSmall',tip.info] cmd=command.copy('image.fluent(\uE743,@segoe_icon_size)') col)
	    item(image=image.fluent(\uE744,segoe_icon_size) tip=['ResizeMouseMedium',tip.info] cmd=command.copy('image.fluent(\uE744,@segoe_icon_size)'))
	    item(image=image.fluent(\uE745,segoe_icon_size) tip=['ResizeMouseWide',tip.info] cmd=command.copy('image.fluent(\uE745,@segoe_icon_size)'))
	    item(image=image.fluent(\uE746,segoe_icon_size) tip=['ResizeMouseTall',tip.info] cmd=command.copy('image.fluent(\uE746,@segoe_icon_size)'))
	    item(image=image.fluent(\uE747,segoe_icon_size) tip=['ResizeMouseLarge',tip.info] cmd=command.copy('image.fluent(\uE747,@segoe_icon_size)'))
	    item(image=image.fluent(\uE748,segoe_icon_size) tip=['SwitchUser',tip.info] cmd=command.copy('image.fluent(\uE748,@segoe_icon_size)'))
	    item(image=image.fluent(\uE749,segoe_icon_size) tip=['Print',tip.info] cmd=command.copy('image.fluent(\uE749,@segoe_icon_size)'))
	    item(image=image.fluent(\uE74A,segoe_icon_size) tip=['Up',tip.info] cmd=command.copy('image.fluent(\uE74A,@segoe_icon_size)'))
	    item(image=image.fluent(\uE74B,segoe_icon_size) tip=['Down',tip.info] cmd=command.copy('image.fluent(\uE74B,@segoe_icon_size)'))
	    item(image=image.fluent(\uE74C,segoe_icon_size) tip=['OEM',tip.info] cmd=command.copy('image.fluent(\uE74C,@segoe_icon_size)'))
	    item(image=image.fluent(\uE74D,segoe_icon_size) tip=['Delete',tip.info] cmd=command.copy('image.fluent(\uE74D,@segoe_icon_size)'))
	    item(image=image.fluent(\uE74E,segoe_icon_size) tip=['Save',tip.info] cmd=command.copy('image.fluent(\uE74E,@segoe_icon_size)'))
	    item(image=image.fluent(\uE74F,segoe_icon_size) tip=['Mute',tip.info] cmd=command.copy('image.fluent(\uE74F,@segoe_icon_size)'))
	    item(image=image.fluent(\uE750,segoe_icon_size) tip=['BackSpaceQWERTY',tip.info] cmd=command.copy('image.fluent(\uE750,@segoe_icon_size)'))
	    item(image=image.fluent(\uE751,segoe_icon_size) tip=['ReturnKey',tip.info] cmd=command.copy('image.fluent(\uE751,@segoe_icon_size)'))
	    item(image=image.fluent(\uE752,segoe_icon_size) tip=['UpArrowShiftKey',tip.info] cmd=command.copy('image.fluent(\uE752,@segoe_icon_size)'))

	    item(image=image.fluent(\uE753,segoe_icon_size) tip=['Cloud',tip.info] cmd=command.copy('image.fluent(\uE753,@segoe_icon_size)') col)
	    item(image=image.fluent(\uE754,segoe_icon_size) tip=['Flashlight',tip.info] cmd=command.copy('image.fluent(\uE754,@segoe_icon_size)'))
	    item(image=image.fluent(\uE755,segoe_icon_size) tip=['RotationLock',tip.info] cmd=command.copy('image.fluent(\uE755,@segoe_icon_size)'))
	    item(image=image.fluent(\uE756,segoe_icon_size) tip=['CommandPrompt',tip.info] cmd=command.copy('image.fluent(\uE756,@segoe_icon_size)'))
	    item(image=image.fluent(\uE759,segoe_icon_size) tip=['SIPMove',tip.info] cmd=command.copy('image.fluent(\uE759,@segoe_icon_size)'))
	    item(image=image.fluent(\uE75A,segoe_icon_size) tip=['SIPUndock',tip.info] cmd=command.copy('image.fluent(\uE75A,@segoe_icon_size)'))
	    item(image=image.fluent(\uE75B,segoe_icon_size) tip=['SIPRedock',tip.info] cmd=command.copy('image.fluent(\uE75B,@segoe_icon_size)'))
	    item(image=image.fluent(\uE75C,segoe_icon_size) tip=['EraseTool',tip.info] cmd=command.copy('image.fluent(\uE75C,@segoe_icon_size)'))
	    item(image=image.fluent(\uE75D,segoe_icon_size) tip=['UnderscoreSpace',tip.info] cmd=command.copy('image.fluent(\uE75D,@segoe_icon_size)'))
	    item(image=image.fluent(\uE75E,segoe_icon_size) tip=['GripperTool',tip.info] cmd=command.copy('image.fluent(\uE75E,@segoe_icon_size)'))
	    item(image=image.fluent(\uE75F,segoe_icon_size) tip=['Dialpad',tip.info] cmd=command.copy('image.fluent(\uE75F,@segoe_icon_size)'))
	    item(image=image.fluent(\uE760,segoe_icon_size) tip=['PageLeft',tip.info] cmd=command.copy('image.fluent(\uE760,@segoe_icon_size)'))
	    item(image=image.fluent(\uE761,segoe_icon_size) tip=['PageRight',tip.info] cmd=command.copy('image.fluent(\uE761,@segoe_icon_size)'))
	    item(image=image.fluent(\uE762,segoe_icon_size) tip=['MultiSelect',tip.info] cmd=command.copy('image.fluent(\uE762,@segoe_icon_size)'))
	    item(image=image.fluent(\uE763,segoe_icon_size) tip=['KeyboardLeftHanded',tip.info] cmd=command.copy('image.fluent(\uE763,@segoe_icon_size)'))
	    item(image=image.fluent(\uE764,segoe_icon_size) tip=['KeyboardRightHanded',tip.info] cmd=command.copy('image.fluent(\uE764,@segoe_icon_size)'))

	    item(image=image.fluent(\uE765,segoe_icon_size) tip=['KeyboardClassic',tip.info] cmd=command.copy('image.fluent(\uE765,@segoe_icon_size)') col)
	    item(image=image.fluent(\uE766,segoe_icon_size) tip=['KeyboardSplit',tip.info] cmd=command.copy('image.fluent(\uE766,@segoe_icon_size)'))
	    item(image=image.fluent(\uE767,segoe_icon_size) tip=['Volume',tip.info] cmd=command.copy('image.fluent(\uE767,@segoe_icon_size)'))
	    item(image=image.fluent(\uE768,segoe_icon_size) tip=['Play',tip.info] cmd=command.copy('image.fluent(\uE768,@segoe_icon_size)'))
	    item(image=image.fluent(\uE769,segoe_icon_size) tip=['Pause',tip.info] cmd=command.copy('image.fluent(\uE769,@segoe_icon_size)'))
	    item(image=image.fluent(\uE76B,segoe_icon_size) tip=['ChevronLeft',tip.info] cmd=command.copy('image.fluent(\uE76B,@segoe_icon_size)'))
	    item(image=image.fluent(\uE76C,segoe_icon_size) tip=['ChevronRight',tip.info] cmd=command.copy('image.fluent(\uE76C,@segoe_icon_size)'))
	    item(image=image.fluent(\uE76D,segoe_icon_size) tip=['InkingTool',tip.info] cmd=command.copy('image.fluent(\uE76D,@segoe_icon_size)'))
	    item(image=image.fluent(\uE76E,segoe_icon_size) tip=['Emoji2',tip.info] cmd=command.copy('image.fluent(\uE76E,@segoe_icon_size)'))
	    item(image=image.fluent(\uE76F,segoe_icon_size) tip=['GripperBarHorizontal',tip.info] cmd=command.copy('image.fluent(\uE76F,@segoe_icon_size)'))
	    item(image=image.fluent(\uE770,segoe_icon_size) tip=['System',tip.info] cmd=command.copy('image.fluent(\uE770,@segoe_icon_size)'))
	    item(image=image.fluent(\uE771,segoe_icon_size) tip=['Personalize',tip.info] cmd=command.copy('image.fluent(\uE771,@segoe_icon_size)'))
	    item(image=image.fluent(\uE772,segoe_icon_size) tip=['Devices',tip.info] cmd=command.copy('image.fluent(\uE772,@segoe_icon_size)'))
	    item(image=image.fluent(\uE773,segoe_icon_size) tip=['SearchAndApps',tip.info] cmd=command.copy('image.fluent(\uE773,@segoe_icon_size)'))
	    item(image=image.fluent(\uE774,segoe_icon_size) tip=['Globe',tip.info] cmd=command.copy('image.fluent(\uE774,@segoe_icon_size)'))
	    item(image=image.fluent(\uE775,segoe_icon_size) tip=['TimeLanguage',tip.info] cmd=command.copy('image.fluent(\uE775,@segoe_icon_size)'))

	    item(image=image.fluent(\uE776,segoe_icon_size) tip=['EaseOfAccess',tip.info] cmd=command.copy('image.fluent(\uE776,@segoe_icon_size)') col)
	    item(image=image.fluent(\uE777,segoe_icon_size) tip=['UpdateRestore',tip.info] cmd=command.copy('image.fluent(\uE777,@segoe_icon_size)'))
	    item(image=image.fluent(\uE778,segoe_icon_size) tip=['HangUp',tip.info] cmd=command.copy('image.fluent(\uE778,@segoe_icon_size)'))
	    item(image=image.fluent(\uE779,segoe_icon_size) tip=['ContactInfo',tip.info] cmd=command.copy('image.fluent(\uE779,@segoe_icon_size)'))
	    item(image=image.fluent(\uE77A,segoe_icon_size) tip=['Unpin',tip.info] cmd=command.copy('image.fluent(\uE77A,@segoe_icon_size)'))
	    item(image=image.fluent(\uE77B,segoe_icon_size) tip=['Contact',tip.info] cmd=command.copy('image.fluent(\uE77B,@segoe_icon_size)'))
	    item(image=image.fluent(\uE77C,segoe_icon_size) tip=['Memo',tip.info] cmd=command.copy('image.fluent(\uE77C,@segoe_icon_size)'))
	    item(image=image.fluent(\uE77E,segoe_icon_size) tip=['IncomingCall',tip.info] cmd=command.copy('image.fluent(\uE77E,@segoe_icon_size)'))
	    item(image=image.fluent(\uE77F,segoe_icon_size) tip=['Paste',tip.info] cmd=command.copy('image.fluent(\uE77F,@segoe_icon_size)'))
	    item(image=image.fluent(\uE780,segoe_icon_size) tip=['PhoneBook',tip.info] cmd=command.copy('image.fluent(\uE780,@segoe_icon_size)'))
	    item(image=image.fluent(\uE781,segoe_icon_size) tip=['LEDLight',tip.info] cmd=command.copy('image.fluent(\uE781,@segoe_icon_size)'))
	    item(image=image.fluent(\uE783,segoe_icon_size) tip=['Error',tip.info] cmd=command.copy('image.fluent(\uE783,@segoe_icon_size)'))
	    item(image=image.fluent(\uE784,segoe_icon_size) tip=['GripperBarVertical',tip.info] cmd=command.copy('image.fluent(\uE784,@segoe_icon_size)'))
	    item(image=image.fluent(\uE785,segoe_icon_size) tip=['Unlock',tip.info] cmd=command.copy('image.fluent(\uE785,@segoe_icon_size)'))
	    item(image=image.fluent(\uE786,segoe_icon_size) tip=['Slideshow',tip.info] cmd=command.copy('image.fluent(\uE786,@segoe_icon_size)'))
	    item(image=image.fluent(\uE787,segoe_icon_size) tip=['Calendar',tip.info] cmd=command.copy('image.fluent(\uE787,@segoe_icon_size)'))

	    item(image=image.fluent(\uE788,segoe_icon_size) tip=['GripperResize',tip.info] cmd=command.copy('image.fluent(\uE788,@segoe_icon_size)') col)
	    item(image=image.fluent(\uE789,segoe_icon_size) tip=['Megaphone',tip.info] cmd=command.copy('image.fluent(\uE789,@segoe_icon_size)'))
	    item(image=image.fluent(\uE78A,segoe_icon_size) tip=['Trim',tip.info] cmd=command.copy('image.fluent(\uE78A,@segoe_icon_size)'))
	    item(image=image.fluent(\uE78B,segoe_icon_size) tip=['NewWindow',tip.info] cmd=command.copy('image.fluent(\uE78B,@segoe_icon_size)'))
	    item(image=image.fluent(\uE78C,segoe_icon_size) tip=['SaveLocal',tip.info] cmd=command.copy('image.fluent(\uE78C,@segoe_icon_size)'))
	    item(image=image.fluent(\uE790,segoe_icon_size) tip=['Color',tip.info] cmd=command.copy('image.fluent(\uE790,@segoe_icon_size)'))
	    item(image=image.fluent(\uE791,segoe_icon_size) tip=['DataSense',tip.info] cmd=command.copy('image.fluent(\uE791,@segoe_icon_size)'))
	    item(image=image.fluent(\uE792,segoe_icon_size) tip=['SaveAs',tip.info] cmd=command.copy('image.fluent(\uE792,@segoe_icon_size)'))
	    item(image=image.fluent(\uE793,segoe_icon_size) tip=['Light',tip.info] cmd=command.copy('image.fluent(\uE793,@segoe_icon_size)'))
	    item(image=image.fluent(\uE799,segoe_icon_size) tip=['AspectRatio',tip.info] cmd=command.copy('image.fluent(\uE799,@segoe_icon_size)'))
	    item(image=image.fluent(\uE7A5,segoe_icon_size) tip=['DataSenseBar',tip.info] cmd=command.copy('image.fluent(\uE7A5,@segoe_icon_size)'))
	    item(image=image.fluent(\uE7A6,segoe_icon_size) tip=['Redo',tip.info] cmd=command.copy('image.fluent(\uE7A6,@segoe_icon_size)'))
	    item(image=image.fluent(\uE7A7,segoe_icon_size) tip=['Undo',tip.info] cmd=command.copy('image.fluent(\uE7A7,@segoe_icon_size)'))
	    item(image=image.fluent(\uE7A8,segoe_icon_size) tip=['Crop',tip.info] cmd=command.copy('image.fluent(\uE7A8,@segoe_icon_size)'))
	    item(image=image.fluent(\uE7AC,segoe_icon_size) tip=['OpenWith',tip.info] cmd=command.copy('image.fluent(\uE7AC,@segoe_icon_size)'))
	    item(image=image.fluent(\uE7AD,segoe_icon_size) tip=['Rotate',tip.info] cmd=command.copy('image.fluent(\uE7AD,@segoe_icon_size)'))

	    item(image=image.fluent(\uE7B3,segoe_icon_size) tip=['RedEye',tip.info] cmd=command.copy('image.fluent(\uE7B3,@segoe_icon_size)') col)
	    item(image=image.fluent(\uE7B5,segoe_icon_size) tip=['SetlockScreen',tip.info] cmd=command.copy('image.fluent(\uE7B5,@segoe_icon_size)'))
	    item(image=image.fluent(\uE7B7,segoe_icon_size) tip=['MapPin2',tip.info] cmd=command.copy('image.fluent(\uE7B7,@segoe_icon_size)'))
	    item(image=image.fluent(\uE7B8,segoe_icon_size) tip=['Package',tip.info] cmd=command.copy('image.fluent(\uE7B8,@segoe_icon_size)'))
	    item(image=image.fluent(\uE7BA,segoe_icon_size) tip=['Warning',tip.info] cmd=command.copy('image.fluent(\uE7BA,@segoe_icon_size)'))
	    item(image=image.fluent(\uE7BC,segoe_icon_size) tip=['ReadingList',tip.info] cmd=command.copy('image.fluent(\uE7BC,@segoe_icon_size)'))
	    item(image=image.fluent(\uE7BE,segoe_icon_size) tip=['Education',tip.info] cmd=command.copy('image.fluent(\uE7BE,@segoe_icon_size)'))
	    item(image=image.fluent(\uE7BF,segoe_icon_size) tip=['ShoppingCart',tip.info] cmd=command.copy('image.fluent(\uE7BF,@segoe_icon_size)'))
	    item(image=image.fluent(\uE7C0,segoe_icon_size) tip=['Train',tip.info] cmd=command.copy('image.fluent(\uE7C0,@segoe_icon_size)'))
	    item(image=image.fluent(\uE7C1,segoe_icon_size) tip=['Flag',tip.info] cmd=command.copy('image.fluent(\uE7C1,@segoe_icon_size)'))
	    item(image=image.fluent(\uE7C2,segoe_icon_size) tip=['Move',tip.info] cmd=command.copy('image.fluent(\uE7C2,@segoe_icon_size)'))
	    item(image=image.fluent(\uE7C3,segoe_icon_size) tip=['Page',tip.info] cmd=command.copy('image.fluent(\uE7C3,@segoe_icon_size)'))
	    item(image=image.fluent(\uE7C4,segoe_icon_size) tip=['TaskView',tip.info] cmd=command.copy('image.fluent(\uE7C4,@segoe_icon_size)'))
	    item(image=image.fluent(\uE7C5,segoe_icon_size) tip=['BrowsePhotos',tip.info] cmd=command.copy('image.fluent(\uE7C5,@segoe_icon_size)'))
	    item(image=image.fluent(\uE7C6,segoe_icon_size) tip=['HalfStarLeft',tip.info] cmd=command.copy('image.fluent(\uE7C6,@segoe_icon_size)'))
	    item(image=image.fluent(\uE7C7,segoe_icon_size) tip=['HalfStarRight',tip.info] cmd=command.copy('image.fluent(\uE7C7,@segoe_icon_size)'))

	    item(image=image.fluent(\uE7C8,segoe_icon_size) tip=['Record',tip.info] cmd=command.copy('image.fluent(\uE7C8,@segoe_icon_size)') col)
	    item(image=image.fluent(\uE7C9,segoe_icon_size) tip=['TouchPointer',tip.info] cmd=command.copy('image.fluent(\uE7C9,@segoe_icon_size)'))
	    item(image=image.fluent(\uE7DE,segoe_icon_size) tip=['LangJPN',tip.info] cmd=command.copy('image.fluent(\uE7DE,@segoe_icon_size)'))
	    item(image=image.fluent(\uE7E3,segoe_icon_size) tip=['Ferry',tip.info] cmd=command.copy('image.fluent(\uE7E3,@segoe_icon_size)'))
	    item(image=image.fluent(\uE7E6,segoe_icon_size) tip=['Highlight',tip.info] cmd=command.copy('image.fluent(\uE7E6,@segoe_icon_size)'))
	    item(image=image.fluent(\uE7E7,segoe_icon_size) tip=['ActionCenterNotification',tip.info] cmd=command.copy('image.fluent(\uE7E7,@segoe_icon_size)'))
	    item(image=image.fluent(\uE7E8,segoe_icon_size) tip=['PowerButton',tip.info] cmd=command.copy('image.fluent(\uE7E8,@segoe_icon_size)'))
	    item(image=image.fluent(\uE7EA,segoe_icon_size) tip=['ResizeTouchNarrower',tip.info] cmd=command.copy('image.fluent(\uE7EA,@segoe_icon_size)'))
	    item(image=image.fluent(\uE7EB,segoe_icon_size) tip=['ResizeTouchShorter',tip.info] cmd=command.copy('image.fluent(\uE7EB,@segoe_icon_size)'))
	    item(image=image.fluent(\uE7EC,segoe_icon_size) tip=['DrivingMode',tip.info] cmd=command.copy('image.fluent(\uE7EC,@segoe_icon_size)'))
	    item(image=image.fluent(\uE7ED,segoe_icon_size) tip=['RingerSilent',tip.info] cmd=command.copy('image.fluent(\uE7ED,@segoe_icon_size)'))
	    item(image=image.fluent(\uE7EE,segoe_icon_size) tip=['OtherUser',tip.info] cmd=command.copy('image.fluent(\uE7EE,@segoe_icon_size)'))
	    item(image=image.fluent(\uE7EF,segoe_icon_size) tip=['Admin',tip.info] cmd=command.copy('image.fluent(\uE7EF,@segoe_icon_size)'))
	    item(image=image.fluent(\uE7F0,segoe_icon_size) tip=['CC',tip.info] cmd=command.copy('image.fluent(\uE7F0,@segoe_icon_size)'))
	    item(image=image.fluent(\uE7F1,segoe_icon_size) tip=['SDCard',tip.info] cmd=command.copy('image.fluent(\uE7F1,@segoe_icon_size)'))
	    item(image=image.fluent(\uE7F2,segoe_icon_size) tip=['CallForwarding',tip.info] cmd=command.copy('image.fluent(\uE7F2,@segoe_icon_size)'))

	    item(image=image.fluent(\uE7F3,segoe_icon_size) tip=['SettingsDisplaySound',tip.info] cmd=command.copy('image.fluent(\uE7F3,@segoe_icon_size)') col)
	    item(image=image.fluent(\uE7F4,segoe_icon_size) tip=['TVMonitor',tip.info] cmd=command.copy('image.fluent(\uE7F4,@segoe_icon_size)'))
	    item(image=image.fluent(\uE7F5,segoe_icon_size) tip=['Speakers',tip.info] cmd=command.copy('image.fluent(\uE7F5,@segoe_icon_size)'))
	    item(image=image.fluent(\uE7F6,segoe_icon_size) tip=['Headphone',tip.info] cmd=command.copy('image.fluent(\uE7F6,@segoe_icon_size)'))
	    item(image=image.fluent(\uE7F7,segoe_icon_size) tip=['DeviceLaptopPic',tip.info] cmd=command.copy('image.fluent(\uE7F7,@segoe_icon_size)'))
	    item(image=image.fluent(\uE7F8,segoe_icon_size) tip=['DeviceLaptopNoPic',tip.info] cmd=command.copy('image.fluent(\uE7F8,@segoe_icon_size)'))
	    item(image=image.fluent(\uE7F9,segoe_icon_size) tip=['DeviceMonitorRightPic',tip.info] cmd=command.copy('image.fluent(\uE7F9,@segoe_icon_size)'))
	    item(image=image.fluent(\uE7FA,segoe_icon_size) tip=['DeviceMonitorLeftPic',tip.info] cmd=command.copy('image.fluent(\uE7FA,@segoe_icon_size)'))
	    item(image=image.fluent(\uE7FB,segoe_icon_size) tip=['DeviceMonitorNoPic',tip.info] cmd=command.copy('image.fluent(\uE7FB,@segoe_icon_size)'))
	    item(image=image.fluent(\uE7FC,segoe_icon_size) tip=['Game',tip.info] cmd=command.copy('image.fluent(\uE7FC,@segoe_icon_size)'))
	    item(image=image.fluent(\uE7FD,segoe_icon_size) tip=['HorizontalTabKey',tip.info] cmd=command.copy('image.fluent(\uE7FD,@segoe_icon_size)'))
	    item(image=image.fluent(\uE802,segoe_icon_size) tip=['StreetsideSplitMinimize',tip.info] cmd=command.copy('image.fluent(\uE802,@segoe_icon_size)'))
	    item(image=image.fluent(\uE803,segoe_icon_size) tip=['StreetsideSplitExpand',tip.info] cmd=command.copy('image.fluent(\uE803,@segoe_icon_size)'))
	    item(image=image.fluent(\uE804,segoe_icon_size) tip=['Car',tip.info] cmd=command.copy('image.fluent(\uE804,@segoe_icon_size)'))
	    item(image=image.fluent(\uE805,segoe_icon_size) tip=['Walk',tip.info] cmd=command.copy('image.fluent(\uE805,@segoe_icon_size)'))
	    item(image=image.fluent(\uE806,segoe_icon_size) tip=['Bus',tip.info] cmd=command.copy('image.fluent(\uE806,@segoe_icon_size)'))

	    item(image=image.fluent(\uE809,segoe_icon_size) tip=['TiltUp',tip.info] cmd=command.copy('image.fluent(\uE809,@segoe_icon_size)') col)
	    item(image=image.fluent(\uE80A,segoe_icon_size) tip=['TiltDown',tip.info] cmd=command.copy('image.fluent(\uE80A,@segoe_icon_size)'))
	    item(image=image.fluent(\uE80B,segoe_icon_size) tip=['CallControl',tip.info] cmd=command.copy('image.fluent(\uE80B,@segoe_icon_size)'))
	    item(image=image.fluent(\uE80C,segoe_icon_size) tip=['RotateMapRight',tip.info] cmd=command.copy('image.fluent(\uE80C,@segoe_icon_size)'))
	    item(image=image.fluent(\uE80D,segoe_icon_size) tip=['RotateMapLeft',tip.info] cmd=command.copy('image.fluent(\uE80D,@segoe_icon_size)'))
	    item(image=image.fluent(\uE80F,segoe_icon_size) tip=['Home',tip.info] cmd=command.copy('image.fluent(\uE80F,@segoe_icon_size)'))
	    item(image=image.fluent(\uE811,segoe_icon_size) tip=['ParkingLocation',tip.info] cmd=command.copy('image.fluent(\uE811,@segoe_icon_size)'))
	    item(image=image.fluent(\uE812,segoe_icon_size) tip=['MapCompassTop',tip.info] cmd=command.copy('image.fluent(\uE812,@segoe_icon_size)'))
	    item(image=image.fluent(\uE813,segoe_icon_size) tip=['MapCompassBottom',tip.info] cmd=command.copy('image.fluent(\uE813,@segoe_icon_size)'))
	    item(image=image.fluent(\uE814,segoe_icon_size) tip=['IncidentTriangle',tip.info] cmd=command.copy('image.fluent(\uE814,@segoe_icon_size)'))
	    item(image=image.fluent(\uE815,segoe_icon_size) tip=['Touch',tip.info] cmd=command.copy('image.fluent(\uE815,@segoe_icon_size)'))
	    item(image=image.fluent(\uE816,segoe_icon_size) tip=['MapDirections',tip.info] cmd=command.copy('image.fluent(\uE816,@segoe_icon_size)'))
	    item(image=image.fluent(\uE819,segoe_icon_size) tip=['StartPoint',tip.info] cmd=command.copy('image.fluent(\uE819,@segoe_icon_size)'))
	    item(image=image.fluent(\uE81A,segoe_icon_size) tip=['StopPoint',tip.info] cmd=command.copy('image.fluent(\uE81A,@segoe_icon_size)'))
	    item(image=image.fluent(\uE81B,segoe_icon_size) tip=['EndPoint',tip.info] cmd=command.copy('image.fluent(\uE81B,@segoe_icon_size)'))
	    item(image=image.fluent(\uE81C,segoe_icon_size) tip=['History',tip.info] cmd=command.copy('image.fluent(\uE81C,@segoe_icon_size)'))

	    item(image=image.fluent(\uE81D,segoe_icon_size) tip=['Location',tip.info] cmd=command.copy('image.fluent(\uE81D,@segoe_icon_size)') col)
	    item(image=image.fluent(\uE81E,segoe_icon_size) tip=['MapLayers',tip.info] cmd=command.copy('image.fluent(\uE81E,@segoe_icon_size)'))
	    item(image=image.fluent(\uE81F,segoe_icon_size) tip=['Accident',tip.info] cmd=command.copy('image.fluent(\uE81F,@segoe_icon_size)'))
	    item(image=image.fluent(\uE821,segoe_icon_size) tip=['Work',tip.info] cmd=command.copy('image.fluent(\uE821,@segoe_icon_size)'))
	    item(image=image.fluent(\uE822,segoe_icon_size) tip=['Construction',tip.info] cmd=command.copy('image.fluent(\uE822,@segoe_icon_size)'))
	    item(image=image.fluent(\uE823,segoe_icon_size) tip=['Recent',tip.info] cmd=command.copy('image.fluent(\uE823,@segoe_icon_size)'))
	    item(image=image.fluent(\uE825,segoe_icon_size) tip=['Bank',tip.info] cmd=command.copy('image.fluent(\uE825,@segoe_icon_size)'))
	    item(image=image.fluent(\uE826,segoe_icon_size) tip=['DownloadMap',tip.info] cmd=command.copy('image.fluent(\uE826,@segoe_icon_size)'))
	    item(image=image.fluent(\uE829,segoe_icon_size) tip=['InkingToolFill2',tip.info] cmd=command.copy('image.fluent(\uE829,@segoe_icon_size)'))
	    item(image=image.fluent(\uE82A,segoe_icon_size) tip=['HighlightFill2',tip.info] cmd=command.copy('image.fluent(\uE82A,@segoe_icon_size)'))
	    item(image=image.fluent(\uE82B,segoe_icon_size) tip=['EraseToolFill',tip.info] cmd=command.copy('image.fluent(\uE82B,@segoe_icon_size)'))
	    item(image=image.fluent(\uE82C,segoe_icon_size) tip=['EraseToolFill2',tip.info] cmd=command.copy('image.fluent(\uE82C,@segoe_icon_size)'))
	    item(image=image.fluent(\uE82D,segoe_icon_size) tip=['Dictionary',tip.info] cmd=command.copy('image.fluent(\uE82D,@segoe_icon_size)'))
	    item(image=image.fluent(\uE82E,segoe_icon_size) tip=['DictionaryAdd',tip.info] cmd=command.copy('image.fluent(\uE82E,@segoe_icon_size)'))
	    item(image=image.fluent(\uE82F,segoe_icon_size) tip=['ToolTip',tip.info] cmd=command.copy('image.fluent(\uE82F,@segoe_icon_size)'))
	    item(image=image.fluent(\uE830,segoe_icon_size) tip=['ChromeBack',tip.info] cmd=command.copy('image.fluent(\uE830,@segoe_icon_size)'))

	    item(image=image.fluent(\uE835,segoe_icon_size) tip=['ProvisioningPackage',tip.info] cmd=command.copy('image.fluent(\uE835,@segoe_icon_size)') col)
	    item(image=image.fluent(\uE836,segoe_icon_size) tip=['AddRemoteDevice',tip.info] cmd=command.copy('image.fluent(\uE836,@segoe_icon_size)'))
	    item(image=image.fluent(\uE838,segoe_icon_size) tip=['FolderOpen',tip.info] cmd=command.copy('image.fluent(\uE838,@segoe_icon_size)'))
	    item(image=image.fluent(\uE839,segoe_icon_size) tip=['Ethernet',tip.info] cmd=command.copy('image.fluent(\uE839,@segoe_icon_size)'))
	    item(image=image.fluent(\uE83A,segoe_icon_size) tip=['ShareBroadband',tip.info] cmd=command.copy('image.fluent(\uE83A,@segoe_icon_size)'))
	    item(image=image.fluent(\uE83B,segoe_icon_size) tip=['DirectAccess',tip.info] cmd=command.copy('image.fluent(\uE83B,@segoe_icon_size)'))
	    item(image=image.fluent(\uE83C,segoe_icon_size) tip=['DialUp',tip.info] cmd=command.copy('image.fluent(\uE83C,@segoe_icon_size)'))
	    item(image=image.fluent(\uE83D,segoe_icon_size) tip=['DefenderApp',tip.info] cmd=command.copy('image.fluent(\uE83D,@segoe_icon_size)'))
	    item(image=image.fluent(\uE83E,segoe_icon_size) tip=['BatteryCharging9',tip.info] cmd=command.copy('image.fluent(\uE83E,@segoe_icon_size)'))
	    item(image=image.fluent(\uE83F,segoe_icon_size) tip=['Battery10',tip.info] cmd=command.copy('image.fluent(\uE83F,@segoe_icon_size)'))
	    item(image=image.fluent(\uE840,segoe_icon_size) tip=['Pinned',tip.info] cmd=command.copy('image.fluent(\uE840,@segoe_icon_size)'))
	    item(image=image.fluent(\uE841,segoe_icon_size) tip=['PinFill',tip.info] cmd=command.copy('image.fluent(\uE841,@segoe_icon_size)'))
	    item(image=image.fluent(\uE842,segoe_icon_size) tip=['PinnedFill',tip.info] cmd=command.copy('image.fluent(\uE842,@segoe_icon_size)'))
	    item(image=image.fluent(\uE843,segoe_icon_size) tip=['PeriodKey',tip.info] cmd=command.copy('image.fluent(\uE843,@segoe_icon_size)'))
	    item(image=image.fluent(\uE844,segoe_icon_size) tip=['PuncKey',tip.info] cmd=command.copy('image.fluent(\uE844,@segoe_icon_size)'))
	    item(image=image.fluent(\uE845,segoe_icon_size) tip=['RevToggleKey',tip.info] cmd=command.copy('image.fluent(\uE845,@segoe_icon_size)'))

	    item(image=image.fluent(\uE846,segoe_icon_size) tip=['RightArrowKeyTime1',tip.info] cmd=command.copy('image.fluent(\uE846,@segoe_icon_size)') col)
	    item(image=image.fluent(\uE847,segoe_icon_size) tip=['RightArrowKeyTime2',tip.info] cmd=command.copy('image.fluent(\uE847,@segoe_icon_size)'))
	    item(image=image.fluent(\uE848,segoe_icon_size) tip=['LeftQuote',tip.info] cmd=command.copy('image.fluent(\uE848,@segoe_icon_size)'))
	    item(image=image.fluent(\uE849,segoe_icon_size) tip=['RightQuote',tip.info] cmd=command.copy('image.fluent(\uE849,@segoe_icon_size)'))
	    item(image=image.fluent(\uE84A,segoe_icon_size) tip=['DownShiftKey',tip.info] cmd=command.copy('image.fluent(\uE84A,@segoe_icon_size)'))
	    item(image=image.fluent(\uE84B,segoe_icon_size) tip=['UpShiftKey',tip.info] cmd=command.copy('image.fluent(\uE84B,@segoe_icon_size)'))
	    item(image=image.fluent(\uE84C,segoe_icon_size) tip=['PuncKey0',tip.info] cmd=command.copy('image.fluent(\uE84C,@segoe_icon_size)'))
	    item(image=image.fluent(\uE84D,segoe_icon_size) tip=['PuncKeyLeftBottom',tip.info] cmd=command.copy('image.fluent(\uE84D,@segoe_icon_size)'))
	    item(image=image.fluent(\uE84E,segoe_icon_size) tip=['RightArrowKeyTime3',tip.info] cmd=command.copy('image.fluent(\uE84E,@segoe_icon_size)'))
	    item(image=image.fluent(\uE84F,segoe_icon_size) tip=['RightArrowKeyTime4',tip.info] cmd=command.copy('image.fluent(\uE84F,@segoe_icon_size)'))
	    item(image=image.fluent(\uE850,segoe_icon_size) tip=['Battery0',tip.info] cmd=command.copy('image.fluent(\uE850,@segoe_icon_size)'))
	    item(image=image.fluent(\uE851,segoe_icon_size) tip=['Battery1',tip.info] cmd=command.copy('image.fluent(\uE851,@segoe_icon_size)'))
	    item(image=image.fluent(\uE852,segoe_icon_size) tip=['Battery2',tip.info] cmd=command.copy('image.fluent(\uE852,@segoe_icon_size)'))
	    item(image=image.fluent(\uE853,segoe_icon_size) tip=['Battery3',tip.info] cmd=command.copy('image.fluent(\uE853,@segoe_icon_size)'))
	    item(image=image.fluent(\uE854,segoe_icon_size) tip=['Battery4',tip.info] cmd=command.copy('image.fluent(\uE854,@segoe_icon_size)'))
	    item(image=image.fluent(\uE855,segoe_icon_size) tip=['Battery5',tip.info] cmd=command.copy('image.fluent(\uE855,@segoe_icon_size)'))
	}

	menu(title='Fluent #2')
	{
	    item(image=image.fluent(\uE856,segoe_icon_size) tip=['Battery6',tip.info] cmd=command.copy('image.fluent(\uE856,@segoe_icon_size)'))
	    item(image=image.fluent(\uE857,segoe_icon_size) tip=['Battery7',tip.info] cmd=command.copy('image.fluent(\uE857,@segoe_icon_size)'))
	    item(image=image.fluent(\uE858,segoe_icon_size) tip=['Battery8',tip.info] cmd=command.copy('image.fluent(\uE858,@segoe_icon_size)'))
	    item(image=image.fluent(\uE859,segoe_icon_size) tip=['Battery9',tip.info] cmd=command.copy('image.fluent(\uE859,@segoe_icon_size)'))
	    item(image=image.fluent(\uE85A,segoe_icon_size) tip=['BatteryCharging0',tip.info] cmd=command.copy('image.fluent(\uE85A,@segoe_icon_size)'))
	    item(image=image.fluent(\uE85B,segoe_icon_size) tip=['BatteryCharging1',tip.info] cmd=command.copy('image.fluent(\uE85B,@segoe_icon_size)'))
	    item(image=image.fluent(\uE85C,segoe_icon_size) tip=['BatteryCharging2',tip.info] cmd=command.copy('image.fluent(\uE85C,@segoe_icon_size)'))
	    item(image=image.fluent(\uE85D,segoe_icon_size) tip=['BatteryCharging3',tip.info] cmd=command.copy('image.fluent(\uE85D,@segoe_icon_size)'))
	    item(image=image.fluent(\uE85E,segoe_icon_size) tip=['BatteryCharging4',tip.info] cmd=command.copy('image.fluent(\uE85E,@segoe_icon_size)'))
	    item(image=image.fluent(\uE85F,segoe_icon_size) tip=['BatteryCharging5',tip.info] cmd=command.copy('image.fluent(\uE85F,@segoe_icon_size)'))
	    item(image=image.fluent(\uE860,segoe_icon_size) tip=['BatteryCharging6',tip.info] cmd=command.copy('image.fluent(\uE860,@segoe_icon_size)'))
	    item(image=image.fluent(\uE861,segoe_icon_size) tip=['BatteryCharging7',tip.info] cmd=command.copy('image.fluent(\uE861,@segoe_icon_size)'))
	    item(image=image.fluent(\uE862,segoe_icon_size) tip=['BatteryCharging8',tip.info] cmd=command.copy('image.fluent(\uE862,@segoe_icon_size)'))
	    item(image=image.fluent(\uE863,segoe_icon_size) tip=['BatterySaver0',tip.info] cmd=command.copy('image.fluent(\uE863,@segoe_icon_size)'))
	    item(image=image.fluent(\uE864,segoe_icon_size) tip=['BatterySaver1',tip.info] cmd=command.copy('image.fluent(\uE864,@segoe_icon_size)'))
	    item(image=image.fluent(\uE865,segoe_icon_size) tip=['BatterySaver2',tip.info] cmd=command.copy('image.fluent(\uE865,@segoe_icon_size)'))

	    item(image=image.fluent(\uE866,segoe_icon_size) tip=['BatterySaver3',tip.info] cmd=command.copy('image.fluent(\uE866,@segoe_icon_size)') col)
	    item(image=image.fluent(\uE867,segoe_icon_size) tip=['BatterySaver4',tip.info] cmd=command.copy('image.fluent(\uE867,@segoe_icon_size)'))
	    item(image=image.fluent(\uE868,segoe_icon_size) tip=['BatterySaver5',tip.info] cmd=command.copy('image.fluent(\uE868,@segoe_icon_size)'))
	    item(image=image.fluent(\uE869,segoe_icon_size) tip=['BatterySaver6',tip.info] cmd=command.copy('image.fluent(\uE869,@segoe_icon_size)'))
	    item(image=image.fluent(\uE86A,segoe_icon_size) tip=['BatterySaver7',tip.info] cmd=command.copy('image.fluent(\uE86A,@segoe_icon_size)'))
	    item(image=image.fluent(\uE86B,segoe_icon_size) tip=['BatterySaver8',tip.info] cmd=command.copy('image.fluent(\uE86B,@segoe_icon_size)'))
	    item(image=image.fluent(\uE86C,segoe_icon_size) tip=['SignalBars1',tip.info] cmd=command.copy('image.fluent(\uE86C,@segoe_icon_size)'))
	    item(image=image.fluent(\uE86D,segoe_icon_size) tip=['SignalBars2',tip.info] cmd=command.copy('image.fluent(\uE86D,@segoe_icon_size)'))
	    item(image=image.fluent(\uE86E,segoe_icon_size) tip=['SignalBars3',tip.info] cmd=command.copy('image.fluent(\uE86E,@segoe_icon_size)'))
	    item(image=image.fluent(\uE86F,segoe_icon_size) tip=['SignalBars4',tip.info] cmd=command.copy('image.fluent(\uE86F,@segoe_icon_size)'))
	    item(image=image.fluent(\uE870,segoe_icon_size) tip=['SignalBars5',tip.info] cmd=command.copy('image.fluent(\uE870,@segoe_icon_size)'))
	    item(image=image.fluent(\uE871,segoe_icon_size) tip=['SignalNotConnected',tip.info] cmd=command.copy('image.fluent(\uE871,@segoe_icon_size)'))
	    item(image=image.fluent(\uE872,segoe_icon_size) tip=['Wifi1',tip.info] cmd=command.copy('image.fluent(\uE872,@segoe_icon_size)'))
	    item(image=image.fluent(\uE873,segoe_icon_size) tip=['Wifi2',tip.info] cmd=command.copy('image.fluent(\uE873,@segoe_icon_size)'))
	    item(image=image.fluent(\uE874,segoe_icon_size) tip=['Wifi3',tip.info] cmd=command.copy('image.fluent(\uE874,@segoe_icon_size)'))
	    item(image=image.fluent(\uE875,segoe_icon_size) tip=['MobSIMLock',tip.info] cmd=command.copy('image.fluent(\uE875,@segoe_icon_size)'))

	    item(image=image.fluent(\uE876,segoe_icon_size) tip=['MobSIMMissing',tip.info] cmd=command.copy('image.fluent(\uE876,@segoe_icon_size)') col)
	    item(image=image.fluent(\uE877,segoe_icon_size) tip=['Vibrate',tip.info] cmd=command.copy('image.fluent(\uE877,@segoe_icon_size)'))
	    item(image=image.fluent(\uE878,segoe_icon_size) tip=['RoamingInternational',tip.info] cmd=command.copy('image.fluent(\uE878,@segoe_icon_size)'))
	    item(image=image.fluent(\uE879,segoe_icon_size) tip=['RoamingDomestic',tip.info] cmd=command.copy('image.fluent(\uE879,@segoe_icon_size)'))
	    item(image=image.fluent(\uE87A,segoe_icon_size) tip=['CallForwardInternational',tip.info] cmd=command.copy('image.fluent(\uE87A,@segoe_icon_size)'))
	    item(image=image.fluent(\uE87B,segoe_icon_size) tip=['CallForwardRoaming',tip.info] cmd=command.copy('image.fluent(\uE87B,@segoe_icon_size)'))
	    item(image=image.fluent(\uE87C,segoe_icon_size) tip=['JpnRomanji',tip.info] cmd=command.copy('image.fluent(\uE87C,@segoe_icon_size)'))
	    item(image=image.fluent(\uE87D,segoe_icon_size) tip=['JpnRomanjiLock',tip.info] cmd=command.copy('image.fluent(\uE87D,@segoe_icon_size)'))
	    item(image=image.fluent(\uE87E,segoe_icon_size) tip=['JpnRomanjiShift',tip.info] cmd=command.copy('image.fluent(\uE87E,@segoe_icon_size)'))
	    item(image=image.fluent(\uE87F,segoe_icon_size) tip=['JpnRomanjiShiftLock',tip.info] cmd=command.copy('image.fluent(\uE87F,@segoe_icon_size)'))
	    item(image=image.fluent(\uE880,segoe_icon_size) tip=['StatusDataTransfer',tip.info] cmd=command.copy('image.fluent(\uE880,@segoe_icon_size)'))
	    item(image=image.fluent(\uE881,segoe_icon_size) tip=['StatusDataTransferVPN',tip.info] cmd=command.copy('image.fluent(\uE881,@segoe_icon_size)'))
	    item(image=image.fluent(\uE882,segoe_icon_size) tip=['StatusDualSIM2',tip.info] cmd=command.copy('image.fluent(\uE882,@segoe_icon_size)'))
	    item(image=image.fluent(\uE883,segoe_icon_size) tip=['StatusDualSIM2VPN',tip.info] cmd=command.copy('image.fluent(\uE883,@segoe_icon_size)'))
	    item(image=image.fluent(\uE884,segoe_icon_size) tip=['StatusDualSIM1',tip.info] cmd=command.copy('image.fluent(\uE884,@segoe_icon_size)'))
	    item(image=image.fluent(\uE885,segoe_icon_size) tip=['StatusDualSIM1VPN',tip.info] cmd=command.copy('image.fluent(\uE885,@segoe_icon_size)'))

	    item(image=image.fluent(\uE886,segoe_icon_size) tip=['StatusSGLTE',tip.info] cmd=command.copy('image.fluent(\uE886,@segoe_icon_size)') col)
	    item(image=image.fluent(\uE887,segoe_icon_size) tip=['StatusSGLTECell',tip.info] cmd=command.copy('image.fluent(\uE887,@segoe_icon_size)'))
	    item(image=image.fluent(\uE888,segoe_icon_size) tip=['StatusSGLTEDataVPN',tip.info] cmd=command.copy('image.fluent(\uE888,@segoe_icon_size)'))
	    item(image=image.fluent(\uE889,segoe_icon_size) tip=['StatusVPN',tip.info] cmd=command.copy('image.fluent(\uE889,@segoe_icon_size)'))
	    item(image=image.fluent(\uE88A,segoe_icon_size) tip=['WifiHotspot',tip.info] cmd=command.copy('image.fluent(\uE88A,@segoe_icon_size)'))
	    item(image=image.fluent(\uE88B,segoe_icon_size) tip=['LanguageKor',tip.info] cmd=command.copy('image.fluent(\uE88B,@segoe_icon_size)'))
	    item(image=image.fluent(\uE88C,segoe_icon_size) tip=['LanguageCht',tip.info] cmd=command.copy('image.fluent(\uE88C,@segoe_icon_size)'))
	    item(image=image.fluent(\uE88D,segoe_icon_size) tip=['LanguageChs',tip.info] cmd=command.copy('image.fluent(\uE88D,@segoe_icon_size)'))
	    item(image=image.fluent(\uE88E,segoe_icon_size) tip=['USB',tip.info] cmd=command.copy('image.fluent(\uE88E,@segoe_icon_size)'))
	    item(image=image.fluent(\uE88F,segoe_icon_size) tip=['InkingToolFill',tip.info] cmd=command.copy('image.fluent(\uE88F,@segoe_icon_size)'))
	    item(image=image.fluent(\uE890,segoe_icon_size) tip=['View',tip.info] cmd=command.copy('image.fluent(\uE890,@segoe_icon_size)'))
	    item(image=image.fluent(\uE891,segoe_icon_size) tip=['HighlightFill',tip.info] cmd=command.copy('image.fluent(\uE891,@segoe_icon_size)'))
	    item(image=image.fluent(\uE892,segoe_icon_size) tip=['Previous',tip.info] cmd=command.copy('image.fluent(\uE892,@segoe_icon_size)'))
	    item(image=image.fluent(\uE893,segoe_icon_size) tip=['Next',tip.info] cmd=command.copy('image.fluent(\uE893,@segoe_icon_size)'))
	    item(image=image.fluent(\uE894,segoe_icon_size) tip=['Clear',tip.info] cmd=command.copy('image.fluent(\uE894,@segoe_icon_size)'))
	    item(image=image.fluent(\uE895,segoe_icon_size) tip=['Sync',tip.info] cmd=command.copy('image.fluent(\uE895,@segoe_icon_size)'))

	    item(image=image.fluent(\uE896,segoe_icon_size) tip=['Download',tip.info] cmd=command.copy('image.fluent(\uE896,@segoe_icon_size)') col)
	    item(image=image.fluent(\uE897,segoe_icon_size) tip=['Help',tip.info] cmd=command.copy('image.fluent(\uE897,@segoe_icon_size)'))
	    item(image=image.fluent(\uE898,segoe_icon_size) tip=['Upload',tip.info] cmd=command.copy('image.fluent(\uE898,@segoe_icon_size)'))
	    item(image=image.fluent(\uE899,segoe_icon_size) tip=['Emoji',tip.info] cmd=command.copy('image.fluent(\uE899,@segoe_icon_size)'))
	    item(image=image.fluent(\uE89A,segoe_icon_size) tip=['TwoPage',tip.info] cmd=command.copy('image.fluent(\uE89A,@segoe_icon_size)'))
	    item(image=image.fluent(\uE89B,segoe_icon_size) tip=['LeaveChat',tip.info] cmd=command.copy('image.fluent(\uE89B,@segoe_icon_size)'))
	    item(image=image.fluent(\uE89C,segoe_icon_size) tip=['MailForward',tip.info] cmd=command.copy('image.fluent(\uE89C,@segoe_icon_size)'))
	    item(image=image.fluent(\uE89E,segoe_icon_size) tip=['RotateCamera',tip.info] cmd=command.copy('image.fluent(\uE89E,@segoe_icon_size)'))
	    item(image=image.fluent(\uE89F,segoe_icon_size) tip=['ClosePane',tip.info] cmd=command.copy('image.fluent(\uE89F,@segoe_icon_size)'))
	    item(image=image.fluent(\uE8A0,segoe_icon_size) tip=['OpenPane',tip.info] cmd=command.copy('image.fluent(\uE8A0,@segoe_icon_size)'))
	    item(image=image.fluent(\uE8A1,segoe_icon_size) tip=['PreviewLink',tip.info] cmd=command.copy('image.fluent(\uE8A1,@segoe_icon_size)'))
	    item(image=image.fluent(\uE8A2,segoe_icon_size) tip=['AttachCamera',tip.info] cmd=command.copy('image.fluent(\uE8A2,@segoe_icon_size)'))
	    item(image=image.fluent(\uE8A3,segoe_icon_size) tip=['ZoomIn',tip.info] cmd=command.copy('image.fluent(\uE8A3,@segoe_icon_size)'))
	    item(image=image.fluent(\uE8A4,segoe_icon_size) tip=['Bookmarks',tip.info] cmd=command.copy('image.fluent(\uE8A4,@segoe_icon_size)'))
	    item(image=image.fluent(\uE8A5,segoe_icon_size) tip=['Document',tip.info] cmd=command.copy('image.fluent(\uE8A5,@segoe_icon_size)'))
	    item(image=image.fluent(\uE8A6,segoe_icon_size) tip=['ProtectedDocument',tip.info] cmd=command.copy('image.fluent(\uE8A6,@segoe_icon_size)'))

	    item(image=image.fluent(\uE8A7,segoe_icon_size) tip=['OpenInNewWindow',tip.info] cmd=command.copy('image.fluent(\uE8A7,@segoe_icon_size)') col)
	    item(image=image.fluent(\uE8A8,segoe_icon_size) tip=['MailFill',tip.info] cmd=command.copy('image.fluent(\uE8A8,@segoe_icon_size)'))
	    item(image=image.fluent(\uE8A9,segoe_icon_size) tip=['ViewAll',tip.info] cmd=command.copy('image.fluent(\uE8A9,@segoe_icon_size)'))
	    item(image=image.fluent(\uE8AA,segoe_icon_size) tip=['VideoChat',tip.info] cmd=command.copy('image.fluent(\uE8AA,@segoe_icon_size)'))
	    item(image=image.fluent(\uE8AB,segoe_icon_size) tip=['Switch',tip.info] cmd=command.copy('image.fluent(\uE8AB,@segoe_icon_size)'))
	    item(image=image.fluent(\uE8AC,segoe_icon_size) tip=['Rename',tip.info] cmd=command.copy('image.fluent(\uE8AC,@segoe_icon_size)'))
	    item(image=image.fluent(\uE8AD,segoe_icon_size) tip=['Go',tip.info] cmd=command.copy('image.fluent(\uE8AD,@segoe_icon_size)'))
	    item(image=image.fluent(\uE8AE,segoe_icon_size) tip=['SurfaceHub',tip.info] cmd=command.copy('image.fluent(\uE8AE,@segoe_icon_size)'))
	    item(image=image.fluent(\uE8AF,segoe_icon_size) tip=['Remote',tip.info] cmd=command.copy('image.fluent(\uE8AF,@segoe_icon_size)'))
	    item(image=image.fluent(\uE8B0,segoe_icon_size) tip=['Click',tip.info] cmd=command.copy('image.fluent(\uE8B0,@segoe_icon_size)'))
	    item(image=image.fluent(\uE8B1,segoe_icon_size) tip=['Shuffle',tip.info] cmd=command.copy('image.fluent(\uE8B1,@segoe_icon_size)'))
	    item(image=image.fluent(\uE8B2,segoe_icon_size) tip=['Movies',tip.info] cmd=command.copy('image.fluent(\uE8B2,@segoe_icon_size)'))
	    item(image=image.fluent(\uE8B3,segoe_icon_size) tip=['SelectAll',tip.info] cmd=command.copy('image.fluent(\uE8B3,@segoe_icon_size)'))
	    item(image=image.fluent(\uE8B4,segoe_icon_size) tip=['Orientation',tip.info] cmd=command.copy('image.fluent(\uE8B4,@segoe_icon_size)'))
	    item(image=image.fluent(\uE8B5,segoe_icon_size) tip=['Import',tip.info] cmd=command.copy('image.fluent(\uE8B5,@segoe_icon_size)'))
	    item(image=image.fluent(\uE8B6,segoe_icon_size) tip=['ImportAll',tip.info] cmd=command.copy('image.fluent(\uE8B6,@segoe_icon_size)'))

	    item(image=image.fluent(\uE8B7,segoe_icon_size) tip=['Folder',tip.info] cmd=command.copy('image.fluent(\uE8B7,@segoe_icon_size)') col)
	    item(image=image.fluent(\uE8B8,segoe_icon_size) tip=['Webcam',tip.info] cmd=command.copy('image.fluent(\uE8B8,@segoe_icon_size)'))
	    item(image=image.fluent(\uE8B9,segoe_icon_size) tip=['Picture',tip.info] cmd=command.copy('image.fluent(\uE8B9,@segoe_icon_size)'))
	    item(image=image.fluent(\uE8BA,segoe_icon_size) tip=['Caption',tip.info] cmd=command.copy('image.fluent(\uE8BA,@segoe_icon_size)'))
	    item(image=image.fluent(\uE8BB,segoe_icon_size) tip=['ChromeClose',tip.info] cmd=command.copy('image.fluent(\uE8BB,@segoe_icon_size)'))
	    item(image=image.fluent(\uE8BC,segoe_icon_size) tip=['ShowResults',tip.info] cmd=command.copy('image.fluent(\uE8BC,@segoe_icon_size)'))
	    item(image=image.fluent(\uE8BD,segoe_icon_size) tip=['Message',tip.info] cmd=command.copy('image.fluent(\uE8BD,@segoe_icon_size)'))
	    item(image=image.fluent(\uE8BE,segoe_icon_size) tip=['Leaf',tip.info] cmd=command.copy('image.fluent(\uE8BE,@segoe_icon_size)'))
	    item(image=image.fluent(\uE8BF,segoe_icon_size) tip=['CalendarDay',tip.info] cmd=command.copy('image.fluent(\uE8BF,@segoe_icon_size)'))
	    item(image=image.fluent(\uE8C0,segoe_icon_size) tip=['CalendarWeek',tip.info] cmd=command.copy('image.fluent(\uE8C0,@segoe_icon_size)'))
	    item(image=image.fluent(\uE8C1,segoe_icon_size) tip=['Characters',tip.info] cmd=command.copy('image.fluent(\uE8C1,@segoe_icon_size)'))
	    item(image=image.fluent(\uE8C2,segoe_icon_size) tip=['MailReplyAll',tip.info] cmd=command.copy('image.fluent(\uE8C2,@segoe_icon_size)'))
	    item(image=image.fluent(\uE8C3,segoe_icon_size) tip=['Read',tip.info] cmd=command.copy('image.fluent(\uE8C3,@segoe_icon_size)'))
	    item(image=image.fluent(\uE8C4,segoe_icon_size) tip=['ShowBcc',tip.info] cmd=command.copy('image.fluent(\uE8C4,@segoe_icon_size)'))
	    item(image=image.fluent(\uE8C5,segoe_icon_size) tip=['HideBcc',tip.info] cmd=command.copy('image.fluent(\uE8C5,@segoe_icon_size)'))
	    item(image=image.fluent(\uE8C6,segoe_icon_size) tip=['Cut',tip.info] cmd=command.copy('image.fluent(\uE8C6,@segoe_icon_size)'))

	    item(image=image.fluent(\uE8C7,segoe_icon_size) tip=['PaymentCard',tip.info] cmd=command.copy('image.fluent(\uE8C7,@segoe_icon_size)') col)
	    item(image=image.fluent(\uE8C8,segoe_icon_size) tip=['Copy',tip.info] cmd=command.copy('image.fluent(\uE8C8,@segoe_icon_size)'))
	    item(image=image.fluent(\uE8C9,segoe_icon_size) tip=['Important',tip.info] cmd=command.copy('image.fluent(\uE8C9,@segoe_icon_size)'))
	    item(image=image.fluent(\uE8CA,segoe_icon_size) tip=['MailReply',tip.info] cmd=command.copy('image.fluent(\uE8CA,@segoe_icon_size)'))
	    item(image=image.fluent(\uE8CB,segoe_icon_size) tip=['Sort',tip.info] cmd=command.copy('image.fluent(\uE8CB,@segoe_icon_size)'))
	    item(image=image.fluent(\uE8CC,segoe_icon_size) tip=['MobileTablet',tip.info] cmd=command.copy('image.fluent(\uE8CC,@segoe_icon_size)'))
	    item(image=image.fluent(\uE8CD,segoe_icon_size) tip=['DisconnectDrive',tip.info] cmd=command.copy('image.fluent(\uE8CD,@segoe_icon_size)'))
	    item(image=image.fluent(\uE8CE,segoe_icon_size) tip=['MapDrive',tip.info] cmd=command.copy('image.fluent(\uE8CE,@segoe_icon_size)'))
	    item(image=image.fluent(\uE8CF,segoe_icon_size) tip=['ContactPresence',tip.info] cmd=command.copy('image.fluent(\uE8CF,@segoe_icon_size)'))
	    item(image=image.fluent(\uE8D0,segoe_icon_size) tip=['Priority',tip.info] cmd=command.copy('image.fluent(\uE8D0,@segoe_icon_size)'))
	    item(image=image.fluent(\uE8D1,segoe_icon_size) tip=['GotoToday',tip.info] cmd=command.copy('image.fluent(\uE8D1,@segoe_icon_size)'))
	    item(image=image.fluent(\uE8D2,segoe_icon_size) tip=['Font',tip.info] cmd=command.copy('image.fluent(\uE8D2,@segoe_icon_size)'))
	    item(image=image.fluent(\uE8D3,segoe_icon_size) tip=['FontColor',tip.info] cmd=command.copy('image.fluent(\uE8D3,@segoe_icon_size)'))
	    item(image=image.fluent(\uE8D4,segoe_icon_size) tip=['Contact2',tip.info] cmd=command.copy('image.fluent(\uE8D4,@segoe_icon_size)'))
	    item(image=image.fluent(\uE8D5,segoe_icon_size) tip=['FolderFill',tip.info] cmd=command.copy('image.fluent(\uE8D5,@segoe_icon_size)'))
	    item(image=image.fluent(\uE8D6,segoe_icon_size) tip=['Audio',tip.info] cmd=command.copy('image.fluent(\uE8D6,@segoe_icon_size)'))

	    item(image=image.fluent(\uE8D7,segoe_icon_size) tip=['Permissions',tip.info] cmd=command.copy('image.fluent(\uE8D7,@segoe_icon_size)') col)
	    item(image=image.fluent(\uE8D8,segoe_icon_size) tip=['DisableUpdates',tip.info] cmd=command.copy('image.fluent(\uE8D8,@segoe_icon_size)'))
	    item(image=image.fluent(\uE8D9,segoe_icon_size) tip=['Unfavorite',tip.info] cmd=command.copy('image.fluent(\uE8D9,@segoe_icon_size)'))
	    item(image=image.fluent(\uE8DA,segoe_icon_size) tip=['OpenLocal',tip.info] cmd=command.copy('image.fluent(\uE8DA,@segoe_icon_size)'))
	    item(image=image.fluent(\uE8DB,segoe_icon_size) tip=['Italic',tip.info] cmd=command.copy('image.fluent(\uE8DB,@segoe_icon_size)'))
	    item(image=image.fluent(\uE8DC,segoe_icon_size) tip=['Underline',tip.info] cmd=command.copy('image.fluent(\uE8DC,@segoe_icon_size)'))
	    item(image=image.fluent(\uE8DD,segoe_icon_size) tip=['Bold',tip.info] cmd=command.copy('image.fluent(\uE8DD,@segoe_icon_size)'))
	    item(image=image.fluent(\uE8DE,segoe_icon_size) tip=['MoveToFolder',tip.info] cmd=command.copy('image.fluent(\uE8DE,@segoe_icon_size)'))
	    item(image=image.fluent(\uE8DF,segoe_icon_size) tip=['LikeDislike',tip.info] cmd=command.copy('image.fluent(\uE8DF,@segoe_icon_size)'))
	    item(image=image.fluent(\uE8E0,segoe_icon_size) tip=['Dislike',tip.info] cmd=command.copy('image.fluent(\uE8E0,@segoe_icon_size)'))
	    item(image=image.fluent(\uE8E1,segoe_icon_size) tip=['Like',tip.info] cmd=command.copy('image.fluent(\uE8E1,@segoe_icon_size)'))
	    item(image=image.fluent(\uE8E2,segoe_icon_size) tip=['AlignRight',tip.info] cmd=command.copy('image.fluent(\uE8E2,@segoe_icon_size)'))
	    item(image=image.fluent(\uE8E3,segoe_icon_size) tip=['AlignCenter',tip.info] cmd=command.copy('image.fluent(\uE8E3,@segoe_icon_size)'))
	    item(image=image.fluent(\uE8E4,segoe_icon_size) tip=['AlignLeft',tip.info] cmd=command.copy('image.fluent(\uE8E4,@segoe_icon_size)'))
	    item(image=image.fluent(\uE8E5,segoe_icon_size) tip=['OpenFile',tip.info] cmd=command.copy('image.fluent(\uE8E5,@segoe_icon_size)'))
	    item(image=image.fluent(\uE8E6,segoe_icon_size) tip=['ClearSelection',tip.info] cmd=command.copy('image.fluent(\uE8E6,@segoe_icon_size)'))

	    item(image=image.fluent(\uE8E7,segoe_icon_size) tip=['FontDecrease',tip.info] cmd=command.copy('image.fluent(\uE8E7,@segoe_icon_size)') col)
	    item(image=image.fluent(\uE8E8,segoe_icon_size) tip=['FontIncrease',tip.info] cmd=command.copy('image.fluent(\uE8E8,@segoe_icon_size)'))
	    item(image=image.fluent(\uE8E9,segoe_icon_size) tip=['FontSize',tip.info] cmd=command.copy('image.fluent(\uE8E9,@segoe_icon_size)'))
	    item(image=image.fluent(\uE8EA,segoe_icon_size) tip=['CellPhone',tip.info] cmd=command.copy('image.fluent(\uE8EA,@segoe_icon_size)'))
	    item(image=image.fluent(\uE8EB,segoe_icon_size) tip=['Reshare',tip.info] cmd=command.copy('image.fluent(\uE8EB,@segoe_icon_size)'))
	    item(image=image.fluent(\uE8EC,segoe_icon_size) tip=['Tag',tip.info] cmd=command.copy('image.fluent(\uE8EC,@segoe_icon_size)'))
	    item(image=image.fluent(\uE8ED,segoe_icon_size) tip=['RepeatOne',tip.info] cmd=command.copy('image.fluent(\uE8ED,@segoe_icon_size)'))
	    item(image=image.fluent(\uE8EE,segoe_icon_size) tip=['RepeatAll',tip.info] cmd=command.copy('image.fluent(\uE8EE,@segoe_icon_size)'))
	    item(image=image.fluent(\uE8EF,segoe_icon_size) tip=['Calculator',tip.info] cmd=command.copy('image.fluent(\uE8EF,@segoe_icon_size)'))
	    item(image=image.fluent(\uE8F0,segoe_icon_size) tip=['Directions',tip.info] cmd=command.copy('image.fluent(\uE8F0,@segoe_icon_size)'))
	    item(image=image.fluent(\uE8F1,segoe_icon_size) tip=['Library',tip.info] cmd=command.copy('image.fluent(\uE8F1,@segoe_icon_size)'))
	    item(image=image.fluent(\uE8F2,segoe_icon_size) tip=['ChatBubbles',tip.info] cmd=command.copy('image.fluent(\uE8F2,@segoe_icon_size)'))
	    item(image=image.fluent(\uE8F3,segoe_icon_size) tip=['PostUpdate',tip.info] cmd=command.copy('image.fluent(\uE8F3,@segoe_icon_size)'))
	    item(image=image.fluent(\uE8F4,segoe_icon_size) tip=['NewFolder',tip.info] cmd=command.copy('image.fluent(\uE8F4,@segoe_icon_size)'))
	    item(image=image.fluent(\uE8F5,segoe_icon_size) tip=['CalendarReply',tip.info] cmd=command.copy('image.fluent(\uE8F5,@segoe_icon_size)'))
	    item(image=image.fluent(\uE8F6,segoe_icon_size) tip=['UnsyncFolder',tip.info] cmd=command.copy('image.fluent(\uE8F6,@segoe_icon_size)'))

	    item(image=image.fluent(\uE8F7,segoe_icon_size) tip=['SyncFolder',tip.info] cmd=command.copy('image.fluent(\uE8F7,@segoe_icon_size)') col)
	    item(image=image.fluent(\uE8F8,segoe_icon_size) tip=['BlockContact',tip.info] cmd=command.copy('image.fluent(\uE8F8,@segoe_icon_size)'))
	    item(image=image.fluent(\uE8F9,segoe_icon_size) tip=['SwitchApps',tip.info] cmd=command.copy('image.fluent(\uE8F9,@segoe_icon_size)'))
	    item(image=image.fluent(\uE8FA,segoe_icon_size) tip=['AddFriend',tip.info] cmd=command.copy('image.fluent(\uE8FA,@segoe_icon_size)'))
	    item(image=image.fluent(\uE8FB,segoe_icon_size) tip=['Accept',tip.info] cmd=command.copy('image.fluent(\uE8FB,@segoe_icon_size)'))
	    item(image=image.fluent(\uE8FC,segoe_icon_size) tip=['GoToStart',tip.info] cmd=command.copy('image.fluent(\uE8FC,@segoe_icon_size)'))
	    item(image=image.fluent(\uE8FD,segoe_icon_size) tip=['BulletedList',tip.info] cmd=command.copy('image.fluent(\uE8FD,@segoe_icon_size)'))
	    item(image=image.fluent(\uE8FE,segoe_icon_size) tip=['Scan',tip.info] cmd=command.copy('image.fluent(\uE8FE,@segoe_icon_size)'))
	    item(image=image.fluent(\uE8FF,segoe_icon_size) tip=['Preview',tip.info] cmd=command.copy('image.fluent(\uE8FF,@segoe_icon_size)'))
	    item(image=image.fluent(\uE902,segoe_icon_size) tip=['Group',tip.info] cmd=command.copy('image.fluent(\uE902,@segoe_icon_size)'))
	    item(image=image.fluent(\uE904,segoe_icon_size) tip=['ZeroBars',tip.info] cmd=command.copy('image.fluent(\uE904,@segoe_icon_size)'))
	    item(image=image.fluent(\uE905,segoe_icon_size) tip=['OneBar',tip.info] cmd=command.copy('image.fluent(\uE905,@segoe_icon_size)'))
	    item(image=image.fluent(\uE906,segoe_icon_size) tip=['TwoBars',tip.info] cmd=command.copy('image.fluent(\uE906,@segoe_icon_size)'))
	    item(image=image.fluent(\uE907,segoe_icon_size) tip=['ThreeBars',tip.info] cmd=command.copy('image.fluent(\uE907,@segoe_icon_size)'))
	    item(image=image.fluent(\uE908,segoe_icon_size) tip=['FourBars',tip.info] cmd=command.copy('image.fluent(\uE908,@segoe_icon_size)'))
	    item(image=image.fluent(\uE909,segoe_icon_size) tip=['World',tip.info] cmd=command.copy('image.fluent(\uE909,@segoe_icon_size)'))

	    item(image=image.fluent(\uE90A,segoe_icon_size) tip=['Comment',tip.info] cmd=command.copy('image.fluent(\uE90A,@segoe_icon_size)') col)
	    item(image=image.fluent(\uE90B,segoe_icon_size) tip=['MusicInfo',tip.info] cmd=command.copy('image.fluent(\uE90B,@segoe_icon_size)'))
	    item(image=image.fluent(\uE90C,segoe_icon_size) tip=['DockLeft',tip.info] cmd=command.copy('image.fluent(\uE90C,@segoe_icon_size)'))
	    item(image=image.fluent(\uE90D,segoe_icon_size) tip=['DockRight',tip.info] cmd=command.copy('image.fluent(\uE90D,@segoe_icon_size)'))
	    item(image=image.fluent(\uE90E,segoe_icon_size) tip=['DockBottom',tip.info] cmd=command.copy('image.fluent(\uE90E,@segoe_icon_size)'))
	    item(image=image.fluent(\uE90F,segoe_icon_size) tip=['Repair',tip.info] cmd=command.copy('image.fluent(\uE90F,@segoe_icon_size)'))
	    item(image=image.fluent(\uE910,segoe_icon_size) tip=['Accounts',tip.info] cmd=command.copy('image.fluent(\uE910,@segoe_icon_size)'))
	    item(image=image.fluent(\uE911,segoe_icon_size) tip=['DullSound',tip.info] cmd=command.copy('image.fluent(\uE911,@segoe_icon_size)'))
	    item(image=image.fluent(\uE912,segoe_icon_size) tip=['Manage',tip.info] cmd=command.copy('image.fluent(\uE912,@segoe_icon_size)'))
	    item(image=image.fluent(\uE913,segoe_icon_size) tip=['Street',tip.info] cmd=command.copy('image.fluent(\uE913,@segoe_icon_size)'))
	    item(image=image.fluent(\uE914,segoe_icon_size) tip=['Printer3D',tip.info] cmd=command.copy('image.fluent(\uE914,@segoe_icon_size)'))
	    item(image=image.fluent(\uE915,segoe_icon_size) tip=['RadioBullet',tip.info] cmd=command.copy('image.fluent(\uE915,@segoe_icon_size)'))
	    item(image=image.fluent(\uE916,segoe_icon_size) tip=['Stopwatch',tip.info] cmd=command.copy('image.fluent(\uE916,@segoe_icon_size)'))
	    item(image=image.fluent(\uE91B,segoe_icon_size) tip=['Photo',tip.info] cmd=command.copy('image.fluent(\uE91B,@segoe_icon_size)'))
	    item(image=image.fluent(\uE91C,segoe_icon_size) tip=['ActionCenter',tip.info] cmd=command.copy('image.fluent(\uE91C,@segoe_icon_size)'))
	    item(image=image.fluent(\uE91F,segoe_icon_size) tip=['FullCircleMask',tip.info] cmd=command.copy('image.fluent(\uE91F,@segoe_icon_size)'))

	    item(image=image.fluent(\uE921,segoe_icon_size) tip=['ChromeMinimize',tip.info] cmd=command.copy('image.fluent(\uE921,@segoe_icon_size)') col)
	    item(image=image.fluent(\uE922,segoe_icon_size) tip=['ChromeMaximize',tip.info] cmd=command.copy('image.fluent(\uE922,@segoe_icon_size)'))
	    item(image=image.fluent(\uE923,segoe_icon_size) tip=['ChromeRestore',tip.info] cmd=command.copy('image.fluent(\uE923,@segoe_icon_size)'))
	    item(image=image.fluent(\uE924,segoe_icon_size) tip=['Annotation',tip.info] cmd=command.copy('image.fluent(\uE924,@segoe_icon_size)'))
	    item(image=image.fluent(\uE925,segoe_icon_size) tip=['BackSpaceQWERTYSm',tip.info] cmd=command.copy('image.fluent(\uE925,@segoe_icon_size)'))
	    item(image=image.fluent(\uE926,segoe_icon_size) tip=['BackSpaceQWERTYMd',tip.info] cmd=command.copy('image.fluent(\uE926,@segoe_icon_size)'))
	    item(image=image.fluent(\uE927,segoe_icon_size) tip=['Swipe',tip.info] cmd=command.copy('image.fluent(\uE927,@segoe_icon_size)'))
	    item(image=image.fluent(\uE928,segoe_icon_size) tip=['Fingerprint',tip.info] cmd=command.copy('image.fluent(\uE928,@segoe_icon_size)'))
	    item(image=image.fluent(\uE929,segoe_icon_size) tip=['Handwriting',tip.info] cmd=command.copy('image.fluent(\uE929,@segoe_icon_size)'))
	    item(image=image.fluent(\uE92C,segoe_icon_size) tip=['ChromeBackToWindow',tip.info] cmd=command.copy('image.fluent(\uE92C,@segoe_icon_size)'))
	    item(image=image.fluent(\uE92D,segoe_icon_size) tip=['ChromeFullScreen',tip.info] cmd=command.copy('image.fluent(\uE92D,@segoe_icon_size)'))
	    item(image=image.fluent(\uE92E,segoe_icon_size) tip=['KeyboardStandard',tip.info] cmd=command.copy('image.fluent(\uE92E,@segoe_icon_size)'))
	    item(image=image.fluent(\uE92F,segoe_icon_size) tip=['KeyboardDismiss',tip.info] cmd=command.copy('image.fluent(\uE92F,@segoe_icon_size)'))
	    item(image=image.fluent(\uE930,segoe_icon_size) tip=['Completed',tip.info] cmd=command.copy('image.fluent(\uE930,@segoe_icon_size)'))
	    item(image=image.fluent(\uE931,segoe_icon_size) tip=['ChromeAnnotate',tip.info] cmd=command.copy('image.fluent(\uE931,@segoe_icon_size)'))
	    item(image=image.fluent(\uE932,segoe_icon_size) tip=['Label',tip.info] cmd=command.copy('image.fluent(\uE932,@segoe_icon_size)'))

	    item(image=image.fluent(\uE933,segoe_icon_size) tip=['IBeam',tip.info] cmd=command.copy('image.fluent(\uE933,@segoe_icon_size)') col)
	    item(image=image.fluent(\uE934,segoe_icon_size) tip=['IBeamOutline',tip.info] cmd=command.copy('image.fluent(\uE934,@segoe_icon_size)'))
	    item(image=image.fluent(\uE935,segoe_icon_size) tip=['FlickDown',tip.info] cmd=command.copy('image.fluent(\uE935,@segoe_icon_size)'))
	    item(image=image.fluent(\uE936,segoe_icon_size) tip=['FlickUp',tip.info] cmd=command.copy('image.fluent(\uE936,@segoe_icon_size)'))
	    item(image=image.fluent(\uE937,segoe_icon_size) tip=['FlickLeft',tip.info] cmd=command.copy('image.fluent(\uE937,@segoe_icon_size)'))
	    item(image=image.fluent(\uE938,segoe_icon_size) tip=['FlickRight',tip.info] cmd=command.copy('image.fluent(\uE938,@segoe_icon_size)'))
	    item(image=image.fluent(\uE939,segoe_icon_size) tip=['FeedbackApp',tip.info] cmd=command.copy('image.fluent(\uE939,@segoe_icon_size)'))
	    item(image=image.fluent(\uE93C,segoe_icon_size) tip=['MusicAlbum',tip.info] cmd=command.copy('image.fluent(\uE93C,@segoe_icon_size)'))
	    item(image=image.fluent(\uE93E,segoe_icon_size) tip=['Streaming',tip.info] cmd=command.copy('image.fluent(\uE93E,@segoe_icon_size)'))
	    item(image=image.fluent(\uE943,segoe_icon_size) tip=['Code',tip.info] cmd=command.copy('image.fluent(\uE943,@segoe_icon_size)'))
	    item(image=image.fluent(\uE944,segoe_icon_size) tip=['ReturnToWindow',tip.info] cmd=command.copy('image.fluent(\uE944,@segoe_icon_size)'))
	    item(image=image.fluent(\uE945,segoe_icon_size) tip=['LightningBolt',tip.info] cmd=command.copy('image.fluent(\uE945,@segoe_icon_size)'))
	    item(image=image.fluent(\uE946,segoe_icon_size) tip=['Info',tip.info] cmd=command.copy('image.fluent(\uE946,@segoe_icon_size)'))
	    item(image=image.fluent(\uE947,segoe_icon_size) tip=['CalculatorMultiply',tip.info] cmd=command.copy('image.fluent(\uE947,@segoe_icon_size)'))
	    item(image=image.fluent(\uE948,segoe_icon_size) tip=['CalculatorAddition',tip.info] cmd=command.copy('image.fluent(\uE948,@segoe_icon_size)'))
	    item(image=image.fluent(\uE949,segoe_icon_size) tip=['CalculatorSubtract',tip.info] cmd=command.copy('image.fluent(\uE949,@segoe_icon_size)'))

	    item(image=image.fluent(\uE94A,segoe_icon_size) tip=['CalculatorDivide',tip.info] cmd=command.copy('image.fluent(\uE94A,@segoe_icon_size)') col)
	    item(image=image.fluent(\uE94B,segoe_icon_size) tip=['CalculatorSquareroot',tip.info] cmd=command.copy('image.fluent(\uE94B,@segoe_icon_size)'))
	    item(image=image.fluent(\uE94C,segoe_icon_size) tip=['CalculatorPercentage',tip.info] cmd=command.copy('image.fluent(\uE94C,@segoe_icon_size)'))
	    item(image=image.fluent(\uE94D,segoe_icon_size) tip=['CalculatorNegate',tip.info] cmd=command.copy('image.fluent(\uE94D,@segoe_icon_size)'))
	    item(image=image.fluent(\uE94E,segoe_icon_size) tip=['CalculatorEqualTo',tip.info] cmd=command.copy('image.fluent(\uE94E,@segoe_icon_size)'))
	    item(image=image.fluent(\uE94F,segoe_icon_size) tip=['CalculatorBackspace',tip.info] cmd=command.copy('image.fluent(\uE94F,@segoe_icon_size)'))
	    item(image=image.fluent(\uE950,segoe_icon_size) tip=['Component',tip.info] cmd=command.copy('image.fluent(\uE950,@segoe_icon_size)'))
	    item(image=image.fluent(\uE951,segoe_icon_size) tip=['DMC',tip.info] cmd=command.copy('image.fluent(\uE951,@segoe_icon_size)'))
	    item(image=image.fluent(\uE952,segoe_icon_size) tip=['Dock',tip.info] cmd=command.copy('image.fluent(\uE952,@segoe_icon_size)'))
	    item(image=image.fluent(\uE953,segoe_icon_size) tip=['MultimediaDMS',tip.info] cmd=command.copy('image.fluent(\uE953,@segoe_icon_size)'))
	    item(image=image.fluent(\uE954,segoe_icon_size) tip=['MultimediaDVR',tip.info] cmd=command.copy('image.fluent(\uE954,@segoe_icon_size)'))
	    item(image=image.fluent(\uE955,segoe_icon_size) tip=['MultimediaPMP',tip.info] cmd=command.copy('image.fluent(\uE955,@segoe_icon_size)'))
	    item(image=image.fluent(\uE956,segoe_icon_size) tip=['PrintfaxPrinterFile',tip.info] cmd=command.copy('image.fluent(\uE956,@segoe_icon_size)'))
	    item(image=image.fluent(\uE957,segoe_icon_size) tip=['Sensor',tip.info] cmd=command.copy('image.fluent(\uE957,@segoe_icon_size)'))
	    item(image=image.fluent(\uE958,segoe_icon_size) tip=['StorageOptical',tip.info] cmd=command.copy('image.fluent(\uE958,@segoe_icon_size)'))
	    item(image=image.fluent(\uE95A,segoe_icon_size) tip=['Communications',tip.info] cmd=command.copy('image.fluent(\uE95A,@segoe_icon_size)'))

	    item(image=image.fluent(\uE95B,segoe_icon_size) tip=['Headset',tip.info] cmd=command.copy('image.fluent(\uE95B,@segoe_icon_size)') col)
	    item(image=image.fluent(\uE95D,segoe_icon_size) tip=['Projector',tip.info] cmd=command.copy('image.fluent(\uE95D,@segoe_icon_size)'))
	    item(image=image.fluent(\uE95E,segoe_icon_size) tip=['Health',tip.info] cmd=command.copy('image.fluent(\uE95E,@segoe_icon_size)'))
	    item(image=image.fluent(\uE95F,segoe_icon_size) tip=['Wire',tip.info] cmd=command.copy('image.fluent(\uE95F,@segoe_icon_size)'))
	    item(image=image.fluent(\uE960,segoe_icon_size) tip=['Webcam2',tip.info] cmd=command.copy('image.fluent(\uE960,@segoe_icon_size)'))
	    item(image=image.fluent(\uE961,segoe_icon_size) tip=['Input',tip.info] cmd=command.copy('image.fluent(\uE961,@segoe_icon_size)'))
	    item(image=image.fluent(\uE962,segoe_icon_size) tip=['Mouse',tip.info] cmd=command.copy('image.fluent(\uE962,@segoe_icon_size)'))
	    item(image=image.fluent(\uE963,segoe_icon_size) tip=['Smartcard',tip.info] cmd=command.copy('image.fluent(\uE963,@segoe_icon_size)'))
	    item(image=image.fluent(\uE964,segoe_icon_size) tip=['SmartcardVirtual',tip.info] cmd=command.copy('image.fluent(\uE964,@segoe_icon_size)'))
	    item(image=image.fluent(\uE965,segoe_icon_size) tip=['MediaStorageTower',tip.info] cmd=command.copy('image.fluent(\uE965,@segoe_icon_size)'))
	    item(image=image.fluent(\uE966,segoe_icon_size) tip=['ReturnKeySm',tip.info] cmd=command.copy('image.fluent(\uE966,@segoe_icon_size)'))
	    item(image=image.fluent(\uE967,segoe_icon_size) tip=['GameConsole',tip.info] cmd=command.copy('image.fluent(\uE967,@segoe_icon_size)'))
	    item(image=image.fluent(\uE968,segoe_icon_size) tip=['Network',tip.info] cmd=command.copy('image.fluent(\uE968,@segoe_icon_size)'))
	    item(image=image.fluent(\uE969,segoe_icon_size) tip=['StorageNetworkWireless',tip.info] cmd=command.copy('image.fluent(\uE969,@segoe_icon_size)'))
	    item(image=image.fluent(\uE96A,segoe_icon_size) tip=['StorageTape',tip.info] cmd=command.copy('image.fluent(\uE96A,@segoe_icon_size)'))
	    item(image=image.fluent(\uE96D,segoe_icon_size) tip=['ChevronUpSmall',tip.info] cmd=command.copy('image.fluent(\uE96D,@segoe_icon_size)'))
	}

	menu(title='Fluent #3')
	{
	    item(image=image.fluent(\uE96E,segoe_icon_size) tip=['ChevronDownSmall',tip.info] cmd=command.copy('image.fluent(\uE96E,@segoe_icon_size)'))
	    item(image=image.fluent(\uE96F,segoe_icon_size) tip=['ChevronLeftSmall',tip.info] cmd=command.copy('image.fluent(\uE96F,@segoe_icon_size)'))
	    item(image=image.fluent(\uE970,segoe_icon_size) tip=['ChevronRightSmall',tip.info] cmd=command.copy('image.fluent(\uE970,@segoe_icon_size)'))
	    item(image=image.fluent(\uE971,segoe_icon_size) tip=['ChevronUpMed',tip.info] cmd=command.copy('image.fluent(\uE971,@segoe_icon_size)'))
	    item(image=image.fluent(\uE972,segoe_icon_size) tip=['ChevronDownMed',tip.info] cmd=command.copy('image.fluent(\uE972,@segoe_icon_size)'))
	    item(image=image.fluent(\uE973,segoe_icon_size) tip=['ChevronLeftMed',tip.info] cmd=command.copy('image.fluent(\uE973,@segoe_icon_size)'))
	    item(image=image.fluent(\uE974,segoe_icon_size) tip=['ChevronRightMed',tip.info] cmd=command.copy('image.fluent(\uE974,@segoe_icon_size)'))
	    item(image=image.fluent(\uE975,segoe_icon_size) tip=['Devices2',tip.info] cmd=command.copy('image.fluent(\uE975,@segoe_icon_size)'))
	    item(image=image.fluent(\uE976,segoe_icon_size) tip=['ExpandTile',tip.info] cmd=command.copy('image.fluent(\uE976,@segoe_icon_size)'))
	    item(image=image.fluent(\uE977,segoe_icon_size) tip=['PC1',tip.info] cmd=command.copy('image.fluent(\uE977,@segoe_icon_size)'))
	    item(image=image.fluent(\uE978,segoe_icon_size) tip=['PresenceChicklet',tip.info] cmd=command.copy('image.fluent(\uE978,@segoe_icon_size)'))
	    item(image=image.fluent(\uE979,segoe_icon_size) tip=['PresenceChickletVideo',tip.info] cmd=command.copy('image.fluent(\uE979,@segoe_icon_size)'))
	    item(image=image.fluent(\uE97A,segoe_icon_size) tip=['Reply',tip.info] cmd=command.copy('image.fluent(\uE97A,@segoe_icon_size)'))
	    item(image=image.fluent(\uE97B,segoe_icon_size) tip=['SetTile',tip.info] cmd=command.copy('image.fluent(\uE97B,@segoe_icon_size)'))
	    item(image=image.fluent(\uE97C,segoe_icon_size) tip=['Type',tip.info] cmd=command.copy('image.fluent(\uE97C,@segoe_icon_size)'))
	    item(image=image.fluent(\uE97D,segoe_icon_size) tip=['Korean',tip.info] cmd=command.copy('image.fluent(\uE97D,@segoe_icon_size)'))

	    item(image=image.fluent(\uE97E,segoe_icon_size) tip=['HalfAlpha',tip.info] cmd=command.copy('image.fluent(\uE97E,@segoe_icon_size)') col)
	    item(image=image.fluent(\uE97F,segoe_icon_size) tip=['FullAlpha',tip.info] cmd=command.copy('image.fluent(\uE97F,@segoe_icon_size)'))
	    item(image=image.fluent(\uE980,segoe_icon_size) tip=['Key12On',tip.info] cmd=command.copy('image.fluent(\uE980,@segoe_icon_size)'))
	    item(image=image.fluent(\uE981,segoe_icon_size) tip=['ChineseChangjie',tip.info] cmd=command.copy('image.fluent(\uE981,@segoe_icon_size)'))
	    item(image=image.fluent(\uE982,segoe_icon_size) tip=['QWERTYOn',tip.info] cmd=command.copy('image.fluent(\uE982,@segoe_icon_size)'))
	    item(image=image.fluent(\uE983,segoe_icon_size) tip=['QWERTYOff',tip.info] cmd=command.copy('image.fluent(\uE983,@segoe_icon_size)'))
	    item(image=image.fluent(\uE984,segoe_icon_size) tip=['ChineseQuick',tip.info] cmd=command.copy('image.fluent(\uE984,@segoe_icon_size)'))
	    item(image=image.fluent(\uE985,segoe_icon_size) tip=['Japanese',tip.info] cmd=command.copy('image.fluent(\uE985,@segoe_icon_size)'))
	    item(image=image.fluent(\uE986,segoe_icon_size) tip=['FullHiragana',tip.info] cmd=command.copy('image.fluent(\uE986,@segoe_icon_size)'))
	    item(image=image.fluent(\uE987,segoe_icon_size) tip=['FullKatakana',tip.info] cmd=command.copy('image.fluent(\uE987,@segoe_icon_size)'))
	    item(image=image.fluent(\uE988,segoe_icon_size) tip=['HalfKatakana',tip.info] cmd=command.copy('image.fluent(\uE988,@segoe_icon_size)'))
	    item(image=image.fluent(\uE989,segoe_icon_size) tip=['ChineseBoPoMoFo',tip.info] cmd=command.copy('image.fluent(\uE989,@segoe_icon_size)'))
	    item(image=image.fluent(\uE98A,segoe_icon_size) tip=['ChinesePinyin',tip.info] cmd=command.copy('image.fluent(\uE98A,@segoe_icon_size)'))
	    item(image=image.fluent(\uE98F,segoe_icon_size) tip=['ConstructionCone',tip.info] cmd=command.copy('image.fluent(\uE98F,@segoe_icon_size)'))
	    item(image=image.fluent(\uE990,segoe_icon_size) tip=['XboxOneConsole',tip.info] cmd=command.copy('image.fluent(\uE990,@segoe_icon_size)'))
	    item(image=image.fluent(\uE992,segoe_icon_size) tip=['Volume0',tip.info] cmd=command.copy('image.fluent(\uE992,@segoe_icon_size)'))

	    item(image=image.fluent(\uE993,segoe_icon_size) tip=['Volume1',tip.info] cmd=command.copy('image.fluent(\uE993,@segoe_icon_size)') col)
	    item(image=image.fluent(\uE994,segoe_icon_size) tip=['Volume2',tip.info] cmd=command.copy('image.fluent(\uE994,@segoe_icon_size)'))
	    item(image=image.fluent(\uE995,segoe_icon_size) tip=['Volume3',tip.info] cmd=command.copy('image.fluent(\uE995,@segoe_icon_size)'))
	    item(image=image.fluent(\uE996,segoe_icon_size) tip=['BatteryUnknown',tip.info] cmd=command.copy('image.fluent(\uE996,@segoe_icon_size)'))
	    item(image=image.fluent(\uE998,segoe_icon_size) tip=['WifiAttentionOverlay',tip.info] cmd=command.copy('image.fluent(\uE998,@segoe_icon_size)'))
	    item(image=image.fluent(\uE99A,segoe_icon_size) tip=['Robot',tip.info] cmd=command.copy('image.fluent(\uE99A,@segoe_icon_size)'))
	    item(image=image.fluent(\uE9A1,segoe_icon_size) tip=['TapAndSend',tip.info] cmd=command.copy('image.fluent(\uE9A1,@segoe_icon_size)'))
	    item(image=image.fluent(\uE9A6,segoe_icon_size) tip=['FitPage',tip.info] cmd=command.copy('image.fluent(\uE9A6,@segoe_icon_size)'))
	    item(image=image.fluent(\uE9A8,segoe_icon_size) tip=['PasswordKeyShow',tip.info] cmd=command.copy('image.fluent(\uE9A8,@segoe_icon_size)'))
	    item(image=image.fluent(\uE9A9,segoe_icon_size) tip=['PasswordKeyHide',tip.info] cmd=command.copy('image.fluent(\uE9A9,@segoe_icon_size)'))
	    item(image=image.fluent(\uE9AA,segoe_icon_size) tip=['BidiLtr',tip.info] cmd=command.copy('image.fluent(\uE9AA,@segoe_icon_size)'))
	    item(image=image.fluent(\uE9AB,segoe_icon_size) tip=['BidiRtl',tip.info] cmd=command.copy('image.fluent(\uE9AB,@segoe_icon_size)'))
	    item(image=image.fluent(\uE9AC,segoe_icon_size) tip=['ForwardSm',tip.info] cmd=command.copy('image.fluent(\uE9AC,@segoe_icon_size)'))
	    item(image=image.fluent(\uE9AD,segoe_icon_size) tip=['CommaKey',tip.info] cmd=command.copy('image.fluent(\uE9AD,@segoe_icon_size)'))
	    item(image=image.fluent(\uE9AE,segoe_icon_size) tip=['DashKey',tip.info] cmd=command.copy('image.fluent(\uE9AE,@segoe_icon_size)'))
	    item(image=image.fluent(\uE9AF,segoe_icon_size) tip=['DullSoundKey',tip.info] cmd=command.copy('image.fluent(\uE9AF,@segoe_icon_size)'))

	    item(image=image.fluent(\uE9B0,segoe_icon_size) tip=['HalfDullSound',tip.info] cmd=command.copy('image.fluent(\uE9B0,@segoe_icon_size)') col)
	    item(image=image.fluent(\uE9B1,segoe_icon_size) tip=['RightDoubleQuote',tip.info] cmd=command.copy('image.fluent(\uE9B1,@segoe_icon_size)'))
	    item(image=image.fluent(\uE9B2,segoe_icon_size) tip=['LeftDoubleQuote',tip.info] cmd=command.copy('image.fluent(\uE9B2,@segoe_icon_size)'))
	    item(image=image.fluent(\uE9B3,segoe_icon_size) tip=['PuncKeyRightBottom',tip.info] cmd=command.copy('image.fluent(\uE9B3,@segoe_icon_size)'))
	    item(image=image.fluent(\uE9B4,segoe_icon_size) tip=['PuncKey1',tip.info] cmd=command.copy('image.fluent(\uE9B4,@segoe_icon_size)'))
	    item(image=image.fluent(\uE9B5,segoe_icon_size) tip=['PuncKey2',tip.info] cmd=command.copy('image.fluent(\uE9B5,@segoe_icon_size)'))
	    item(image=image.fluent(\uE9B6,segoe_icon_size) tip=['PuncKey3',tip.info] cmd=command.copy('image.fluent(\uE9B6,@segoe_icon_size)'))
	    item(image=image.fluent(\uE9B7,segoe_icon_size) tip=['PuncKey4',tip.info] cmd=command.copy('image.fluent(\uE9B7,@segoe_icon_size)'))
	    item(image=image.fluent(\uE9B8,segoe_icon_size) tip=['PuncKey5',tip.info] cmd=command.copy('image.fluent(\uE9B8,@segoe_icon_size)'))
	    item(image=image.fluent(\uE9B9,segoe_icon_size) tip=['PuncKey6',tip.info] cmd=command.copy('image.fluent(\uE9B9,@segoe_icon_size)'))
	    item(image=image.fluent(\uE9BA,segoe_icon_size) tip=['PuncKey9',tip.info] cmd=command.copy('image.fluent(\uE9BA,@segoe_icon_size)'))
	    item(image=image.fluent(\uE9BB,segoe_icon_size) tip=['PuncKey7',tip.info] cmd=command.copy('image.fluent(\uE9BB,@segoe_icon_size)'))
	    item(image=image.fluent(\uE9BC,segoe_icon_size) tip=['PuncKey8',tip.info] cmd=command.copy('image.fluent(\uE9BC,@segoe_icon_size)'))
	    item(image=image.fluent(\uE9CA,segoe_icon_size) tip=['Frigid',tip.info] cmd=command.copy('image.fluent(\uE9CA,@segoe_icon_size)'))
	    item(image=image.fluent(\uE9CE,segoe_icon_size) tip=['Unknown',tip.info] cmd=command.copy('image.fluent(\uE9CE,@segoe_icon_size)'))
	    item(image=image.fluent(\uE9D2,segoe_icon_size) tip=['AreaChart',tip.info] cmd=command.copy('image.fluent(\uE9D2,@segoe_icon_size)'))

	    item(image=image.fluent(\uE9D5,segoe_icon_size) tip=['CheckList',tip.info] cmd=command.copy('image.fluent(\uE9D5,@segoe_icon_size)') col)
	    item(image=image.fluent(\uE9D9,segoe_icon_size) tip=['Diagnostic',tip.info] cmd=command.copy('image.fluent(\uE9D9,@segoe_icon_size)'))
	    item(image=image.fluent(\uE9E9,segoe_icon_size) tip=['Equalizer',tip.info] cmd=command.copy('image.fluent(\uE9E9,@segoe_icon_size)'))
	    item(image=image.fluent(\uE9F3,segoe_icon_size) tip=['Process',tip.info] cmd=command.copy('image.fluent(\uE9F3,@segoe_icon_size)'))
	    item(image=image.fluent(\uE9F5,segoe_icon_size) tip=['Processing',tip.info] cmd=command.copy('image.fluent(\uE9F5,@segoe_icon_size)'))
	    item(image=image.fluent(\uE9F9,segoe_icon_size) tip=['ReportDocument',tip.info] cmd=command.copy('image.fluent(\uE9F9,@segoe_icon_size)'))
	    item(image=image.fluent(\uEA0C,segoe_icon_size) tip=['VideoSolid',tip.info] cmd=command.copy('image.fluent(\uEA0C,@segoe_icon_size)'))
	    item(image=image.fluent(\uEA0D,segoe_icon_size) tip=['MixedMediaBadge',tip.info] cmd=command.copy('image.fluent(\uEA0D,@segoe_icon_size)'))
	    item(image=image.fluent(\uEA14,segoe_icon_size) tip=['DisconnectDisplay',tip.info] cmd=command.copy('image.fluent(\uEA14,@segoe_icon_size)'))
	    item(image=image.fluent(\uEA18,segoe_icon_size) tip=['Shield',tip.info] cmd=command.copy('image.fluent(\uEA18,@segoe_icon_size)'))
	    item(image=image.fluent(\uEA1F,segoe_icon_size) tip=['Info2',tip.info] cmd=command.copy('image.fluent(\uEA1F,@segoe_icon_size)'))
	    item(image=image.fluent(\uEA21,segoe_icon_size) tip=['ActionCenterAsterisk',tip.info] cmd=command.copy('image.fluent(\uEA21,@segoe_icon_size)'))
	    item(image=image.fluent(\uEA24,segoe_icon_size) tip=['Beta',tip.info] cmd=command.copy('image.fluent(\uEA24,@segoe_icon_size)'))
	    item(image=image.fluent(\uEA35,segoe_icon_size) tip=['SaveCopy',tip.info] cmd=command.copy('image.fluent(\uEA35,@segoe_icon_size)'))
	    item(image=image.fluent(\uEA37,segoe_icon_size) tip=['List',tip.info] cmd=command.copy('image.fluent(\uEA37,@segoe_icon_size)'))
	    item(image=image.fluent(\uEA38,segoe_icon_size) tip=['Asterisk',tip.info] cmd=command.copy('image.fluent(\uEA38,@segoe_icon_size)'))

	    item(image=image.fluent(\uEA39,segoe_icon_size) tip=['ErrorBadge',tip.info] cmd=command.copy('image.fluent(\uEA39,@segoe_icon_size)') col)
	    item(image=image.fluent(\uEA3A,segoe_icon_size) tip=['CircleRing',tip.info] cmd=command.copy('image.fluent(\uEA3A,@segoe_icon_size)'))
	    item(image=image.fluent(\uEA3B,segoe_icon_size) tip=['CircleFill',tip.info] cmd=command.copy('image.fluent(\uEA3B,@segoe_icon_size)'))
	    item(image=image.fluent(\uEA3C,segoe_icon_size) tip=['MergeCall',tip.info] cmd=command.copy('image.fluent(\uEA3C,@segoe_icon_size)'))
	    item(image=image.fluent(\uEA3D,segoe_icon_size) tip=['PrivateCall',tip.info] cmd=command.copy('image.fluent(\uEA3D,@segoe_icon_size)'))
	    item(image=image.fluent(\uEA3F,segoe_icon_size) tip=['Record2',tip.info] cmd=command.copy('image.fluent(\uEA3F,@segoe_icon_size)'))
	    item(image=image.fluent(\uEA40,segoe_icon_size) tip=['AllAppsMirrored',tip.info] cmd=command.copy('image.fluent(\uEA40,@segoe_icon_size)'))
	    item(image=image.fluent(\uEA41,segoe_icon_size) tip=['BookmarksMirrored',tip.info] cmd=command.copy('image.fluent(\uEA41,@segoe_icon_size)'))
	    item(image=image.fluent(\uEA42,segoe_icon_size) tip=['BulletedListMirrored',tip.info] cmd=command.copy('image.fluent(\uEA42,@segoe_icon_size)'))
	    item(image=image.fluent(\uEA43,segoe_icon_size) tip=['CallForwardInternationalMirrored',tip.info] cmd=command.copy('image.fluent(\uEA43,@segoe_icon_size)'))
	    item(image=image.fluent(\uEA44,segoe_icon_size) tip=['CallForwardRoamingMirrored',tip.info] cmd=command.copy('image.fluent(\uEA44,@segoe_icon_size)'))
	    item(image=image.fluent(\uEA47,segoe_icon_size) tip=['ChromeBackMirrored',tip.info] cmd=command.copy('image.fluent(\uEA47,@segoe_icon_size)'))
	    item(image=image.fluent(\uEA48,segoe_icon_size) tip=['ClearSelectionMirrored',tip.info] cmd=command.copy('image.fluent(\uEA48,@segoe_icon_size)'))
	    item(image=image.fluent(\uEA49,segoe_icon_size) tip=['ClosePaneMirrored',tip.info] cmd=command.copy('image.fluent(\uEA49,@segoe_icon_size)'))
	    item(image=image.fluent(\uEA4A,segoe_icon_size) tip=['ContactInfoMirrored',tip.info] cmd=command.copy('image.fluent(\uEA4A,@segoe_icon_size)'))
	    item(image=image.fluent(\uEA4B,segoe_icon_size) tip=['DockRightMirrored',tip.info] cmd=command.copy('image.fluent(\uEA4B,@segoe_icon_size)'))

	    item(image=image.fluent(\uEA4C,segoe_icon_size) tip=['DockLeftMirrored',tip.info] cmd=command.copy('image.fluent(\uEA4C,@segoe_icon_size)') col)
	    item(image=image.fluent(\uEA4E,segoe_icon_size) tip=['ExpandTileMirrored',tip.info] cmd=command.copy('image.fluent(\uEA4E,@segoe_icon_size)'))
	    item(image=image.fluent(\uEA4F,segoe_icon_size) tip=['GoMirrored',tip.info] cmd=command.copy('image.fluent(\uEA4F,@segoe_icon_size)'))
	    item(image=image.fluent(\uEA50,segoe_icon_size) tip=['GripperResizeMirrored',tip.info] cmd=command.copy('image.fluent(\uEA50,@segoe_icon_size)'))
	    item(image=image.fluent(\uEA51,segoe_icon_size) tip=['HelpMirrored',tip.info] cmd=command.copy('image.fluent(\uEA51,@segoe_icon_size)'))
	    item(image=image.fluent(\uEA52,segoe_icon_size) tip=['ImportMirrored',tip.info] cmd=command.copy('image.fluent(\uEA52,@segoe_icon_size)'))
	    item(image=image.fluent(\uEA53,segoe_icon_size) tip=['ImportAllMirrored',tip.info] cmd=command.copy('image.fluent(\uEA53,@segoe_icon_size)'))
	    item(image=image.fluent(\uEA54,segoe_icon_size) tip=['LeaveChatMirrored',tip.info] cmd=command.copy('image.fluent(\uEA54,@segoe_icon_size)'))
	    item(image=image.fluent(\uEA55,segoe_icon_size) tip=['ListMirrored',tip.info] cmd=command.copy('image.fluent(\uEA55,@segoe_icon_size)'))
	    item(image=image.fluent(\uEA56,segoe_icon_size) tip=['MailForwardMirrored',tip.info] cmd=command.copy('image.fluent(\uEA56,@segoe_icon_size)'))
	    item(image=image.fluent(\uEA57,segoe_icon_size) tip=['MailReplyMirrored',tip.info] cmd=command.copy('image.fluent(\uEA57,@segoe_icon_size)'))
	    item(image=image.fluent(\uEA58,segoe_icon_size) tip=['MailReplyAllMirrored',tip.info] cmd=command.copy('image.fluent(\uEA58,@segoe_icon_size)'))
	    item(image=image.fluent(\uEA5B,segoe_icon_size) tip=['OpenPaneMirrored',tip.info] cmd=command.copy('image.fluent(\uEA5B,@segoe_icon_size)'))
	    item(image=image.fluent(\uEA5C,segoe_icon_size) tip=['OpenWithMirrored',tip.info] cmd=command.copy('image.fluent(\uEA5C,@segoe_icon_size)'))
	    item(image=image.fluent(\uEA5E,segoe_icon_size) tip=['ParkingLocationMirrored',tip.info] cmd=command.copy('image.fluent(\uEA5E,@segoe_icon_size)'))
	    item(image=image.fluent(\uEA5F,segoe_icon_size) tip=['ResizeMouseMediumMirrored',tip.info] cmd=command.copy('image.fluent(\uEA5F,@segoe_icon_size)'))

	    item(image=image.fluent(\uEA60,segoe_icon_size) tip=['ResizeMouseSmallMirrored',tip.info] cmd=command.copy('image.fluent(\uEA60,@segoe_icon_size)') col)
	    item(image=image.fluent(\uEA61,segoe_icon_size) tip=['ResizeMouseTallMirrored',tip.info] cmd=command.copy('image.fluent(\uEA61,@segoe_icon_size)'))
	    item(image=image.fluent(\uEA62,segoe_icon_size) tip=['ResizeTouchNarrowerMirrored',tip.info] cmd=command.copy('image.fluent(\uEA62,@segoe_icon_size)'))
	    item(image=image.fluent(\uEA63,segoe_icon_size) tip=['SendMirrored',tip.info] cmd=command.copy('image.fluent(\uEA63,@segoe_icon_size)'))
	    item(image=image.fluent(\uEA64,segoe_icon_size) tip=['SendFillMirrored',tip.info] cmd=command.copy('image.fluent(\uEA64,@segoe_icon_size)'))
	    item(image=image.fluent(\uEA65,segoe_icon_size) tip=['ShowResultsMirrored',tip.info] cmd=command.copy('image.fluent(\uEA65,@segoe_icon_size)'))
	    item(image=image.fluent(\uEA69,segoe_icon_size) tip=['Media',tip.info] cmd=command.copy('image.fluent(\uEA69,@segoe_icon_size)'))
	    item(image=image.fluent(\uEA6A,segoe_icon_size) tip=['SyncError',tip.info] cmd=command.copy('image.fluent(\uEA6A,@segoe_icon_size)'))
	    item(image=image.fluent(\uEA6C,segoe_icon_size) tip=['Devices3',tip.info] cmd=command.copy('image.fluent(\uEA6C,@segoe_icon_size)'))
	    item(image=image.fluent(\uEA79,segoe_icon_size) tip=['SlowMotionOn',tip.info] cmd=command.copy('image.fluent(\uEA79,@segoe_icon_size)'))
	    item(image=image.fluent(\uEA80,segoe_icon_size) tip=['Lightbulb',tip.info] cmd=command.copy('image.fluent(\uEA80,@segoe_icon_size)'))
	    item(image=image.fluent(\uEA81,segoe_icon_size) tip=['StatusCircle',tip.info] cmd=command.copy('image.fluent(\uEA81,@segoe_icon_size)'))
	    item(image=image.fluent(\uEA82,segoe_icon_size) tip=['StatusTriangle',tip.info] cmd=command.copy('image.fluent(\uEA82,@segoe_icon_size)'))
	    item(image=image.fluent(\uEA83,segoe_icon_size) tip=['StatusError',tip.info] cmd=command.copy('image.fluent(\uEA83,@segoe_icon_size)'))
	    item(image=image.fluent(\uEA84,segoe_icon_size) tip=['StatusWarning',tip.info] cmd=command.copy('image.fluent(\uEA84,@segoe_icon_size)'))
	    item(image=image.fluent(\uEA86,segoe_icon_size) tip=['Puzzle',tip.info] cmd=command.copy('image.fluent(\uEA86,@segoe_icon_size)'))

	    item(image=image.fluent(\uEA89,segoe_icon_size) tip=['CalendarSolid',tip.info] cmd=command.copy('image.fluent(\uEA89,@segoe_icon_size)') col)
	    item(image=image.fluent(\uEA8A,segoe_icon_size) tip=['HomeSolid',tip.info] cmd=command.copy('image.fluent(\uEA8A,@segoe_icon_size)'))
	    item(image=image.fluent(\uEA8B,segoe_icon_size) tip=['ParkingLocationSolid',tip.info] cmd=command.copy('image.fluent(\uEA8B,@segoe_icon_size)'))
	    item(image=image.fluent(\uEA8C,segoe_icon_size) tip=['ContactSolid',tip.info] cmd=command.copy('image.fluent(\uEA8C,@segoe_icon_size)'))
	    item(image=image.fluent(\uEA8D,segoe_icon_size) tip=['ConstructionSolid',tip.info] cmd=command.copy('image.fluent(\uEA8D,@segoe_icon_size)'))
	    item(image=image.fluent(\uEA8E,segoe_icon_size) tip=['AccidentSolid',tip.info] cmd=command.copy('image.fluent(\uEA8E,@segoe_icon_size)'))
	    item(image=image.fluent(\uEA8F,segoe_icon_size) tip=['Ringer',tip.info] cmd=command.copy('image.fluent(\uEA8F,@segoe_icon_size)'))
	    item(image=image.fluent(\uEA90,segoe_icon_size) tip=['PDF',tip.info] cmd=command.copy('image.fluent(\uEA90,@segoe_icon_size)'))
	    item(image=image.fluent(\uEA91,segoe_icon_size) tip=['ThoughtBubble',tip.info] cmd=command.copy('image.fluent(\uEA91,@segoe_icon_size)'))
	    item(image=image.fluent(\uEA92,segoe_icon_size) tip=['HeartBroken',tip.info] cmd=command.copy('image.fluent(\uEA92,@segoe_icon_size)'))
	    item(image=image.fluent(\uEA93,segoe_icon_size) tip=['BatteryCharging10',tip.info] cmd=command.copy('image.fluent(\uEA93,@segoe_icon_size)'))
	    item(image=image.fluent(\uEA94,segoe_icon_size) tip=['BatterySaver9',tip.info] cmd=command.copy('image.fluent(\uEA94,@segoe_icon_size)'))
	    item(image=image.fluent(\uEA95,segoe_icon_size) tip=['BatterySaver10',tip.info] cmd=command.copy('image.fluent(\uEA95,@segoe_icon_size)'))
	    item(image=image.fluent(\uEA97,segoe_icon_size) tip=['CallForwardingMirrored',tip.info] cmd=command.copy('image.fluent(\uEA97,@segoe_icon_size)'))
	    item(image=image.fluent(\uEA98,segoe_icon_size) tip=['MultiSelectMirrored',tip.info] cmd=command.copy('image.fluent(\uEA98,@segoe_icon_size)'))
	    item(image=image.fluent(\uEA99,segoe_icon_size) tip=['Broom',tip.info] cmd=command.copy('image.fluent(\uEA99,@segoe_icon_size)'))

	    item(image=image.fluent(\uEAC2,segoe_icon_size) tip=['ForwardCall',tip.info] cmd=command.copy('image.fluent(\uEAC2,@segoe_icon_size)') col)
	    item(image=image.fluent(\uEADF,segoe_icon_size) tip=['Trackers',tip.info] cmd=command.copy('image.fluent(\uEADF,@segoe_icon_size)'))
	    item(image=image.fluent(\uEAFC,segoe_icon_size) tip=['Market',tip.info] cmd=command.copy('image.fluent(\uEAFC,@segoe_icon_size)'))
	    item(image=image.fluent(\uEB05,segoe_icon_size) tip=['PieSingle',tip.info] cmd=command.copy('image.fluent(\uEB05,@segoe_icon_size)'))
	    item(image=image.fluent(\uEB0F,segoe_icon_size) tip=['StockUp',tip.info] cmd=command.copy('image.fluent(\uEB0F,@segoe_icon_size)'))
	    item(image=image.fluent(\uEB11,segoe_icon_size) tip=['StockDown',tip.info] cmd=command.copy('image.fluent(\uEB11,@segoe_icon_size)'))
	    item(image=image.fluent(\uEB3C,segoe_icon_size) tip=['Design',tip.info] cmd=command.copy('image.fluent(\uEB3C,@segoe_icon_size)'))
	    item(image=image.fluent(\uEB41,segoe_icon_size) tip=['Website',tip.info] cmd=command.copy('image.fluent(\uEB41,@segoe_icon_size)'))
	    item(image=image.fluent(\uEB42,segoe_icon_size) tip=['Drop',tip.info] cmd=command.copy('image.fluent(\uEB42,@segoe_icon_size)'))
	    item(image=image.fluent(\uEB44,segoe_icon_size) tip=['Radar',tip.info] cmd=command.copy('image.fluent(\uEB44,@segoe_icon_size)'))
	    item(image=image.fluent(\uEB47,segoe_icon_size) tip=['BusSolid',tip.info] cmd=command.copy('image.fluent(\uEB47,@segoe_icon_size)'))
	    item(image=image.fluent(\uEB48,segoe_icon_size) tip=['FerrySolid',tip.info] cmd=command.copy('image.fluent(\uEB48,@segoe_icon_size)'))
	    item(image=image.fluent(\uEB49,segoe_icon_size) tip=['StartPointSolid',tip.info] cmd=command.copy('image.fluent(\uEB49,@segoe_icon_size)'))
	    item(image=image.fluent(\uEB4A,segoe_icon_size) tip=['StopPointSolid',tip.info] cmd=command.copy('image.fluent(\uEB4A,@segoe_icon_size)'))
	    item(image=image.fluent(\uEB4B,segoe_icon_size) tip=['EndPointSolid',tip.info] cmd=command.copy('image.fluent(\uEB4B,@segoe_icon_size)'))
	    item(image=image.fluent(\uEB4C,segoe_icon_size) tip=['AirplaneSolid',tip.info] cmd=command.copy('image.fluent(\uEB4C,@segoe_icon_size)'))

	    item(image=image.fluent(\uEB4D,segoe_icon_size) tip=['TrainSolid',tip.info] cmd=command.copy('image.fluent(\uEB4D,@segoe_icon_size)') col)
	    item(image=image.fluent(\uEB4E,segoe_icon_size) tip=['WorkSolid',tip.info] cmd=command.copy('image.fluent(\uEB4E,@segoe_icon_size)'))
	    item(image=image.fluent(\uEB4F,segoe_icon_size) tip=['ReminderFill',tip.info] cmd=command.copy('image.fluent(\uEB4F,@segoe_icon_size)'))
	    item(image=image.fluent(\uEB50,segoe_icon_size) tip=['Reminder',tip.info] cmd=command.copy('image.fluent(\uEB50,@segoe_icon_size)'))
	    item(image=image.fluent(\uEB51,segoe_icon_size) tip=['Heart',tip.info] cmd=command.copy('image.fluent(\uEB51,@segoe_icon_size)'))
	    item(image=image.fluent(\uEB52,segoe_icon_size) tip=['HeartFill',tip.info] cmd=command.copy('image.fluent(\uEB52,@segoe_icon_size)'))
	    item(image=image.fluent(\uEB55,segoe_icon_size) tip=['EthernetError',tip.info] cmd=command.copy('image.fluent(\uEB55,@segoe_icon_size)'))
	    item(image=image.fluent(\uEB56,segoe_icon_size) tip=['EthernetWarning',tip.info] cmd=command.copy('image.fluent(\uEB56,@segoe_icon_size)'))
	    item(image=image.fluent(\uEB57,segoe_icon_size) tip=['StatusConnecting1',tip.info] cmd=command.copy('image.fluent(\uEB57,@segoe_icon_size)'))
	    item(image=image.fluent(\uEB58,segoe_icon_size) tip=['StatusConnecting2',tip.info] cmd=command.copy('image.fluent(\uEB58,@segoe_icon_size)'))
	    item(image=image.fluent(\uEB59,segoe_icon_size) tip=['StatusUnsecure',tip.info] cmd=command.copy('image.fluent(\uEB59,@segoe_icon_size)'))
	    item(image=image.fluent(\uEB5A,segoe_icon_size) tip=['WifiError0',tip.info] cmd=command.copy('image.fluent(\uEB5A,@segoe_icon_size)'))
	    item(image=image.fluent(\uEB5B,segoe_icon_size) tip=['WifiError1',tip.info] cmd=command.copy('image.fluent(\uEB5B,@segoe_icon_size)'))
	    item(image=image.fluent(\uEB5C,segoe_icon_size) tip=['WifiError2',tip.info] cmd=command.copy('image.fluent(\uEB5C,@segoe_icon_size)'))
	    item(image=image.fluent(\uEB5D,segoe_icon_size) tip=['WifiError3',tip.info] cmd=command.copy('image.fluent(\uEB5D,@segoe_icon_size)'))
	    item(image=image.fluent(\uEB5E,segoe_icon_size) tip=['WifiError4',tip.info] cmd=command.copy('image.fluent(\uEB5E,@segoe_icon_size)'))

	    item(image=image.fluent(\uEB5F,segoe_icon_size) tip=['WifiWarning0',tip.info] cmd=command.copy('image.fluent(\uEB5F,@segoe_icon_size)') col)
	    item(image=image.fluent(\uEB60,segoe_icon_size) tip=['WifiWarning1',tip.info] cmd=command.copy('image.fluent(\uEB60,@segoe_icon_size)'))
	    item(image=image.fluent(\uEB61,segoe_icon_size) tip=['WifiWarning2',tip.info] cmd=command.copy('image.fluent(\uEB61,@segoe_icon_size)'))
	    item(image=image.fluent(\uEB62,segoe_icon_size) tip=['WifiWarning3',tip.info] cmd=command.copy('image.fluent(\uEB62,@segoe_icon_size)'))
	    item(image=image.fluent(\uEB63,segoe_icon_size) tip=['WifiWarning4',tip.info] cmd=command.copy('image.fluent(\uEB63,@segoe_icon_size)'))
	    item(image=image.fluent(\uEB66,segoe_icon_size) tip=['Devices4',tip.info] cmd=command.copy('image.fluent(\uEB66,@segoe_icon_size)'))
	    item(image=image.fluent(\uEB67,segoe_icon_size) tip=['NUIIris',tip.info] cmd=command.copy('image.fluent(\uEB67,@segoe_icon_size)'))
	    item(image=image.fluent(\uEB68,segoe_icon_size) tip=['NUIFace',tip.info] cmd=command.copy('image.fluent(\uEB68,@segoe_icon_size)'))
	    item(image=image.fluent(\uEB77,segoe_icon_size) tip=['GatewayRouter',tip.info] cmd=command.copy('image.fluent(\uEB77,@segoe_icon_size)'))
	    item(image=image.fluent(\uEB7E,segoe_icon_size) tip=['EditMirrored',tip.info] cmd=command.copy('image.fluent(\uEB7E,@segoe_icon_size)'))
	    item(image=image.fluent(\uEB82,segoe_icon_size) tip=['NUIFPStartSlideHand',tip.info] cmd=command.copy('image.fluent(\uEB82,@segoe_icon_size)'))
	    item(image=image.fluent(\uEB83,segoe_icon_size) tip=['NUIFPStartSlideAction',tip.info] cmd=command.copy('image.fluent(\uEB83,@segoe_icon_size)'))
	    item(image=image.fluent(\uEB84,segoe_icon_size) tip=['NUIFPContinueSlideHand',tip.info] cmd=command.copy('image.fluent(\uEB84,@segoe_icon_size)'))
	    item(image=image.fluent(\uEB85,segoe_icon_size) tip=['NUIFPContinueSlideAction',tip.info] cmd=command.copy('image.fluent(\uEB85,@segoe_icon_size)'))
	    item(image=image.fluent(\uEB86,segoe_icon_size) tip=['NUIFPRollRightHand',tip.info] cmd=command.copy('image.fluent(\uEB86,@segoe_icon_size)'))
	    item(image=image.fluent(\uEB87,segoe_icon_size) tip=['NUIFPRollRightHandAction',tip.info] cmd=command.copy('image.fluent(\uEB87,@segoe_icon_size)'))

	    item(image=image.fluent(\uEB88,segoe_icon_size) tip=['NUIFPRollLeftHand',tip.info] cmd=command.copy('image.fluent(\uEB88,@segoe_icon_size)') col)
	    item(image=image.fluent(\uEB89,segoe_icon_size) tip=['NUIFPRollLeftAction',tip.info] cmd=command.copy('image.fluent(\uEB89,@segoe_icon_size)'))
	    item(image=image.fluent(\uEB8A,segoe_icon_size) tip=['NUIFPPressHand',tip.info] cmd=command.copy('image.fluent(\uEB8A,@segoe_icon_size)'))
	    item(image=image.fluent(\uEB8B,segoe_icon_size) tip=['NUIFPPressAction',tip.info] cmd=command.copy('image.fluent(\uEB8B,@segoe_icon_size)'))
	    item(image=image.fluent(\uEB8C,segoe_icon_size) tip=['NUIFPPressRepeatHand',tip.info] cmd=command.copy('image.fluent(\uEB8C,@segoe_icon_size)'))
	    item(image=image.fluent(\uEB8D,segoe_icon_size) tip=['NUIFPPressRepeatAction',tip.info] cmd=command.copy('image.fluent(\uEB8D,@segoe_icon_size)'))
	    item(image=image.fluent(\uEB90,segoe_icon_size) tip=['StatusErrorFull',tip.info] cmd=command.copy('image.fluent(\uEB90,@segoe_icon_size)'))
	    item(image=image.fluent(\uEB91,segoe_icon_size) tip=['TaskViewExpanded',tip.info] cmd=command.copy('image.fluent(\uEB91,@segoe_icon_size)'))
	    item(image=image.fluent(\uEB95,segoe_icon_size) tip=['Certificate',tip.info] cmd=command.copy('image.fluent(\uEB95,@segoe_icon_size)'))
	    item(image=image.fluent(\uEB96,segoe_icon_size) tip=['BackSpaceQWERTYLg',tip.info] cmd=command.copy('image.fluent(\uEB96,@segoe_icon_size)'))
	    item(image=image.fluent(\uEB97,segoe_icon_size) tip=['ReturnKeyLg',tip.info] cmd=command.copy('image.fluent(\uEB97,@segoe_icon_size)'))
	    item(image=image.fluent(\uEB9D,segoe_icon_size) tip=['FastForward',tip.info] cmd=command.copy('image.fluent(\uEB9D,@segoe_icon_size)'))
	    item(image=image.fluent(\uEB9E,segoe_icon_size) tip=['Rewind',tip.info] cmd=command.copy('image.fluent(\uEB9E,@segoe_icon_size)'))
	    item(image=image.fluent(\uEB9F,segoe_icon_size) tip=['Photo2',tip.info] cmd=command.copy('image.fluent(\uEB9F,@segoe_icon_size)'))
	    item(image=image.fluent(\uEBA0,segoe_icon_size) tip=['MobBattery0',tip.info] cmd=command.copy('image.fluent(\uEBA0,@segoe_icon_size)'))
	    item(image=image.fluent(\uEBA1,segoe_icon_size) tip=['MobBattery1',tip.info] cmd=command.copy('image.fluent(\uEBA1,@segoe_icon_size)'))

	    item(image=image.fluent(\uEBA2,segoe_icon_size) tip=['MobBattery2',tip.info] cmd=command.copy('image.fluent(\uEBA2,@segoe_icon_size)') col)
	    item(image=image.fluent(\uEBA3,segoe_icon_size) tip=['MobBattery3',tip.info] cmd=command.copy('image.fluent(\uEBA3,@segoe_icon_size)'))
	    item(image=image.fluent(\uEBA4,segoe_icon_size) tip=['MobBattery4',tip.info] cmd=command.copy('image.fluent(\uEBA4,@segoe_icon_size)'))
	    item(image=image.fluent(\uEBA5,segoe_icon_size) tip=['MobBattery5',tip.info] cmd=command.copy('image.fluent(\uEBA5,@segoe_icon_size)'))
	    item(image=image.fluent(\uEBA6,segoe_icon_size) tip=['MobBattery6',tip.info] cmd=command.copy('image.fluent(\uEBA6,@segoe_icon_size)'))
	    item(image=image.fluent(\uEBA7,segoe_icon_size) tip=['MobBattery7',tip.info] cmd=command.copy('image.fluent(\uEBA7,@segoe_icon_size)'))
	    item(image=image.fluent(\uEBA8,segoe_icon_size) tip=['MobBattery8',tip.info] cmd=command.copy('image.fluent(\uEBA8,@segoe_icon_size)'))
	    item(image=image.fluent(\uEBA9,segoe_icon_size) tip=['MobBattery9',tip.info] cmd=command.copy('image.fluent(\uEBA9,@segoe_icon_size)'))
	    item(image=image.fluent(\uEBAA,segoe_icon_size) tip=['MobBattery10',tip.info] cmd=command.copy('image.fluent(\uEBAA,@segoe_icon_size)'))
	    item(image=image.fluent(\uEBAB,segoe_icon_size) tip=['MobBatteryCharging0',tip.info] cmd=command.copy('image.fluent(\uEBAB,@segoe_icon_size)'))
	    item(image=image.fluent(\uEBAC,segoe_icon_size) tip=['MobBatteryCharging1',tip.info] cmd=command.copy('image.fluent(\uEBAC,@segoe_icon_size)'))
	    item(image=image.fluent(\uEBAD,segoe_icon_size) tip=['MobBatteryCharging2',tip.info] cmd=command.copy('image.fluent(\uEBAD,@segoe_icon_size)'))
	    item(image=image.fluent(\uEBAE,segoe_icon_size) tip=['MobBatteryCharging3',tip.info] cmd=command.copy('image.fluent(\uEBAE,@segoe_icon_size)'))
	    item(image=image.fluent(\uEBAF,segoe_icon_size) tip=['MobBatteryCharging4',tip.info] cmd=command.copy('image.fluent(\uEBAF,@segoe_icon_size)'))
	    item(image=image.fluent(\uEBB0,segoe_icon_size) tip=['MobBatteryCharging5',tip.info] cmd=command.copy('image.fluent(\uEBB0,@segoe_icon_size)'))
	    item(image=image.fluent(\uEBB1,segoe_icon_size) tip=['MobBatteryCharging6',tip.info] cmd=command.copy('image.fluent(\uEBB1,@segoe_icon_size)'))

	    item(image=image.fluent(\uEBB2,segoe_icon_size) tip=['MobBatteryCharging7',tip.info] cmd=command.copy('image.fluent(\uEBB2,@segoe_icon_size)') col)
	    item(image=image.fluent(\uEBB3,segoe_icon_size) tip=['MobBatteryCharging8',tip.info] cmd=command.copy('image.fluent(\uEBB3,@segoe_icon_size)'))
	    item(image=image.fluent(\uEBB4,segoe_icon_size) tip=['MobBatteryCharging9',tip.info] cmd=command.copy('image.fluent(\uEBB4,@segoe_icon_size)'))
	    item(image=image.fluent(\uEBB5,segoe_icon_size) tip=['MobBatteryCharging10',tip.info] cmd=command.copy('image.fluent(\uEBB5,@segoe_icon_size)'))
	    item(image=image.fluent(\uEBB6,segoe_icon_size) tip=['MobBatterySaver0',tip.info] cmd=command.copy('image.fluent(\uEBB6,@segoe_icon_size)'))
	    item(image=image.fluent(\uEBB7,segoe_icon_size) tip=['MobBatterySaver1',tip.info] cmd=command.copy('image.fluent(\uEBB7,@segoe_icon_size)'))
	    item(image=image.fluent(\uEBB8,segoe_icon_size) tip=['MobBatterySaver2',tip.info] cmd=command.copy('image.fluent(\uEBB8,@segoe_icon_size)'))
	    item(image=image.fluent(\uEBB9,segoe_icon_size) tip=['MobBatterySaver3',tip.info] cmd=command.copy('image.fluent(\uEBB9,@segoe_icon_size)'))
	    item(image=image.fluent(\uEBBA,segoe_icon_size) tip=['MobBatterySaver4',tip.info] cmd=command.copy('image.fluent(\uEBBA,@segoe_icon_size)'))
	    item(image=image.fluent(\uEBBB,segoe_icon_size) tip=['MobBatterySaver5',tip.info] cmd=command.copy('image.fluent(\uEBBB,@segoe_icon_size)'))
	    item(image=image.fluent(\uEBBC,segoe_icon_size) tip=['MobBatterySaver6',tip.info] cmd=command.copy('image.fluent(\uEBBC,@segoe_icon_size)'))
	    item(image=image.fluent(\uEBBD,segoe_icon_size) tip=['MobBatterySaver7',tip.info] cmd=command.copy('image.fluent(\uEBBD,@segoe_icon_size)'))
	    item(image=image.fluent(\uEBBE,segoe_icon_size) tip=['MobBatterySaver8',tip.info] cmd=command.copy('image.fluent(\uEBBE,@segoe_icon_size)'))
	    item(image=image.fluent(\uEBBF,segoe_icon_size) tip=['MobBatterySaver9',tip.info] cmd=command.copy('image.fluent(\uEBBF,@segoe_icon_size)'))
	    item(image=image.fluent(\uEBC0,segoe_icon_size) tip=['MobBatterySaver10',tip.info] cmd=command.copy('image.fluent(\uEBC0,@segoe_icon_size)'))
	    item(image=image.fluent(\uEBC3,segoe_icon_size) tip=['DictionaryCloud',tip.info] cmd=command.copy('image.fluent(\uEBC3,@segoe_icon_size)'))

	    item(image=image.fluent(\uEBC4,segoe_icon_size) tip=['ResetDrive',tip.info] cmd=command.copy('image.fluent(\uEBC4,@segoe_icon_size)') col)
	    item(image=image.fluent(\uEBC5,segoe_icon_size) tip=['VolumeBars',tip.info] cmd=command.copy('image.fluent(\uEBC5,@segoe_icon_size)'))
	    item(image=image.fluent(\uEBC6,segoe_icon_size) tip=['Project',tip.info] cmd=command.copy('image.fluent(\uEBC6,@segoe_icon_size)'))
	    item(image=image.fluent(\uEBD2,segoe_icon_size) tip=['AdjustHologram',tip.info] cmd=command.copy('image.fluent(\uEBD2,@segoe_icon_size)'))
	    item(image=image.fluent(\uEBD3,segoe_icon_size) tip=['CloudDownload',tip.info] cmd=command.copy('image.fluent(\uEBD3,@segoe_icon_size)'))
	    item(image=image.fluent(\uEBD4,segoe_icon_size) tip=['MobWifiCallBars',tip.info] cmd=command.copy('image.fluent(\uEBD4,@segoe_icon_size)'))
	    item(image=image.fluent(\uEBD5,segoe_icon_size) tip=['MobWifiCall0',tip.info] cmd=command.copy('image.fluent(\uEBD5,@segoe_icon_size)'))
	    item(image=image.fluent(\uEBD6,segoe_icon_size) tip=['MobWifiCall1',tip.info] cmd=command.copy('image.fluent(\uEBD6,@segoe_icon_size)'))
	    item(image=image.fluent(\uEBD7,segoe_icon_size) tip=['MobWifiCall2',tip.info] cmd=command.copy('image.fluent(\uEBD7,@segoe_icon_size)'))
	    item(image=image.fluent(\uEBD8,segoe_icon_size) tip=['MobWifiCall3',tip.info] cmd=command.copy('image.fluent(\uEBD8,@segoe_icon_size)'))
	    item(image=image.fluent(\uEBD9,segoe_icon_size) tip=['MobWifiCall4',tip.info] cmd=command.copy('image.fluent(\uEBD9,@segoe_icon_size)'))
	    item(image=image.fluent(\uEBDA,segoe_icon_size) tip=['Family',tip.info] cmd=command.copy('image.fluent(\uEBDA,@segoe_icon_size)'))
	    item(image=image.fluent(\uEBDB,segoe_icon_size) tip=['LockFeedback',tip.info] cmd=command.copy('image.fluent(\uEBDB,@segoe_icon_size)'))
	    item(image=image.fluent(\uEBDE,segoe_icon_size) tip=['DeviceDiscovery',tip.info] cmd=command.copy('image.fluent(\uEBDE,@segoe_icon_size)'))
	    item(image=image.fluent(\uEBE6,segoe_icon_size) tip=['WindDirection',tip.info] cmd=command.copy('image.fluent(\uEBE6,@segoe_icon_size)'))
	    item(image=image.fluent(\uEBE7,segoe_icon_size) tip=['RightArrowKeyTime0',tip.info] cmd=command.copy('image.fluent(\uEBE7,@segoe_icon_size)'))
	}

	menu(title='Fluent #4')
	{
	    item(image=image.fluent(\uEBE8,segoe_icon_size) tip=['Bug',tip.info] cmd=command.copy('image.fluent(\uEBE8,@segoe_icon_size)'))
	    item(image=image.fluent(\uEBFC,segoe_icon_size) tip=['TabletMode',tip.info] cmd=command.copy('image.fluent(\uEBFC,@segoe_icon_size)'))
	    item(image=image.fluent(\uEBFD,segoe_icon_size) tip=['StatusCircleLeft',tip.info] cmd=command.copy('image.fluent(\uEBFD,@segoe_icon_size)'))
	    item(image=image.fluent(\uEBFE,segoe_icon_size) tip=['StatusTriangleLeft',tip.info] cmd=command.copy('image.fluent(\uEBFE,@segoe_icon_size)'))
	    item(image=image.fluent(\uEBFF,segoe_icon_size) tip=['StatusErrorLeft',tip.info] cmd=command.copy('image.fluent(\uEBFF,@segoe_icon_size)'))
	    item(image=image.fluent(\uEC00,segoe_icon_size) tip=['StatusWarningLeft',tip.info] cmd=command.copy('image.fluent(\uEC00,@segoe_icon_size)'))
	    item(image=image.fluent(\uEC02,segoe_icon_size) tip=['MobBatteryUnknown',tip.info] cmd=command.copy('image.fluent(\uEC02,@segoe_icon_size)'))
	    item(image=image.fluent(\uEC05,segoe_icon_size) tip=['NetworkTower',tip.info] cmd=command.copy('image.fluent(\uEC05,@segoe_icon_size)'))
	    item(image=image.fluent(\uEC06,segoe_icon_size) tip=['CityNext',tip.info] cmd=command.copy('image.fluent(\uEC06,@segoe_icon_size)'))
	    item(image=image.fluent(\uEC07,segoe_icon_size) tip=['CityNext2',tip.info] cmd=command.copy('image.fluent(\uEC07,@segoe_icon_size)'))
	    item(image=image.fluent(\uEC08,segoe_icon_size) tip=['Courthouse',tip.info] cmd=command.copy('image.fluent(\uEC08,@segoe_icon_size)'))
	    item(image=image.fluent(\uEC09,segoe_icon_size) tip=['Groceries',tip.info] cmd=command.copy('image.fluent(\uEC09,@segoe_icon_size)'))
	    item(image=image.fluent(\uEC0A,segoe_icon_size) tip=['Sustainable',tip.info] cmd=command.copy('image.fluent(\uEC0A,@segoe_icon_size)'))
	    item(image=image.fluent(\uEC0B,segoe_icon_size) tip=['BuildingEnergy',tip.info] cmd=command.copy('image.fluent(\uEC0B,@segoe_icon_size)'))
	    item(image=image.fluent(\uEC11,segoe_icon_size) tip=['ToggleFilled',tip.info] cmd=command.copy('image.fluent(\uEC11,@segoe_icon_size)'))
	    item(image=image.fluent(\uEC12,segoe_icon_size) tip=['ToggleBorder',tip.info] cmd=command.copy('image.fluent(\uEC12,@segoe_icon_size)'))

	    item(image=image.fluent(\uEC13,segoe_icon_size) tip=['SliderThumb',tip.info] cmd=command.copy('image.fluent(\uEC13,@segoe_icon_size)') col)
	    item(image=image.fluent(\uEC14,segoe_icon_size) tip=['ToggleThumb',tip.info] cmd=command.copy('image.fluent(\uEC14,@segoe_icon_size)'))
	    item(image=image.fluent(\uEC15,segoe_icon_size) tip=['MiracastLogoSmall',tip.info] cmd=command.copy('image.fluent(\uEC15,@segoe_icon_size)'))
	    item(image=image.fluent(\uEC16,segoe_icon_size) tip=['MiracastLogoLarge',tip.info] cmd=command.copy('image.fluent(\uEC16,@segoe_icon_size)'))
	    item(image=image.fluent(\uEC19,segoe_icon_size) tip=['PLAP',tip.info] cmd=command.copy('image.fluent(\uEC19,@segoe_icon_size)'))
	    item(image=image.fluent(\uEC1B,segoe_icon_size) tip=['Badge',tip.info] cmd=command.copy('image.fluent(\uEC1B,@segoe_icon_size)'))
	    item(image=image.fluent(\uEC1E,segoe_icon_size) tip=['SignalRoaming',tip.info] cmd=command.copy('image.fluent(\uEC1E,@segoe_icon_size)'))
	    item(image=image.fluent(\uEC20,segoe_icon_size) tip=['MobileLocked',tip.info] cmd=command.copy('image.fluent(\uEC20,@segoe_icon_size)'))
	    item(image=image.fluent(\uEC24,segoe_icon_size) tip=['InsiderHubApp',tip.info] cmd=command.copy('image.fluent(\uEC24,@segoe_icon_size)'))
	    item(image=image.fluent(\uEC25,segoe_icon_size) tip=['PersonalFolder',tip.info] cmd=command.copy('image.fluent(\uEC25,@segoe_icon_size)'))
	    item(image=image.fluent(\uEC26,segoe_icon_size) tip=['HomeGroup',tip.info] cmd=command.copy('image.fluent(\uEC26,@segoe_icon_size)'))
	    item(image=image.fluent(\uEC27,segoe_icon_size) tip=['MyNetwork',tip.info] cmd=command.copy('image.fluent(\uEC27,@segoe_icon_size)'))
	    item(image=image.fluent(\uEC31,segoe_icon_size) tip=['KeyboardFull',tip.info] cmd=command.copy('image.fluent(\uEC31,@segoe_icon_size)'))
	    item(image=image.fluent(\uEC32,segoe_icon_size) tip=['Cafe',tip.info] cmd=command.copy('image.fluent(\uEC32,@segoe_icon_size)'))
	    item(image=image.fluent(\uEC37,segoe_icon_size) tip=['MobSignal1',tip.info] cmd=command.copy('image.fluent(\uEC37,@segoe_icon_size)'))
	    item(image=image.fluent(\uEC38,segoe_icon_size) tip=['MobSignal2',tip.info] cmd=command.copy('image.fluent(\uEC38,@segoe_icon_size)'))

	    item(image=image.fluent(\uEC39,segoe_icon_size) tip=['MobSignal3',tip.info] cmd=command.copy('image.fluent(\uEC39,@segoe_icon_size)') col)
	    item(image=image.fluent(\uEC3A,segoe_icon_size) tip=['MobSignal4',tip.info] cmd=command.copy('image.fluent(\uEC3A,@segoe_icon_size)'))
	    item(image=image.fluent(\uEC3B,segoe_icon_size) tip=['MobSignal5',tip.info] cmd=command.copy('image.fluent(\uEC3B,@segoe_icon_size)'))
	    item(image=image.fluent(\uEC3C,segoe_icon_size) tip=['MobWifi1',tip.info] cmd=command.copy('image.fluent(\uEC3C,@segoe_icon_size)'))
	    item(image=image.fluent(\uEC3D,segoe_icon_size) tip=['MobWifi2',tip.info] cmd=command.copy('image.fluent(\uEC3D,@segoe_icon_size)'))
	    item(image=image.fluent(\uEC3E,segoe_icon_size) tip=['MobWifi3',tip.info] cmd=command.copy('image.fluent(\uEC3E,@segoe_icon_size)'))
	    item(image=image.fluent(\uEC3F,segoe_icon_size) tip=['MobWifi4',tip.info] cmd=command.copy('image.fluent(\uEC3F,@segoe_icon_size)'))
	    item(image=image.fluent(\uEC40,segoe_icon_size) tip=['MobAirplane',tip.info] cmd=command.copy('image.fluent(\uEC40,@segoe_icon_size)'))
	    item(image=image.fluent(\uEC41,segoe_icon_size) tip=['MobBluetooth',tip.info] cmd=command.copy('image.fluent(\uEC41,@segoe_icon_size)'))
	    item(image=image.fluent(\uEC42,segoe_icon_size) tip=['MobActionCenter',tip.info] cmd=command.copy('image.fluent(\uEC42,@segoe_icon_size)'))
	    item(image=image.fluent(\uEC43,segoe_icon_size) tip=['MobLocation',tip.info] cmd=command.copy('image.fluent(\uEC43,@segoe_icon_size)'))
	    item(image=image.fluent(\uEC44,segoe_icon_size) tip=['MobWifiHotspot',tip.info] cmd=command.copy('image.fluent(\uEC44,@segoe_icon_size)'))
	    item(image=image.fluent(\uEC45,segoe_icon_size) tip=['LanguageJpn',tip.info] cmd=command.copy('image.fluent(\uEC45,@segoe_icon_size)'))
	    item(image=image.fluent(\uEC46,segoe_icon_size) tip=['MobQuietHours',tip.info] cmd=command.copy('image.fluent(\uEC46,@segoe_icon_size)'))
	    item(image=image.fluent(\uEC47,segoe_icon_size) tip=['MobDrivingMode',tip.info] cmd=command.copy('image.fluent(\uEC47,@segoe_icon_size)'))
	    item(image=image.fluent(\uEC48,segoe_icon_size) tip=['SpeedOff',tip.info] cmd=command.copy('image.fluent(\uEC48,@segoe_icon_size)'))

	    item(image=image.fluent(\uEC49,segoe_icon_size) tip=['SpeedMedium',tip.info] cmd=command.copy('image.fluent(\uEC49,@segoe_icon_size)') col)
	    item(image=image.fluent(\uEC4A,segoe_icon_size) tip=['SpeedHigh',tip.info] cmd=command.copy('image.fluent(\uEC4A,@segoe_icon_size)'))
	    item(image=image.fluent(\uEC4E,segoe_icon_size) tip=['ThisPC',tip.info] cmd=command.copy('image.fluent(\uEC4E,@segoe_icon_size)'))
	    item(image=image.fluent(\uEC4F,segoe_icon_size) tip=['MusicNote',tip.info] cmd=command.copy('image.fluent(\uEC4F,@segoe_icon_size)'))
	    item(image=image.fluent(\uEC50,segoe_icon_size) tip=['FileExplorer',tip.info] cmd=command.copy('image.fluent(\uEC50,@segoe_icon_size)'))
	    item(image=image.fluent(\uEC51,segoe_icon_size) tip=['FileExplorerApp',tip.info] cmd=command.copy('image.fluent(\uEC51,@segoe_icon_size)'))
	    item(image=image.fluent(\uEC52,segoe_icon_size) tip=['LeftArrowKeyTime0',tip.info] cmd=command.copy('image.fluent(\uEC52,@segoe_icon_size)'))
	    item(image=image.fluent(\uEC54,segoe_icon_size) tip=['MicOff',tip.info] cmd=command.copy('image.fluent(\uEC54,@segoe_icon_size)'))
	    item(image=image.fluent(\uEC55,segoe_icon_size) tip=['MicSleep',tip.info] cmd=command.copy('image.fluent(\uEC55,@segoe_icon_size)'))
	    item(image=image.fluent(\uEC56,segoe_icon_size) tip=['MicError',tip.info] cmd=command.copy('image.fluent(\uEC56,@segoe_icon_size)'))
	    item(image=image.fluent(\uEC57,segoe_icon_size) tip=['PlaybackRate1x',tip.info] cmd=command.copy('image.fluent(\uEC57,@segoe_icon_size)'))
	    item(image=image.fluent(\uEC58,segoe_icon_size) tip=['PlaybackRateOther',tip.info] cmd=command.copy('image.fluent(\uEC58,@segoe_icon_size)'))
	    item(image=image.fluent(\uEC59,segoe_icon_size) tip=['CashDrawer',tip.info] cmd=command.copy('image.fluent(\uEC59,@segoe_icon_size)'))
	    item(image=image.fluent(\uEC5A,segoe_icon_size) tip=['BarcodeScanner',tip.info] cmd=command.copy('image.fluent(\uEC5A,@segoe_icon_size)'))
	    item(image=image.fluent(\uEC5B,segoe_icon_size) tip=['ReceiptPrinter',tip.info] cmd=command.copy('image.fluent(\uEC5B,@segoe_icon_size)'))
	    item(image=image.fluent(\uEC5C,segoe_icon_size) tip=['MagStripeReader',tip.info] cmd=command.copy('image.fluent(\uEC5C,@segoe_icon_size)'))

	    item(image=image.fluent(\uEC61,segoe_icon_size) tip=['CompletedSolid',tip.info] cmd=command.copy('image.fluent(\uEC61,@segoe_icon_size)') col)
	    item(image=image.fluent(\uEC64,segoe_icon_size) tip=['CompanionApp',tip.info] cmd=command.copy('image.fluent(\uEC64,@segoe_icon_size)'))
	    item(image=image.fluent(\uEC6C,segoe_icon_size) tip=['Favicon2',tip.info] cmd=command.copy('image.fluent(\uEC6C,@segoe_icon_size)'))
	    item(image=image.fluent(\uEC6D,segoe_icon_size) tip=['SwipeRevealArt',tip.info] cmd=command.copy('image.fluent(\uEC6D,@segoe_icon_size)'))
	    item(image=image.fluent(\uEC71,segoe_icon_size) tip=['MicOn',tip.info] cmd=command.copy('image.fluent(\uEC71,@segoe_icon_size)'))
	    item(image=image.fluent(\uEC72,segoe_icon_size) tip=['MicClipping',tip.info] cmd=command.copy('image.fluent(\uEC72,@segoe_icon_size)'))
	    item(image=image.fluent(\uEC74,segoe_icon_size) tip=['TabletSelected',tip.info] cmd=command.copy('image.fluent(\uEC74,@segoe_icon_size)'))
	    item(image=image.fluent(\uEC75,segoe_icon_size) tip=['MobileSelected',tip.info] cmd=command.copy('image.fluent(\uEC75,@segoe_icon_size)'))
	    item(image=image.fluent(\uEC76,segoe_icon_size) tip=['LaptopSelected',tip.info] cmd=command.copy('image.fluent(\uEC76,@segoe_icon_size)'))
	    item(image=image.fluent(\uEC77,segoe_icon_size) tip=['TVMonitorSelected',tip.info] cmd=command.copy('image.fluent(\uEC77,@segoe_icon_size)'))
	    item(image=image.fluent(\uEC7A,segoe_icon_size) tip=['DeveloperTools',tip.info] cmd=command.copy('image.fluent(\uEC7A,@segoe_icon_size)'))
	    item(image=image.fluent(\uEC7E,segoe_icon_size) tip=['MobCallForwarding',tip.info] cmd=command.copy('image.fluent(\uEC7E,@segoe_icon_size)'))
	    item(image=image.fluent(\uEC7F,segoe_icon_size) tip=['MobCallForwardingMirrored',tip.info] cmd=command.copy('image.fluent(\uEC7F,@segoe_icon_size)'))
	    item(image=image.fluent(\uEC80,segoe_icon_size) tip=['BodyCam',tip.info] cmd=command.copy('image.fluent(\uEC80,@segoe_icon_size)'))
	    item(image=image.fluent(\uEC81,segoe_icon_size) tip=['PoliceCar',tip.info] cmd=command.copy('image.fluent(\uEC81,@segoe_icon_size)'))
	    item(image=image.fluent(\uEC87,segoe_icon_size) tip=['Draw',tip.info] cmd=command.copy('image.fluent(\uEC87,@segoe_icon_size)'))

	    item(image=image.fluent(\uEC88,segoe_icon_size) tip=['DrawSolid',tip.info] cmd=command.copy('image.fluent(\uEC88,@segoe_icon_size)') col)
	    item(image=image.fluent(\uEC8A,segoe_icon_size) tip=['LowerBrightness',tip.info] cmd=command.copy('image.fluent(\uEC8A,@segoe_icon_size)'))
	    item(image=image.fluent(\uEC8F,segoe_icon_size) tip=['ScrollUpDown',tip.info] cmd=command.copy('image.fluent(\uEC8F,@segoe_icon_size)'))
	    item(image=image.fluent(\uEC92,segoe_icon_size) tip=['DateTime',tip.info] cmd=command.copy('image.fluent(\uEC92,@segoe_icon_size)'))
	    item(image=image.fluent(\uEC94,segoe_icon_size) tip=['HoloLens',tip.info] cmd=command.copy('image.fluent(\uEC94,@segoe_icon_size)'))
	    item(image=image.fluent(\uECA5,segoe_icon_size) tip=['Tiles',tip.info] cmd=command.copy('image.fluent(\uECA5,@segoe_icon_size)'))
	    item(image=image.fluent(\uECA7,segoe_icon_size) tip=['PartyLeader',tip.info] cmd=command.copy('image.fluent(\uECA7,@segoe_icon_size)'))
	    item(image=image.fluent(\uECAA,segoe_icon_size) tip=['AppIconDefault',tip.info] cmd=command.copy('image.fluent(\uECAA,@segoe_icon_size)'))
	    item(image=image.fluent(\uECAD,segoe_icon_size) tip=['Calories',tip.info] cmd=command.copy('image.fluent(\uECAD,@segoe_icon_size)'))
	    item(image=image.fluent(\uECAF,segoe_icon_size) tip=['POI',tip.info] cmd=command.copy('image.fluent(\uECAF,@segoe_icon_size)'))
	    item(image=image.fluent(\uECB9,segoe_icon_size) tip=['BandBattery0',tip.info] cmd=command.copy('image.fluent(\uECB9,@segoe_icon_size)'))
	    item(image=image.fluent(\uECBA,segoe_icon_size) tip=['BandBattery1',tip.info] cmd=command.copy('image.fluent(\uECBA,@segoe_icon_size)'))
	    item(image=image.fluent(\uECBB,segoe_icon_size) tip=['BandBattery2',tip.info] cmd=command.copy('image.fluent(\uECBB,@segoe_icon_size)'))
	    item(image=image.fluent(\uECBC,segoe_icon_size) tip=['BandBattery3',tip.info] cmd=command.copy('image.fluent(\uECBC,@segoe_icon_size)'))
	    item(image=image.fluent(\uECBD,segoe_icon_size) tip=['BandBattery4',tip.info] cmd=command.copy('image.fluent(\uECBD,@segoe_icon_size)'))
	    item(image=image.fluent(\uECBE,segoe_icon_size) tip=['BandBattery5',tip.info] cmd=command.copy('image.fluent(\uECBE,@segoe_icon_size)'))

	    item(image=image.fluent(\uECBF,segoe_icon_size) tip=['BandBattery6',tip.info] cmd=command.copy('image.fluent(\uECBF,@segoe_icon_size)') col)
	    item(image=image.fluent(\uECC4,segoe_icon_size) tip=['AddSurfaceHub',tip.info] cmd=command.copy('image.fluent(\uECC4,@segoe_icon_size)'))
	    item(image=image.fluent(\uECC5,segoe_icon_size) tip=['DevUpdate',tip.info] cmd=command.copy('image.fluent(\uECC5,@segoe_icon_size)'))
	    item(image=image.fluent(\uECC6,segoe_icon_size) tip=['Unit',tip.info] cmd=command.copy('image.fluent(\uECC6,@segoe_icon_size)'))
	    item(image=image.fluent(\uECC8,segoe_icon_size) tip=['AddTo',tip.info] cmd=command.copy('image.fluent(\uECC8,@segoe_icon_size)'))
	    item(image=image.fluent(\uECC9,segoe_icon_size) tip=['RemoveFrom',tip.info] cmd=command.copy('image.fluent(\uECC9,@segoe_icon_size)'))
	    item(image=image.fluent(\uECCA,segoe_icon_size) tip=['RadioBtnOff',tip.info] cmd=command.copy('image.fluent(\uECCA,@segoe_icon_size)'))
	    item(image=image.fluent(\uECCB,segoe_icon_size) tip=['RadioBtnOn',tip.info] cmd=command.copy('image.fluent(\uECCB,@segoe_icon_size)'))
	    item(image=image.fluent(\uECCC,segoe_icon_size) tip=['RadioBullet2',tip.info] cmd=command.copy('image.fluent(\uECCC,@segoe_icon_size)'))
	    item(image=image.fluent(\uECCD,segoe_icon_size) tip=['ExploreContent',tip.info] cmd=command.copy('image.fluent(\uECCD,@segoe_icon_size)'))
	    item(image=image.fluent(\uECE4,segoe_icon_size) tip=['Blocked2',tip.info] cmd=command.copy('image.fluent(\uECE4,@segoe_icon_size)'))
	    item(image=image.fluent(\uECE7,segoe_icon_size) tip=['ScrollMode',tip.info] cmd=command.copy('image.fluent(\uECE7,@segoe_icon_size)'))
	    item(image=image.fluent(\uECE8,segoe_icon_size) tip=['ZoomMode',tip.info] cmd=command.copy('image.fluent(\uECE8,@segoe_icon_size)'))
	    item(image=image.fluent(\uECE9,segoe_icon_size) tip=['PanMode',tip.info] cmd=command.copy('image.fluent(\uECE9,@segoe_icon_size)'))
	    item(image=image.fluent(\uECF0,segoe_icon_size) tip=['WiredUSB',tip.info] cmd=command.copy('image.fluent(\uECF0,@segoe_icon_size)'))
	    item(image=image.fluent(\uECF1,segoe_icon_size) tip=['WirelessUSB',tip.info] cmd=command.copy('image.fluent(\uECF1,@segoe_icon_size)'))

	    item(image=image.fluent(\uECF3,segoe_icon_size) tip=['USBSafeConnect',tip.info] cmd=command.copy('image.fluent(\uECF3,@segoe_icon_size)') col)
	    item(image=image.fluent(\uED0C,segoe_icon_size) tip=['ActionCenterNotificationMirrored',tip.info] cmd=command.copy('image.fluent(\uED0C,@segoe_icon_size)'))
	    item(image=image.fluent(\uED0D,segoe_icon_size) tip=['ActionCenterMirrored',tip.info] cmd=command.copy('image.fluent(\uED0D,@segoe_icon_size)'))
	    item(image=image.fluent(\uED0E,segoe_icon_size) tip=['SubscriptionAdd',tip.info] cmd=command.copy('image.fluent(\uED0E,@segoe_icon_size)'))
	    item(image=image.fluent(\uED10,segoe_icon_size) tip=['ResetDevice',tip.info] cmd=command.copy('image.fluent(\uED10,@segoe_icon_size)'))
	    item(image=image.fluent(\uED11,segoe_icon_size) tip=['SubscriptionAddMirrored',tip.info] cmd=command.copy('image.fluent(\uED11,@segoe_icon_size)'))
	    item(image=image.fluent(\uED14,segoe_icon_size) tip=['QRCode',tip.info] cmd=command.copy('image.fluent(\uED14,@segoe_icon_size)'))
	    item(image=image.fluent(\uED15,segoe_icon_size) tip=['Feedback',tip.info] cmd=command.copy('image.fluent(\uED15,@segoe_icon_size)'))
	    item(image=image.fluent(\uED1A,segoe_icon_size) tip=['Hide',tip.info] cmd=command.copy('image.fluent(\uED1A,@segoe_icon_size)'))
	    item(image=image.fluent(\uED1E,segoe_icon_size) tip=['Subtitles',tip.info] cmd=command.copy('image.fluent(\uED1E,@segoe_icon_size)'))
	    item(image=image.fluent(\uED1F,segoe_icon_size) tip=['SubtitlesAudio',tip.info] cmd=command.copy('image.fluent(\uED1F,@segoe_icon_size)'))
	    item(image=image.fluent(\uED25,segoe_icon_size) tip=['OpenFolderHorizontal',tip.info] cmd=command.copy('image.fluent(\uED25,@segoe_icon_size)'))
	    item(image=image.fluent(\uED28,segoe_icon_size) tip=['CalendarMirrored',tip.info] cmd=command.copy('image.fluent(\uED28,@segoe_icon_size)'))
	    item(image=image.fluent(\uED2A,segoe_icon_size) tip=['MobeSIM',tip.info] cmd=command.copy('image.fluent(\uED2A,@segoe_icon_size)'))
	    item(image=image.fluent(\uED2B,segoe_icon_size) tip=['MobeSIMNoProfile',tip.info] cmd=command.copy('image.fluent(\uED2B,@segoe_icon_size)'))
	    item(image=image.fluent(\uED2C,segoe_icon_size) tip=['MobeSIMLocked',tip.info] cmd=command.copy('image.fluent(\uED2C,@segoe_icon_size)'))

	    item(image=image.fluent(\uED2D,segoe_icon_size) tip=['MobeSIMBusy',tip.info] cmd=command.copy('image.fluent(\uED2D,@segoe_icon_size)') col)
	    item(image=image.fluent(\uED2E,segoe_icon_size) tip=['SignalError',tip.info] cmd=command.copy('image.fluent(\uED2E,@segoe_icon_size)'))
	    item(image=image.fluent(\uED2F,segoe_icon_size) tip=['StreamingEnterprise',tip.info] cmd=command.copy('image.fluent(\uED2F,@segoe_icon_size)'))
	    item(image=image.fluent(\uED30,segoe_icon_size) tip=['Headphone0',tip.info] cmd=command.copy('image.fluent(\uED30,@segoe_icon_size)'))
	    item(image=image.fluent(\uED31,segoe_icon_size) tip=['Headphone1',tip.info] cmd=command.copy('image.fluent(\uED31,@segoe_icon_size)'))
	    item(image=image.fluent(\uED32,segoe_icon_size) tip=['Headphone2',tip.info] cmd=command.copy('image.fluent(\uED32,@segoe_icon_size)'))
	    item(image=image.fluent(\uED33,segoe_icon_size) tip=['Headphone3',tip.info] cmd=command.copy('image.fluent(\uED33,@segoe_icon_size)'))
	    item(image=image.fluent(\uED35,segoe_icon_size) tip=['Apps',tip.info] cmd=command.copy('image.fluent(\uED35,@segoe_icon_size)'))
	    item(image=image.fluent(\uED39,segoe_icon_size) tip=['KeyboardBrightness',tip.info] cmd=command.copy('image.fluent(\uED39,@segoe_icon_size)'))
	    item(image=image.fluent(\uED3A,segoe_icon_size) tip=['KeyboardLowerBrightness',tip.info] cmd=command.copy('image.fluent(\uED3A,@segoe_icon_size)'))
	    item(image=image.fluent(\uED3C,segoe_icon_size) tip=['SkipBack10',tip.info] cmd=command.copy('image.fluent(\uED3C,@segoe_icon_size)'))
	    item(image=image.fluent(\uED3D,segoe_icon_size) tip=['SkipForward30',tip.info] cmd=command.copy('image.fluent(\uED3D,@segoe_icon_size)'))
	    item(image=image.fluent(\uED41,segoe_icon_size) tip=['TreeFolderFolder',tip.info] cmd=command.copy('image.fluent(\uED41,@segoe_icon_size)'))
	    item(image=image.fluent(\uED42,segoe_icon_size) tip=['TreeFolderFolderFill',tip.info] cmd=command.copy('image.fluent(\uED42,@segoe_icon_size)'))
	    item(image=image.fluent(\uED43,segoe_icon_size) tip=['TreeFolderFolderOpen',tip.info] cmd=command.copy('image.fluent(\uED43,@segoe_icon_size)'))
	    item(image=image.fluent(\uED44,segoe_icon_size) tip=['TreeFolderFolderOpenFill',tip.info] cmd=command.copy('image.fluent(\uED44,@segoe_icon_size)'))

	    item(image=image.fluent(\uED47,segoe_icon_size) tip=['MultimediaDMP',tip.info] cmd=command.copy('image.fluent(\uED47,@segoe_icon_size)') col)
	    item(image=image.fluent(\uED4C,segoe_icon_size) tip=['KeyboardOneHanded',tip.info] cmd=command.copy('image.fluent(\uED4C,@segoe_icon_size)'))
	    item(image=image.fluent(\uED4D,segoe_icon_size) tip=['Narrator',tip.info] cmd=command.copy('image.fluent(\uED4D,@segoe_icon_size)'))
	    item(image=image.fluent(\uED53,segoe_icon_size) tip=['EmojiTabPeople',tip.info] cmd=command.copy('image.fluent(\uED53,@segoe_icon_size)'))
	    item(image=image.fluent(\uED54,segoe_icon_size) tip=['EmojiTabSmilesAnimals',tip.info] cmd=command.copy('image.fluent(\uED54,@segoe_icon_size)'))
	    item(image=image.fluent(\uED55,segoe_icon_size) tip=['EmojiTabCelebrationObjects',tip.info] cmd=command.copy('image.fluent(\uED55,@segoe_icon_size)'))
	    item(image=image.fluent(\uED56,segoe_icon_size) tip=['EmojiTabFoodPlants',tip.info] cmd=command.copy('image.fluent(\uED56,@segoe_icon_size)'))
	    item(image=image.fluent(\uED57,segoe_icon_size) tip=['EmojiTabTransitPlaces',tip.info] cmd=command.copy('image.fluent(\uED57,@segoe_icon_size)'))
	    item(image=image.fluent(\uED58,segoe_icon_size) tip=['EmojiTabSymbols',tip.info] cmd=command.copy('image.fluent(\uED58,@segoe_icon_size)'))
	    item(image=image.fluent(\uED59,segoe_icon_size) tip=['EmojiTabTextSmiles',tip.info] cmd=command.copy('image.fluent(\uED59,@segoe_icon_size)'))
	    item(image=image.fluent(\uED5A,segoe_icon_size) tip=['EmojiTabFavorites',tip.info] cmd=command.copy('image.fluent(\uED5A,@segoe_icon_size)'))
	    item(image=image.fluent(\uED5B,segoe_icon_size) tip=['EmojiSwatch',tip.info] cmd=command.copy('image.fluent(\uED5B,@segoe_icon_size)'))
	    item(image=image.fluent(\uED5C,segoe_icon_size) tip=['ConnectApp',tip.info] cmd=command.copy('image.fluent(\uED5C,@segoe_icon_size)'))
	    item(image=image.fluent(\uED5D,segoe_icon_size) tip=['CompanionDeviceFramework',tip.info] cmd=command.copy('image.fluent(\uED5D,@segoe_icon_size)'))
	    item(image=image.fluent(\uED5E,segoe_icon_size) tip=['Ruler',tip.info] cmd=command.copy('image.fluent(\uED5E,@segoe_icon_size)'))
	    item(image=image.fluent(\uED5F,segoe_icon_size) tip=['FingerInking',tip.info] cmd=command.copy('image.fluent(\uED5F,@segoe_icon_size)'))

	    item(image=image.fluent(\uED60,segoe_icon_size) tip=['StrokeErase',tip.info] cmd=command.copy('image.fluent(\uED60,@segoe_icon_size)') col)
	    item(image=image.fluent(\uED61,segoe_icon_size) tip=['PointErase',tip.info] cmd=command.copy('image.fluent(\uED61,@segoe_icon_size)'))
	    item(image=image.fluent(\uED62,segoe_icon_size) tip=['ClearAllInk',tip.info] cmd=command.copy('image.fluent(\uED62,@segoe_icon_size)'))
	    item(image=image.fluent(\uED63,segoe_icon_size) tip=['Pencil',tip.info] cmd=command.copy('image.fluent(\uED63,@segoe_icon_size)'))
	    item(image=image.fluent(\uED64,segoe_icon_size) tip=['Marker',tip.info] cmd=command.copy('image.fluent(\uED64,@segoe_icon_size)'))
	    item(image=image.fluent(\uED65,segoe_icon_size) tip=['InkingCaret',tip.info] cmd=command.copy('image.fluent(\uED65,@segoe_icon_size)'))
	    item(image=image.fluent(\uED66,segoe_icon_size) tip=['InkingColorOutline',tip.info] cmd=command.copy('image.fluent(\uED66,@segoe_icon_size)'))
	    item(image=image.fluent(\uED67,segoe_icon_size) tip=['InkingColorFill',tip.info] cmd=command.copy('image.fluent(\uED67,@segoe_icon_size)'))
	    item(image=image.fluent(\uEDA2,segoe_icon_size) tip=['HardDrive',tip.info] cmd=command.copy('image.fluent(\uEDA2,@segoe_icon_size)'))
	    item(image=image.fluent(\uEDA3,segoe_icon_size) tip=['NetworkAdapter',tip.info] cmd=command.copy('image.fluent(\uEDA3,@segoe_icon_size)'))
	    item(image=image.fluent(\uEDA4,segoe_icon_size) tip=['Touchscreen',tip.info] cmd=command.copy('image.fluent(\uEDA4,@segoe_icon_size)'))
	    item(image=image.fluent(\uEDA5,segoe_icon_size) tip=['NetworkPrinter',tip.info] cmd=command.copy('image.fluent(\uEDA5,@segoe_icon_size)'))
	    item(image=image.fluent(\uEDA6,segoe_icon_size) tip=['CloudPrinter',tip.info] cmd=command.copy('image.fluent(\uEDA6,@segoe_icon_size)'))
	    item(image=image.fluent(\uEDA7,segoe_icon_size) tip=['KeyboardShortcut',tip.info] cmd=command.copy('image.fluent(\uEDA7,@segoe_icon_size)'))
	    item(image=image.fluent(\uEDA8,segoe_icon_size) tip=['BrushSize',tip.info] cmd=command.copy('image.fluent(\uEDA8,@segoe_icon_size)'))
	    item(image=image.fluent(\uEDA9,segoe_icon_size) tip=['NarratorForward',tip.info] cmd=command.copy('image.fluent(\uEDA9,@segoe_icon_size)'))

	    item(image=image.fluent(\uEDAA,segoe_icon_size) tip=['NarratorForwardMirrored',tip.info] cmd=command.copy('image.fluent(\uEDAA,@segoe_icon_size)') col)
	    item(image=image.fluent(\uEDAB,segoe_icon_size) tip=['SyncBadge12',tip.info] cmd=command.copy('image.fluent(\uEDAB,@segoe_icon_size)'))
	    item(image=image.fluent(\uEDAC,segoe_icon_size) tip=['RingerBadge12',tip.info] cmd=command.copy('image.fluent(\uEDAC,@segoe_icon_size)'))
	    item(image=image.fluent(\uEDAD,segoe_icon_size) tip=['AsteriskBadge12',tip.info] cmd=command.copy('image.fluent(\uEDAD,@segoe_icon_size)'))
	    item(image=image.fluent(\uEDAE,segoe_icon_size) tip=['ErrorBadge12',tip.info] cmd=command.copy('image.fluent(\uEDAE,@segoe_icon_size)'))
	    item(image=image.fluent(\uEDAF,segoe_icon_size) tip=['CircleRingBadge12',tip.info] cmd=command.copy('image.fluent(\uEDAF,@segoe_icon_size)'))
	    item(image=image.fluent(\uEDB0,segoe_icon_size) tip=['CircleFillBadge12',tip.info] cmd=command.copy('image.fluent(\uEDB0,@segoe_icon_size)'))
	    item(image=image.fluent(\uEDB1,segoe_icon_size) tip=['ImportantBadge12',tip.info] cmd=command.copy('image.fluent(\uEDB1,@segoe_icon_size)'))
	    item(image=image.fluent(\uEDB3,segoe_icon_size) tip=['MailBadge12',tip.info] cmd=command.copy('image.fluent(\uEDB3,@segoe_icon_size)'))
	    item(image=image.fluent(\uEDB4,segoe_icon_size) tip=['PauseBadge12',tip.info] cmd=command.copy('image.fluent(\uEDB4,@segoe_icon_size)'))
	    item(image=image.fluent(\uEDB5,segoe_icon_size) tip=['PlayBadge12',tip.info] cmd=command.copy('image.fluent(\uEDB5,@segoe_icon_size)'))
	    item(image=image.fluent(\uEDC6,segoe_icon_size) tip=['PenWorkspace',tip.info] cmd=command.copy('image.fluent(\uEDC6,@segoe_icon_size)'))
	    item(image=image.fluent(\uEDD5,segoe_icon_size) tip=['CaretLeft8',tip.info] cmd=command.copy('image.fluent(\uEDD5,@segoe_icon_size)'))
	    item(image=image.fluent(\uEDD6,segoe_icon_size) tip=['CaretRight8',tip.info] cmd=command.copy('image.fluent(\uEDD6,@segoe_icon_size)'))
	    item(image=image.fluent(\uEDD7,segoe_icon_size) tip=['CaretUp8',tip.info] cmd=command.copy('image.fluent(\uEDD7,@segoe_icon_size)'))
	    item(image=image.fluent(\uEDD8,segoe_icon_size) tip=['CaretDown8',tip.info] cmd=command.copy('image.fluent(\uEDD8,@segoe_icon_size)'))

	    item(image=image.fluent(\uEDD9,segoe_icon_size) tip=['CaretLeftSolid8',tip.info] cmd=command.copy('image.fluent(\uEDD9,@segoe_icon_size)') col)
	    item(image=image.fluent(\uEDDA,segoe_icon_size) tip=['CaretRightSolid8',tip.info] cmd=command.copy('image.fluent(\uEDDA,@segoe_icon_size)'))
	    item(image=image.fluent(\uEDDB,segoe_icon_size) tip=['CaretUpSolid8',tip.info] cmd=command.copy('image.fluent(\uEDDB,@segoe_icon_size)'))
	    item(image=image.fluent(\uEDDC,segoe_icon_size) tip=['CaretDownSolid8',tip.info] cmd=command.copy('image.fluent(\uEDDC,@segoe_icon_size)'))
	    item(image=image.fluent(\uEDE0,segoe_icon_size) tip=['Strikethrough',tip.info] cmd=command.copy('image.fluent(\uEDE0,@segoe_icon_size)'))
	    item(image=image.fluent(\uEDE1,segoe_icon_size) tip=['Export',tip.info] cmd=command.copy('image.fluent(\uEDE1,@segoe_icon_size)'))
	    item(image=image.fluent(\uEDE2,segoe_icon_size) tip=['ExportMirrored',tip.info] cmd=command.copy('image.fluent(\uEDE2,@segoe_icon_size)'))
	    item(image=image.fluent(\uEDE3,segoe_icon_size) tip=['ButtonMenu',tip.info] cmd=command.copy('image.fluent(\uEDE3,@segoe_icon_size)'))
	    item(image=image.fluent(\uEDE4,segoe_icon_size) tip=['CloudSearch',tip.info] cmd=command.copy('image.fluent(\uEDE4,@segoe_icon_size)'))
	    item(image=image.fluent(\uEDE5,segoe_icon_size) tip=['PinyinIMELogo',tip.info] cmd=command.copy('image.fluent(\uEDE5,@segoe_icon_size)'))
	    item(image=image.fluent(\uEDFB,segoe_icon_size) tip=['CalligraphyPen',tip.info] cmd=command.copy('image.fluent(\uEDFB,@segoe_icon_size)'))
	    item(image=image.fluent(\uEE35,segoe_icon_size) tip=['ReplyMirrored',tip.info] cmd=command.copy('image.fluent(\uEE35,@segoe_icon_size)'))
	    item(image=image.fluent(\uEE3F,segoe_icon_size) tip=['LockscreenDesktop',tip.info] cmd=command.copy('image.fluent(\uEE3F,@segoe_icon_size)'))
	    item(image=image.fluent(\uEE40,segoe_icon_size) tip=['TaskViewSettings',tip.info] cmd=command.copy('image.fluent(\uEE40,@segoe_icon_size)'))
	    item(image=image.fluent(\uEE47,segoe_icon_size) tip=['MiniExpand2Mirrored',tip.info] cmd=command.copy('image.fluent(\uEE47,@segoe_icon_size)'))
	    item(image=image.fluent(\uEE49,segoe_icon_size) tip=['MiniContract2Mirrored',tip.info] cmd=command.copy('image.fluent(\uEE49,@segoe_icon_size)'))

	    item(image=image.fluent(\uEE4A,segoe_icon_size) tip=['Play36',tip.info] cmd=command.copy('image.fluent(\uEE4A,@segoe_icon_size)') col)
	    item(image=image.fluent(\uEE56,segoe_icon_size) tip=['PenPalette',tip.info] cmd=command.copy('image.fluent(\uEE56,@segoe_icon_size)'))
	    item(image=image.fluent(\uEE57,segoe_icon_size) tip=['GuestUser',tip.info] cmd=command.copy('image.fluent(\uEE57,@segoe_icon_size)'))
	    item(image=image.fluent(\uEE63,segoe_icon_size) tip=['SettingsBattery',tip.info] cmd=command.copy('image.fluent(\uEE63,@segoe_icon_size)'))
	    item(image=image.fluent(\uEE64,segoe_icon_size) tip=['TaskbarPhone',tip.info] cmd=command.copy('image.fluent(\uEE64,@segoe_icon_size)'))
	    item(image=image.fluent(\uEE65,segoe_icon_size) tip=['LockScreenGlance',tip.info] cmd=command.copy('image.fluent(\uEE65,@segoe_icon_size)'))
	    item(image=image.fluent(\uEE6F,segoe_icon_size) tip=['GenericScan',tip.info] cmd=command.copy('image.fluent(\uEE6F,@segoe_icon_size)'))
	    item(image=image.fluent(\uEE71,segoe_icon_size) tip=['ImageExport',tip.info] cmd=command.copy('image.fluent(\uEE71,@segoe_icon_size)'))
	    item(image=image.fluent(\uEE77,segoe_icon_size) tip=['WifiEthernet',tip.info] cmd=command.copy('image.fluent(\uEE77,@segoe_icon_size)'))
	    item(image=image.fluent(\uEE79,segoe_icon_size) tip=['ActionCenterQuiet',tip.info] cmd=command.copy('image.fluent(\uEE79,@segoe_icon_size)'))
	    item(image=image.fluent(\uEE7A,segoe_icon_size) tip=['ActionCenterQuietNotification',tip.info] cmd=command.copy('image.fluent(\uEE7A,@segoe_icon_size)'))
	    item(image=image.fluent(\uEE92,segoe_icon_size) tip=['TrackersMirrored',tip.info] cmd=command.copy('image.fluent(\uEE92,@segoe_icon_size)'))
	    item(image=image.fluent(\uEE93,segoe_icon_size) tip=['DateTimeMirrored',tip.info] cmd=command.copy('image.fluent(\uEE93,@segoe_icon_size)'))
	    item(image=image.fluent(\uEE94,segoe_icon_size) tip=['Wheel',tip.info] cmd=command.copy('image.fluent(\uEE94,@segoe_icon_size)'))
	    item(image=image.fluent(\uEEA3,segoe_icon_size) tip=['VirtualMachineGroup',tip.info] cmd=command.copy('image.fluent(\uEEA3,@segoe_icon_size)'))
	    item(image=image.fluent(\uEECA,segoe_icon_size) tip=['ButtonView2',tip.info] cmd=command.copy('image.fluent(\uEECA,@segoe_icon_size)'))

	    item(image=image.fluent(\uEF15,segoe_icon_size) tip=['PenWorkspaceMirrored',tip.info] cmd=command.copy('image.fluent(\uEF15,@segoe_icon_size)') col)
	    item(image=image.fluent(\uEF16,segoe_icon_size) tip=['PenPaletteMirrored',tip.info] cmd=command.copy('image.fluent(\uEF16,@segoe_icon_size)'))
	    item(image=image.fluent(\uEF17,segoe_icon_size) tip=['StrokeEraseMirrored',tip.info] cmd=command.copy('image.fluent(\uEF17,@segoe_icon_size)'))
	    item(image=image.fluent(\uEF18,segoe_icon_size) tip=['PointEraseMirrored',tip.info] cmd=command.copy('image.fluent(\uEF18,@segoe_icon_size)'))
	    item(image=image.fluent(\uEF19,segoe_icon_size) tip=['ClearAllInkMirrored',tip.info] cmd=command.copy('image.fluent(\uEF19,@segoe_icon_size)'))
	    item(image=image.fluent(\uEF1F,segoe_icon_size) tip=['BackgroundToggle',tip.info] cmd=command.copy('image.fluent(\uEF1F,@segoe_icon_size)'))
	    item(image=image.fluent(\uEF20,segoe_icon_size) tip=['Marquee',tip.info] cmd=command.copy('image.fluent(\uEF20,@segoe_icon_size)'))
	    item(image=image.fluent(\uEF2C,segoe_icon_size) tip=['ChromeCloseContrast',tip.info] cmd=command.copy('image.fluent(\uEF2C,@segoe_icon_size)'))
	    item(image=image.fluent(\uEF2D,segoe_icon_size) tip=['ChromeMinimizeContrast',tip.info] cmd=command.copy('image.fluent(\uEF2D,@segoe_icon_size)'))
	    item(image=image.fluent(\uEF2E,segoe_icon_size) tip=['ChromeMaximizeContrast',tip.info] cmd=command.copy('image.fluent(\uEF2E,@segoe_icon_size)'))
	    item(image=image.fluent(\uEF2F,segoe_icon_size) tip=['ChromeRestoreContrast',tip.info] cmd=command.copy('image.fluent(\uEF2F,@segoe_icon_size)'))
	    item(image=image.fluent(\uEF31,segoe_icon_size) tip=['TrafficLight',tip.info] cmd=command.copy('image.fluent(\uEF31,@segoe_icon_size)'))
	    item(image=image.fluent(\uEF3B,segoe_icon_size) tip=['Replay',tip.info] cmd=command.copy('image.fluent(\uEF3B,@segoe_icon_size)'))
	    item(image=image.fluent(\uEF3C,segoe_icon_size) tip=['Eyedropper',tip.info] cmd=command.copy('image.fluent(\uEF3C,@segoe_icon_size)'))
	    item(image=image.fluent(\uEF3D,segoe_icon_size) tip=['LineDisplay',tip.info] cmd=command.copy('image.fluent(\uEF3D,@segoe_icon_size)'))
	    item(image=image.fluent(\uEF3E,segoe_icon_size) tip=['PINPad',tip.info] cmd=command.copy('image.fluent(\uEF3E,@segoe_icon_size)'))

	    item(image=image.fluent(\uEF3F,segoe_icon_size) tip=['SignatureCapture',tip.info] cmd=command.copy('image.fluent(\uEF3F,@segoe_icon_size)') col)
	    item(image=image.fluent(\uEF40,segoe_icon_size) tip=['ChipCardCreditCardReader',tip.info] cmd=command.copy('image.fluent(\uEF40,@segoe_icon_size)'))
	    item(image=image.fluent(\uEF42,segoe_icon_size) tip=['MarketDown',tip.info] cmd=command.copy('image.fluent(\uEF42,@segoe_icon_size)'))
	    item(image=image.fluent(\uEF58,segoe_icon_size) tip=['PlayerSettings',tip.info] cmd=command.copy('image.fluent(\uEF58,@segoe_icon_size)'))
	    item(image=image.fluent(\uEF6B,segoe_icon_size) tip=['LandscapeOrientation',tip.info] cmd=command.copy('image.fluent(\uEF6B,@segoe_icon_size)'))
	    item(image=image.fluent(\uEF90,segoe_icon_size) tip=['Flow',tip.info] cmd=command.copy('image.fluent(\uEF90,@segoe_icon_size)'))
	    item(image=image.fluent(\uEFA5,segoe_icon_size) tip=['Touchpad',tip.info] cmd=command.copy('image.fluent(\uEFA5,@segoe_icon_size)'))
	    item(image=image.fluent(\uEFA9,segoe_icon_size) tip=['Speech',tip.info] cmd=command.copy('image.fluent(\uEFA9,@segoe_icon_size)'))
	    item(image=image.fluent(\uF000,segoe_icon_size) tip=['KnowledgeArticle',tip.info] cmd=command.copy('image.fluent(\uF000,@segoe_icon_size)'))
	    item(image=image.fluent(\uF003,segoe_icon_size) tip=['Relationship',tip.info] cmd=command.copy('image.fluent(\uF003,@segoe_icon_size)'))
	    item(image=image.fluent(\uF012,segoe_icon_size) tip=['ZipFolder',tip.info] cmd=command.copy('image.fluent(\uF012,@segoe_icon_size)'))
	    item(image=image.fluent(\uF080,segoe_icon_size) tip=['DefaultAPN',tip.info] cmd=command.copy('image.fluent(\uF080,@segoe_icon_size)'))
	    item(image=image.fluent(\uF081,segoe_icon_size) tip=['UserAPN',tip.info] cmd=command.copy('image.fluent(\uF081,@segoe_icon_size)'))
	    item(image=image.fluent(\uF085,segoe_icon_size) tip=['DoublePinyin',tip.info] cmd=command.copy('image.fluent(\uF085,@segoe_icon_size)'))
	    item(image=image.fluent(\uF08C,segoe_icon_size) tip=['BlueLight',tip.info] cmd=command.copy('image.fluent(\uF08C,@segoe_icon_size)'))
	    item(image=image.fluent(\uF08D,segoe_icon_size) tip=['CaretSolidLeft',tip.info] cmd=command.copy('image.fluent(\uF08D,@segoe_icon_size)'))
	}

	menu(title='Fluent #5')
	{
	    item(image=image.fluent(\uF08E,segoe_icon_size) tip=['CaretSolidDown',tip.info] cmd=command.copy('image.fluent(\uF08E,@segoe_icon_size)'))
	    item(image=image.fluent(\uF08F,segoe_icon_size) tip=['CaretSolidRight',tip.info] cmd=command.copy('image.fluent(\uF08F,@segoe_icon_size)'))
	    item(image=image.fluent(\uF090,segoe_icon_size) tip=['CaretSolidUp',tip.info] cmd=command.copy('image.fluent(\uF090,@segoe_icon_size)'))
	    item(image=image.fluent(\uF093,segoe_icon_size) tip=['ButtonA',tip.info] cmd=command.copy('image.fluent(\uF093,@segoe_icon_size)'))
	    item(image=image.fluent(\uF094,segoe_icon_size) tip=['ButtonB',tip.info] cmd=command.copy('image.fluent(\uF094,@segoe_icon_size)'))
	    item(image=image.fluent(\uF095,segoe_icon_size) tip=['ButtonY',tip.info] cmd=command.copy('image.fluent(\uF095,@segoe_icon_size)'))
	    item(image=image.fluent(\uF096,segoe_icon_size) tip=['ButtonX',tip.info] cmd=command.copy('image.fluent(\uF096,@segoe_icon_size)'))
	    item(image=image.fluent(\uF0AD,segoe_icon_size) tip=['ArrowUp8',tip.info] cmd=command.copy('image.fluent(\uF0AD,@segoe_icon_size)'))
	    item(image=image.fluent(\uF0AE,segoe_icon_size) tip=['ArrowDown8',tip.info] cmd=command.copy('image.fluent(\uF0AE,@segoe_icon_size)'))
	    item(image=image.fluent(\uF0AF,segoe_icon_size) tip=['ArrowRight8',tip.info] cmd=command.copy('image.fluent(\uF0AF,@segoe_icon_size)'))
	    item(image=image.fluent(\uF0B0,segoe_icon_size) tip=['ArrowLeft8',tip.info] cmd=command.copy('image.fluent(\uF0B0,@segoe_icon_size)'))
	    item(image=image.fluent(\uF0B2,segoe_icon_size) tip=['QuarentinedItems',tip.info] cmd=command.copy('image.fluent(\uF0B2,@segoe_icon_size)'))
	    item(image=image.fluent(\uF0B3,segoe_icon_size) tip=['QuarentinedItemsMirrored',tip.info] cmd=command.copy('image.fluent(\uF0B3,@segoe_icon_size)'))
	    item(image=image.fluent(\uF0B4,segoe_icon_size) tip=['Protractor',tip.info] cmd=command.copy('image.fluent(\uF0B4,@segoe_icon_size)'))
	    item(image=image.fluent(\uF0B5,segoe_icon_size) tip=['ChecklistMirrored',tip.info] cmd=command.copy('image.fluent(\uF0B5,@segoe_icon_size)'))
	    item(image=image.fluent(\uF0B6,segoe_icon_size) tip=['StatusCircle7',tip.info] cmd=command.copy('image.fluent(\uF0B6,@segoe_icon_size)'))

	    item(image=image.fluent(\uF0B7,segoe_icon_size) tip=['StatusCheckmark7',tip.info] cmd=command.copy('image.fluent(\uF0B7,@segoe_icon_size)') col)
	    item(image=image.fluent(\uF0B8,segoe_icon_size) tip=['StatusErrorCircle7',tip.info] cmd=command.copy('image.fluent(\uF0B8,@segoe_icon_size)'))
	    item(image=image.fluent(\uF0B9,segoe_icon_size) tip=['Connected',tip.info] cmd=command.copy('image.fluent(\uF0B9,@segoe_icon_size)'))
	    item(image=image.fluent(\uF0C6,segoe_icon_size) tip=['PencilFill',tip.info] cmd=command.copy('image.fluent(\uF0C6,@segoe_icon_size)'))
	    item(image=image.fluent(\uF0C7,segoe_icon_size) tip=['CalligraphyFill',tip.info] cmd=command.copy('image.fluent(\uF0C7,@segoe_icon_size)'))
	    item(image=image.fluent(\uF0CA,segoe_icon_size) tip=['QuarterStarLeft',tip.info] cmd=command.copy('image.fluent(\uF0CA,@segoe_icon_size)'))
	    item(image=image.fluent(\uF0CB,segoe_icon_size) tip=['QuarterStarRight',tip.info] cmd=command.copy('image.fluent(\uF0CB,@segoe_icon_size)'))
	    item(image=image.fluent(\uF0CC,segoe_icon_size) tip=['ThreeQuarterStarLeft',tip.info] cmd=command.copy('image.fluent(\uF0CC,@segoe_icon_size)'))
	    item(image=image.fluent(\uF0CD,segoe_icon_size) tip=['ThreeQuarterStarRight',tip.info] cmd=command.copy('image.fluent(\uF0CD,@segoe_icon_size)'))
	    item(image=image.fluent(\uF0CE,segoe_icon_size) tip=['QuietHoursBadge12',tip.info] cmd=command.copy('image.fluent(\uF0CE,@segoe_icon_size)'))
	    item(image=image.fluent(\uF0D2,segoe_icon_size) tip=['BackMirrored',tip.info] cmd=command.copy('image.fluent(\uF0D2,@segoe_icon_size)'))
	    item(image=image.fluent(\uF0D3,segoe_icon_size) tip=['ForwardMirrored',tip.info] cmd=command.copy('image.fluent(\uF0D3,@segoe_icon_size)'))
	    item(image=image.fluent(\uF0D5,segoe_icon_size) tip=['ChromeBackContrast',tip.info] cmd=command.copy('image.fluent(\uF0D5,@segoe_icon_size)'))
	    item(image=image.fluent(\uF0D6,segoe_icon_size) tip=['ChromeBackContrastMirrored',tip.info] cmd=command.copy('image.fluent(\uF0D6,@segoe_icon_size)'))
	    item(image=image.fluent(\uF0D7,segoe_icon_size) tip=['ChromeBackToWindowContrast',tip.info] cmd=command.copy('image.fluent(\uF0D7,@segoe_icon_size)'))
	    item(image=image.fluent(\uF0D8,segoe_icon_size) tip=['ChromeFullScreenContrast',tip.info] cmd=command.copy('image.fluent(\uF0D8,@segoe_icon_size)'))

	    item(image=image.fluent(\uF0E2,segoe_icon_size) tip=['GridView',tip.info] cmd=command.copy('image.fluent(\uF0E2,@segoe_icon_size)') col)
	    item(image=image.fluent(\uF0E3,segoe_icon_size) tip=['ClipboardList',tip.info] cmd=command.copy('image.fluent(\uF0E3,@segoe_icon_size)'))
	    item(image=image.fluent(\uF0E4,segoe_icon_size) tip=['ClipboardListMirrored',tip.info] cmd=command.copy('image.fluent(\uF0E4,@segoe_icon_size)'))
	    item(image=image.fluent(\uF0E5,segoe_icon_size) tip=['OutlineQuarterStarLeft',tip.info] cmd=command.copy('image.fluent(\uF0E5,@segoe_icon_size)'))
	    item(image=image.fluent(\uF0E6,segoe_icon_size) tip=['OutlineQuarterStarRight',tip.info] cmd=command.copy('image.fluent(\uF0E6,@segoe_icon_size)'))
	    item(image=image.fluent(\uF0E7,segoe_icon_size) tip=['OutlineHalfStarLeft',tip.info] cmd=command.copy('image.fluent(\uF0E7,@segoe_icon_size)'))
	    item(image=image.fluent(\uF0E8,segoe_icon_size) tip=['OutlineHalfStarRight',tip.info] cmd=command.copy('image.fluent(\uF0E8,@segoe_icon_size)'))
	    item(image=image.fluent(\uF0E9,segoe_icon_size) tip=['OutlineThreeQuarterStarLeft',tip.info] cmd=command.copy('image.fluent(\uF0E9,@segoe_icon_size)'))
	    item(image=image.fluent(\uF0EA,segoe_icon_size) tip=['OutlineThreeQuarterStarRight',tip.info] cmd=command.copy('image.fluent(\uF0EA,@segoe_icon_size)'))
	    item(image=image.fluent(\uF0EB,segoe_icon_size) tip=['SpatialVolume0',tip.info] cmd=command.copy('image.fluent(\uF0EB,@segoe_icon_size)'))
	    item(image=image.fluent(\uF0EC,segoe_icon_size) tip=['SpatialVolume1',tip.info] cmd=command.copy('image.fluent(\uF0EC,@segoe_icon_size)'))
	    item(image=image.fluent(\uF0ED,segoe_icon_size) tip=['SpatialVolume2',tip.info] cmd=command.copy('image.fluent(\uF0ED,@segoe_icon_size)'))
	    item(image=image.fluent(\uF0EE,segoe_icon_size) tip=['SpatialVolume3',tip.info] cmd=command.copy('image.fluent(\uF0EE,@segoe_icon_size)'))
	    item(image=image.fluent(\uF0EF,segoe_icon_size) tip=['ApplicationGuard',tip.info] cmd=command.copy('image.fluent(\uF0EF,@segoe_icon_size)'))
	    item(image=image.fluent(\uF0F7,segoe_icon_size) tip=['OutlineStarLeftHalf',tip.info] cmd=command.copy('image.fluent(\uF0F7,@segoe_icon_size)'))
	    item(image=image.fluent(\uF0F8,segoe_icon_size) tip=['OutlineStarRightHalf',tip.info] cmd=command.copy('image.fluent(\uF0F8,@segoe_icon_size)'))

	    item(image=image.fluent(\uF0F9,segoe_icon_size) tip=['ChromeAnnotateContrast',tip.info] cmd=command.copy('image.fluent(\uF0F9,@segoe_icon_size)') col)
	    item(image=image.fluent(\uF0FB,segoe_icon_size) tip=['DefenderBadge12',tip.info] cmd=command.copy('image.fluent(\uF0FB,@segoe_icon_size)'))
	    item(image=image.fluent(\uF103,segoe_icon_size) tip=['DetachablePC',tip.info] cmd=command.copy('image.fluent(\uF103,@segoe_icon_size)'))
	    item(image=image.fluent(\uF108,segoe_icon_size) tip=['LeftStick',tip.info] cmd=command.copy('image.fluent(\uF108,@segoe_icon_size)'))
	    item(image=image.fluent(\uF109,segoe_icon_size) tip=['RightStick',tip.info] cmd=command.copy('image.fluent(\uF109,@segoe_icon_size)'))
	    item(image=image.fluent(\uF10A,segoe_icon_size) tip=['TriggerLeft',tip.info] cmd=command.copy('image.fluent(\uF10A,@segoe_icon_size)'))
	    item(image=image.fluent(\uF10B,segoe_icon_size) tip=['TriggerRight',tip.info] cmd=command.copy('image.fluent(\uF10B,@segoe_icon_size)'))
	    item(image=image.fluent(\uF10C,segoe_icon_size) tip=['BumperLeft',tip.info] cmd=command.copy('image.fluent(\uF10C,@segoe_icon_size)'))
	    item(image=image.fluent(\uF10D,segoe_icon_size) tip=['BumperRight',tip.info] cmd=command.copy('image.fluent(\uF10D,@segoe_icon_size)'))
	    item(image=image.fluent(\uF10E,segoe_icon_size) tip=['Dpad',tip.info] cmd=command.copy('image.fluent(\uF10E,@segoe_icon_size)'))
	    item(image=image.fluent(\uF110,segoe_icon_size) tip=['EnglishPunctuation',tip.info] cmd=command.copy('image.fluent(\uF110,@segoe_icon_size)'))
	    item(image=image.fluent(\uF111,segoe_icon_size) tip=['ChinesePunctuation',tip.info] cmd=command.copy('image.fluent(\uF111,@segoe_icon_size)'))
	    item(image=image.fluent(\uF119,segoe_icon_size) tip=['HMD',tip.info] cmd=command.copy('image.fluent(\uF119,@segoe_icon_size)'))
	    item(image=image.fluent(\uF11B,segoe_icon_size) tip=['CtrlSpatialRight',tip.info] cmd=command.copy('image.fluent(\uF11B,@segoe_icon_size)'))
	    item(image=image.fluent(\uF126,segoe_icon_size) tip=['PaginationDotOutline10',tip.info] cmd=command.copy('image.fluent(\uF126,@segoe_icon_size)'))
	    item(image=image.fluent(\uF127,segoe_icon_size) tip=['PaginationDotSolid10',tip.info] cmd=command.copy('image.fluent(\uF127,@segoe_icon_size)'))

	    item(image=image.fluent(\uF128,segoe_icon_size) tip=['StrokeErase2',tip.info] cmd=command.copy('image.fluent(\uF128,@segoe_icon_size)') col)
	    item(image=image.fluent(\uF129,segoe_icon_size) tip=['SmallErase',tip.info] cmd=command.copy('image.fluent(\uF129,@segoe_icon_size)'))
	    item(image=image.fluent(\uF12A,segoe_icon_size) tip=['LargeErase',tip.info] cmd=command.copy('image.fluent(\uF12A,@segoe_icon_size)'))
	    item(image=image.fluent(\uF12B,segoe_icon_size) tip=['FolderHorizontal',tip.info] cmd=command.copy('image.fluent(\uF12B,@segoe_icon_size)'))
	    item(image=image.fluent(\uF12E,segoe_icon_size) tip=['MicrophoneListening',tip.info] cmd=command.copy('image.fluent(\uF12E,@segoe_icon_size)'))
	    item(image=image.fluent(\uF12F,segoe_icon_size) tip=['StatusExclamationCircle7',tip.info] cmd=command.copy('image.fluent(\uF12F,@segoe_icon_size)'))
	    item(image=image.fluent(\uF131,segoe_icon_size) tip=['Video360',tip.info] cmd=command.copy('image.fluent(\uF131,@segoe_icon_size)'))
	    item(image=image.fluent(\uF133,segoe_icon_size) tip=['GiftboxOpen',tip.info] cmd=command.copy('image.fluent(\uF133,@segoe_icon_size)'))
	    item(image=image.fluent(\uF136,segoe_icon_size) tip=['StatusCircleOuter',tip.info] cmd=command.copy('image.fluent(\uF136,@segoe_icon_size)'))
	    item(image=image.fluent(\uF137,segoe_icon_size) tip=['StatusCircleInner',tip.info] cmd=command.copy('image.fluent(\uF137,@segoe_icon_size)'))
	    item(image=image.fluent(\uF138,segoe_icon_size) tip=['StatusCircleRing',tip.info] cmd=command.copy('image.fluent(\uF138,@segoe_icon_size)'))
	    item(image=image.fluent(\uF139,segoe_icon_size) tip=['StatusTriangleOuter',tip.info] cmd=command.copy('image.fluent(\uF139,@segoe_icon_size)'))
	    item(image=image.fluent(\uF13A,segoe_icon_size) tip=['StatusTriangleInner',tip.info] cmd=command.copy('image.fluent(\uF13A,@segoe_icon_size)'))
	    item(image=image.fluent(\uF13B,segoe_icon_size) tip=['StatusTriangleExclamation',tip.info] cmd=command.copy('image.fluent(\uF13B,@segoe_icon_size)'))
	    item(image=image.fluent(\uF13C,segoe_icon_size) tip=['StatusCircleExclamation',tip.info] cmd=command.copy('image.fluent(\uF13C,@segoe_icon_size)'))
	    item(image=image.fluent(\uF13D,segoe_icon_size) tip=['StatusCircleErrorX',tip.info] cmd=command.copy('image.fluent(\uF13D,@segoe_icon_size)'))

	    item(image=image.fluent(\uF13E,segoe_icon_size) tip=['StatusCircleCheckmark',tip.info] cmd=command.copy('image.fluent(\uF13E,@segoe_icon_size)') col)
	    item(image=image.fluent(\uF13F,segoe_icon_size) tip=['StatusCircleInfo',tip.info] cmd=command.copy('image.fluent(\uF13F,@segoe_icon_size)'))
	    item(image=image.fluent(\uF140,segoe_icon_size) tip=['StatusCircleBlock',tip.info] cmd=command.copy('image.fluent(\uF140,@segoe_icon_size)'))
	    item(image=image.fluent(\uF141,segoe_icon_size) tip=['StatusCircleBlock2',tip.info] cmd=command.copy('image.fluent(\uF141,@segoe_icon_size)'))
	    item(image=image.fluent(\uF142,segoe_icon_size) tip=['StatusCircleQuestionMark',tip.info] cmd=command.copy('image.fluent(\uF142,@segoe_icon_size)'))
	    item(image=image.fluent(\uF143,segoe_icon_size) tip=['StatusCircleSync',tip.info] cmd=command.copy('image.fluent(\uF143,@segoe_icon_size)'))
	    item(image=image.fluent(\uF146,segoe_icon_size) tip=['Dial1',tip.info] cmd=command.copy('image.fluent(\uF146,@segoe_icon_size)'))
	    item(image=image.fluent(\uF147,segoe_icon_size) tip=['Dial2',tip.info] cmd=command.copy('image.fluent(\uF147,@segoe_icon_size)'))
	    item(image=image.fluent(\uF148,segoe_icon_size) tip=['Dial3',tip.info] cmd=command.copy('image.fluent(\uF148,@segoe_icon_size)'))
	    item(image=image.fluent(\uF149,segoe_icon_size) tip=['Dial4',tip.info] cmd=command.copy('image.fluent(\uF149,@segoe_icon_size)'))
	    item(image=image.fluent(\uF14A,segoe_icon_size) tip=['Dial5',tip.info] cmd=command.copy('image.fluent(\uF14A,@segoe_icon_size)'))
	    item(image=image.fluent(\uF14B,segoe_icon_size) tip=['Dial6',tip.info] cmd=command.copy('image.fluent(\uF14B,@segoe_icon_size)'))
	    item(image=image.fluent(\uF14C,segoe_icon_size) tip=['Dial7',tip.info] cmd=command.copy('image.fluent(\uF14C,@segoe_icon_size)'))
	    item(image=image.fluent(\uF14D,segoe_icon_size) tip=['Dial8',tip.info] cmd=command.copy('image.fluent(\uF14D,@segoe_icon_size)'))
	    item(image=image.fluent(\uF14E,segoe_icon_size) tip=['Dial9',tip.info] cmd=command.copy('image.fluent(\uF14E,@segoe_icon_size)'))
	    item(image=image.fluent(\uF14F,segoe_icon_size) tip=['Dial10',tip.info] cmd=command.copy('image.fluent(\uF14F,@segoe_icon_size)'))

	    item(image=image.fluent(\uF150,segoe_icon_size) tip=['Dial11',tip.info] cmd=command.copy('image.fluent(\uF150,@segoe_icon_size)') col)
	    item(image=image.fluent(\uF151,segoe_icon_size) tip=['Dial12',tip.info] cmd=command.copy('image.fluent(\uF151,@segoe_icon_size)'))
	    item(image=image.fluent(\uF152,segoe_icon_size) tip=['Dial13',tip.info] cmd=command.copy('image.fluent(\uF152,@segoe_icon_size)'))
	    item(image=image.fluent(\uF153,segoe_icon_size) tip=['Dial14',tip.info] cmd=command.copy('image.fluent(\uF153,@segoe_icon_size)'))
	    item(image=image.fluent(\uF154,segoe_icon_size) tip=['Dial15',tip.info] cmd=command.copy('image.fluent(\uF154,@segoe_icon_size)'))
	    item(image=image.fluent(\uF155,segoe_icon_size) tip=['Dial16',tip.info] cmd=command.copy('image.fluent(\uF155,@segoe_icon_size)'))
	    item(image=image.fluent(\uF156,segoe_icon_size) tip=['DialShape1',tip.info] cmd=command.copy('image.fluent(\uF156,@segoe_icon_size)'))
	    item(image=image.fluent(\uF157,segoe_icon_size) tip=['DialShape2',tip.info] cmd=command.copy('image.fluent(\uF157,@segoe_icon_size)'))
	    item(image=image.fluent(\uF158,segoe_icon_size) tip=['DialShape3',tip.info] cmd=command.copy('image.fluent(\uF158,@segoe_icon_size)'))
	    item(image=image.fluent(\uF159,segoe_icon_size) tip=['DialShape4',tip.info] cmd=command.copy('image.fluent(\uF159,@segoe_icon_size)'))
	    item(image=image.fluent(\uF15F,segoe_icon_size) tip=['ClosedCaptionsInternational',tip.info] cmd=command.copy('image.fluent(\uF15F,@segoe_icon_size)'))
	    item(image=image.fluent(\uF161,segoe_icon_size) tip=['TollSolid',tip.info] cmd=command.copy('image.fluent(\uF161,@segoe_icon_size)'))
	    item(image=image.fluent(\uF163,segoe_icon_size) tip=['TrafficCongestionSolid',tip.info] cmd=command.copy('image.fluent(\uF163,@segoe_icon_size)'))
	    item(image=image.fluent(\uF164,segoe_icon_size) tip=['ExploreContentSingle',tip.info] cmd=command.copy('image.fluent(\uF164,@segoe_icon_size)'))
	    item(image=image.fluent(\uF165,segoe_icon_size) tip=['CollapseContent',tip.info] cmd=command.copy('image.fluent(\uF165,@segoe_icon_size)'))
	    item(image=image.fluent(\uF166,segoe_icon_size) tip=['CollapseContentSingle',tip.info] cmd=command.copy('image.fluent(\uF166,@segoe_icon_size)'))

	    item(image=image.fluent(\uF167,segoe_icon_size) tip=['InfoSolid',tip.info] cmd=command.copy('image.fluent(\uF167,@segoe_icon_size)') col)
	    item(image=image.fluent(\uF168,segoe_icon_size) tip=['GroupList',tip.info] cmd=command.copy('image.fluent(\uF168,@segoe_icon_size)'))
	    item(image=image.fluent(\uF169,segoe_icon_size) tip=['CaretBottomRightSolidCenter8',tip.info] cmd=command.copy('image.fluent(\uF169,@segoe_icon_size)'))
	    item(image=image.fluent(\uF16A,segoe_icon_size) tip=['ProgressRingDots',tip.info] cmd=command.copy('image.fluent(\uF16A,@segoe_icon_size)'))
	    item(image=image.fluent(\uF16B,segoe_icon_size) tip=['Checkbox14',tip.info] cmd=command.copy('image.fluent(\uF16B,@segoe_icon_size)'))
	    item(image=image.fluent(\uF16C,segoe_icon_size) tip=['CheckboxComposite14',tip.info] cmd=command.copy('image.fluent(\uF16C,@segoe_icon_size)'))
	    item(image=image.fluent(\uF16D,segoe_icon_size) tip=['CheckboxIndeterminateCombo14',tip.info] cmd=command.copy('image.fluent(\uF16D,@segoe_icon_size)'))
	    item(image=image.fluent(\uF16E,segoe_icon_size) tip=['CheckboxIndeterminateCombo',tip.info] cmd=command.copy('image.fluent(\uF16E,@segoe_icon_size)'))
	    item(image=image.fluent(\uF175,segoe_icon_size) tip=['StatusPause7',tip.info] cmd=command.copy('image.fluent(\uF175,@segoe_icon_size)'))
	    item(image=image.fluent(\uF17F,segoe_icon_size) tip=['CharacterAppearance',tip.info] cmd=command.copy('image.fluent(\uF17F,@segoe_icon_size)'))
	    item(image=image.fluent(\uF180,segoe_icon_size) tip=['Lexicon',tip.info] cmd=command.copy('image.fluent(\uF180,@segoe_icon_size)'))
	    item(image=image.fluent(\uF182,segoe_icon_size) tip=['ScreenTime',tip.info] cmd=command.copy('image.fluent(\uF182,@segoe_icon_size)'))
	    item(image=image.fluent(\uF191,segoe_icon_size) tip=['HeadlessDevice',tip.info] cmd=command.copy('image.fluent(\uF191,@segoe_icon_size)'))
	    item(image=image.fluent(\uF193,segoe_icon_size) tip=['NetworkSharing',tip.info] cmd=command.copy('image.fluent(\uF193,@segoe_icon_size)'))
	    item(image=image.fluent(\uF19D,segoe_icon_size) tip=['EyeGaze',tip.info] cmd=command.copy('image.fluent(\uF19D,@segoe_icon_size)'))
	    item(image=image.fluent(\uF19E,segoe_icon_size) tip=['ToggleLeft',tip.info] cmd=command.copy('image.fluent(\uF19E,@segoe_icon_size)'))

	    item(image=image.fluent(\uF19F,segoe_icon_size) tip=['ToggleRight',tip.info] cmd=command.copy('image.fluent(\uF19F,@segoe_icon_size)') col)
	    item(image=image.fluent(\uF1AD,segoe_icon_size) tip=['WindowsInsider',tip.info] cmd=command.copy('image.fluent(\uF1AD,@segoe_icon_size)'))
	    item(image=image.fluent(\uF1CB,segoe_icon_size) tip=['ChromeSwitch',tip.info] cmd=command.copy('image.fluent(\uF1CB,@segoe_icon_size)'))
	    item(image=image.fluent(\uF1CC,segoe_icon_size) tip=['ChromeSwitchContast',tip.info] cmd=command.copy('image.fluent(\uF1CC,@segoe_icon_size)'))
	    item(image=image.fluent(\uF1D8,segoe_icon_size) tip=['StatusCheckmark',tip.info] cmd=command.copy('image.fluent(\uF1D8,@segoe_icon_size)'))
	    item(image=image.fluent(\uF1D9,segoe_icon_size) tip=['StatusCheckmarkLeft',tip.info] cmd=command.copy('image.fluent(\uF1D9,@segoe_icon_size)'))
	    item(image=image.fluent(\uF20C,segoe_icon_size) tip=['KeyboardLeftAligned',tip.info] cmd=command.copy('image.fluent(\uF20C,@segoe_icon_size)'))
	    item(image=image.fluent(\uF20D,segoe_icon_size) tip=['KeyboardRightAligned',tip.info] cmd=command.copy('image.fluent(\uF20D,@segoe_icon_size)'))
	    item(image=image.fluent(\uF210,segoe_icon_size) tip=['KeyboardSettings',tip.info] cmd=command.copy('image.fluent(\uF210,@segoe_icon_size)'))
	    item(image=image.fluent(\uF211,segoe_icon_size) tip=['NetworkPhysical',tip.info] cmd=command.copy('image.fluent(\uF211,@segoe_icon_size)'))
	    item(image=image.fluent(\uF22C,segoe_icon_size) tip=['IOT',tip.info] cmd=command.copy('image.fluent(\uF22C,@segoe_icon_size)'))
	    item(image=image.fluent(\uF22E,segoe_icon_size) tip=['UnknownMirrored',tip.info] cmd=command.copy('image.fluent(\uF22E,@segoe_icon_size)'))
	    item(image=image.fluent(\uF246,segoe_icon_size) tip=['ViewDashboard',tip.info] cmd=command.copy('image.fluent(\uF246,@segoe_icon_size)'))
	    item(image=image.fluent(\uF259,segoe_icon_size) tip=['ExploitProtectionSettings',tip.info] cmd=command.copy('image.fluent(\uF259,@segoe_icon_size)'))
	    item(image=image.fluent(\uF260,segoe_icon_size) tip=['KeyboardNarrow',tip.info] cmd=command.copy('image.fluent(\uF260,@segoe_icon_size)'))
	    item(image=image.fluent(\uF261,segoe_icon_size) tip=['Keyboard12Key',tip.info] cmd=command.copy('image.fluent(\uF261,@segoe_icon_size)'))

	    item(image=image.fluent(\uF26B,segoe_icon_size) tip=['KeyboardDock',tip.info] cmd=command.copy('image.fluent(\uF26B,@segoe_icon_size)') col)
	    item(image=image.fluent(\uF26C,segoe_icon_size) tip=['KeyboardUndock',tip.info] cmd=command.copy('image.fluent(\uF26C,@segoe_icon_size)'))
	    item(image=image.fluent(\uF26D,segoe_icon_size) tip=['KeyboardLeftDock',tip.info] cmd=command.copy('image.fluent(\uF26D,@segoe_icon_size)'))
	    item(image=image.fluent(\uF26E,segoe_icon_size) tip=['KeyboardRightDock',tip.info] cmd=command.copy('image.fluent(\uF26E,@segoe_icon_size)'))
	    item(image=image.fluent(\uF270,segoe_icon_size) tip=['Ear',tip.info] cmd=command.copy('image.fluent(\uF270,@segoe_icon_size)'))
	    item(image=image.fluent(\uF271,segoe_icon_size) tip=['PointerHand',tip.info] cmd=command.copy('image.fluent(\uF271,@segoe_icon_size)'))
	    item(image=image.fluent(\uF272,segoe_icon_size) tip=['Bullseye',tip.info] cmd=command.copy('image.fluent(\uF272,@segoe_icon_size)'))
	    item(image=image.fluent(\uF28B,segoe_icon_size) tip=['DocumentApproval',tip.info] cmd=command.copy('image.fluent(\uF28B,@segoe_icon_size)'))
	    item(image=image.fluent(\uF2B7,segoe_icon_size) tip=['LocaleLanguage',tip.info] cmd=command.copy('image.fluent(\uF2B7,@segoe_icon_size)'))
	    item(image=image.fluent(\uF32A,segoe_icon_size) tip=['PassiveAuthentication',tip.info] cmd=command.copy('image.fluent(\uF32A,@segoe_icon_size)'))
	    item(image=image.fluent(\uF354,segoe_icon_size) tip=['ColorSolid',tip.info] cmd=command.copy('image.fluent(\uF354,@segoe_icon_size)'))
	    item(image=image.fluent(\uF384,segoe_icon_size) tip=['NetworkOffline',tip.info] cmd=command.copy('image.fluent(\uF384,@segoe_icon_size)'))
	    item(image=image.fluent(\uF385,segoe_icon_size) tip=['NetworkConnected',tip.info] cmd=command.copy('image.fluent(\uF385,@segoe_icon_size)'))
	    item(image=image.fluent(\uF386,segoe_icon_size) tip=['NetworkConnectedCheckmark',tip.info] cmd=command.copy('image.fluent(\uF386,@segoe_icon_size)'))
	    item(image=image.fluent(\uF3B1,segoe_icon_size) tip=['SignOut',tip.info] cmd=command.copy('image.fluent(\uF3B1,@segoe_icon_size)'))
	    item(image=image.fluent(\uF3CC,segoe_icon_size) tip=['StatusInfo',tip.info] cmd=command.copy('image.fluent(\uF3CC,@segoe_icon_size)'))

	    item(image=image.fluent(\uF3CD,segoe_icon_size) tip=['StatusInfoLeft',tip.info] cmd=command.copy('image.fluent(\uF3CD,@segoe_icon_size)') col)
	    item(image=image.fluent(\uF3E2,segoe_icon_size) tip=['NearbySharing',tip.info] cmd=command.copy('image.fluent(\uF3E2,@segoe_icon_size)'))
	    item(image=image.fluent(\uF3E7,segoe_icon_size) tip=['CtrlSpatialLeft',tip.info] cmd=command.copy('image.fluent(\uF3E7,@segoe_icon_size)'))
	    item(image=image.fluent(\uF404,segoe_icon_size) tip=['InteractiveDashboard',tip.info] cmd=command.copy('image.fluent(\uF404,@segoe_icon_size)'))
	    item(image=image.fluent(\uF405,segoe_icon_size) tip=['DeclineCall',tip.info] cmd=command.copy('image.fluent(\uF405,@segoe_icon_size)'))
	    item(image=image.fluent(\uF406,segoe_icon_size) tip=['ClippingTool',tip.info] cmd=command.copy('image.fluent(\uF406,@segoe_icon_size)'))
	    item(image=image.fluent(\uF407,segoe_icon_size) tip=['RectangularClipping',tip.info] cmd=command.copy('image.fluent(\uF407,@segoe_icon_size)'))
	    item(image=image.fluent(\uF408,segoe_icon_size) tip=['FreeFormClipping',tip.info] cmd=command.copy('image.fluent(\uF408,@segoe_icon_size)'))
	    item(image=image.fluent(\uF413,segoe_icon_size) tip=['CopyTo',tip.info] cmd=command.copy('image.fluent(\uF413,@segoe_icon_size)'))
	    item(image=image.fluent(\uF427,segoe_icon_size) tip=['IDBadge',tip.info] cmd=command.copy('image.fluent(\uF427,@segoe_icon_size)'))
	    item(image=image.fluent(\uF439,segoe_icon_size) tip=['DynamicLock',tip.info] cmd=command.copy('image.fluent(\uF439,@segoe_icon_size)'))
	    item(image=image.fluent(\uF45E,segoe_icon_size) tip=['PenTips',tip.info] cmd=command.copy('image.fluent(\uF45E,@segoe_icon_size)'))
	    item(image=image.fluent(\uF45F,segoe_icon_size) tip=['PenTipsMirrored',tip.info] cmd=command.copy('image.fluent(\uF45F,@segoe_icon_size)'))
	    item(image=image.fluent(\uF460,segoe_icon_size) tip=['HWPJoin',tip.info] cmd=command.copy('image.fluent(\uF460,@segoe_icon_size)'))
	    item(image=image.fluent(\uF461,segoe_icon_size) tip=['HWPInsert',tip.info] cmd=command.copy('image.fluent(\uF461,@segoe_icon_size)'))
	    item(image=image.fluent(\uF462,segoe_icon_size) tip=['HWPStrikeThrough',tip.info] cmd=command.copy('image.fluent(\uF462,@segoe_icon_size)'))

	    item(image=image.fluent(\uF463,segoe_icon_size) tip=['HWPScratchOut',tip.info] cmd=command.copy('image.fluent(\uF463,@segoe_icon_size)') col)
	    item(image=image.fluent(\uF464,segoe_icon_size) tip=['HWPSplit',tip.info] cmd=command.copy('image.fluent(\uF464,@segoe_icon_size)'))
	    item(image=image.fluent(\uF465,segoe_icon_size) tip=['HWPNewLine',tip.info] cmd=command.copy('image.fluent(\uF465,@segoe_icon_size)'))
	    item(image=image.fluent(\uF466,segoe_icon_size) tip=['HWPOverwrite',tip.info] cmd=command.copy('image.fluent(\uF466,@segoe_icon_size)'))
	    item(image=image.fluent(\uF473,segoe_icon_size) tip=['MobWifiWarning1',tip.info] cmd=command.copy('image.fluent(\uF473,@segoe_icon_size)'))
	    item(image=image.fluent(\uF474,segoe_icon_size) tip=['MobWifiWarning2',tip.info] cmd=command.copy('image.fluent(\uF474,@segoe_icon_size)'))
	    item(image=image.fluent(\uF475,segoe_icon_size) tip=['MobWifiWarning3',tip.info] cmd=command.copy('image.fluent(\uF475,@segoe_icon_size)'))
	    item(image=image.fluent(\uF476,segoe_icon_size) tip=['MobWifiWarning4',tip.info] cmd=command.copy('image.fluent(\uF476,@segoe_icon_size)'))
	    item(image=image.fluent(\uF47F,segoe_icon_size) tip=['MicLocationCombo',tip.info] cmd=command.copy('image.fluent(\uF47F,@segoe_icon_size)'))
	    item(image=image.fluent(\uF49A,segoe_icon_size) tip=['Globe2',tip.info] cmd=command.copy('image.fluent(\uF49A,@segoe_icon_size)'))
	    item(image=image.fluent(\uF4A5,segoe_icon_size) tip=['SpecialEffectSize',tip.info] cmd=command.copy('image.fluent(\uF4A5,@segoe_icon_size)'))
	    item(image=image.fluent(\uF4A9,segoe_icon_size) tip=['GIF',tip.info] cmd=command.copy('image.fluent(\uF4A9,@segoe_icon_size)'))
	    item(image=image.fluent(\uF4AA,segoe_icon_size) tip=['Sticker2',tip.info] cmd=command.copy('image.fluent(\uF4AA,@segoe_icon_size)'))
	    item(image=image.fluent(\uF4BE,segoe_icon_size) tip=['SurfaceHubSelected',tip.info] cmd=command.copy('image.fluent(\uF4BE,@segoe_icon_size)'))
	    item(image=image.fluent(\uF4BF,segoe_icon_size) tip=['HoloLensSelected',tip.info] cmd=command.copy('image.fluent(\uF4BF,@segoe_icon_size)'))
	    item(image=image.fluent(\uF4C0,segoe_icon_size) tip=['Earbud',tip.info] cmd=command.copy('image.fluent(\uF4C0,@segoe_icon_size)'))

	    item(image=image.fluent(\uF4C3,segoe_icon_size) tip=['MixVolumes',tip.info] cmd=command.copy('image.fluent(\uF4C3,@segoe_icon_size)') col)
	    item(image=image.fluent(\uF540,segoe_icon_size) tip=['Safe',tip.info] cmd=command.copy('image.fluent(\uF540,@segoe_icon_size)'))
	    item(image=image.fluent(\uF552,segoe_icon_size) tip=['LaptopSecure',tip.info] cmd=command.copy('image.fluent(\uF552,@segoe_icon_size)'))
	    item(image=image.fluent(\uF56D,segoe_icon_size) tip=['PrintDefault',tip.info] cmd=command.copy('image.fluent(\uF56D,@segoe_icon_size)'))
	    item(image=image.fluent(\uF56E,segoe_icon_size) tip=['PageMirrored',tip.info] cmd=command.copy('image.fluent(\uF56E,@segoe_icon_size)'))
	    item(image=image.fluent(\uF56F,segoe_icon_size) tip=['LandscapeOrientationMirrored',tip.info] cmd=command.copy('image.fluent(\uF56F,@segoe_icon_size)'))
	    item(image=image.fluent(\uF570,segoe_icon_size) tip=['ColorOff',tip.info] cmd=command.copy('image.fluent(\uF570,@segoe_icon_size)'))
	    item(image=image.fluent(\uF571,segoe_icon_size) tip=['PrintAllPages',tip.info] cmd=command.copy('image.fluent(\uF571,@segoe_icon_size)'))
	    item(image=image.fluent(\uF572,segoe_icon_size) tip=['PrintCustomRange',tip.info] cmd=command.copy('image.fluent(\uF572,@segoe_icon_size)'))
	    item(image=image.fluent(\uF573,segoe_icon_size) tip=['PageMarginPortraitNarrow',tip.info] cmd=command.copy('image.fluent(\uF573,@segoe_icon_size)'))
	    item(image=image.fluent(\uF574,segoe_icon_size) tip=['PageMarginPortraitNormal',tip.info] cmd=command.copy('image.fluent(\uF574,@segoe_icon_size)'))
	    item(image=image.fluent(\uF575,segoe_icon_size) tip=['PageMarginPortraitModerate',tip.info] cmd=command.copy('image.fluent(\uF575,@segoe_icon_size)'))
	    item(image=image.fluent(\uF576,segoe_icon_size) tip=['PageMarginPortraitWide',tip.info] cmd=command.copy('image.fluent(\uF576,@segoe_icon_size)'))
	    item(image=image.fluent(\uF577,segoe_icon_size) tip=['PageMarginLandscapeNarrow',tip.info] cmd=command.copy('image.fluent(\uF577,@segoe_icon_size)'))
	    item(image=image.fluent(\uF578,segoe_icon_size) tip=['PageMarginLandscapeNormal',tip.info] cmd=command.copy('image.fluent(\uF578,@segoe_icon_size)'))
	    item(image=image.fluent(\uF579,segoe_icon_size) tip=['PageMarginLandscapeModerate',tip.info] cmd=command.copy('image.fluent(\uF579,@segoe_icon_size)'))

	    item(image=image.fluent(\uF57A,segoe_icon_size) tip=['PageMarginLandscapeWide',tip.info] cmd=command.copy('image.fluent(\uF57A,@segoe_icon_size)') col)
	    item(image=image.fluent(\uF57B,segoe_icon_size) tip=['CollateLandscape',tip.info] cmd=command.copy('image.fluent(\uF57B,@segoe_icon_size)'))
	    item(image=image.fluent(\uF57C,segoe_icon_size) tip=['CollatePortrait',tip.info] cmd=command.copy('image.fluent(\uF57C,@segoe_icon_size)'))
	    item(image=image.fluent(\uF57D,segoe_icon_size) tip=['CollatePortraitSeparated',tip.info] cmd=command.copy('image.fluent(\uF57D,@segoe_icon_size)'))
	    item(image=image.fluent(\uF57E,segoe_icon_size) tip=['DuplexLandscapeOneSided',tip.info] cmd=command.copy('image.fluent(\uF57E,@segoe_icon_size)'))
	    item(image=image.fluent(\uF57F,segoe_icon_size) tip=['DuplexLandscapeOneSidedMirrored',tip.info] cmd=command.copy('image.fluent(\uF57F,@segoe_icon_size)'))
	    item(image=image.fluent(\uF580,segoe_icon_size) tip=['DuplexLandscapeTwoSidedLongEdge',tip.info] cmd=command.copy('image.fluent(\uF580,@segoe_icon_size)'))
	    item(image=image.fluent(\uF581,segoe_icon_size) tip=['DuplexLandscapeTwoSidedLongEdgeMirrored',tip.info] cmd=command.copy('image.fluent(\uF581,@segoe_icon_size)'))
	    item(image=image.fluent(\uF582,segoe_icon_size) tip=['DuplexLandscapeTwoSidedShortEdge',tip.info] cmd=command.copy('image.fluent(\uF582,@segoe_icon_size)'))
	    item(image=image.fluent(\uF583,segoe_icon_size) tip=['DuplexLandscapeTwoSidedShortEdgeMirrored',tip.info] cmd=command.copy('image.fluent(\uF583,@segoe_icon_size)'))
	    item(image=image.fluent(\uF584,segoe_icon_size) tip=['DuplexPortraitOneSided',tip.info] cmd=command.copy('image.fluent(\uF584,@segoe_icon_size)'))
	    item(image=image.fluent(\uF585,segoe_icon_size) tip=['DuplexPortraitOneSidedMirrored',tip.info] cmd=command.copy('image.fluent(\uF585,@segoe_icon_size)'))
	    item(image=image.fluent(\uF586,segoe_icon_size) tip=['DuplexPortraitTwoSidedLongEdge',tip.info] cmd=command.copy('image.fluent(\uF586,@segoe_icon_size)'))
	    item(image=image.fluent(\uF587,segoe_icon_size) tip=['DuplexPortraitTwoSidedLongEdgeMirrored',tip.info] cmd=command.copy('image.fluent(\uF587,@segoe_icon_size)'))
	    item(image=image.fluent(\uF588,segoe_icon_size) tip=['DuplexPortraitTwoSidedShortEdge',tip.info] cmd=command.copy('image.fluent(\uF588,@segoe_icon_size)'))
	    item(image=image.fluent(\uF589,segoe_icon_size) tip=['DuplexPortraitTwoSidedShortEdgeMirrored',tip.info] cmd=command.copy('image.fluent(\uF589,@segoe_icon_size)'))

	    item(image=image.fluent(\uF58A,segoe_icon_size) tip=['PPSOneLandscape',tip.info] cmd=command.copy('image.fluent(\uF58A,@segoe_icon_size)') col)
	    item(image=image.fluent(\uF58B,segoe_icon_size) tip=['PPSTwoLandscape',tip.info] cmd=command.copy('image.fluent(\uF58B,@segoe_icon_size)'))
	    item(image=image.fluent(\uF58C,segoe_icon_size) tip=['PPSTwoPortrait',tip.info] cmd=command.copy('image.fluent(\uF58C,@segoe_icon_size)'))
	    item(image=image.fluent(\uF58D,segoe_icon_size) tip=['PPSFourLandscape',tip.info] cmd=command.copy('image.fluent(\uF58D,@segoe_icon_size)'))
	    item(image=image.fluent(\uF58E,segoe_icon_size) tip=['PPSFourPortrait',tip.info] cmd=command.copy('image.fluent(\uF58E,@segoe_icon_size)'))
	    item(image=image.fluent(\uF58F,segoe_icon_size) tip=['HolePunchOff',tip.info] cmd=command.copy('image.fluent(\uF58F,@segoe_icon_size)'))
	    item(image=image.fluent(\uF590,segoe_icon_size) tip=['HolePunchPortraitLeft',tip.info] cmd=command.copy('image.fluent(\uF590,@segoe_icon_size)'))
	    item(image=image.fluent(\uF591,segoe_icon_size) tip=['HolePunchPortraitRight',tip.info] cmd=command.copy('image.fluent(\uF591,@segoe_icon_size)'))
	    item(image=image.fluent(\uF592,segoe_icon_size) tip=['HolePunchPortraitTop',tip.info] cmd=command.copy('image.fluent(\uF592,@segoe_icon_size)'))
	    item(image=image.fluent(\uF593,segoe_icon_size) tip=['HolePunchPortraitBottom',tip.info] cmd=command.copy('image.fluent(\uF593,@segoe_icon_size)'))
	    item(image=image.fluent(\uF594,segoe_icon_size) tip=['HolePunchLandscapeLeft',tip.info] cmd=command.copy('image.fluent(\uF594,@segoe_icon_size)'))
	    item(image=image.fluent(\uF595,segoe_icon_size) tip=['HolePunchLandscapeRight',tip.info] cmd=command.copy('image.fluent(\uF595,@segoe_icon_size)'))
	    item(image=image.fluent(\uF596,segoe_icon_size) tip=['HolePunchLandscapeTop',tip.info] cmd=command.copy('image.fluent(\uF596,@segoe_icon_size)'))
	    item(image=image.fluent(\uF597,segoe_icon_size) tip=['HolePunchLandscapeBottom',tip.info] cmd=command.copy('image.fluent(\uF597,@segoe_icon_size)'))
	    item(image=image.fluent(\uF598,segoe_icon_size) tip=['StaplingOff',tip.info] cmd=command.copy('image.fluent(\uF598,@segoe_icon_size)'))
	    item(image=image.fluent(\uF599,segoe_icon_size) tip=['StaplingPortraitTopLeft',tip.info] cmd=command.copy('image.fluent(\uF599,@segoe_icon_size)'))

	    item(image=image.fluent(\uF59A,segoe_icon_size) tip=['StaplingPortraitTopRight',tip.info] cmd=command.copy('image.fluent(\uF59A,@segoe_icon_size)') col)
	    item(image=image.fluent(\uF59B,segoe_icon_size) tip=['StaplingPortraitBottomRight',tip.info] cmd=command.copy('image.fluent(\uF59B,@segoe_icon_size)'))
	    item(image=image.fluent(\uF59C,segoe_icon_size) tip=['StaplingPortraitTwoLeft',tip.info] cmd=command.copy('image.fluent(\uF59C,@segoe_icon_size)'))
	    item(image=image.fluent(\uF59D,segoe_icon_size) tip=['StaplingPortraitTwoRight',tip.info] cmd=command.copy('image.fluent(\uF59D,@segoe_icon_size)'))
	    item(image=image.fluent(\uF59E,segoe_icon_size) tip=['StaplingPortraitTwoTop',tip.info] cmd=command.copy('image.fluent(\uF59E,@segoe_icon_size)'))
	    item(image=image.fluent(\uF59F,segoe_icon_size) tip=['StaplingPortraitTwoBottom',tip.info] cmd=command.copy('image.fluent(\uF59F,@segoe_icon_size)'))
	    item(image=image.fluent(\uF5A0,segoe_icon_size) tip=['StaplingPortraitBookBinding',tip.info] cmd=command.copy('image.fluent(\uF5A0,@segoe_icon_size)'))
	    item(image=image.fluent(\uF5A1,segoe_icon_size) tip=['StaplingLandscapeTopLeft',tip.info] cmd=command.copy('image.fluent(\uF5A1,@segoe_icon_size)'))
	    item(image=image.fluent(\uF5A2,segoe_icon_size) tip=['StaplingLandscapeTopRight',tip.info] cmd=command.copy('image.fluent(\uF5A2,@segoe_icon_size)'))
	    item(image=image.fluent(\uF5A3,segoe_icon_size) tip=['StaplingLandscapeBottomLeft',tip.info] cmd=command.copy('image.fluent(\uF5A3,@segoe_icon_size)'))
	    item(image=image.fluent(\uF5A4,segoe_icon_size) tip=['StaplingLandscapeBottomRight',tip.info] cmd=command.copy('image.fluent(\uF5A4,@segoe_icon_size)'))
	    item(image=image.fluent(\uF5A5,segoe_icon_size) tip=['StaplingLandscapeTwoLeft',tip.info] cmd=command.copy('image.fluent(\uF5A5,@segoe_icon_size)'))
	    item(image=image.fluent(\uF5A6,segoe_icon_size) tip=['StaplingLandscapeTwoRight',tip.info] cmd=command.copy('image.fluent(\uF5A6,@segoe_icon_size)'))
	    item(image=image.fluent(\uF5A7,segoe_icon_size) tip=['StaplingLandscapeTwoTop',tip.info] cmd=command.copy('image.fluent(\uF5A7,@segoe_icon_size)'))
	    item(image=image.fluent(\uF5A8,segoe_icon_size) tip=['StaplingLandscapeTwoBottom',tip.info] cmd=command.copy('image.fluent(\uF5A8,@segoe_icon_size)'))
	    item(image=image.fluent(\uF5A9,segoe_icon_size) tip=['StaplingLandscapeBookBinding',tip.info] cmd=command.copy('image.fluent(\uF5A9,@segoe_icon_size)'))
	}

	separator

	menu(title='MDL2 #1')
	{
		item(image=image.mdl(\uE700,segoe_icon_size) tip=['GlobalNavigationButton',tip.info] cmd=command.copy('image.mdl(\uE700,@segoe_icon_size)'))
		item(image=image.mdl(\uE701,segoe_icon_size) tip=['Wifi',tip.info] cmd=command.copy('image.mdl(\uE701,@segoe_icon_size)'))
		item(image=image.mdl(\uE702,segoe_icon_size) tip=['Bluetooth',tip.info] cmd=command.copy('image.mdl(\uE702,@segoe_icon_size)'))
		item(image=image.mdl(\uE703,segoe_icon_size) tip=['Connect',tip.info] cmd=command.copy('image.mdl(\uE703,@segoe_icon_size)'))
		item(image=image.mdl(\uE704,segoe_icon_size) tip=['InternetSharing',tip.info] cmd=command.copy('image.mdl(\uE704,@segoe_icon_size)'))
		item(image=image.mdl(\uE705,segoe_icon_size) tip=['VPN',tip.info] cmd=command.copy('image.mdl(\uE705,@segoe_icon_size)'))
		item(image=image.mdl(\uE706,segoe_icon_size) tip=['Brightness',tip.info] cmd=command.copy('image.mdl(\uE706,@segoe_icon_size)'))
		item(image=image.mdl(\uE707,segoe_icon_size) tip=['MapPin',tip.info] cmd=command.copy('image.mdl(\uE707,@segoe_icon_size)'))
		item(image=image.mdl(\uE708,segoe_icon_size) tip=['QuietHours',tip.info] cmd=command.copy('image.mdl(\uE708,@segoe_icon_size)'))
		item(image=image.mdl(\uE709,segoe_icon_size) tip=['Airplane',tip.info] cmd=command.copy('image.mdl(\uE709,@segoe_icon_size)'))
		item(image=image.mdl(\uE70A,segoe_icon_size) tip=['Tablet',tip.info] cmd=command.copy('image.mdl(\uE70A,@segoe_icon_size)'))
		item(image=image.mdl(\uE70B,segoe_icon_size) tip=['QuickNote',tip.info] cmd=command.copy('image.mdl(\uE70B,@segoe_icon_size)'))
		item(image=image.mdl(\uE70C,segoe_icon_size) tip=['RememberedDevice',tip.info] cmd=command.copy('image.mdl(\uE70C,@segoe_icon_size)'))
		item(image=image.mdl(\uE70D,segoe_icon_size) tip=['ChevronDown',tip.info] cmd=command.copy('image.mdl(\uE70D,@segoe_icon_size)'))
		item(image=image.mdl(\uE70E,segoe_icon_size) tip=['ChevronUp',tip.info] cmd=command.copy('image.mdl(\uE70E,@segoe_icon_size)'))
		item(image=image.mdl(\uE70F,segoe_icon_size) tip=['Edit',tip.info] cmd=command.copy('image.mdl(\uE70F,@segoe_icon_size)'))

		item(image=image.mdl(\uE710,segoe_icon_size) tip=['Add',tip.info] cmd=command.copy('image.mdl(\uE710,@segoe_icon_size)') col)
		item(image=image.mdl(\uE711,segoe_icon_size) tip=['Cancel',tip.info] cmd=command.copy('image.mdl(\uE711,@segoe_icon_size)'))
		item(image=image.mdl(\uE712,segoe_icon_size) tip=['More',tip.info] cmd=command.copy('image.mdl(\uE712,@segoe_icon_size)'))
		item(image=image.mdl(\uE713,segoe_icon_size) tip=['Setting',tip.info] cmd=command.copy('image.mdl(\uE713,@segoe_icon_size)'))
		item(image=image.mdl(\uE714,segoe_icon_size) tip=['Video',tip.info] cmd=command.copy('image.mdl(\uE714,@segoe_icon_size)'))
		item(image=image.mdl(\uE715,segoe_icon_size) tip=['Mail',tip.info] cmd=command.copy('image.mdl(\uE715,@segoe_icon_size)'))
		item(image=image.mdl(\uE716,segoe_icon_size) tip=['People',tip.info] cmd=command.copy('image.mdl(\uE716,@segoe_icon_size)'))
		item(image=image.mdl(\uE717,segoe_icon_size) tip=['Phone',tip.info] cmd=command.copy('image.mdl(\uE717,@segoe_icon_size)'))
		item(image=image.mdl(\uE718,segoe_icon_size) tip=['Pin',tip.info] cmd=command.copy('image.mdl(\uE718,@segoe_icon_size)'))
		item(image=image.mdl(\uE719,segoe_icon_size) tip=['Shop',tip.info] cmd=command.copy('image.mdl(\uE719,@segoe_icon_size)'))
		item(image=image.mdl(\uE71A,segoe_icon_size) tip=['Stop',tip.info] cmd=command.copy('image.mdl(\uE71A,@segoe_icon_size)'))
		item(image=image.mdl(\uE71B,segoe_icon_size) tip=['Link',tip.info] cmd=command.copy('image.mdl(\uE71B,@segoe_icon_size)'))
		item(image=image.mdl(\uE71C,segoe_icon_size) tip=['Filter',tip.info] cmd=command.copy('image.mdl(\uE71C,@segoe_icon_size)'))
		item(image=image.mdl(\uE71D,segoe_icon_size) tip=['AllApps',tip.info] cmd=command.copy('image.mdl(\uE71D,@segoe_icon_size)'))
		item(image=image.mdl(\uE71E,segoe_icon_size) tip=['Zoom',tip.info] cmd=command.copy('image.mdl(\uE71E,@segoe_icon_size)'))
		item(image=image.mdl(\uE71F,segoe_icon_size) tip=['ZoomOut',tip.info] cmd=command.copy('image.mdl(\uE71F,@segoe_icon_size)'))

		item(image=image.mdl(\uE720,segoe_icon_size) tip=['Microphone',tip.info] cmd=command.copy('image.mdl(\uE720,@segoe_icon_size)') col)
		item(image=image.mdl(\uE721,segoe_icon_size) tip=['Search',tip.info] cmd=command.copy('image.mdl(\uE721,@segoe_icon_size)'))
		item(image=image.mdl(\uE722,segoe_icon_size) tip=['Camera',tip.info] cmd=command.copy('image.mdl(\uE722,@segoe_icon_size)'))
		item(image=image.mdl(\uE723,segoe_icon_size) tip=['Attach',tip.info] cmd=command.copy('image.mdl(\uE723,@segoe_icon_size)'))
		item(image=image.mdl(\uE724,segoe_icon_size) tip=['Send',tip.info] cmd=command.copy('image.mdl(\uE724,@segoe_icon_size)'))
		item(image=image.mdl(\uE725,segoe_icon_size) tip=['SendFill',tip.info] cmd=command.copy('image.mdl(\uE725,@segoe_icon_size)'))
		item(image=image.mdl(\uE726,segoe_icon_size) tip=['WalkSolid',tip.info] cmd=command.copy('image.mdl(\uE726,@segoe_icon_size)'))
		item(image=image.mdl(\uE727,segoe_icon_size) tip=['InPrivate',tip.info] cmd=command.copy('image.mdl(\uE727,@segoe_icon_size)'))
		item(image=image.mdl(\uE728,segoe_icon_size) tip=['FavoriteList',tip.info] cmd=command.copy('image.mdl(\uE728,@segoe_icon_size)'))
		item(image=image.mdl(\uE729,segoe_icon_size) tip=['PageSolid',tip.info] cmd=command.copy('image.mdl(\uE729,@segoe_icon_size)'))
		item(image=image.mdl(\uE72A,segoe_icon_size) tip=['Forward',tip.info] cmd=command.copy('image.mdl(\uE72A,@segoe_icon_size)'))
		item(image=image.mdl(\uE72B,segoe_icon_size) tip=['Back',tip.info] cmd=command.copy('image.mdl(\uE72B,@segoe_icon_size)'))
		item(image=image.mdl(\uE72C,segoe_icon_size) tip=['Refresh',tip.info] cmd=command.copy('image.mdl(\uE72C,@segoe_icon_size)'))
		item(image=image.mdl(\uE72D,segoe_icon_size) tip=['Share',tip.info] cmd=command.copy('image.mdl(\uE72D,@segoe_icon_size)'))
		item(image=image.mdl(\uE72E,segoe_icon_size) tip=['Lock',tip.info] cmd=command.copy('image.mdl(\uE72E,@segoe_icon_size)'))
		item(image=image.mdl(\uE730,segoe_icon_size) tip=['ReportHacked',tip.info] cmd=command.copy('image.mdl(\uE730,@segoe_icon_size)'))

		item(image=image.mdl(\uE731,segoe_icon_size) tip=['EMI',tip.info] cmd=command.copy('image.mdl(\uE731,@segoe_icon_size)') col)
		item(image=image.mdl(\uE734,segoe_icon_size) tip=['FavoriteStar',tip.info] cmd=command.copy('image.mdl(\uE734,@segoe_icon_size)'))
		item(image=image.mdl(\uE735,segoe_icon_size) tip=['FavoriteStarFill',tip.info] cmd=command.copy('image.mdl(\uE735,@segoe_icon_size)'))
		item(image=image.mdl(\uE736,segoe_icon_size) tip=['ReadingMode',tip.info] cmd=command.copy('image.mdl(\uE736,@segoe_icon_size)'))
		item(image=image.mdl(\uE737,segoe_icon_size) tip=['Favicon',tip.info] cmd=command.copy('image.mdl(\uE737,@segoe_icon_size)'))
		item(image=image.mdl(\uE738,segoe_icon_size) tip=['Remove',tip.info] cmd=command.copy('image.mdl(\uE738,@segoe_icon_size)'))
		item(image=image.mdl(\uE739,segoe_icon_size) tip=['Checkbox',tip.info] cmd=command.copy('image.mdl(\uE739,@segoe_icon_size)'))
		item(image=image.mdl(\uE73A,segoe_icon_size) tip=['CheckboxComposite',tip.info] cmd=command.copy('image.mdl(\uE73A,@segoe_icon_size)'))
		item(image=image.mdl(\uE73B,segoe_icon_size) tip=['CheckboxFill',tip.info] cmd=command.copy('image.mdl(\uE73B,@segoe_icon_size)'))
		item(image=image.mdl(\uE73C,segoe_icon_size) tip=['CheckboxIndeterminate',tip.info] cmd=command.copy('image.mdl(\uE73C,@segoe_icon_size)'))
		item(image=image.mdl(\uE73D,segoe_icon_size) tip=['CheckboxCompositeReversed',tip.info] cmd=command.copy('image.mdl(\uE73D,@segoe_icon_size)'))
		item(image=image.mdl(\uE73E,segoe_icon_size) tip=['CheckMark',tip.info] cmd=command.copy('image.mdl(\uE73E,@segoe_icon_size)'))
		item(image=image.mdl(\uE73F,segoe_icon_size) tip=['BackToWindow',tip.info] cmd=command.copy('image.mdl(\uE73F,@segoe_icon_size)'))
		item(image=image.mdl(\uE740,segoe_icon_size) tip=['FullScreen',tip.info] cmd=command.copy('image.mdl(\uE740,@segoe_icon_size)'))
		item(image=image.mdl(\uE741,segoe_icon_size) tip=['ResizeTouchLarger',tip.info] cmd=command.copy('image.mdl(\uE741,@segoe_icon_size)'))
		item(image=image.mdl(\uE742,segoe_icon_size) tip=['ResizeTouchSmaller',tip.info] cmd=command.copy('image.mdl(\uE742,@segoe_icon_size)'))

		item(image=image.mdl(\uE743,segoe_icon_size) tip=['ResizeMouseSmall',tip.info] cmd=command.copy('image.mdl(\uE743,@segoe_icon_size)') col)
		item(image=image.mdl(\uE744,segoe_icon_size) tip=['ResizeMouseMedium',tip.info] cmd=command.copy('image.mdl(\uE744,@segoe_icon_size)'))
		item(image=image.mdl(\uE745,segoe_icon_size) tip=['ResizeMouseWide',tip.info] cmd=command.copy('image.mdl(\uE745,@segoe_icon_size)'))
		item(image=image.mdl(\uE746,segoe_icon_size) tip=['ResizeMouseTall',tip.info] cmd=command.copy('image.mdl(\uE746,@segoe_icon_size)'))
		item(image=image.mdl(\uE747,segoe_icon_size) tip=['ResizeMouseLarge',tip.info] cmd=command.copy('image.mdl(\uE747,@segoe_icon_size)'))
		item(image=image.mdl(\uE748,segoe_icon_size) tip=['SwitchUser',tip.info] cmd=command.copy('image.mdl(\uE748,@segoe_icon_size)'))
		item(image=image.mdl(\uE749,segoe_icon_size) tip=['Print',tip.info] cmd=command.copy('image.mdl(\uE749,@segoe_icon_size)'))
		item(image=image.mdl(\uE74A,segoe_icon_size) tip=['Up',tip.info] cmd=command.copy('image.mdl(\uE74A,@segoe_icon_size)'))
		item(image=image.mdl(\uE74B,segoe_icon_size) tip=['Down',tip.info] cmd=command.copy('image.mdl(\uE74B,@segoe_icon_size)'))
		item(image=image.mdl(\uE74C,segoe_icon_size) tip=['OEM',tip.info] cmd=command.copy('image.mdl(\uE74C,@segoe_icon_size)'))
		item(image=image.mdl(\uE74D,segoe_icon_size) tip=['Delete',tip.info] cmd=command.copy('image.mdl(\uE74D,@segoe_icon_size)'))
		item(image=image.mdl(\uE74E,segoe_icon_size) tip=['Save',tip.info] cmd=command.copy('image.mdl(\uE74E,@segoe_icon_size)'))
		item(image=image.mdl(\uE74F,segoe_icon_size) tip=['Mute',tip.info] cmd=command.copy('image.mdl(\uE74F,@segoe_icon_size)'))
		item(image=image.mdl(\uE750,segoe_icon_size) tip=['BackSpaceQWERTY',tip.info] cmd=command.copy('image.mdl(\uE750,@segoe_icon_size)'))
		item(image=image.mdl(\uE751,segoe_icon_size) tip=['ReturnKey',tip.info] cmd=command.copy('image.mdl(\uE751,@segoe_icon_size)'))
		item(image=image.mdl(\uE752,segoe_icon_size) tip=['UpArrowShiftKey',tip.info] cmd=command.copy('image.mdl(\uE752,@segoe_icon_size)'))

		item(image=image.mdl(\uE753,segoe_icon_size) tip=['Cloud',tip.info] cmd=command.copy('image.mdl(\uE753,@segoe_icon_size)') col)
		item(image=image.mdl(\uE754,segoe_icon_size) tip=['Flashlight',tip.info] cmd=command.copy('image.mdl(\uE754,@segoe_icon_size)'))
		item(image=image.mdl(\uE755,segoe_icon_size) tip=['RotationLock',tip.info] cmd=command.copy('image.mdl(\uE755,@segoe_icon_size)'))
		item(image=image.mdl(\uE756,segoe_icon_size) tip=['CommandPrompt',tip.info] cmd=command.copy('image.mdl(\uE756,@segoe_icon_size)'))
		item(image=image.mdl(\uE759,segoe_icon_size) tip=['SIPMove',tip.info] cmd=command.copy('image.mdl(\uE759,@segoe_icon_size)'))
		item(image=image.mdl(\uE75A,segoe_icon_size) tip=['SIPUndock',tip.info] cmd=command.copy('image.mdl(\uE75A,@segoe_icon_size)'))
		item(image=image.mdl(\uE75B,segoe_icon_size) tip=['SIPRedock',tip.info] cmd=command.copy('image.mdl(\uE75B,@segoe_icon_size)'))
		item(image=image.mdl(\uE75C,segoe_icon_size) tip=['EraseTool',tip.info] cmd=command.copy('image.mdl(\uE75C,@segoe_icon_size)'))
		item(image=image.mdl(\uE75D,segoe_icon_size) tip=['UnderscoreSpace',tip.info] cmd=command.copy('image.mdl(\uE75D,@segoe_icon_size)'))
		item(image=image.mdl(\uE75E,segoe_icon_size) tip=['GripperTool',tip.info] cmd=command.copy('image.mdl(\uE75E,@segoe_icon_size)'))
		item(image=image.mdl(\uE75F,segoe_icon_size) tip=['Dialpad',tip.info] cmd=command.copy('image.mdl(\uE75F,@segoe_icon_size)'))
		item(image=image.mdl(\uE760,segoe_icon_size) tip=['PageLeft',tip.info] cmd=command.copy('image.mdl(\uE760,@segoe_icon_size)'))
		item(image=image.mdl(\uE761,segoe_icon_size) tip=['PageRight',tip.info] cmd=command.copy('image.mdl(\uE761,@segoe_icon_size)'))
		item(image=image.mdl(\uE762,segoe_icon_size) tip=['MultiSelect',tip.info] cmd=command.copy('image.mdl(\uE762,@segoe_icon_size)'))
		item(image=image.mdl(\uE763,segoe_icon_size) tip=['KeyboardLeftHanded',tip.info] cmd=command.copy('image.mdl(\uE763,@segoe_icon_size)'))
		item(image=image.mdl(\uE764,segoe_icon_size) tip=['KeyboardRightHanded',tip.info] cmd=command.copy('image.mdl(\uE764,@segoe_icon_size)'))

		item(image=image.mdl(\uE765,segoe_icon_size) tip=['KeyboardClassic',tip.info] cmd=command.copy('image.mdl(\uE765,@segoe_icon_size)') col)
		item(image=image.mdl(\uE766,segoe_icon_size) tip=['KeyboardSplit',tip.info] cmd=command.copy('image.mdl(\uE766,@segoe_icon_size)'))
		item(image=image.mdl(\uE767,segoe_icon_size) tip=['Volume',tip.info] cmd=command.copy('image.mdl(\uE767,@segoe_icon_size)'))
		item(image=image.mdl(\uE768,segoe_icon_size) tip=['Play',tip.info] cmd=command.copy('image.mdl(\uE768,@segoe_icon_size)'))
		item(image=image.mdl(\uE769,segoe_icon_size) tip=['Pause',tip.info] cmd=command.copy('image.mdl(\uE769,@segoe_icon_size)'))
		item(image=image.mdl(\uE76B,segoe_icon_size) tip=['ChevronLeft',tip.info] cmd=command.copy('image.mdl(\uE76B,@segoe_icon_size)'))
		item(image=image.mdl(\uE76C,segoe_icon_size) tip=['ChevronRight',tip.info] cmd=command.copy('image.mdl(\uE76C,@segoe_icon_size)'))
		item(image=image.mdl(\uE76D,segoe_icon_size) tip=['InkingTool',tip.info] cmd=command.copy('image.mdl(\uE76D,@segoe_icon_size)'))
		item(image=image.mdl(\uE76E,segoe_icon_size) tip=['Emoji2',tip.info] cmd=command.copy('image.mdl(\uE76E,@segoe_icon_size)'))
		item(image=image.mdl(\uE76F,segoe_icon_size) tip=['GripperBarHorizontal',tip.info] cmd=command.copy('image.mdl(\uE76F,@segoe_icon_size)'))
		item(image=image.mdl(\uE770,segoe_icon_size) tip=['System',tip.info] cmd=command.copy('image.mdl(\uE770,@segoe_icon_size)'))
		item(image=image.mdl(\uE771,segoe_icon_size) tip=['Personalize',tip.info] cmd=command.copy('image.mdl(\uE771,@segoe_icon_size)'))
		item(image=image.mdl(\uE772,segoe_icon_size) tip=['Devices',tip.info] cmd=command.copy('image.mdl(\uE772,@segoe_icon_size)'))
		item(image=image.mdl(\uE773,segoe_icon_size) tip=['SearchAndApps',tip.info] cmd=command.copy('image.mdl(\uE773,@segoe_icon_size)'))
		item(image=image.mdl(\uE774,segoe_icon_size) tip=['Globe',tip.info] cmd=command.copy('image.mdl(\uE774,@segoe_icon_size)'))
		item(image=image.mdl(\uE775,segoe_icon_size) tip=['TimeLanguage',tip.info] cmd=command.copy('image.mdl(\uE775,@segoe_icon_size)'))

		item(image=image.mdl(\uE776,segoe_icon_size) tip=['EaseOfAccess',tip.info] cmd=command.copy('image.mdl(\uE776,@segoe_icon_size)') col)
		item(image=image.mdl(\uE777,segoe_icon_size) tip=['UpdateRestore',tip.info] cmd=command.copy('image.mdl(\uE777,@segoe_icon_size)'))
		item(image=image.mdl(\uE778,segoe_icon_size) tip=['HangUp',tip.info] cmd=command.copy('image.mdl(\uE778,@segoe_icon_size)'))
		item(image=image.mdl(\uE779,segoe_icon_size) tip=['ContactInfo',tip.info] cmd=command.copy('image.mdl(\uE779,@segoe_icon_size)'))
		item(image=image.mdl(\uE77A,segoe_icon_size) tip=['Unpin',tip.info] cmd=command.copy('image.mdl(\uE77A,@segoe_icon_size)'))
		item(image=image.mdl(\uE77B,segoe_icon_size) tip=['Contact',tip.info] cmd=command.copy('image.mdl(\uE77B,@segoe_icon_size)'))
		item(image=image.mdl(\uE77C,segoe_icon_size) tip=['Memo',tip.info] cmd=command.copy('image.mdl(\uE77C,@segoe_icon_size)'))
		item(image=image.mdl(\uE77E,segoe_icon_size) tip=['IncomingCall',tip.info] cmd=command.copy('image.mdl(\uE77E,@segoe_icon_size)'))
		item(image=image.mdl(\uE77F,segoe_icon_size) tip=['Paste',tip.info] cmd=command.copy('image.mdl(\uE77F,@segoe_icon_size)'))
		item(image=image.mdl(\uE780,segoe_icon_size) tip=['PhoneBook',tip.info] cmd=command.copy('image.mdl(\uE780,@segoe_icon_size)'))
		item(image=image.mdl(\uE781,segoe_icon_size) tip=['LEDLight',tip.info] cmd=command.copy('image.mdl(\uE781,@segoe_icon_size)'))
		item(image=image.mdl(\uE783,segoe_icon_size) tip=['Error',tip.info] cmd=command.copy('image.mdl(\uE783,@segoe_icon_size)'))
		item(image=image.mdl(\uE784,segoe_icon_size) tip=['GripperBarVertical',tip.info] cmd=command.copy('image.mdl(\uE784,@segoe_icon_size)'))
		item(image=image.mdl(\uE785,segoe_icon_size) tip=['Unlock',tip.info] cmd=command.copy('image.mdl(\uE785,@segoe_icon_size)'))
		item(image=image.mdl(\uE786,segoe_icon_size) tip=['Slideshow',tip.info] cmd=command.copy('image.mdl(\uE786,@segoe_icon_size)'))
		item(image=image.mdl(\uE787,segoe_icon_size) tip=['Calendar',tip.info] cmd=command.copy('image.mdl(\uE787,@segoe_icon_size)'))

		item(image=image.mdl(\uE788,segoe_icon_size) tip=['GripperResize',tip.info] cmd=command.copy('image.mdl(\uE788,@segoe_icon_size)') col)
		item(image=image.mdl(\uE789,segoe_icon_size) tip=['Megaphone',tip.info] cmd=command.copy('image.mdl(\uE789,@segoe_icon_size)'))
		item(image=image.mdl(\uE78A,segoe_icon_size) tip=['Trim',tip.info] cmd=command.copy('image.mdl(\uE78A,@segoe_icon_size)'))
		item(image=image.mdl(\uE78B,segoe_icon_size) tip=['NewWindow',tip.info] cmd=command.copy('image.mdl(\uE78B,@segoe_icon_size)'))
		item(image=image.mdl(\uE78C,segoe_icon_size) tip=['SaveLocal',tip.info] cmd=command.copy('image.mdl(\uE78C,@segoe_icon_size)'))
		item(image=image.mdl(\uE790,segoe_icon_size) tip=['Color',tip.info] cmd=command.copy('image.mdl(\uE790,@segoe_icon_size)'))
		item(image=image.mdl(\uE791,segoe_icon_size) tip=['DataSense',tip.info] cmd=command.copy('image.mdl(\uE791,@segoe_icon_size)'))
		item(image=image.mdl(\uE792,segoe_icon_size) tip=['SaveAs',tip.info] cmd=command.copy('image.mdl(\uE792,@segoe_icon_size)'))
		item(image=image.mdl(\uE793,segoe_icon_size) tip=['Light',tip.info] cmd=command.copy('image.mdl(\uE793,@segoe_icon_size)'))
		item(image=image.mdl(\uE799,segoe_icon_size) tip=['AspectRatio',tip.info] cmd=command.copy('image.mdl(\uE799,@segoe_icon_size)'))
		item(image=image.mdl(\uE7A5,segoe_icon_size) tip=['DataSenseBar',tip.info] cmd=command.copy('image.mdl(\uE7A5,@segoe_icon_size)'))
		item(image=image.mdl(\uE7A6,segoe_icon_size) tip=['Redo',tip.info] cmd=command.copy('image.mdl(\uE7A6,@segoe_icon_size)'))
		item(image=image.mdl(\uE7A7,segoe_icon_size) tip=['Undo',tip.info] cmd=command.copy('image.mdl(\uE7A7,@segoe_icon_size)'))
		item(image=image.mdl(\uE7A8,segoe_icon_size) tip=['Crop',tip.info] cmd=command.copy('image.mdl(\uE7A8,@segoe_icon_size)'))
		item(image=image.mdl(\uE7AC,segoe_icon_size) tip=['OpenWith',tip.info] cmd=command.copy('image.mdl(\uE7AC,@segoe_icon_size)'))
		item(image=image.mdl(\uE7AD,segoe_icon_size) tip=['Rotate',tip.info] cmd=command.copy('image.mdl(\uE7AD,@segoe_icon_size)'))

		item(image=image.mdl(\uE7B3,segoe_icon_size) tip=['RedEye',tip.info] cmd=command.copy('image.mdl(\uE7B3,@segoe_icon_size)') col)
		item(image=image.mdl(\uE7B5,segoe_icon_size) tip=['SetlockScreen',tip.info] cmd=command.copy('image.mdl(\uE7B5,@segoe_icon_size)'))
		item(image=image.mdl(\uE7B7,segoe_icon_size) tip=['MapPin2',tip.info] cmd=command.copy('image.mdl(\uE7B7,@segoe_icon_size)'))
		item(image=image.mdl(\uE7B8,segoe_icon_size) tip=['Package',tip.info] cmd=command.copy('image.mdl(\uE7B8,@segoe_icon_size)'))
		item(image=image.mdl(\uE7BA,segoe_icon_size) tip=['Warning',tip.info] cmd=command.copy('image.mdl(\uE7BA,@segoe_icon_size)'))
		item(image=image.mdl(\uE7BC,segoe_icon_size) tip=['ReadingList',tip.info] cmd=command.copy('image.mdl(\uE7BC,@segoe_icon_size)'))
		item(image=image.mdl(\uE7BE,segoe_icon_size) tip=['Education',tip.info] cmd=command.copy('image.mdl(\uE7BE,@segoe_icon_size)'))
		item(image=image.mdl(\uE7BF,segoe_icon_size) tip=['ShoppingCart',tip.info] cmd=command.copy('image.mdl(\uE7BF,@segoe_icon_size)'))
		item(image=image.mdl(\uE7C0,segoe_icon_size) tip=['Train',tip.info] cmd=command.copy('image.mdl(\uE7C0,@segoe_icon_size)'))
		item(image=image.mdl(\uE7C1,segoe_icon_size) tip=['Flag',tip.info] cmd=command.copy('image.mdl(\uE7C1,@segoe_icon_size)'))
		item(image=image.mdl(\uE7C3,segoe_icon_size) tip=['Page',tip.info] cmd=command.copy('image.mdl(\uE7C3,@segoe_icon_size)'))
		item(image=image.mdl(\uE7C4,segoe_icon_size) tip=['TaskView',tip.info] cmd=command.copy('image.mdl(\uE7C4,@segoe_icon_size)'))
		item(image=image.mdl(\uE7C5,segoe_icon_size) tip=['BrowsePhotos',tip.info] cmd=command.copy('image.mdl(\uE7C5,@segoe_icon_size)'))
		item(image=image.mdl(\uE7C6,segoe_icon_size) tip=['HalfStarLeft',tip.info] cmd=command.copy('image.mdl(\uE7C6,@segoe_icon_size)'))
		item(image=image.mdl(\uE7C7,segoe_icon_size) tip=['HalfStarRight',tip.info] cmd=command.copy('image.mdl(\uE7C7,@segoe_icon_size)'))
		item(image=image.mdl(\uE7C8,segoe_icon_size) tip=['Record',tip.info] cmd=command.copy('image.mdl(\uE7C8,@segoe_icon_size)'))

		item(image=image.mdl(\uE7C9,segoe_icon_size) tip=['TouchPointer',tip.info] cmd=command.copy('image.mdl(\uE7C9,@segoe_icon_size)') col)
		item(image=image.mdl(\uE7DE,segoe_icon_size) tip=['LangJPN',tip.info] cmd=command.copy('image.mdl(\uE7DE,@segoe_icon_size)'))
		item(image=image.mdl(\uE7E3,segoe_icon_size) tip=['Ferry',tip.info] cmd=command.copy('image.mdl(\uE7E3,@segoe_icon_size)'))
		item(image=image.mdl(\uE7E6,segoe_icon_size) tip=['Highlight',tip.info] cmd=command.copy('image.mdl(\uE7E6,@segoe_icon_size)'))
		item(image=image.mdl(\uE7E7,segoe_icon_size) tip=['ActionCenterNotification',tip.info] cmd=command.copy('image.mdl(\uE7E7,@segoe_icon_size)'))
		item(image=image.mdl(\uE7E8,segoe_icon_size) tip=['PowerButton',tip.info] cmd=command.copy('image.mdl(\uE7E8,@segoe_icon_size)'))
		item(image=image.mdl(\uE7EA,segoe_icon_size) tip=['ResizeTouchNarrower',tip.info] cmd=command.copy('image.mdl(\uE7EA,@segoe_icon_size)'))
		item(image=image.mdl(\uE7EB,segoe_icon_size) tip=['ResizeTouchShorter',tip.info] cmd=command.copy('image.mdl(\uE7EB,@segoe_icon_size)'))
		item(image=image.mdl(\uE7EC,segoe_icon_size) tip=['DrivingMode',tip.info] cmd=command.copy('image.mdl(\uE7EC,@segoe_icon_size)'))
		item(image=image.mdl(\uE7ED,segoe_icon_size) tip=['RingerSilent',tip.info] cmd=command.copy('image.mdl(\uE7ED,@segoe_icon_size)'))
		item(image=image.mdl(\uE7EE,segoe_icon_size) tip=['OtherUser',tip.info] cmd=command.copy('image.mdl(\uE7EE,@segoe_icon_size)'))
		item(image=image.mdl(\uE7EF,segoe_icon_size) tip=['Admin',tip.info] cmd=command.copy('image.mdl(\uE7EF,@segoe_icon_size)'))
		item(image=image.mdl(\uE7F0,segoe_icon_size) tip=['CC',tip.info] cmd=command.copy('image.mdl(\uE7F0,@segoe_icon_size)'))
		item(image=image.mdl(\uE7F1,segoe_icon_size) tip=['SDCard',tip.info] cmd=command.copy('image.mdl(\uE7F1,@segoe_icon_size)'))
		item(image=image.mdl(\uE7F2,segoe_icon_size) tip=['CallForwarding',tip.info] cmd=command.copy('image.mdl(\uE7F2,@segoe_icon_size)'))
		item(image=image.mdl(\uE7F3,segoe_icon_size) tip=['SettingsDisplaySound',tip.info] cmd=command.copy('image.mdl(\uE7F3,@segoe_icon_size)'))

		item(image=image.mdl(\uE7F4,segoe_icon_size) tip=['TVMonitor',tip.info] cmd=command.copy('image.mdl(\uE7F4,@segoe_icon_size)') col)
		item(image=image.mdl(\uE7F5,segoe_icon_size) tip=['Speakers',tip.info] cmd=command.copy('image.mdl(\uE7F5,@segoe_icon_size)'))
		item(image=image.mdl(\uE7F6,segoe_icon_size) tip=['Headphone',tip.info] cmd=command.copy('image.mdl(\uE7F6,@segoe_icon_size)'))
		item(image=image.mdl(\uE7F7,segoe_icon_size) tip=['DeviceLaptopPic',tip.info] cmd=command.copy('image.mdl(\uE7F7,@segoe_icon_size)'))
		item(image=image.mdl(\uE7F8,segoe_icon_size) tip=['DeviceLaptopNoPic',tip.info] cmd=command.copy('image.mdl(\uE7F8,@segoe_icon_size)'))
		item(image=image.mdl(\uE7F9,segoe_icon_size) tip=['DeviceMonitorRightPic',tip.info] cmd=command.copy('image.mdl(\uE7F9,@segoe_icon_size)'))
		item(image=image.mdl(\uE7FA,segoe_icon_size) tip=['DeviceMonitorLeftPic',tip.info] cmd=command.copy('image.mdl(\uE7FA,@segoe_icon_size)'))
		item(image=image.mdl(\uE7FB,segoe_icon_size) tip=['DeviceMonitorNoPic',tip.info] cmd=command.copy('image.mdl(\uE7FB,@segoe_icon_size)'))
		item(image=image.mdl(\uE7FC,segoe_icon_size) tip=['Game',tip.info] cmd=command.copy('image.mdl(\uE7FC,@segoe_icon_size)'))
		item(image=image.mdl(\uE7FD,segoe_icon_size) tip=['HorizontalTabKey',tip.info] cmd=command.copy('image.mdl(\uE7FD,@segoe_icon_size)'))
		item(image=image.mdl(\uE802,segoe_icon_size) tip=['StreetsideSplitMinimize',tip.info] cmd=command.copy('image.mdl(\uE802,@segoe_icon_size)'))
		item(image=image.mdl(\uE803,segoe_icon_size) tip=['StreetsideSplitExpand',tip.info] cmd=command.copy('image.mdl(\uE803,@segoe_icon_size)'))
		item(image=image.mdl(\uE804,segoe_icon_size) tip=['Car',tip.info] cmd=command.copy('image.mdl(\uE804,@segoe_icon_size)'))
		item(image=image.mdl(\uE805,segoe_icon_size) tip=['Walk',tip.info] cmd=command.copy('image.mdl(\uE805,@segoe_icon_size)'))
		item(image=image.mdl(\uE806,segoe_icon_size) tip=['Bus',tip.info] cmd=command.copy('image.mdl(\uE806,@segoe_icon_size)'))
		item(image=image.mdl(\uE809,segoe_icon_size) tip=['TiltUp',tip.info] cmd=command.copy('image.mdl(\uE809,@segoe_icon_size)'))

		item(image=image.mdl(\uE80A,segoe_icon_size) tip=['TiltDown',tip.info] cmd=command.copy('image.mdl(\uE80A,@segoe_icon_size)') col)
		item(image=image.mdl(\uE80B,segoe_icon_size) tip=['CallControl',tip.info] cmd=command.copy('image.mdl(\uE80B,@segoe_icon_size)'))
		item(image=image.mdl(\uE80C,segoe_icon_size) tip=['RotateMapRight',tip.info] cmd=command.copy('image.mdl(\uE80C,@segoe_icon_size)'))
		item(image=image.mdl(\uE80D,segoe_icon_size) tip=['RotateMapLeft',tip.info] cmd=command.copy('image.mdl(\uE80D,@segoe_icon_size)'))
		item(image=image.mdl(\uE80F,segoe_icon_size) tip=['Home',tip.info] cmd=command.copy('image.mdl(\uE80F,@segoe_icon_size)'))
		item(image=image.mdl(\uE811,segoe_icon_size) tip=['ParkingLocation',tip.info] cmd=command.copy('image.mdl(\uE811,@segoe_icon_size)'))
		item(image=image.mdl(\uE812,segoe_icon_size) tip=['MapCompassTop',tip.info] cmd=command.copy('image.mdl(\uE812,@segoe_icon_size)'))
		item(image=image.mdl(\uE813,segoe_icon_size) tip=['MapCompassBottom',tip.info] cmd=command.copy('image.mdl(\uE813,@segoe_icon_size)'))
		item(image=image.mdl(\uE814,segoe_icon_size) tip=['IncidentTriangle',tip.info] cmd=command.copy('image.mdl(\uE814,@segoe_icon_size)'))
		item(image=image.mdl(\uE815,segoe_icon_size) tip=['Touch',tip.info] cmd=command.copy('image.mdl(\uE815,@segoe_icon_size)'))
		item(image=image.mdl(\uE816,segoe_icon_size) tip=['MapDirections',tip.info] cmd=command.copy('image.mdl(\uE816,@segoe_icon_size)'))
		item(image=image.mdl(\uE819,segoe_icon_size) tip=['StartPoint',tip.info] cmd=command.copy('image.mdl(\uE819,@segoe_icon_size)'))
		item(image=image.mdl(\uE81A,segoe_icon_size) tip=['StopPoint',tip.info] cmd=command.copy('image.mdl(\uE81A,@segoe_icon_size)'))
		item(image=image.mdl(\uE81B,segoe_icon_size) tip=['EndPoint',tip.info] cmd=command.copy('image.mdl(\uE81B,@segoe_icon_size)'))
		item(image=image.mdl(\uE81C,segoe_icon_size) tip=['History',tip.info] cmd=command.copy('image.mdl(\uE81C,@segoe_icon_size)'))
		item(image=image.mdl(\uE81D,segoe_icon_size) tip=['Location',tip.info] cmd=command.copy('image.mdl(\uE81D,@segoe_icon_size)'))

		item(image=image.mdl(\uE81E,segoe_icon_size) tip=['MapLayers',tip.info] cmd=command.copy('image.mdl(\uE81E,@segoe_icon_size)') col)
		item(image=image.mdl(\uE81F,segoe_icon_size) tip=['Accident',tip.info] cmd=command.copy('image.mdl(\uE81F,@segoe_icon_size)'))
		item(image=image.mdl(\uE821,segoe_icon_size) tip=['Work',tip.info] cmd=command.copy('image.mdl(\uE821,@segoe_icon_size)'))
		item(image=image.mdl(\uE822,segoe_icon_size) tip=['Construction',tip.info] cmd=command.copy('image.mdl(\uE822,@segoe_icon_size)'))
		item(image=image.mdl(\uE823,segoe_icon_size) tip=['Recent',tip.info] cmd=command.copy('image.mdl(\uE823,@segoe_icon_size)'))
		item(image=image.mdl(\uE825,segoe_icon_size) tip=['Bank',tip.info] cmd=command.copy('image.mdl(\uE825,@segoe_icon_size)'))
		item(image=image.mdl(\uE826,segoe_icon_size) tip=['DownloadMap',tip.info] cmd=command.copy('image.mdl(\uE826,@segoe_icon_size)'))
		item(image=image.mdl(\uE829,segoe_icon_size) tip=['InkingToolFill2',tip.info] cmd=command.copy('image.mdl(\uE829,@segoe_icon_size)'))
		item(image=image.mdl(\uE82A,segoe_icon_size) tip=['HighlightFill2',tip.info] cmd=command.copy('image.mdl(\uE82A,@segoe_icon_size)'))
		item(image=image.mdl(\uE82B,segoe_icon_size) tip=['EraseToolFill',tip.info] cmd=command.copy('image.mdl(\uE82B,@segoe_icon_size)'))
		item(image=image.mdl(\uE82C,segoe_icon_size) tip=['EraseToolFill2',tip.info] cmd=command.copy('image.mdl(\uE82C,@segoe_icon_size)'))
		item(image=image.mdl(\uE82D,segoe_icon_size) tip=['Dictionary',tip.info] cmd=command.copy('image.mdl(\uE82D,@segoe_icon_size)'))
		item(image=image.mdl(\uE82E,segoe_icon_size) tip=['DictionaryAdd',tip.info] cmd=command.copy('image.mdl(\uE82E,@segoe_icon_size)'))
		item(image=image.mdl(\uE82F,segoe_icon_size) tip=['ToolTip',tip.info] cmd=command.copy('image.mdl(\uE82F,@segoe_icon_size)'))
		item(image=image.mdl(\uE830,segoe_icon_size) tip=['ChromeBack',tip.info] cmd=command.copy('image.mdl(\uE830,@segoe_icon_size)'))
		item(image=image.mdl(\uE835,segoe_icon_size) tip=['ProvisioningPackage',tip.info] cmd=command.copy('image.mdl(\uE835,@segoe_icon_size)'))

		item(image=image.mdl(\uE836,segoe_icon_size) tip=['AddRemoteDevice',tip.info] cmd=command.copy('image.mdl(\uE836,@segoe_icon_size)') col)
		item(image=image.mdl(\uE838,segoe_icon_size) tip=['FolderOpen',tip.info] cmd=command.copy('image.mdl(\uE838,@segoe_icon_size)'))
		item(image=image.mdl(\uE839,segoe_icon_size) tip=['Ethernet',tip.info] cmd=command.copy('image.mdl(\uE839,@segoe_icon_size)'))
		item(image=image.mdl(\uE83A,segoe_icon_size) tip=['ShareBroadband',tip.info] cmd=command.copy('image.mdl(\uE83A,@segoe_icon_size)'))
		item(image=image.mdl(\uE83B,segoe_icon_size) tip=['DirectAccess',tip.info] cmd=command.copy('image.mdl(\uE83B,@segoe_icon_size)'))
		item(image=image.mdl(\uE83C,segoe_icon_size) tip=['DialUp',tip.info] cmd=command.copy('image.mdl(\uE83C,@segoe_icon_size)'))
		item(image=image.mdl(\uE83D,segoe_icon_size) tip=['DefenderApp',tip.info] cmd=command.copy('image.mdl(\uE83D,@segoe_icon_size)'))
		item(image=image.mdl(\uE83E,segoe_icon_size) tip=['BatteryCharging9',tip.info] cmd=command.copy('image.mdl(\uE83E,@segoe_icon_size)'))
		item(image=image.mdl(\uE83F,segoe_icon_size) tip=['Battery10',tip.info] cmd=command.copy('image.mdl(\uE83F,@segoe_icon_size)'))
		item(image=image.mdl(\uE840,segoe_icon_size) tip=['Pinned',tip.info] cmd=command.copy('image.mdl(\uE840,@segoe_icon_size)'))
		item(image=image.mdl(\uE841,segoe_icon_size) tip=['PinFill',tip.info] cmd=command.copy('image.mdl(\uE841,@segoe_icon_size)'))
		item(image=image.mdl(\uE842,segoe_icon_size) tip=['PinnedFill',tip.info] cmd=command.copy('image.mdl(\uE842,@segoe_icon_size)'))
		item(image=image.mdl(\uE843,segoe_icon_size) tip=['PeriodKey',tip.info] cmd=command.copy('image.mdl(\uE843,@segoe_icon_size)'))
		item(image=image.mdl(\uE844,segoe_icon_size) tip=['PuncKey',tip.info] cmd=command.copy('image.mdl(\uE844,@segoe_icon_size)'))
		item(image=image.mdl(\uE845,segoe_icon_size) tip=['RevToggleKey',tip.info] cmd=command.copy('image.mdl(\uE845,@segoe_icon_size)'))
		item(image=image.mdl(\uE846,segoe_icon_size) tip=['RightArrowKeyTime1',tip.info] cmd=command.copy('image.mdl(\uE846,@segoe_icon_size)'))

		item(image=image.mdl(\uE847,segoe_icon_size) tip=['RightArrowKeyTime2',tip.info] cmd=command.copy('image.mdl(\uE847,@segoe_icon_size)') col)
		item(image=image.mdl(\uE848,segoe_icon_size) tip=['LeftQuote',tip.info] cmd=command.copy('image.mdl(\uE848,@segoe_icon_size)'))
		item(image=image.mdl(\uE849,segoe_icon_size) tip=['RightQuote',tip.info] cmd=command.copy('image.mdl(\uE849,@segoe_icon_size)'))
		item(image=image.mdl(\uE84A,segoe_icon_size) tip=['DownShiftKey',tip.info] cmd=command.copy('image.mdl(\uE84A,@segoe_icon_size)'))
		item(image=image.mdl(\uE84B,segoe_icon_size) tip=['UpShiftKey',tip.info] cmd=command.copy('image.mdl(\uE84B,@segoe_icon_size)'))
		item(image=image.mdl(\uE84C,segoe_icon_size) tip=['PuncKey0',tip.info] cmd=command.copy('image.mdl(\uE84C,@segoe_icon_size)'))
		item(image=image.mdl(\uE84D,segoe_icon_size) tip=['PuncKeyLeftBottom',tip.info] cmd=command.copy('image.mdl(\uE84D,@segoe_icon_size)'))
		item(image=image.mdl(\uE84E,segoe_icon_size) tip=['RightArrowKeyTime3',tip.info] cmd=command.copy('image.mdl(\uE84E,@segoe_icon_size)'))
		item(image=image.mdl(\uE84F,segoe_icon_size) tip=['RightArrowKeyTime4',tip.info] cmd=command.copy('image.mdl(\uE84F,@segoe_icon_size)'))
		item(image=image.mdl(\uE850,segoe_icon_size) tip=['Battery0',tip.info] cmd=command.copy('image.mdl(\uE850,@segoe_icon_size)'))
		item(image=image.mdl(\uE851,segoe_icon_size) tip=['Battery1',tip.info] cmd=command.copy('image.mdl(\uE851,@segoe_icon_size)'))
		item(image=image.mdl(\uE852,segoe_icon_size) tip=['Battery2',tip.info] cmd=command.copy('image.mdl(\uE852,@segoe_icon_size)'))
		item(image=image.mdl(\uE853,segoe_icon_size) tip=['Battery3',tip.info] cmd=command.copy('image.mdl(\uE853,@segoe_icon_size)'))
		item(image=image.mdl(\uE854,segoe_icon_size) tip=['Battery4',tip.info] cmd=command.copy('image.mdl(\uE854,@segoe_icon_size)'))
		item(image=image.mdl(\uE855,segoe_icon_size) tip=['Battery5',tip.info] cmd=command.copy('image.mdl(\uE855,@segoe_icon_size)'))
		item(image=image.mdl(\uE856,segoe_icon_size) tip=['Battery6',tip.info] cmd=command.copy('image.mdl(\uE856,@segoe_icon_size)'))
	}

	menu(title='MDL2 #2')
	{
		item(image=image.mdl(\uE857,segoe_icon_size) tip=['Battery7',tip.info] cmd=command.copy('image.mdl(\uE857,@segoe_icon_size)'))
		item(image=image.mdl(\uE858,segoe_icon_size) tip=['Battery8',tip.info] cmd=command.copy('image.mdl(\uE858,@segoe_icon_size)'))
		item(image=image.mdl(\uE859,segoe_icon_size) tip=['Battery9',tip.info] cmd=command.copy('image.mdl(\uE859,@segoe_icon_size)'))
		item(image=image.mdl(\uE85A,segoe_icon_size) tip=['BatteryCharging0',tip.info] cmd=command.copy('image.mdl(\uE85A,@segoe_icon_size)'))
		item(image=image.mdl(\uE85B,segoe_icon_size) tip=['BatteryCharging1',tip.info] cmd=command.copy('image.mdl(\uE85B,@segoe_icon_size)'))
		item(image=image.mdl(\uE85C,segoe_icon_size) tip=['BatteryCharging2',tip.info] cmd=command.copy('image.mdl(\uE85C,@segoe_icon_size)'))
		item(image=image.mdl(\uE85D,segoe_icon_size) tip=['BatteryCharging3',tip.info] cmd=command.copy('image.mdl(\uE85D,@segoe_icon_size)'))
		item(image=image.mdl(\uE85E,segoe_icon_size) tip=['BatteryCharging4',tip.info] cmd=command.copy('image.mdl(\uE85E,@segoe_icon_size)'))
		item(image=image.mdl(\uE85F,segoe_icon_size) tip=['BatteryCharging5',tip.info] cmd=command.copy('image.mdl(\uE85F,@segoe_icon_size)'))
		item(image=image.mdl(\uE860,segoe_icon_size) tip=['BatteryCharging6',tip.info] cmd=command.copy('image.mdl(\uE860,@segoe_icon_size)'))
		item(image=image.mdl(\uE861,segoe_icon_size) tip=['BatteryCharging7',tip.info] cmd=command.copy('image.mdl(\uE861,@segoe_icon_size)'))
		item(image=image.mdl(\uE862,segoe_icon_size) tip=['BatteryCharging8',tip.info] cmd=command.copy('image.mdl(\uE862,@segoe_icon_size)'))
		item(image=image.mdl(\uE863,segoe_icon_size) tip=['BatterySaver0',tip.info] cmd=command.copy('image.mdl(\uE863,@segoe_icon_size)'))
		item(image=image.mdl(\uE864,segoe_icon_size) tip=['BatterySaver1',tip.info] cmd=command.copy('image.mdl(\uE864,@segoe_icon_size)'))
		item(image=image.mdl(\uE865,segoe_icon_size) tip=['BatterySaver2',tip.info] cmd=command.copy('image.mdl(\uE865,@segoe_icon_size)'))
		item(image=image.mdl(\uE866,segoe_icon_size) tip=['BatterySaver3',tip.info] cmd=command.copy('image.mdl(\uE866,@segoe_icon_size)'))

		item(image=image.mdl(\uE867,segoe_icon_size) tip=['BatterySaver4',tip.info] cmd=command.copy('image.mdl(\uE867,@segoe_icon_size)') col)
		item(image=image.mdl(\uE868,segoe_icon_size) tip=['BatterySaver5',tip.info] cmd=command.copy('image.mdl(\uE868,@segoe_icon_size)'))
		item(image=image.mdl(\uE869,segoe_icon_size) tip=['BatterySaver6',tip.info] cmd=command.copy('image.mdl(\uE869,@segoe_icon_size)'))
		item(image=image.mdl(\uE86A,segoe_icon_size) tip=['BatterySaver7',tip.info] cmd=command.copy('image.mdl(\uE86A,@segoe_icon_size)'))
		item(image=image.mdl(\uE86B,segoe_icon_size) tip=['BatterySaver8',tip.info] cmd=command.copy('image.mdl(\uE86B,@segoe_icon_size)'))
		item(image=image.mdl(\uE86C,segoe_icon_size) tip=['SignalBars1',tip.info] cmd=command.copy('image.mdl(\uE86C,@segoe_icon_size)'))
		item(image=image.mdl(\uE86D,segoe_icon_size) tip=['SignalBars2',tip.info] cmd=command.copy('image.mdl(\uE86D,@segoe_icon_size)'))
		item(image=image.mdl(\uE86E,segoe_icon_size) tip=['SignalBars3',tip.info] cmd=command.copy('image.mdl(\uE86E,@segoe_icon_size)'))
		item(image=image.mdl(\uE86F,segoe_icon_size) tip=['SignalBars4',tip.info] cmd=command.copy('image.mdl(\uE86F,@segoe_icon_size)'))
		item(image=image.mdl(\uE870,segoe_icon_size) tip=['SignalBars5',tip.info] cmd=command.copy('image.mdl(\uE870,@segoe_icon_size)'))
		item(image=image.mdl(\uE871,segoe_icon_size) tip=['SignalNotConnected',tip.info] cmd=command.copy('image.mdl(\uE871,@segoe_icon_size)'))
		item(image=image.mdl(\uE872,segoe_icon_size) tip=['Wifi1',tip.info] cmd=command.copy('image.mdl(\uE872,@segoe_icon_size)'))
		item(image=image.mdl(\uE873,segoe_icon_size) tip=['Wifi2',tip.info] cmd=command.copy('image.mdl(\uE873,@segoe_icon_size)'))
		item(image=image.mdl(\uE874,segoe_icon_size) tip=['Wifi3',tip.info] cmd=command.copy('image.mdl(\uE874,@segoe_icon_size)'))
		item(image=image.mdl(\uE875,segoe_icon_size) tip=['MobSIMLock',tip.info] cmd=command.copy('image.mdl(\uE875,@segoe_icon_size)'))
		item(image=image.mdl(\uE876,segoe_icon_size) tip=['MobSIMMissing',tip.info] cmd=command.copy('image.mdl(\uE876,@segoe_icon_size)'))

		item(image=image.mdl(\uE877,segoe_icon_size) tip=['Vibrate',tip.info] cmd=command.copy('image.mdl(\uE877,@segoe_icon_size)') col)
		item(image=image.mdl(\uE878,segoe_icon_size) tip=['RoamingInternational',tip.info] cmd=command.copy('image.mdl(\uE878,@segoe_icon_size)'))
		item(image=image.mdl(\uE879,segoe_icon_size) tip=['RoamingDomestic',tip.info] cmd=command.copy('image.mdl(\uE879,@segoe_icon_size)'))
		item(image=image.mdl(\uE87A,segoe_icon_size) tip=['CallForwardInternational',tip.info] cmd=command.copy('image.mdl(\uE87A,@segoe_icon_size)'))
		item(image=image.mdl(\uE87B,segoe_icon_size) tip=['CallForwardRoaming',tip.info] cmd=command.copy('image.mdl(\uE87B,@segoe_icon_size)'))
		item(image=image.mdl(\uE87C,segoe_icon_size) tip=['JpnRomanji',tip.info] cmd=command.copy('image.mdl(\uE87C,@segoe_icon_size)'))
		item(image=image.mdl(\uE87D,segoe_icon_size) tip=['JpnRomanjiLock',tip.info] cmd=command.copy('image.mdl(\uE87D,@segoe_icon_size)'))
		item(image=image.mdl(\uE87E,segoe_icon_size) tip=['JpnRomanjiShift',tip.info] cmd=command.copy('image.mdl(\uE87E,@segoe_icon_size)'))
		item(image=image.mdl(\uE87F,segoe_icon_size) tip=['JpnRomanjiShiftLock',tip.info] cmd=command.copy('image.mdl(\uE87F,@segoe_icon_size)'))
		item(image=image.mdl(\uE880,segoe_icon_size) tip=['StatusDataTransfer',tip.info] cmd=command.copy('image.mdl(\uE880,@segoe_icon_size)'))
		item(image=image.mdl(\uE881,segoe_icon_size) tip=['StatusDataTransferVPN',tip.info] cmd=command.copy('image.mdl(\uE881,@segoe_icon_size)'))
		item(image=image.mdl(\uE882,segoe_icon_size) tip=['StatusDualSIM2',tip.info] cmd=command.copy('image.mdl(\uE882,@segoe_icon_size)'))
		item(image=image.mdl(\uE883,segoe_icon_size) tip=['StatusDualSIM2VPN',tip.info] cmd=command.copy('image.mdl(\uE883,@segoe_icon_size)'))
		item(image=image.mdl(\uE884,segoe_icon_size) tip=['StatusDualSIM1',tip.info] cmd=command.copy('image.mdl(\uE884,@segoe_icon_size)'))
		item(image=image.mdl(\uE885,segoe_icon_size) tip=['StatusDualSIM1VPN',tip.info] cmd=command.copy('image.mdl(\uE885,@segoe_icon_size)'))
		item(image=image.mdl(\uE886,segoe_icon_size) tip=['StatusSGLTE',tip.info] cmd=command.copy('image.mdl(\uE886,@segoe_icon_size)'))

		item(image=image.mdl(\uE887,segoe_icon_size) tip=['StatusSGLTECell',tip.info] cmd=command.copy('image.mdl(\uE887,@segoe_icon_size)') col)
		item(image=image.mdl(\uE888,segoe_icon_size) tip=['StatusSGLTEDataVPN',tip.info] cmd=command.copy('image.mdl(\uE888,@segoe_icon_size)'))
		item(image=image.mdl(\uE889,segoe_icon_size) tip=['StatusVPN',tip.info] cmd=command.copy('image.mdl(\uE889,@segoe_icon_size)'))
		item(image=image.mdl(\uE88A,segoe_icon_size) tip=['WifiHotspot',tip.info] cmd=command.copy('image.mdl(\uE88A,@segoe_icon_size)'))
		item(image=image.mdl(\uE88B,segoe_icon_size) tip=['LanguageKor',tip.info] cmd=command.copy('image.mdl(\uE88B,@segoe_icon_size)'))
		item(image=image.mdl(\uE88C,segoe_icon_size) tip=['LanguageCht',tip.info] cmd=command.copy('image.mdl(\uE88C,@segoe_icon_size)'))
		item(image=image.mdl(\uE88D,segoe_icon_size) tip=['LanguageChs',tip.info] cmd=command.copy('image.mdl(\uE88D,@segoe_icon_size)'))
		item(image=image.mdl(\uE88E,segoe_icon_size) tip=['USB',tip.info] cmd=command.copy('image.mdl(\uE88E,@segoe_icon_size)'))
		item(image=image.mdl(\uE88F,segoe_icon_size) tip=['InkingToolFill',tip.info] cmd=command.copy('image.mdl(\uE88F,@segoe_icon_size)'))
		item(image=image.mdl(\uE890,segoe_icon_size) tip=['View',tip.info] cmd=command.copy('image.mdl(\uE890,@segoe_icon_size)'))
		item(image=image.mdl(\uE891,segoe_icon_size) tip=['HighlightFill',tip.info] cmd=command.copy('image.mdl(\uE891,@segoe_icon_size)'))
		item(image=image.mdl(\uE892,segoe_icon_size) tip=['Previous',tip.info] cmd=command.copy('image.mdl(\uE892,@segoe_icon_size)'))
		item(image=image.mdl(\uE893,segoe_icon_size) tip=['Next',tip.info] cmd=command.copy('image.mdl(\uE893,@segoe_icon_size)'))
		item(image=image.mdl(\uE894,segoe_icon_size) tip=['Clear',tip.info] cmd=command.copy('image.mdl(\uE894,@segoe_icon_size)'))
		item(image=image.mdl(\uE895,segoe_icon_size) tip=['Sync',tip.info] cmd=command.copy('image.mdl(\uE895,@segoe_icon_size)'))
		item(image=image.mdl(\uE896,segoe_icon_size) tip=['Download',tip.info] cmd=command.copy('image.mdl(\uE896,@segoe_icon_size)'))

		item(image=image.mdl(\uE897,segoe_icon_size) tip=['Help',tip.info] cmd=command.copy('image.mdl(\uE897,@segoe_icon_size)') col)
		item(image=image.mdl(\uE898,segoe_icon_size) tip=['Upload',tip.info] cmd=command.copy('image.mdl(\uE898,@segoe_icon_size)'))
		item(image=image.mdl(\uE899,segoe_icon_size) tip=['Emoji',tip.info] cmd=command.copy('image.mdl(\uE899,@segoe_icon_size)'))
		item(image=image.mdl(\uE89A,segoe_icon_size) tip=['TwoPage',tip.info] cmd=command.copy('image.mdl(\uE89A,@segoe_icon_size)'))
		item(image=image.mdl(\uE89B,segoe_icon_size) tip=['LeaveChat',tip.info] cmd=command.copy('image.mdl(\uE89B,@segoe_icon_size)'))
		item(image=image.mdl(\uE89C,segoe_icon_size) tip=['MailForward',tip.info] cmd=command.copy('image.mdl(\uE89C,@segoe_icon_size)'))
		item(image=image.mdl(\uE89E,segoe_icon_size) tip=['RotateCamera',tip.info] cmd=command.copy('image.mdl(\uE89E,@segoe_icon_size)'))
		item(image=image.mdl(\uE89F,segoe_icon_size) tip=['ClosePane',tip.info] cmd=command.copy('image.mdl(\uE89F,@segoe_icon_size)'))
		item(image=image.mdl(\uE8A0,segoe_icon_size) tip=['OpenPane',tip.info] cmd=command.copy('image.mdl(\uE8A0,@segoe_icon_size)'))
		item(image=image.mdl(\uE8A1,segoe_icon_size) tip=['PreviewLink',tip.info] cmd=command.copy('image.mdl(\uE8A1,@segoe_icon_size)'))
		item(image=image.mdl(\uE8A2,segoe_icon_size) tip=['AttachCamera',tip.info] cmd=command.copy('image.mdl(\uE8A2,@segoe_icon_size)'))
		item(image=image.mdl(\uE8A3,segoe_icon_size) tip=['ZoomIn',tip.info] cmd=command.copy('image.mdl(\uE8A3,@segoe_icon_size)'))
		item(image=image.mdl(\uE8A4,segoe_icon_size) tip=['Bookmarks',tip.info] cmd=command.copy('image.mdl(\uE8A4,@segoe_icon_size)'))
		item(image=image.mdl(\uE8A5,segoe_icon_size) tip=['Document',tip.info] cmd=command.copy('image.mdl(\uE8A5,@segoe_icon_size)'))
		item(image=image.mdl(\uE8A6,segoe_icon_size) tip=['ProtectedDocument',tip.info] cmd=command.copy('image.mdl(\uE8A6,@segoe_icon_size)'))
		item(image=image.mdl(\uE8A7,segoe_icon_size) tip=['OpenInNewWindow',tip.info] cmd=command.copy('image.mdl(\uE8A7,@segoe_icon_size)'))

		item(image=image.mdl(\uE8A8,segoe_icon_size) tip=['MailFill',tip.info] cmd=command.copy('image.mdl(\uE8A8,@segoe_icon_size)') col)
		item(image=image.mdl(\uE8A9,segoe_icon_size) tip=['ViewAll',tip.info] cmd=command.copy('image.mdl(\uE8A9,@segoe_icon_size)'))
		item(image=image.mdl(\uE8AA,segoe_icon_size) tip=['VideoChat',tip.info] cmd=command.copy('image.mdl(\uE8AA,@segoe_icon_size)'))
		item(image=image.mdl(\uE8AB,segoe_icon_size) tip=['Switch',tip.info] cmd=command.copy('image.mdl(\uE8AB,@segoe_icon_size)'))
		item(image=image.mdl(\uE8AC,segoe_icon_size) tip=['Rename',tip.info] cmd=command.copy('image.mdl(\uE8AC,@segoe_icon_size)'))
		item(image=image.mdl(\uE8AD,segoe_icon_size) tip=['Go',tip.info] cmd=command.copy('image.mdl(\uE8AD,@segoe_icon_size)'))
		item(image=image.mdl(\uE8AE,segoe_icon_size) tip=['SurfaceHub',tip.info] cmd=command.copy('image.mdl(\uE8AE,@segoe_icon_size)'))
		item(image=image.mdl(\uE8AF,segoe_icon_size) tip=['Remote',tip.info] cmd=command.copy('image.mdl(\uE8AF,@segoe_icon_size)'))
		item(image=image.mdl(\uE8B0,segoe_icon_size) tip=['Click',tip.info] cmd=command.copy('image.mdl(\uE8B0,@segoe_icon_size)'))
		item(image=image.mdl(\uE8B1,segoe_icon_size) tip=['Shuffle',tip.info] cmd=command.copy('image.mdl(\uE8B1,@segoe_icon_size)'))
		item(image=image.mdl(\uE8B2,segoe_icon_size) tip=['Movies',tip.info] cmd=command.copy('image.mdl(\uE8B2,@segoe_icon_size)'))
		item(image=image.mdl(\uE8B3,segoe_icon_size) tip=['SelectAll',tip.info] cmd=command.copy('image.mdl(\uE8B3,@segoe_icon_size)'))
		item(image=image.mdl(\uE8B4,segoe_icon_size) tip=['Orientation',tip.info] cmd=command.copy('image.mdl(\uE8B4,@segoe_icon_size)'))
		item(image=image.mdl(\uE8B5,segoe_icon_size) tip=['Import',tip.info] cmd=command.copy('image.mdl(\uE8B5,@segoe_icon_size)'))
		item(image=image.mdl(\uE8B6,segoe_icon_size) tip=['ImportAll',tip.info] cmd=command.copy('image.mdl(\uE8B6,@segoe_icon_size)'))
		item(image=image.mdl(\uE8B7,segoe_icon_size) tip=['Folder',tip.info] cmd=command.copy('image.mdl(\uE8B7,@segoe_icon_size)'))

		item(image=image.mdl(\uE8B8,segoe_icon_size) tip=['Webcam',tip.info] cmd=command.copy('image.mdl(\uE8B8,@segoe_icon_size)') col)
		item(image=image.mdl(\uE8B9,segoe_icon_size) tip=['Picture',tip.info] cmd=command.copy('image.mdl(\uE8B9,@segoe_icon_size)'))
		item(image=image.mdl(\uE8BA,segoe_icon_size) tip=['Caption',tip.info] cmd=command.copy('image.mdl(\uE8BA,@segoe_icon_size)'))
		item(image=image.mdl(\uE8BB,segoe_icon_size) tip=['ChromeClose',tip.info] cmd=command.copy('image.mdl(\uE8BB,@segoe_icon_size)'))
		item(image=image.mdl(\uE8BC,segoe_icon_size) tip=['ShowResults',tip.info] cmd=command.copy('image.mdl(\uE8BC,@segoe_icon_size)'))
		item(image=image.mdl(\uE8BD,segoe_icon_size) tip=['Message',tip.info] cmd=command.copy('image.mdl(\uE8BD,@segoe_icon_size)'))
		item(image=image.mdl(\uE8BE,segoe_icon_size) tip=['Leaf',tip.info] cmd=command.copy('image.mdl(\uE8BE,@segoe_icon_size)'))
		item(image=image.mdl(\uE8BF,segoe_icon_size) tip=['CalendarDay',tip.info] cmd=command.copy('image.mdl(\uE8BF,@segoe_icon_size)'))
		item(image=image.mdl(\uE8C0,segoe_icon_size) tip=['CalendarWeek',tip.info] cmd=command.copy('image.mdl(\uE8C0,@segoe_icon_size)'))
		item(image=image.mdl(\uE8C1,segoe_icon_size) tip=['Characters',tip.info] cmd=command.copy('image.mdl(\uE8C1,@segoe_icon_size)'))
		item(image=image.mdl(\uE8C2,segoe_icon_size) tip=['MailReplyAll',tip.info] cmd=command.copy('image.mdl(\uE8C2,@segoe_icon_size)'))
		item(image=image.mdl(\uE8C3,segoe_icon_size) tip=['Read',tip.info] cmd=command.copy('image.mdl(\uE8C3,@segoe_icon_size)'))
		item(image=image.mdl(\uE8C4,segoe_icon_size) tip=['ShowBcc',tip.info] cmd=command.copy('image.mdl(\uE8C4,@segoe_icon_size)'))
		item(image=image.mdl(\uE8C5,segoe_icon_size) tip=['HideBcc',tip.info] cmd=command.copy('image.mdl(\uE8C5,@segoe_icon_size)'))
		item(image=image.mdl(\uE8C6,segoe_icon_size) tip=['Cut',tip.info] cmd=command.copy('image.mdl(\uE8C6,@segoe_icon_size)'))
		item(image=image.mdl(\uE8C7,segoe_icon_size) tip=['PaymentCard',tip.info] cmd=command.copy('image.mdl(\uE8C7,@segoe_icon_size)'))

		item(image=image.mdl(\uE8C8,segoe_icon_size) tip=['Copy',tip.info] cmd=command.copy('image.mdl(\uE8C8,@segoe_icon_size)') col)
		item(image=image.mdl(\uE8C9,segoe_icon_size) tip=['Important',tip.info] cmd=command.copy('image.mdl(\uE8C9,@segoe_icon_size)'))
		item(image=image.mdl(\uE8CA,segoe_icon_size) tip=['MailReply',tip.info] cmd=command.copy('image.mdl(\uE8CA,@segoe_icon_size)'))
		item(image=image.mdl(\uE8CB,segoe_icon_size) tip=['Sort',tip.info] cmd=command.copy('image.mdl(\uE8CB,@segoe_icon_size)'))
		item(image=image.mdl(\uE8CC,segoe_icon_size) tip=['MobileTablet',tip.info] cmd=command.copy('image.mdl(\uE8CC,@segoe_icon_size)'))
		item(image=image.mdl(\uE8CD,segoe_icon_size) tip=['DisconnectDrive',tip.info] cmd=command.copy('image.mdl(\uE8CD,@segoe_icon_size)'))
		item(image=image.mdl(\uE8CE,segoe_icon_size) tip=['MapDrive',tip.info] cmd=command.copy('image.mdl(\uE8CE,@segoe_icon_size)'))
		item(image=image.mdl(\uE8CF,segoe_icon_size) tip=['ContactPresence',tip.info] cmd=command.copy('image.mdl(\uE8CF,@segoe_icon_size)'))
		item(image=image.mdl(\uE8D0,segoe_icon_size) tip=['Priority',tip.info] cmd=command.copy('image.mdl(\uE8D0,@segoe_icon_size)'))
		item(image=image.mdl(\uE8D1,segoe_icon_size) tip=['GotoToday',tip.info] cmd=command.copy('image.mdl(\uE8D1,@segoe_icon_size)'))
		item(image=image.mdl(\uE8D2,segoe_icon_size) tip=['Font',tip.info] cmd=command.copy('image.mdl(\uE8D2,@segoe_icon_size)'))
		item(image=image.mdl(\uE8D3,segoe_icon_size) tip=['FontColor',tip.info] cmd=command.copy('image.mdl(\uE8D3,@segoe_icon_size)'))
		item(image=image.mdl(\uE8D4,segoe_icon_size) tip=['Contact2',tip.info] cmd=command.copy('image.mdl(\uE8D4,@segoe_icon_size)'))
		item(image=image.mdl(\uE8D5,segoe_icon_size) tip=['FolderFill',tip.info] cmd=command.copy('image.mdl(\uE8D5,@segoe_icon_size)'))
		item(image=image.mdl(\uE8D6,segoe_icon_size) tip=['Audio',tip.info] cmd=command.copy('image.mdl(\uE8D6,@segoe_icon_size)'))
		item(image=image.mdl(\uE8D7,segoe_icon_size) tip=['Permissions',tip.info] cmd=command.copy('image.mdl(\uE8D7,@segoe_icon_size)'))

		item(image=image.mdl(\uE8D8,segoe_icon_size) tip=['DisableUpdates',tip.info] cmd=command.copy('image.mdl(\uE8D8,@segoe_icon_size)') col)
		item(image=image.mdl(\uE8D9,segoe_icon_size) tip=['Unfavorite',tip.info] cmd=command.copy('image.mdl(\uE8D9,@segoe_icon_size)'))
		item(image=image.mdl(\uE8DA,segoe_icon_size) tip=['OpenLocal',tip.info] cmd=command.copy('image.mdl(\uE8DA,@segoe_icon_size)'))
		item(image=image.mdl(\uE8DB,segoe_icon_size) tip=['Italic',tip.info] cmd=command.copy('image.mdl(\uE8DB,@segoe_icon_size)'))
		item(image=image.mdl(\uE8DC,segoe_icon_size) tip=['Underline',tip.info] cmd=command.copy('image.mdl(\uE8DC,@segoe_icon_size)'))
		item(image=image.mdl(\uE8DD,segoe_icon_size) tip=['Bold',tip.info] cmd=command.copy('image.mdl(\uE8DD,@segoe_icon_size)'))
		item(image=image.mdl(\uE8DE,segoe_icon_size) tip=['MoveToFolder',tip.info] cmd=command.copy('image.mdl(\uE8DE,@segoe_icon_size)'))
		item(image=image.mdl(\uE8DF,segoe_icon_size) tip=['LikeDislike',tip.info] cmd=command.copy('image.mdl(\uE8DF,@segoe_icon_size)'))
		item(image=image.mdl(\uE8E0,segoe_icon_size) tip=['Dislike',tip.info] cmd=command.copy('image.mdl(\uE8E0,@segoe_icon_size)'))
		item(image=image.mdl(\uE8E1,segoe_icon_size) tip=['Like',tip.info] cmd=command.copy('image.mdl(\uE8E1,@segoe_icon_size)'))
		item(image=image.mdl(\uE8E2,segoe_icon_size) tip=['AlignRight',tip.info] cmd=command.copy('image.mdl(\uE8E2,@segoe_icon_size)'))
		item(image=image.mdl(\uE8E3,segoe_icon_size) tip=['AlignCenter',tip.info] cmd=command.copy('image.mdl(\uE8E3,@segoe_icon_size)'))
		item(image=image.mdl(\uE8E4,segoe_icon_size) tip=['AlignLeft',tip.info] cmd=command.copy('image.mdl(\uE8E4,@segoe_icon_size)'))
		item(image=image.mdl(\uE8E5,segoe_icon_size) tip=['OpenFile',tip.info] cmd=command.copy('image.mdl(\uE8E5,@segoe_icon_size)'))
		item(image=image.mdl(\uE8E6,segoe_icon_size) tip=['ClearSelection',tip.info] cmd=command.copy('image.mdl(\uE8E6,@segoe_icon_size)'))
		item(image=image.mdl(\uE8E7,segoe_icon_size) tip=['FontDecrease',tip.info] cmd=command.copy('image.mdl(\uE8E7,@segoe_icon_size)'))

		item(image=image.mdl(\uE8E8,segoe_icon_size) tip=['FontIncrease',tip.info] cmd=command.copy('image.mdl(\uE8E8,@segoe_icon_size)') col)
		item(image=image.mdl(\uE8E9,segoe_icon_size) tip=['FontSize',tip.info] cmd=command.copy('image.mdl(\uE8E9,@segoe_icon_size)'))
		item(image=image.mdl(\uE8EA,segoe_icon_size) tip=['CellPhone',tip.info] cmd=command.copy('image.mdl(\uE8EA,@segoe_icon_size)'))
		item(image=image.mdl(\uE8EB,segoe_icon_size) tip=['Reshare',tip.info] cmd=command.copy('image.mdl(\uE8EB,@segoe_icon_size)'))
		item(image=image.mdl(\uE8EC,segoe_icon_size) tip=['Tag',tip.info] cmd=command.copy('image.mdl(\uE8EC,@segoe_icon_size)'))
		item(image=image.mdl(\uE8ED,segoe_icon_size) tip=['RepeatOne',tip.info] cmd=command.copy('image.mdl(\uE8ED,@segoe_icon_size)'))
		item(image=image.mdl(\uE8EE,segoe_icon_size) tip=['RepeatAll',tip.info] cmd=command.copy('image.mdl(\uE8EE,@segoe_icon_size)'))
		item(image=image.mdl(\uE8EF,segoe_icon_size) tip=['Calculator',tip.info] cmd=command.copy('image.mdl(\uE8EF,@segoe_icon_size)'))
		item(image=image.mdl(\uE8F0,segoe_icon_size) tip=['Directions',tip.info] cmd=command.copy('image.mdl(\uE8F0,@segoe_icon_size)'))
		item(image=image.mdl(\uE8F1,segoe_icon_size) tip=['Library',tip.info] cmd=command.copy('image.mdl(\uE8F1,@segoe_icon_size)'))
		item(image=image.mdl(\uE8F2,segoe_icon_size) tip=['ChatBubbles',tip.info] cmd=command.copy('image.mdl(\uE8F2,@segoe_icon_size)'))
		item(image=image.mdl(\uE8F3,segoe_icon_size) tip=['PostUpdate',tip.info] cmd=command.copy('image.mdl(\uE8F3,@segoe_icon_size)'))
		item(image=image.mdl(\uE8F4,segoe_icon_size) tip=['NewFolder',tip.info] cmd=command.copy('image.mdl(\uE8F4,@segoe_icon_size)'))
		item(image=image.mdl(\uE8F5,segoe_icon_size) tip=['CalendarReply',tip.info] cmd=command.copy('image.mdl(\uE8F5,@segoe_icon_size)'))
		item(image=image.mdl(\uE8F6,segoe_icon_size) tip=['UnsyncFolder',tip.info] cmd=command.copy('image.mdl(\uE8F6,@segoe_icon_size)'))
		item(image=image.mdl(\uE8F7,segoe_icon_size) tip=['SyncFolder',tip.info] cmd=command.copy('image.mdl(\uE8F7,@segoe_icon_size)'))

		item(image=image.mdl(\uE8F8,segoe_icon_size) tip=['BlockContact',tip.info] cmd=command.copy('image.mdl(\uE8F8,@segoe_icon_size)') col)
		item(image=image.mdl(\uE8F9,segoe_icon_size) tip=['SwitchApps',tip.info] cmd=command.copy('image.mdl(\uE8F9,@segoe_icon_size)'))
		item(image=image.mdl(\uE8FA,segoe_icon_size) tip=['AddFriend',tip.info] cmd=command.copy('image.mdl(\uE8FA,@segoe_icon_size)'))
		item(image=image.mdl(\uE8FB,segoe_icon_size) tip=['Accept',tip.info] cmd=command.copy('image.mdl(\uE8FB,@segoe_icon_size)'))
		item(image=image.mdl(\uE8FC,segoe_icon_size) tip=['GoToStart',tip.info] cmd=command.copy('image.mdl(\uE8FC,@segoe_icon_size)'))
		item(image=image.mdl(\uE8FD,segoe_icon_size) tip=['BulletedList',tip.info] cmd=command.copy('image.mdl(\uE8FD,@segoe_icon_size)'))
		item(image=image.mdl(\uE8FE,segoe_icon_size) tip=['Scan',tip.info] cmd=command.copy('image.mdl(\uE8FE,@segoe_icon_size)'))
		item(image=image.mdl(\uE8FF,segoe_icon_size) tip=['Preview',tip.info] cmd=command.copy('image.mdl(\uE8FF,@segoe_icon_size)'))
		item(image=image.mdl(\uE902,segoe_icon_size) tip=['Group',tip.info] cmd=command.copy('image.mdl(\uE902,@segoe_icon_size)'))
		item(image=image.mdl(\uE904,segoe_icon_size) tip=['ZeroBars',tip.info] cmd=command.copy('image.mdl(\uE904,@segoe_icon_size)'))
		item(image=image.mdl(\uE905,segoe_icon_size) tip=['OneBar',tip.info] cmd=command.copy('image.mdl(\uE905,@segoe_icon_size)'))
		item(image=image.mdl(\uE906,segoe_icon_size) tip=['TwoBars',tip.info] cmd=command.copy('image.mdl(\uE906,@segoe_icon_size)'))
		item(image=image.mdl(\uE907,segoe_icon_size) tip=['ThreeBars',tip.info] cmd=command.copy('image.mdl(\uE907,@segoe_icon_size)'))
		item(image=image.mdl(\uE908,segoe_icon_size) tip=['FourBars',tip.info] cmd=command.copy('image.mdl(\uE908,@segoe_icon_size)'))
		item(image=image.mdl(\uE909,segoe_icon_size) tip=['World',tip.info] cmd=command.copy('image.mdl(\uE909,@segoe_icon_size)'))
		item(image=image.mdl(\uE90A,segoe_icon_size) tip=['Comment',tip.info] cmd=command.copy('image.mdl(\uE90A,@segoe_icon_size)'))

		item(image=image.mdl(\uE90B,segoe_icon_size) tip=['MusicInfo',tip.info] cmd=command.copy('image.mdl(\uE90B,@segoe_icon_size)') col)
		item(image=image.mdl(\uE90C,segoe_icon_size) tip=['DockLeft',tip.info] cmd=command.copy('image.mdl(\uE90C,@segoe_icon_size)'))
		item(image=image.mdl(\uE90D,segoe_icon_size) tip=['DockRight',tip.info] cmd=command.copy('image.mdl(\uE90D,@segoe_icon_size)'))
		item(image=image.mdl(\uE90E,segoe_icon_size) tip=['DockBottom',tip.info] cmd=command.copy('image.mdl(\uE90E,@segoe_icon_size)'))
		item(image=image.mdl(\uE90F,segoe_icon_size) tip=['Repair',tip.info] cmd=command.copy('image.mdl(\uE90F,@segoe_icon_size)'))
		item(image=image.mdl(\uE910,segoe_icon_size) tip=['Accounts',tip.info] cmd=command.copy('image.mdl(\uE910,@segoe_icon_size)'))
		item(image=image.mdl(\uE911,segoe_icon_size) tip=['DullSound',tip.info] cmd=command.copy('image.mdl(\uE911,@segoe_icon_size)'))
		item(image=image.mdl(\uE912,segoe_icon_size) tip=['Manage',tip.info] cmd=command.copy('image.mdl(\uE912,@segoe_icon_size)'))
		item(image=image.mdl(\uE913,segoe_icon_size) tip=['Street',tip.info] cmd=command.copy('image.mdl(\uE913,@segoe_icon_size)'))
		item(image=image.mdl(\uE914,segoe_icon_size) tip=['Printer3D',tip.info] cmd=command.copy('image.mdl(\uE914,@segoe_icon_size)'))
		item(image=image.mdl(\uE915,segoe_icon_size) tip=['RadioBullet',tip.info] cmd=command.copy('image.mdl(\uE915,@segoe_icon_size)'))
		item(image=image.mdl(\uE916,segoe_icon_size) tip=['Stopwatch',tip.info] cmd=command.copy('image.mdl(\uE916,@segoe_icon_size)'))
		item(image=image.mdl(\uE91B,segoe_icon_size) tip=['Photo',tip.info] cmd=command.copy('image.mdl(\uE91B,@segoe_icon_size)'))
		item(image=image.mdl(\uE91C,segoe_icon_size) tip=['ActionCenter',tip.info] cmd=command.copy('image.mdl(\uE91C,@segoe_icon_size)'))
		item(image=image.mdl(\uE91F,segoe_icon_size) tip=['FullCircleMask',tip.info] cmd=command.copy('image.mdl(\uE91F,@segoe_icon_size)'))
		item(image=image.mdl(\uE921,segoe_icon_size) tip=['ChromeMinimize',tip.info] cmd=command.copy('image.mdl(\uE921,@segoe_icon_size)'))

		item(image=image.mdl(\uE922,segoe_icon_size) tip=['ChromeMaximize',tip.info] cmd=command.copy('image.mdl(\uE922,@segoe_icon_size)') col)
		item(image=image.mdl(\uE923,segoe_icon_size) tip=['ChromeRestore',tip.info] cmd=command.copy('image.mdl(\uE923,@segoe_icon_size)'))
		item(image=image.mdl(\uE924,segoe_icon_size) tip=['Annotation',tip.info] cmd=command.copy('image.mdl(\uE924,@segoe_icon_size)'))
		item(image=image.mdl(\uE925,segoe_icon_size) tip=['BackSpaceQWERTYSm',tip.info] cmd=command.copy('image.mdl(\uE925,@segoe_icon_size)'))
		item(image=image.mdl(\uE926,segoe_icon_size) tip=['BackSpaceQWERTYMd',tip.info] cmd=command.copy('image.mdl(\uE926,@segoe_icon_size)'))
		item(image=image.mdl(\uE927,segoe_icon_size) tip=['Swipe',tip.info] cmd=command.copy('image.mdl(\uE927,@segoe_icon_size)'))
		item(image=image.mdl(\uE928,segoe_icon_size) tip=['Fingerprint',tip.info] cmd=command.copy('image.mdl(\uE928,@segoe_icon_size)'))
		item(image=image.mdl(\uE929,segoe_icon_size) tip=['Handwriting',tip.info] cmd=command.copy('image.mdl(\uE929,@segoe_icon_size)'))
		item(image=image.mdl(\uE92C,segoe_icon_size) tip=['ChromeBackToWindow',tip.info] cmd=command.copy('image.mdl(\uE92C,@segoe_icon_size)'))
		item(image=image.mdl(\uE92D,segoe_icon_size) tip=['ChromeFullScreen',tip.info] cmd=command.copy('image.mdl(\uE92D,@segoe_icon_size)'))
		item(image=image.mdl(\uE92E,segoe_icon_size) tip=['KeyboardStandard',tip.info] cmd=command.copy('image.mdl(\uE92E,@segoe_icon_size)'))
		item(image=image.mdl(\uE92F,segoe_icon_size) tip=['KeyboardDismiss',tip.info] cmd=command.copy('image.mdl(\uE92F,@segoe_icon_size)'))
		item(image=image.mdl(\uE930,segoe_icon_size) tip=['Completed',tip.info] cmd=command.copy('image.mdl(\uE930,@segoe_icon_size)'))
		item(image=image.mdl(\uE931,segoe_icon_size) tip=['ChromeAnnotate',tip.info] cmd=command.copy('image.mdl(\uE931,@segoe_icon_size)'))
		item(image=image.mdl(\uE932,segoe_icon_size) tip=['Label',tip.info] cmd=command.copy('image.mdl(\uE932,@segoe_icon_size)'))
		item(image=image.mdl(\uE933,segoe_icon_size) tip=['IBeam',tip.info] cmd=command.copy('image.mdl(\uE933,@segoe_icon_size)'))

		item(image=image.mdl(\uE934,segoe_icon_size) tip=['IBeamOutline',tip.info] cmd=command.copy('image.mdl(\uE934,@segoe_icon_size)') col)
		item(image=image.mdl(\uE935,segoe_icon_size) tip=['FlickDown',tip.info] cmd=command.copy('image.mdl(\uE935,@segoe_icon_size)'))
		item(image=image.mdl(\uE936,segoe_icon_size) tip=['FlickUp',tip.info] cmd=command.copy('image.mdl(\uE936,@segoe_icon_size)'))
		item(image=image.mdl(\uE937,segoe_icon_size) tip=['FlickLeft',tip.info] cmd=command.copy('image.mdl(\uE937,@segoe_icon_size)'))
		item(image=image.mdl(\uE938,segoe_icon_size) tip=['FlickRight',tip.info] cmd=command.copy('image.mdl(\uE938,@segoe_icon_size)'))
		item(image=image.mdl(\uE939,segoe_icon_size) tip=['FeedbackApp',tip.info] cmd=command.copy('image.mdl(\uE939,@segoe_icon_size)'))
		item(image=image.mdl(\uE93C,segoe_icon_size) tip=['MusicAlbum',tip.info] cmd=command.copy('image.mdl(\uE93C,@segoe_icon_size)'))
		item(image=image.mdl(\uE93E,segoe_icon_size) tip=['Streaming',tip.info] cmd=command.copy('image.mdl(\uE93E,@segoe_icon_size)'))
		item(image=image.mdl(\uE943,segoe_icon_size) tip=['Code',tip.info] cmd=command.copy('image.mdl(\uE943,@segoe_icon_size)'))
		item(image=image.mdl(\uE944,segoe_icon_size) tip=['ReturnToWindow',tip.info] cmd=command.copy('image.mdl(\uE944,@segoe_icon_size)'))
		item(image=image.mdl(\uE945,segoe_icon_size) tip=['LightningBolt',tip.info] cmd=command.copy('image.mdl(\uE945,@segoe_icon_size)'))
		item(image=image.mdl(\uE946,segoe_icon_size) tip=['Info',tip.info] cmd=command.copy('image.mdl(\uE946,@segoe_icon_size)'))
		item(image=image.mdl(\uE947,segoe_icon_size) tip=['CalculatorMultiply',tip.info] cmd=command.copy('image.mdl(\uE947,@segoe_icon_size)'))
		item(image=image.mdl(\uE948,segoe_icon_size) tip=['CalculatorAddition',tip.info] cmd=command.copy('image.mdl(\uE948,@segoe_icon_size)'))
		item(image=image.mdl(\uE949,segoe_icon_size) tip=['CalculatorSubtract',tip.info] cmd=command.copy('image.mdl(\uE949,@segoe_icon_size)'))
		item(image=image.mdl(\uE94A,segoe_icon_size) tip=['CalculatorDivide',tip.info] cmd=command.copy('image.mdl(\uE94A,@segoe_icon_size)'))

		item(image=image.mdl(\uE94B,segoe_icon_size) tip=['CalculatorSquareroot',tip.info] cmd=command.copy('image.mdl(\uE94B,@segoe_icon_size)') col)
		item(image=image.mdl(\uE94C,segoe_icon_size) tip=['CalculatorPercentage',tip.info] cmd=command.copy('image.mdl(\uE94C,@segoe_icon_size)'))
		item(image=image.mdl(\uE94D,segoe_icon_size) tip=['CalculatorNegate',tip.info] cmd=command.copy('image.mdl(\uE94D,@segoe_icon_size)'))
		item(image=image.mdl(\uE94E,segoe_icon_size) tip=['CalculatorEqualTo',tip.info] cmd=command.copy('image.mdl(\uE94E,@segoe_icon_size)'))
		item(image=image.mdl(\uE94F,segoe_icon_size) tip=['CalculatorBackspace',tip.info] cmd=command.copy('image.mdl(\uE94F,@segoe_icon_size)'))
		item(image=image.mdl(\uE950,segoe_icon_size) tip=['Component',tip.info] cmd=command.copy('image.mdl(\uE950,@segoe_icon_size)'))
		item(image=image.mdl(\uE951,segoe_icon_size) tip=['DMC',tip.info] cmd=command.copy('image.mdl(\uE951,@segoe_icon_size)'))
		item(image=image.mdl(\uE952,segoe_icon_size) tip=['Dock',tip.info] cmd=command.copy('image.mdl(\uE952,@segoe_icon_size)'))
		item(image=image.mdl(\uE953,segoe_icon_size) tip=['MultimediaDMS',tip.info] cmd=command.copy('image.mdl(\uE953,@segoe_icon_size)'))
		item(image=image.mdl(\uE954,segoe_icon_size) tip=['MultimediaDVR',tip.info] cmd=command.copy('image.mdl(\uE954,@segoe_icon_size)'))
		item(image=image.mdl(\uE955,segoe_icon_size) tip=['MultimediaPMP',tip.info] cmd=command.copy('image.mdl(\uE955,@segoe_icon_size)'))
		item(image=image.mdl(\uE956,segoe_icon_size) tip=['PrintfaxPrinterFile',tip.info] cmd=command.copy('image.mdl(\uE956,@segoe_icon_size)'))
		item(image=image.mdl(\uE957,segoe_icon_size) tip=['Sensor',tip.info] cmd=command.copy('image.mdl(\uE957,@segoe_icon_size)'))
		item(image=image.mdl(\uE958,segoe_icon_size) tip=['StorageOptical',tip.info] cmd=command.copy('image.mdl(\uE958,@segoe_icon_size)'))
		item(image=image.mdl(\uE95A,segoe_icon_size) tip=['Communications',tip.info] cmd=command.copy('image.mdl(\uE95A,@segoe_icon_size)'))
		item(image=image.mdl(\uE95B,segoe_icon_size) tip=['Headset',tip.info] cmd=command.copy('image.mdl(\uE95B,@segoe_icon_size)'))

		item(image=image.mdl(\uE95D,segoe_icon_size) tip=['Projector',tip.info] cmd=command.copy('image.mdl(\uE95D,@segoe_icon_size)') col)
		item(image=image.mdl(\uE95E,segoe_icon_size) tip=['Health',tip.info] cmd=command.copy('image.mdl(\uE95E,@segoe_icon_size)'))
		item(image=image.mdl(\uE95F,segoe_icon_size) tip=['Wire',tip.info] cmd=command.copy('image.mdl(\uE95F,@segoe_icon_size)'))
		item(image=image.mdl(\uE960,segoe_icon_size) tip=['Webcam2',tip.info] cmd=command.copy('image.mdl(\uE960,@segoe_icon_size)'))
		item(image=image.mdl(\uE961,segoe_icon_size) tip=['Input',tip.info] cmd=command.copy('image.mdl(\uE961,@segoe_icon_size)'))
		item(image=image.mdl(\uE962,segoe_icon_size) tip=['Mouse',tip.info] cmd=command.copy('image.mdl(\uE962,@segoe_icon_size)'))
		item(image=image.mdl(\uE963,segoe_icon_size) tip=['Smartcard',tip.info] cmd=command.copy('image.mdl(\uE963,@segoe_icon_size)'))
		item(image=image.mdl(\uE964,segoe_icon_size) tip=['SmartcardVirtual',tip.info] cmd=command.copy('image.mdl(\uE964,@segoe_icon_size)'))
		item(image=image.mdl(\uE965,segoe_icon_size) tip=['MediaStorageTower',tip.info] cmd=command.copy('image.mdl(\uE965,@segoe_icon_size)'))
		item(image=image.mdl(\uE966,segoe_icon_size) tip=['ReturnKeySm',tip.info] cmd=command.copy('image.mdl(\uE966,@segoe_icon_size)'))
		item(image=image.mdl(\uE967,segoe_icon_size) tip=['GameConsole',tip.info] cmd=command.copy('image.mdl(\uE967,@segoe_icon_size)'))
		item(image=image.mdl(\uE968,segoe_icon_size) tip=['Network',tip.info] cmd=command.copy('image.mdl(\uE968,@segoe_icon_size)'))
		item(image=image.mdl(\uE969,segoe_icon_size) tip=['StorageNetworkWireless',tip.info] cmd=command.copy('image.mdl(\uE969,@segoe_icon_size)'))
		item(image=image.mdl(\uE96A,segoe_icon_size) tip=['StorageTape',tip.info] cmd=command.copy('image.mdl(\uE96A,@segoe_icon_size)'))
		item(image=image.mdl(\uE96D,segoe_icon_size) tip=['ChevronUpSmall',tip.info] cmd=command.copy('image.mdl(\uE96D,@segoe_icon_size)'))
		item(image=image.mdl(\uE96E,segoe_icon_size) tip=['ChevronDownSmall',tip.info] cmd=command.copy('image.mdl(\uE96E,@segoe_icon_size)'))
	}

	menu(title='MDL2 #3')
	{
		item(image=image.mdl(\uE96F,segoe_icon_size) tip=['ChevronLeftSmall',tip.info] cmd=command.copy('image.mdl(\uE96F,@segoe_icon_size)'))
		item(image=image.mdl(\uE970,segoe_icon_size) tip=['ChevronRightSmall',tip.info] cmd=command.copy('image.mdl(\uE970,@segoe_icon_size)'))
		item(image=image.mdl(\uE971,segoe_icon_size) tip=['ChevronUpMed',tip.info] cmd=command.copy('image.mdl(\uE971,@segoe_icon_size)'))
		item(image=image.mdl(\uE972,segoe_icon_size) tip=['ChevronDownMed',tip.info] cmd=command.copy('image.mdl(\uE972,@segoe_icon_size)'))
		item(image=image.mdl(\uE973,segoe_icon_size) tip=['ChevronLeftMed',tip.info] cmd=command.copy('image.mdl(\uE973,@segoe_icon_size)'))
		item(image=image.mdl(\uE974,segoe_icon_size) tip=['ChevronRightMed',tip.info] cmd=command.copy('image.mdl(\uE974,@segoe_icon_size)'))
		item(image=image.mdl(\uE975,segoe_icon_size) tip=['Devices2',tip.info] cmd=command.copy('image.mdl(\uE975,@segoe_icon_size)'))
		item(image=image.mdl(\uE976,segoe_icon_size) tip=['ExpandTile',tip.info] cmd=command.copy('image.mdl(\uE976,@segoe_icon_size)'))
		item(image=image.mdl(\uE977,segoe_icon_size) tip=['PC1',tip.info] cmd=command.copy('image.mdl(\uE977,@segoe_icon_size)'))
		item(image=image.mdl(\uE978,segoe_icon_size) tip=['PresenceChicklet',tip.info] cmd=command.copy('image.mdl(\uE978,@segoe_icon_size)'))
		item(image=image.mdl(\uE979,segoe_icon_size) tip=['PresenceChickletVideo',tip.info] cmd=command.copy('image.mdl(\uE979,@segoe_icon_size)'))
		item(image=image.mdl(\uE97A,segoe_icon_size) tip=['Reply',tip.info] cmd=command.copy('image.mdl(\uE97A,@segoe_icon_size)'))
		item(image=image.mdl(\uE97B,segoe_icon_size) tip=['SetTile',tip.info] cmd=command.copy('image.mdl(\uE97B,@segoe_icon_size)'))
		item(image=image.mdl(\uE97C,segoe_icon_size) tip=['Type',tip.info] cmd=command.copy('image.mdl(\uE97C,@segoe_icon_size)'))
		item(image=image.mdl(\uE97D,segoe_icon_size) tip=['Korean',tip.info] cmd=command.copy('image.mdl(\uE97D,@segoe_icon_size)'))
		item(image=image.mdl(\uE97E,segoe_icon_size) tip=['HalfAlpha',tip.info] cmd=command.copy('image.mdl(\uE97E,@segoe_icon_size)'))

		item(image=image.mdl(\uE97F,segoe_icon_size) tip=['FullAlpha',tip.info] cmd=command.copy('image.mdl(\uE97F,@segoe_icon_size)') col)
		item(image=image.mdl(\uE980,segoe_icon_size) tip=['Key12On',tip.info] cmd=command.copy('image.mdl(\uE980,@segoe_icon_size)'))
		item(image=image.mdl(\uE981,segoe_icon_size) tip=['ChineseChangjie',tip.info] cmd=command.copy('image.mdl(\uE981,@segoe_icon_size)'))
		item(image=image.mdl(\uE982,segoe_icon_size) tip=['QWERTYOn',tip.info] cmd=command.copy('image.mdl(\uE982,@segoe_icon_size)'))
		item(image=image.mdl(\uE983,segoe_icon_size) tip=['QWERTYOff',tip.info] cmd=command.copy('image.mdl(\uE983,@segoe_icon_size)'))
		item(image=image.mdl(\uE984,segoe_icon_size) tip=['ChineseQuick',tip.info] cmd=command.copy('image.mdl(\uE984,@segoe_icon_size)'))
		item(image=image.mdl(\uE985,segoe_icon_size) tip=['Japanese',tip.info] cmd=command.copy('image.mdl(\uE985,@segoe_icon_size)'))
		item(image=image.mdl(\uE986,segoe_icon_size) tip=['FullHiragana',tip.info] cmd=command.copy('image.mdl(\uE986,@segoe_icon_size)'))
		item(image=image.mdl(\uE987,segoe_icon_size) tip=['FullKatakana',tip.info] cmd=command.copy('image.mdl(\uE987,@segoe_icon_size)'))
		item(image=image.mdl(\uE988,segoe_icon_size) tip=['HalfKatakana',tip.info] cmd=command.copy('image.mdl(\uE988,@segoe_icon_size)'))
		item(image=image.mdl(\uE989,segoe_icon_size) tip=['ChineseBoPoMoFo',tip.info] cmd=command.copy('image.mdl(\uE989,@segoe_icon_size)'))
		item(image=image.mdl(\uE98A,segoe_icon_size) tip=['ChinesePinyin',tip.info] cmd=command.copy('image.mdl(\uE98A,@segoe_icon_size)'))
		item(image=image.mdl(\uE98F,segoe_icon_size) tip=['ConstructionCone',tip.info] cmd=command.copy('image.mdl(\uE98F,@segoe_icon_size)'))
		item(image=image.mdl(\uE990,segoe_icon_size) tip=['XboxOneConsole',tip.info] cmd=command.copy('image.mdl(\uE990,@segoe_icon_size)'))
		item(image=image.mdl(\uE992,segoe_icon_size) tip=['Volume0',tip.info] cmd=command.copy('image.mdl(\uE992,@segoe_icon_size)'))
		item(image=image.mdl(\uE993,segoe_icon_size) tip=['Volume1',tip.info] cmd=command.copy('image.mdl(\uE993,@segoe_icon_size)'))

		item(image=image.mdl(\uE994,segoe_icon_size) tip=['Volume2',tip.info] cmd=command.copy('image.mdl(\uE994,@segoe_icon_size)') col)
		item(image=image.mdl(\uE995,segoe_icon_size) tip=['Volume3',tip.info] cmd=command.copy('image.mdl(\uE995,@segoe_icon_size)'))
		item(image=image.mdl(\uE996,segoe_icon_size) tip=['BatteryUnknown',tip.info] cmd=command.copy('image.mdl(\uE996,@segoe_icon_size)'))
		item(image=image.mdl(\uE998,segoe_icon_size) tip=['WifiAttentionOverlay',tip.info] cmd=command.copy('image.mdl(\uE998,@segoe_icon_size)'))
		item(image=image.mdl(\uE99A,segoe_icon_size) tip=['Robot',tip.info] cmd=command.copy('image.mdl(\uE99A,@segoe_icon_size)'))
		item(image=image.mdl(\uE9A1,segoe_icon_size) tip=['TapAndSend',tip.info] cmd=command.copy('image.mdl(\uE9A1,@segoe_icon_size)'))
		item(image=image.mdl(\uE9A6,segoe_icon_size) tip=['FitPage',tip.info] cmd=command.copy('image.mdl(\uE9A6,@segoe_icon_size)'))
		item(image=image.mdl(\uE9A8,segoe_icon_size) tip=['PasswordKeyShow',tip.info] cmd=command.copy('image.mdl(\uE9A8,@segoe_icon_size)'))
		item(image=image.mdl(\uE9A9,segoe_icon_size) tip=['PasswordKeyHide',tip.info] cmd=command.copy('image.mdl(\uE9A9,@segoe_icon_size)'))
		item(image=image.mdl(\uE9AA,segoe_icon_size) tip=['BidiLtr',tip.info] cmd=command.copy('image.mdl(\uE9AA,@segoe_icon_size)'))
		item(image=image.mdl(\uE9AB,segoe_icon_size) tip=['BidiRtl',tip.info] cmd=command.copy('image.mdl(\uE9AB,@segoe_icon_size)'))
		item(image=image.mdl(\uE9AC,segoe_icon_size) tip=['ForwardSm',tip.info] cmd=command.copy('image.mdl(\uE9AC,@segoe_icon_size)'))
		item(image=image.mdl(\uE9AD,segoe_icon_size) tip=['CommaKey',tip.info] cmd=command.copy('image.mdl(\uE9AD,@segoe_icon_size)'))
		item(image=image.mdl(\uE9AE,segoe_icon_size) tip=['DashKey',tip.info] cmd=command.copy('image.mdl(\uE9AE,@segoe_icon_size)'))
		item(image=image.mdl(\uE9AF,segoe_icon_size) tip=['DullSoundKey',tip.info] cmd=command.copy('image.mdl(\uE9AF,@segoe_icon_size)'))
		item(image=image.mdl(\uE9B0,segoe_icon_size) tip=['HalfDullSound',tip.info] cmd=command.copy('image.mdl(\uE9B0,@segoe_icon_size)'))

		item(image=image.mdl(\uE9B1,segoe_icon_size) tip=['RightDoubleQuote',tip.info] cmd=command.copy('image.mdl(\uE9B1,@segoe_icon_size)') col)
		item(image=image.mdl(\uE9B2,segoe_icon_size) tip=['LeftDoubleQuote',tip.info] cmd=command.copy('image.mdl(\uE9B2,@segoe_icon_size)'))
		item(image=image.mdl(\uE9B3,segoe_icon_size) tip=['PuncKeyRightBottom',tip.info] cmd=command.copy('image.mdl(\uE9B3,@segoe_icon_size)'))
		item(image=image.mdl(\uE9B4,segoe_icon_size) tip=['PuncKey1',tip.info] cmd=command.copy('image.mdl(\uE9B4,@segoe_icon_size)'))
		item(image=image.mdl(\uE9B5,segoe_icon_size) tip=['PuncKey2',tip.info] cmd=command.copy('image.mdl(\uE9B5,@segoe_icon_size)'))
		item(image=image.mdl(\uE9B6,segoe_icon_size) tip=['PuncKey3',tip.info] cmd=command.copy('image.mdl(\uE9B6,@segoe_icon_size)'))
		item(image=image.mdl(\uE9B7,segoe_icon_size) tip=['PuncKey4',tip.info] cmd=command.copy('image.mdl(\uE9B7,@segoe_icon_size)'))
		item(image=image.mdl(\uE9B8,segoe_icon_size) tip=['PuncKey5',tip.info] cmd=command.copy('image.mdl(\uE9B8,@segoe_icon_size)'))
		item(image=image.mdl(\uE9B9,segoe_icon_size) tip=['PuncKey6',tip.info] cmd=command.copy('image.mdl(\uE9B9,@segoe_icon_size)'))
		item(image=image.mdl(\uE9BA,segoe_icon_size) tip=['PuncKey9',tip.info] cmd=command.copy('image.mdl(\uE9BA,@segoe_icon_size)'))
		item(image=image.mdl(\uE9BB,segoe_icon_size) tip=['PuncKey7',tip.info] cmd=command.copy('image.mdl(\uE9BB,@segoe_icon_size)'))
		item(image=image.mdl(\uE9BC,segoe_icon_size) tip=['PuncKey8',tip.info] cmd=command.copy('image.mdl(\uE9BC,@segoe_icon_size)'))
		item(image=image.mdl(\uE9CA,segoe_icon_size) tip=['Frigid',tip.info] cmd=command.copy('image.mdl(\uE9CA,@segoe_icon_size)'))
		item(image=image.mdl(\uE9CE,segoe_icon_size) tip=['Unknown',tip.info] cmd=command.copy('image.mdl(\uE9CE,@segoe_icon_size)'))
		item(image=image.mdl(\uE9D2,segoe_icon_size) tip=['AreaChart',tip.info] cmd=command.copy('image.mdl(\uE9D2,@segoe_icon_size)'))
		item(image=image.mdl(\uE9D5,segoe_icon_size) tip=['CheckList',tip.info] cmd=command.copy('image.mdl(\uE9D5,@segoe_icon_size)'))

		item(image=image.mdl(\uE9D9,segoe_icon_size) tip=['Diagnostic',tip.info] cmd=command.copy('image.mdl(\uE9D9,@segoe_icon_size)') col)
		item(image=image.mdl(\uE9E9,segoe_icon_size) tip=['Equalizer',tip.info] cmd=command.copy('image.mdl(\uE9E9,@segoe_icon_size)'))
		item(image=image.mdl(\uE9F3,segoe_icon_size) tip=['Process',tip.info] cmd=command.copy('image.mdl(\uE9F3,@segoe_icon_size)'))
		item(image=image.mdl(\uE9F5,segoe_icon_size) tip=['Processing',tip.info] cmd=command.copy('image.mdl(\uE9F5,@segoe_icon_size)'))
		item(image=image.mdl(\uE9F9,segoe_icon_size) tip=['ReportDocument',tip.info] cmd=command.copy('image.mdl(\uE9F9,@segoe_icon_size)'))
		item(image=image.mdl(\uEA0C,segoe_icon_size) tip=['VideoSolid',tip.info] cmd=command.copy('image.mdl(\uEA0C,@segoe_icon_size)'))
		item(image=image.mdl(\uEA0D,segoe_icon_size) tip=['MixedMediaBadge',tip.info] cmd=command.copy('image.mdl(\uEA0D,@segoe_icon_size)'))
		item(image=image.mdl(\uEA14,segoe_icon_size) tip=['DisconnectDisplay',tip.info] cmd=command.copy('image.mdl(\uEA14,@segoe_icon_size)'))
		item(image=image.mdl(\uEA18,segoe_icon_size) tip=['Shield',tip.info] cmd=command.copy('image.mdl(\uEA18,@segoe_icon_size)'))
		item(image=image.mdl(\uEA1F,segoe_icon_size) tip=['Info2',tip.info] cmd=command.copy('image.mdl(\uEA1F,@segoe_icon_size)'))
		item(image=image.mdl(\uEA21,segoe_icon_size) tip=['ActionCenterAsterisk',tip.info] cmd=command.copy('image.mdl(\uEA21,@segoe_icon_size)'))
		item(image=image.mdl(\uEA24,segoe_icon_size) tip=['Beta',tip.info] cmd=command.copy('image.mdl(\uEA24,@segoe_icon_size)'))
		item(image=image.mdl(\uEA35,segoe_icon_size) tip=['SaveCopy',tip.info] cmd=command.copy('image.mdl(\uEA35,@segoe_icon_size)'))
		item(image=image.mdl(\uEA37,segoe_icon_size) tip=['List',tip.info] cmd=command.copy('image.mdl(\uEA37,@segoe_icon_size)'))
		item(image=image.mdl(\uEA38,segoe_icon_size) tip=['Asterisk',tip.info] cmd=command.copy('image.mdl(\uEA38,@segoe_icon_size)'))
		item(image=image.mdl(\uEA39,segoe_icon_size) tip=['ErrorBadge',tip.info] cmd=command.copy('image.mdl(\uEA39,@segoe_icon_size)'))

		item(image=image.mdl(\uEA3A,segoe_icon_size) tip=['CircleRing',tip.info] cmd=command.copy('image.mdl(\uEA3A,@segoe_icon_size)') col)
		item(image=image.mdl(\uEA3B,segoe_icon_size) tip=['CircleFill',tip.info] cmd=command.copy('image.mdl(\uEA3B,@segoe_icon_size)'))
		item(image=image.mdl(\uEA3C,segoe_icon_size) tip=['MergeCall',tip.info] cmd=command.copy('image.mdl(\uEA3C,@segoe_icon_size)'))
		item(image=image.mdl(\uEA3D,segoe_icon_size) tip=['PrivateCall',tip.info] cmd=command.copy('image.mdl(\uEA3D,@segoe_icon_size)'))
		item(image=image.mdl(\uEA3F,segoe_icon_size) tip=['Record2',tip.info] cmd=command.copy('image.mdl(\uEA3F,@segoe_icon_size)'))
		item(image=image.mdl(\uEA40,segoe_icon_size) tip=['AllAppsMirrored',tip.info] cmd=command.copy('image.mdl(\uEA40,@segoe_icon_size)'))
		item(image=image.mdl(\uEA41,segoe_icon_size) tip=['BookmarksMirrored',tip.info] cmd=command.copy('image.mdl(\uEA41,@segoe_icon_size)'))
		item(image=image.mdl(\uEA42,segoe_icon_size) tip=['BulletedListMirrored',tip.info] cmd=command.copy('image.mdl(\uEA42,@segoe_icon_size)'))
		item(image=image.mdl(\uEA43,segoe_icon_size) tip=['CallForwardInternationalMirrored',tip.info] cmd=command.copy('image.mdl(\uEA43,@segoe_icon_size)'))
		item(image=image.mdl(\uEA44,segoe_icon_size) tip=['CallForwardRoamingMirrored',tip.info] cmd=command.copy('image.mdl(\uEA44,@segoe_icon_size)'))
		item(image=image.mdl(\uEA47,segoe_icon_size) tip=['ChromeBackMirrored',tip.info] cmd=command.copy('image.mdl(\uEA47,@segoe_icon_size)'))
		item(image=image.mdl(\uEA48,segoe_icon_size) tip=['ClearSelectionMirrored',tip.info] cmd=command.copy('image.mdl(\uEA48,@segoe_icon_size)'))
		item(image=image.mdl(\uEA49,segoe_icon_size) tip=['ClosePaneMirrored',tip.info] cmd=command.copy('image.mdl(\uEA49,@segoe_icon_size)'))
		item(image=image.mdl(\uEA4A,segoe_icon_size) tip=['ContactInfoMirrored',tip.info] cmd=command.copy('image.mdl(\uEA4A,@segoe_icon_size)'))
		item(image=image.mdl(\uEA4B,segoe_icon_size) tip=['DockRightMirrored',tip.info] cmd=command.copy('image.mdl(\uEA4B,@segoe_icon_size)'))
		item(image=image.mdl(\uEA4C,segoe_icon_size) tip=['DockLeftMirrored',tip.info] cmd=command.copy('image.mdl(\uEA4C,@segoe_icon_size)'))

		item(image=image.mdl(\uEA4E,segoe_icon_size) tip=['ExpandTileMirrored',tip.info] cmd=command.copy('image.mdl(\uEA4E,@segoe_icon_size)') col)
		item(image=image.mdl(\uEA4F,segoe_icon_size) tip=['GoMirrored',tip.info] cmd=command.copy('image.mdl(\uEA4F,@segoe_icon_size)'))
		item(image=image.mdl(\uEA50,segoe_icon_size) tip=['GripperResizeMirrored',tip.info] cmd=command.copy('image.mdl(\uEA50,@segoe_icon_size)'))
		item(image=image.mdl(\uEA51,segoe_icon_size) tip=['HelpMirrored',tip.info] cmd=command.copy('image.mdl(\uEA51,@segoe_icon_size)'))
		item(image=image.mdl(\uEA52,segoe_icon_size) tip=['ImportMirrored',tip.info] cmd=command.copy('image.mdl(\uEA52,@segoe_icon_size)'))
		item(image=image.mdl(\uEA53,segoe_icon_size) tip=['ImportAllMirrored',tip.info] cmd=command.copy('image.mdl(\uEA53,@segoe_icon_size)'))
		item(image=image.mdl(\uEA54,segoe_icon_size) tip=['LeaveChatMirrored',tip.info] cmd=command.copy('image.mdl(\uEA54,@segoe_icon_size)'))
		item(image=image.mdl(\uEA55,segoe_icon_size) tip=['ListMirrored',tip.info] cmd=command.copy('image.mdl(\uEA55,@segoe_icon_size)'))
		item(image=image.mdl(\uEA56,segoe_icon_size) tip=['MailForwardMirrored',tip.info] cmd=command.copy('image.mdl(\uEA56,@segoe_icon_size)'))
		item(image=image.mdl(\uEA57,segoe_icon_size) tip=['MailReplyMirrored',tip.info] cmd=command.copy('image.mdl(\uEA57,@segoe_icon_size)'))
		item(image=image.mdl(\uEA58,segoe_icon_size) tip=['MailReplyAllMirrored',tip.info] cmd=command.copy('image.mdl(\uEA58,@segoe_icon_size)'))
		item(image=image.mdl(\uEA5B,segoe_icon_size) tip=['OpenPaneMirrored',tip.info] cmd=command.copy('image.mdl(\uEA5B,@segoe_icon_size)'))
		item(image=image.mdl(\uEA5C,segoe_icon_size) tip=['OpenWithMirrored',tip.info] cmd=command.copy('image.mdl(\uEA5C,@segoe_icon_size)'))
		item(image=image.mdl(\uEA5E,segoe_icon_size) tip=['ParkingLocationMirrored',tip.info] cmd=command.copy('image.mdl(\uEA5E,@segoe_icon_size)'))
		item(image=image.mdl(\uEA5F,segoe_icon_size) tip=['ResizeMouseMediumMirrored',tip.info] cmd=command.copy('image.mdl(\uEA5F,@segoe_icon_size)'))
		item(image=image.mdl(\uEA60,segoe_icon_size) tip=['ResizeMouseSmallMirrored',tip.info] cmd=command.copy('image.mdl(\uEA60,@segoe_icon_size)'))

		item(image=image.mdl(\uEA61,segoe_icon_size) tip=['ResizeMouseTallMirrored',tip.info] cmd=command.copy('image.mdl(\uEA61,@segoe_icon_size)') col)
		item(image=image.mdl(\uEA62,segoe_icon_size) tip=['ResizeTouchNarrowerMirrored',tip.info] cmd=command.copy('image.mdl(\uEA62,@segoe_icon_size)'))
		item(image=image.mdl(\uEA63,segoe_icon_size) tip=['SendMirrored',tip.info] cmd=command.copy('image.mdl(\uEA63,@segoe_icon_size)'))
		item(image=image.mdl(\uEA64,segoe_icon_size) tip=['SendFillMirrored',tip.info] cmd=command.copy('image.mdl(\uEA64,@segoe_icon_size)'))
		item(image=image.mdl(\uEA65,segoe_icon_size) tip=['ShowResultsMirrored',tip.info] cmd=command.copy('image.mdl(\uEA65,@segoe_icon_size)'))
		item(image=image.mdl(\uEA69,segoe_icon_size) tip=['Media',tip.info] cmd=command.copy('image.mdl(\uEA69,@segoe_icon_size)'))
		item(image=image.mdl(\uEA6A,segoe_icon_size) tip=['SyncError',tip.info] cmd=command.copy('image.mdl(\uEA6A,@segoe_icon_size)'))
		item(image=image.mdl(\uEA6C,segoe_icon_size) tip=['Devices3',tip.info] cmd=command.copy('image.mdl(\uEA6C,@segoe_icon_size)'))
		item(image=image.mdl(\uEA79,segoe_icon_size) tip=['SlowMotionOn',tip.info] cmd=command.copy('image.mdl(\uEA79,@segoe_icon_size)'))
		item(image=image.mdl(\uEA80,segoe_icon_size) tip=['Lightbulb',tip.info] cmd=command.copy('image.mdl(\uEA80,@segoe_icon_size)'))
		item(image=image.mdl(\uEA81,segoe_icon_size) tip=['StatusCircle',tip.info] cmd=command.copy('image.mdl(\uEA81,@segoe_icon_size)'))
		item(image=image.mdl(\uEA82,segoe_icon_size) tip=['StatusTriangle',tip.info] cmd=command.copy('image.mdl(\uEA82,@segoe_icon_size)'))
		item(image=image.mdl(\uEA83,segoe_icon_size) tip=['StatusError',tip.info] cmd=command.copy('image.mdl(\uEA83,@segoe_icon_size)'))
		item(image=image.mdl(\uEA84,segoe_icon_size) tip=['StatusWarning',tip.info] cmd=command.copy('image.mdl(\uEA84,@segoe_icon_size)'))
		item(image=image.mdl(\uEA86,segoe_icon_size) tip=['Puzzle',tip.info] cmd=command.copy('image.mdl(\uEA86,@segoe_icon_size)'))
		item(image=image.mdl(\uEA89,segoe_icon_size) tip=['CalendarSolid',tip.info] cmd=command.copy('image.mdl(\uEA89,@segoe_icon_size)'))

		item(image=image.mdl(\uEA8A,segoe_icon_size) tip=['HomeSolid',tip.info] cmd=command.copy('image.mdl(\uEA8A,@segoe_icon_size)') col)
		item(image=image.mdl(\uEA8B,segoe_icon_size) tip=['ParkingLocationSolid',tip.info] cmd=command.copy('image.mdl(\uEA8B,@segoe_icon_size)'))
		item(image=image.mdl(\uEA8C,segoe_icon_size) tip=['ContactSolid',tip.info] cmd=command.copy('image.mdl(\uEA8C,@segoe_icon_size)'))
		item(image=image.mdl(\uEA8D,segoe_icon_size) tip=['ConstructionSolid',tip.info] cmd=command.copy('image.mdl(\uEA8D,@segoe_icon_size)'))
		item(image=image.mdl(\uEA8E,segoe_icon_size) tip=['AccidentSolid',tip.info] cmd=command.copy('image.mdl(\uEA8E,@segoe_icon_size)'))
		item(image=image.mdl(\uEA8F,segoe_icon_size) tip=['Ringer',tip.info] cmd=command.copy('image.mdl(\uEA8F,@segoe_icon_size)'))
		item(image=image.mdl(\uEA90,segoe_icon_size) tip=['PDF',tip.info] cmd=command.copy('image.mdl(\uEA90,@segoe_icon_size)'))
		item(image=image.mdl(\uEA91,segoe_icon_size) tip=['ThoughtBubble',tip.info] cmd=command.copy('image.mdl(\uEA91,@segoe_icon_size)'))
		item(image=image.mdl(\uEA92,segoe_icon_size) tip=['HeartBroken',tip.info] cmd=command.copy('image.mdl(\uEA92,@segoe_icon_size)'))
		item(image=image.mdl(\uEA93,segoe_icon_size) tip=['BatteryCharging10',tip.info] cmd=command.copy('image.mdl(\uEA93,@segoe_icon_size)'))
		item(image=image.mdl(\uEA94,segoe_icon_size) tip=['BatterySaver9',tip.info] cmd=command.copy('image.mdl(\uEA94,@segoe_icon_size)'))
		item(image=image.mdl(\uEA95,segoe_icon_size) tip=['BatterySaver10',tip.info] cmd=command.copy('image.mdl(\uEA95,@segoe_icon_size)'))
		item(image=image.mdl(\uEA97,segoe_icon_size) tip=['CallForwardingMirrored',tip.info] cmd=command.copy('image.mdl(\uEA97,@segoe_icon_size)'))
		item(image=image.mdl(\uEA98,segoe_icon_size) tip=['MultiSelectMirrored',tip.info] cmd=command.copy('image.mdl(\uEA98,@segoe_icon_size)'))
		item(image=image.mdl(\uEA99,segoe_icon_size) tip=['Broom',tip.info] cmd=command.copy('image.mdl(\uEA99,@segoe_icon_size)'))
		item(image=image.mdl(\uEAC2,segoe_icon_size) tip=['ForwardCall',tip.info] cmd=command.copy('image.mdl(\uEAC2,@segoe_icon_size)'))

		item(image=image.mdl(\uEADF,segoe_icon_size) tip=['Trackers',tip.info] cmd=command.copy('image.mdl(\uEADF,@segoe_icon_size)') col)
		item(image=image.mdl(\uEAFC,segoe_icon_size) tip=['Market',tip.info] cmd=command.copy('image.mdl(\uEAFC,@segoe_icon_size)'))
		item(image=image.mdl(\uEB05,segoe_icon_size) tip=['PieSingle',tip.info] cmd=command.copy('image.mdl(\uEB05,@segoe_icon_size)'))
		item(image=image.mdl(\uEB0F,segoe_icon_size) tip=['StockDown',tip.info] cmd=command.copy('image.mdl(\uEB0F,@segoe_icon_size)'))
		item(image=image.mdl(\uEB11,segoe_icon_size) tip=['StockUp',tip.info] cmd=command.copy('image.mdl(\uEB11,@segoe_icon_size)'))
		item(image=image.mdl(\uEB3C,segoe_icon_size) tip=['Design',tip.info] cmd=command.copy('image.mdl(\uEB3C,@segoe_icon_size)'))
		item(image=image.mdl(\uEB41,segoe_icon_size) tip=['Website',tip.info] cmd=command.copy('image.mdl(\uEB41,@segoe_icon_size)'))
		item(image=image.mdl(\uEB42,segoe_icon_size) tip=['Drop',tip.info] cmd=command.copy('image.mdl(\uEB42,@segoe_icon_size)'))
		item(image=image.mdl(\uEB44,segoe_icon_size) tip=['Radar',tip.info] cmd=command.copy('image.mdl(\uEB44,@segoe_icon_size)'))
		item(image=image.mdl(\uEB47,segoe_icon_size) tip=['BusSolid',tip.info] cmd=command.copy('image.mdl(\uEB47,@segoe_icon_size)'))
		item(image=image.mdl(\uEB48,segoe_icon_size) tip=['FerrySolid',tip.info] cmd=command.copy('image.mdl(\uEB48,@segoe_icon_size)'))
		item(image=image.mdl(\uEB49,segoe_icon_size) tip=['StartPointSolid',tip.info] cmd=command.copy('image.mdl(\uEB49,@segoe_icon_size)'))
		item(image=image.mdl(\uEB4A,segoe_icon_size) tip=['StopPointSolid',tip.info] cmd=command.copy('image.mdl(\uEB4A,@segoe_icon_size)'))
		item(image=image.mdl(\uEB4B,segoe_icon_size) tip=['EndPointSolid',tip.info] cmd=command.copy('image.mdl(\uEB4B,@segoe_icon_size)'))
		item(image=image.mdl(\uEB4C,segoe_icon_size) tip=['AirplaneSolid',tip.info] cmd=command.copy('image.mdl(\uEB4C,@segoe_icon_size)'))
		item(image=image.mdl(\uEB4D,segoe_icon_size) tip=['TrainSolid',tip.info] cmd=command.copy('image.mdl(\uEB4D,@segoe_icon_size)'))

		item(image=image.mdl(\uEB4E,segoe_icon_size) tip=['WorkSolid',tip.info] cmd=command.copy('image.mdl(\uEB4E,@segoe_icon_size)') col)
		item(image=image.mdl(\uEB4F,segoe_icon_size) tip=['ReminderFill',tip.info] cmd=command.copy('image.mdl(\uEB4F,@segoe_icon_size)'))
		item(image=image.mdl(\uEB50,segoe_icon_size) tip=['Reminder',tip.info] cmd=command.copy('image.mdl(\uEB50,@segoe_icon_size)'))
		item(image=image.mdl(\uEB51,segoe_icon_size) tip=['Heart',tip.info] cmd=command.copy('image.mdl(\uEB51,@segoe_icon_size)'))
		item(image=image.mdl(\uEB52,segoe_icon_size) tip=['HeartFill',tip.info] cmd=command.copy('image.mdl(\uEB52,@segoe_icon_size)'))
		item(image=image.mdl(\uEB55,segoe_icon_size) tip=['EthernetError',tip.info] cmd=command.copy('image.mdl(\uEB55,@segoe_icon_size)'))
		item(image=image.mdl(\uEB56,segoe_icon_size) tip=['EthernetWarning',tip.info] cmd=command.copy('image.mdl(\uEB56,@segoe_icon_size)'))
		item(image=image.mdl(\uEB57,segoe_icon_size) tip=['StatusConnecting1',tip.info] cmd=command.copy('image.mdl(\uEB57,@segoe_icon_size)'))
		item(image=image.mdl(\uEB58,segoe_icon_size) tip=['StatusConnecting2',tip.info] cmd=command.copy('image.mdl(\uEB58,@segoe_icon_size)'))
		item(image=image.mdl(\uEB59,segoe_icon_size) tip=['StatusUnsecure',tip.info] cmd=command.copy('image.mdl(\uEB59,@segoe_icon_size)'))
		item(image=image.mdl(\uEB5A,segoe_icon_size) tip=['WifiError0',tip.info] cmd=command.copy('image.mdl(\uEB5A,@segoe_icon_size)'))
		item(image=image.mdl(\uEB5B,segoe_icon_size) tip=['WifiError1',tip.info] cmd=command.copy('image.mdl(\uEB5B,@segoe_icon_size)'))
		item(image=image.mdl(\uEB5C,segoe_icon_size) tip=['WifiError2',tip.info] cmd=command.copy('image.mdl(\uEB5C,@segoe_icon_size)'))
		item(image=image.mdl(\uEB5D,segoe_icon_size) tip=['WifiError3',tip.info] cmd=command.copy('image.mdl(\uEB5D,@segoe_icon_size)'))
		item(image=image.mdl(\uEB5E,segoe_icon_size) tip=['WifiError4',tip.info] cmd=command.copy('image.mdl(\uEB5E,@segoe_icon_size)'))
		item(image=image.mdl(\uEB5F,segoe_icon_size) tip=['WifiWarning0',tip.info] cmd=command.copy('image.mdl(\uEB5F,@segoe_icon_size)'))

		item(image=image.mdl(\uEB60,segoe_icon_size) tip=['WifiWarning1',tip.info] cmd=command.copy('image.mdl(\uEB60,@segoe_icon_size)') col)
		item(image=image.mdl(\uEB61,segoe_icon_size) tip=['WifiWarning2',tip.info] cmd=command.copy('image.mdl(\uEB61,@segoe_icon_size)'))
		item(image=image.mdl(\uEB62,segoe_icon_size) tip=['WifiWarning3',tip.info] cmd=command.copy('image.mdl(\uEB62,@segoe_icon_size)'))
		item(image=image.mdl(\uEB63,segoe_icon_size) tip=['WifiWarning4',tip.info] cmd=command.copy('image.mdl(\uEB63,@segoe_icon_size)'))
		item(image=image.mdl(\uEB66,segoe_icon_size) tip=['Devices4',tip.info] cmd=command.copy('image.mdl(\uEB66,@segoe_icon_size)'))
		item(image=image.mdl(\uEB67,segoe_icon_size) tip=['NUIIris',tip.info] cmd=command.copy('image.mdl(\uEB67,@segoe_icon_size)'))
		item(image=image.mdl(\uEB68,segoe_icon_size) tip=['NUIFace',tip.info] cmd=command.copy('image.mdl(\uEB68,@segoe_icon_size)'))
		item(image=image.mdl(\uEB7E,segoe_icon_size) tip=['EditMirrored',tip.info] cmd=command.copy('image.mdl(\uEB7E,@segoe_icon_size)'))
		item(image=image.mdl(\uEB82,segoe_icon_size) tip=['NUIFPStartSlideHand',tip.info] cmd=command.copy('image.mdl(\uEB82,@segoe_icon_size)'))
		item(image=image.mdl(\uEB83,segoe_icon_size) tip=['NUIFPStartSlideAction',tip.info] cmd=command.copy('image.mdl(\uEB83,@segoe_icon_size)'))
		item(image=image.mdl(\uEB84,segoe_icon_size) tip=['NUIFPContinueSlideHand',tip.info] cmd=command.copy('image.mdl(\uEB84,@segoe_icon_size)'))
		item(image=image.mdl(\uEB85,segoe_icon_size) tip=['NUIFPContinueSlideAction',tip.info] cmd=command.copy('image.mdl(\uEB85,@segoe_icon_size)'))
		item(image=image.mdl(\uEB86,segoe_icon_size) tip=['NUIFPRollRightHand',tip.info] cmd=command.copy('image.mdl(\uEB86,@segoe_icon_size)'))
		item(image=image.mdl(\uEB87,segoe_icon_size) tip=['NUIFPRollRightHandAction',tip.info] cmd=command.copy('image.mdl(\uEB87,@segoe_icon_size)'))
		item(image=image.mdl(\uEB88,segoe_icon_size) tip=['NUIFPRollLeftHand',tip.info] cmd=command.copy('image.mdl(\uEB88,@segoe_icon_size)'))
		item(image=image.mdl(\uEB89,segoe_icon_size) tip=['NUIFPRollLeftAction',tip.info] cmd=command.copy('image.mdl(\uEB89,@segoe_icon_size)'))

		item(image=image.mdl(\uEB8A,segoe_icon_size) tip=['NUIFPPressHand',tip.info] cmd=command.copy('image.mdl(\uEB8A,@segoe_icon_size)') col)
		item(image=image.mdl(\uEB8B,segoe_icon_size) tip=['NUIFPPressAction',tip.info] cmd=command.copy('image.mdl(\uEB8B,@segoe_icon_size)'))
		item(image=image.mdl(\uEB8C,segoe_icon_size) tip=['NUIFPPressRepeatHand',tip.info] cmd=command.copy('image.mdl(\uEB8C,@segoe_icon_size)'))
		item(image=image.mdl(\uEB8D,segoe_icon_size) tip=['NUIFPPressRepeatAction',tip.info] cmd=command.copy('image.mdl(\uEB8D,@segoe_icon_size)'))
		item(image=image.mdl(\uEB90,segoe_icon_size) tip=['StatusErrorFull',tip.info] cmd=command.copy('image.mdl(\uEB90,@segoe_icon_size)'))
		item(image=image.mdl(\uEB91,segoe_icon_size) tip=['TaskViewExpanded',tip.info] cmd=command.copy('image.mdl(\uEB91,@segoe_icon_size)'))
		item(image=image.mdl(\uEB95,segoe_icon_size) tip=['Certificate',tip.info] cmd=command.copy('image.mdl(\uEB95,@segoe_icon_size)'))
		item(image=image.mdl(\uEB96,segoe_icon_size) tip=['BackSpaceQWERTYLg',tip.info] cmd=command.copy('image.mdl(\uEB96,@segoe_icon_size)'))
		item(image=image.mdl(\uEB97,segoe_icon_size) tip=['ReturnKeyLg',tip.info] cmd=command.copy('image.mdl(\uEB97,@segoe_icon_size)'))
		item(image=image.mdl(\uEB9D,segoe_icon_size) tip=['FastForward',tip.info] cmd=command.copy('image.mdl(\uEB9D,@segoe_icon_size)'))
		item(image=image.mdl(\uEB9E,segoe_icon_size) tip=['Rewind',tip.info] cmd=command.copy('image.mdl(\uEB9E,@segoe_icon_size)'))
		item(image=image.mdl(\uEB9F,segoe_icon_size) tip=['Photo2',tip.info] cmd=command.copy('image.mdl(\uEB9F,@segoe_icon_size)'))
		item(image=image.mdl(\uEBA0,segoe_icon_size) tip=['MobBattery0',tip.info] cmd=command.copy('image.mdl(\uEBA0,@segoe_icon_size)'))
		item(image=image.mdl(\uEBA1,segoe_icon_size) tip=['MobBattery1',tip.info] cmd=command.copy('image.mdl(\uEBA1,@segoe_icon_size)'))
		item(image=image.mdl(\uEBA2,segoe_icon_size) tip=['MobBattery2',tip.info] cmd=command.copy('image.mdl(\uEBA2,@segoe_icon_size)'))
		item(image=image.mdl(\uEBA3,segoe_icon_size) tip=['MobBattery3',tip.info] cmd=command.copy('image.mdl(\uEBA3,@segoe_icon_size)'))

		item(image=image.mdl(\uEBA4,segoe_icon_size) tip=['MobBattery4',tip.info] cmd=command.copy('image.mdl(\uEBA4,@segoe_icon_size)') col)
		item(image=image.mdl(\uEBA5,segoe_icon_size) tip=['MobBattery5',tip.info] cmd=command.copy('image.mdl(\uEBA5,@segoe_icon_size)'))
		item(image=image.mdl(\uEBA6,segoe_icon_size) tip=['MobBattery6',tip.info] cmd=command.copy('image.mdl(\uEBA6,@segoe_icon_size)'))
		item(image=image.mdl(\uEBA7,segoe_icon_size) tip=['MobBattery7',tip.info] cmd=command.copy('image.mdl(\uEBA7,@segoe_icon_size)'))
		item(image=image.mdl(\uEBA8,segoe_icon_size) tip=['MobBattery8',tip.info] cmd=command.copy('image.mdl(\uEBA8,@segoe_icon_size)'))
		item(image=image.mdl(\uEBA9,segoe_icon_size) tip=['MobBattery9',tip.info] cmd=command.copy('image.mdl(\uEBA9,@segoe_icon_size)'))
		item(image=image.mdl(\uEBAA,segoe_icon_size) tip=['MobBattery10',tip.info] cmd=command.copy('image.mdl(\uEBAA,@segoe_icon_size)'))
		item(image=image.mdl(\uEBAB,segoe_icon_size) tip=['MobBatteryCharging0',tip.info] cmd=command.copy('image.mdl(\uEBAB,@segoe_icon_size)'))
		item(image=image.mdl(\uEBAC,segoe_icon_size) tip=['MobBatteryCharging1',tip.info] cmd=command.copy('image.mdl(\uEBAC,@segoe_icon_size)'))
		item(image=image.mdl(\uEBAD,segoe_icon_size) tip=['MobBatteryCharging2',tip.info] cmd=command.copy('image.mdl(\uEBAD,@segoe_icon_size)'))
		item(image=image.mdl(\uEBAE,segoe_icon_size) tip=['MobBatteryCharging3',tip.info] cmd=command.copy('image.mdl(\uEBAE,@segoe_icon_size)'))
		item(image=image.mdl(\uEBAF,segoe_icon_size) tip=['MobBatteryCharging4',tip.info] cmd=command.copy('image.mdl(\uEBAF,@segoe_icon_size)'))
		item(image=image.mdl(\uEBB0,segoe_icon_size) tip=['MobBatteryCharging5',tip.info] cmd=command.copy('image.mdl(\uEBB0,@segoe_icon_size)'))
		item(image=image.mdl(\uEBB1,segoe_icon_size) tip=['MobBatteryCharging6',tip.info] cmd=command.copy('image.mdl(\uEBB1,@segoe_icon_size)'))
		item(image=image.mdl(\uEBB2,segoe_icon_size) tip=['MobBatteryCharging7',tip.info] cmd=command.copy('image.mdl(\uEBB2,@segoe_icon_size)'))
		item(image=image.mdl(\uEBB3,segoe_icon_size) tip=['MobBatteryCharging8',tip.info] cmd=command.copy('image.mdl(\uEBB3,@segoe_icon_size)'))

		item(image=image.mdl(\uEBB4,segoe_icon_size) tip=['MobBatteryCharging9',tip.info] cmd=command.copy('image.mdl(\uEBB4,@segoe_icon_size)') col)
		item(image=image.mdl(\uEBB5,segoe_icon_size) tip=['MobBatteryCharging10',tip.info] cmd=command.copy('image.mdl(\uEBB5,@segoe_icon_size)'))
		item(image=image.mdl(\uEBB6,segoe_icon_size) tip=['MobBatterySaver0',tip.info] cmd=command.copy('image.mdl(\uEBB6,@segoe_icon_size)'))
		item(image=image.mdl(\uEBB7,segoe_icon_size) tip=['MobBatterySaver1',tip.info] cmd=command.copy('image.mdl(\uEBB7,@segoe_icon_size)'))
		item(image=image.mdl(\uEBB8,segoe_icon_size) tip=['MobBatterySaver2',tip.info] cmd=command.copy('image.mdl(\uEBB8,@segoe_icon_size)'))
		item(image=image.mdl(\uEBB9,segoe_icon_size) tip=['MobBatterySaver3',tip.info] cmd=command.copy('image.mdl(\uEBB9,@segoe_icon_size)'))
		item(image=image.mdl(\uEBBA,segoe_icon_size) tip=['MobBatterySaver4',tip.info] cmd=command.copy('image.mdl(\uEBBA,@segoe_icon_size)'))
		item(image=image.mdl(\uEBBB,segoe_icon_size) tip=['MobBatterySaver5',tip.info] cmd=command.copy('image.mdl(\uEBBB,@segoe_icon_size)'))
		item(image=image.mdl(\uEBBC,segoe_icon_size) tip=['MobBatterySaver6',tip.info] cmd=command.copy('image.mdl(\uEBBC,@segoe_icon_size)'))
		item(image=image.mdl(\uEBBD,segoe_icon_size) tip=['MobBatterySaver7',tip.info] cmd=command.copy('image.mdl(\uEBBD,@segoe_icon_size)'))
		item(image=image.mdl(\uEBBE,segoe_icon_size) tip=['MobBatterySaver8',tip.info] cmd=command.copy('image.mdl(\uEBBE,@segoe_icon_size)'))
		item(image=image.mdl(\uEBBF,segoe_icon_size) tip=['MobBatterySaver9',tip.info] cmd=command.copy('image.mdl(\uEBBF,@segoe_icon_size)'))
		item(image=image.mdl(\uEBC0,segoe_icon_size) tip=['MobBatterySaver10',tip.info] cmd=command.copy('image.mdl(\uEBC0,@segoe_icon_size)'))
		item(image=image.mdl(\uEBC3,segoe_icon_size) tip=['DictionaryCloud',tip.info] cmd=command.copy('image.mdl(\uEBC3,@segoe_icon_size)'))
		item(image=image.mdl(\uEBC4,segoe_icon_size) tip=['ResetDrive',tip.info] cmd=command.copy('image.mdl(\uEBC4,@segoe_icon_size)'))
		item(image=image.mdl(\uEBC5,segoe_icon_size) tip=['VolumeBars',tip.info] cmd=command.copy('image.mdl(\uEBC5,@segoe_icon_size)'))

		item(image=image.mdl(\uEBC6,segoe_icon_size) tip=['Project',tip.info] cmd=command.copy('image.mdl(\uEBC6,@segoe_icon_size)') col)
		item(image=image.mdl(\uEBD2,segoe_icon_size) tip=['AdjustHologram',tip.info] cmd=command.copy('image.mdl(\uEBD2,@segoe_icon_size)'))
		item(image=image.mdl(\uEBD4,segoe_icon_size) tip=['WifiCallBars',tip.info] cmd=command.copy('image.mdl(\uEBD4,@segoe_icon_size)'))
		item(image=image.mdl(\uEBD5,segoe_icon_size) tip=['WifiCall0',tip.info] cmd=command.copy('image.mdl(\uEBD5,@segoe_icon_size)'))
		item(image=image.mdl(\uEBD6,segoe_icon_size) tip=['WifiCall1',tip.info] cmd=command.copy('image.mdl(\uEBD6,@segoe_icon_size)'))
		item(image=image.mdl(\uEBD7,segoe_icon_size) tip=['WifiCall2',tip.info] cmd=command.copy('image.mdl(\uEBD7,@segoe_icon_size)'))
		item(image=image.mdl(\uEBD8,segoe_icon_size) tip=['WifiCall3',tip.info] cmd=command.copy('image.mdl(\uEBD8,@segoe_icon_size)'))
		item(image=image.mdl(\uEBD9,segoe_icon_size) tip=['WifiCall4',tip.info] cmd=command.copy('image.mdl(\uEBD9,@segoe_icon_size)'))
		item(image=image.mdl(\uEBDA,segoe_icon_size) tip=['Family',tip.info] cmd=command.copy('image.mdl(\uEBDA,@segoe_icon_size)'))
		item(image=image.mdl(\uEBDB,segoe_icon_size) tip=['LockFeedback',tip.info] cmd=command.copy('image.mdl(\uEBDB,@segoe_icon_size)'))
		item(image=image.mdl(\uEBDE,segoe_icon_size) tip=['DeviceDiscovery',tip.info] cmd=command.copy('image.mdl(\uEBDE,@segoe_icon_size)'))
		item(image=image.mdl(\uEBE6,segoe_icon_size) tip=['WindDirection',tip.info] cmd=command.copy('image.mdl(\uEBE6,@segoe_icon_size)'))
		item(image=image.mdl(\uEBE7,segoe_icon_size) tip=['RightArrowKeyTime0',tip.info] cmd=command.copy('image.mdl(\uEBE7,@segoe_icon_size)'))
		item(image=image.mdl(\uEBE8,segoe_icon_size) tip=['Bug',tip.info] cmd=command.copy('image.mdl(\uEBE8,@segoe_icon_size)'))
		item(image=image.mdl(\uEBFC,segoe_icon_size) tip=['TabletMode',tip.info] cmd=command.copy('image.mdl(\uEBFC,@segoe_icon_size)'))
		item(image=image.mdl(\uEBFD,segoe_icon_size) tip=['StatusCircleLeft',tip.info] cmd=command.copy('image.mdl(\uEBFD,@segoe_icon_size)'))
	}

	menu(title='MDL2 #4')
	{
		item(image=image.mdl(\uEBFE,segoe_icon_size) tip=['StatusTriangleLeft',tip.info] cmd=command.copy('image.mdl(\uEBFE,@segoe_icon_size)'))
		item(image=image.mdl(\uEBFF,segoe_icon_size) tip=['StatusErrorLeft',tip.info] cmd=command.copy('image.mdl(\uEBFF,@segoe_icon_size)'))
		item(image=image.mdl(\uEC00,segoe_icon_size) tip=['StatusWarningLeft',tip.info] cmd=command.copy('image.mdl(\uEC00,@segoe_icon_size)'))
		item(image=image.mdl(\uEC02,segoe_icon_size) tip=['MobBatteryUnknown',tip.info] cmd=command.copy('image.mdl(\uEC02,@segoe_icon_size)'))
		item(image=image.mdl(\uEC05,segoe_icon_size) tip=['NetworkTower',tip.info] cmd=command.copy('image.mdl(\uEC05,@segoe_icon_size)'))
		item(image=image.mdl(\uEC06,segoe_icon_size) tip=['CityNext',tip.info] cmd=command.copy('image.mdl(\uEC06,@segoe_icon_size)'))
		item(image=image.mdl(\uEC07,segoe_icon_size) tip=['CityNext2',tip.info] cmd=command.copy('image.mdl(\uEC07,@segoe_icon_size)'))
		item(image=image.mdl(\uEC08,segoe_icon_size) tip=['Courthouse',tip.info] cmd=command.copy('image.mdl(\uEC08,@segoe_icon_size)'))
		item(image=image.mdl(\uEC09,segoe_icon_size) tip=['Groceries',tip.info] cmd=command.copy('image.mdl(\uEC09,@segoe_icon_size)'))
		item(image=image.mdl(\uEC0A,segoe_icon_size) tip=['Sustainable',tip.info] cmd=command.copy('image.mdl(\uEC0A,@segoe_icon_size)'))
		item(image=image.mdl(\uEC0B,segoe_icon_size) tip=['BuildingEnergy',tip.info] cmd=command.copy('image.mdl(\uEC0B,@segoe_icon_size)'))
		item(image=image.mdl(\uEC11,segoe_icon_size) tip=['ToggleFilled',tip.info] cmd=command.copy('image.mdl(\uEC11,@segoe_icon_size)'))
		item(image=image.mdl(\uEC12,segoe_icon_size) tip=['ToggleBorder',tip.info] cmd=command.copy('image.mdl(\uEC12,@segoe_icon_size)'))
		item(image=image.mdl(\uEC13,segoe_icon_size) tip=['SliderThumb',tip.info] cmd=command.copy('image.mdl(\uEC13,@segoe_icon_size)'))
		item(image=image.mdl(\uEC14,segoe_icon_size) tip=['ToggleThumb',tip.info] cmd=command.copy('image.mdl(\uEC14,@segoe_icon_size)'))
		item(image=image.mdl(\uEC15,segoe_icon_size) tip=['MiracastLogoSmall',tip.info] cmd=command.copy('image.mdl(\uEC15,@segoe_icon_size)'))

		item(image=image.mdl(\uEC16,segoe_icon_size) tip=['MiracastLogoLarge',tip.info] cmd=command.copy('image.mdl(\uEC16,@segoe_icon_size)') col)
		item(image=image.mdl(\uEC19,segoe_icon_size) tip=['PLAP',tip.info] cmd=command.copy('image.mdl(\uEC19,@segoe_icon_size)'))
		item(image=image.mdl(\uEC1B,segoe_icon_size) tip=['Badge',tip.info] cmd=command.copy('image.mdl(\uEC1B,@segoe_icon_size)'))
		item(image=image.mdl(\uEC1E,segoe_icon_size) tip=['SignalRoaming',tip.info] cmd=command.copy('image.mdl(\uEC1E,@segoe_icon_size)'))
		item(image=image.mdl(\uEC20,segoe_icon_size) tip=['MobileLocked',tip.info] cmd=command.copy('image.mdl(\uEC20,@segoe_icon_size)'))
		item(image=image.mdl(\uEC24,segoe_icon_size) tip=['InsiderHubApp',tip.info] cmd=command.copy('image.mdl(\uEC24,@segoe_icon_size)'))
		item(image=image.mdl(\uEC25,segoe_icon_size) tip=['PersonalFolder',tip.info] cmd=command.copy('image.mdl(\uEC25,@segoe_icon_size)'))
		item(image=image.mdl(\uEC26,segoe_icon_size) tip=['HomeGroup',tip.info] cmd=command.copy('image.mdl(\uEC26,@segoe_icon_size)'))
		item(image=image.mdl(\uEC27,segoe_icon_size) tip=['MyNetwork',tip.info] cmd=command.copy('image.mdl(\uEC27,@segoe_icon_size)'))
		item(image=image.mdl(\uEC31,segoe_icon_size) tip=['KeyboardFull',tip.info] cmd=command.copy('image.mdl(\uEC31,@segoe_icon_size)'))
		item(image=image.mdl(\uEC32,segoe_icon_size) tip=['Cafe',tip.info] cmd=command.copy('image.mdl(\uEC32,@segoe_icon_size)'))
		item(image=image.mdl(\uEC37,segoe_icon_size) tip=['MobSignal1',tip.info] cmd=command.copy('image.mdl(\uEC37,@segoe_icon_size)'))
		item(image=image.mdl(\uEC38,segoe_icon_size) tip=['MobSignal2',tip.info] cmd=command.copy('image.mdl(\uEC38,@segoe_icon_size)'))
		item(image=image.mdl(\uEC39,segoe_icon_size) tip=['MobSignal3',tip.info] cmd=command.copy('image.mdl(\uEC39,@segoe_icon_size)'))
		item(image=image.mdl(\uEC3A,segoe_icon_size) tip=['MobSignal4',tip.info] cmd=command.copy('image.mdl(\uEC3A,@segoe_icon_size)'))
		item(image=image.mdl(\uEC3B,segoe_icon_size) tip=['MobSignal5',tip.info] cmd=command.copy('image.mdl(\uEC3B,@segoe_icon_size)'))

		item(image=image.mdl(\uEC3C,segoe_icon_size) tip=['MobWifi1',tip.info] cmd=command.copy('image.mdl(\uEC3C,@segoe_icon_size)') col)
		item(image=image.mdl(\uEC3D,segoe_icon_size) tip=['MobWifi2',tip.info] cmd=command.copy('image.mdl(\uEC3D,@segoe_icon_size)'))
		item(image=image.mdl(\uEC3E,segoe_icon_size) tip=['MobWifi3',tip.info] cmd=command.copy('image.mdl(\uEC3E,@segoe_icon_size)'))
		item(image=image.mdl(\uEC3F,segoe_icon_size) tip=['MobWifi4',tip.info] cmd=command.copy('image.mdl(\uEC3F,@segoe_icon_size)'))
		item(image=image.mdl(\uEC40,segoe_icon_size) tip=['MobAirplane',tip.info] cmd=command.copy('image.mdl(\uEC40,@segoe_icon_size)'))
		item(image=image.mdl(\uEC41,segoe_icon_size) tip=['MobBluetooth',tip.info] cmd=command.copy('image.mdl(\uEC41,@segoe_icon_size)'))
		item(image=image.mdl(\uEC42,segoe_icon_size) tip=['MobActionCenter',tip.info] cmd=command.copy('image.mdl(\uEC42,@segoe_icon_size)'))
		item(image=image.mdl(\uEC43,segoe_icon_size) tip=['MobLocation',tip.info] cmd=command.copy('image.mdl(\uEC43,@segoe_icon_size)'))
		item(image=image.mdl(\uEC44,segoe_icon_size) tip=['MobWifiHotspot',tip.info] cmd=command.copy('image.mdl(\uEC44,@segoe_icon_size)'))
		item(image=image.mdl(\uEC45,segoe_icon_size) tip=['LanguageJpn',tip.info] cmd=command.copy('image.mdl(\uEC45,@segoe_icon_size)'))
		item(image=image.mdl(\uEC46,segoe_icon_size) tip=['MobQuietHours',tip.info] cmd=command.copy('image.mdl(\uEC46,@segoe_icon_size)'))
		item(image=image.mdl(\uEC47,segoe_icon_size) tip=['MobDrivingMode',tip.info] cmd=command.copy('image.mdl(\uEC47,@segoe_icon_size)'))
		item(image=image.mdl(\uEC48,segoe_icon_size) tip=['SpeedOff',tip.info] cmd=command.copy('image.mdl(\uEC48,@segoe_icon_size)'))
		item(image=image.mdl(\uEC49,segoe_icon_size) tip=['SpeedMedium',tip.info] cmd=command.copy('image.mdl(\uEC49,@segoe_icon_size)'))
		item(image=image.mdl(\uEC4A,segoe_icon_size) tip=['SpeedHigh',tip.info] cmd=command.copy('image.mdl(\uEC4A,@segoe_icon_size)'))
		item(image=image.mdl(\uEC4E,segoe_icon_size) tip=['ThisPC',tip.info] cmd=command.copy('image.mdl(\uEC4E,@segoe_icon_size)'))

		item(image=image.mdl(\uEC4F,segoe_icon_size) tip=['MusicNote',tip.info] cmd=command.copy('image.mdl(\uEC4F,@segoe_icon_size)') col)
		item(image=image.mdl(\uEC50,segoe_icon_size) tip=['FileExplorer',tip.info] cmd=command.copy('image.mdl(\uEC50,@segoe_icon_size)'))
		item(image=image.mdl(\uEC51,segoe_icon_size) tip=['FileExplorerApp',tip.info] cmd=command.copy('image.mdl(\uEC51,@segoe_icon_size)'))
		item(image=image.mdl(\uEC52,segoe_icon_size) tip=['LeftArrowKeyTime0',tip.info] cmd=command.copy('image.mdl(\uEC52,@segoe_icon_size)'))
		item(image=image.mdl(\uEC54,segoe_icon_size) tip=['MicOff',tip.info] cmd=command.copy('image.mdl(\uEC54,@segoe_icon_size)'))
		item(image=image.mdl(\uEC55,segoe_icon_size) tip=['MicSleep',tip.info] cmd=command.copy('image.mdl(\uEC55,@segoe_icon_size)'))
		item(image=image.mdl(\uEC56,segoe_icon_size) tip=['MicError',tip.info] cmd=command.copy('image.mdl(\uEC56,@segoe_icon_size)'))
		item(image=image.mdl(\uEC57,segoe_icon_size) tip=['PlaybackRate1x',tip.info] cmd=command.copy('image.mdl(\uEC57,@segoe_icon_size)'))
		item(image=image.mdl(\uEC58,segoe_icon_size) tip=['PlaybackRateOther',tip.info] cmd=command.copy('image.mdl(\uEC58,@segoe_icon_size)'))
		item(image=image.mdl(\uEC59,segoe_icon_size) tip=['CashDrawer',tip.info] cmd=command.copy('image.mdl(\uEC59,@segoe_icon_size)'))
		item(image=image.mdl(\uEC5A,segoe_icon_size) tip=['BarcodeScanner',tip.info] cmd=command.copy('image.mdl(\uEC5A,@segoe_icon_size)'))
		item(image=image.mdl(\uEC5B,segoe_icon_size) tip=['ReceiptPrinter',tip.info] cmd=command.copy('image.mdl(\uEC5B,@segoe_icon_size)'))
		item(image=image.mdl(\uEC5C,segoe_icon_size) tip=['MagStripeReader',tip.info] cmd=command.copy('image.mdl(\uEC5C,@segoe_icon_size)'))
		item(image=image.mdl(\uEC61,segoe_icon_size) tip=['CompletedSolid',tip.info] cmd=command.copy('image.mdl(\uEC61,@segoe_icon_size)'))
		item(image=image.mdl(\uEC64,segoe_icon_size) tip=['CompanionApp',tip.info] cmd=command.copy('image.mdl(\uEC64,@segoe_icon_size)'))
		item(image=image.mdl(\uEC6C,segoe_icon_size) tip=['Favicon2',tip.info] cmd=command.copy('image.mdl(\uEC6C,@segoe_icon_size)'))

		item(image=image.mdl(\uEC6D,segoe_icon_size) tip=['SwipeRevealArt',tip.info] cmd=command.copy('image.mdl(\uEC6D,@segoe_icon_size)') col)
		item(image=image.mdl(\uEC71,segoe_icon_size) tip=['MicOn',tip.info] cmd=command.copy('image.mdl(\uEC71,@segoe_icon_size)'))
		item(image=image.mdl(\uEC72,segoe_icon_size) tip=['MicClipping',tip.info] cmd=command.copy('image.mdl(\uEC72,@segoe_icon_size)'))
		item(image=image.mdl(\uEC74,segoe_icon_size) tip=['TabletSelected',tip.info] cmd=command.copy('image.mdl(\uEC74,@segoe_icon_size)'))
		item(image=image.mdl(\uEC75,segoe_icon_size) tip=['MobileSelected',tip.info] cmd=command.copy('image.mdl(\uEC75,@segoe_icon_size)'))
		item(image=image.mdl(\uEC76,segoe_icon_size) tip=['LaptopSelected',tip.info] cmd=command.copy('image.mdl(\uEC76,@segoe_icon_size)'))
		item(image=image.mdl(\uEC77,segoe_icon_size) tip=['TVMonitorSelected',tip.info] cmd=command.copy('image.mdl(\uEC77,@segoe_icon_size)'))
		item(image=image.mdl(\uEC7A,segoe_icon_size) tip=['DeveloperTools',tip.info] cmd=command.copy('image.mdl(\uEC7A,@segoe_icon_size)'))
		item(image=image.mdl(\uEC7E,segoe_icon_size) tip=['MobCallForwarding',tip.info] cmd=command.copy('image.mdl(\uEC7E,@segoe_icon_size)'))
		item(image=image.mdl(\uEC7F,segoe_icon_size) tip=['MobCallForwardingMirrored',tip.info] cmd=command.copy('image.mdl(\uEC7F,@segoe_icon_size)'))
		item(image=image.mdl(\uEC80,segoe_icon_size) tip=['BodyCam',tip.info] cmd=command.copy('image.mdl(\uEC80,@segoe_icon_size)'))
		item(image=image.mdl(\uEC81,segoe_icon_size) tip=['PoliceCar',tip.info] cmd=command.copy('image.mdl(\uEC81,@segoe_icon_size)'))
		item(image=image.mdl(\uEC87,segoe_icon_size) tip=['Draw',tip.info] cmd=command.copy('image.mdl(\uEC87,@segoe_icon_size)'))
		item(image=image.mdl(\uEC88,segoe_icon_size) tip=['DrawSolid',tip.info] cmd=command.copy('image.mdl(\uEC88,@segoe_icon_size)'))
		item(image=image.mdl(\uEC8A,segoe_icon_size) tip=['LowerBrightness',tip.info] cmd=command.copy('image.mdl(\uEC8A,@segoe_icon_size)'))
		item(image=image.mdl(\uEC8F,segoe_icon_size) tip=['ScrollUpDown',tip.info] cmd=command.copy('image.mdl(\uEC8F,@segoe_icon_size)'))

		item(image=image.mdl(\uEC92,segoe_icon_size) tip=['DateTime',tip.info] cmd=command.copy('image.mdl(\uEC92,@segoe_icon_size)') col)
		item(image=image.mdl(\uECA5,segoe_icon_size) tip=['Tiles',tip.info] cmd=command.copy('image.mdl(\uECA5,@segoe_icon_size)'))
		item(image=image.mdl(\uECA7,segoe_icon_size) tip=['PartyLeader',tip.info] cmd=command.copy('image.mdl(\uECA7,@segoe_icon_size)'))
		item(image=image.mdl(\uECAA,segoe_icon_size) tip=['AppIconDefault',tip.info] cmd=command.copy('image.mdl(\uECAA,@segoe_icon_size)'))
		item(image=image.mdl(\uECAD,segoe_icon_size) tip=['Calories',tip.info] cmd=command.copy('image.mdl(\uECAD,@segoe_icon_size)'))
		item(image=image.mdl(\uECB9,segoe_icon_size) tip=['BandBattery0',tip.info] cmd=command.copy('image.mdl(\uECB9,@segoe_icon_size)'))
		item(image=image.mdl(\uECBA,segoe_icon_size) tip=['BandBattery1',tip.info] cmd=command.copy('image.mdl(\uECBA,@segoe_icon_size)'))
		item(image=image.mdl(\uECBB,segoe_icon_size) tip=['BandBattery2',tip.info] cmd=command.copy('image.mdl(\uECBB,@segoe_icon_size)'))
		item(image=image.mdl(\uECBC,segoe_icon_size) tip=['BandBattery3',tip.info] cmd=command.copy('image.mdl(\uECBC,@segoe_icon_size)'))
		item(image=image.mdl(\uECBD,segoe_icon_size) tip=['BandBattery4',tip.info] cmd=command.copy('image.mdl(\uECBD,@segoe_icon_size)'))
		item(image=image.mdl(\uECBE,segoe_icon_size) tip=['BandBattery5',tip.info] cmd=command.copy('image.mdl(\uECBE,@segoe_icon_size)'))
		item(image=image.mdl(\uECBF,segoe_icon_size) tip=['BandBattery6',tip.info] cmd=command.copy('image.mdl(\uECBF,@segoe_icon_size)'))
		item(image=image.mdl(\uECC4,segoe_icon_size) tip=['AddSurfaceHub',tip.info] cmd=command.copy('image.mdl(\uECC4,@segoe_icon_size)'))
		item(image=image.mdl(\uECC5,segoe_icon_size) tip=['DevUpdate',tip.info] cmd=command.copy('image.mdl(\uECC5,@segoe_icon_size)'))
		item(image=image.mdl(\uECC6,segoe_icon_size) tip=['Unit',tip.info] cmd=command.copy('image.mdl(\uECC6,@segoe_icon_size)'))
		item(image=image.mdl(\uECC8,segoe_icon_size) tip=['AddTo',tip.info] cmd=command.copy('image.mdl(\uECC8,@segoe_icon_size)'))

		item(image=image.mdl(\uECC9,segoe_icon_size) tip=['RemoveFrom',tip.info] cmd=command.copy('image.mdl(\uECC9,@segoe_icon_size)') col)
		item(image=image.mdl(\uECCA,segoe_icon_size) tip=['RadioBtnOff',tip.info] cmd=command.copy('image.mdl(\uECCA,@segoe_icon_size)'))
		item(image=image.mdl(\uECCB,segoe_icon_size) tip=['RadioBtnOn',tip.info] cmd=command.copy('image.mdl(\uECCB,@segoe_icon_size)'))
		item(image=image.mdl(\uECCC,segoe_icon_size) tip=['RadioBullet2',tip.info] cmd=command.copy('image.mdl(\uECCC,@segoe_icon_size)'))
		item(image=image.mdl(\uECCD,segoe_icon_size) tip=['ExploreContent',tip.info] cmd=command.copy('image.mdl(\uECCD,@segoe_icon_size)'))
		item(image=image.mdl(\uECE4,segoe_icon_size) tip=['Blocked2',tip.info] cmd=command.copy('image.mdl(\uECE4,@segoe_icon_size)'))
		item(image=image.mdl(\uECE7,segoe_icon_size) tip=['ScrollMode',tip.info] cmd=command.copy('image.mdl(\uECE7,@segoe_icon_size)'))
		item(image=image.mdl(\uECE8,segoe_icon_size) tip=['ZoomMode',tip.info] cmd=command.copy('image.mdl(\uECE8,@segoe_icon_size)'))
		item(image=image.mdl(\uECE9,segoe_icon_size) tip=['PanMode',tip.info] cmd=command.copy('image.mdl(\uECE9,@segoe_icon_size)'))
		item(image=image.mdl(\uECF0,segoe_icon_size) tip=['WiredUSB',tip.info] cmd=command.copy('image.mdl(\uECF0,@segoe_icon_size)'))
		item(image=image.mdl(\uECF1,segoe_icon_size) tip=['WirelessUSB',tip.info] cmd=command.copy('image.mdl(\uECF1,@segoe_icon_size)'))
		item(image=image.mdl(\uECF3,segoe_icon_size) tip=['USBSafeConnect',tip.info] cmd=command.copy('image.mdl(\uECF3,@segoe_icon_size)'))
		item(image=image.mdl(\uED0C,segoe_icon_size) tip=['ActionCenterNotificationMirrored',tip.info] cmd=command.copy('image.mdl(\uED0C,@segoe_icon_size)'))
		item(image=image.mdl(\uED0D,segoe_icon_size) tip=['ActionCenterMirrored',tip.info] cmd=command.copy('image.mdl(\uED0D,@segoe_icon_size)'))
		item(image=image.mdl(\uED0E,segoe_icon_size) tip=['SubscriptionAdd',tip.info] cmd=command.copy('image.mdl(\uED0E,@segoe_icon_size)'))
		item(image=image.mdl(\uED10,segoe_icon_size) tip=['ResetDevice',tip.info] cmd=command.copy('image.mdl(\uED10,@segoe_icon_size)'))

		item(image=image.mdl(\uED11,segoe_icon_size) tip=['SubscriptionAddMirrored',tip.info] cmd=command.copy('image.mdl(\uED11,@segoe_icon_size)') col)
		item(image=image.mdl(\uED14,segoe_icon_size) tip=['QRCode',tip.info] cmd=command.copy('image.mdl(\uED14,@segoe_icon_size)'))
		item(image=image.mdl(\uED15,segoe_icon_size) tip=['Feedback',tip.info] cmd=command.copy('image.mdl(\uED15,@segoe_icon_size)'))
		item(image=image.mdl(\uED1E,segoe_icon_size) tip=['Subtitles',tip.info] cmd=command.copy('image.mdl(\uED1E,@segoe_icon_size)'))
		item(image=image.mdl(\uED1F,segoe_icon_size) tip=['SubtitlesAudio',tip.info] cmd=command.copy('image.mdl(\uED1F,@segoe_icon_size)'))
		item(image=image.mdl(\uED25,segoe_icon_size) tip=['OpenFolderHorizontal',tip.info] cmd=command.copy('image.mdl(\uED25,@segoe_icon_size)'))
		item(image=image.mdl(\uED28,segoe_icon_size) tip=['CalendarMirrored',tip.info] cmd=command.copy('image.mdl(\uED28,@segoe_icon_size)'))
		item(image=image.mdl(\uED2A,segoe_icon_size) tip=['MobeSIM',tip.info] cmd=command.copy('image.mdl(\uED2A,@segoe_icon_size)'))
		item(image=image.mdl(\uED2B,segoe_icon_size) tip=['MobeSIMNoProfile',tip.info] cmd=command.copy('image.mdl(\uED2B,@segoe_icon_size)'))
		item(image=image.mdl(\uED2C,segoe_icon_size) tip=['MobeSIMLocked',tip.info] cmd=command.copy('image.mdl(\uED2C,@segoe_icon_size)'))
		item(image=image.mdl(\uED2D,segoe_icon_size) tip=['MobeSIMBusy',tip.info] cmd=command.copy('image.mdl(\uED2D,@segoe_icon_size)'))
		item(image=image.mdl(\uED2E,segoe_icon_size) tip=['SignalError',tip.info] cmd=command.copy('image.mdl(\uED2E,@segoe_icon_size)'))
		item(image=image.mdl(\uED2F,segoe_icon_size) tip=['StreamingEnterprise',tip.info] cmd=command.copy('image.mdl(\uED2F,@segoe_icon_size)'))
		item(image=image.mdl(\uED30,segoe_icon_size) tip=['Headphone0',tip.info] cmd=command.copy('image.mdl(\uED30,@segoe_icon_size)'))
		item(image=image.mdl(\uED31,segoe_icon_size) tip=['Headphone1',tip.info] cmd=command.copy('image.mdl(\uED31,@segoe_icon_size)'))
		item(image=image.mdl(\uED32,segoe_icon_size) tip=['Headphone2',tip.info] cmd=command.copy('image.mdl(\uED32,@segoe_icon_size)'))

		item(image=image.mdl(\uED33,segoe_icon_size) tip=['Headphone3',tip.info] cmd=command.copy('image.mdl(\uED33,@segoe_icon_size)') col)
		item(image=image.mdl(\uED35,segoe_icon_size) tip=['Apps',tip.info] cmd=command.copy('image.mdl(\uED35,@segoe_icon_size)'))
		item(image=image.mdl(\uED39,segoe_icon_size) tip=['KeyboardBrightness',tip.info] cmd=command.copy('image.mdl(\uED39,@segoe_icon_size)'))
		item(image=image.mdl(\uED3A,segoe_icon_size) tip=['KeyboardLowerBrightness',tip.info] cmd=command.copy('image.mdl(\uED3A,@segoe_icon_size)'))
		item(image=image.mdl(\uED3C,segoe_icon_size) tip=['SkipBack10',tip.info] cmd=command.copy('image.mdl(\uED3C,@segoe_icon_size)'))
		item(image=image.mdl(\uED3D,segoe_icon_size) tip=['SkipForward30',tip.info] cmd=command.copy('image.mdl(\uED3D,@segoe_icon_size)'))
		item(image=image.mdl(\uED41,segoe_icon_size) tip=['TreeFolderFolder',tip.info] cmd=command.copy('image.mdl(\uED41,@segoe_icon_size)'))
		item(image=image.mdl(\uED42,segoe_icon_size) tip=['TreeFolderFolderFill',tip.info] cmd=command.copy('image.mdl(\uED42,@segoe_icon_size)'))
		item(image=image.mdl(\uED43,segoe_icon_size) tip=['TreeFolderFolderOpen',tip.info] cmd=command.copy('image.mdl(\uED43,@segoe_icon_size)'))
		item(image=image.mdl(\uED44,segoe_icon_size) tip=['TreeFolderFolderOpenFill',tip.info] cmd=command.copy('image.mdl(\uED44,@segoe_icon_size)'))
		item(image=image.mdl(\uED47,segoe_icon_size) tip=['MultimediaDMP',tip.info] cmd=command.copy('image.mdl(\uED47,@segoe_icon_size)'))
		item(image=image.mdl(\uED4C,segoe_icon_size) tip=['KeyboardOneHanded',tip.info] cmd=command.copy('image.mdl(\uED4C,@segoe_icon_size)'))
		item(image=image.mdl(\uED4D,segoe_icon_size) tip=['Narrator',tip.info] cmd=command.copy('image.mdl(\uED4D,@segoe_icon_size)'))
		item(image=image.mdl(\uED53,segoe_icon_size) tip=['EmojiTabPeople',tip.info] cmd=command.copy('image.mdl(\uED53,@segoe_icon_size)'))
		item(image=image.mdl(\uED54,segoe_icon_size) tip=['EmojiTabSmilesAnimals',tip.info] cmd=command.copy('image.mdl(\uED54,@segoe_icon_size)'))
		item(image=image.mdl(\uED55,segoe_icon_size) tip=['EmojiTabCelebrationObjects',tip.info] cmd=command.copy('image.mdl(\uED55,@segoe_icon_size)'))

		item(image=image.mdl(\uED56,segoe_icon_size) tip=['EmojiTabFoodPlants',tip.info] cmd=command.copy('image.mdl(\uED56,@segoe_icon_size)') col)
		item(image=image.mdl(\uED57,segoe_icon_size) tip=['EmojiTabTransitPlaces',tip.info] cmd=command.copy('image.mdl(\uED57,@segoe_icon_size)'))
		item(image=image.mdl(\uED58,segoe_icon_size) tip=['EmojiTabSymbols',tip.info] cmd=command.copy('image.mdl(\uED58,@segoe_icon_size)'))
		item(image=image.mdl(\uED59,segoe_icon_size) tip=['EmojiTabTextSmiles',tip.info] cmd=command.copy('image.mdl(\uED59,@segoe_icon_size)'))
		item(image=image.mdl(\uED5A,segoe_icon_size) tip=['EmojiTabFavorites',tip.info] cmd=command.copy('image.mdl(\uED5A,@segoe_icon_size)'))
		item(image=image.mdl(\uED5B,segoe_icon_size) tip=['EmojiSwatch',tip.info] cmd=command.copy('image.mdl(\uED5B,@segoe_icon_size)'))
		item(image=image.mdl(\uED5C,segoe_icon_size) tip=['ConnectApp',tip.info] cmd=command.copy('image.mdl(\uED5C,@segoe_icon_size)'))
		item(image=image.mdl(\uED5D,segoe_icon_size) tip=['CompanionDeviceFramework',tip.info] cmd=command.copy('image.mdl(\uED5D,@segoe_icon_size)'))
		item(image=image.mdl(\uED5E,segoe_icon_size) tip=['Ruler',tip.info] cmd=command.copy('image.mdl(\uED5E,@segoe_icon_size)'))
		item(image=image.mdl(\uED5F,segoe_icon_size) tip=['FingerInking',tip.info] cmd=command.copy('image.mdl(\uED5F,@segoe_icon_size)'))
		item(image=image.mdl(\uED60,segoe_icon_size) tip=['StrokeErase',tip.info] cmd=command.copy('image.mdl(\uED60,@segoe_icon_size)'))
		item(image=image.mdl(\uED61,segoe_icon_size) tip=['PointErase',tip.info] cmd=command.copy('image.mdl(\uED61,@segoe_icon_size)'))
		item(image=image.mdl(\uED62,segoe_icon_size) tip=['ClearAllInk',tip.info] cmd=command.copy('image.mdl(\uED62,@segoe_icon_size)'))
		item(image=image.mdl(\uED63,segoe_icon_size) tip=['Pencil',tip.info] cmd=command.copy('image.mdl(\uED63,@segoe_icon_size)'))
		item(image=image.mdl(\uED64,segoe_icon_size) tip=['Marker',tip.info] cmd=command.copy('image.mdl(\uED64,@segoe_icon_size)'))
		item(image=image.mdl(\uED65,segoe_icon_size) tip=['InkingCaret',tip.info] cmd=command.copy('image.mdl(\uED65,@segoe_icon_size)'))

		item(image=image.mdl(\uED66,segoe_icon_size) tip=['InkingColorOutline',tip.info] cmd=command.copy('image.mdl(\uED66,@segoe_icon_size)') col)
		item(image=image.mdl(\uED67,segoe_icon_size) tip=['InkingColorFill',tip.info] cmd=command.copy('image.mdl(\uED67,@segoe_icon_size)'))
		item(image=image.mdl(\uEDA2,segoe_icon_size) tip=['HardDrive',tip.info] cmd=command.copy('image.mdl(\uEDA2,@segoe_icon_size)'))
		item(image=image.mdl(\uEDA3,segoe_icon_size) tip=['NetworkAdapter',tip.info] cmd=command.copy('image.mdl(\uEDA3,@segoe_icon_size)'))
		item(image=image.mdl(\uEDA4,segoe_icon_size) tip=['Touchscreen',tip.info] cmd=command.copy('image.mdl(\uEDA4,@segoe_icon_size)'))
		item(image=image.mdl(\uEDA5,segoe_icon_size) tip=['NetworkPrinter',tip.info] cmd=command.copy('image.mdl(\uEDA5,@segoe_icon_size)'))
		item(image=image.mdl(\uEDA6,segoe_icon_size) tip=['CloudPrinter',tip.info] cmd=command.copy('image.mdl(\uEDA6,@segoe_icon_size)'))
		item(image=image.mdl(\uEDA7,segoe_icon_size) tip=['KeyboardShortcut',tip.info] cmd=command.copy('image.mdl(\uEDA7,@segoe_icon_size)'))
		item(image=image.mdl(\uEDA8,segoe_icon_size) tip=['BrushSize',tip.info] cmd=command.copy('image.mdl(\uEDA8,@segoe_icon_size)'))
		item(image=image.mdl(\uEDA9,segoe_icon_size) tip=['NarratorForward',tip.info] cmd=command.copy('image.mdl(\uEDA9,@segoe_icon_size)'))
		item(image=image.mdl(\uEDAA,segoe_icon_size) tip=['NarratorForwardMirrored',tip.info] cmd=command.copy('image.mdl(\uEDAA,@segoe_icon_size)'))
		item(image=image.mdl(\uEDAB,segoe_icon_size) tip=['SyncBadge12',tip.info] cmd=command.copy('image.mdl(\uEDAB,@segoe_icon_size)'))
		item(image=image.mdl(\uEDAC,segoe_icon_size) tip=['RingerBadge12',tip.info] cmd=command.copy('image.mdl(\uEDAC,@segoe_icon_size)'))
		item(image=image.mdl(\uEDAD,segoe_icon_size) tip=['AsteriskBadge12',tip.info] cmd=command.copy('image.mdl(\uEDAD,@segoe_icon_size)'))
		item(image=image.mdl(\uEDAE,segoe_icon_size) tip=['ErrorBadge12',tip.info] cmd=command.copy('image.mdl(\uEDAE,@segoe_icon_size)'))
		item(image=image.mdl(\uEDAF,segoe_icon_size) tip=['CircleRingBadge12',tip.info] cmd=command.copy('image.mdl(\uEDAF,@segoe_icon_size)'))

		item(image=image.mdl(\uEDB0,segoe_icon_size) tip=['CircleFillBadge12',tip.info] cmd=command.copy('image.mdl(\uEDB0,@segoe_icon_size)') col)
		item(image=image.mdl(\uEDB1,segoe_icon_size) tip=['ImportantBadge12',tip.info] cmd=command.copy('image.mdl(\uEDB1,@segoe_icon_size)'))
		item(image=image.mdl(\uEDB3,segoe_icon_size) tip=['MailBadge12',tip.info] cmd=command.copy('image.mdl(\uEDB3,@segoe_icon_size)'))
		item(image=image.mdl(\uEDB4,segoe_icon_size) tip=['PauseBadge12',tip.info] cmd=command.copy('image.mdl(\uEDB4,@segoe_icon_size)'))
		item(image=image.mdl(\uEDB5,segoe_icon_size) tip=['PlayBadge12',tip.info] cmd=command.copy('image.mdl(\uEDB5,@segoe_icon_size)'))
		item(image=image.mdl(\uEDC6,segoe_icon_size) tip=['PenWorkspace',tip.info] cmd=command.copy('image.mdl(\uEDC6,@segoe_icon_size)'))
		item(image=image.mdl(\uEDD6,segoe_icon_size) tip=['CaretRight8',tip.info] cmd=command.copy('image.mdl(\uEDD6,@segoe_icon_size)'))
		item(image=image.mdl(\uEDD9,segoe_icon_size) tip=['CaretLeftSolid8',tip.info] cmd=command.copy('image.mdl(\uEDD9,@segoe_icon_size)'))
		item(image=image.mdl(\uEDDA,segoe_icon_size) tip=['CaretRightSolid8',tip.info] cmd=command.copy('image.mdl(\uEDDA,@segoe_icon_size)'))
		item(image=image.mdl(\uEDDB,segoe_icon_size) tip=['CaretUpSolid8',tip.info] cmd=command.copy('image.mdl(\uEDDB,@segoe_icon_size)'))
		item(image=image.mdl(\uEDDC,segoe_icon_size) tip=['CaretDownSolid8',tip.info] cmd=command.copy('image.mdl(\uEDDC,@segoe_icon_size)'))
		item(image=image.mdl(\uEDE0,segoe_icon_size) tip=['Strikethrough',tip.info] cmd=command.copy('image.mdl(\uEDE0,@segoe_icon_size)'))
		item(image=image.mdl(\uEDE1,segoe_icon_size) tip=['Export',tip.info] cmd=command.copy('image.mdl(\uEDE1,@segoe_icon_size)'))
		item(image=image.mdl(\uEDE2,segoe_icon_size) tip=['ExportMirrored',tip.info] cmd=command.copy('image.mdl(\uEDE2,@segoe_icon_size)'))
		item(image=image.mdl(\uEDE3,segoe_icon_size) tip=['ButtonMenu',tip.info] cmd=command.copy('image.mdl(\uEDE3,@segoe_icon_size)'))
		item(image=image.mdl(\uEDE4,segoe_icon_size) tip=['CloudSearch',tip.info] cmd=command.copy('image.mdl(\uEDE4,@segoe_icon_size)'))

		item(image=image.mdl(\uEDE5,segoe_icon_size) tip=['PinyinIMELogo',tip.info] cmd=command.copy('image.mdl(\uEDE5,@segoe_icon_size)') col)
		item(image=image.mdl(\uEDFB,segoe_icon_size) tip=['CalligraphyPen',tip.info] cmd=command.copy('image.mdl(\uEDFB,@segoe_icon_size)'))
		item(image=image.mdl(\uEE35,segoe_icon_size) tip=['ReplyMirrored',tip.info] cmd=command.copy('image.mdl(\uEE35,@segoe_icon_size)'))
		item(image=image.mdl(\uEE3F,segoe_icon_size) tip=['LockscreenDesktop',tip.info] cmd=command.copy('image.mdl(\uEE3F,@segoe_icon_size)'))
		item(image=image.mdl(\uEE40,segoe_icon_size) tip=['TaskViewSettings',tip.info] cmd=command.copy('image.mdl(\uEE40,@segoe_icon_size)'))
		item(image=image.mdl(\uEE47,segoe_icon_size) tip=['MiniExpand2Mirrored',tip.info] cmd=command.copy('image.mdl(\uEE47,@segoe_icon_size)'))
		item(image=image.mdl(\uEE49,segoe_icon_size) tip=['MiniContract2Mirrored',tip.info] cmd=command.copy('image.mdl(\uEE49,@segoe_icon_size)'))
		item(image=image.mdl(\uEE4A,segoe_icon_size) tip=['Play36',tip.info] cmd=command.copy('image.mdl(\uEE4A,@segoe_icon_size)'))
		item(image=image.mdl(\uEE56,segoe_icon_size) tip=['PenPalette',tip.info] cmd=command.copy('image.mdl(\uEE56,@segoe_icon_size)'))
		item(image=image.mdl(\uEE57,segoe_icon_size) tip=['GuestUser',tip.info] cmd=command.copy('image.mdl(\uEE57,@segoe_icon_size)'))
		item(image=image.mdl(\uEE63,segoe_icon_size) tip=['SettingsBattery',tip.info] cmd=command.copy('image.mdl(\uEE63,@segoe_icon_size)'))
		item(image=image.mdl(\uEE64,segoe_icon_size) tip=['TaskbarPhone',tip.info] cmd=command.copy('image.mdl(\uEE64,@segoe_icon_size)'))
		item(image=image.mdl(\uEE65,segoe_icon_size) tip=['LockScreenGlance',tip.info] cmd=command.copy('image.mdl(\uEE65,@segoe_icon_size)'))
		item(image=image.mdl(\uEE6F,segoe_icon_size) tip=['GenericScan',tip.info] cmd=command.copy('image.mdl(\uEE6F,@segoe_icon_size)'))
		item(image=image.mdl(\uEE71,segoe_icon_size) tip=['ImageExport',tip.info] cmd=command.copy('image.mdl(\uEE71,@segoe_icon_size)'))
		item(image=image.mdl(\uEE77,segoe_icon_size) tip=['WifiEthernet',tip.info] cmd=command.copy('image.mdl(\uEE77,@segoe_icon_size)'))

		item(image=image.mdl(\uEE79,segoe_icon_size) tip=['ActionCenterQuiet',tip.info] cmd=command.copy('image.mdl(\uEE79,@segoe_icon_size)') col)
		item(image=image.mdl(\uEE7A,segoe_icon_size) tip=['ActionCenterQuietNotification',tip.info] cmd=command.copy('image.mdl(\uEE7A,@segoe_icon_size)'))
		item(image=image.mdl(\uEE92,segoe_icon_size) tip=['TrackersMirrored',tip.info] cmd=command.copy('image.mdl(\uEE92,@segoe_icon_size)'))
		item(image=image.mdl(\uEE93,segoe_icon_size) tip=['DateTimeMirrored',tip.info] cmd=command.copy('image.mdl(\uEE93,@segoe_icon_size)'))
		item(image=image.mdl(\uEE94,segoe_icon_size) tip=['Wheel',tip.info] cmd=command.copy('image.mdl(\uEE94,@segoe_icon_size)'))
		item(image=image.mdl(\uEEA3,segoe_icon_size) tip=['VirtualMachineGroup',tip.info] cmd=command.copy('image.mdl(\uEEA3,@segoe_icon_size)'))
		item(image=image.mdl(\uEECA,segoe_icon_size) tip=['ButtonView2',tip.info] cmd=command.copy('image.mdl(\uEECA,@segoe_icon_size)'))
		item(image=image.mdl(\uEF15,segoe_icon_size) tip=['PenWorkspaceMirrored',tip.info] cmd=command.copy('image.mdl(\uEF15,@segoe_icon_size)'))
		item(image=image.mdl(\uEF16,segoe_icon_size) tip=['PenPaletteMirrored',tip.info] cmd=command.copy('image.mdl(\uEF16,@segoe_icon_size)'))
		item(image=image.mdl(\uEF17,segoe_icon_size) tip=['StrokeEraseMirrored',tip.info] cmd=command.copy('image.mdl(\uEF17,@segoe_icon_size)'))
		item(image=image.mdl(\uEF18,segoe_icon_size) tip=['PointEraseMirrored',tip.info] cmd=command.copy('image.mdl(\uEF18,@segoe_icon_size)'))
		item(image=image.mdl(\uEF19,segoe_icon_size) tip=['ClearAllInkMirrored',tip.info] cmd=command.copy('image.mdl(\uEF19,@segoe_icon_size)'))
		item(image=image.mdl(\uEF1F,segoe_icon_size) tip=['BackgroundToggle',tip.info] cmd=command.copy('image.mdl(\uEF1F,@segoe_icon_size)'))
		item(image=image.mdl(\uEF20,segoe_icon_size) tip=['Marquee',tip.info] cmd=command.copy('image.mdl(\uEF20,@segoe_icon_size)'))
		item(image=image.mdl(\uEF2C,segoe_icon_size) tip=['ChromeCloseContrast',tip.info] cmd=command.copy('image.mdl(\uEF2C,@segoe_icon_size)'))
		item(image=image.mdl(\uEF2D,segoe_icon_size) tip=['ChromeMinimizeContrast',tip.info] cmd=command.copy('image.mdl(\uEF2D,@segoe_icon_size)'))

		item(image=image.mdl(\uEF2E,segoe_icon_size) tip=['ChromeMaximizeContrast',tip.info] cmd=command.copy('image.mdl(\uEF2E,@segoe_icon_size)') col)
		item(image=image.mdl(\uEF2F,segoe_icon_size) tip=['ChromeRestoreContrast',tip.info] cmd=command.copy('image.mdl(\uEF2F,@segoe_icon_size)'))
		item(image=image.mdl(\uEF31,segoe_icon_size) tip=['TrafficLight',tip.info] cmd=command.copy('image.mdl(\uEF31,@segoe_icon_size)'))
		item(image=image.mdl(\uEF3B,segoe_icon_size) tip=['Replay',tip.info] cmd=command.copy('image.mdl(\uEF3B,@segoe_icon_size)'))
		item(image=image.mdl(\uEF3C,segoe_icon_size) tip=['Eyedropper',tip.info] cmd=command.copy('image.mdl(\uEF3C,@segoe_icon_size)'))
		item(image=image.mdl(\uEF3D,segoe_icon_size) tip=['LineDisplay',tip.info] cmd=command.copy('image.mdl(\uEF3D,@segoe_icon_size)'))
		item(image=image.mdl(\uEF3E,segoe_icon_size) tip=['PINPad',tip.info] cmd=command.copy('image.mdl(\uEF3E,@segoe_icon_size)'))
		item(image=image.mdl(\uEF3F,segoe_icon_size) tip=['SignatureCapture',tip.info] cmd=command.copy('image.mdl(\uEF3F,@segoe_icon_size)'))
		item(image=image.mdl(\uEF40,segoe_icon_size) tip=['ChipCardCreditCardReader',tip.info] cmd=command.copy('image.mdl(\uEF40,@segoe_icon_size)'))
		item(image=image.mdl(\uEF58,segoe_icon_size) tip=['PlayerSettings',tip.info] cmd=command.copy('image.mdl(\uEF58,@segoe_icon_size)'))
		item(image=image.mdl(\uEF6B,segoe_icon_size) tip=['LandscapeOrientation',tip.info] cmd=command.copy('image.mdl(\uEF6B,@segoe_icon_size)'))
		item(image=image.mdl(\uEF90,segoe_icon_size) tip=['Flow',tip.info] cmd=command.copy('image.mdl(\uEF90,@segoe_icon_size)'))
		item(image=image.mdl(\uEFA5,segoe_icon_size) tip=['Touchpad',tip.info] cmd=command.copy('image.mdl(\uEFA5,@segoe_icon_size)'))
		item(image=image.mdl(\uEFA9,segoe_icon_size) tip=['Speech',tip.info] cmd=command.copy('image.mdl(\uEFA9,@segoe_icon_size)'))
		item(image=image.mdl(\uF000,segoe_icon_size) tip=['KnowledgeArticle',tip.info] cmd=command.copy('image.mdl(\uF000,@segoe_icon_size)'))
		item(image=image.mdl(\uF003,segoe_icon_size) tip=['Relationship',tip.info] cmd=command.copy('image.mdl(\uF003,@segoe_icon_size)'))

		item(image=image.mdl(\uF080,segoe_icon_size) tip=['DefaultAPN',tip.info] cmd=command.copy('image.mdl(\uF080,@segoe_icon_size)') col)
		item(image=image.mdl(\uF081,segoe_icon_size) tip=['UserAPN',tip.info] cmd=command.copy('image.mdl(\uF081,@segoe_icon_size)'))
		item(image=image.mdl(\uF085,segoe_icon_size) tip=['DoublePinyin',tip.info] cmd=command.copy('image.mdl(\uF085,@segoe_icon_size)'))
		item(image=image.mdl(\uF08C,segoe_icon_size) tip=['BlueLight',tip.info] cmd=command.copy('image.mdl(\uF08C,@segoe_icon_size)'))
		item(image=image.mdl(\uF093,segoe_icon_size) tip=['ButtonA',tip.info] cmd=command.copy('image.mdl(\uF093,@segoe_icon_size)'))
		item(image=image.mdl(\uF094,segoe_icon_size) tip=['ButtonB',tip.info] cmd=command.copy('image.mdl(\uF094,@segoe_icon_size)'))
		item(image=image.mdl(\uF095,segoe_icon_size) tip=['ButtonY',tip.info] cmd=command.copy('image.mdl(\uF095,@segoe_icon_size)'))
		item(image=image.mdl(\uF096,segoe_icon_size) tip=['ButtonX',tip.info] cmd=command.copy('image.mdl(\uF096,@segoe_icon_size)'))
		item(image=image.mdl(\uF0AD,segoe_icon_size) tip=['ArrowUp8',tip.info] cmd=command.copy('image.mdl(\uF0AD,@segoe_icon_size)'))
		item(image=image.mdl(\uF0AE,segoe_icon_size) tip=['ArrowDown8',tip.info] cmd=command.copy('image.mdl(\uF0AE,@segoe_icon_size)'))
		item(image=image.mdl(\uF0AF,segoe_icon_size) tip=['ArrowRight8',tip.info] cmd=command.copy('image.mdl(\uF0AF,@segoe_icon_size)'))
		item(image=image.mdl(\uF0B0,segoe_icon_size) tip=['ArrowLeft8',tip.info] cmd=command.copy('image.mdl(\uF0B0,@segoe_icon_size)'))
		item(image=image.mdl(\uF0B2,segoe_icon_size) tip=['QuarentinedItems',tip.info] cmd=command.copy('image.mdl(\uF0B2,@segoe_icon_size)'))
		item(image=image.mdl(\uF0B3,segoe_icon_size) tip=['QuarentinedItemsMirrored',tip.info] cmd=command.copy('image.mdl(\uF0B3,@segoe_icon_size)'))
		item(image=image.mdl(\uF0B4,segoe_icon_size) tip=['Protractor',tip.info] cmd=command.copy('image.mdl(\uF0B4,@segoe_icon_size)'))
		item(image=image.mdl(\uF0B5,segoe_icon_size) tip=['ChecklistMirrored',tip.info] cmd=command.copy('image.mdl(\uF0B5,@segoe_icon_size)'))
	}

	menu(title='MDL2 #5')
	{
		item(image=image.mdl(\uF0B6,segoe_icon_size) tip=['StatusCircle7',tip.info] cmd=command.copy('image.mdl(\uF0B6,@segoe_icon_size)'))
		item(image=image.mdl(\uF0B7,segoe_icon_size) tip=['StatusCheckmark7',tip.info] cmd=command.copy('image.mdl(\uF0B7,@segoe_icon_size)'))
		item(image=image.mdl(\uF0B8,segoe_icon_size) tip=['StatusErrorCircle7',tip.info] cmd=command.copy('image.mdl(\uF0B8,@segoe_icon_size)'))
		item(image=image.mdl(\uF0B9,segoe_icon_size) tip=['Connected',tip.info] cmd=command.copy('image.mdl(\uF0B9,@segoe_icon_size)'))
		item(image=image.mdl(\uF0C6,segoe_icon_size) tip=['PencilFill',tip.info] cmd=command.copy('image.mdl(\uF0C6,@segoe_icon_size)'))
		item(image=image.mdl(\uF0C7,segoe_icon_size) tip=['CalligraphyFill',tip.info] cmd=command.copy('image.mdl(\uF0C7,@segoe_icon_size)'))
		item(image=image.mdl(\uF0CA,segoe_icon_size) tip=['QuarterStarLeft',tip.info] cmd=command.copy('image.mdl(\uF0CA,@segoe_icon_size)'))
		item(image=image.mdl(\uF0CB,segoe_icon_size) tip=['QuarterStarRight',tip.info] cmd=command.copy('image.mdl(\uF0CB,@segoe_icon_size)'))
		item(image=image.mdl(\uF0CC,segoe_icon_size) tip=['ThreeQuarterStarLeft',tip.info] cmd=command.copy('image.mdl(\uF0CC,@segoe_icon_size)'))
		item(image=image.mdl(\uF0CD,segoe_icon_size) tip=['ThreeQuarterStarRight',tip.info] cmd=command.copy('image.mdl(\uF0CD,@segoe_icon_size)'))
		item(image=image.mdl(\uF0CE,segoe_icon_size) tip=['QuietHoursBadge12',tip.info] cmd=command.copy('image.mdl(\uF0CE,@segoe_icon_size)'))
		item(image=image.mdl(\uF0D2,segoe_icon_size) tip=['BackMirrored',tip.info] cmd=command.copy('image.mdl(\uF0D2,@segoe_icon_size)'))
		item(image=image.mdl(\uF0D3,segoe_icon_size) tip=['ForwardMirrored',tip.info] cmd=command.copy('image.mdl(\uF0D3,@segoe_icon_size)'))
		item(image=image.mdl(\uF0D5,segoe_icon_size) tip=['ChromeBackContrast',tip.info] cmd=command.copy('image.mdl(\uF0D5,@segoe_icon_size)'))
		item(image=image.mdl(\uF0D6,segoe_icon_size) tip=['ChromeBackContrastMirrored',tip.info] cmd=command.copy('image.mdl(\uF0D6,@segoe_icon_size)'))
		item(image=image.mdl(\uF0D7,segoe_icon_size) tip=['ChromeBackToWindowContrast',tip.info] cmd=command.copy('image.mdl(\uF0D7,@segoe_icon_size)'))

		item(image=image.mdl(\uF0D8,segoe_icon_size) tip=['ChromeFullScreenContrast',tip.info] cmd=command.copy('image.mdl(\uF0D8,@segoe_icon_size)') col)
		item(image=image.mdl(\uF0E2,segoe_icon_size) tip=['GridView',tip.info] cmd=command.copy('image.mdl(\uF0E2,@segoe_icon_size)'))
		item(image=image.mdl(\uF0E3,segoe_icon_size) tip=['ClipboardList',tip.info] cmd=command.copy('image.mdl(\uF0E3,@segoe_icon_size)'))
		item(image=image.mdl(\uF0E4,segoe_icon_size) tip=['ClipboardListMirrored',tip.info] cmd=command.copy('image.mdl(\uF0E4,@segoe_icon_size)'))
		item(image=image.mdl(\uF0E5,segoe_icon_size) tip=['OutlineQuarterStarLeft',tip.info] cmd=command.copy('image.mdl(\uF0E5,@segoe_icon_size)'))
		item(image=image.mdl(\uF0E6,segoe_icon_size) tip=['OutlineQuarterStarRight',tip.info] cmd=command.copy('image.mdl(\uF0E6,@segoe_icon_size)'))
		item(image=image.mdl(\uF0E7,segoe_icon_size) tip=['OutlineHalfStarLeft',tip.info] cmd=command.copy('image.mdl(\uF0E7,@segoe_icon_size)'))
		item(image=image.mdl(\uF0E8,segoe_icon_size) tip=['OutlineHalfStarRight',tip.info] cmd=command.copy('image.mdl(\uF0E8,@segoe_icon_size)'))
		item(image=image.mdl(\uF0E9,segoe_icon_size) tip=['OutlineThreeQuarterStarLeft',tip.info] cmd=command.copy('image.mdl(\uF0E9,@segoe_icon_size)'))
		item(image=image.mdl(\uF0EA,segoe_icon_size) tip=['OutlineThreeQuarterStarRight',tip.info] cmd=command.copy('image.mdl(\uF0EA,@segoe_icon_size)'))
		item(image=image.mdl(\uF0EB,segoe_icon_size) tip=['SpatialVolume0',tip.info] cmd=command.copy('image.mdl(\uF0EB,@segoe_icon_size)'))
		item(image=image.mdl(\uF0EC,segoe_icon_size) tip=['SpatialVolume1',tip.info] cmd=command.copy('image.mdl(\uF0EC,@segoe_icon_size)'))
		item(image=image.mdl(\uF0ED,segoe_icon_size) tip=['SpatialVolume2',tip.info] cmd=command.copy('image.mdl(\uF0ED,@segoe_icon_size)'))
		item(image=image.mdl(\uF0EE,segoe_icon_size) tip=['SpatialVolume3',tip.info] cmd=command.copy('image.mdl(\uF0EE,@segoe_icon_size)'))
		item(image=image.mdl(\uF0EF,segoe_icon_size) tip=['ApplicationGuard',tip.info] cmd=command.copy('image.mdl(\uF0EF,@segoe_icon_size)'))
		item(image=image.mdl(\uF0F7,segoe_icon_size) tip=['OutlineStarLeftHalf',tip.info] cmd=command.copy('image.mdl(\uF0F7,@segoe_icon_size)'))

		item(image=image.mdl(\uF0F8,segoe_icon_size) tip=['OutlineStarRightHalf',tip.info] cmd=command.copy('image.mdl(\uF0F8,@segoe_icon_size)') col)
		item(image=image.mdl(\uF0F9,segoe_icon_size) tip=['ChromeAnnotateContrast',tip.info] cmd=command.copy('image.mdl(\uF0F9,@segoe_icon_size)'))
		item(image=image.mdl(\uF0FB,segoe_icon_size) tip=['DefenderBadge12',tip.info] cmd=command.copy('image.mdl(\uF0FB,@segoe_icon_size)'))
		item(image=image.mdl(\uF103,segoe_icon_size) tip=['DetachablePC',tip.info] cmd=command.copy('image.mdl(\uF103,@segoe_icon_size)'))
		item(image=image.mdl(\uF108,segoe_icon_size) tip=['LeftStick',tip.info] cmd=command.copy('image.mdl(\uF108,@segoe_icon_size)'))
		item(image=image.mdl(\uF109,segoe_icon_size) tip=['RightStick',tip.info] cmd=command.copy('image.mdl(\uF109,@segoe_icon_size)'))
		item(image=image.mdl(\uF10A,segoe_icon_size) tip=['TriggerLeft',tip.info] cmd=command.copy('image.mdl(\uF10A,@segoe_icon_size)'))
		item(image=image.mdl(\uF10B,segoe_icon_size) tip=['TriggerRight',tip.info] cmd=command.copy('image.mdl(\uF10B,@segoe_icon_size)'))
		item(image=image.mdl(\uF10C,segoe_icon_size) tip=['BumperLeft',tip.info] cmd=command.copy('image.mdl(\uF10C,@segoe_icon_size)'))
		item(image=image.mdl(\uF10D,segoe_icon_size) tip=['BumperRight',tip.info] cmd=command.copy('image.mdl(\uF10D,@segoe_icon_size)'))
		item(image=image.mdl(\uF10E,segoe_icon_size) tip=['Dpad',tip.info] cmd=command.copy('image.mdl(\uF10E,@segoe_icon_size)'))
		item(image=image.mdl(\uF110,segoe_icon_size) tip=['EnglishPunctuation',tip.info] cmd=command.copy('image.mdl(\uF110,@segoe_icon_size)'))
		item(image=image.mdl(\uF111,segoe_icon_size) tip=['ChinesePunctuation',tip.info] cmd=command.copy('image.mdl(\uF111,@segoe_icon_size)'))
		item(image=image.mdl(\uF119,segoe_icon_size) tip=['HMD',tip.info] cmd=command.copy('image.mdl(\uF119,@segoe_icon_size)'))
		item(image=image.mdl(\uF11B,segoe_icon_size) tip=['CtrlSpatialRight',tip.info] cmd=command.copy('image.mdl(\uF11B,@segoe_icon_size)'))
		item(image=image.mdl(\uF126,segoe_icon_size) tip=['PaginationDotOutline10',tip.info] cmd=command.copy('image.mdl(\uF126,@segoe_icon_size)'))

		item(image=image.mdl(\uF127,segoe_icon_size) tip=['PaginationDotSolid10',tip.info] cmd=command.copy('image.mdl(\uF127,@segoe_icon_size)') col)
		item(image=image.mdl(\uF128,segoe_icon_size) tip=['StrokeErase2',tip.info] cmd=command.copy('image.mdl(\uF128,@segoe_icon_size)'))
		item(image=image.mdl(\uF129,segoe_icon_size) tip=['SmallErase',tip.info] cmd=command.copy('image.mdl(\uF129,@segoe_icon_size)'))
		item(image=image.mdl(\uF12A,segoe_icon_size) tip=['LargeErase',tip.info] cmd=command.copy('image.mdl(\uF12A,@segoe_icon_size)'))
		item(image=image.mdl(\uF12B,segoe_icon_size) tip=['FolderHorizontal',tip.info] cmd=command.copy('image.mdl(\uF12B,@segoe_icon_size)'))
		item(image=image.mdl(\uF12E,segoe_icon_size) tip=['MicrophoneListening',tip.info] cmd=command.copy('image.mdl(\uF12E,@segoe_icon_size)'))
		item(image=image.mdl(\uF12F,segoe_icon_size) tip=['StatusExclamationCircle7',tip.info] cmd=command.copy('image.mdl(\uF12F,@segoe_icon_size)'))
		item(image=image.mdl(\uF131,segoe_icon_size) tip=['Video360',tip.info] cmd=command.copy('image.mdl(\uF131,@segoe_icon_size)'))
		item(image=image.mdl(\uF133,segoe_icon_size) tip=['GiftboxOpen',tip.info] cmd=command.copy('image.mdl(\uF133,@segoe_icon_size)'))
		item(image=image.mdl(\uF136,segoe_icon_size) tip=['StatusCircleOuter',tip.info] cmd=command.copy('image.mdl(\uF136,@segoe_icon_size)'))
		item(image=image.mdl(\uF137,segoe_icon_size) tip=['StatusCircleInner',tip.info] cmd=command.copy('image.mdl(\uF137,@segoe_icon_size)'))
		item(image=image.mdl(\uF138,segoe_icon_size) tip=['StatusCircleRing',tip.info] cmd=command.copy('image.mdl(\uF138,@segoe_icon_size)'))
		item(image=image.mdl(\uF139,segoe_icon_size) tip=['StatusTriangleOuter',tip.info] cmd=command.copy('image.mdl(\uF139,@segoe_icon_size)'))
		item(image=image.mdl(\uF13A,segoe_icon_size) tip=['StatusTriangleInner',tip.info] cmd=command.copy('image.mdl(\uF13A,@segoe_icon_size)'))
		item(image=image.mdl(\uF13B,segoe_icon_size) tip=['StatusTriangleExclamation',tip.info] cmd=command.copy('image.mdl(\uF13B,@segoe_icon_size)'))
		item(image=image.mdl(\uF13C,segoe_icon_size) tip=['StatusCircleExclamation',tip.info] cmd=command.copy('image.mdl(\uF13C,@segoe_icon_size)'))

		item(image=image.mdl(\uF13D,segoe_icon_size) tip=['StatusCircleErrorX',tip.info] cmd=command.copy('image.mdl(\uF13D,@segoe_icon_size)') col)
		item(image=image.mdl(\uF13E,segoe_icon_size) tip=['StatusCircleCheckmark',tip.info] cmd=command.copy('image.mdl(\uF13E,@segoe_icon_size)'))
		item(image=image.mdl(\uF13F,segoe_icon_size) tip=['StatusCircleInfo',tip.info] cmd=command.copy('image.mdl(\uF13F,@segoe_icon_size)'))
		item(image=image.mdl(\uF140,segoe_icon_size) tip=['StatusCircleBlock',tip.info] cmd=command.copy('image.mdl(\uF140,@segoe_icon_size)'))
		item(image=image.mdl(\uF141,segoe_icon_size) tip=['StatusCircleBlock2',tip.info] cmd=command.copy('image.mdl(\uF141,@segoe_icon_size)'))
		item(image=image.mdl(\uF142,segoe_icon_size) tip=['StatusCircleQuestionMark',tip.info] cmd=command.copy('image.mdl(\uF142,@segoe_icon_size)'))
		item(image=image.mdl(\uF143,segoe_icon_size) tip=['StatusCircleSync',tip.info] cmd=command.copy('image.mdl(\uF143,@segoe_icon_size)'))
		item(image=image.mdl(\uF146,segoe_icon_size) tip=['Dial1',tip.info] cmd=command.copy('image.mdl(\uF146,@segoe_icon_size)'))
		item(image=image.mdl(\uF147,segoe_icon_size) tip=['Dial2',tip.info] cmd=command.copy('image.mdl(\uF147,@segoe_icon_size)'))
		item(image=image.mdl(\uF148,segoe_icon_size) tip=['Dial3',tip.info] cmd=command.copy('image.mdl(\uF148,@segoe_icon_size)'))
		item(image=image.mdl(\uF149,segoe_icon_size) tip=['Dial4',tip.info] cmd=command.copy('image.mdl(\uF149,@segoe_icon_size)'))
		item(image=image.mdl(\uF14A,segoe_icon_size) tip=['Dial5',tip.info] cmd=command.copy('image.mdl(\uF14A,@segoe_icon_size)'))
		item(image=image.mdl(\uF14B,segoe_icon_size) tip=['Dial6',tip.info] cmd=command.copy('image.mdl(\uF14B,@segoe_icon_size)'))
		item(image=image.mdl(\uF14C,segoe_icon_size) tip=['Dial7',tip.info] cmd=command.copy('image.mdl(\uF14C,@segoe_icon_size)'))
		item(image=image.mdl(\uF14D,segoe_icon_size) tip=['Dial8',tip.info] cmd=command.copy('image.mdl(\uF14D,@segoe_icon_size)'))
		item(image=image.mdl(\uF14E,segoe_icon_size) tip=['Dial9',tip.info] cmd=command.copy('image.mdl(\uF14E,@segoe_icon_size)'))

		item(image=image.mdl(\uF14F,segoe_icon_size) tip=['Dial10',tip.info] cmd=command.copy('image.mdl(\uF14F,@segoe_icon_size)') col)
		item(image=image.mdl(\uF150,segoe_icon_size) tip=['Dial11',tip.info] cmd=command.copy('image.mdl(\uF150,@segoe_icon_size)'))
		item(image=image.mdl(\uF151,segoe_icon_size) tip=['Dial12',tip.info] cmd=command.copy('image.mdl(\uF151,@segoe_icon_size)'))
		item(image=image.mdl(\uF152,segoe_icon_size) tip=['Dial13',tip.info] cmd=command.copy('image.mdl(\uF152,@segoe_icon_size)'))
		item(image=image.mdl(\uF153,segoe_icon_size) tip=['Dial14',tip.info] cmd=command.copy('image.mdl(\uF153,@segoe_icon_size)'))
		item(image=image.mdl(\uF154,segoe_icon_size) tip=['Dial15',tip.info] cmd=command.copy('image.mdl(\uF154,@segoe_icon_size)'))
		item(image=image.mdl(\uF155,segoe_icon_size) tip=['Dial16',tip.info] cmd=command.copy('image.mdl(\uF155,@segoe_icon_size)'))
		item(image=image.mdl(\uF156,segoe_icon_size) tip=['DialShape1',tip.info] cmd=command.copy('image.mdl(\uF156,@segoe_icon_size)'))
		item(image=image.mdl(\uF157,segoe_icon_size) tip=['DialShape2',tip.info] cmd=command.copy('image.mdl(\uF157,@segoe_icon_size)'))
		item(image=image.mdl(\uF158,segoe_icon_size) tip=['DialShape3',tip.info] cmd=command.copy('image.mdl(\uF158,@segoe_icon_size)'))
		item(image=image.mdl(\uF159,segoe_icon_size) tip=['DialShape4',tip.info] cmd=command.copy('image.mdl(\uF159,@segoe_icon_size)'))
		item(image=image.mdl(\uF161,segoe_icon_size) tip=['TollSolid',tip.info] cmd=command.copy('image.mdl(\uF161,@segoe_icon_size)'))
		item(image=image.mdl(\uF163,segoe_icon_size) tip=['TrafficCongestionSolid',tip.info] cmd=command.copy('image.mdl(\uF163,@segoe_icon_size)'))
		item(image=image.mdl(\uF164,segoe_icon_size) tip=['ExploreContentSingle',tip.info] cmd=command.copy('image.mdl(\uF164,@segoe_icon_size)'))
		item(image=image.mdl(\uF165,segoe_icon_size) tip=['CollapseContent',tip.info] cmd=command.copy('image.mdl(\uF165,@segoe_icon_size)'))
		item(image=image.mdl(\uF166,segoe_icon_size) tip=['CollapseContentSingle',tip.info] cmd=command.copy('image.mdl(\uF166,@segoe_icon_size)'))

		item(image=image.mdl(\uF167,segoe_icon_size) tip=['InfoSolid',tip.info] cmd=command.copy('image.mdl(\uF167,@segoe_icon_size)') col)
		item(image=image.mdl(\uF168,segoe_icon_size) tip=['GroupList',tip.info] cmd=command.copy('image.mdl(\uF168,@segoe_icon_size)'))
		item(image=image.mdl(\uF169,segoe_icon_size) tip=['CaretBottomRightSolidCenter8',tip.info] cmd=command.copy('image.mdl(\uF169,@segoe_icon_size)'))
		item(image=image.mdl(\uF16A,segoe_icon_size) tip=['ProgressRingDots',tip.info] cmd=command.copy('image.mdl(\uF16A,@segoe_icon_size)'))
		item(image=image.mdl(\uF16B,segoe_icon_size) tip=['Checkbox14',tip.info] cmd=command.copy('image.mdl(\uF16B,@segoe_icon_size)'))
		item(image=image.mdl(\uF16C,segoe_icon_size) tip=['CheckboxComposite14',tip.info] cmd=command.copy('image.mdl(\uF16C,@segoe_icon_size)'))
		item(image=image.mdl(\uF16D,segoe_icon_size) tip=['CheckboxIndeterminateCombo14',tip.info] cmd=command.copy('image.mdl(\uF16D,@segoe_icon_size)'))
		item(image=image.mdl(\uF16E,segoe_icon_size) tip=['CheckboxIndeterminateCombo',tip.info] cmd=command.copy('image.mdl(\uF16E,@segoe_icon_size)'))
		item(image=image.mdl(\uF175,segoe_icon_size) tip=['StatusPause7',tip.info] cmd=command.copy('image.mdl(\uF175,@segoe_icon_size)'))
		item(image=image.mdl(\uF17F,segoe_icon_size) tip=['CharacterAppearance',tip.info] cmd=command.copy('image.mdl(\uF17F,@segoe_icon_size)'))
		item(image=image.mdl(\uF180,segoe_icon_size) tip=['Lexicon',tip.info] cmd=command.copy('image.mdl(\uF180,@segoe_icon_size)'))
		item(image=image.mdl(\uF182,segoe_icon_size) tip=['ScreenTime',tip.info] cmd=command.copy('image.mdl(\uF182,@segoe_icon_size)'))
		item(image=image.mdl(\uF191,segoe_icon_size) tip=['HeadlessDevice',tip.info] cmd=command.copy('image.mdl(\uF191,@segoe_icon_size)'))
		item(image=image.mdl(\uF193,segoe_icon_size) tip=['NetworkSharing',tip.info] cmd=command.copy('image.mdl(\uF193,@segoe_icon_size)'))
		item(image=image.mdl(\uF19D,segoe_icon_size) tip=['EyeGaze',tip.info] cmd=command.copy('image.mdl(\uF19D,@segoe_icon_size)'))
		item(image=image.mdl(\uF19E,segoe_icon_size) tip=['ToggleLeft',tip.info] cmd=command.copy('image.mdl(\uF19E,@segoe_icon_size)'))

		item(image=image.mdl(\uF19F,segoe_icon_size) tip=['ToggleRight',tip.info] cmd=command.copy('image.mdl(\uF19F,@segoe_icon_size)') col)
		item(image=image.mdl(\uF1AD,segoe_icon_size) tip=['WindowsInsider',tip.info] cmd=command.copy('image.mdl(\uF1AD,@segoe_icon_size)'))
		item(image=image.mdl(\uF1CB,segoe_icon_size) tip=['ChromeSwitch',tip.info] cmd=command.copy('image.mdl(\uF1CB,@segoe_icon_size)'))
		item(image=image.mdl(\uF1CC,segoe_icon_size) tip=['ChromeSwitchContast',tip.info] cmd=command.copy('image.mdl(\uF1CC,@segoe_icon_size)'))
		item(image=image.mdl(\uF1D8,segoe_icon_size) tip=['StatusCheckmark',tip.info] cmd=command.copy('image.mdl(\uF1D8,@segoe_icon_size)'))
		item(image=image.mdl(\uF1D9,segoe_icon_size) tip=['StatusCheckmarkLeft',tip.info] cmd=command.copy('image.mdl(\uF1D9,@segoe_icon_size)'))
		item(image=image.mdl(\uF20C,segoe_icon_size) tip=['KeyboardLeftAligned',tip.info] cmd=command.copy('image.mdl(\uF20C,@segoe_icon_size)'))
		item(image=image.mdl(\uF20D,segoe_icon_size) tip=['KeyboardRightAligned',tip.info] cmd=command.copy('image.mdl(\uF20D,@segoe_icon_size)'))
		item(image=image.mdl(\uF210,segoe_icon_size) tip=['KeyboardSettings',tip.info] cmd=command.copy('image.mdl(\uF210,@segoe_icon_size)'))
		item(image=image.mdl(\uF211,segoe_icon_size) tip=['NetworkPhysical',tip.info] cmd=command.copy('image.mdl(\uF211,@segoe_icon_size)'))
		item(image=image.mdl(\uF22C,segoe_icon_size) tip=['IOT',tip.info] cmd=command.copy('image.mdl(\uF22C,@segoe_icon_size)'))
		item(image=image.mdl(\uF22E,segoe_icon_size) tip=['UnknownMirrored',tip.info] cmd=command.copy('image.mdl(\uF22E,@segoe_icon_size)'))
		item(image=image.mdl(\uF246,segoe_icon_size) tip=['ViewDashboard',tip.info] cmd=command.copy('image.mdl(\uF246,@segoe_icon_size)'))
		item(image=image.mdl(\uF259,segoe_icon_size) tip=['ExploitProtectionSettings',tip.info] cmd=command.copy('image.mdl(\uF259,@segoe_icon_size)'))
		item(image=image.mdl(\uF260,segoe_icon_size) tip=['KeyboardNarrow',tip.info] cmd=command.copy('image.mdl(\uF260,@segoe_icon_size)'))
		item(image=image.mdl(\uF261,segoe_icon_size) tip=['Keyboard12Key',tip.info] cmd=command.copy('image.mdl(\uF261,@segoe_icon_size)'))

		item(image=image.mdl(\uF26B,segoe_icon_size) tip=['KeyboardDock',tip.info] cmd=command.copy('image.mdl(\uF26B,@segoe_icon_size)') col)
		item(image=image.mdl(\uF26C,segoe_icon_size) tip=['KeyboardUndock',tip.info] cmd=command.copy('image.mdl(\uF26C,@segoe_icon_size)'))
		item(image=image.mdl(\uF26D,segoe_icon_size) tip=['KeyboardLeftDock',tip.info] cmd=command.copy('image.mdl(\uF26D,@segoe_icon_size)'))
		item(image=image.mdl(\uF26E,segoe_icon_size) tip=['KeyboardRightDock',tip.info] cmd=command.copy('image.mdl(\uF26E,@segoe_icon_size)'))
		item(image=image.mdl(\uF270,segoe_icon_size) tip=['Ear',tip.info] cmd=command.copy('image.mdl(\uF270,@segoe_icon_size)'))
		item(image=image.mdl(\uF271,segoe_icon_size) tip=['PointerHand',tip.info] cmd=command.copy('image.mdl(\uF271,@segoe_icon_size)'))
		item(image=image.mdl(\uF272,segoe_icon_size) tip=['Bullseye',tip.info] cmd=command.copy('image.mdl(\uF272,@segoe_icon_size)'))
		item(image=image.mdl(\uF2B7,segoe_icon_size) tip=['LocaleLanguage',tip.info] cmd=command.copy('image.mdl(\uF2B7,@segoe_icon_size)'))
		item(image=image.mdl(\uF32A,segoe_icon_size) tip=['PassiveAuthentication',tip.info] cmd=command.copy('image.mdl(\uF32A,@segoe_icon_size)'))
		item(image=image.mdl(\uF354,segoe_icon_size) tip=['ColorSolid',tip.info] cmd=command.copy('image.mdl(\uF354,@segoe_icon_size)'))
		item(image=image.mdl(\uF384,segoe_icon_size) tip=['NetworkOffline',tip.info] cmd=command.copy('image.mdl(\uF384,@segoe_icon_size)'))
		item(image=image.mdl(\uF385,segoe_icon_size) tip=['NetworkConnected',tip.info] cmd=command.copy('image.mdl(\uF385,@segoe_icon_size)'))
		item(image=image.mdl(\uF386,segoe_icon_size) tip=['NetworkConnectedCheckmark',tip.info] cmd=command.copy('image.mdl(\uF386,@segoe_icon_size)'))
		item(image=image.mdl(\uF3B1,segoe_icon_size) tip=['SignOut',tip.info] cmd=command.copy('image.mdl(\uF3B1,@segoe_icon_size)'))
		item(image=image.mdl(\uF3CC,segoe_icon_size) tip=['StatusInfo',tip.info] cmd=command.copy('image.mdl(\uF3CC,@segoe_icon_size)'))
		item(image=image.mdl(\uF3CD,segoe_icon_size) tip=['StatusInfoLeft',tip.info] cmd=command.copy('image.mdl(\uF3CD,@segoe_icon_size)'))

		item(image=image.mdl(\uF3E2,segoe_icon_size) tip=['NearbySharing',tip.info] cmd=command.copy('image.mdl(\uF3E2,@segoe_icon_size)') col)
		item(image=image.mdl(\uF3E7,segoe_icon_size) tip=['CtrlSpatialLeft',tip.info] cmd=command.copy('image.mdl(\uF3E7,@segoe_icon_size)'))
		item(image=image.mdl(\uF404,segoe_icon_size) tip=['InteractiveDashboard',tip.info] cmd=command.copy('image.mdl(\uF404,@segoe_icon_size)'))
		item(image=image.mdl(\uF406,segoe_icon_size) tip=['ClippingTool',tip.info] cmd=command.copy('image.mdl(\uF406,@segoe_icon_size)'))
		item(image=image.mdl(\uF407,segoe_icon_size) tip=['RectangularClipping',tip.info] cmd=command.copy('image.mdl(\uF407,@segoe_icon_size)'))
		item(image=image.mdl(\uF408,segoe_icon_size) tip=['FreeFormClipping',tip.info] cmd=command.copy('image.mdl(\uF408,@segoe_icon_size)'))
		item(image=image.mdl(\uF413,segoe_icon_size) tip=['CopyTo',tip.info] cmd=command.copy('image.mdl(\uF413,@segoe_icon_size)'))
		item(image=image.mdl(\uF439,segoe_icon_size) tip=['DynamicLock',tip.info] cmd=command.copy('image.mdl(\uF439,@segoe_icon_size)'))
		item(image=image.mdl(\uF45E,segoe_icon_size) tip=['PenTips',tip.info] cmd=command.copy('image.mdl(\uF45E,@segoe_icon_size)'))
		item(image=image.mdl(\uF45F,segoe_icon_size) tip=['PenTipsMirrored',tip.info] cmd=command.copy('image.mdl(\uF45F,@segoe_icon_size)'))
		item(image=image.mdl(\uF460,segoe_icon_size) tip=['HWPJoin',tip.info] cmd=command.copy('image.mdl(\uF460,@segoe_icon_size)'))
		item(image=image.mdl(\uF461,segoe_icon_size) tip=['HWPInsert',tip.info] cmd=command.copy('image.mdl(\uF461,@segoe_icon_size)'))
		item(image=image.mdl(\uF462,segoe_icon_size) tip=['HWPStrikeThrough',tip.info] cmd=command.copy('image.mdl(\uF462,@segoe_icon_size)'))
		item(image=image.mdl(\uF463,segoe_icon_size) tip=['HWPScratchOut',tip.info] cmd=command.copy('image.mdl(\uF463,@segoe_icon_size)'))
		item(image=image.mdl(\uF464,segoe_icon_size) tip=['HWPSplit',tip.info] cmd=command.copy('image.mdl(\uF464,@segoe_icon_size)'))
		item(image=image.mdl(\uF465,segoe_icon_size) tip=['HWPNewLine',tip.info] cmd=command.copy('image.mdl(\uF465,@segoe_icon_size)'))

		item(image=image.mdl(\uF466,segoe_icon_size) tip=['HWPOverwrite',tip.info] cmd=command.copy('image.mdl(\uF466,@segoe_icon_size)') col)
		item(image=image.mdl(\uF473,segoe_icon_size) tip=['MobWifiWarning1',tip.info] cmd=command.copy('image.mdl(\uF473,@segoe_icon_size)'))
		item(image=image.mdl(\uF474,segoe_icon_size) tip=['MobWifiWarning2',tip.info] cmd=command.copy('image.mdl(\uF474,@segoe_icon_size)'))
		item(image=image.mdl(\uF475,segoe_icon_size) tip=['MobWifiWarning3',tip.info] cmd=command.copy('image.mdl(\uF475,@segoe_icon_size)'))
		item(image=image.mdl(\uF476,segoe_icon_size) tip=['MobWifiWarning4',tip.info] cmd=command.copy('image.mdl(\uF476,@segoe_icon_size)'))
		item(image=image.mdl(\uF49A,segoe_icon_size) tip=['Globe2',tip.info] cmd=command.copy('image.mdl(\uF49A,@segoe_icon_size)'))
		item(image=image.mdl(\uF4A5,segoe_icon_size) tip=['SpecialEffectSize',tip.info] cmd=command.copy('image.mdl(\uF4A5,@segoe_icon_size)'))
		item(image=image.mdl(\uF4A9,segoe_icon_size) tip=['GIF',tip.info] cmd=command.copy('image.mdl(\uF4A9,@segoe_icon_size)'))
		item(image=image.mdl(\uF4AA,segoe_icon_size) tip=['Sticker2',tip.info] cmd=command.copy('image.mdl(\uF4AA,@segoe_icon_size)'))
		item(image=image.mdl(\uF4BE,segoe_icon_size) tip=['SurfaceHubSelected',tip.info] cmd=command.copy('image.mdl(\uF4BE,@segoe_icon_size)'))
		item(image=image.mdl(\uF4BF,segoe_icon_size) tip=['HoloLensSelected',tip.info] cmd=command.copy('image.mdl(\uF4BF,@segoe_icon_size)'))
		item(image=image.mdl(\uF4C0,segoe_icon_size) tip=['Earbud',tip.info] cmd=command.copy('image.mdl(\uF4C0,@segoe_icon_size)'))
		item(image=image.mdl(\uF4C3,segoe_icon_size) tip=['MixVolumes',tip.info] cmd=command.copy('image.mdl(\uF4C3,@segoe_icon_size)'))
		item(image=image.mdl(\uF540,segoe_icon_size) tip=['Safe',tip.info] cmd=command.copy('image.mdl(\uF540,@segoe_icon_size)'))
		item(image=image.mdl(\uF552,segoe_icon_size) tip=['LaptopSecure',tip.info] cmd=command.copy('image.mdl(\uF552,@segoe_icon_size)'))
		item(image=image.mdl(\uF56D,segoe_icon_size) tip=['PrintDefault',tip.info] cmd=command.copy('image.mdl(\uF56D,@segoe_icon_size)'))

		item(image=image.mdl(\uF56E,segoe_icon_size) tip=['PageMirrored',tip.info] cmd=command.copy('image.mdl(\uF56E,@segoe_icon_size)') col)
		item(image=image.mdl(\uF56F,segoe_icon_size) tip=['LandscapeOrientationMirrored',tip.info] cmd=command.copy('image.mdl(\uF56F,@segoe_icon_size)'))
		item(image=image.mdl(\uF570,segoe_icon_size) tip=['ColorOff',tip.info] cmd=command.copy('image.mdl(\uF570,@segoe_icon_size)'))
		item(image=image.mdl(\uF571,segoe_icon_size) tip=['PrintAllPages',tip.info] cmd=command.copy('image.mdl(\uF571,@segoe_icon_size)'))
		item(image=image.mdl(\uF572,segoe_icon_size) tip=['PrintCustomRange',tip.info] cmd=command.copy('image.mdl(\uF572,@segoe_icon_size)'))
		item(image=image.mdl(\uF573,segoe_icon_size) tip=['PageMarginPortraitNarrow',tip.info] cmd=command.copy('image.mdl(\uF573,@segoe_icon_size)'))
		item(image=image.mdl(\uF574,segoe_icon_size) tip=['PageMarginPortraitNormal',tip.info] cmd=command.copy('image.mdl(\uF574,@segoe_icon_size)'))
		item(image=image.mdl(\uF575,segoe_icon_size) tip=['PageMarginPortraitModerate',tip.info] cmd=command.copy('image.mdl(\uF575,@segoe_icon_size)'))
		item(image=image.mdl(\uF576,segoe_icon_size) tip=['PageMarginPortraitWide',tip.info] cmd=command.copy('image.mdl(\uF576,@segoe_icon_size)'))
		item(image=image.mdl(\uF577,segoe_icon_size) tip=['PageMarginLandscapeNarrow',tip.info] cmd=command.copy('image.mdl(\uF577,@segoe_icon_size)'))
		item(image=image.mdl(\uF578,segoe_icon_size) tip=['PageMarginLandscapeNormal',tip.info] cmd=command.copy('image.mdl(\uF578,@segoe_icon_size)'))
		item(image=image.mdl(\uF579,segoe_icon_size) tip=['PageMarginLandscapeModerate',tip.info] cmd=command.copy('image.mdl(\uF579,@segoe_icon_size)'))
		item(image=image.mdl(\uF57A,segoe_icon_size) tip=['PageMarginLandscapeWide',tip.info] cmd=command.copy('image.mdl(\uF57A,@segoe_icon_size)'))
		item(image=image.mdl(\uF57B,segoe_icon_size) tip=['CollateLandscape',tip.info] cmd=command.copy('image.mdl(\uF57B,@segoe_icon_size)'))
		item(image=image.mdl(\uF57C,segoe_icon_size) tip=['CollatePortrait',tip.info] cmd=command.copy('image.mdl(\uF57C,@segoe_icon_size)'))
		item(image=image.mdl(\uF57D,segoe_icon_size) tip=['CollatePortraitSeparated',tip.info] cmd=command.copy('image.mdl(\uF57D,@segoe_icon_size)'))

		item(image=image.mdl(\uF57E,segoe_icon_size) tip=['DuplexLandscapeOneSided',tip.info] cmd=command.copy('image.mdl(\uF57E,@segoe_icon_size)') col)
		item(image=image.mdl(\uF57F,segoe_icon_size) tip=['DuplexLandscapeOneSidedMirrored',tip.info] cmd=command.copy('image.mdl(\uF57F,@segoe_icon_size)'))
		item(image=image.mdl(\uF580,segoe_icon_size) tip=['DuplexLandscapeTwoSidedLongEdge',tip.info] cmd=command.copy('image.mdl(\uF580,@segoe_icon_size)'))
		item(image=image.mdl(\uF581,segoe_icon_size) tip=['DuplexLandscapeTwoSidedLongEdgeMirrored',tip.info] cmd=command.copy('image.mdl(\uF581,@segoe_icon_size)'))
		item(image=image.mdl(\uF582,segoe_icon_size) tip=['DuplexLandscapeTwoSidedShortEdge',tip.info] cmd=command.copy('image.mdl(\uF582,@segoe_icon_size)'))
		item(image=image.mdl(\uF583,segoe_icon_size) tip=['DuplexLandscapeTwoSidedShortEdgeMirrored',tip.info] cmd=command.copy('image.mdl(\uF583,@segoe_icon_size)'))
		item(image=image.mdl(\uF584,segoe_icon_size) tip=['DuplexPortraitOneSided',tip.info] cmd=command.copy('image.mdl(\uF584,@segoe_icon_size)'))
		item(image=image.mdl(\uF585,segoe_icon_size) tip=['DuplexPortraitOneSidedMirrored',tip.info] cmd=command.copy('image.mdl(\uF585,@segoe_icon_size)'))
		item(image=image.mdl(\uF586,segoe_icon_size) tip=['DuplexPortraitTwoSidedLongEdge',tip.info] cmd=command.copy('image.mdl(\uF586,@segoe_icon_size)'))
		item(image=image.mdl(\uF587,segoe_icon_size) tip=['DuplexPortraitTwoSidedLongEdgeMirrored',tip.info] cmd=command.copy('image.mdl(\uF587,@segoe_icon_size)'))
		item(image=image.mdl(\uF588,segoe_icon_size) tip=['DuplexPortraitTwoSidedShortEdge',tip.info] cmd=command.copy('image.mdl(\uF588,@segoe_icon_size)'))
		item(image=image.mdl(\uF589,segoe_icon_size) tip=['DuplexPortraitTwoSidedShortEdgeMirrored',tip.info] cmd=command.copy('image.mdl(\uF589,@segoe_icon_size)'))
		item(image=image.mdl(\uF58A,segoe_icon_size) tip=['PPSOneLandscape',tip.info] cmd=command.copy('image.mdl(\uF58A,@segoe_icon_size)'))
		item(image=image.mdl(\uF58B,segoe_icon_size) tip=['PPSTwoLandscape',tip.info] cmd=command.copy('image.mdl(\uF58B,@segoe_icon_size)'))
		item(image=image.mdl(\uF58C,segoe_icon_size) tip=['PPSTwoPortrait',tip.info] cmd=command.copy('image.mdl(\uF58C,@segoe_icon_size)'))
		item(image=image.mdl(\uF58D,segoe_icon_size) tip=['PPSFourLandscape',tip.info] cmd=command.copy('image.mdl(\uF58D,@segoe_icon_size)'))

		item(image=image.mdl(\uF58E,segoe_icon_size) tip=['PPSFourPortrait',tip.info] cmd=command.copy('image.mdl(\uF58E,@segoe_icon_size)') col)
		item(image=image.mdl(\uF58F,segoe_icon_size) tip=['HolePunchOff',tip.info] cmd=command.copy('image.mdl(\uF58F,@segoe_icon_size)'))
		item(image=image.mdl(\uF590,segoe_icon_size) tip=['HolePunchPortraitLeft',tip.info] cmd=command.copy('image.mdl(\uF590,@segoe_icon_size)'))
		item(image=image.mdl(\uF591,segoe_icon_size) tip=['HolePunchPortraitRight',tip.info] cmd=command.copy('image.mdl(\uF591,@segoe_icon_size)'))
		item(image=image.mdl(\uF592,segoe_icon_size) tip=['HolePunchPortraitTop',tip.info] cmd=command.copy('image.mdl(\uF592,@segoe_icon_size)'))
		item(image=image.mdl(\uF593,segoe_icon_size) tip=['HolePunchPortraitBottom',tip.info] cmd=command.copy('image.mdl(\uF593,@segoe_icon_size)'))
		item(image=image.mdl(\uF594,segoe_icon_size) tip=['HolePunchLandscapeLeft',tip.info] cmd=command.copy('image.mdl(\uF594,@segoe_icon_size)'))
		item(image=image.mdl(\uF595,segoe_icon_size) tip=['HolePunchLandscapeRight',tip.info] cmd=command.copy('image.mdl(\uF595,@segoe_icon_size)'))
		item(image=image.mdl(\uF596,segoe_icon_size) tip=['HolePunchLandscapeTop',tip.info] cmd=command.copy('image.mdl(\uF596,@segoe_icon_size)'))
		item(image=image.mdl(\uF597,segoe_icon_size) tip=['HolePunchLandscapeBottom',tip.info] cmd=command.copy('image.mdl(\uF597,@segoe_icon_size)'))
		item(image=image.mdl(\uF598,segoe_icon_size) tip=['StaplingOff',tip.info] cmd=command.copy('image.mdl(\uF598,@segoe_icon_size)'))
		item(image=image.mdl(\uF599,segoe_icon_size) tip=['StaplingPortraitTopLeft',tip.info] cmd=command.copy('image.mdl(\uF599,@segoe_icon_size)'))
		item(image=image.mdl(\uF59A,segoe_icon_size) tip=['StaplingPortraitTopRight',tip.info] cmd=command.copy('image.mdl(\uF59A,@segoe_icon_size)'))
		item(image=image.mdl(\uF59B,segoe_icon_size) tip=['StaplingPortraitBottomRight',tip.info] cmd=command.copy('image.mdl(\uF59B,@segoe_icon_size)'))
		item(image=image.mdl(\uF59C,segoe_icon_size) tip=['StaplingPortraitTwoLeft',tip.info] cmd=command.copy('image.mdl(\uF59C,@segoe_icon_size)'))
		item(image=image.mdl(\uF59D,segoe_icon_size) tip=['StaplingPortraitTwoRight',tip.info] cmd=command.copy('image.mdl(\uF59D,@segoe_icon_size)'))

		item(image=image.mdl(\uF59E,segoe_icon_size) tip=['StaplingPortraitTwoTop',tip.info] cmd=command.copy('image.mdl(\uF59E,@segoe_icon_size)') col)
		item(image=image.mdl(\uF59F,segoe_icon_size) tip=['StaplingPortraitTwoBottom',tip.info] cmd=command.copy('image.mdl(\uF59F,@segoe_icon_size)'))
		item(image=image.mdl(\uF5A0,segoe_icon_size) tip=['StaplingPortraitBookBinding',tip.info] cmd=command.copy('image.mdl(\uF5A0,@segoe_icon_size)'))
		item(image=image.mdl(\uF5A1,segoe_icon_size) tip=['StaplingLandscapeTopLeft',tip.info] cmd=command.copy('image.mdl(\uF5A1,@segoe_icon_size)'))
		item(image=image.mdl(\uF5A2,segoe_icon_size) tip=['StaplingLandscapeTopRight',tip.info] cmd=command.copy('image.mdl(\uF5A2,@segoe_icon_size)'))
		item(image=image.mdl(\uF5A3,segoe_icon_size) tip=['StaplingLandscapeBottomLeft',tip.info] cmd=command.copy('image.mdl(\uF5A3,@segoe_icon_size)'))
		item(image=image.mdl(\uF5A4,segoe_icon_size) tip=['StaplingLandscapeBottomRight',tip.info] cmd=command.copy('image.mdl(\uF5A4,@segoe_icon_size)'))
		item(image=image.mdl(\uF5A5,segoe_icon_size) tip=['StaplingLandscapeTwoLeft',tip.info] cmd=command.copy('image.mdl(\uF5A5,@segoe_icon_size)'))
		item(image=image.mdl(\uF5A6,segoe_icon_size) tip=['StaplingLandscapeTwoRight',tip.info] cmd=command.copy('image.mdl(\uF5A6,@segoe_icon_size)'))
		item(image=image.mdl(\uF5A7,segoe_icon_size) tip=['StaplingLandscapeTwoTop',tip.info] cmd=command.copy('image.mdl(\uF5A7,@segoe_icon_size)'))
		item(image=image.mdl(\uF5A8,segoe_icon_size) tip=['StaplingLandscapeTwoBottom',tip.info] cmd=command.copy('image.mdl(\uF5A8,@segoe_icon_size)'))
		item(image=image.mdl(\uF5A9,segoe_icon_size) tip=['StaplingLandscapeBookBinding',tip.info] cmd=command.copy('image.mdl(\uF5A9,@segoe_icon_size)'))
		item(image=image.mdl(\uF5AA,segoe_icon_size) tip=['StatusDataTransferRoaming',tip.info] cmd=command.copy('image.mdl(\uF5AA,@segoe_icon_size)'))
		item(image=image.mdl(\uF5AB,segoe_icon_size) tip=['MobSIMError',tip.info] cmd=command.copy('image.mdl(\uF5AB,@segoe_icon_size)'))
		item(image=image.mdl(\uF5AC,segoe_icon_size) tip=['CollateLandscapeSeparated',tip.info] cmd=command.copy('image.mdl(\uF5AC,@segoe_icon_size)'))
		item(image=image.mdl(\uF5AD,segoe_icon_size) tip=['PPSOnePortrait',tip.info] cmd=command.copy('image.mdl(\uF5AD,@segoe_icon_size)'))

		item(image=image.mdl(\uF5AE,segoe_icon_size) tip=['StaplingPortraitBottomLeft',tip.info] cmd=command.copy('image.mdl(\uF5AE,@segoe_icon_size)') col)
		item(image=image.mdl(\uF5B0,segoe_icon_size) tip=['PlaySolid',tip.info] cmd=command.copy('image.mdl(\uF5B0,@segoe_icon_size)'))
		item(image=image.mdl(\uF5E7,segoe_icon_size) tip=['RepeatOff',tip.info] cmd=command.copy('image.mdl(\uF5E7,@segoe_icon_size)'))
		item(image=image.mdl(\uF5ED,segoe_icon_size) tip=['Set',tip.info] cmd=command.copy('image.mdl(\uF5ED,@segoe_icon_size)'))
		item(image=image.mdl(\uF5EE,segoe_icon_size) tip=['SetSolid',tip.info] cmd=command.copy('image.mdl(\uF5EE,@segoe_icon_size)'))
		item(image=image.mdl(\uF5EF,segoe_icon_size) tip=['FuzzyReading',tip.info] cmd=command.copy('image.mdl(\uF5EF,@segoe_icon_size)'))
		item(image=image.mdl(\uF5F2,segoe_icon_size) tip=['VerticalBattery0',tip.info] cmd=command.copy('image.mdl(\uF5F2,@segoe_icon_size)'))
		item(image=image.mdl(\uF5F3,segoe_icon_size) tip=['VerticalBattery1',tip.info] cmd=command.copy('image.mdl(\uF5F3,@segoe_icon_size)'))
		item(image=image.mdl(\uF5F4,segoe_icon_size) tip=['VerticalBattery2',tip.info] cmd=command.copy('image.mdl(\uF5F4,@segoe_icon_size)'))
		item(image=image.mdl(\uF5F5,segoe_icon_size) tip=['VerticalBattery3',tip.info] cmd=command.copy('image.mdl(\uF5F5,@segoe_icon_size)'))
		item(image=image.mdl(\uF5F6,segoe_icon_size) tip=['VerticalBattery4',tip.info] cmd=command.copy('image.mdl(\uF5F6,@segoe_icon_size)'))
		item(image=image.mdl(\uF5F7,segoe_icon_size) tip=['VerticalBattery5',tip.info] cmd=command.copy('image.mdl(\uF5F7,@segoe_icon_size)'))
		item(image=image.mdl(\uF5F8,segoe_icon_size) tip=['VerticalBattery6',tip.info] cmd=command.copy('image.mdl(\uF5F8,@segoe_icon_size)'))
		item(image=image.mdl(\uF5F9,segoe_icon_size) tip=['VerticalBattery7',tip.info] cmd=command.copy('image.mdl(\uF5F9,@segoe_icon_size)'))
		item(image=image.mdl(\uF5FA,segoe_icon_size) tip=['VerticalBattery8',tip.info] cmd=command.copy('image.mdl(\uF5FA,@segoe_icon_size)'))
		item(image=image.mdl(\uF5FB,segoe_icon_size) tip=['VerticalBattery9',tip.info] cmd=command.copy('image.mdl(\uF5FB,@segoe_icon_size)'))
	}

	separator

	menu(title='Tooltips')
	{
		item(image=image.fluent(\uE897,segoe_icon_size) title='Default' tip=['Default',tip.default,0.5] cmd=command.copy("tip=['Default',tip.default,1.0]"))
		item(image=image.fluent(\uE90A,segoe_icon_size) title='Info' tip=['Info',tip.info,0.5] cmd=command.copy("tip=['Info',tip.info,1.0]"))
		item(image=image.fluent(\uEA8F,segoe_icon_size) title='Primary' tip=['Primary',tip.primary,0.5] cmd=command.copy("tip=['Primary',tip.primary,1.0]"))
		item(image=image.fluent(\uE930,segoe_icon_size) title='Success' tip=['Success',tip.success,0.5] cmd=command.copy("tip=['Success',tip.success,1.0]"))
		item(image=image.fluent(\uE7BA,segoe_icon_size) title='Warning' tip=['Warning',tip.warning,0.5] cmd=command.copy("tip=['Warning',tip.warning,1.0]"))
		item(image=image.fluent(\uEA39,segoe_icon_size) title='Danger' tip=['Danger',tip.danger,0.5] cmd=command.copy("tip=['Danger',tip.danger,1.0]"))
	}
}