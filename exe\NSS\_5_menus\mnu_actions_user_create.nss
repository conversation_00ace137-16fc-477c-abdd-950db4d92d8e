
//
menu(title="&Create" type='Desktop|Back.Dir|Back.Drive|File' image=[E1D2,BLUE] image-sel=[E1D2,HOVER] sep='None') {

    //
    $STR_TIME = sys.datetime("H.M")
    // $STR_DATE = sys.datetime("d.m.y")
    $STR_DATE = sys.datetime("y.m.d")
    // $STR_DATETIME = sys.datetime("d.m.y - H.M - ")
    $STR_DATETIME = sys.datetime("y.m.d-kl.H.M--")
    //
    $STR_SEPARATOR = "============================================================================="
    $STR_LINEBREAK = "\n"

    //
    $STR_CONTENT_BAT = "@ECHO OFF\nSETLOCAL ENABLEDELAYEDEXPANSION\nIF EXIST \"%~1\" (CD /D \"%~1\") ELSE (CD /D \"%~dp0\")"
    //
    $STR_CONTENT_PY = '# @STR_SEPARATOR'+STR_LINEBREAK+'# [@STR_TIME] - @STR_DATE'+STR_LINEBREAK

    //
    item(title="&Directory" keys="/"  image=[E0E7,GREY]  image-sel=[E0E7,HOVER] cmd=io.dir.create('@(STR_DATETIME)'))
    separator()

    //
    //
    //
    item(title="&bat"  keys="*.*" image=[E0AC,WHITE] image-sel=[E0AC,HOVER] cmd=io.file.create('@(STR_DATETIME).bat','@STR_CONTENT_BAT',1))
    item(title="&py"   keys="*.*" image=[E230,WHITE] image-sel=[E230,HOVER] cmd=io.file.create('@(STR_DATETIME).py','@STR_CONTENT_PY',1))
    item(title="&todo" keys="*.*" image=[E230,WHITE] image-sel=[E230,HOVER] cmd=io.file.create('@(STR_DATETIME)__todo.py','@STR_DATETIME',1))
    item(title="&txt"  keys="*.*" image=[E17A,WHITE] image-sel=[E17A,HOVER] cmd=io.file.create('@(STR_DATETIME).txt','@STR_DATETIME',1))
    separator()

    // item(title="Sync" image=[E09D,BLUE] vis='Static' sep='both' )
    import '@app.dir/NSS/_3_items/itm_py_directorycleaner.nss'
    import '@app.dir/NSS/_3_items/itm_py_dirtreegenerator.nss'
    import '@app.dir/NSS/_3_items/itm_py_markdowngenerator.nss'
    import '@app.dir/NSS/_3_items/itm_py_pinterestdownloader.nss'
    import '@app.dir/NSS/_3_items/itm_py_projectgenerator.nss'
    import '@app.dir/NSS/_3_items/itm_py_renamewitheditor.nss'
    import '@app.dir/NSS/_3_items/itm_py_urlgenerator.nss'
    import '@app.dir/NSS/_3_items/itm_py_windowtiler.nss'
    import '@app.dir/NSS/_3_items/itm_py_youtubedownloader.nss'
    separator()

    //
    $CONTENT_GITIGNORE = "# everything except .gitignore\n# *\n# !.gitignore\n\n# files without extension\n# *\n# !*/\n# !*.*"
    $CONTENT_GITIGNORE = "\n# =======================================================\n# DIRNAMES\n# =======================================================\n# dirs: python\n**/.cache/\n**/.env/\n**/.pytest_cache/\n**/.venv/\n**/__pycache__/\n**/env/\n**/venv/\n# dirs: react\n**/node_modules/\n# dirs: logs and temp\n**/build/\n**/cache/\n**/dist/\n**/logs/\n**/temp/\n**/tmp/\n\n# =======================================================\n# EXTENSIONS\n# =======================================================\n# extensions: media\n*.mp4\n*.mkv\n*.webm\n*.mp3\n*.wav\n# extensions: unsorted\n*.bin\n*.blend1\n*.dll\n*.DS_Store\n*.exe\n*.ini.bak\n*.ldb\n*.log\n*.pak\n*.pickle\n*.png\n*.prv.ppk\n*.prv.pub\n*.pyc\n*.pyo\n*.swp\n*.tmp\n\n# =======================================================\n# -- OVERRIDES --\n# =======================================================\n\n# filenames: unsorted\n**/.what-is-this.md\n**/app.log.yml\n**/quit.blend\n**/Run History-1.5a.csv\n**/Search History-1.5a.csv\n**/Session-1.5a.backup.json\n**/Session-1.5a.json\n\n# dirs\n# **/.backups/\n# **/.specstory/\n# **/__meta__/\n\n# types\n*.sublime-workspace\n*.sublime_session\n\n# paths: files\n**/*sync-conflict*.*\n**/user-data/**/ui_messages.json\n**/.specstory/history/.what-is-this.md"
    //
    item(title="__init__.py"       keys="" image=[E230,WHITE] image-sel=[E230,HOVER] cmd=io.file.create('__init__.py',"",1))
    item(title=".&gitignore"       keys="" image=[E22C,WHITE] image-sel=[E22C,HOVER] cmd=io.file.create('.gitignore','@CONTENT_GITIGNORE',1))
    item(title="&requirements.txt" keys="" image=[E17A,WHITE] image-sel=[E17A,HOVER] cmd=io.file.create('requirements.txt',"",1))
    separator()

    // (
        // # dirs: python
        // **/.env/
        // **/.venv/
        // **/__pycache__/
        // **/env/
        // **/venv/

        // # dirs: logs and temp
        // **/build/
        // **/cache/
        // **/dist/
        // **/logs/
        // **/temp/
        // **/tmp/

        // # files: workflow
        // *.bak
        // *.blend1
        // *.ini.bak
        // *.ldb
        // *.log
        // *.prv.ppk
        // *.prv.pub
        // *.pyc
        // *.pyo
        // *.sublime-workspace
        // *.sublime_session
        // *.swp
        // *.tmp
        // .DS_Store

        // # files
        // **/quit.blend
        // **/Run History-1.5a.csv
        // **/Search History-1.5a.csv
        // **/Session-1.5a.backup.json
        // **/Session-1.5a.json
    // )

}
