
// EXAMPLES: Validation Properties
// ┌─────────────┬────────────────────────────────────────────────────────────────────────────────────┐
// │ Property    │ Description                                                                        │
// ├─────────────┼────────────────────────────────────────────────────────────────────────────────────┤
// │ Mode        │ Determines menuitem display based on selection type.                               │
// │ Type        │ Specifies object types for which the menuitem will appear.                         │
// │ Where       │ Process menuitem if a certain condition is met (evaluates expressions).            │
// └─────────────┴────────────────────────────────────────────────────────────────────────────────────┘
// |> HOW ARE VALIDATION PROPERTIES USED?
  - item(mode="single" ...)           // Single object selection
  - item(type="File|Directory" ...)   // Files or directories
  - item(where="expression" ...)      // Conditional expression met

// EXAMPLES: Filter Properties
// ┌─────────────┬────────────────────────────────────────────────────────────────────────────────────┐
// │ Property    │ Description                                                                        │
// ├─────────────┼────────────────────────────────────────────────────────────────────────────────────┤
// │ Find        │ Applies process instructions based on title pattern matching.                      │
// └─────────────┴────────────────────────────────────────────────────────────────────────────────────┘
// |> HOW ARE FILTER PROPERTIES USED?
  - item(find="'pattern'" ...)        // Title pattern match

// EXAMPLES: Menuitem Properties
// ┌─────────────┬────────────────────────────────────────────────────────────────────────────────────┐
// │ Property    │ Description                                                                        │
// ├─────────────┼────────────────────────────────────────────────────────────────────────────────────┤
// │ Title       │ Sets the caption of the menuitem.                                                  │
// │ Visibility  │ Controls the visibility and state of a menuitem.                                   │
// │ Separator   │ Adds a separator to the menuitem.                                                  │
// │ Position    │ Specifies the position of a menuitem in the menu.                                  │
// │ Image       │ Assigns an icon to the menuitem.                                                   │
// │ Parent      │ Moves menuitem to another menu.                                                    │
// │ Checked     │ Determines the selection state of the menuitem.                                    │
// │ Default     │ Marks a menuitem as the default (displayed in bold).                               │
// │ Expanded    │ Moves all immediate menuitems to the parent menu.                                  │
// │ Column      │ Creates a new column in the menu.                                                  │
// │ Keys        │ Shows keyboard shortcuts for the menuitem.                                         │
// │ Tip         │ Displays a tooltip for the menuitem.                                               │
// └─────────────┴────────────────────────────────────────────────────────────────────────────────────┘
// |> HOW ARE MENUITEM PROPERTIES USED?
  - item(title="My Menu Item" ...)    // Set caption
  - item(visibility="Normal" ...)     // Control visibility
  - item(separator="Before" ...)      // Add separator before item

// EXAMPLES: Command Properties
// ┌─────────────┬────────────────────────────────────────────────────────────────────────────────────┐
// │ Property    │ Description                                                                        │
// ├─────────────┼────────────────────────────────────────────────────────────────────────────────────┤
// │ Command     │ Associates a command with the menuitem.                                            │
// │ Arguments   │ Passes command line parameters to the menuitem's command.                          │
// │ Invoke      │ Sets execution type for the command.                                               │
// │ Window      │ Controls the window behavior of the executed command.                              │
// │ Directory   │ Specifies the working directory for the command.                                   │
// │ Admin       │ Executes the command with administrative permissions.                              │
// │ Verb        │ Specifies the default operation for the selected file.                             │
// │ Wait        │ Waits for the command to complete before proceeding.                               │
// └─────────────┴────────────────────────────────────────────────────────────────────────────────────┘
// |> HOW ARE COMMAND PROPERTIES USED?
  - item(command="cmd.exe" ...)       // Execute a command
  - item(arguments="/c dir" ...)      // Pass parameters
  - item(invoke="single" ...)         // Execution type




// 19.01.2024 - Kl.21:28:

// =============================================================================
// |> menu(mode="single")
// =============================================================================
// ┌──────────────┬─────────────────────────────────────────────────────────────────────────────────┐
// │ Mode         │ Description                                                                     │
// ├──────────────┼─────────────────────────────────────────────────────────────────────────────────┤
// │ none         │ Display menuitem when there is no selection.                                    │
// │ single       │ Display menuitem when there is a single object selected.                        │
// │ multi_unique │ Display menuitem when multiple objects of the same type are selected.           │
// │ multi_single │ Display menuitem when multiple files with a single file extension are selected. │
// │ multiple     │ Display menuitem on any type of selection, unless there is none.                │
// └──────────────┴─────────────────────────────────────────────────────────────────────────────────┘
// |> IN WHAT CONTEXT SHOULD THE MENUITEM APPEAR?
  - item(mode="multiple" ...)        // Any selection
  - item(mode="multi_unique" ...)    // Multiple same-type
  - item(mode="multi_single" ...)    // Same extension
  - item(mode="single" ...)          // Single selection
  - item(mode="none" ...)            // No selection

// =============================================================================
// menu(type="File|Directory")
// =============================================================================
// |> ┌─────────────────┬───────────────────────────────────────────────────────────────────────────────┐
//    │ Type Option     │ Description                                                                   │
//    ├─────────────────┼───────────────────────────────────────────────────────────────────────────────┤
//    │ file            │ Display menuitem when files are selected.                                     │
//    │ directory (dir) │ Display menuitem when directories are selected.                               │
//    │ drive           │ Display menuitem when drives are selected.                                    │
//    │ usb             │ Display menuitem when USB flash-drives are selected.                          │
//    │ dvd             │ Display menuitem when DVD-ROM drives are selected.                            │
//    │ fixed           │ Display menuitem for fixed drives (e.g., hard disk, flash drive).             │
//    │ vhd             │ Display menuitem when Virtual Hard Disks are selected.                        │
//    │ removable       │ Display menuitem for drives with removable media (e.g., floppy, thumb drive). │
//    │ remote          │ Display menuitem for remote (network) drives.                                 │
//    │ back            │ Display menuitem for background selections.                                   │
//    │ desktop         │ Display menuitem when the Desktop is selected.                                │
//    │ namespace       │ Display menuitem for selected Namespaces (virtual objects).                   │
//    │ computer        │ Display menuitem when My Computer is selected.                                │
//    │ recyclebin      │ Display menuitem when the Recycle bin is selected.                            │
//    │ taskbar         │ Display menuitem when the Taskbar is selected.                                │
//    └─────────────────┴───────────────────────────────────────────────────────────────────────────────┘
// |> IN WHAT TYPES OF ENVIRONMENTS SHOULD THE MENUITEM APPEAR?
//    - menu(type='file' ...)             // For files
//    - menu(type='dir' ...)              // For directories
//    - menu(type='~file' ...)            // Exclude files
//    - menu(type='drive|usb' ...)        // For drives and USBs
//    - menu(type='desktop|namespace' ...) // For Desktop or Namespaces
// |> EXAMPLE:
item(type="File" mode="multi_single" title=sel.path cmd=msg('@user.name \n @sel.path'))




// -----------------------------------------------------------------------------
// -> Appears regardless of selection
item(mode="none" title=sel.path cmd=msg('@user.name \n @sel.path'))

// -> If a single file/folder is selected
item(mode="single" title=sel.path cmd=msg('@user.name \n @sel.path'))
// Displayed regardless of context
menu(type='~taskbar' mode="none" title="Test -> Mode: none" image=icon.pin) {
    item(title='Test -> Mode: none' cmd=msg('Hello @user.name'))
}
// Displayed when a single type is selected (e.g. a single file/folder)
menu(type='~taskbar' mode="single" title="Test -> Mode: single" image=icon.pin) {
    item(title='Test -> Mode: single' cmd=msg('Hello @user.name'))
}
// Displayed when multiple similar types is selected (e.g. multiple files)
menu(type='~taskbar' mode="multi_unique" title="Test -> Mode: multi_unique" image=icon.pin) {
    item(title='Test -> Mode: multi_unique' cmd=msg('Hello @user.name'))
}
// Displayed when multiple identical types is selected (e.g. multiple .txt files)
menu(type='~taskbar' mode="multi_single" title="Test -> Mode: multi_single" image=icon.pin) {
    item(title='Test -> Mode: multi_single' cmd=msg('Hello @user.name'))
}
// Displayed if any of the given types is matched.
menu(type='~taskbar' mode="multiple" title="Test -> Mode: multiple" image=icon.pin) {
    item(title='Test -> Mode: multiple' cmd=msg('Hello @user.name'))
}

// =============================================================================
// 18.01.2024 - Kl.17:32:
// Actions based on regex
remove(where=regex.match(this.name, "^(.*send a.*)$"))


// =============================================================================
// 13.10.2023 - Kl.20:20:
// How to move an existing item?
item(find='Upload with ShareX'      menu='ShareX')
item(find='Edit with ShareX'        menu='ShareX')
// 13.10.2023 - Kl.21:10:
// WORKS: Move NordVPN to Submenu
menu(mode="none" title="-> Applications" pos="middle" image=icon.new_folder) {
    menu(separator="both" title="NordVPN" image=icon.copy_path) {
        // ...
    }
}
modify(find="NordVPN" menu="-> Applications/NordVPN")





// =============================================================================
// 13.10.2023 - Kl.20:06:
// - match with '*{input}*' (single input) -> [NOR | ENG]
remove(find='åpne')
remove(find='open')
// - match with '*{input}*' (multi input) -> [NOR | ENG]
remove(where=regex.match(this.name, "^(.*åpne.*|.*open.*)$"))
// - exact phrase match (single input) #1 -> [NOR | ENG]
remove(where=this.name!="åpne")
remove(where=this.name!="open")
// - exact phrase match (multi input) -> [NOR | ENG]
remove(where=this.name("open", "åpne" ))



// -----------------------------------------------------------------------------

item(type='file'
    find='add zip|add 7z'
    where=(sel.file.ext== '.zip' || sel.file.ext== '.z7')
    menu='zip')
// -----------------------------------------------------------------------------
// to move items
item(find='add zip|add 7z|"extract"' menu='more options')

// to remove items
item(find='open as archive|extract here' vis=0)

// -----------------------------------------------------------------------------


remove(where=this.disabled find='edit with notepad++' vis="remove")

// =============================================================================
// 13.10.2023 - Kl.11:57: Untested examples
// this also works:
remove(find='NordVPN|queries|cant|be|defined|like|this|with')


// Regex example:
item(title='test regex.match' cmd=msg(regex.match(sel.path, '(.*)?\.exe$')))
$matches = regex.matches('this subject has a submarine as a subsequence','\b(sub)([^ ]*)')
item(title='test regex.matches' cmd=msg(str.join('found matches: @length(matches)', "", matches,"\n")))
//
menu(title='New Folder' image=icon.new_folder menu=id.new.name pos=0 sep=sep.before)
{
    item(title='New Folder (Current Date)' cmd=io.dir.create(sys.datetime("y.m.d")))
    # item(title='DateTime' cmd=io.dir.create(sys.datetime("ymdHMSs")))
    item(title='Guid' cmd=io.dir.create(str.guid))
}
menu(title='New File' image=icon.new_file menu=id.new.name pos=1 sep=sep.after)
{
    $dt = sys.datetime("ymdHMSs")
    item(title='TXT' cmd=io.file.create('@(dt).txt', 'Hello World!'))
    item(title='JSON' cmd=io.file.create('@(dt).json', '{}'))
    item(title='HTML' cmd=io.file.create('@(dt).html', "<html>\n\t<head>\n\t</head>\n\t<body>Hello World!\n\t</body>\n</html>"))
}


// =============================================================================
// EXAMPLES: WORKS
// =============================================================================
// remove all menuitems which is not named "refresh"
// - single
remove(where=this.name!="open")
// - multi (regex)
remove(where=!regex.match(this.name, "^(refresh|open|edit|new folder)$"))
// - multi (regex - with wildcards)
remove(where=!regex.match(this.name, "^(refresh|open|edit|new folder|properties|.*with.*)$"))


// hide specific
remove(where=regex.match(this.name, "^(open in new tab|open in new window)$"))

// exclude anything that contains the word "with" (case insensitive)
remove(where=regex.match(this.name, ".*with.*"))

// exclude anything that does -not- contain "with"
remove(where=regex.match(this.name, "^((?:(?!with).)*$)"))
// exclude anything that does -not- contain "with" OR "open in"
remove(where=regex.match(this.name, "^((?:(?!with)(?!open in)(?!properties).)*$)"))

// exclude anything that starts with "open in new"
remove(where=regex.match(this.name, "open in new.*"))


add menuitem
menu(type='~taskbar' mode="none" sep="both" title="refresh" image=icon.pin) {
    item(title='refresh' cmd=msg('Hello @user.name'))
}

// REMOVE MULTIPLE BY TITLE
remove(
    where=!this.name(
        "open",
        "properties"
    )
)


// REMOVE EVERYTHING EXCEPT DEFAULT WINDOWS ITEMS:
// // REMOVE DEFAULT WINDOWS ITEMS:
remove(
    where=!this.id(
        id.add_a_network_location, id.adjust_date_time, id.align_icons_to_grid,
        id.arrange_by, id.auto_arrange_icons, id.autoplay, id.cancel,
        id.cascade_windows, id.cast_to_device, id.cleanup, id.collapse,
        id.collapse_all_groups, id.collapse_group, id.command_prompt,
        id.compressed, id.configure, id.content, id.control_panel, id.copy,
        id.copy_as_path, id.copy_here, id.copy_path, id.copy_to,
        id.copy_to_folder, id.cortana, id.create_shortcut,
        id.create_shortcuts_here, id.customize_notification_icons,
        id.customize_this_folder, id.cut, id.delete, id.desktop, id.details,
        id.device_manager, id.disconnect, id.disconnect_network_drive,
        id.display_settings, id.edit, id.eject, id.empty_recycle_bin,
        id.erase_this_disc, id.exit_explorer, id.expand, id.expand_all_groups,
        id.expand_group, id.extra_large_icons, id.extract_all, id.extract_to,
        id.file_explorer, id.folder_options, id.format, id.give_access_to,
        id.group_by, id.include_in_library, id.insert_unicode_control_character,
        id.install, id.large_icons, id.list, id.lock_all_taskbars,
        id.lock_the_taskbar, id.make_available_offline,
        id.make_available_online, id.manage, id.map_as_drive,
        id.map_network_drive, id.medium_icons, id.merge, id.more_options,
        id.mount, id.move_here, id.move_to, id.move_to_folder, id.new,
        id.new_folder, id.new_item, id.news_and_interests,
        id.next_desktop_background, id.open, id.open_as_portable,
        id.open_autoplay, id.open_command_prompt, id.open_command_window_here,
        id.open_file_location, id.open_folder_location, id.open_in_new_process,
        id.open_in_new_tab, id.open_in_new_window, id.open_new_tab,
        id.open_new_window, id.open_powershell_window_here,
        id.open_windows_powershell, id.open_with, id.options, id.paste,
        id.paste_shortcut, id.personalize,
        id.pin_current_folder_to_quick_access, id.pin_to_quick_access,
        id.pin_to_start, id.pin_to_taskbar, id.play, id.power_options,
        id.preview, id.print, id.properties, id.reconversion, id.redo,
        id.refresh, id.remove_from_quick_access, id.remove_properties,
        id.rename, id.restore, id.restore_default_libraries,
        id.restore_previous_versions, id.rotate_left, id.rotate_right, id.run,
        id.run_as_administrator, id.run_as_another_user, id.search,
        id.select_all, id.send_to, id.set_as_desktop_background,
        id.set_as_desktop_wallpaper, id.settings, id.share, id.share_with,
        id.shield, id.show_cortana_button, id.show_desktop_icons,
        id.show_file_extensions, id.show_hidden_files, id.show_libraries,
        id.show_network, id.show_pen_button, id.show_people_on_the_taskbar,
        id.show_task_view_button, id.show_the_desktop, id.show_this_pc,
        id.show_touch_keyboard_button, id.show_touchpad_button,
        id.show_windows_side_by_side, id.show_windows_stacked, id.small_icons,
        id.sort_by, id.store, id.task_manager, id.taskbar_settings, id.tiles,
        id.troubleshoot_compatibility, id.turn_off_bitlocker,
        id.turn_on_bitlocker, id.undo, id.unpin_from_quick_access,
        id.unpin_from_start, id.unpin_from_taskbar, id.view
    )
)


// =============================================================================

// // var1 = "Open"
// // var2 = "He is called 'Johnny'"
// // var3 = 'He is called "Johnny"'
// // regex.match(str, pattern)
// remove(where=!regex.match(this.name, "^(refresh|open|edit|new folder)$"))


// // remove(where=this.name!="Open")
// // remove(where=this.name!="Refresh" && this.name!="Open")
// // remove(where=this.name in var1)

// // var excludedWindows = ["Explorer", "Browser"]
// // exclude.where = !window.name in excludedWindows
// // remove(where=!this.name in excludedWindows)

// // remove(where=this.name!="Open" || this.name!="tesst")
// // items_to_remove = ["Open", "refresh", "settings", "test"]
// // remove(where=items_to_remove.contains(this.name))

// // remove(where=this.name!="refresh")
// // theme
// // {
// // 	name="modern"
// // 	dark=auto
// // 	background
// // 	{
// // 		color=white
// // 		opacity=100
// // 		effect=0
// // 	}
// // 	image.align=2
// // }

// // import 'imports/theme.nss'
// import 'custom_themes/custom/custom_theme.nss'
// import 'imports/images.nss'

// // import 'imports/modify.nss'


// // item(title='Audio Device' image=[\uE28A, #22A7F2] cmd='mmsys.cpl')
// // item(title='Subscene' image=[\uE270, #22A7F2] cmd='chrome' args='"https://subscene.com/subtitles/searchbytitle"')
// // item(title='qBittorrent' image=[\uE0CA, #22A7F2] cmd='cmd.exe' args='START /MAX CMD /C "C:\Program Files\qBittorrent\qbittorrent.exe"')
// // separator
// // separator

// // menu(type='file|dir|back|root|namespace' mode="multiple"  title='copy to clipboard' image=#ff00ff)


// menu(type='file' mode="none" title="Test -> Type: file" image=icon.pin) {
//     item(title='Test -> Type: file' cmd=msg('Hello @user.name'))
// }
// menu(type='dir' mode="none" title="Test -> Type: dir" image=icon.pin) {
//     item(title='Test -> Type: dir' cmd=msg('Hello @user.name'))
// }
// menu(type='~taskbar' mode="none" sep="both" title="Test -> Type: ~taskbar" image=icon.pin) {
//     item(title='Test -> Type: ~taskbar' cmd=msg('Hello @user.name'))
// }


// // import 'imports/terminal.nss'
// // import 'imports/file-manage.nss'
// // import 'imports/develop.nss'
// // import 'imports/goto.nss'
// // import 'imports/taskbar.nss'
