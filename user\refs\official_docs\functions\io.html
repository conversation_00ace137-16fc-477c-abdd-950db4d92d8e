﻿<h5>IO</h5>
<br>
<section id="io.attribute" class="my-5">
	<h5>io.attribute enumerations.</h5>
	<p>Syntax</p>
	<pre><code>io.attribute.archive
io.attribute.compressed
io.attribute.device
io.attribute.directory
io.attribute.encrypted
io.attribute.hidden
io.attribute.invalid
io.attribute.normal
io.attribute.offline
io.attribute.readonly
io.attribute.sparsefile
io.attribute.system
io.attribute.temporary
io.attribute.virtual</code></pre>
</section>

<section id="io.attributes" class="my-5">
	<h5>io.attributes</h5>
	<p>Retrieves file system attributes for a specified path.</p>
	<p>Syntax</p>
	<code>io.attributes(path)</code><br><br>
	<p>attribute verification</p>
<pre><code>// check if path is hidden
io.attribute.hidden(path)

// check if path is directory
io.attribute.directory(path)

var { atrr = io.attributes(path) }

// check if attr is hidden
io.attribute.hidden(atrr)

// check if attr is directory
io.attribute.directory(atrr)</code></pre>
</section>

<section id="io.copy" class="my-5">
	<h5>io.copy</h5>
	<p>Copies an existing file to a new file.</p>
	<p>Syntax</p>
	<code>io.copy(pathFrom, pathTo)</code>
	<br>
	<code>io.copy(pathFrom, pathTo, options)</code>
	options:<br>
	1 = skip_existing, 2 = overwrite_existing, 4 = update_existing, 16 = recursive<br>
default = update_existing | recursive<br><br>
	Example:<br>
	<code>io.copy('c:\old', 'd:\new', 16 | 4)</code>
</section>

<section id="io.move" class="my-5">
	<h5>io.move</h5>
	<p>Moves an existing file or a directory, including its children..</p>
	<p>Syntax</p>
	<code>io.move(oldPath, newPath)</code>
</section>

<section id="io.rename" class="my-5">
	<h5>io.rename</h5>
	<p>Rename a file or directory.</p>
	<p>Syntax</p>
	<code>io.rename(oldName, newName)</code>
</section>

<section id="io.delete" class="my-5">
	<h5>io.delete</h5>
	<p>Deletes an existing path.</p>
	<p>Syntax</p>
	<code>io.delete(path)</code>
</section>

<section id="io.directory.create" class="my-5">
	<h5>io.directory.create (io.dir.create)</h5>
	<p>Create new directory.</p>
	<p>Syntax</p>
	<code>io.directory.create(path)</code>
</section>

<section id="io.directory.exists" class="my-5">
	<h5>io.directory.exists (io.dir.exists)</h5>
	<p>Check if one or more directories exists.</p>
	<p>Syntax</p>
	<code>io.directory.exists(path)</code>
	<br>
	<code>io.directory.exists(path1, path2, path3, ...)</code>
</section>

<section id="io.directory.empty" class="my-5">
	<h5>io.directory.empty (io.dir.empty)</h5>
	<p>Check if one or more directory is empty.</p>
	<p>Syntax</p>
	<code>io.directory.empty(path)</code>
	<br>
	<code>io.directory.empty(path1, path2, path3, ...)</code>
</section>

<h5 id="file-functions" class="my-5">File functions.</h5>

<section id="io.file.size" class="my-5">
	<h5>io.file.size</h5>
	<p>Retrieves the size of the specified file, in bytes.</p>
	<p>Syntax</p>
	<code>io.file.size(path)</code>
</section>

<section id="io.file.exists" class="my-5">
	<h5>io.file.exists</h5>
	<p>Check if one or more files exists.</p>
	<p>Syntax</p>
	<code>io.file.exists(path)</code>
	<br>
	<code>io.file.exists(path1, path2, path3, ...)</code>
</section>

<section id="io.file.read" class="my-5">
	<h5>io.file.read</h5>
	<p>Read file contents as text with character count option.</p>
	<p>Syntax</p>
	<code>io.file.read(path)</code><br>
	<code>io.file.read(path, 12)</code>
</section>

<section id="io.file.create" class="my-5">
	<h5>io.file.create</h5>
	<p>Create new file with content option.</p>
	<p>Syntax</p>
	<code>io.file.create(path)</code><br>
	<code>io.file.create(path, "Hello World!")</code>
</section>

<section id="io.file.write" class="my-5">
	<h5>io.file.write</h5>
	<p>Writing to a file with new content.</p>
	<p>Syntax</p>
	<code>io.file.write(path, "Hello World!")</code>
</section>

<section id="io.file.append" class="my-5">
	<h5>io.file.append</h5>
	<p>Appends text to an existing file, or to a new file if the specified file does not exist.</p>
	<p>Syntax</p>
	<code>io.file.append(path, "Hello ")</code>
	<br>
	<code>io.file.append(path, "World!")</code>
</section>

<section id="io.datetime" class="my-5">
	<h5>io.datetime</h5>
	<p>Gets or Sets the time of the file.</p>
	<p>Syntax</p>
	get date time
	<code>
io.datetime.created(sel.path)<br>
io.datetime.modified(sel.path)<br>
io.datetime.accessed(sel.path)<br>
<br>
io.datetime.created(sel.path, 'y/m/d')<br>
io.datetime.modified(sel.path, 'y/m/d')<br>
io.datetime.accessed(sel.path, 'y/m/d')<br>
		
	</code>
	<br>
	set date time
	<br>
	<code>
io.datetime.created(sel.path,2000,1,1))<br>
io.datetime.modified(sel.path,2000,1,1))<br>
io.datetime.accessed(sel.path,2000,1,1))<br>
	</code>
</section>

<section id="io.meta" class="my-5">
	<h5>io.meta</h5>
	<p>Gets meta data by <a href="https://github.com/MicrosoftDocs/win32/blob/docs/desktop-src/properties/core-bumper.md">property key</a>.</p>
	<p>Syntax</p>
	<code>io.meta('path\to\file',"System.Size"))</code>
</section>
