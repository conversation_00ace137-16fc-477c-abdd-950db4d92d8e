
//
$APP_USER_NOTEPADPLUSPLUS_DIR = '@user.desktop\my\flow\home\__GOTO__\Apps\app_notepad++\exe'
$APP_USER_NOTEPADPLUSPLUS_EXE = '@APP_USER_NOTEPADPLUSPLUS_DIR\notepad++.exe'
$APP_USER_NOTEPADPLUSPLUS_TIP = "..."+str.trimstart('@APP_USER_NOTEPADPLUSPLUS_EXE','@app.dir')

// -> context: file
item(
    title  = ":  &Notepad++"
    keys   = "exe"
    type   = 'File'
    args   = '"@sel.file"'
    where  = !str.equals(sel.file.ext,[".7z",".zip",".rar",".apk",".bin",".dll",".dmg",".exe",".sys"])
    //
    image  = APP_USER_NOTEPADPLUSPLUS_EXE
    tip    = [APP_USER_NOTEPADPLUSPLUS_TIP,TIP3,0.8]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_USER_NOTEPADPLUSPLUS_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_NOTEPADPLUSPLUS_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_NOTEPADPLUSPLUS_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_NOTEPADPLUSPLUS_DIR')),
    }
)
// context: directory
item(
    title  = ":  &Notepad++"
    keys   = "exe"
    type   = 'Desktop|Dir|Drive|Back.Dir|Back.Drive'
    args   = '"@sel.dir"'
    //
    image  = APP_USER_NOTEPADPLUSPLUS_EXE
    tip    = [APP_USER_NOTEPADPLUSPLUS_TIP,TIP3,0.8]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_USER_NOTEPADPLUSPLUS_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_NOTEPADPLUSPLUS_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_NOTEPADPLUSPLUS_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_NOTEPADPLUSPLUS_DIR')),
    }
)
// Context: Taskbar
item(
    title  = ":  &Notepad++"
    keys   = "exe"
    type   = 'Taskbar'
    args   = ''
    //
    image  = APP_USER_NOTEPADPLUSPLUS_EXE
    tip    = [APP_USER_NOTEPADPLUSPLUS_TIP,TIP3,0.8]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_USER_NOTEPADPLUSPLUS_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_NOTEPADPLUSPLUS_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_NOTEPADPLUSPLUS_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_NOTEPADPLUSPLUS_DIR')),
    }
)
