
//
$PS1_COPYPATH = '-Command @sel("\\\"",",") | % {[System.IO.Path]::GetFullPath($_)} | Set-Clipboard'

//
item(
    title    = "&Copy Path"
    keys     = ""
    type   = 'Desktop|Dir|Drive|Back.Dir|Back.Drive|File'
    where  = sel.count==1
    cmd-ps = '@PS1_COPYPATH'
    //
    image    = [E0B5,G<PERSON><PERSON>] image-sel=[E0B5,HOVER]
    // tip      = ["@PS1_COPYPATH",TIP3,0.75]
    window   = 'Hidden'
)
