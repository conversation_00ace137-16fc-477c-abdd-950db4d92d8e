

$default_icon_size=20

menu(type='taskbar' title='Custom Icons' image=\uE249 tip=['Embedded Glyphs Gallery'] pos=0)
{
        // item(image=icon.glyph(\uE0A4, #ffffff) tip=['E001'] cmd=command.copy('\uE001'))
        item(image=[\uE0A4, #ffffff] tip=['E001'] cmd=command.copy('\uE001'))

item(image=["\uE000", #ffffff] tip='E000 (account-login)' cmd=command.copy('\uE000'))
item(image=["\uE001", #ffffff] tip='E001 (account-logout)' cmd=command.copy('\uE001'))
item(image=["\uE002", #ffffff] tip='E002 (action-redo)' cmd=command.copy('\uE002'))
item(image=["\uE003", #ffffff] tip='E003 (action-undo)' cmd=command.copy('\uE003'))
item(image=["\uE004", #ffffff] tip='E004 (align-center)' cmd=command.copy('\uE004'))
item(image=["\uE005", #ffffff] tip='E005 (align-left)' cmd=command.copy('\uE005'))
item(image=["\uE006", #ffffff] tip='E006 (align-right)' cmd=command.copy('\uE006'))
item(image=["\uE007", #ffffff] tip='E007 (aperture)' cmd=command.copy('\uE007'))
item(image=["\uE008", #ffffff] tip='E008 (arrow-bottom-fill-acute)' cmd=command.copy('\uE008'))
item(image=["\uE009", #ffffff] tip='E009 (arrow-bottom-fill-angled)' cmd=command.copy('\uE009'))

item(image=["\uE00A", #ffffff] tip='E00A (arrow-bottom-fill-large-acute)' cmd=command.copy('\uE00A'))
item(image=["\uE00B", #ffffff] tip='E00B (arrow-bottom-fill-large-angled)' cmd=command.copy('\uE00B'))
item(image=["\uE00C", #ffffff] tip='E00C (arrow-bottom-fill-large-oblique)' cmd=command.copy('\uE00C'))
item(image=["\uE00D", #ffffff] tip='E00D (arrow-bottom-fill-large)' cmd=command.copy('\uE00D'))
item(image=["\uE00E", #ffffff] tip='E00E (arrow-bottom-fill-oblique)' cmd=command.copy('\uE00E'))
item(image=["\uE00F", #ffffff] tip='E00F (arrow-bottom-fill)' cmd=command.copy('\uE00F'))
item(image=["\uE010", #ffffff] tip='E010 (arrow-bottom-left-fill-acute)' cmd=command.copy('\uE010'))
item(image=["\uE011", #ffffff] tip='E011 (arrow-bottom-left-fill-angled)' cmd=command.copy('\uE011'))
item(image=["\uE012", #ffffff] tip='E012 (arrow-bottom-left-fill-large-acu)' cmd=command.copy('\uE012'))
item(image=["\uE013", #ffffff] tip='E013 (arrow-bottom-left-fill-large-ang)' cmd=command.copy('\uE013') col)

item(image=["\uE014", #ffffff] tip='E014 (arrow-bottom-left-fill-large-obl)' cmd=command.copy('\uE014'))
item(image=["\uE015", #ffffff] tip='E015 (arrow-bottom-left-fill-large)' cmd=command.copy('\uE015'))
item(image=["\uE016", #ffffff] tip='E016 (arrow-bottom-left-fill-oblique)' cmd=command.copy('\uE016'))
item(image=["\uE017", #ffffff] tip='E017 (arrow-bottom-left-fill)' cmd=command.copy('\uE017'))
item(image=["\uE018", #ffffff] tip='E018 (arrow-bottom-left-line-acute)' cmd=command.copy('\uE018'))
item(image=["\uE019", #ffffff] tip='E019 (arrow-bottom-left-line-large-acu)' cmd=command.copy('\uE019'))
item(image=["\uE01A", #ffffff] tip='E01A (arrow-bottom-left-line-large-obl)' cmd=command.copy('\uE01A'))
item(image=["\uE01B", #ffffff] tip='E01B (arrow-bottom-left-line-large)' cmd=command.copy('\uE01B'))
item(image=["\uE01C", #ffffff] tip='E01C (arrow-bottom-left-line-oblique)' cmd=command.copy('\uE01C'))

item(image=["\uE01D", #ffffff] tip='E01D (arrow-bottom-left-line)' cmd=command.copy('\uE01D'))
item(image=["\uE01E", #ffffff] tip='E01E (arrow-bottom-line-acute)' cmd=command.copy('\uE01E'))
item(image=["\uE01F", #ffffff] tip='E01F (arrow-bottom-line-large-acute)' cmd=command.copy('\uE01F'))
item(image=["\uE020", #ffffff] tip='E020 (arrow-bottom-line-large-oblique)' cmd=command.copy('\uE020'))
item(image=["\uE021", #ffffff] tip='E021 (arrow-bottom-line-large)' cmd=command.copy('\uE021'))
item(image=["\uE022", #ffffff] tip='E022 (arrow-bottom-line-oblique)' cmd=command.copy('\uE022'))
item(image=["\uE023", #ffffff] tip='E023 (arrow-bottom-line)' cmd=command.copy('\uE023'))
item(image=["\uE024", #ffffff] tip='E024 (arrow-bottom-right-fill-acute)' cmd=command.copy('\uE024'))
item(image=["\uE025", #ffffff] tip='E025 (arrow-bottom-right-fill-angled)' cmd=command.copy('\uE025'))
item(image=["\uE026", #ffffff] tip='E026 (arrow-bottom-right-fill-large-ac)' cmd=command.copy('\uE026') col)

item(image=["\uE027", #ffffff] tip='E027 (arrow-bottom-right-fill-large-an)' cmd=command.copy('\uE027'))
item(image=["\uE028", #ffffff] tip='E028 (arrow-bottom-right-fill-large-ob)' cmd=command.copy('\uE028'))
item(image=["\uE029", #ffffff] tip='E029 (arrow-bottom-right-fill-large)' cmd=command.copy('\uE029'))
item(image=["\uE02A", #ffffff] tip='E02A (arrow-bottom-right-fill-oblique)' cmd=command.copy('\uE02A'))
item(image=["\uE02B", #ffffff] tip='E02B (arrow-bottom-right-fill)' cmd=command.copy('\uE02B'))
item(image=["\uE02C", #ffffff] tip='E02C (arrow-bottom-right-line-acute)' cmd=command.copy('\uE02C'))
item(image=["\uE02D", #ffffff] tip='E02D (arrow-bottom-right-line-large-ac)' cmd=command.copy('\uE02D'))
item(image=["\uE02E", #ffffff] tip='E02E (arrow-bottom-right-line-large-ob)' cmd=command.copy('\uE02E'))
item(image=["\uE02F", #ffffff] tip='E02F (arrow-bottom-right-line-large)' cmd=command.copy('\uE02F'))
item(image=["\uE030", #ffffff] tip='E030 (arrow-bottom-right-line-oblique)' cmd=command.copy('\uE030'))

item(image=["\uE031", #ffffff] tip='E031 (arrow-bottom-right-line)' cmd=command.copy('\uE031'))
item(image=["\uE032", #ffffff] tip='E032 (arrow-left-fill-acute)' cmd=command.copy('\uE032'))
item(image=["\uE033", #ffffff] tip='E033 (arrow-left-fill-angled)' cmd=command.copy('\uE033'))
item(image=["\uE034", #ffffff] tip='E034 (arrow-left-fill-large-acute)' cmd=command.copy('\uE034'))
item(image=["\uE035", #ffffff] tip='E035 (arrow-left-fill-large-angled)' cmd=command.copy('\uE035'))
item(image=["\uE036", #ffffff] tip='E036 (arrow-left-fill-large-oblique)' cmd=command.copy('\uE036'))
item(image=["\uE037", #ffffff] tip='E037 (arrow-left-fill-large)' cmd=command.copy('\uE037'))
item(image=["\uE038", #ffffff] tip='E038 (arrow-left-fill-oblique)' cmd=command.copy('\uE038'))
item(image=["\uE039", #ffffff] tip='E039 (arrow-left-fill)' cmd=command.copy('\uE039'))
item(image=["\uE03A", #ffffff] tip='E03A (arrow-left-line-acute)' cmd=command.copy('\uE03A') col)

item(image=["\uE03B", #ffffff] tip='E03B (arrow-left-line-large-acute)' cmd=command.copy('\uE03B'))
item(image=["\uE03C", #ffffff] tip='E03C (arrow-left-line-large-oblique)' cmd=command.copy('\uE03C'))
item(image=["\uE03D", #ffffff] tip='E03D (arrow-left-line-large)' cmd=command.copy('\uE03D'))
item(image=["\uE03E", #ffffff] tip='E03E (arrow-left-line-oblique)' cmd=command.copy('\uE03E'))
item(image=["\uE03F", #ffffff] tip='E03F (arrow-left-line)' cmd=command.copy('\uE03F'))
item(image=["\uE040", #ffffff] tip='E040 (arrow-right-angle-bottom-left-fi)' cmd=command.copy('\uE040'))
item(image=["\uE041", #ffffff] tip='E041 (arrow-right-angle-bottom-left-fi)' cmd=command.copy('\uE041'))
item(image=["\uE042", #ffffff] tip='E042 (arrow-right-angle-bottom-left-fi)' cmd=command.copy('\uE042'))
item(image=["\uE043", #ffffff] tip='E043 (arrow-right-angle-bottom-left-li)' cmd=command.copy('\uE043'))
item(image=["\uE044", #ffffff] tip='E044 (arrow-right-angle-bottom-right-f)' cmd=command.copy('\uE044'))

item(image=["\uE045", #ffffff] tip='E045 (arrow-right-angle-bottom-right-f)' cmd=command.copy('\uE045'))
item(image=["\uE046", #ffffff] tip='E046 (arrow-right-angle-bottom-right-f)' cmd=command.copy('\uE046'))
item(image=["\uE047", #ffffff] tip='E047 (arrow-right-angle-bottom-right-l)' cmd=command.copy('\uE047'))
item(image=["\uE048", #ffffff] tip='E048 (arrow-right-angle-left-bottom-fi)' cmd=command.copy('\uE048'))
item(image=["\uE049", #ffffff] tip='E049 (arrow-right-angle-left-bottom-fi)' cmd=command.copy('\uE049'))
item(image=["\uE04A", #ffffff] tip='E04A (arrow-right-angle-left-bottom-fi)' cmd=command.copy('\uE04A'))
item(image=["\uE04B", #ffffff] tip='E04B (arrow-right-angle-left-bottom-li)' cmd=command.copy('\uE04B'))
item(image=["\uE04C", #ffffff] tip='E04C (arrow-right-angle-left-top-fill-)' cmd=command.copy('\uE04C'))
item(image=["\uE04D", #ffffff] tip='E04D (arrow-right-angle-left-top-fill-)' cmd=command.copy('\uE04D'))
item(image=["\uE04E", #ffffff] tip='E04E (arrow-right-angle-left-top-fill)' cmd=command.copy('\uE04E') col)

item(image=["\uE04F", #ffffff] tip='E04F (arrow-right-angle-left-top-line)' cmd=command.copy('\uE04F'))
item(image=["\uE050", #ffffff] tip='E050 (arrow-right-angle-right-bottom-f)' cmd=command.copy('\uE050'))
item(image=["\uE051", #ffffff] tip='E051 (arrow-right-angle-right-bottom-f)' cmd=command.copy('\uE051'))
item(image=["\uE052", #ffffff] tip='E052 (arrow-right-angle-right-bottom-f)' cmd=command.copy('\uE052'))
item(image=["\uE053", #ffffff] tip='E053 (arrow-right-angle-right-bottom-l)' cmd=command.copy('\uE053'))
item(image=["\uE054", #ffffff] tip='E054 (arrow-right-angle-right-top-fill)' cmd=command.copy('\uE054'))
item(image=["\uE055", #ffffff] tip='E055 (arrow-right-angle-right-top-fill)' cmd=command.copy('\uE055'))
item(image=["\uE056", #ffffff] tip='E056 (arrow-right-angle-right-top-fill)' cmd=command.copy('\uE056'))
item(image=["\uE057", #ffffff] tip='E057 (arrow-right-angle-right-top-line)' cmd=command.copy('\uE057'))
item(image=["\uE058", #ffffff] tip='E058 (arrow-right-angle-thick-bottom-l)' cmd=command.copy('\uE058'))

item(image=["\uE059", #ffffff] tip='E059 (arrow-right-angle-thick-bottom-l)' cmd=command.copy('\uE059'))
item(image=["\uE05A", #ffffff] tip='E05A (arrow-right-angle-thick-bottom-l)' cmd=command.copy('\uE05A'))
item(image=["\uE05B", #ffffff] tip='E05B (arrow-right-angle-thick-bottom-l)' cmd=command.copy('\uE05B'))
item(image=["\uE05C", #ffffff] tip='E05C (arrow-right-angle-thick-bottom-r)' cmd=command.copy('\uE05C'))
item(image=["\uE05D", #ffffff] tip='E05D (arrow-right-angle-thick-bottom-r)' cmd=command.copy('\uE05D'))
item(image=["\uE05E", #ffffff] tip='E05E (arrow-right-angle-thick-bottom-r)' cmd=command.copy('\uE05E'))
item(image=["\uE05F", #ffffff] tip='E05F (arrow-right-angle-thick-bottom-r)' cmd=command.copy('\uE05F'))
item(image=["\uE060", #ffffff] tip='E060 (arrow-right-angle-thick-left-bot)' cmd=command.copy('\uE060'))
item(image=["\uE061", #ffffff] tip='E061 (arrow-right-angle-thick-left-bot)' cmd=command.copy('\uE061'))
item(image=["\uE062", #ffffff] tip='E062 (arrow-right-angle-thick-left-bot)' cmd=command.copy('\uE062') col)

item(image=["\uE063", #ffffff] tip='E063 (arrow-right-angle-thick-left-bot)' cmd=command.copy('\uE063'))
item(image=["\uE064", #ffffff] tip='E064 (arrow-right-angle-thick-left-top)' cmd=command.copy('\uE064'))
item(image=["\uE065", #ffffff] tip='E065 (arrow-right-angle-thick-left-top)' cmd=command.copy('\uE065'))
item(image=["\uE066", #ffffff] tip='E066 (arrow-right-angle-thick-left-top)' cmd=command.copy('\uE066'))
item(image=["\uE067", #ffffff] tip='E067 (arrow-right-angle-thick-left-top)' cmd=command.copy('\uE067'))
item(image=["\uE068", #ffffff] tip='E068 (arrow-right-angle-thick-right-bo)' cmd=command.copy('\uE068'))
item(image=["\uE069", #ffffff] tip='E069 (arrow-right-angle-thick-right-bo)' cmd=command.copy('\uE069'))
item(image=["\uE06A", #ffffff] tip='E06A (arrow-right-angle-thick-right-bo)' cmd=command.copy('\uE06A'))
item(image=["\uE06B", #ffffff] tip='E06B (arrow-right-angle-thick-right-bo)' cmd=command.copy('\uE06B'))
item(image=["\uE06C", #ffffff] tip='E06C (arrow-right-angle-thick-right-to)' cmd=command.copy('\uE06C'))

item(image=["\uE06D", #ffffff] tip='E06D (arrow-right-angle-thick-right-to)' cmd=command.copy('\uE06D'))
item(image=["\uE06E", #ffffff] tip='E06E (arrow-right-angle-thick-right-to)' cmd=command.copy('\uE06E'))
item(image=["\uE06F", #ffffff] tip='E06F (arrow-right-angle-thick-right-to)' cmd=command.copy('\uE06F'))
item(image=["\uE070", #ffffff] tip='E070 (arrow-right-angle-thick-top-left)' cmd=command.copy('\uE070'))
item(image=["\uE071", #ffffff] tip='E071 (arrow-right-angle-thick-top-left)' cmd=command.copy('\uE071'))
item(image=["\uE072", #ffffff] tip='E072 (arrow-right-angle-thick-top-left)' cmd=command.copy('\uE072'))
item(image=["\uE073", #ffffff] tip='E073 (arrow-right-angle-thick-top-left)' cmd=command.copy('\uE073'))
item(image=["\uE074", #ffffff] tip='E074 (arrow-right-angle-thick-top-righ)' cmd=command.copy('\uE074'))
item(image=["\uE075", #ffffff] tip='E075 (arrow-right-angle-thick-top-righ)' cmd=command.copy('\uE075'))
item(image=["\uE076", #ffffff] tip='E076 (arrow-right-angle-thick-top-righ)' cmd=command.copy('\uE076') col)

item(image=["\uE077", #ffffff] tip='E077 (arrow-right-angle-thick-top-righ)' cmd=command.copy('\uE077'))
item(image=["\uE078", #ffffff] tip='E078 (arrow-right-angle-top-left-fill-)' cmd=command.copy('\uE078'))
item(image=["\uE079", #ffffff] tip='E079 (arrow-right-angle-top-left-fill-)' cmd=command.copy('\uE079'))
item(image=["\uE07A", #ffffff] tip='E07A (arrow-right-angle-top-left-fill)' cmd=command.copy('\uE07A'))
item(image=["\uE07B", #ffffff] tip='E07B (arrow-right-angle-top-left-line)' cmd=command.copy('\uE07B'))
item(image=["\uE07C", #ffffff] tip='E07C (arrow-right-angle-top-right-fill)' cmd=command.copy('\uE07C'))
item(image=["\uE07D", #ffffff] tip='E07D (arrow-right-angle-top-right-fill)' cmd=command.copy('\uE07D'))
item(image=["\uE07E", #ffffff] tip='E07E (arrow-right-angle-top-right-fill)' cmd=command.copy('\uE07E'))
item(image=["\uE07F", #ffffff] tip='E07F (arrow-right-angle-top-right-line)' cmd=command.copy('\uE07F'))
item(image=["\uE080", #ffffff] tip='E080 (arrow-right-fill-acute)' cmd=command.copy('\uE080'))

item(image=["\uE081", #ffffff] tip='E081 (arrow-right-fill-angled)' cmd=command.copy('\uE081'))
item(image=["\uE082", #ffffff] tip='E082 (arrow-right-fill-large-acute)' cmd=command.copy('\uE082'))
item(image=["\uE083", #ffffff] tip='E083 (arrow-right-fill-large-angled)' cmd=command.copy('\uE083'))
item(image=["\uE084", #ffffff] tip='E084 (arrow-right-fill-large-oblique)' cmd=command.copy('\uE084'))
item(image=["\uE085", #ffffff] tip='E085 (arrow-right-fill-large)' cmd=command.copy('\uE085'))
item(image=["\uE086", #ffffff] tip='E086 (arrow-right-fill-oblique)' cmd=command.copy('\uE086'))
item(image=["\uE087", #ffffff] tip='E087 (arrow-right-fill)' cmd=command.copy('\uE087'))
item(image=["\uE088", #ffffff] tip='E088 (arrow-right-line-acute)' cmd=command.copy('\uE088'))
item(image=["\uE089", #ffffff] tip='E089 (arrow-right-line-large-acute)' cmd=command.copy('\uE089'))
item(image=["\uE08A", #ffffff] tip='E08A (arrow-right-line-large-oblique)' cmd=command.copy('\uE08A') col)

item(image=["\uE08B", #ffffff] tip='E08B (arrow-right-line-large)' cmd=command.copy('\uE08B'))
item(image=["\uE08C", #ffffff] tip='E08C (arrow-right-line-oblique)' cmd=command.copy('\uE08C'))
item(image=["\uE08D", #ffffff] tip='E08D (arrow-right-line)' cmd=command.copy('\uE08D'))
item(image=["\uE08E", #ffffff] tip='E08E (arrow-thick-bottom-fill-acute)' cmd=command.copy('\uE08E'))
item(image=["\uE08F", #ffffff] tip='E08F (arrow-thick-bottom-fill-angled)' cmd=command.copy('\uE08F'))
item(image=["\uE090", #ffffff] tip='E090 (arrow-thick-bottom-fill-large-ac)' cmd=command.copy('\uE090'))
item(image=["\uE091", #ffffff] tip='E091 (arrow-thick-bottom-fill-large-an)' cmd=command.copy('\uE091'))
item(image=["\uE092", #ffffff] tip='E092 (arrow-thick-bottom-fill-large-ob)' cmd=command.copy('\uE092'))
item(image=["\uE093", #ffffff] tip='E093 (arrow-thick-bottom-fill-large)' cmd=command.copy('\uE093'))
item(image=["\uE094", #ffffff] tip='E094 (arrow-thick-bottom-fill-oblique)' cmd=command.copy('\uE094'))

item(image=["\uE095", #ffffff] tip='E095 (arrow-thick-bottom-fill)' cmd=command.copy('\uE095'))
item(image=["\uE096", #ffffff] tip='E096 (arrow-thick-bottom-left-fill-acu)' cmd=command.copy('\uE096'))
item(image=["\uE097", #ffffff] tip='E097 (arrow-thick-bottom-left-fill-ang)' cmd=command.copy('\uE097'))
item(image=["\uE098", #ffffff] tip='E098 (arrow-thick-bottom-left-fill-lar)' cmd=command.copy('\uE098'))
item(image=["\uE099", #ffffff] tip='E099 (arrow-thick-bottom-left-fill-lar)' cmd=command.copy('\uE099'))
item(image=["\uE09A", #ffffff] tip='E09A (arrow-thick-bottom-left-fill-lar)' cmd=command.copy('\uE09A'))
item(image=["\uE09B", #ffffff] tip='E09B (arrow-thick-bottom-left-fill-lar)' cmd=command.copy('\uE09B'))
item(image=["\uE09C", #ffffff] tip='E09C (arrow-thick-bottom-left-fill-obl)' cmd=command.copy('\uE09C'))
item(image=["\uE09D", #ffffff] tip='E09D (arrow-thick-bottom-left-fill)' cmd=command.copy('\uE09D'))
item(image=["\uE09E", #ffffff] tip='E09E (arrow-thick-bottom-left-line-acu)' cmd=command.copy('\uE09E') col)

item(image=["\uE09F", #ffffff] tip='E09F (arrow-thick-bottom-left-line-lar)' cmd=command.copy('\uE09F'))
item(image=["\uE0A0", #ffffff] tip='E0A0 (arrow-thick-bottom-left-line-lar)' cmd=command.copy('\uE0A0'))
item(image=["\uE0A1", #ffffff] tip='E0A1 (arrow-thick-bottom-left-line-lar)' cmd=command.copy('\uE0A1'))
item(image=["\uE0A2", #ffffff] tip='E0A2 (arrow-thick-bottom-left-line-obl)' cmd=command.copy('\uE0A2'))
item(image=["\uE0A3", #ffffff] tip='E0A3 (arrow-thick-bottom-left-line)' cmd=command.copy('\uE0A3'))
item(image=["\uE0A4", #ffffff] tip='E0A4 (arrow-thick-bottom-line-acute)' cmd=command.copy('\uE0A4'))
item(image=["\uE0A5", #ffffff] tip='E0A5 (arrow-thick-bottom-line-large-ac)' cmd=command.copy('\uE0A5'))
item(image=["\uE0A6", #ffffff] tip='E0A6 (arrow-thick-bottom-line-large-ob)' cmd=command.copy('\uE0A6'))
item(image=["\uE0A7", #ffffff] tip='E0A7 (arrow-thick-bottom-line-large)' cmd=command.copy('\uE0A7'))
item(image=["\uE0A8", #ffffff] tip='E0A8 (arrow-thick-bottom-line-oblique)' cmd=command.copy('\uE0A8'))

item(image=["\uE0A9", #ffffff] tip='E0A9 (arrow-thick-bottom-line)' cmd=command.copy('\uE0A9'))
item(image=["\uE0AA", #ffffff] tip='E0AA (arrow-thick-bottom-right-fill-ac)' cmd=command.copy('\uE0AA'))
item(image=["\uE0AB", #ffffff] tip='E0AB (arrow-thick-bottom-right-fill-an)' cmd=command.copy('\uE0AB'))
item(image=["\uE0AC", #ffffff] tip='E0AC (arrow-thick-bottom-right-fill-la)' cmd=command.copy('\uE0AC'))
item(image=["\uE0AD", #ffffff] tip='E0AD (arrow-thick-bottom-right-fill-la)' cmd=command.copy('\uE0AD'))
item(image=["\uE0AE", #ffffff] tip='E0AE (arrow-thick-bottom-right-fill-la)' cmd=command.copy('\uE0AE'))
item(image=["\uE0AF", #ffffff] tip='E0AF (arrow-thick-bottom-right-fill-la)' cmd=command.copy('\uE0AF'))
item(image=["\uE0B0", #ffffff] tip='E0B0 (arrow-thick-bottom-right-fill-ob)' cmd=command.copy('\uE0B0'))
item(image=["\uE0B1", #ffffff] tip='E0B1 (arrow-thick-bottom-right-fill)' cmd=command.copy('\uE0B1'))
item(image=["\uE0B2", #ffffff] tip='E0B2 (arrow-thick-bottom-right-line-ac)' cmd=command.copy('\uE0B2') col)

item(image=["\uE0B3", #ffffff] tip='E0B3 (arrow-thick-bottom-right-line-la)' cmd=command.copy('\uE0B3'))
item(image=["\uE0B4", #ffffff] tip='E0B4 (arrow-thick-bottom-right-line-la)' cmd=command.copy('\uE0B4'))
item(image=["\uE0B5", #ffffff] tip='E0B5 (arrow-thick-bottom-right-line-la)' cmd=command.copy('\uE0B5'))
item(image=["\uE0B6", #ffffff] tip='E0B6 (arrow-thick-bottom-right-line-ob)' cmd=command.copy('\uE0B6'))
item(image=["\uE0B7", #ffffff] tip='E0B7 (arrow-thick-bottom-right-line)' cmd=command.copy('\uE0B7'))
item(image=["\uE0B8", #ffffff] tip='E0B8 (arrow-thick-left-fill-acute)' cmd=command.copy('\uE0B8'))
item(image=["\uE0B9", #ffffff] tip='E0B9 (arrow-thick-left-fill-angled)' cmd=command.copy('\uE0B9'))
item(image=["\uE0BA", #ffffff] tip='E0BA (arrow-thick-left-fill-large-acut)' cmd=command.copy('\uE0BA'))
item(image=["\uE0BB", #ffffff] tip='E0BB (arrow-thick-left-fill-large-angl)' cmd=command.copy('\uE0BB'))
item(image=["\uE0BC", #ffffff] tip='E0BC (arrow-thick-left-fill-large-obli)' cmd=command.copy('\uE0BC'))

item(image=["\uE0BD", #ffffff] tip='E0BD (arrow-thick-left-fill-large)' cmd=command.copy('\uE0BD'))
item(image=["\uE0BE", #ffffff] tip='E0BE (arrow-thick-left-fill-oblique)' cmd=command.copy('\uE0BE'))
item(image=["\uE0BF", #ffffff] tip='E0BF (arrow-thick-left-fill)' cmd=command.copy('\uE0BF'))
item(image=["\uE0C0", #ffffff] tip='E0C0 (arrow-thick-left-line-acute)' cmd=command.copy('\uE0C0'))
item(image=["\uE0C1", #ffffff] tip='E0C1 (arrow-thick-left-line-large-acut)' cmd=command.copy('\uE0C1'))
item(image=["\uE0C2", #ffffff] tip='E0C2 (arrow-thick-left-line-large-obli)' cmd=command.copy('\uE0C2'))
item(image=["\uE0C3", #ffffff] tip='E0C3 (arrow-thick-left-line-large)' cmd=command.copy('\uE0C3'))
item(image=["\uE0C4", #ffffff] tip='E0C4 (arrow-thick-left-line-oblique)' cmd=command.copy('\uE0C4'))
item(image=["\uE0C5", #ffffff] tip='E0C5 (arrow-thick-left-line)' cmd=command.copy('\uE0C5'))
item(image=["\uE0C6", #ffffff] tip='E0C6 (arrow-thick-right-fill-acute)' cmd=command.copy('\uE0C6') col)

item(image=["\uE0C7", #ffffff] tip='E0C7 (arrow-thick-right-fill-angled)' cmd=command.copy('\uE0C7'))
item(image=["\uE0C8", #ffffff] tip='E0C8 (arrow-thick-right-fill-large-acu)' cmd=command.copy('\uE0C8'))
item(image=["\uE0C9", #ffffff] tip='E0C9 (arrow-thick-right-fill-large-ang)' cmd=command.copy('\uE0C9'))
item(image=["\uE0CA", #ffffff] tip='E0CA (arrow-thick-right-fill-large-obl)' cmd=command.copy('\uE0CA'))
item(image=["\uE0CB", #ffffff] tip='E0CB (arrow-thick-right-fill-large)' cmd=command.copy('\uE0CB'))
item(image=["\uE0CC", #ffffff] tip='E0CC (arrow-thick-right-fill-oblique)' cmd=command.copy('\uE0CC'))
item(image=["\uE0CD", #ffffff] tip='E0CD (arrow-thick-right-fill)' cmd=command.copy('\uE0CD'))
item(image=["\uE0CE", #ffffff] tip='E0CE (arrow-thick-right-line-acute)' cmd=command.copy('\uE0CE'))
item(image=["\uE0CF", #ffffff] tip='E0CF (arrow-thick-right-line-large-acu)' cmd=command.copy('\uE0CF'))
item(image=["\uE0D0", #ffffff] tip='E0D0 (arrow-thick-right-line-large-obl)' cmd=command.copy('\uE0D0'))

item(image=["\uE0D1", #ffffff] tip='E0D1 (arrow-thick-right-line-large)' cmd=command.copy('\uE0D1'))
item(image=["\uE0D2", #ffffff] tip='E0D2 (arrow-thick-right-line-oblique)' cmd=command.copy('\uE0D2'))
item(image=["\uE0D3", #ffffff] tip='E0D3 (arrow-thick-right-line)' cmd=command.copy('\uE0D3'))
item(image=["\uE0D4", #ffffff] tip='E0D4 (arrow-thick-top-fill-acute)' cmd=command.copy('\uE0D4'))
item(image=["\uE0D5", #ffffff] tip='E0D5 (arrow-thick-top-fill-angled)' cmd=command.copy('\uE0D5'))
item(image=["\uE0D6", #ffffff] tip='E0D6 (arrow-thick-top-fill-large-acute)' cmd=command.copy('\uE0D6'))
item(image=["\uE0D7", #ffffff] tip='E0D7 (arrow-thick-top-fill-large-angle)' cmd=command.copy('\uE0D7'))
item(image=["\uE0D8", #ffffff] tip='E0D8 (arrow-thick-top-fill-large-obliq)' cmd=command.copy('\uE0D8'))
item(image=["\uE0D9", #ffffff] tip='E0D9 (arrow-thick-top-fill-large)' cmd=command.copy('\uE0D9'))
item(image=["\uE0DA", #ffffff] tip='E0DA (arrow-thick-top-fill-oblique)' cmd=command.copy('\uE0DA') col)

item(image=["\uE0DB", #ffffff] tip='E0DB (arrow-thick-top-fill)' cmd=command.copy('\uE0DB'))
item(image=["\uE0DC", #ffffff] tip='E0DC (arrow-thick-top-left-fill-acute)' cmd=command.copy('\uE0DC'))
item(image=["\uE0DD", #ffffff] tip='E0DD (arrow-thick-top-left-fill-angled)' cmd=command.copy('\uE0DD'))
item(image=["\uE0DE", #ffffff] tip='E0DE (arrow-thick-top-left-fill-large-)' cmd=command.copy('\uE0DE'))
item(image=["\uE0DF", #ffffff] tip='E0DF (arrow-thick-top-left-fill-large-)' cmd=command.copy('\uE0DF'))
item(image=["\uE0E0", #ffffff] tip='E0E0 (arrow-thick-top-left-fill-large-)' cmd=command.copy('\uE0E0'))
item(image=["\uE0E1", #ffffff] tip='E0E1 (arrow-thick-top-left-fill-large)' cmd=command.copy('\uE0E1'))
item(image=["\uE0E2", #ffffff] tip='E0E2 (arrow-thick-top-left-fill-obliqu)' cmd=command.copy('\uE0E2'))
item(image=["\uE0E3", #ffffff] tip='E0E3 (arrow-thick-top-left-fill)' cmd=command.copy('\uE0E3'))
item(image=["\uE0E4", #ffffff] tip='E0E4 (arrow-thick-top-left-line-acute)' cmd=command.copy('\uE0E4'))

item(image=["\uE0E5", #ffffff] tip='E0E5 (arrow-thick-top-left-line-large-)' cmd=command.copy('\uE0E5'))
item(image=["\uE0E6", #ffffff] tip='E0E6 (arrow-thick-top-left-line-large-)' cmd=command.copy('\uE0E6'))
item(image=["\uE0E7", #ffffff] tip='E0E7 (arrow-thick-top-left-line-large)' cmd=command.copy('\uE0E7'))
item(image=["\uE0E8", #ffffff] tip='E0E8 (arrow-thick-top-left-line-obliqu)' cmd=command.copy('\uE0E8'))
item(image=["\uE0E9", #ffffff] tip='E0E9 (arrow-thick-top-left-line)' cmd=command.copy('\uE0E9'))
item(image=["\uE0EA", #ffffff] tip='E0EA (arrow-thick-top-line-acute)' cmd=command.copy('\uE0EA'))
item(image=["\uE0EB", #ffffff] tip='E0EB (arrow-thick-top-line-large-acute)' cmd=command.copy('\uE0EB'))
item(image=["\uE0EC", #ffffff] tip='E0EC (arrow-thick-top-line-large-obliq)' cmd=command.copy('\uE0EC'))
item(image=["\uE0ED", #ffffff] tip='E0ED (arrow-thick-top-line-large)' cmd=command.copy('\uE0ED'))
item(image=["\uE0EE", #ffffff] tip='E0EE (arrow-thick-top-line-oblique)' cmd=command.copy('\uE0EE') col)

item(image=["\uE0EF", #ffffff] tip='E0EF (arrow-thick-top-line)' cmd=command.copy('\uE0EF'))
item(image=["\uE0F0", #ffffff] tip='E0F0 (arrow-thick-top-right-fill-acute)' cmd=command.copy('\uE0F0'))
item(image=["\uE0F1", #ffffff] tip='E0F1 (arrow-thick-top-right-fill-angle)' cmd=command.copy('\uE0F1'))
item(image=["\uE0F2", #ffffff] tip='E0F2 (arrow-thick-top-right-fill-large)' cmd=command.copy('\uE0F2'))
item(image=["\uE0F3", #ffffff] tip='E0F3 (arrow-thick-top-right-fill-large)' cmd=command.copy('\uE0F3'))
item(image=["\uE0F4", #ffffff] tip='E0F4 (arrow-thick-top-right-fill-large)' cmd=command.copy('\uE0F4'))
item(image=["\uE0F5", #ffffff] tip='E0F5 (arrow-thick-top-right-fill-large)' cmd=command.copy('\uE0F5'))
item(image=["\uE0F6", #ffffff] tip='E0F6 (arrow-thick-top-right-fill-obliq)' cmd=command.copy('\uE0F6'))
item(image=["\uE0F7", #ffffff] tip='E0F7 (arrow-thick-top-right-fill)' cmd=command.copy('\uE0F7'))
item(image=["\uE0F8", #ffffff] tip='E0F8 (arrow-thick-top-right-line-acute)' cmd=command.copy('\uE0F8') col)

item(image=["\uE0F9", #ffffff] tip='E0F9 (arrow-thick-top-right-line-large)' cmd=command.copy('\uE0F9'))
item(image=["\uE0FA", #ffffff] tip='E0FA (arrow-thick-top-right-line-large)' cmd=command.copy('\uE0FA'))
item(image=["\uE0FB", #ffffff] tip='E0FB (arrow-thick-top-right-line-large)' cmd=command.copy('\uE0FB'))
item(image=["\uE0FC", #ffffff] tip='E0FC (arrow-thick-top-right-line-obliq)' cmd=command.copy('\uE0FC'))
item(image=["\uE0FD", #ffffff] tip='E0FD (arrow-thick-top-right-line)' cmd=command.copy('\uE0FD'))
item(image=["\uE0FE", #ffffff] tip='E0FE (arrow-top-fill-acute)' cmd=command.copy('\uE0FE'))
item(image=["\uE0FF", #ffffff] tip='E0FF (arrow-top-fill-angled)' cmd=command.copy('\uE0FF'))
item(image=["\uE100", #ffffff] tip='E100 (arrow-top-fill-large-acute)' cmd=command.copy('\uE100'))
item(image=["\uE101", #ffffff] tip='E101 (arrow-top-fill-large-angled)' cmd=command.copy('\uE101'))
item(image=["\uE102", #ffffff] tip='E102 (arrow-top-fill-large-oblique)' cmd=command.copy('\uE102'))

item(image=["\uE103", #ffffff] tip='E103 (arrow-top-fill-large)' cmd=command.copy('\uE103'))
item(image=["\uE104", #ffffff] tip='E104 (arrow-top-fill-oblique)' cmd=command.copy('\uE104'))
item(image=["\uE105", #ffffff] tip='E105 (arrow-top-fill)' cmd=command.copy('\uE105'))
item(image=["\uE106", #ffffff] tip='E106 (arrow-top-left-fill-acute)' cmd=command.copy('\uE106'))
item(image=["\uE107", #ffffff] tip='E107 (arrow-top-left-fill-angled)' cmd=command.copy('\uE107'))
item(image=["\uE108", #ffffff] tip='E108 (arrow-top-left-fill-large-acute)' cmd=command.copy('\uE108'))
item(image=["\uE109", #ffffff] tip='E109 (arrow-top-left-fill-large-angled)' cmd=command.copy('\uE109'))
item(image=["\uE10A", #ffffff] tip='E10A (arrow-top-left-fill-large-obliqu)' cmd=command.copy('\uE10A'))
item(image=["\uE10B", #ffffff] tip='E10B (arrow-top-left-fill-large)' cmd=command.copy('\uE10B'))
item(image=["\uE10C", #ffffff] tip='E10C (arrow-top-left-fill-oblique)' cmd=command.copy('\uE10C') col)

item(image=["\uE10D", #ffffff] tip='E10D (arrow-top-left-fill)' cmd=command.copy('\uE10D'))
item(image=["\uE10E", #ffffff] tip='E10E (arrow-top-left-line-acute)' cmd=command.copy('\uE10E'))
item(image=["\uE10F", #ffffff] tip='E10F (arrow-top-left-line-large-acute)' cmd=command.copy('\uE10F'))
item(image=["\uE110", #ffffff] tip='E110 (arrow-top-left-line-large-obliqu)' cmd=command.copy('\uE110'))
item(image=["\uE111", #ffffff] tip='E111 (arrow-top-left-line-large)' cmd=command.copy('\uE111'))
item(image=["\uE112", #ffffff] tip='E112 (arrow-top-left-line-oblique)' cmd=command.copy('\uE112'))
item(image=["\uE113", #ffffff] tip='E113 (arrow-top-left-line)' cmd=command.copy('\uE113'))
item(image=["\uE114", #ffffff] tip='E114 (arrow-top-line-acute)' cmd=command.copy('\uE114'))
item(image=["\uE115", #ffffff] tip='E115 (arrow-top-line-large-acute)' cmd=command.copy('\uE115'))
item(image=["\uE116", #ffffff] tip='E116 (arrow-top-line-large-oblique)' cmd=command.copy('\uE116'))

item(image=["\uE117", #ffffff] tip='E117 (arrow-top-line-large)' cmd=command.copy('\uE117'))
item(image=["\uE118", #ffffff] tip='E118 (arrow-top-line-oblique)' cmd=command.copy('\uE118'))
item(image=["\uE119", #ffffff] tip='E119 (arrow-top-line)' cmd=command.copy('\uE119'))
item(image=["\uE11A", #ffffff] tip='E11A (arrow-top-right-fill-acute)' cmd=command.copy('\uE11A'))
item(image=["\uE11B", #ffffff] tip='E11B (arrow-top-right-fill-angled)' cmd=command.copy('\uE11B'))
item(image=["\uE11C", #ffffff] tip='E11C (arrow-top-right-fill-large-acute)' cmd=command.copy('\uE11C'))
item(image=["\uE11D", #ffffff] tip='E11D (arrow-top-right-fill-large-angle)' cmd=command.copy('\uE11D'))
item(image=["\uE11E", #ffffff] tip='E11E (arrow-top-right-fill-large-obliq)' cmd=command.copy('\uE11E'))
item(image=["\uE11F", #ffffff] tip='E11F (arrow-top-right-fill-large)' cmd=command.copy('\uE11F'))
item(image=["\uE120", #ffffff] tip='E120 (arrow-top-right-fill-oblique)' cmd=command.copy('\uE120') col)

item(image=["\uE121", #ffffff] tip='E121 (arrow-top-right-fill)' cmd=command.copy('\uE121'))
item(image=["\uE122", #ffffff] tip='E122 (arrow-top-right-line-acute)' cmd=command.copy('\uE122'))
item(image=["\uE123", #ffffff] tip='E123 (arrow-top-right-line-large-acute)' cmd=command.copy('\uE123'))
item(image=["\uE124", #ffffff] tip='E124 (arrow-top-right-line-large-obliq)' cmd=command.copy('\uE124'))
item(image=["\uE125", #ffffff] tip='E125 (arrow-top-right-line-large)' cmd=command.copy('\uE125'))
item(image=["\uE126", #ffffff] tip='E126 (arrow-top-right-line-oblique)' cmd=command.copy('\uE126'))
item(image=["\uE127", #ffffff] tip='E127 (arrow-top-right-line)' cmd=command.copy('\uE127'))
item(image=["\uE128", #ffffff] tip='E128 (audio-spectrum)' cmd=command.copy('\uE128'))
item(image=["\uE129", #ffffff] tip='E129 (ban)' cmd=command.copy('\uE129'))
item(image=["\uE12A", #ffffff] tip='E12A (bar-chart)' cmd=command.copy('\uE12A'))

item(image=["\uE12B", #ffffff] tip='E12B (basket)' cmd=command.copy('\uE12B'))
item(image=["\uE12C", #ffffff] tip='E12C (battery)' cmd=command.copy('\uE12C'))
item(image=["\uE12D", #ffffff] tip='E12D (beaker)' cmd=command.copy('\uE12D'))
item(image=["\uE12E", #ffffff] tip='E12E (bell)' cmd=command.copy('\uE12E'))
item(image=["\uE12F", #ffffff] tip='E12F (bitcoin-address)' cmd=command.copy('\uE12F'))
item(image=["\uE130", #ffffff] tip='E130 (bitcoin-block)' cmd=command.copy('\uE130'))
item(image=["\uE131", #ffffff] tip='E131 (bitcoin)' cmd=command.copy('\uE131'))
item(image=["\uE132", #ffffff] tip='E132 (bitcoin-transaction)' cmd=command.copy('\uE132'))
item(image=["\uE133", #ffffff] tip='E133 (bluetooth)' cmd=command.copy('\uE133'))
item(image=["\uE134", #ffffff] tip='E134 (bold)' cmd=command.copy('\uE134') col)

item(image=["\uE135", #ffffff] tip='E135 (book)' cmd=command.copy('\uE135'))
item(image=["\uE136", #ffffff] tip='E136 (bookmark)' cmd=command.copy('\uE136'))
item(image=["\uE137", #ffffff] tip='E137 (box)' cmd=command.copy('\uE137'))
item(image=["\uE138", #ffffff] tip='E138 (brain)' cmd=command.copy('\uE138'))
item(image=["\uE139", #ffffff] tip='E139 (briefcase)' cmd=command.copy('\uE139'))
item(image=["\uE13A", #ffffff] tip='E13A (british-pound)' cmd=command.copy('\uE13A'))
item(image=["\uE13B", #ffffff] tip='E13B (browser)' cmd=command.copy('\uE13B'))
item(image=["\uE13C", #ffffff] tip='E13C (brush)' cmd=command.copy('\uE13C'))
item(image=["\uE13D", #ffffff] tip='E13D (bug)' cmd=command.copy('\uE13D'))
item(image=["\uE13E", #ffffff] tip='E13E (bullhorn)' cmd=command.copy('\uE13E'))

item(image=["\uE13F", #ffffff] tip='E13F (calculator)' cmd=command.copy('\uE13F'))
item(image=["\uE140", #ffffff] tip='E140 (calendar)' cmd=command.copy('\uE140'))
item(image=["\uE141", #ffffff] tip='E141 (camera-rangefinder)' cmd=command.copy('\uE141'))
item(image=["\uE142", #ffffff] tip='E142 (camera-slr)' cmd=command.copy('\uE142'))
item(image=["\uE143", #ffffff] tip='E143 (caret-bottom)' cmd=command.copy('\uE143'))
item(image=["\uE144", #ffffff] tip='E144 (caret-left)' cmd=command.copy('\uE144'))
item(image=["\uE145", #ffffff] tip='E145 (caret-right)' cmd=command.copy('\uE145'))
item(image=["\uE146", #ffffff] tip='E146 (caret-top)' cmd=command.copy('\uE146'))
item(image=["\uE147", #ffffff] tip='E147 (cart)' cmd=command.copy('\uE147'))
item(image=["\uE148", #ffffff] tip='E148 (chat)' cmd=command.copy('\uE148') col)

item(image=["\uE149", #ffffff] tip='E149 (check)' cmd=command.copy('\uE149'))
item(image=["\uE14A", #ffffff] tip='E14A (chevron-bottom)' cmd=command.copy('\uE14A'))
item(image=["\uE14B", #ffffff] tip='E14B (chevron-left)' cmd=command.copy('\uE14B'))
item(image=["\uE14C", #ffffff] tip='E14C (chevron-right)' cmd=command.copy('\uE14C'))
item(image=["\uE14D", #ffffff] tip='E14D (chevron-top)' cmd=command.copy('\uE14D'))
item(image=["\uE14E", #ffffff] tip='E14E (circle-check)' cmd=command.copy('\uE14E'))
item(image=["\uE14F", #ffffff] tip='E14F (circle-x)' cmd=command.copy('\uE14F'))
item(image=["\uE150", #ffffff] tip='E150 (circular-loop)' cmd=command.copy('\uE150'))
item(image=["\uE151", #ffffff] tip='E151 (clock)' cmd=command.copy('\uE151'))
item(image=["\uE152", #ffffff] tip='E152 (cloud)' cmd=command.copy('\uE152'))

item(image=["\uE153", #ffffff] tip='E153 (cloud-transfer-download)' cmd=command.copy('\uE153'))
item(image=["\uE154", #ffffff] tip='E154 (cloud-transfer-upload)' cmd=command.copy('\uE154'))
item(image=["\uE155", #ffffff] tip='E155 (cloudy)' cmd=command.copy('\uE155'))
item(image=["\uE156", #ffffff] tip='E156 (code)' cmd=command.copy('\uE156'))
item(image=["\uE157", #ffffff] tip='E157 (cog)' cmd=command.copy('\uE157'))
item(image=["\uE158", #ffffff] tip='E158 (collapse-down)' cmd=command.copy('\uE158'))
item(image=["\uE159", #ffffff] tip='E159 (collapse-left)' cmd=command.copy('\uE159'))
item(image=["\uE15A", #ffffff] tip='E15A (collapse-right)' cmd=command.copy('\uE15A'))
item(image=["\uE15B", #ffffff] tip='E15B (collapse-up)' cmd=command.copy('\uE15B'))
item(image=["\uE15C", #ffffff] tip='E15C (comment-square)' cmd=command.copy('\uE15C') col)

item(image=["\uE15D", #ffffff] tip='E15D (compass)' cmd=command.copy('\uE15D'))
item(image=["\uE15E", #ffffff] tip='E15E (contrast)' cmd=command.copy('\uE15E'))
item(image=["\uE15F", #ffffff] tip='E15F (copywriting)' cmd=command.copy('\uE15F'))
item(image=["\uE160", #ffffff] tip='E160 (credit-card)' cmd=command.copy('\uE160'))
item(image=["\uE161", #ffffff] tip='E161 (crop)' cmd=command.copy('\uE161'))
item(image=["\uE162", #ffffff] tip='E162 (dashboard)' cmd=command.copy('\uE162'))
item(image=["\uE163", #ffffff] tip='E163 (data-transfer-download)' cmd=command.copy('\uE163'))
item(image=["\uE164", #ffffff] tip='E164 (data-transfer-upload)' cmd=command.copy('\uE164'))
item(image=["\uE165", #ffffff] tip='E165 (delete)' cmd=command.copy('\uE165'))
item(image=["\uE166", #ffffff] tip='E166 (dial)' cmd=command.copy('\uE166'))

item(image=["\uE167", #ffffff] tip='E167 (document)' cmd=command.copy('\uE167'))
item(image=["\uE168", #ffffff] tip='E168 (dollar)' cmd=command.copy('\uE168'))
item(image=["\uE169", #ffffff] tip='E169 (double-quote-sans-left)' cmd=command.copy('\uE169'))
item(image=["\uE16A", #ffffff] tip='E16A (double-quote-sans-right)' cmd=command.copy('\uE16A'))
item(image=["\uE16B", #ffffff] tip='E16B (double-quote-serif-left)' cmd=command.copy('\uE16B'))
item(image=["\uE16C", #ffffff] tip='E16C (double-quote-serif-right)' cmd=command.copy('\uE16C'))
item(image=["\uE16D", #ffffff] tip='E16D (eject)' cmd=command.copy('\uE16D'))
item(image=["\uE16E", #ffffff] tip='E16E (electric)' cmd=command.copy('\uE16E'))
item(image=["\uE16F", #ffffff] tip='E16F (ellipses)' cmd=command.copy('\uE16F'))
item(image=["\uE170", #ffffff] tip='E170 (envelope-closed)' cmd=command.copy('\uE170') col)

item(image=["\uE171", #ffffff] tip='E171 (envelope-open)' cmd=command.copy('\uE171'))
item(image=["\uE172", #ffffff] tip='E172 (euro)' cmd=command.copy('\uE172'))
item(image=["\uE173", #ffffff] tip='E173 (excerpt)' cmd=command.copy('\uE173'))
item(image=["\uE174", #ffffff] tip='E174 (expand-down)' cmd=command.copy('\uE174'))
item(image=["\uE175", #ffffff] tip='E175 (expand-left)' cmd=command.copy('\uE175'))
item(image=["\uE176", #ffffff] tip='E176 (expand-right)' cmd=command.copy('\uE176'))
item(image=["\uE177", #ffffff] tip='E177 (expand-up)' cmd=command.copy('\uE177'))
item(image=["\uE178", #ffffff] tip='E178 (eye)' cmd=command.copy('\uE178'))
item(image=["\uE179", #ffffff] tip='E179 (eyedropper)' cmd=command.copy('\uE179'))
item(image=["\uE17A", #ffffff] tip='E17A (file-ai)' cmd=command.copy('\uE17A'))

item(image=["\uE17B", #ffffff] tip='E17B (file-css)' cmd=command.copy('\uE17B'))
item(image=["\uE17C", #ffffff] tip='E17C (file-dmg)' cmd=command.copy('\uE17C'))
item(image=["\uE17D", #ffffff] tip='E17D (file-doc)' cmd=command.copy('\uE17D'))
item(image=["\uE17E", #ffffff] tip='E17E (file-gif)' cmd=command.copy('\uE17E'))
item(image=["\uE17F", #ffffff] tip='E17F (file-html)' cmd=command.copy('\uE17F'))
item(image=["\uE180", #ffffff] tip='E180 (file-jpg)' cmd=command.copy('\uE180'))
item(image=["\uE181", #ffffff] tip='E181 (file-js)' cmd=command.copy('\uE181'))
item(image=["\uE182", #ffffff] tip='E182 (file-json)' cmd=command.copy('\uE182'))
item(image=["\uE183", #ffffff] tip='E183 (file-mov)' cmd=command.copy('\uE183'))
item(image=["\uE184", #ffffff] tip='E184 (file-mp3)' cmd=command.copy('\uE184') col)

item(image=["\uE185", #ffffff] tip='E185 (file-pdf)' cmd=command.copy('\uE185'))
item(image=["\uE186", #ffffff] tip='E186 (file-png)' cmd=command.copy('\uE186'))
item(image=["\uE187", #ffffff] tip='E187 (file-psd)' cmd=command.copy('\uE187'))
item(image=["\uE188", #ffffff] tip='E188 (file-svg)' cmd=command.copy('\uE188'))
item(image=["\uE189", #ffffff] tip='E189 (file-txt)' cmd=command.copy('\uE189'))
item(image=["\uE18A", #ffffff] tip='E18A (file-xls)' cmd=command.copy('\uE18A'))
item(image=["\uE18B", #ffffff] tip='E18B (file-xml)' cmd=command.copy('\uE18B'))
item(image=["\uE18C", #ffffff] tip='E18C (file-zip)' cmd=command.copy('\uE18C'))
item(image=["\uE18D", #ffffff] tip='E18D (fire)' cmd=command.copy('\uE18D'))
item(image=["\uE18E", #ffffff] tip='E18E (firefly)' cmd=command.copy('\uE18E'))

item(image=["\uE18F", #ffffff] tip='E18F (flag)' cmd=command.copy('\uE18F'))
item(image=["\uE190", #ffffff] tip='E190 (flash)' cmd=command.copy('\uE190'))
item(image=["\uE191", #ffffff] tip='E191 (folder)' cmd=command.copy('\uE191'))
item(image=["\uE192", #ffffff] tip='E192 (fork)' cmd=command.copy('\uE192'))
item(image=["\uE193", #ffffff] tip='E193 (fullscreen-enter)' cmd=command.copy('\uE193'))
item(image=["\uE194", #ffffff] tip='E194 (fullscreen-exit)' cmd=command.copy('\uE194'))
item(image=["\uE195", #ffffff] tip='E195 (globe)' cmd=command.copy('\uE195'))
item(image=["\uE196", #ffffff] tip='E196 (graph)' cmd=command.copy('\uE196'))
item(image=["\uE197", #ffffff] tip='E197 (grid-four-up)' cmd=command.copy('\uE197'))
item(image=["\uE198", #ffffff] tip='E198 (grid-three-up)' cmd=command.copy('\uE198') col)

item(image=["\uE199", #ffffff] tip='E199 (grid-two-up)' cmd=command.copy('\uE199'))
item(image=["\uE19A", #ffffff] tip='E19A (guides)' cmd=command.copy('\uE19A'))
item(image=["\uE19B", #ffffff] tip='E19B (hard-drive)' cmd=command.copy('\uE19B'))
item(image=["\uE19C", #ffffff] tip='E19C (headphones)' cmd=command.copy('\uE19C'))
item(image=["\uE19D", #ffffff] tip='E19D (heart)' cmd=command.copy('\uE19D'))
item(image=["\uE19E", #ffffff] tip='E19E (home)' cmd=command.copy('\uE19E'))
item(image=["\uE19F", #ffffff] tip='E19F (image-landscape)' cmd=command.copy('\uE19F'))
item(image=["\uE1A0", #ffffff] tip='E1A0 (image-portrait)' cmd=command.copy('\uE1A0'))
item(image=["\uE1A1", #ffffff] tip='E1A1 (inbox)' cmd=command.copy('\uE1A1'))
item(image=["\uE1A2", #ffffff] tip='E1A2 (infinity)' cmd=command.copy('\uE1A2'))

item(image=["\uE1A3", #ffffff] tip='E1A3 (info)' cmd=command.copy('\uE1A3'))
item(image=["\uE1A4", #ffffff] tip='E1A4 (iphone)' cmd=command.copy('\uE1A4'))
item(image=["\uE1A5", #ffffff] tip='E1A5 (italic)' cmd=command.copy('\uE1A5'))
item(image=["\uE1A6", #ffffff] tip='E1A6 (justify-center)' cmd=command.copy('\uE1A6'))
item(image=["\uE1A7", #ffffff] tip='E1A7 (justify-left)' cmd=command.copy('\uE1A7'))
item(image=["\uE1A8", #ffffff] tip='E1A8 (justify-right)' cmd=command.copy('\uE1A8'))
item(image=["\uE1A9", #ffffff] tip='E1A9 (key)' cmd=command.copy('\uE1A9'))
item(image=["\uE1AA", #ffffff] tip='E1AA (keyboard)' cmd=command.copy('\uE1AA'))
item(image=["\uE1AB", #ffffff] tip='E1AB (laptop)' cmd=command.copy('\uE1AB'))
item(image=["\uE1AC", #ffffff] tip='E1AC (layers)' cmd=command.copy('\uE1AC') col)

item(image=["\uE1AD", #ffffff] tip='E1AD (lightbulb)' cmd=command.copy('\uE1AD'))
item(image=["\uE1AE", #ffffff] tip='E1AE (lightning-bolt)' cmd=command.copy('\uE1AE'))
item(image=["\uE1AF", #ffffff] tip='E1AF (lightning)' cmd=command.copy('\uE1AF'))
item(image=["\uE1B0", #ffffff] tip='E1B0 (link-broken)' cmd=command.copy('\uE1B0'))
item(image=["\uE1B1", #ffffff] tip='E1B1 (link-intact)' cmd=command.copy('\uE1B1'))
item(image=["\uE1B2", #ffffff] tip='E1B2 (list-rich)' cmd=command.copy('\uE1B2'))
item(image=["\uE1B3", #ffffff] tip='E1B3 (list)' cmd=command.copy('\uE1B3'))
item(image=["\uE1B4", #ffffff] tip='E1B4 (location)' cmd=command.copy('\uE1B4'))
item(image=["\uE1B5", #ffffff] tip='E1B5 (lock-locked)' cmd=command.copy('\uE1B5'))
item(image=["\uE1B6", #ffffff] tip='E1B6 (lock-unlocked)' cmd=command.copy('\uE1B6'))

item(image=["\uE1B7", #ffffff] tip='E1B7 (loop-circular)' cmd=command.copy('\uE1B7'))
item(image=["\uE1B8", #ffffff] tip='E1B8 (loop)' cmd=command.copy('\uE1B8'))
item(image=["\uE1B9", #ffffff] tip='E1B9 (loop-square)' cmd=command.copy('\uE1B9'))
item(image=["\uE1BA", #ffffff] tip='E1BA (magic-wand)' cmd=command.copy('\uE1BA'))
item(image=["\uE1BB", #ffffff] tip='E1BB (magnifying-glass)' cmd=command.copy('\uE1BB'))
item(image=["\uE1BC", #ffffff] tip='E1BC (map-marker)' cmd=command.copy('\uE1BC'))
item(image=["\uE1BD", #ffffff] tip='E1BD (map)' cmd=command.copy('\uE1BD'))
item(image=["\uE1BE", #ffffff] tip='E1BE (medal)' cmd=command.copy('\uE1BE'))
item(image=["\uE1BF", #ffffff] tip='E1BF (media-pause)' cmd=command.copy('\uE1BF'))
item(image=["\uE1C0", #ffffff] tip='E1C0 (media-play-circle)' cmd=command.copy('\uE1C0') col)

item(image=["\uE1C1", #ffffff] tip='E1C1 (media-play)' cmd=command.copy('\uE1C1'))
item(image=["\uE1C2", #ffffff] tip='E1C2 (media-record)' cmd=command.copy('\uE1C2'))
item(image=["\uE1C3", #ffffff] tip='E1C3 (media-skip-backward)' cmd=command.copy('\uE1C3'))
item(image=["\uE1C4", #ffffff] tip='E1C4 (media-skip-forward)' cmd=command.copy('\uE1C4'))
item(image=["\uE1C5", #ffffff] tip='E1C5 (media-step-backward)' cmd=command.copy('\uE1C5'))
item(image=["\uE1C6", #ffffff] tip='E1C6 (media-step-forward)' cmd=command.copy('\uE1C6'))
item(image=["\uE1C7", #ffffff] tip='E1C7 (media-stop)' cmd=command.copy('\uE1C7'))
item(image=["\uE1C8", #ffffff] tip='E1C8 (medical-cross)' cmd=command.copy('\uE1C8'))
item(image=["\uE1C9", #ffffff] tip='E1C9 (menu)' cmd=command.copy('\uE1C9'))
item(image=["\uE1CA", #ffffff] tip='E1CA (microphone)' cmd=command.copy('\uE1CA'))

item(image=["\uE1CB", #ffffff] tip='E1CB (minus)' cmd=command.copy('\uE1CB'))
item(image=["\uE1CC", #ffffff] tip='E1CC (monitor)' cmd=command.copy('\uE1CC'))
item(image=["\uE1CD", #ffffff] tip='E1CD (moon)' cmd=command.copy('\uE1CD'))
item(image=["\uE1CE", #ffffff] tip='E1CE (move)' cmd=command.copy('\uE1CE'))
item(image=["\uE1CF", #ffffff] tip='E1CF (musical-note)' cmd=command.copy('\uE1CF'))
item(image=["\uE1D0", #ffffff] tip='E1D0 (nexus)' cmd=command.copy('\uE1D0'))
item(image=["\uE1D1", #ffffff] tip='E1D1 (paperclip)' cmd=command.copy('\uE1D1'))
item(image=["\uE1D2", #ffffff] tip='E1D2 (pen)' cmd=command.copy('\uE1D2'))
item(image=["\uE1D3", #ffffff] tip='E1D3 (pencil)' cmd=command.copy('\uE1D3'))
item(image=["\uE1D4", #ffffff] tip='E1D4 (people)' cmd=command.copy('\uE1D4') col)

item(image=["\uE1D5", #ffffff] tip='E1D5 (person-female)' cmd=command.copy('\uE1D5'))
item(image=["\uE1D6", #ffffff] tip='E1D6 (person-genderless)' cmd=command.copy('\uE1D6'))
item(image=["\uE1D7", #ffffff] tip='E1D7 (person-male)' cmd=command.copy('\uE1D7'))
item(image=["\uE1D8", #ffffff] tip='E1D8 (pie-chart)' cmd=command.copy('\uE1D8'))
item(image=["\uE1D9", #ffffff] tip='E1D9 (pin)' cmd=command.copy('\uE1D9'))
item(image=["\uE1DA", #ffffff] tip='E1DA (plus)' cmd=command.copy('\uE1DA'))
item(image=["\uE1DB", #ffffff] tip='E1DB (power-standby)' cmd=command.copy('\uE1DB'))
item(image=["\uE1DC", #ffffff] tip='E1DC (print)' cmd=command.copy('\uE1DC'))
item(image=["\uE1DD", #ffffff] tip='E1DD (project)' cmd=command.copy('\uE1DD'))
item(image=["\uE1DE", #ffffff] tip='E1DE (pulse)' cmd=command.copy('\uE1DE'))

item(image=["\uE1DF", #ffffff] tip='E1DF (rainy)' cmd=command.copy('\uE1DF'))
item(image=["\uE1E0", #ffffff] tip='E1E0 (random)' cmd=command.copy('\uE1E0'))
item(image=["\uE1E1", #ffffff] tip='E1E1 (reload)' cmd=command.copy('\uE1E1'))
item(image=["\uE1E2", #ffffff] tip='E1E2 (resize-both)' cmd=command.copy('\uE1E2'))
item(image=["\uE1E3", #ffffff] tip='E1E3 (resize-height)' cmd=command.copy('\uE1E3'))
item(image=["\uE1E4", #ffffff] tip='E1E4 (resize-width)' cmd=command.copy('\uE1E4'))
item(image=["\uE1E5", #ffffff] tip='E1E5 (rss-alt)' cmd=command.copy('\uE1E5'))
item(image=["\uE1E6", #ffffff] tip='E1E6 (rss)' cmd=command.copy('\uE1E6'))
item(image=["\uE1E7", #ffffff] tip='E1E7 (screwdriver)' cmd=command.copy('\uE1E7'))
item(image=["\uE1E8", #ffffff] tip='E1E8 (script)' cmd=command.copy('\uE1E8') col)

item(image=["\uE1E9", #ffffff] tip='E1E9 (settings)' cmd=command.copy('\uE1E9'))
item(image=["\uE1EA", #ffffff] tip='E1EA (share-boxed)' cmd=command.copy('\uE1EA'))
item(image=["\uE1EB", #ffffff] tip='E1EB (share)' cmd=command.copy('\uE1EB'))
item(image=["\uE1EC", #ffffff] tip='E1EC (shield)' cmd=command.copy('\uE1EC'))
item(image=["\uE1ED", #ffffff] tip='E1ED (signal)' cmd=command.copy('\uE1ED'))
item(image=["\uE1EE", #ffffff] tip='E1EE (signpost)' cmd=command.copy('\uE1EE'))
item(image=["\uE1EF", #ffffff] tip='E1EF (sort-ascending)' cmd=command.copy('\uE1EF'))
item(image=["\uE1F0", #ffffff] tip='E1F0 (sort-descending)' cmd=command.copy('\uE1F0'))
item(image=["\uE1F1", #ffffff] tip='E1F1 (spreadsheet)' cmd=command.copy('\uE1F1'))
item(image=["\uE1F2", #ffffff] tip='E1F2 (star)' cmd=command.copy('\uE1F2'))

item(image=["\uE1F3", #ffffff] tip='E1F3 (sun)' cmd=command.copy('\uE1F3'))
item(image=["\uE1F4", #ffffff] tip='E1F4 (tablet)' cmd=command.copy('\uE1F4'))
item(image=["\uE1F5", #ffffff] tip='E1F5 (tag)' cmd=command.copy('\uE1F5'))
item(image=["\uE1F6", #ffffff] tip='E1F6 (tags)' cmd=command.copy('\uE1F6'))
item(image=["\uE1F7", #ffffff] tip='E1F7 (task)' cmd=command.copy('\uE1F7'))
item(image=["\uE1F8", #ffffff] tip='E1F8 (terminal)' cmd=command.copy('\uE1F8'))
item(image=["\uE1F9", #ffffff] tip='E1F9 (text)' cmd=command.copy('\uE1F9'))
item(image=["\uE1FA", #ffffff] tip='E1FA (thermometer)' cmd=command.copy('\uE1FA'))
item(image=["\uE1FB", #ffffff] tip='E1FB (thumb-down)' cmd=command.copy('\uE1FB'))
item(image=["\uE1FC", #ffffff] tip='E1FC (thumb-up)' cmd=command.copy('\uE1FC') col)

item(image=["\uE1FD", #ffffff] tip='E1FD (tiara)' cmd=command.copy('\uE1FD'))
item(image=["\uE1FE", #ffffff] tip='E1FE (timer)' cmd=command.copy('\uE1FE'))
item(image=["\uE1FF", #ffffff] tip='E1FF (tint)' cmd=command.copy('\uE1FF'))
item(image=["\uE200", #ffffff] tip='E200 (tools)' cmd=command.copy('\uE200'))
item(image=["\uE201", #ffffff] tip='E201 (transfer)' cmd=command.copy('\uE201'))
item(image=["\uE202", #ffffff] tip='E202 (trash)' cmd=command.copy('\uE202'))
item(image=["\uE203", #ffffff] tip='E203 (vertical-align-bottom)' cmd=command.copy('\uE203'))
item(image=["\uE204", #ffffff] tip='E204 (vertical-align-center)' cmd=command.copy('\uE204'))
item(image=["\uE205", #ffffff] tip='E205 (vertical-align-top)' cmd=command.copy('\uE205'))
item(image=["\uE206", #ffffff] tip='E206 (video)' cmd=command.copy('\uE206'))

item(image=["\uE207", #ffffff] tip='E207 (volume-high)' cmd=command.copy('\uE207'))
item(image=["\uE208", #ffffff] tip='E208 (volume-low)' cmd=command.copy('\uE208'))
item(image=["\uE209", #ffffff] tip='E209 (volume-medium)' cmd=command.copy('\uE209'))
item(image=["\uE20A", #ffffff] tip='E20A (volume-off)' cmd=command.copy('\uE20A'))
item(image=["\uE20B", #ffffff] tip='E20B (warning)' cmd=command.copy('\uE20B'))
item(image=["\uE20C", #ffffff] tip='E20C (wifi)' cmd=command.copy('\uE20C'))
item(image=["\uE20D", #ffffff] tip='E20D (wrench)' cmd=command.copy('\uE20D'))
item(image=["\uE20E", #ffffff] tip='E20E (x)' cmd=command.copy('\uE20E'))
item(image=["\uE20F", #ffffff] tip='E20F (yen)' cmd=command.copy('\uE20F'))
item(image=["\uE210", #ffffff] tip='E210 (zoom-in)' cmd=command.copy('\uE210'))
item(image=["\uE211", #ffffff] tip='E211 (zoom-out)' cmd=command.copy('\uE211'))


        // item(image=icon.glyph(\uE002,default_icon_size)                 tip=['E002']                                cmd=command.copy('\uE002'))
        // item(image=icon.glyph(\uE003,default_icon_size)                  tip=['E003']                                cmd=command.copy('\uE003'))
        // item(image=icon.glyph(\uE004,default_icon_size)                  tip=['E004']                                cmd=command.copy('\uE004'))
        // item(image=icon.glyph(\uE005,default_icon_size)                  tip=['E005']                                cmd=command.copy('\uE005'))
        // item(image=icon.glyph(\uE006,default_icon_size)                  tip=['E006']                                cmd=command.copy('\uE006'))
        // item(image=icon.glyph(\uE007,default_icon_size)                  tip=['E007']                                cmd=command.copy('\uE007'))
        // item(image=icon.glyph(\uE008,default_icon_size)                  tip=['E008']                                cmd=command.copy('\uE008'))
        // item(image=icon.glyph(\uE009,default_icon_size)                  tip=['E009']                                cmd=command.copy('\uE009'))
        // item(image=icon.glyph(\uE00A,default_icon_size)                  tip=['E00A']                                cmd=command.copy('\uE00A'))
        // item(image=icon.glyph(\uE00B,default_icon_size)                  tip=['E00B']


}