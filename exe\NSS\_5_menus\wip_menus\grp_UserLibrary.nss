
// ->
menu(title="&User Library" type='Taskbar|Desktop|Drive|Back.Dir|Back.Drive' image=[E0FF,SOFT] image-sel=[E0FF,HOVER] sep='None') {

    // -> applications
    import '@app.dir/NSS/_5_menus/wip_menus/mnu_AppLibrary.nss'
    separator()

    // -> icons
    import '@app.dir/NSS/_5_menus/wip_menus/mnu_IconLibrary.nss'
    separator()

    // -> for debug
    import '@app.dir/NSS/_5_menus/wip_menus/mnu_Debugging.nss'
    separator()

    // -> unsorted
    import '@app.dir/NSS/_5_menus/wip_menus/mnu_TMP.nss'

}
