
//
$APP_USER_WINEXPLORER_DIR = '@user.desktop\my\flow\home\__GOTO__\Apps\grp_nirsoft\app_winexplorer\exe'
$APP_USER_WINEXPLORER_EXE = '@APP_USER_WINEXPLORER_DIR\winexp.exe'
$APP_USER_WINEXPLORER_TIP = "..."+str.trimstart('@APP_USER_WINEXPLORER_EXE','@app.dir')

// -> WinExplorer
item(
    title="&WinExplorer"
    keys="exe"
    type='Taskbar|File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    //
    image=APP_USER_WINEXPLORER_EXE
    tip=[APP_USER_WINEXPLORER_TIP,TIP3,0.5]
    //
    admin=keys.rbutton()
    cmd=if(KEYS_EXE_OPEN_EXE,(APP_USER_WINEXPLORER_EXE))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set(APP_USER_WINEXPLORER_DIR)),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set(APP_USER_WINEXPLORER_EXE)),
        cmd=if(KEYS_EXE_OPEN_DIR,(APP_USER_WINEXPLORER_DIR)),
    }
)
