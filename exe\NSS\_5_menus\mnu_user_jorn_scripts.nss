
// optional redirect for venv path
$VENV_PYTHON_EXE = path.full('@app.dir/PORTAL/PY/venv/Scripts/python.exe')

// ->
// menu(title="&Utils" type='Taskbar|Desktop|File|Dir|Drive|Back.Dir|Back.Drive' image=[E1D7,PURPLE] image-sel=[E1D7,HOVER] sep='None') {
// menu(title="&User: Scripts" type='Taskbar|Desktop|File|Dir|Drive|Back.Dir|Back.Drive' image=["\uE122",PURPLE] image-sel=["\uE123",HOVER] sep='None') {
menu(title='&Scripts@"\t"Jorn' type='Titlebar|Taskbar|Desktop|File|Dir|Drive|Back.Dir|Back.Drive' image=["\uE122",PURPLE] image-sel=["\uE123",HOVER] sep='None') {

    item(title="Python Utils" keys="" image=[E230,PURPLE] vis='Static' sep='both' col)

    //
    import '@app.dir/NSS/_3_items/itm_py_audioandvideocombiner.nss'
    import '@app.dir/NSS/_3_items/itm_py_audiofromvideoextractor.nss'
    import '@app.dir/NSS/_3_items/itm_py_bookmarkfolderizer.nss'
    import '@app.dir/NSS/_3_items/itm_py_closeduplicatewindows.nss'
    import '@app.dir/NSS/_3_items/itm_py_consolidatelogs.nss'
    import '@app.dir/NSS/_3_items/itm_py_directorycleaner.nss'
    import '@app.dir/NSS/_3_items/itm_py_dirtreegenerator.nss'
    import '@app.dir/NSS/_3_items/itm_py_environmentvariablesmanager.nss'
    import '@app.dir/NSS/_3_items/itm_py_markdowngenerator.nss'
    import '@app.dir/NSS/_3_items/itm_py_pinterestdownloader.nss'
    import '@app.dir/NSS/_3_items/itm_py_projectgenerator.nss'
    import '@app.dir/NSS/_3_items/itm_py_renamewitheditor.nss'
    import '@app.dir/NSS/_3_items/itm_py_sanitizefilenames.nss'
    import '@app.dir/NSS/_3_items/itm_py_speechtotext.nss'
    import '@app.dir/NSS/_3_items/itm_py_sshkeymanager.nss'
    import '@app.dir/NSS/_3_items/itm_py_urlgenerator.nss'
    import '@app.dir/NSS/_3_items/itm_py_videocompressor.nss'
    import '@app.dir/NSS/_3_items/itm_py_videosplitter.nss'
    import '@app.dir/NSS/_3_items/itm_py_windowtiler.nss'
    import '@app.dir/NSS/_3_items/itm_py_windowutils.nss'
    import '@app.dir/NSS/_3_items/itm_py_youtubedownloader.nss'
    import '@app.dir/NSS/_3_items/itm_py_youtubeplaylistexporter.nss'
    import '@app.dir/NSS/_3_items/itm_py_youtubeplaylistmanager.nss'
    separator()
    import '@app.dir/NSS/_3_items/itm_py_gitsizeanalyzer.nss'
    import '@app.dir/NSS/_3_items/itm_py_gitfilterrepo.nss'
    separator()
    import '@app.dir/NSS/_3_items/itm_py_win4ro1.nss'
    separator()

    //
    item(title="Python Misc" keys="" image=[E230,PURPLE] vis='Static' sep='both' /*col*/)
    item(title="&visualize_directory_tree.py"         type='Taskbar|Desktop|File|Dir|Drive|Back.Dir|Back.Drive' keys="py" image=[E17C,PURPLE] image-sel=[E17C,HOVER] cmd='@VENV_PYTHON_EXE' args='@py_visualize_dir_view' window='Hidden' sep='None')
    item(title="&close_duplicate_explorer_windows.py" type='Taskbar|Desktop|File|Dir|Drive|Back.Dir|Back.Drive' keys="py" image=[E17C,PURPLE] image-sel=[E17C,HOVER] cmd='@VENV_PYTHON_EXE' args='@py_close_duplicate_explorer_windows' window='Hidden' sep='None')
    item(title="&WindowManager"                       type='Taskbar|Desktop|File|Dir|Drive|Back.Dir|Back.Drive' keys="py" image=[E17C,PURPLE] image-sel=[E17C,HOVER] cmd='@VENV_PYTHON_EXE' args='@py_WindowManager_cmd' tip=py_WindowManager_cmd window='Visible' sep='None')
    separator()

    item(title="System" keys="" image=[E26D,WHITE1] vis='Static' sep='both' /*col*/)
    item(title="CMD" image=[E17C,GREY] vis='Static' sep='both')
    item(title="&Ping Web" type='Taskbar|Desktop|File|Dir|Drive|Back.Dir|Back.Drive' keys="cmd" image=[E17C,WHITE] image-sel=[E17C,HOVER] cmd-line='/c (TITLE ^(cmd:ping:@sys.datetime("H.M")^)) & ping ************* -t' window='Visible' sep='None')
    separator()


}




// item(title="&Explore in Everything" image=[E157,GREEN] image-sel=[E158,GREEN] cmd='"@exe_everything"' args='-search "EE: EX1: AND EX2: AND EX3: AND EX4:" -filter " ├─ Sorted: Modified" -explore "@sel.dir"'  sep='Both')

// menu(title="&Everything" type='Desktop|Dir|Drive|Back.Dir|Back.Drive|File' image=[E157,YELLOW] image-sel=[E158,BLUE]   sep='Both')
// {
//     item(title="&Modified: 24 hours"       keys="*" image=[E157,BLUE]  image-sel=[E158,ORANGE] cmd='"@exe_everything"' args='-search "dm:last24hours" -filter " ┌─ (Mix) Sorted: Modified" -explore "@sel.dir"')
//     item(title="&Modified: 30 days"       keys="*" image=[E157,BLUE]  image-sel=[E158,ORANGE] cmd='"@exe_everything"' args='-search "dm:last30days" -filter " ┌─ (Mix) Sorted: Modified" -explore "@sel.dir"')
//     item(title="&Modified: 52 weeks"       keys="*" image=[E157,BLUE]  image-sel=[E158,ORANGE] cmd='"@exe_everything"' args='-search "dm:last52weeks" -filter " ┌─ (Mix) Sorted: Modified" -explore "@sel.dir"')
//     separator


//     item(title="&(Mix) Sorted: Modified"       keys="0" image=[E157,BLUE]  image-sel=[E158,ORANGE] cmd='"@exe_everything"' args='-search "!file:" -filter " ┌─ (Mix) Sorted: Modified" -explore "@sel.dir"')
//     item(title="&(Mix) Sorted: Accessed"       keys="0" image=[E157,BLUE]  image-sel=[E158,ORANGE] cmd='"@exe_everything"' args='-search "!file:" -filter " ├─ (Mix) Sorted: Accessed" -explore "@sel.dir"')
//     item(title="&(Mix) Sorted: Size"           keys="0" image=[E157,BLUE]  image-sel=[E158,ORANGE] cmd='"@exe_everything"' args='-search "!file:" -filter " └─ (Mix) Sorted: Size" -explore "@sel.dir"')

// }






//     // item(title="py_execute" image=[E157,RED] image-sel=[E158,ORANGE] Window='Hidden' cmd='cmd.exe' args='/K python "@dir_py/utils/visualize_directory_tree.py" "@sel.dir"')
//     item(title="py_execute" image=[E157,RED] image-sel=[E158,ORANGE] Window='Hidden' cmd-line='/K python "@dir_py/utils/visualize_directory_tree.py" "@sel.dir"')

// /* menu */

//     item(title="py_execute" image=[E157,RED] cmd-line='python "@dir_py/utils/visualize_directory_tree.py" "@sel.dir"')
//      args='-search "dm:last24hours" -filter " ┌─ (Mix) Sorted: Modified" -explore "@sel.dir"')

// item(title="&Explore in Everything" image=[E157,GREEN] image-sel=[E158,GREEN] cmd='"@exe_everything"' args='-search "EE: EX1: AND EX2: AND EX3: AND EX4:" -filter " ├─ Sorted: Modified" -explore "@sel.dir"'  sep='Both')

// menu(title="&Everything" type='Desktop|Dir|Drive|Back.Dir|Back.Drive|File' image=[E157,YELLOW] image-sel=[E158,BLUE]   sep='Both')
// {
//     item(title="&Modified: 24 hours"       keys="*" image=[E157,BLUE]  image-sel=[E158,ORANGE] cmd='"@exe_everything"' args='-search "dm:last24hours" -filter " ┌─ (Mix) Sorted: Modified" -explore "@sel.dir"')
//     item(title="&Modified: 30 days"       keys="*" image=[E157,BLUE]  image-sel=[E158,ORANGE] cmd='"@exe_everything"' args='-search "dm:last30days" -filter " ┌─ (Mix) Sorted: Modified" -explore "@sel.dir"')
//     item(title="&Modified: 52 weeks"       keys="*" image=[E157,BLUE]  image-sel=[E158,ORANGE] cmd='"@exe_everything"' args='-search "dm:last52weeks" -filter " ┌─ (Mix) Sorted: Modified" -explore "@sel.dir"')
//     separator


//     item(title="&(Mix) Sorted: Modified"       keys="0" image=[E157,BLUE]  image-sel=[E158,ORANGE] cmd='"@exe_everything"' args='-search "!file:" -filter " ┌─ (Mix) Sorted: Modified" -explore "@sel.dir"')
//     item(title="&(Mix) Sorted: Accessed"       keys="0" image=[E157,BLUE]  image-sel=[E158,ORANGE] cmd='"@exe_everything"' args='-search "!file:" -filter " ├─ (Mix) Sorted: Accessed" -explore "@sel.dir"')
//     item(title="&(Mix) Sorted: Size"           keys="0" image=[E157,BLUE]  image-sel=[E158,ORANGE] cmd='"@exe_everything"' args='-search "!file:" -filter " └─ (Mix) Sorted: Size" -explore "@sel.dir"')
//     // item(title="&Explore in Everything"       keys="*" image=[E19D,WHITE]  image-sel=[E19D,HOVER] cmd='"@exe_everything"' args='-explore "@sel.dir"')
//     // item(title="Everything here"      image=[E19D,WHITE]  image-sel=[E19D,HOVER] cmd='"@exe_everything"' args='-explore "@sel.dir"')
//     separator
//     // item(title="*"       keys="Everything: Overview" image=[E19D,WHITE]  image-sel=[E19D,HOVER] cmd='"@exe_everything"' args='-explore "@sel.dir"')
//     //
//     // item(title="Overview"   keys="*.*" image=[E0E7,GREEN]  image-sel=[E0E7,HOVER] cmd-ps=ps1_get_location  type='File'                where=sel.count==1 window='Hidden' sep='None')
//     // item(title="&Overview"       keys="/*" image=[E19D,WHITE]  image-sel=[E19D,HOVER] cmd-line='/K @everything_exe -explore "@sel.dir"')
//     // item(title="&Overview"       keys="/*" image=[E19D,WHITE]  image-sel=[E19D,HOVER] cmd='@everything_exe' args='-explore "@sel.dir"')
//     // item(title="&Overview"       keys="/*" image=[E19D,WHITE]  image-sel=[E19D,HOVER] command='"@everything_exe"' args='-explore "@sel.dir"')
//     // item(title="Directories"   keys="/" image=[E0E7,GREEN]  image-sel=[E0E7,HOVER] cmd-ps=ps1_get_location  type='File'                where=sel.count==1 window='Hidden' sep='None')
//     // item(title="Recent"   keys="files" image=[E0E7,GREEN]  image-sel=[E0E7,HOVER] cmd-ps=ps1_get_location  type='File'                where=sel.count==1 window='Hidden' sep='None')
//     // item(title="Content"   keys="text" image=[E0E7,GREEN]  image-sel=[E0E7,HOVER] cmd-ps=ps1_get_location  type='File'                where=sel.count==1 window='Hidden' sep='None')
//     // item(title="Directories"   keys="/" image=[E0E7,GREEN]  image-sel=[E0E7,HOVER] cmd-ps=ps1_get_location  type='File'                where=sel.count==1 window='Hidden' sep='None')
//     // item(title="Directories"   keys="/" image=[E0E7,GREEN]  image-sel=[E0E7,HOVER] cmd-ps=ps1_get_location  type='File'                where=sel.count==1 window='Hidden' sep='None')
//     // item(title="Directories"   keys="/" image=[E0E7,GREEN]  image-sel=[E0E7,HOVER] cmd-ps=ps1_get_location  type='File'                where=sel.count==1 window='Hidden' sep='None')

// }
