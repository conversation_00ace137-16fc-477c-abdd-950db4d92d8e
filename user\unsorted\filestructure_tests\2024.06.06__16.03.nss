// =============================================================================
// [16:03] -> 06.06.2024: 'https://gemini.google.com/app/6db231fb809a6dc4'
NSS
│   // Configuration Files
├── config
│   ├── colors.nss
│   ├── icons.nss
│   ├── keys.nss
│   ├── tips.nss
│   └── theme.nss
│   // Initialization and Setup Scripts
├── setup
│   ├── initialize.nss
│   ├── variables.nss
│   └── update.nss
│   // Executable Scripts and Utility Functions
├── actions
│   │   // Scripts that execute external commands
│   ├── exec
│   │   ├── cmd.nss
│   │   ├── ps1.nss
│   │   └── py.nss
│   │   // Scripts for creating specific menu items
│   └── items
│       ├── open_*.nss     // (open_sublime, open_bulkrename, etc.)
│       ├── py_*.nss       // (py_markdown_gen, py_window_manager)
│       ├── regedit_*.nss  // (regedit_keyboard_rate, regedit_taskbar_left)
│       └── restart_*.nss  // (restart_chrome, restart_photoshop, etc.)
│   // Menu Definition Scripts
├── menus
│   ├── desktop.nss
│   ├── everything64.nss
│   ├── explorer.nss
│   ├── taskbar.nss
│   └── titlebar.nss
│   // Submenu Definition Scripts
├── submenus
│   ├── apps.nss
│   ├── clipboard.nss
│   ├── create.nss
│   ├── debug.nss
│   ├── directories.nss
│   ├── directories_jorn.nss
│   ├── everything.nss
│   ├── goto.nss
│   ├── microsoft_tools.nss
│   ├── processes.nss
│   ├── projects.nss
│   ├── preferences.nss
│   ├── shell.nss
│   ├── sublime.nss
│   ├── urls.nss
│   ├── utilities.nss
│   └── windows.nss
│   // Content Sections for Menus
└── sections
    ├── apps_nirsoft.nss
    ├── apps_sysinternals.nss
    ├── cloud_services.nss
    ├── debugging_tooltips.nss
    ├── git_commands.nss
    ├── microsoft_tools.nss
    ├── nilesoft_tools.nss
    ├── portal_links.nss
    ├── py_utilities.nss
    └── sync_tools.nss
