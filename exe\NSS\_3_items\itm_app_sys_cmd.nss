
//
$APP_SYS_CMD_DIR = '@sys.dir\System32'
$APP_SYS_CMD_EXE = '@APP_SYS_CMD_DIR\cmd.exe'
$APP_SYS_CMD_TIP = '@APP_SYS_CMD_EXE'

// Context: Explorer
item(
    title  = ":  &Cmd"
    keys   = "exe"
    type   = 'File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    args   = '/K (CD /D "@sel.dir") & (TITLE ^(cmd-prompt:@sys.datetime("H.M")^))'
    //
    image  = APP_SYS_CMD_EXE
    tip    = [APP_SYS_CMD_TIP,TIP3,0.5]
    //
    admin  = KEYS_EXE_ADMIN
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_SYS_CMD_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_SYS_CMD_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_SYS_CMD_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_SYS_CMD_DIR')),
    }
)
// Context: Taskbar
item(
    title  = ":  &Cmd"
    keys   = "exe"
    type   = 'Taskbar'
    args   = '/K (CD /D "@user.desktop") & (TITLE ^(cmd-prompt:@sys.datetime("H.M")^))'
    //
    image  = APP_SYS_CMD_EXE
    tip    = [APP_SYS_CMD_TIP,TIP3,0.5]
    //
    admin  = KEYS_EXE_ADMIN
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_SYS_CMD_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_SYS_CMD_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_SYS_CMD_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_SYS_CMD_DIR')),
    }
)