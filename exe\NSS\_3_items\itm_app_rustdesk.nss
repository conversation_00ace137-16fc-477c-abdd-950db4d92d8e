
//
$APP_USER_RUSTDESK_DIR = '@user.desktop\my\flow\home\__GOTO__\Apps\app_rustdesk\exe'
$APP_USER_RUSTDESK_EXE = '@APP_USER_RUSTDESK_DIR\RustDesk.exe'
$APP_USER_RUSTDESK_TIP = "..."+str.trimstart('@APP_USER_RUSTDESK_EXE','@app.dir')

// context: directory
item(
    title  = ":  &RustDesk"
    keys   = "exe"
    type   = 'File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    // find   = '.ts|.mp4|.mkv|.webm|.mov|.wmv|.avi|.flv|.mpg'
    // args   = '"@sel.file"'
    //
    image  = APP_USER_RUSTDESK_EXE
    tip    = [APP_USER_RUSTDESK_TIP,TIP3,0.8]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_USER_RUSTDESK_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_RUSTDESK_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_RUSTDESK_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_RUSTDESK_DIR')),
    }
)
// Context: Taskbar
item(
    title  = ":  &RustDesk"
    keys   = "exe"
    type   = 'Taskbar'
    args   = ''
    //
    image  = APP_USER_RUSTDESK_EXE
    tip    = [APP_USER_RUSTDESK_TIP,TIP3,0.8]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_USER_RUSTDESK_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_RUSTDESK_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_RUSTDESK_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_RUSTDESK_DIR')),
    }
)
