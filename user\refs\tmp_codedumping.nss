//
// item(title='01' image=[\uE043, #22A7F2] cmd='mmsys.cpl')
// item(title='02' image=[\uE05E, #22A7F2] cmd='mmsys.cpl')
// item(title='03' image=[\uE067, #22A7F2] cmd='mmsys.cpl')
// item(title='04' image=[\uE05F, #22A7F2] cmd='mmsys.cpl')
// item(title='05' image=[\uE055, #22A7F2] cmd='mmsys.cpl')
// item(title='06' image=[\uE068, #22A7F2] cmd='mmsys.cpl')
// item(title='07' image=[\uE073, #22A7F2] cmd='mmsys.cpl')
// item(title='08' image=[\uE074, #22A7F2] cmd='mmsys.cpl')
// item(title='10' image=[\uE0AB, #22A7F2] cmd='mmsys.cpl')
// item(title='11' image=[\uE0D0, #22A7F2] cmd='mmsys.cpl')
// item(title='12' image=[\uE0CF, #22A7F2] cmd='mmsys.cpl')
// item(title='13' image=[\uE0D6, #22A7F2] cmd='mmsys.cpl')
// item(title='14' image=[\uE0E8, #22A7F2] cmd='mmsys.cpl')
// item(title='15' image=[\uE01B, #22A7F2] cmd='mmsys.cpl')
// item(title='16' image=[\uE052, #22A7F2] cmd='mmsys.cpl')
// item(title='17' image=[\uE052, #22A7F2] cmd='mmsys.cpl')
// item(title='18' image=[\uE087, #22A7F2] cmd='mmsys.cpl')
// item(title='19' image=[\uE07E, #22A7F2] cmd='mmsys.cpl')
// item(title='20' image=[\uE06A, #22A7F2] cmd='mmsys.cpl')
// item(title='21' image=[\uE09B, #22A7F2] cmd='mmsys.cpl')
// item(title='22' image=[\uE01C, #22A7F2] cmd='mmsys.cpl')
// item(title='23' image=[\uE0D7, #22A7F2] cmd='mmsys.cpl')
// item(title='24' image=[\uE0E7, #22A7F2] cmd='mmsys.cpl')
// item(title='25' image=[\uE0E8, #22A7F2] cmd='mmsys.cpl')
// item(title='29' image=[\uE110, #22A7F2] cmd='mmsys.cpl')
// item(title='32' image=[\uE163, #22A7F2] cmd='mmsys.cpl')
// item(title='30' image=[\uE113, #22A7F2] cmd='mmsys.cpl')
// item(title='31' image=[\uE144, #22A7F2] cmd='mmsys.cpl')
// separator
// // item(title='Copy Filepath' image=[\uE1F3, #22A7F2] cmd='mmsys.cpl')
// item(title='Copy Filepath' image=[\uE09B, #22A7F2] cmd='mmsys.cpl')
// item(title='Copy Path' image=[\uE0E7, #22A7F2] cmd='mmsys.cpl')
// separator
// item(title='Show In Folder' image=[\uE099, #22A7F2] cmd='mmsys.cpl')
// separator
// item(title='26' image=[\uE0E9, #22A7F2] cmd='mmsys.cpl')
// item(title='27' image=[\uE0EA, #22A7F2] cmd='mmsys.cpl')
// item(title='33' image=[\uE164, #22A7F2] cmd='mmsys.cpl')
// item(title='34' image=[\uE17B, #22A7F2] cmd='mmsys.cpl')
// separator


// item(title='Copy Filepath' image=[\uE09B, #22A7F2] cmd='mmsys.cpl')
// item(title='Copy Path' image=[\uE0E7, #22A7F2] cmd='mmsys.cpl')
// separator
// item(title='Show In Folder' image=[\uE099, #22A7F2] cmd='mmsys.cpl')
// separator
// menu(separator="after" title=title.copy_path image=icon.copy_path)
// {
    // item(where=sel.count > 1 title='Copy (@sel.count) items selected' image=[\uE09B, #22A7F2] cmd=command.copy(sel(false, "\n")))

    // copy filepath
    item(mode="single" type='file' where=sel.file.len != sel.file.title.len title='Copy "'+@sel.file.title+'"' image=[\uE055, #4d8e4a] cmd=command.copy(sel.file.title))
    item(mode="single" title='Copy "'+@sel.path+'"' tip=sel.path image=[\uE09B, #4d8e4a] cmd=command.copy(sel.path))
    item(mode="single" where=@sel.parent.len>3 title='Copy "'+sel.parent+'"' image=[\uE0E7, #4d8e4a] cmd=@command.copy(sel.parent))
    separator

    separator
    // item(mode="single" type='file' separator="before" find='.lnk' image=[\uE099, #22A7F2] title='Show In Folder')
    // item(mode="single" type='file' separator="before" find='.lnk' title='open file location')
    // separator
    // // separator
    // item(mode="single" type='file' where=sel.file.ext.len>0 title=sel.file.ext image=[icon.copy_path, #22A7F2] cmd=command.copy(sel.file.ext))
// }
// menu(where=@(this.count == 0 && isw11) type='taskbar' image=icon.settings expanded=true)
// {
// 	item(title='Audio Output' image=\uE0EC cmd='mmsys.cpl')
// }
// 	// item(title='Audio Output' image=[\uE259, #22A7F2] cmd='mmsys.cpl')
// 	item(title='Audio Output' image=[\uE16F, #22A7F2] cmd='mmsys.cpl')
// 	// item(title='Audio Output' image=[\uE155, #22A7F2] cmd='mmsys.cpl')
// 	// item(title='qBittorrent' image cmd='C:\Program Files\qBittorrent\qbittorrent.exe')
// 	// item(title='qBittorrent' image=[\uE15E, #22A7F2] cmd='C:\Program Files\qBittorrent\qbittorrent.exe')
// 	// item(title='qBittorrent' image=[\uE15E, #22A7F2] cmd='powershell.exe' args='-noexit -command Set-Location -Path "@sel.dir\."')
// 	separator
// }
// 	// item(title=title.windows_powershell admin=@key.shift() tip=tip_run_admin image cmd='powershell.exe' args='-noexit -command Set-Location -Path "@sel.dir\."')
// cmd='powershell.exe' args='"(New-Object -ComObject WScript.Shell).AppActivate((get-process qbittorrent).MainWindowTitle)"')


// powershell -Command "(New-Object -ComObject WScript.Shell).AppActivate((get-process qbittorrent).MainWindowTitle)"


// (New-Object -ComObject WScript.Shell).AppActivate((get-process qbittorrent).MainWindowTitle)