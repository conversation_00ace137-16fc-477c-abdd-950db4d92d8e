
//
$PY_MARKDOWNGENERATOR_DIR = '@user.desktop\my\flow\home\__GOTO__\Scripts\Python\Py_Cli_Utils\py__MarkdownGenerator'
$PY_MARKDOWNGENERATOR_EXE = '@PY_MARKDOWNGENERATOR_DIR\venv\Scripts\python.exe'
$PY_MARKDOWNGENERATOR_APP = '@PY_MARKDOWNGENERATOR_DIR\main.py'
//

// Context: Explorer
$PY_MARKDOWNGENERATOR_EXPLORER = '-i "@sel.dir" -op "@sel.dir" -of "@sel.dir.name" -d 99 -e py --prompt'
item(
    title="&MarkdownGenerator"
    keys="py"
    type='File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    //
    image=[E17C,GREY] image-sel=[E17C,PURPLE]
    tip=['"@PY_MARKDOWNGENERATOR_APP" @PY_MARKDOWNGENERATOR_EXPLORER',TIP3,0.75]
    //
    admin=keys.rbutton()
    cmd=if(KEYS_EXE_OPEN_EXE,('"@PY_MARKDOWNGENERATOR_EXE"'))
    args='"@PY_MARKDOWNGENERATOR_APP" @PY_MARKDOWNGENERATOR_EXPLORER'
    //
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@PY_MARKDOWNGENERATOR_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@PY_MARKDOWNGENERATOR_APP')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@PY_MARKDOWNGENERATOR_DIR')),
    }
)
// Context: Taskbar
$PY_MARKDOWNGENERATOR_TASKBAR = '-i "@user.desktop" -op "@user.desktop" -of "output.md" -d 99 -e py --prompt'
item(
    title="&MarkdownGenerator"
    keys="py"
    type='Taskbar'
    //
    image=[E17C,GREY]
    image-sel=[E17C,PURPLE]
    //
    admin=keys.rbutton()
    cmd=if(KEYS_EXE_OPEN_EXE,('"@PY_MARKDOWNGENERATOR_EXE"'))
    args='"@PY_MARKDOWNGENERATOR_APP" @PY_MARKDOWNGENERATOR_TASKBAR'
    tip=['"@PY_MARKDOWNGENERATOR_APP" @PY_MARKDOWNGENERATOR_TASKBAR',TIP3,0.75]
    //
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@PY_MARKDOWNGENERATOR_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@PY_MARKDOWNGENERATOR_APP')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@PY_MARKDOWNGENERATOR_DIR')),
    }
)
