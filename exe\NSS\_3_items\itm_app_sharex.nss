
//
$APP_USER_SHAREX_DIR = '@user.desktop\my\flow\home\__GOTO__\Apps\app_sharex\exe'
$APP_USER_SHAREX_EXE = '@APP_USER_SHAREX_DIR\ShareX.exe'
$APP_USER_SHAREX_TIP = "..."+str.trimstart('@APP_USER_SHAREX_EXE','@app.dir')

// context: directory
item(
    title  = ":  &ShareX"
    keys   = "exe"
    type   = 'File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    // find   = '.ts|.mp4|.mkv|.webm|.mov|.wmv|.avi|.flv|.mpg'
    // args   = '"@sel.file"'
    //
    image  = APP_USER_SHAREX_EXE
    tip    = [APP_USER_SHAREX_TIP,TIP3,0.8]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_USER_SHAREX_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_SHAREX_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_SHAREX_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_SHAREX_DIR')),
    }
)
// Context: Taskbar
item(
    title  = ":  &ShareX"
    keys   = "exe"
    type   = 'Taskbar'
    args   = ''
    //
    image  = APP_USER_SHAREX_EXE
    tip    = [APP_USER_SHAREX_TIP,TIP3,0.8]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_USER_SHAREX_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_SHAREX_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_SHAREX_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_SHAREX_DIR')),
    }
)
