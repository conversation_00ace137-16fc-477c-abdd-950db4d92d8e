
/* item: singles/system/cmd */


//
$APP_CMD_DIR = '@sys.dir\System32'
$APP_CMD_EXE = '@APP_CMD_DIR\cmd.exe'
$APP_CMD_TIP = "..."+str.trimstart('@APP_CMD_EXE','@app.dir')

// Context: Explorer
item(
    title  = ":  &CMD"
    keys   = "exe"
    type   = 'File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    args   = '/K (CD /D "@sel.dir") & (TITLE ^(cmd-prompt:@sys.datetime("H.M")^))'
    //
    image  = APP_CMD_EXE
    tip    = [APP_CMD_TIP,TIP3,1.0]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_CMD_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_CMD_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_CMD_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_CMD_DIR')),
    }
)
// Context: Taskbar
item(
    title  = ":  &CMD"
    keys   = "exe"
    type   = 'Taskbar'
    args   = '/K (CD /D "@user.desktop") & (TITLE ^(cmd-prompt:@sys.datetime("H.M")^))'
    //
    image  = APP_CMD_EXE
    tip    = [APP_CMD_TIP,TIP3,1.0]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_CMD_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_CMD_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_CMD_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_CMD_DIR')),
    }
)
