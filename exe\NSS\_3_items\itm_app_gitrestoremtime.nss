
//
$APP_USER_GITRESTOREMTIME_DIR = '@user.desktop\my\flow\home\__GOTO__\Apps\grp_gittools\app_gitrestoremtime\exe'
$APP_USER_GITRESTOREMTIME_EXE = '@APP_USER_GITRESTOREMTIME_DIR\git-restore-mtime.exe'
$APP_USER_GITRESTOREMTIME_TIP = "..."+str.trimstart('@APP_USER_GITRESTOREMTIME_EXE','@app.dir')

//
$BATCH_SEL_AS_STRING = for(i=0, i< sel.count, '"@sel[i]" ')
$BATCH_FINISHED_MSG1 = "TITLE "+"["+sys.datetime("H.M")+"]"+" Finished ... Exiting"
$BATCH_FINISHED_MSG2 = "ECHO. & ECHO. & ECHO Finished && ECHO ---------"
$BATCH_FINISHED_MSG3 = "ECHO Window will close in 10 seconds ... & PING 127.0.0.1 -n 10 > NUL & EXIT"
$BATCH_EXIT_WITH_MSG = '@BATCH_FINISHED_MSG1 && @BATCH_FINISHED_MSG2 && @BATCH_FINISHED_MSG3'

// $BATCH_EXIT_WITH_MESSAGE = "ECHO. & ECHO Finished && ECHO --------- & ECHO. & ECHO. & ECHO Window will close in 10 seconds ...& TITLE Finished & PING 127.0.0.1 -n 10 > NUL & EXIT"
// $BATCH_EXIT_WITH_MESSAGE = "ECHO. & ECHO FINISHED & ECHO. & ECHO Window will close in 10 seconds ...& PING 127.0.0.1 -n 10 > NUL & EXIT"
//
$APP_USER_GITRESTOREMTIME_SELECTION     = '/C (CD /D "@sel.parent") && (@APP_USER_GITRESTOREMTIME_EXE @BATCH_SEL_AS_STRING)'
$APP_USER_GITRESTOREMTIME_SELECTION_V   = '/K (CD /D "@sel.parent") && (@APP_USER_GITRESTOREMTIME_EXE @BATCH_SEL_AS_STRING) && @BATCH_EXIT_WITH_MSG'
$APP_USER_GITRESTOREMTIME_SELECTION_F   = '/C (CD /D "@sel.parent") && (@APP_USER_GITRESTOREMTIME_EXE @BATCH_SEL_AS_STRING) --force'
$APP_USER_GITRESTOREMTIME_SELECTION_F_V = '/K (CD /D "@sel.parent") && (@APP_USER_GITRESTOREMTIME_EXE @BATCH_SEL_AS_STRING) --force && @BATCH_EXIT_WITH_MSG'

// -> gitrestoremtime selection
item(title="&gitrestoremtime: (selection)"
    keys="..."
    image=[E26E,PURPLE]
    image-sel=[E26E,GREEN]
    type='File|Desktop|Dir|Drive'
    where=sel.count>=1
    tip=['@APP_USER_GITRESTOREMTIME_SELECTION_V','@tip.info',0.75]
    admin=(keys.shift() AND (keys.lbutton() OR keys.rbutton()))
    commands{
        cmd-line='@APP_USER_GITRESTOREMTIME_SELECTION_V',
    }
    window='Hidden'
    vis=KEYS_MNU_VISIBILITY_0_DEFAULT
)
// -> gitrestoremtime *
item(title="&gitrestoremtime: *"
    keys="..."
    image=[E26E,PURPLE]
    image-sel=[E26E,GREEN]
    type='Back.Dir|Back.Drive'
    where=sel.count>=1
    tip=['@APP_USER_GITRESTOREMTIME_SELECTION_V','@tip.info',0.75]
    admin=(keys.shift() AND (keys.lbutton() OR keys.rbutton()))
    commands{
        cmd-line='@APP_USER_GITRESTOREMTIME_SELECTION_V',
    }
    window='Hidden'
    vis=KEYS_MNU_VISIBILITY_0_DEFAULT
)

// -> gitrestoremtime selection --force
item(title="&gitrestoremtime: (selection) --force"
    keys="..."
    image=[E26E,ORANGE]
    image-sel=[E26E,RED]
    type='File|Desktop|Dir|Drive'
    where=sel.count>=1
    tip=['@APP_USER_GITRESTOREMTIME_SELECTION_F_V','@tip.info',0.75]
    admin=(keys.shift() AND (keys.lbutton() OR keys.rbutton()))
    commands{
        cmd-line='@APP_USER_GITRESTOREMTIME_SELECTION_F_V',
    }
    window='Hidden'
    vis=KEYS_MNU_VISIBILITY_1_SHIFT
)
// -> gitrestoremtime * --force
item(title="&gitrestoremtime: * --force"
    keys="..."
    image=[E26E,ORANGE]
    image-sel=[E26E,RED]
    type='Back.Dir|Back.Drive'
    where=sel.count>=1
    tip=['@APP_USER_GITRESTOREMTIME_SELECTION_F_V','@tip.info',0.75]
    admin=(keys.shift() AND (keys.lbutton() OR keys.rbutton()))
    commands{
        cmd-line='@APP_USER_GITRESTOREMTIME_SELECTION_F_V',
    }
    window='Hidden'
    vis=KEYS_MNU_VISIBILITY_1_SHIFT
)

