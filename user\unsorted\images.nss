$color3 = @if(theme.islight,image.color3,'none')
$color_islight_WB = @if(theme.islight,'#fff','#000')
$clipPath='<defs><clipPath id="clip0"><path fill="#fff" d="M0 0h16v16H0z"/></clipPath></defs>'
$svg_window_template2='<path fill="@image.color1" d="M3.453 15C3.12 15 2.805 14.93 2.508 14.8C2.508 14.8 2.211 14.66 1.953 14.48C1.953 14.48 1.734 14.27 1.516 14.05C1.516 14.05 1.339 13.79 1.203 13.49C1.203 13.49 1.068 13.2 1 12.88L1 3.438C1 3.115 1.068 2.805 1.203 2.508C1.203 2.508 1.339 2.211 1.516 1.951C1.516 1.951 1.734 1.727 1.953 1.503C1.953 1.503 2.211 1.326 2.508 1.195C2.508 1.195 2.805 1.065 3.12 1L12.56 1C12.89 1 13.2 1.065 13.49 1.195C13.49 1.195 13.79 1.326 14.05 1.503C14.05 1.503 14.27 1.727 14.5 1.951C14.5 1.951 14.67 2.211 14.8 2.508C14.8 2.508 14.93 2.805 15 3.115L15 6.938C14.84 6.812 14.68 6.7 14.52 6.602C14.52 6.602 14.35 6.503 14.18 6.406L14 5L2 5L2 12.5C2 12.7 2.039 12.89 2.117 13.07C2.117 13.07 2.195 13.25 2.305 13.41C2.305 13.41 2.445 13.55 2.586 13.7C2.586 13.7 2.747 13.8 2.93 13.88C2.93 13.88 3.112 13.96 3.302 14L6.328 14C6.412 14.18 6.505 14.35 6.609 14.52C6.609 14.52 6.714 14.68 6.823 14.84ZM2 4L14 4L14 3.5C14 3.292 13.96 3.096 13.88 2.914C13.88 2.914 13.8 2.732 13.7 2.573C13.7 2.573 13.56 2.438 13.43 2.302C13.43 2.302 13.27 2.195 13.09 2.117C13.09 2.117 12.9 2.039 12.71 2L3.5 2C3.302 2 3.112 2.039 2.93 2.117C2.93 2.117 2.747 2.195 2.586 2.302C2.586 2.302 2.445 2.438 2.305 2.573C2.305 2.573 2.195 2.732 2.117 2.914C2.117 2.914 2.039 3.096 2 3.292Z"/>'
$svg_window_template='<path fill="@image.color1" d="M3.453 15C3.12 15 2.805 14.93 2.508 14.8C2.508 14.8 2.211 14.66 1.953 14.48C1.953 14.48 1.734 14.27 1.516 14.05C1.516 14.05 1.339 13.79 1.203 13.49C1.203 13.49 1.068 13.2 1 12.88L1 3.438C1 3.115 1.068 2.805 1.203 2.508C1.203 2.508 1.339 2.211 1.516 1.951C1.516 1.951 1.734 1.727 1.953 1.503C1.953 1.503 2.211 1.326 2.508 1.195C2.508 1.195 2.805 1.065 3.12 1L12.56 1C12.89 1 13.2 1.065 13.49 1.195C13.49 1.195 13.79 1.326 14.05 1.503C14.05 1.503 14.27 1.727 14.5 1.951C14.5 1.951 14.67 2.211 14.8 2.508C14.8 2.508 14.93 2.805 15 3.115L15 12.55C15 12.88 14.93 13.2 14.8 13.49C14.8 13.49 14.67 13.79 14.5 14.05C14.5 14.05 14.27 14.27 14.05 14.48C14.05 14.48 13.79 14.66 13.49 14.8C13.49 14.8 13.2 14.93 12.89 15ZM2 4L14 4L14 3.5C14 3.292 13.96 3.096 13.88 2.914C13.88 2.914 13.8 2.732 13.7 2.573C13.7 2.573 13.56 2.438 13.43 2.302C13.43 2.302 13.27 2.195 13.09 2.117C13.09 2.117 12.9 2.039 12.71 2L3.5 2C3.302 2 3.112 2.039 2.93 2.117C2.93 2.117 2.747 2.195 2.586 2.302C2.586 2.302 2.445 2.438 2.305 2.573C2.305 2.573 2.195 2.732 2.117 2.914C2.117 2.914 2.039 3.096 2 3.292ZM12.5 14C12.71 14 12.9 13.96 13.09 13.88C13.09 13.88 13.27 13.8 13.43 13.7C13.43 13.7 13.56 13.55 13.7 13.41C13.7 13.41 13.8 13.25 13.88 13.07C13.88 13.07 13.96 12.89 14 12.7L14 5L2 5L2 12.5C2 12.7 2.039 12.89 2.117 13.07C2.117 13.07 2.195 13.25 2.305 13.41C2.305 13.41 2.445 13.55 2.586 13.7C2.586 13.7 2.747 13.8 2.93 13.88C2.93 13.88 3.112 13.96 3.302 14Z"/>'

@copy,copy_to_clipboard='<svg fill="none" viewBox="0 0 16 16">
  <path fill="@color3" d="M5 4.5H3.5a2 2 0 00-2 2v6a2 2 0 002 2h4A2 2 0 009.43 13H8a3 3 0 01-3-3V4.5z"/>
  <path fill="@image.color1" d="M5 4H3.5A2.5 2.5 0 001 6.5v6A2.5 2.5 0 003.5 15h4a2.5 2.5 0 002.45-2H8.91A1.5 1.5 0 017.5 14h-4A1.5 1.5 0 012 12.5v-6A1.5 1.5 0 013.5 5H5V4z"/>
  <path fill="@color3" d="M6.5 9.5v-6a2 2 0 012-2h4a2 2 0 012 2v6a2 2 0 01-2 2h-4a2 2 0 01-2-2z"/>
  <path fill="@image.color2" d="M8.5 2A1.5 1.5 0 007 3.5v6A1.5 1.5 0 008.5 11h4A1.5 1.5 0 0014 9.5v-6A1.5 1.5 0 0012.5 2h-4zM6 3.5A2.5 2.5 0 018.5 1h4A2.5 2.5 0 0115 3.5v6a2.5 2.5 0 01-2.5 2.5h-4A2.5 2.5 0 016 9.5v-6z"/>
</svg>'

@cut='<svg fill="none" viewBox="0 0 16 16">
  <g clip-path="url(#clip0)">
    <path fill="@image.color1" d="M3.22.08a.5.5 0 01.69.14l6.82 10.49a.5.5 0 11-.83.54L3.08.77a.5.5 0 01.14-.69z"/>
    <path fill="@image.color1" d="M7.85 6.74l-2.59 4a.5.5 0 00.83.54L8.45 7.66l-.59-.91zm1.34-.23L12.92.77a.5.5 0 00-.83-.54L8.59 5.59l.59.91z"/>
    <path fill="@image.color2" d="M4 11a2 2 0 100 4 2 2 0 000-4zm-3 2a3 3 0 116 0 3 3 0 01-6 0zm11-2a2 2 0 100 4 2 2 0 000-4zm-3 2a3 3 0 116 0 3 3 0 01-6 0z"/>
  </g>
  @clipPath
</svg>'

@paste='<svg fill="none" viewBox="0 0 16 16">
  <path fill="@color3" d="M2.5 1.5h2a1 1 0 011-1h3a1 1 0 011 1h2a1 1 0 011 1V4H8a2 2 0 00-2 2v9.5H2.5a1 1 0 01-1-1v-12a1 1 0 011-1z"/>
  <path fill="@image.color1" d="M2.5 2a.5.5 0 00-.5.5v12a.5.5 0 00.5.5h3a.5.5 0 010 1h-3A1.5 1.5 0 011 14.5v-12A1.5 1.5 0 012.5 1h2a.5.5 0 010 1h-2zM9 1.5a.5.5 0 01.5-.5h2A1.5 1.5 0 0113 2.5v1a.5.5 0 01-1 0v-1a.5.5 0 00-.5-.5h-2a.5.5 0 01-.5-.5z"/>
  <path fill="@image.color1" d="M4 1.5A1.5 1.5 0 015.5 0h3a1.5 1.5 0 110 3h-3A1.5 1.5 0 014 1.5zM5.5 1a.5.5 0 000 1h3a.5.5 0 000-1h-3z"/>
  <path fill="@color3" d="M13.5 5.5h-5a1 1 0 00-1 1v8a1 1 0 001 1h5a1 1 0 001-1v-8a1 1 0 00-1-1z"/>
  <path fill="@image.color2" d="M7 6.5A1.5 1.5 0 018.5 5h5A1.5 1.5 0 0115 6.5v8a1.5 1.5 0 01-1.5 1.5h-5A1.5 1.5 0 017 14.5v-8zM8.5 6a.5.5 0 00-.5.5v8a.5.5 0 00.5.5h5a.5.5 0 00.5-.5v-8a.5.5 0 00-.5-.5h-5z"/>
</svg>'

@paste_shortcut='<svg fill="none" viewBox="0 0 16 16">
  <path fill="@color3" d="M3.5 1.5h2a1 1 0 011-1h3a1 1 0 011 1h2a1 1 0 011 1V7H9a2 2 0 00-2 2v6.5H3.5a1 1 0 01-1-1v-12a1 1 0 011-1z"/>
  <path fill="@image.color1" d="M3.5 2a.5.5 0 00-.5.5v12a.5.5 0 00.5.5h3a.5.5 0 010 1h-3A1.5 1.5 0 012 14.5v-12A1.5 1.5 0 013.5 1h2a.5.5 0 010 1h-2zm6.5-.5a.5.5 0 01.5-.5h2A1.5 1.5 0 0114 2.5v4a.5.5 0 01-1 0v-4a.5.5 0 00-.5-.5h-2a.5.5 0 01-.5-.5z"/>
  <path fill="@image.color1" d="M5 1.5A1.5 1.5 0 016.5 0h3a1.5 1.5 0 010 3h-3A1.5 1.5 0 015 1.5zM6.5 1a.5.5 0 000 1h3a.5.5 0 000-1h-3z"/>
  <path fill="@image.color2" d="M8 9.5A1.5 1.5 0 019.5 8h5A1.5 1.5 0 0116 9.5v5a1.5 1.5 0 01-1.5 1.5h-5A1.5 1.5 0 018 14.5v-5z"/>
  <path fill="@color_islight_WB" d="M10.5 10a.5.5 0 000 1h1.61l-2.05 2.05a.62.62 0 10.88.88L13 11.88V13.5a.5.5 0 001 0v-3a.5.5 0 00-.5-.5h-3z"/>
</svg>'

@move_to, move_here='<svg fill="none" viewBox="0 0 16 16">
  <path fill="@color3" d="M2.5 1.5h3a2 2 0 011.6.8L8 3.5h4.5a2 2 0 012 2 .87.87 0 01.86.99l-.09.66A5.75 5.75 0 006.59 14.5H2.5a2 2 0 01-2-2v-9a2 2 0 012-2z"/>
  <path fill="@image.color1" d="M2.5 1A2.5 2.5 0 000 3.5v9A2.5 2.5 0 002.5 15h4.43a5.75 5.75 0 01-.61-1H2.5c-.25 0-.49-.06-.70-.17l1.44-7.02A1 1 0 014.22 6h5.59a5.75 5.75 0 011.68-.25c.58 0 1.15.08 1.68.25h.59a1 1 0 01.97.75c.***********.96.83l.04-.31a1.99 1.99 0 00-.76-1.86A2.5 2.5 0 0012.5 3H8.25L7.5 2a2.5 2.5 0 00-2-1h-3zm10 3a1.5 1.5 0 011.41 1.00A2.04 2.04 0 0013.77 5H4.22a2 2 0 00-1.96 1.59l-1.25 6.08c-.00-.06-.01-.12-.01-.18v-9A1.5 1.5 0 012.5 2h3a1.5 1.5 0 011.2.6l.9 1.2.15.2h4.75z"/>
  <path fill="@image.color2" d="M11.5 7a4.5 4.5 0 110 9 4.5 4.5 0 010-9z"/>
  <path fill="#fff" d="M11.14 13.85a.5.5 0 010-.70l1.64-1.64-1.64-1.64a.5.5 0 01.70-.70l2 2a.5.5 0 010 .70l-2 2a.5.5 0 01-.70 0z"/>
  <path fill="#fff" d="M9 11.5a.5.5 0 01.5-.5h4a.5.5 0 010 1h-4a.5.5 0 01-.5-.5z"/>
</svg>'

@copy_as_path, copy_path='<svg fill="none" viewBox="0 0 16 16">
  <g>
    <path fill="@color3" d="M13 2.5H3A2.5 2.5 0 00.5 5v6A2.5 2.5 0 003 13.5h10a2.5 2.5 0 002.5-2.5V5A2.5 2.5 0 0013 2.5z"/>
    <path fill="@image.color1" d="M0 5a3 3 0 013-3h10a3 3 0 013 3v6a3 3 0 01-3 3H3a3 3 0 01-3-3V5zm3-2a2 2 0 00-2 2v6a2 2 0 002 2h10a2 2 0 002-2V5a2 2 0 00-2-2H3z"/>
    <path fill="@image.color2" d="M2.36 4.02a.5.5 0 01.61.34l2 7a.5.5 0 01-.96.27l-2-7a.5.5 0 01.34-.61zm3 0a.5.5 0 01.61.34l2 7a.5.5 0 01-.96.27l-2-7a.5.5 0 01.34-.61zM11 11.25a.75.75 0 11-1.5 0 .75.75 0 011.5 0zM13.5 11.25a.75.75 0 11-1.5 0 .75.75 0 011.5 0z"/>
  </g>
</svg>'

@settings, options, folder_options, taskbar_settings='<svg fill="none" viewBox="0 0 16 16">
  <g clip-path="url(#clip0)">
	<path fill="@image.color1" d="M8.00 16c-.43 0-.87-.03-1.3-.10a1 1 0 01-.81-.82l-.26-1.51a.93.93 0 00-.45-.63.94.94 0 00-.38-.12.90.90 0 00-.4.05l-1.47.52a1 1 0 01-1.11-.30 8.21 8.21 0 01-1.28-2.24 1.00 1.00 0 01.3-1.12l1.18-.98a.94.94 0 000-1.43L.80 6.27a1 1 0 01-.29-1.12 8.21 8.21 0 011.30-2.22 1.00 1.00 0 011.12-.3l1.44.53a.91.91 0 **********.96 0 00.38-.12.93.93 0 00.45-.64L5.89.90A1 1 0 016.73.09c.42-.06.84-.09 1.26-.09.43-.00.87.03 ********.***********.28a.97.97 0 01.27.53l.26 1.52a.95.95 0 ***********.95 0 00.4-.05l1.47-.52a1 1 0 011.11.30 8.18 8.18 0 011.27 2.24 1.00 1.00 0 01-.30 1.12l-1.17.98a.93.93 0 00-.*********** 0 00.33.72l1.18 1a1 1 0 01.29 1.12 8.17 8.17 0 01-1.31 2.22 1.01 1.01 0 01-1.11.3 52.11 52.11 0 01-.97-.36 2.01 2.01 0 00-.78-.22.94.94 0 00-.88.62c-.02.07-.06.3-.17.89l-.14.79a1 1 0 01-.83.81A9.22 9.22 0 018.00 16zm-3.30-4.21a1.98 1.98 0 01.97.25 1.94 1.94 0 01.94 1.33l.26 1.52c.74.12 1.51.12 2.26.01l.13-.79a7.54 7.54 0 01.21-1.04 1.94 1.94 0 011.82-1.28 2.86 2.86 0 011.13.29c.23.08.53.2.95.35.49-.58.88-1.23 1.15-1.94h-.00L13.36 9.48a1.94 1.94 0 01-.50-2.29 1.9 1.9 0 01.50-.66l1.18-.98a6.98 6.98 0 00-1.11-1.96l-1.47.53A1.94 1.94 0 019.4 2.62l-.26-1.52A6.67 6.67 0 008.00 1a8.13 8.13 0 00-1.12.08l-.27 1.53A1.94 1.94 0 014.04 4.09l-1.44-.53a7.01 7.01 0 00-1.15 1.94l1.2 1.01a1.94 1.94 0 01.50 2.29 1.93 1.93 0 01-.5.66l-1.18.98a7.02 7.02 0 001.11 1.96l1.47-.53a1.95 1.95 0 01.65-.11z"/>
	<path stroke="@image.color2" stroke-linejoin="round" d="M8 10.5a2.5 2.5 0 110-5 2.5 2.5 0 010 5z"/>
  </g>
  @clipPath
</svg>'

@task_manager='<svg fill="none" viewBox="0 0 16 16">
	<path fill="@image.color1" d="M3.9 15C3.6 15 3.2 15 2.8 15C2.8 15 2.5 15 2.1 14C2.1 14 1.9 14 1.6 14C1.6 14 1.4 14 1.2 13C1.2 13 1.1 13 1 12L1 3.9C1 3.5 1.1 3.2 1.2 2.8C1.2 2.8 1.4 2.5 1.6 2.1C1.6 2.1 1.9 1.9 2.1 1.6C2.1 1.6 2.5 1.4 2.8 1.2C2.8 1.2 3.2 1.1 3.6 1L12 1C12 1 13 1.1 13 1.2C13 1.2 14 1.4 14 1.6C14 1.6 14 1.9 14 2.1C14 2.1 15 2.5 15 2.8C15 2.8 15 3.2 15 3.5L15 12C15 12 15 13 15 13C15 13 15 14 14 14C14 14 14 14 14 14C14 14 14 15 13 15C13 15 13 15 12 15ZM12 14C12 14 13 14 13 14C13 14 13 14 13 14C13 14 13 13 14 13C14 13 14 13 14 13C14 13 14 13 14 12L14 4C14 3.7 14 3.5 14 3.2C14 3.2 14 3 14 2.8C14 2.8 13 2.6 13 2.4C13 2.4 13 2.3 13 2.2C13 2.2 13 2 12 2L4 2C3.7 2 3.5 2 3.2 2.2C3.2 2.2 3 2.3 2.8 2.4C2.8 2.4 2.6 2.6 2.4 2.8C2.4 2.8 2.3 3 2.2 3.2C2.2 3.2 2 3.5 2 3.7L2 12C2 12 2 13 2.2 13C2.2 13 2.3 13 2.4 13C2.4 13 2.6 13 2.8 14C2.8 14 3 14 3.2 14C3.2 14 3.5 14 3.7 14Z"/>
	<path fill="@image.color2" d="M9 12C8.9 12 8.8 12 8.7 12C8.7 12 8.6 12 8.6 12L6.9 6.1L6 8.7C5.9 8.8 5.9 8.8 5.8 8.9C5.8 8.9 5.7 9 5.6 9L3.5 9C3.4 9 3.2 9 3.1 8.8C3.1 8.8 3.1 8.8 3 8.6C3 8.4 3.1 8.2 3.1 8.2C3.1 8.2 3.2 8.1 3.4 8L5.2 8L6.5 4.3C6.6 4.2 6.6 4.1 6.7 4.1C6.7 4.1 6.8 4 6.9 4C7.1 4 7.2 4 7.3 4.1C7.3 4.1 7.4 4.2 7.5 4.3L9.1 10L10 8.3C10 8.2 10 8.1 10 8.1C10 8.1 10 8 10 8L12 8C13 8 13 8.1 13 8.2C13 8.2 13 8.2 13 8.4C13 8.6 13 8.8 13 8.8C13 8.8 13 9 13 9L11 9L9.5 12C9.4 12 9.3 12 9.2 12C9.2 12 9.2 12 9.1 12Z"/>
</svg>'

@run_as_administrator='<svg fill="none" viewBox="0 0 16 16">
  <g clip-path="url(#clip0)">
    <path fill="@color3" d="M1.5 4v8A2.5 2.5 0 004 14.5h4.13c-.77-1.15-.99-2.49-1.12-3.81L7 8.08a1.41 1.41 0 011.4-1.41 3.52 3.52 0 002.59-1.23 1.38 1.38 0 012.00 0A3.51 3.51 0 0014.5 6.46V4A2.5 2.5 0 0012 1.5H4A2.5 2.5 0 001.5 4z"/>
    <path fill="@image.color1" d="M2 12V5h12v1.23a3.48 3.48 0 001 .36V4a3 3 0 00-3-3H4a3 3 0 00-3 3v8a3 3 0 003 3h4.51a5.21 5.21 0 01-.67-1H4a2 2 0 01-2-2zM4 2h8a2 2 0 012 2H2a2 2 0 012-2z"/>
    <path fill="@image.color2" d="M15.6 7.66a.40.40 0 01.39.33L16 10.58a5.29 5.29 0 01-3.87 ********** 0 01-.25 0C8.81 14.91 8.24 13.05 8 10.58V8.08a.40.40 0 01.4-.41 4.49 4.49 0 003.31-********** 0 01.56 0A4.50 4.50 0 0015.6 7.66z"/>
  </g>
  @clipPath
</svg>'

@run_as_different_user='<svg fill="none" viewBox="0 0 16 16">
	<path fill="@image.color1" d="M6.9 14C7 14 7.2 15 7.4 15L3.9 15C3.6 15 3.2 15 2.8 15C2.8 15 2.5 15 2.1 14C2.1 14 1.9 14 1.6 14C1.6 14 1.4 14 1.2 13C1.2 13 1.1 13 1 12L1 3.9C1 3.5 1.1 3.2 1.2 2.8C1.2 2.8 1.4 2.5 1.6 2.1C1.6 2.1 1.9 1.9 2.1 1.6C2.1 1.6 2.5 1.4 2.8 1.2C2.8 1.2 3.2 1.1 3.6 1L12 1C12 1 13 1.1 13 1.2C13 1.2 14 1.4 14 1.6C14 1.6 14 1.9 14 2.1C14 2.1 15 2.5 15 2.8C15 2.8 15 3.2 15 3.5L15 5.2C15 5.1 15 4.9 15 4.8C15 4.8 14 4.6 14 4.5L14 4C14 3.7 14 3.5 14 3.2C14 3.2 14 3 14 2.8C14 2.8 13 2.6 13 2.4C13 2.4 13 2.3 13 2.2C13 2.2 13 2 12 2L4 2C3.7 2 3.4 2 3.2 2.2C3.2 2.2 3 2.3 2.8 2.4C2.8 2.4 2.6 2.6 2.4 2.8C2.4 2.8 2.3 3 2.2 3.2C2.2 3.2 2 3.5 2 3.7L11 4C10 4.2 9.6 4.5 9.2 5L2 5L2 12C2 12 2 13 2.2 13C2.2 13 2.3 13 2.4 13C2.4 13 2.6 13 2.8 14C2.8 14 3 14 3.2 14C3.2 14 3.5 14 3.7 14Z"/>
	<path fill="@image.color2" d="M14 7.5C14 7.9 14 8.2 14 8.5C14 8.5 14 8.8 14 9.1C14 9.1 14 9.3 14 9.5C14 9.5 13 9.7 13 9.8C13 9.8 13 9.9 12 10C12 10 11 9.9 11 9.8C11 9.8 11 9.7 10 9.5C10 9.5 10 9.3 10 9C10 9 9.8 8.8 9.7 8.5C9.7 8.5 9.6 8.2 9.5 7.8C9.5 7.2 9.6 6.8 9.7 6.5C9.7 6.5 9.8 6.2 10 6C10 6 10 5.7 10 5.5C10 5.5 11 5.3 11 5.2C11 5.2 11 5.1 12 5C12 5 13 5.1 13 5.2C13 5.2 13 5.3 14 5.5C14 5.5 14 5.7 14 6C14 6 14 6.2 14 6.5C14 6.5 14 6.9 14 7.2Z"/>
	<path fill="@image.color2" d="M16 13C16 13 16 14 16 14C16 14 16 14 16 14C16 14 15 15 15 15C15 15 15 15 15 15C15 15 14 15 14 16C14 16 14 16 14 16C14 16 13 16 13 16C13 16 13 16 12 16C12 16 11 16 11 16C11 16 11 16 11 16C11 16 10 16 9.9 16C9.9 16 9.7 15 9.4 15C9.4 15 9.1 15 8.9 15C8.9 15 8.7 15 8.5 14C8.5 14 8.3 14 8.2 14C8.2 14 8.1 14 8 13L8 13C8 12 8 12 8.1 12C8.1 12 8.2 12 8.3 12C8.3 12 8.5 11 8.6 11C8.6 11 8.8 11 9 11C9 11 9.2 11 9.4 11L14 11C15 11 15 11 15 11C15 11 15 11 15 11C15 11 16 11 16 12C16 12 16 12 16 12C16 12 16 12 16 12Z"/>
</svg>'

@personalize='<svg fill="none" viewBox="0 0 16 16">
  <g clip-path="url(#clip0)">
    <path fill="@color3" d="M15.17.76c.***********.06 1.41l-1.32 1.94a47.29 47.29 0 01-7.33 8.40l-2.27-2.11a46.41 46.41 0 017.91-8.32L13.76.82C14.2.45 14.76.39 15.17.76z"/>
    <path fill="@image.color1" d="M13.44.44c.56-.47 1.42-.64 2.06-.***********.***********.37-.12.73-.32 1.05l-.00.01-1.32 1.93a47.79 47.79 0 01-7.40 8.49l-.67-.74a46.79 46.79 0 007.25-8.31l1.32-1.93c.13-.20.17-.36.16-.47-.00-.09-.03-.19-.15-.29-.16-.15-.44-.18-.75.08l-.00.00-1.53 1.25a45.91 45.91 0 00-7.83 8.22l-.80-.59a46.91 46.91 0 018-8.40L13.44.44z"/>
    <path fill="@color3" d="M2.49 15.5c-.62 0-1.60-.15-1.99-.28 1.32-1.11 1.68-1.18 1.85-2.56 0-1.75 1.47-2.16 2.21-********** 1.92.57 1.92 2.30 0 2.16-2.84 2.69-4.00 2.69z"/>
    <path fill="@image.color2" d="M2.90 10.50A2.96 2.96 0 014.60 10c.43.03 1.03.20 **********.45.87 1.16.87 2.15 0 1.35-.91 2.16-1.85 2.61-.94.44-2.01.58-2.65.58-.67 0-1.70-.16-2.14-.30a.5.5 0 01-.17-.85c.2-.16.39-.32.55-.45l.27-.22c.21-.18.36-.31.47-.44.18-.22.29-.47.38-1.09.01-1.02.45-1.70 1.04-2.11zm.57.82c-.33.22-.61.61-.61 1.32a.50.50 0 01-.00.06c-.09.73-.24 1.20-.61 1.64a4.64 4.64 0 01-.60.57c.**********.86.06.52 0 1.45-.12 2.22-.48.76-.36 1.28-.89 1.28-1.70 0-.73-.25-1.16-.53-1.41A1.61 1.61 0 004.55 11a1.96 1.96 0 00-1.07.32z"/>
  </g>
  @clipPath
</svg>'

@display_settings='<svg fill="none" viewBox="0 0 16 16">
	<path fill="@color3" d="M2 .5h12A1.5 1.5 0 0115.5 2v5.72A5.5 5.5 0 006.09 12.5H2A1.5 1.5 0 01.5 11V2A1.5 1.5 0 012 .5z"/>
	<path fill="@image.color1" d="M14 1H2a1 1 0 00-1 1v9a1 1 0 001 1h4.02c.***********.18 1H6v2h1.25c.*********** 1.08 1H3.5a.5.5 0 010-1H5v-2H2a2 2 0 01-2-2V2a2 2 0 012-2h12a2 2 0 012 2v6.33a5.53 5.53 0 00-1-1.08V2a1 1 0 00-1-1z"/>
	<path fill="@image.color2" d="M10.07 15.13l-.35.35a4.53 4.53 0 01-.87-.48l.11-.39a2 2 0 00-1.45-2.52l-.48-.11a4.25 4.25 0 010-.96l.48-.11a2 2 0 001.45-2.52l-.11-.39c.27-.19.56-.35.87-.48l.35.35a2 2 0 002.86 0l.35-.35c.***********.87.48l-.11.39a2 2 0 001.45 2.52l.48.11a4.25 4.25 0 010 .96l-.48.11a2 2 0 00-1.45 2.52l.11.39c-.27.19-.56.35-.87.48l-.35-.35a2 2 0 00-2.86 0zM11.5 10.5a1 1 0 100 2 1 1 0 000-2z"/>
</svg>'

// pin
@pin, pin_to_start, pin_to_taskbar, pin_to_quick_access, pin_current_folder_to_quick_access, pin_to_start_menu='<svg fill="none" viewBox="0 0 16 16">
  <path fill="@image.color1" d="M5.27 11.43L.70 16l-.00-.00H.00v-.70L0 15.29l4.56-4.56.70.70z"/>
  <path fill="@color3" d="M8.40 1.31l-1.73 3.82a1 1 0 01-.53.51L1.5 7.5l7 7 1.36-4.54a1 1 0 01.57-.63l4.18-1.74a1 1 0 00.32-1.63L10.02 1.02a1 1 0 00-1.61.29z"/>
  <path fill="@image.color2" fill-rule="evenodd" d="M9.04.25a1.5 1.5 0 011.32.41l4.92 4.92a1.50 1.50 0 01-.48 2.44l-4.18 1.74a.5.5 0 00-.28.31l-1.36 4.54a.5.5 0 01-.83.21l-7-7a.5.5 0 01.16-.81l4.62-1.85a.5.5 0 00.27-.25m0 0l1.73-3.82A1.5 1.5 0 019.04.25m.41.99a.5.5 0 00-.60.27L7.12 5.34c-.16.35-.45.62-.80.77L2.39 7.68l5.87 5.87L9.38 9.81a1.5 1.5 0 01.86-.95l4.18-1.74a.5.5 0 00.16-.81L9.66 1.37" clip-rule="evenodd"/>
</svg>'

// unpin
@unpin, unpin_from_start, unpin_from_taskbar, unpin_from_quick_access, remove_from_quick_access, remove_from_recent, unpin_from_start_menu='<svg fill="none" viewBox="0 0 16 16">
  <g clip-path="url(#clip0)">
    <path fill="@image.color1" d="M5.27 11.43L.70 16l-.00-.00H.00v-.70L0 15.29l4.56-4.56.70.70z"/>
    <path fill="@color3" d="M5.28 5.98L1.5 7.5l7 7 1.22-4.07-4.44-4.44zM11.14 9.02l3.47-1.44a1 1 0 00.32-1.63L10.02 1.02a1 1 0 00-1.61.29L6.85 4.72l4.29 4.29zM5.78 5.78l4.1 4.1a1 1 0 01.29-.41L6.28 5.57a1 1 0 01-.15.07l-.34.13z"/>
    <g>
      <path fill="@image.color1" d="M4.89 5.60L1.31 7.03a.5.5 0 00-.16.81l7 7a.5.5 0 00.83-.21l1.14-3.81-.80-.80-1.05 3.52L2.39 7.68l3.27-1.31-.77-.77zM9.51 9.51l.77.77.05-.18a.5.5 0 01.19-.26l-.71-.71a1.5 1.5 0 00-.31.39zM6.64 5.93a1.5 1.5 0 01-.32.18l-.14.05-.76-.76.50-.20.73.73zM10.76 8.64l3.66-1.52a.5.5 0 00.16-.81L9.66 1.37l-.20-.12a.5.5 0 00-.60.27l-1.63 3.58-.75-.75 1.47-3.24a1.5 1.5 0 012.42-.44l4.92 4.92a1.50 1.50 0 01-.48 2.44l-3.28 1.37-.76-.76z"/>
    </g>
    <path fill="@image.color2" d="M.14.14a.5.5 0 01.70 0l15 15a.5.5 0 01-.70.70l-15-15a.5.5 0 010-.70z"/>
  </g>
  @clipPath
</svg>'

@add_to_favorites='<svg fill="none" viewBox="0 0 16 16">
  <path fill="@color3" d="M10.3 5.6L8 .9 5.7 5.6l-5.2.7L4.25 10l-.86 5.10L8 12.68l4.60 2.42-.86-5.10L15.5 6.3l-5.2-.7z"/>
  <path fill="@image.color2" d="M8 2.01l1.74 **********.52.07 3.91.56-2.83 2.75-.***********.66 3.89-3.49-1.83L8 12.12l-.46.24-3.49 1.83.66-3.89.08-.51-.37-.36L1.59 6.66 5.5 6.1l.52-.07.23-.47L8 2.01zM7.99.38a.49.49 0 00-.44.27l-2.19 4.45-4.91.71a.5.5 0 00-.27.85l3.55 3.46-.83 4.89a.50.50 0 00.49.58c.08 0 .16-.02.23-.05L8 13.25l4.39 2.31a.49.49 0 00.61-.12.50.50 0 00.11-.40l-.84-4.89 3.55-3.46a.5.5 0 00-.27-.85l-4.91-.71L8.44.65a.49.49 0 00-.45-.27z"/>
</svg>'

@remove_from_favorites='<svg fill="none" viewBox="0 0 16 16">
  <g clip-path="url(#clip0)">
    <path fill="@color3" d="M4.98 5.69L.5 6.3 4.25 10l-.86 5.10L8 12.68l4.60 2.42-.36-2.15-7.25-7.25zM11.93 9.81L15.5 6.3l-5.2-.7L8 .9 6.36 4.24l5.57 5.57zM5.61 5.61l6.49 6.49-.14-.85-6.06-6.06L5.7 5.6l-.08.01z"/>
    <g>
      <path fill="@image.color1" d="M4.52 5.23l-4.08.59a.5.5 0 00-.27.85l3.55 3.46-.83 4.89a.50.50 0 00.49.58c.08 0 .16-.02.23-.05L8 13.25l4.39 2.31a.49.49 0 00.61-.12.50.50 0 00.11-.40l-.25-1.45-1.22-1.22.31 1.85L8 12.12l-3.96 2.08.75-4.41-3.20-3.12 3.81-.55-.88-.88zM11.49 11.49l1.22 1.22-.14-.85-1.22-1.22.14.85zM6.25 5.54l-.23.47-.88-.88.21-.03.15-.31.74.74zM11.56 9.44l2.84-2.77-4.43-.64L8 2.01 6.72 4.6l-.74-.74L7.55.65A.49.49 0 017.99.38a.49.49 0 01.45.27l2.19 4.45 4.91.71a.5.5 0 01.27.85l-3.55 **********-.72-.71z"/>
    </g>
    <path fill="@image.color2" d="M.14.14a.5.5 0 01.70 0l15 15a.5.5 0 01-.70.70l-15-15a.5.5 0 010-.70z"/>
  </g>
  @clipPath
</svg>'

@delete, permanently_delete, empty_recycle_bin='<svg fill="none" viewBox="0 0 16 16">
  <path fill="@color3" d="M14 2.5H2l1.34 11.67a1.5 1.5 0 001.49 1.32h6.32a1.5 1.5 0 001.49-1.32L14 2.5z"/>
  <path fill="@image.color1" d="M15 2a.5.5 0 010 1h-.55L13.15 14.23A2 2 0 0111.16 16H4.83a2 2 0 01-1.98-1.77L1.55 3H1a.5.5 0 01-.49-.41L.5 2.5A.5.5 0 011 2h14zm-1.56 1H2.56l1.28 11.11a1 1 0 00.99.88h6.32a1 1 0 00.99-.88L13.43 3z"/>
  <path fill="@image.color2" d="M8 1a1.5 1.5 0 00-1.5 1.5h-1a2.5 2.5 0 015 0h-1A1.5 1.5 0 008 1zM6.5 6a.5.5 0 01.5.5v5a.5.5 0 01-1 0v-5a.5.5 0 01.5-.5zM9.5 6a.5.5 0 01.5.5v5a.5.5 0 01-1 0v-5a.5.5 0 01.5-.5z"/>
</svg>'

@sort_by='<svg fill="none" viewBox="0 0 16 16">
  <g clip-path="url(#clip0)">
    <path fill="@image.color2" fill-rule="evenodd" d="M11.5 2a.5.5 0 01.5.5v11a.5.5 0 01-1 0v-11a.5.5 0 01.5-.5z" clip-rule="evenodd"/>
    <path fill="@image.color2" fill-rule="evenodd" d="M7.14 9.14a.5.5 0 01.70 0l3.64 3.64 3.64-3.64a.5.5 0 01.70.70l-4 4a.5.5 0 01-.70 0l-4-4a.5.5 0 010-.70z" clip-rule="evenodd"/>
    <path fill="@image.color1" fill-rule="evenodd" d="M4.5 2a.5.5 0 01.5.5v11a.5.5 0 01-1 0v-11a.5.5 0 01.5-.5z" clip-rule="evenodd"/>
    <path fill="@image.color1" fill-rule="evenodd" d="M4.14 2.14a.5.5 0 01.70 0l4 4a.5.5 0 11-.70.70L4.5 3.20.85 6.85a.5.5 0 11-.70-.70l4-4z" clip-rule="evenodd"/>
  </g>
  @clipPath
</svg>'

@group_by='<svg fill="none" viewBox="0 0 16 16">
  <path fill="@image.color1" d="M5 2.5a.5.5 0 01.5-.5h9a.5.5 0 010 1h-9a.5.5 0 01-.5-.5zm0 3a.5.5 0 01.5-.5h9a.5.5 0 010 1h-9a.5.5 0 01-.5-.5zm0 5a.5.5 0 01.5-.5h9a.5.5 0 010 1h-9a.5.5 0 01-.5-.5zm0 3a.5.5 0 01.5-.5h9a.5.5 0 010 1h-9a.5.5 0 01-.5-.5z"/>
  <path fill="@image.color2" d="M1 2.5A1.5 1.5 0 012.5 1h1a.5.5 0 010 1h-1a.5.5 0 00-.5.5v3a.5.5 0 00.5.5h1a.5.5 0 010 1h-1A1.5 1.5 0 011 5.5v-3zm0 8A1.5 1.5 0 012.5 9h1a.5.5 0 010 1h-1a.5.5 0 00-.5.5v3a.5.5 0 00.5.5h1a.5.5 0 010 1h-1A1.5 1.5 0 011 13.5v-3z"/>
</svg>'

@view='<svg fill="none" viewBox="0 0 16 16">
  <path fill="@color3" d="M1.5 3A1.5 1.5 0 013 1.5h2A1.5 1.5 0 016.5 3v2A1.5 1.5 0 015 6.5H3A1.5 1.5 0 011.5 5V3z"/>
  <path fill="@image.color1" fill-rule="evenodd" d="M1 3a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H3a2 2 0 01-2-2V3zm2-1a1 1 0 00-1 1v2a1 1 0 001 1h2a1 1 0 001-1V3a1 1 0 00-1-1H3z" clip-rule="evenodd"/>
  <path fill="@color3" d="M9.5 3A1.5 1.5 0 0111 1.5h2A1.5 1.5 0 0114.5 3v2A1.5 1.5 0 0113 6.5h-2A1.5 1.5 0 019.5 5V3z"/>
  <path fill="@image.color1" fill-rule="evenodd" d="M9 3a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V3zm2-1a1 1 0 00-1 1v2a1 1 0 001 1h2a1 1 0 001-1V3a1 1 0 00-1-1h-2z" clip-rule="evenodd"/>
  <path fill="@color3" d="M1.5 11A1.5 1.5 0 013 9.5h2A1.5 1.5 0 016.5 11v2A1.5 1.5 0 015 14.5H3A1.5 1.5 0 011.5 13v-2z"/>
  <path fill="@image.color1" fill-rule="evenodd" d="M1 11a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H3a2 2 0 01-2-2v-2zm2-1a1 1 0 00-1 1v2a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 00-1-1H3z" clip-rule="evenodd"/>
  <path fill="@color3" d="M9.5 11A1.5 1.5 0 0111 9.5h2a1.5 1.5 0 011.5 1.5v2a1.5 1.5 0 01-1.5 1.5h-2A1.5 1.5 0 019.5 13v-2z"/>
  <path fill="@image.color1" fill-rule="evenodd" d="M9 11a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2zm2-1a1 1 0 00-1 1v2a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 00-1-1h-2z" clip-rule="evenodd"/>
</svg>'

@view2='<svg fill="none" viewBox="0 0 16 16">
	<path fill="@image.color1" d="M0.2344 9.516C0.2344 9.484 0.2396 9.443 0.25 9.391C0.4167 8.787 0.6302 8.208 0.8906 7.656C0.8906 7.656 1.151 7.104 1.461 6.591C1.461 6.591 1.82 6.117 2.18 5.643C2.18 5.643 2.583 5.214 3.031 4.828C3.031 4.828 3.479 4.443 3.964 4.115C3.964 4.115 4.484 3.844 5.005 3.573C5.005 3.573 5.56 3.365 6.148 3.219C6.148 3.219 6.737 3.073 7.354 3C8.75 3 9.474 3.107 10.17 3.32C10.17 3.32 10.87 3.534 11.52 3.833C11.52 3.833 12.12 4.219 12.73 4.604C12.73 4.604 13.28 5.065 13.77 5.602C13.77 5.602 14.27 6.138 14.69 6.734C15.08 7.495 15.15 7.643 15.23 7.836C15.23 7.836 15.32 8.029 15.4 8.232C15.4 8.232 15.48 8.445 15.55 8.659C15.55 8.659 15.62 8.862 15.68 9.055C15.68 9.055 15.74 9.247 15.77 9.396C15.77 9.635 15.72 9.753 15.62 9.852C15.62 9.852 15.52 9.95 15.4 10C15.13 10 15.03 9.963 14.96 9.891C14.96 9.891 14.89 9.818 14.83 9.724C14.73 9.495 14.69 9.372 14.66 9.242C14.66 9.242 14.64 9.112 14.6 8.984C14.44 8.474 14.27 8.096 14.07 7.727C14.07 7.727 13.87 7.357 13.64 7.016C13.06 6.276 12.69 5.896 12.28 5.562C12.28 5.562 11.88 5.229 11.44 4.945C11.44 4.945 10.98 4.711 10.51 4.477C10.51 4.477 10.03 4.3 9.516 4.18C9.516 4.18 9.005 4.06 8.474 4C7.276 4 6.654 4.096 6.055 4.289C6.055 4.289 5.456 4.482 4.896 4.747C4.896 4.747 4.375 5.086 3.854 5.425C3.854 5.425 3.38 5.831 2.953 6.305C2.953 6.305 2.526 6.779 2.167 7.292C1.833 7.917 1.781 8.021 1.719 8.156C1.719 8.156 1.656 8.292 1.594 8.435C1.594 8.435 1.531 8.586 1.469 8.737C1.469 8.737 1.417 8.88 1.375 9.016C1.375 9.016 1.333 9.151 1.312 9.25C1.312 9.396 1.297 9.477 1.266 9.555C1.266 9.555 1.234 9.633 1.195 9.706C1.195 9.706 1.148 9.773 1.102 9.841C1.102 9.841 1.042 9.896 0.9688 9.938C0.9688 9.938 0.8958 9.979 0.8177 10C0.599 10 0.4818 9.953 0.3828 9.859C0.3828 9.859 0.2839 9.766 0.2344 9.651Z"/>
	<path fill="@image.color2" d="M4.5 9.438C4.5 8.969 4.596 8.526 4.789 8.109C4.789 8.109 4.982 7.693 5.237 7.328C5.237 7.328 5.555 7.016 5.872 6.703C5.872 6.703 6.242 6.456 6.664 6.273C6.664 6.273 7.086 6.091 7.531 6C8.479 6 8.932 6.091 9.359 6.273C9.359 6.273 9.787 6.456 10.16 6.706C10.16 6.706 10.48 7.023 10.79 7.341C10.79 7.341 11.04 7.714 11.23 8.141C11.23 8.141 11.41 8.568 11.5 9.021C11.5 9.979 11.41 10.43 11.23 10.85C11.23 10.85 11.04 11.27 10.79 11.64C10.79 11.64 10.48 11.96 10.16 12.28C10.16 12.28 9.787 12.53 9.359 12.72C9.359 12.72 8.932 12.91 8.479 13C7.51 13 7.052 12.91 6.625 12.72C6.625 12.72 6.198 12.53 5.828 12.28C5.828 12.28 5.516 11.95 5.203 11.63C5.203 11.63 4.956 11.25 4.773 10.82C4.773 10.82 4.591 10.39 4.5 9.927ZM10.5 9.438C10.5 9.104 10.43 8.789 10.3 8.492C10.3 8.492 10.16 8.195 9.979 7.935C9.979 7.935 9.75 7.711 9.521 7.487C9.521 7.487 9.255 7.312 8.953 7.188C8.953 7.188 8.651 7.062 8.333 7C7.656 7 7.333 7.065 7.031 7.195C7.031 7.195 6.729 7.326 6.464 7.503C6.464 7.503 6.234 7.727 6.005 7.951C6.005 7.951 5.825 8.213 5.695 8.516C5.695 8.516 5.565 8.818 5.5 9.146C5.5 9.844 5.565 10.17 5.695 10.47C5.695 10.47 5.825 10.77 6.005 11.04C6.005 11.04 6.234 11.27 6.464 11.49C6.464 11.49 6.729 11.67 7.031 11.8C7.031 11.8 7.333 11.93 7.656 12C8.365 12 8.698 11.93 9 11.8C9 11.8 9.302 11.66 9.565 11.48C9.565 11.48 9.789 11.25 10.01 11.02C10.01 11.02 10.19 10.75 10.31 10.44C10.31 10.44 10.44 10.12 10.5 9.792Z"/>
</svg>'

@align_icons_to_grid='<svg fill="none" viewBox="0 0 16 16">
  <path fill="@image.color2" d="M2.5 0a.5.5 0 01.5.5V2h12.5a.5.5 0 010 1H3v12.5a.5.5 0 01-1 0V3H.5a.5.5 0 010-1H2V.5a.5.5 0 01.5-.5z"/>
  <path fill="@color3" d="M4.5 12.5v-7a1 1 0 011-1h7a1 1 0 011 1v7a1 1 0 01-1 1h-7a1 1 0 01-1-1z"/>
  <path fill="@image.color1" d="M5.5 5a.5.5 0 00-.5.5v7a.5.5 0 00.5.5h7a.5.5 0 00.5-.5v-7a.5.5 0 00-.5-.5h-7zM4 5.5A1.5 1.5 0 015.5 4h7A1.5 1.5 0 0114 5.5v7a1.5 1.5 0 01-1.5 1.5h-7A1.5 1.5 0 014 12.5v-7z"/>
</svg>'

@auto_arrange_icons='<svg fill="none" viewBox="0 0 16 16">
  <path fill="@image.color2" d="M6.5 10H4.75a.5.5 0 01-.46-.69l2.08-5A.5.5 0 016.83 4h3.47a.5.5 0 01.47.65L10 7h1.09a.75.75 0 01.51 1.29l-4.87 4.54c-.36.34-.94-.00-.82-.48L6.5 10z"/>
  <path fill="@image.color1" d="M1.5 0a.5.5 0 01.5.5V1h12V.5a.5.5 0 011 0V1h.5a.5.5 0 010 1H15v12h.5a.5.5 0 010 1H15v.5a.5.5 0 01-1 0V15H2v.5a.5.5 0 01-1 0V15H.5a.5.5 0 010-1H1V2H.5a.5.5 0 010-1H1V.5a.5.5 0 01.5-.5zM2 2v12h12V2H2z"/>
</svg>'

@close, cancel='<svg fill="none" viewBox="0 0 16 16">
  <path fill="@image.color2" d="M1.85 1.14a.5.5 0 10-.70.70L7.29 8l-6.14 6.14a.5.5 0 00.70.70L8 8.70l6.14 6.14a.5.5 0 00.70-.70L8.70 8l6.14-6.14a.5.5 0 00-.70-.70L8 7.29 1.85 1.14z"/>
</svg>'

@expand='<svg fill="none" viewBox="0 0 16 16">
  <path fill="@color3" d="M11 1.5H4A2.5 2.5 0 001.5 4v7A2.5 2.5 0 004 13.5h7a2.5 2.5 0 002.5-2.5V4A2.5 2.5 0 0011 1.5z"/>
  <path fill="@image.color1" d="M1 4a3 3 0 013-3h7a3 3 0 013 3v7a3 3 0 01-3 3H4a3 3 0 01-3-3V4zm3-2a2 2 0 00-2 2v7a2 2 0 002 2h7a2 2 0 002-2V4a2 2 0 00-2-2H4z"/>
  <path fill="@image.color2" d="M7.5 4a.5.5 0 01.5.5V7h2.5a.5.5 0 010 1H8v2.5a.5.5 0 01-1 0V8H4.5a.5.5 0 010-1H7V4.5a.5.5 0 01.5-.5z"/>
</svg>'

@expand_all, expand_group, expand_all_groups='<svg fill="none" viewBox="0 0 16 16">
  <g clip-path="url(#clip0)">
    <path fill="@color3" d="M11 1.5H4A2.5 2.5 0 001.5 4v7A2.5 2.5 0 004 13.5h7a2.5 2.5 0 002.5-2.5V4A2.5 2.5 0 0011 1.5z"/>
    <path fill="@image.color1" d="M3.76 15c.54.61 1.34 1 2.23 1h6a4 4 0 004-4V6c0-.88-.38-1.68-1-2.23V12a3 3 0 01-3 3H3.76z"/>
    <path fill="@image.color1" d="M1 4a3 3 0 013-3h7a3 3 0 013 3v7a3 3 0 01-3 3H4a3 3 0 01-3-3V4zm3-2a2 2 0 00-2 2v7a2 2 0 002 2h7a2 2 0 002-2V4a2 2 0 00-2-2H4z"/>
    <path fill="@image.color2" d="M7.5 4a.5.5 0 01.5.5V7h2.5a.5.5 0 010 1H8v2.5a.5.5 0 01-1 0V8H4.5a.5.5 0 010-1H7V4.5a.5.5 0 01.5-.5z"/>
  </g>
  @clipPath
</svg>'

@collapse='<svg fill="none" viewBox="0 0 16 16">
  <path fill="@color3" d="M11 1.5H4A2.5 2.5 0 001.5 4v7A2.5 2.5 0 004 13.5h7a2.5 2.5 0 002.5-2.5V4A2.5 2.5 0 0011 1.5z"/>
  <path fill="@image.color1" d="M1 4a3 3 0 013-3h7a3 3 0 013 3v7a3 3 0 01-3 3H4a3 3 0 01-3-3V4zm3-2a2 2 0 00-2 2v7a2 2 0 002 2h7a2 2 0 002-2V4a2 2 0 00-2-2H4z"/>
  <path fill="@image.color2" d="M4 7.5a.5.5 0 01.5-.5h6a.5.5 0 010 1h-6a.5.5 0 01-.5-.5z"/>
</svg>'

@collapse_all, collapse_group, collapse_all_groups='<svg fill="none" viewBox="0 0 16 16">
  <g clip-path="url(#clip0)">
    <path fill="@color3" d="M11 1.5H4A2.5 2.5 0 001.5 4v7A2.5 2.5 0 004 13.5h7a2.5 2.5 0 002.5-2.5V4A2.5 2.5 0 0011 1.5z"/>
    <path fill="@image.color1" fill-rule="evenodd" d="M3.76 15c.55.61 1.34 1 2.23 1h6a4 4 0 004-4V6c0-.88-.38-1.68-1-2.23V12a3 3 0 01-3 3H3.76z" clip-rule="evenodd"/>
    <path fill="@image.color1" fill-rule="evenodd" d="M1 4a3 3 0 013-3h7a3 3 0 013 3v7a3 3 0 01-3 3H4a3 3 0 01-3-3V4zm3-2a2 2 0 00-2 2v7a2 2 0 002 2h7a2 2 0 002-2V4a2 2 0 00-2-2H4z" clip-rule="evenodd"/>
    <path fill="@image.color2" fill-rule="evenodd" d="M4 7.5a.5.5 0 01.5-.5h6a.5.5 0 010 1h-6a.5.5 0 01-.5-.5z" clip-rule="evenodd"/>
  </g>
  @clipPath
</svg>'

@format='<svg fill="none" viewBox="0 0 16 16">
  <g clip-path="url(#clip0_3639_261)">
    <path fill="@color3" d="M5.55 8.5H2A1.5 1.5 0 00.5 10v2A1.5 1.5 0 002 13.5h12a1.5 1.5 0 001.5-1.5v-2A1.5 1.5 0 0014 8.5h-3.55c-.63.61-1.49 1-2.45 1a3.48 3.48 0 01-2.45-1z"/>
    <path fill="@image.color1" d="M2 9a1 1 0 00-1 1v2a1 1 0 001 1h12a1 1 0 001-1v-2a1 1 0 00-1-1h-3.5V8H14a2 2 0 012 2v2a2 2 0 01-2 2H2a2 2 0 01-2-2v-2a2 2 0 012-2h3.5v1H2z"/>
    <path fill="@image.color1" d="M13 10.75a.75.75 0 11-1.5 0 .75.75 0 011.5 0z"/>
    <path fill="@image.color2" d="M5.85.14a.5.5 0 010 .70L4.70 2H8a4 4 0 11-4 4 .5.5 0 011 0 3 3 0 103-3H4.70l1.14 1.14a.5.5 0 11-.70.70l-2-2a.5.5 0 010-.70l2-2a.5.5 0 01.70 0z"/>
  </g>
  <defs>
    <clipPath id="clip0_3639_261">
      <path fill="#fff" d="M0 0h16v16H0z"/>
    </clipPath>
  </defs>
</svg>'

@eject='<svg fill="none" viewBox="0 0 16 16">
  <g clip-path="url(#clip0)">
    <path fill="@color3" d="M7.37.82L1.76 9.33a.75.75 0 00.62 1.16h11.21a.75.75 0 00.62-1.16L8.62.82a.75.75 0 00-1.25 0z"/>
    <path fill="@image.color2" d="M8.20 1.1a.25.25 0 00-.41 0L2.18 9.61a.25.25 0 00.21.38h11.21a.25.25 0 00.20-.38L8.21 1.10zM6.95.55a1.25 1.25 0 012.08 0l5.60 8.51A1.25 1.25 0 0113.60 11H2.39a1.25 1.25 0 01-1.04-1.93L6.95.55zM1 13.5a.5.5 0 01.5-.5h13a.5.5 0 010 1h-13a.5.5 0 01-.5-.5z"/>
  </g>
  @clipPath
</svg>'

@content='<svg fill="none" viewBox="0 0 16 16">
  <path fill="@color3" d="M1.5 2.5a1 1 0 011-1h2a1 1 0 011 1v1a1 1 0 01-1 1h-2a1 1 0 01-1-1v-1z"/>
  <path fill="@image.color1" d="M1 2.5A1.5 1.5 0 012.5 1h2A1.5 1.5 0 016 2.5v1A1.5 1.5 0 014.5 5h-2A1.5 1.5 0 011 3.5v-1zM2.5 2a.5.5 0 00-.5.5v1a.5.5 0 00.5.5h2a.5.5 0 00.5-.5v-1a.5.5 0 00-.5-.5h-2z"/>
  <path fill="@color3" d="M1.5 7.5a1 1 0 011-1h2a1 1 0 011 1v1a1 1 0 01-1 1h-2a1 1 0 01-1-1v-1z"/>
  <path fill="@image.color1" d="M1 7.5A1.5 1.5 0 012.5 6h2A1.5 1.5 0 016 7.5v1A1.5 1.5 0 014.5 10h-2A1.5 1.5 0 011 8.5v-1zM2.5 7a.5.5 0 00-.5.5v1a.5.5 0 00.5.5h2a.5.5 0 00.5-.5v-1a.5.5 0 00-.5-.5h-2z"/>
  <path fill="@color3" d="M1.5 12.5a1 1 0 011-1h2a1 1 0 011 1v1a1 1 0 01-1 1h-2a1 1 0 01-1-1v-1z"/>
  <path fill="@image.color1" d="M1 12.5A1.5 1.5 0 012.5 11h2A1.5 1.5 0 016 12.5v1A1.5 1.5 0 014.5 15h-2A1.5 1.5 0 011 13.5v-1zm1.5-.5a.5.5 0 00-.5.5v1a.5.5 0 00.5.5h2a.5.5 0 00.5-.5v-1a.5.5 0 00-.5-.5h-2zM8 12.5a.5.5 0 01.5-.5h6a.5.5 0 010 1h-6a.5.5 0 01-.5-.5zM8 7.5a.5.5 0 01.5-.5h6a.5.5 0 010 1h-6a.5.5 0 01-.5-.5zM8 2.5a.5.5 0 01.5-.5h6a.5.5 0 010 1h-6a.5.5 0 01-.5-.5z"/>
</svg>'

@details='<svg fill="none" viewBox="0 0 16 16">
  <path fill="@image.color1" d="M.5 6a.5.5 0 000 1h15a.5.5 0 000-1H.5zM.5 12a.5.5 0 000 1h15a.5.5 0 000-1H.5zM.5 9a.5.5 0 000 1h15a.5.5 0 000-1H.5zM.5 3a.5.5 0 000 1h15a.5.5 0 000-1H.5z"/>
</svg>'

@extra_large_icons='<svg fill="none" viewBox="0 0 16 16">
  <path fill="@color3" d="M1.5 3A1.5 1.5 0 013 1.5h10A1.5 1.5 0 0114.5 3v8a1.5 1.5 0 01-1.5 1.5H3A1.5 1.5 0 011.5 11V3z"/>
  <path fill="@image.color1" d="M1 3a2 2 0 012-2h10a2 2 0 012 2v8a2 2 0 01-2 2H3a2 2 0 01-2-2V3zm2-1a1 1 0 00-1 1v8a1 1 0 001 1h10a1 1 0 001-1V3a1 1 0 00-1-1H3zM3.5 14a.5.5 0 000 1h9a.5.5 0 000-1h-9z"/>
</svg>'

@large_icons='<svg fill="none" viewBox="0 0 16 16">
  <path fill="@color3" d="M2.5 4A1.5 1.5 0 014 2.5h8A1.5 1.5 0 0113.5 4v6a1.5 1.5 0 01-1.5 1.5H4A1.5 1.5 0 012.5 10V4z"/>
  <path fill="@image.color1" d="M2 4a2 2 0 012-2h8a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V4zm2-1a1 1 0 00-1 1v6a1 1 0 001 1h8a1 1 0 001-1V4a1 1 0 00-1-1H4zM2.5 13a.5.5 0 000 1h11a.5.5 0 000-1h-11z"/>
</svg>'

@list='<svg fill="none" viewBox="0 0 16 16">
  <path fill="@image.color1" d="M.5 6a.5.5 0 000 1h6a.5.5 0 000-1h-6zM9.5 6a.5.5 0 000 1h6a.5.5 0 000-1h-6zM.5 12a.5.5 0 000 1h6a.5.5 0 000-1h-6zM9.5 12a.5.5 0 000 1h6a.5.5 0 000-1h-6zM.5 9a.5.5 0 000 1h6a.5.5 0 000-1h-6zM9.5 9a.5.5 0 000 1h6a.5.5 0 000-1h-6zM.5 3a.5.5 0 000 1h6a.5.5 0 000-1h-6zM9.5 3a.5.5 0 000 1h6a.5.5 0 000-1h-6z"/>
</svg>'

@medium_icons='<svg fill="none" viewBox="0 0 16 16">
  <path fill="@color3" d="M3.5 4A1.5 1.5 0 015 2.5h6A1.5 1.5 0 0112.5 4v4A1.5 1.5 0 0111 9.5H5A1.5 1.5 0 013.5 8V4z"/>
  <path fill="@image.color1" d="M3 4a2 2 0 012-2h6a2 2 0 012 2v4a2 2 0 01-2 2H5a2 2 0 01-2-2V4zm2-1a1 1 0 00-1 1v4a1 1 0 001 1h6a1 1 0 001-1V4a1 1 0 00-1-1H5zM4.5 11a.5.5 0 000 1h7a.5.5 0 000-1h-7zM4.5 13a.5.5 0 000 1h7a.5.5 0 000-1h-7z"/>
</svg>'

@small_icons='<svg fill="none" viewBox="0 0 16 16">
  <path fill="@color3" d="M1.5 3A1.5 1.5 0 013 1.5h2A1.5 1.5 0 016.5 3v2A1.5 1.5 0 015 6.5H3A1.5 1.5 0 011.5 5V3z"/>
  <path fill="@image.color1" d="M1 3a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H3a2 2 0 01-2-2V3zm2-1a1 1 0 00-1 1v2a1 1 0 001 1h2a1 1 0 001-1V3a1 1 0 00-1-1H3z"/>
  <path fill="@color3" d="M9.5 3A1.5 1.5 0 0111 1.5h2A1.5 1.5 0 0114.5 3v2A1.5 1.5 0 0113 6.5h-2A1.5 1.5 0 019.5 5V3z"/>
  <path fill="@image.color1" d="M9 3a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V3zm2-1a1 1 0 00-1 1v2a1 1 0 001 1h2a1 1 0 001-1V3a1 1 0 00-1-1h-2z"/>
  <path fill="@color3" d="M1.5 11A1.5 1.5 0 013 9.5h2A1.5 1.5 0 016.5 11v2A1.5 1.5 0 015 14.5H3A1.5 1.5 0 011.5 13v-2z"/>
  <path fill="@image.color1" d="M1 11a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H3a2 2 0 01-2-2v-2zm2-1a1 1 0 00-1 1v2a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 00-1-1H3z"/>
  <path fill="@color3" d="M9.5 11A1.5 1.5 0 0111 9.5h2a1.5 1.5 0 011.5 1.5v2a1.5 1.5 0 01-1.5 1.5h-2A1.5 1.5 0 019.5 13v-2z"/>
  <path fill="@image.color1" d="M9 11a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2zm2-1a1 1 0 00-1 1v2a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 00-1-1h-2z"/>
</svg>'

@tiles='<svg fill="none" viewBox="0 0 16 16">
  <path fill="@color3" d="M.5 4A1.5 1.5 0 012 2.5h2A1.5 1.5 0 015.5 4v1A1.5 1.5 0 014 6.5H2A1.5 1.5 0 01.5 5V4z"/>
  <path fill="@image.color1" d="M0 4a2 2 0 012-2h2a2 2 0 012 2v1a2 2 0 01-2 2H2a2 2 0 01-2-2V4zm2-1a1 1 0 00-1 1v1a1 1 0 001 1h2a1 1 0 001-1V4a1 1 0 00-1-1H2z"/>
  <path fill="@color3" d="M.5 11A1.5 1.5 0 012 9.5h2A1.5 1.5 0 015.5 11v1A1.5 1.5 0 014 13.5H2A1.5 1.5 0 01.5 12v-1z"/>
  <path fill="@image.color1" d="M0 11a2 2 0 012-2h2a2 2 0 012 2v1a2 2 0 01-2 2H2a2 2 0 01-2-2v-1zm2-1a1 1 0 00-1 1v1a1 1 0 001 1h2a1 1 0 001-1v-1a1 1 0 00-1-1H2zM8 3.5a.5.5 0 01.5-.5h7a.5.5 0 010 1h-7a.5.5 0 01-.5-.5zM8 10.5a.5.5 0 01.5-.5h7a.5.5 0 010 1h-7a.5.5 0 01-.5-.5z"/>
</svg>'
@install='<svg fill="none" viewBox="0 0 16 16">
  <path fill="@image.color2" d="M7.5 0a.5.5 0 01.5.5v10.79l3.14-3.14a.5.5 0 01.70.70l-4 4a.5.5 0 01-.70 0l-4-4a.5.5 0 11.70-.70L7 11.29V.5a.5.5 0 01.5-.5zm-6 12a.5.5 0 01.5.5v1A1.5 1.5 0 003.5 15h8a1.5 1.5 0 001.5-1.5v-1a.5.5 0 011 0v1a2.5 2.5 0 01-2.5 2.5h-8A2.5 2.5 0 011 13.5v-1a.5.5 0 01.5-.5z"/>
</svg>'

@select_all, 'Выбрать все'='<svg fill="none" viewBox="0 0 16 16">
  <path fill="@image.color1" d="M0 2a2 2 0 012-2h.5a.5.5 0 010 1H2a1 1 0 00-1 1v.5a.5.5 0 01-1 0V2zM4.5.5A.5.5 0 015 0h2a.5.5 0 010 1H5a.5.5 0 01-.5-.5zM9 .5a.5.5 0 01.5-.5h2a.5.5 0 010 1h-2A.5.5 0 019 .5zm4 0a.5.5 0 01.5-.5h.5a2 2 0 012 2v.5a.5.5 0 01-1 0V2a1 1 0 00-1-1h-.5a.5.5 0 01-.5-.5zM.5 4a.5.5 0 01.5.5v2a.5.5 0 01-1 0v-2A.5.5 0 01.5 4zm15 0a.5.5 0 01.5.5v2a.5.5 0 01-1 0v-2a.5.5 0 01.5-.5zM.5 8.5A.5.5 0 011 9v2a.5.5 0 01-1 0V9a.5.5 0 01.5-.5zm15 0a.5.5 0 01.5.5v2a.5.5 0 01-1 0V9a.5.5 0 01.5-.5zM.5 13a.5.5 0 01.5.5v.5a1 1 0 001 1h.5a.5.5 0 010 1H2a2 2 0 01-2-2v-.5a.5.5 0 01.5-.5zm15 0a.5.5 0 01.5.5v.5a2 2 0 01-2 2h-.5a.5.5 0 010-1h.5a1 1 0 001-1v-.5a.5.5 0 01.5-.5zm-11 2.5A.5.5 0 015 15h2a.5.5 0 010 1H5a.5.5 0 01-.5-.5zm4.5 0a.5.5 0 01.5-.5h2a.5.5 0 010 1h-2a.5.5 0 01-.5-.5z"/>
  <path fill="@image.color2" d="M5.5 2.5h-2a1 1 0 00-1 1v2a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 00-1-1zM5.5 9.5h-2a1 1 0 00-1 1v2a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 00-1-1zM12.5 2.5h-2a1 1 0 00-1 1v2a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 00-1-1zM12.5 9.5h-2a1 1 0 00-1 1v2a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 00-1-1z"/>
  <path fill="@image.color2" d="M2 3.5A1.5 1.5 0 013.5 2h2A1.5 1.5 0 017 3.5v2A1.5 1.5 0 015.5 7h-2A1.5 1.5 0 012 5.5v-2zM3.5 3a.5.5 0 00-.5.5v2a.5.5 0 00.5.5h2a.5.5 0 00.5-.5v-2a.5.5 0 00-.5-.5h-2zm5.5.5A1.5 1.5 0 0110.5 2h2A1.5 1.5 0 0114 3.5v2A1.5 1.5 0 0112.5 7h-2A1.5 1.5 0 019 5.5v-2zm1.5-.5a.5.5 0 00-.5.5v2a.5.5 0 00.5.5h2a.5.5 0 00.5-.5v-2a.5.5 0 00-.5-.5h-2zM2 10.5A1.5 1.5 0 013.5 9h2A1.5 1.5 0 017 10.5v2A1.5 1.5 0 015.5 14h-2A1.5 1.5 0 012 12.5v-2zm1.5-.5a.5.5 0 00-.5.5v2a.5.5 0 00.5.5h2a.5.5 0 00.5-.5v-2a.5.5 0 00-.5-.5h-2zm5.5.5A1.5 1.5 0 0110.5 9h2a1.5 1.5 0 011.5 1.5v2a1.5 1.5 0 01-1.5 1.5h-2A1.5 1.5 0 019 12.5v-2zm1.5-.5a.5.5 0 00-.5.5v2a.5.5 0 00.5.5h2a.5.5 0 00.5-.5v-2a.5.5 0 00-.5-.5h-2z"/>
</svg>'

@invert_selection='<svg fill="none" viewBox="0 0 16 16">
  <path fill="@image.color2" d="M5.5 9.5h-2a1 1 0 00-1 1v2a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 00-1-1zM12.5 2.5h-2a1 1 0 00-1 1v2a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 00-1-1z"/>
  <path fill="@image.color2" d="M9 3.5A1.5 1.5 0 0110.5 2h2A1.5 1.5 0 0114 3.5v2A1.5 1.5 0 0112.5 7h-2A1.5 1.5 0 019 5.5v-2zm1.5-.5a.5.5 0 00-.5.5v2a.5.5 0 00.5.5h2a.5.5 0 00.5-.5v-2a.5.5 0 00-.5-.5h-2zM2 10.5A1.5 1.5 0 013.5 9h2A1.5 1.5 0 017 10.5v2A1.5 1.5 0 015.5 14h-2A1.5 1.5 0 012 12.5v-2zm1.5-.5a.5.5 0 00-.5.5v2a.5.5 0 00.5.5h2a.5.5 0 00.5-.5v-2a.5.5 0 00-.5-.5h-2z"/>
  <path fill="@color3" d="M4.5 1.5h-2a1 1 0 00-1 1v2a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 00-1-1zM13.5 10.5h-2a1 1 0 00-1 1v2a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 00-1-1z"/>
  <path fill="@image.color1" d="M1 2.5A1.5 1.5 0 012.5 1h2A1.5 1.5 0 016 2.5v2A1.5 1.5 0 014.5 6h-2A1.5 1.5 0 011 4.5v-2zM2.5 2a.5.5 0 00-.5.5v2a.5.5 0 00.5.5h2a.5.5 0 00.5-.5v-2a.5.5 0 00-.5-.5h-2zm7.5 9.5a1.5 1.5 0 011.5-1.5h2a1.5 1.5 0 011.5 1.5v2a1.5 1.5 0 01-1.5 1.5h-2a1.5 1.5 0 01-1.5-1.5v-2zm1.5-.5a.5.5 0 00-.5.5v2a.5.5 0 00.5.5h2a.5.5 0 00.5-.5v-2a.5.5 0 00-.5-.5h-2zM0 8.25C0 7.56.56 7 1.25 7h.25a.5.5 0 010 1h-.25a.25.25 0 00-.25.25v.25a.5.5 0 01-1 0v-.25zm3-.75a.5.5 0 01.5-.5h2a.5.5 0 010 1h-2a.5.5 0 01-.5-.5zm4 0a.5.5 0 01.5-.5h.25C8.44 7 9 7.56 9 8.25v.25a.5.5 0 01-1 0v-.25A.25.25 0 007.75 8H7.5a.5.5 0 01-.5-.5zM.5 10a.5.5 0 01.5.5v2a.5.5 0 01-1 0v-2a.5.5 0 01.5-.5zm8 0a.5.5 0 01.5.5v2a.5.5 0 01-1 0v-2a.5.5 0 01.5-.5zm-8 4a.5.5 0 01.5.5v.25c0 .13.11.25.25.25h.25a.5.5 0 010 1h-.25C.56 16 0 15.44 0 14.75v-.25a.5.5 0 01.5-.5zm8 0a.5.5 0 01.5.5v.25C9 15.44 8.44 16 7.75 16H7.5a.5.5 0 010-1h.25a.25.25 0 00.25-.25v-.25a.5.5 0 01.5-.5zM3 15.5a.5.5 0 01.5-.5h2a.5.5 0 010 1h-2a.5.5 0 01-.5-.5zM7 1.25C7 .56 7.56 0 8.25 0h.25a.5.5 0 010 1h-.25a.25.25 0 00-.25.25v.25a.5.5 0 01-1 0v-.25zm3-.75a.5.5 0 01.5-.5h2a.5.5 0 010 1h-2a.5.5 0 01-.5-.5zm4 0a.5.5 0 01.5-.5h.25C15.44 0 16 .56 16 1.25v.25a.5.5 0 01-1 0v-.25a.25.25 0 00-.25-.25h-.25a.5.5 0 01-.5-.5zM7.5 3a.5.5 0 01.5.5v2a.5.5 0 01-1 0v-2a.5.5 0 01.5-.5zm8 0a.5.5 0 01.5.5v2a.5.5 0 01-1 0v-2a.5.5 0 01.5-.5zm0 4a.5.5 0 01.5.5v.25C16 8.44 15.44 9 14.75 9h-.25a.5.5 0 010-1h.25a.25.25 0 00.25-.25V7.5a.5.5 0 01.5-.5zM10 8.5a.5.5 0 01.5-.5h2a.5.5 0 010 1h-2a.5.5 0 01-.5-.5z"/>
</svg>'

@select_none='<svg fill="none" viewBox="0 0 16 16">
  <path fill="@color3" d="M5.5 1.5h-3a1 1 0 00-1 1v3a1 1 0 001 1h3a1 1 0 001-1v-3a1 1 0 00-1-1zM13.5 1.5h-3a1 1 0 00-1 1v3a1 1 0 001 1h3a1 1 0 001-1v-3a1 1 0 00-1-1zM13.5 9.5h-3a1 1 0 00-1 1v3a1 1 0 001 1h3a1 1 0 001-1v-3a1 1 0 00-1-1zM6.5 13.5v-3a1 1 0 00-1-1h-3a1 1 0 00-1 1v3a1 1 0 001 1h3a1 1 0 001-1z"/>
  <path fill="@image.color1" d="M1 2.5A1.5 1.5 0 012.5 1h3A1.5 1.5 0 017 2.5v3A1.5 1.5 0 015.5 7h-3A1.5 1.5 0 011 5.5v-3zM2.5 2a.5.5 0 00-.5.5v3a.5.5 0 00.5.5h3a.5.5 0 00.5-.5v-3a.5.5 0 00-.5-.5h-3zm6.5.5A1.5 1.5 0 0110.5 1h3A1.5 1.5 0 0115 2.5v3A1.5 1.5 0 0113.5 7h-3A1.5 1.5 0 019 5.5v-3zm1.5-.5a.5.5 0 00-.5.5v3a.5.5 0 00.5.5h3a.5.5 0 00.5-.5v-3a.5.5 0 00-.5-.5h-3zm-8 8a.5.5 0 00-.5.5v3a.5.5 0 00.5.5h3a.5.5 0 00.5-.5v-3a.5.5 0 00-.5-.5h-3zm-1.5.5A1.5 1.5 0 012.5 9h3A1.5 1.5 0 017 10.5v3A1.5 1.5 0 015.5 15h-3A1.5 1.5 0 011 13.5v-3zm8 0A1.5 1.5 0 0110.5 9h3a1.5 1.5 0 011.5 1.5v3a1.5 1.5 0 01-1.5 1.5h-3A1.5 1.5 0 019 13.5v-3zm1.5-.5a.5.5 0 00-.5.5v3a.5.5 0 00.5.5h3a.5.5 0 00.5-.5v-3a.5.5 0 00-.5-.5h-3z"/>
</svg>'

@share, share_with='<svg fill="none" viewBox="0 0 16 16">
  <path fill="@image.color1" d="M1 4.5A2.5 2.5 0 013.5 2h3a.5.5 0 010 1h-3A1.5 1.5 0 002 4.5v8A1.5 1.5 0 003.5 14h8a1.5 1.5 0 001.5-1.5v-1a.5.5 0 011 0v1a2.5 2.5 0 01-2.5 2.5h-8A2.5 2.5 0 011 12.5v-8z"/>
  <path fill="@color3" d="M15.11 6L10.5 1.5v2.67c-5.10 0-6.35 4.32-6.35 6.32 2.53-2.23 4.5-2.53 6.38-2.72L10.5 10.5 15.11 6z"/>
  <path fill="@image.color2" d="M10.95 10.83l4.49-4.42.05-.06a.58.58 0 00-.05-.76l-4.49-4.42-.06-.05a.56.56 0 00-.89.46v2.13l-.22.01c-3.56.28-5.64 2.53-6.14 6.62a.57.57 0 00.95.48A9.61 9.61 0 019 8.46a7.67 7.67 0 01.74-.11L10 8.32v2.09l.00.08a.56.56 0 00.94.33zM9.85 4.72L11 4.64V2.61l3.44 3.38L11 9.39V7.23l-1.35.12h-.00a9.72 9.72 0 00-4.82 2.00 6.48 6.48 0 011.47-3.06A5.2 5.2 0 019.84 4.72z"/>
</svg>'

@mount='<svg fill="none" viewBox="0 0 16 16">
  <g clip-path="url(#clip0)">
    <path fill="@color3" d="M9.89 6.5A5.75 5.75 0 01.5 8.63V10A1.5 1.5 0 002 11.5h12a1.5 1.5 0 001.5-1.5V8A1.5 1.5 0 0014 6.5H9.89z"/>
    <path fill="@image.color1" d="M9.68 7H14a1 1 0 011 1v2a1 1 0 01-1 1H2a1 1 0 01-1-1v-.93a5.78 5.78 0 01-1-.98V10a2 2 0 002 2h12a2 2 0 002-2V8a2 2 0 00-2-2h-3.94c-.09.34-.21.68-.37 1z"/>
    <path fill="@image.color1" d="M13 8.75a.75.75 0 11-1.5 0 .75.75 0 011.5 0z"/>
    <path fill="@image.color2" d="M0 4.5a4.5 4.5 0 119 0 4.5 4.5 0 01-9 0z"/>
    <path fill="#fff" d="M6.89 4.89a.56.56 0 01-.79 0L4.5 3.29 2.89 4.89a.56.56 0 01-.79-.79l2-2c.22-.22.57-.22.79 0l2 2c.*********** 0 .79z"/>
    <path fill="#fff" d="M4.5 7a.5.5 0 01-.5-.5v-4a.5.5 0 011 0v4a.5.5 0 01-.5.5z"/>
  </g>
  @clipPath
</svg>'

@new, new_item='<svg fill="none" viewBox="0 0 16 16">
  <path fill="@color3" d="M14.5 7.5a7 7 0 11-14 0 7 7 0 0114 0z"/>
  <path fill="@image.color1" d="M7.5 1a6.5 6.5 0 100 13 6.5 6.5 0 000-13zM0 7.5a7.5 7.5 0 1115 0 7.5 7.5 0 01-15 0z"/>
  <path fill="@image.color2" d="M7.5 4a.5.5 0 01.5.5V7h2.5a.5.5 0 010 1H8v2.5a.5.5 0 01-1 0V8H4.5a.5.5 0 010-1H7V4.5a.5.5 0 01.5-.5z"/>
</svg>'

@new_folder='<svg fill="none" viewBox="0 0 16 16">
	<path fill="@color3" d="M2.5 1.5h3a2 2 0 011.6.8L8 3.5h5.5a2 2 0 012 2v1.87a5.75 5.75 0 00-8.90 7.13H2.5a2 2 0 01-2-2v-9a2 2 0 012-2z"/>
	<path fill="@image.color1" d="M0 3.5A2.5 2.5 0 012.5 1h3a2.5 2.5 0 012 1l.75 1h5.25A2.5 2.5 0 0116 5.5v2.42a5.78 5.78 0 00-1-.98V5.5A1.5 1.5 0 0013.5 4H8.15l-.87.58A2.5 2.5 0 015.89 5H1v7.5A1.5 1.5 0 002.5 14h3.82c.***********.61 1H2.5A2.5 2.5 0 010 12.5v-9zM2.5 2A1.5 1.5 0 001 3.5V4h4.89a1.5 1.5 0 00.83-.25l.55-.37L6.7 2.6A1.5 1.5 0 005.5 2h-3z"/>
	<path fill="@image.color2" d="M16 11.5a4.5 4.5 0 11-9 0 4.5 4.5 0 019 0z"/>
	<path fill="@if(theme.islight,'#fff','#000')" d="M11.5 9a.5.5 0 01.5.5V11h1.5a.5.5 0 010 1H12v1.5a.5.5 0 01-1 0V12H9.5a.5.5 0 010-1H11V9.5a.5.5 0 01.5-.5z"/>
</svg>'

@new_file='<svg viewBox="-10 0 2058 2048">
	<path fill="@image.color1" d="M1920 784v1013q0 50 -20.5 95.5t-55 80t-80 55t-95.5 20.5h-507q35 -28 67 -60t59 -68h376q27 0 50 -10t40.5 -27.5t27.5 -40.5t10 -50v-1008v-8t-1 -8h-388q-52 0 -97.5 -21t-79.5 -56t-54 -81.5t-20 -97.5v-383q-4 -1 -8 -1h-8h-496q-27 0 -50 10t-40.5 27.5t-27.5 40.5t-10 50v505q-67 18 -128 48v-558q0 -50 20.5 -95.5t55 -80t80 -55t95.5 -20.5h501q51 0 98 19.5t83 55.5l528 528q36 36 55.5 83t19.5 98zM1280 512q0 27 10 50.5t27 40.5t40.5 27t50.5 10h294l-422 -422v294z" />
	<path fill="@image.color2" d="M126 1462q0 -119 45.5 -224t123.5 -183t183 -123.5t224 -45.5q79 0 152.5 20.5t137.5 58t117 90.5t90.5 117t58 137.5t20.5 152.5q0 119 -45.5 224t-123.5 183t-183 123.5t-224 45.5q-120 0 -225 -45t-183 -123t-123 -183t-45 -225zM766 1526h192q26 0 45 -19t19 -45t-19 -45t-45 -19h-192v-192q0 -26 -19 -45t-45 -19t-45 19t-19 45v192h-192q-26 0 -45 19t-19 45t19 45t45 19h192v192q0 26 19 45t45 19t45 -19t19 -45v-192z"/>
</svg>'

@open_folder,open_folder_location, open_file_location, open_as_portable='<svg fill="none" viewBox="0 0 16 16">
  <path fill="@color3" d="M5.75 1.5H2A1.5 1.5 0 00.5 3v10A1.5 1.5 0 002 14.5h10.94a1.5 1.5 0 001.48-1.29l1.01-7.27a.37.37 0 00-.37-.43.56.56 0 01-.56-.56V4.5a1 1 0 00-1-1H8L6.95 2.1a1.5 1.5 0 00-1.2-.6z"/>
  <path fill="@image.color1" fill-rule="evenodd" d="M0 3a2 2 0 012-2h3.75a2 2 0 011.6.8l.9 1.2H13a2 2 0 012 2v.14a1.5 1.5 0 01.83 1.56l-.91 6.56A2 2 0 0112.94 15H2a2 2 0 01-2-2V3zm14 2a1 1 0 00-1-1H7.75l-1.2-1.6a1 1 0 00-.8-.4H2a1 1 0 00-1 1v9.73l1.26-6.13A2 2 0 014.22 5H14zM1.76 13.97c.07.01.15.02.23.02h10.94a1 1 0 00.99-.86l.91-6.57A.5.5 0 0014.35 6H4.22a1 1 0 00-.97.79l-1.47 7.17z"/>
</svg>'

@open_new_window, open_in_new_window, open_in_new_process='<svg fill="none" viewBox="0 0 16 16">
  <path fill="@image.color2" d="M8 1.5a.5.5 0 01.5-.5h6a.5.5 0 01.5.5v6a.5.5 0 01-1 0V2.70L7.85 8.85a.5.5 0 11-.70-.70L13.29 2H8.5a.5.5 0 01-.5-.5z"/>
  <path fill="@image.color1" d="M1 4.5A2.5 2.5 0 013.5 2h3a.5.5 0 010 1h-3A1.5 1.5 0 002 4.5v8A1.5 1.5 0 003.5 14h8a1.5 1.5 0 001.5-1.5v-3a.5.5 0 011 0v3a2.5 2.5 0 01-2.5 2.5h-8A2.5 2.5 0 011 12.5v-8z"/>
</svg>'

@open_new_tab, open_in_new_tab='<svg fill="none" viewBox="0 0 16 16">
  <path fill="@color3" d="M13 1.5H5A1.5 1.5 0 003.5 3v8A1.5 1.5 0 005 12.5h8a1.5 1.5 0 001.5-1.5V3A1.5 1.5 0 0013 1.5z"/>
  <path fill="@image.color1" fill-rule="evenodd" d="M5 1a2 2 0 00-2 2 2 2 0 00-2 2v7a3 3 0 003 3h7a2 2 0 002-2 2 2 0 002-2V3a2 2 0 00-2-2H5zm7 12H5a2 2 0 01-2-2V4a1 1 0 00-1 1v7a2 2 0 002 2h7a1 1 0 001-1zM4 3a1 1 0 011-1h8a1 1 0 011 1v8a1 1 0 01-1 1H5a1 1 0 01-1-1V3z" clip-rule="evenodd"/>
  <path fill="@image.color2" fill-rule="evenodd" d="M7 4.5a.5.5 0 01.5-.5h4a.5.5 0 01.5.5v4a.5.5 0 01-1 0V5.70L6.85 9.85a.5.5 0 01-.70-.70L10.29 5H7.5a.5.5 0 01-.5-.5z" clip-rule="evenodd"/>
</svg>'

@open_spot_light='<svg fill="none" viewBox="0 0 16 16">
<path d="M13.03 1C13.29 1 13.54 1.05 13.78 1.16C14.02 1.26 14.22 1.41 14.40 1.59C14.58 1.77 14.73 1.97 14.83 2.21C14.94 2.45 15 2.70 15 2.96V11.03C15 11.29 14.94 11.54 14.84 11.78C14.73 12.01 14.59 12.22 14.41 12.40C14.23 12.58 14.03 12.72 13.79 12.83C13.56 12.94 13.31 13 13.06 13H13V13.03C13 13.29 12.94 13.54 12.83 13.78C12.73 14.02 12.58 14.23 12.40 14.41C12.22 14.59 12.02 14.73 11.78 14.84C11.54 14.94 11.29 15 11.03 15H3.93C3.54 15 3.17 14.92 2.81 14.76C2.45 14.60 2.14 14.39 1.87 14.12C1.60 13.85 1.39 13.54 1.23 13.18C1.07 12.82 1 12.45 1 12.06V5C1 4.74 1.04 4.49 1.14 4.25C1.25 4.01 1.39 3.79 1.57 3.61C1.74 3.42 1.95 3.28 2.18 3.17C2.42 3.05 2.67 3 2.93 3H3V2.96C3 2.70 3.05 2.45 3.15 2.21C3.26 1.97 3.40 1.77 3.58 1.59C3.76 1.41 3.97 1.26 4.21 1.16C4.45 1.05 4.70 1 4.96 1H13.03ZM14 3C14 2.86 13.97 2.73 13.92 2.61C13.86 2.49 13.79 2.38 13.70 2.29C13.61 2.20 13.50 2.13 13.38 2.07C13.26 2.02 13.13 2 13 2H5C4.85 2 4.72 2.02 4.60 2.07C4.48 2.13 4.38 2.20 4.28 2.29C4.20 2.38 4.13 2.48 4.07 2.60C4.02 2.72 4 2.85 4 3V11C4 11.14 4.02 11.27 4.07 11.39C4.13 11.51 4.20 11.62 4.28 11.71C4.37 11.79 4.48 11.86 4.60 11.92C4.72 11.97 4.85 12 5 12H13C13.14 12 13.27 11.97 13.39 11.92C13.51 11.86 13.61 11.79 13.70 11.71C13.79 11.61 13.86 11.51 13.92 11.39C13.97 11.27 14 11.14 14 11V3ZM12 13H4.96C4.69 13 4.43 12.94 4.19 12.83C3.96 12.72 3.75 12.58 3.57 12.39C3.40 12.21 3.26 12.00 3.15 11.76C3.05 11.52 3 11.26 3 11V4C2.85 4 2.72 4.02 2.60 4.07C2.48 4.13 2.38 4.20 2.28 4.29C2.20 4.38 2.13 4.48 2.07 4.60C2.02 4.72 2 4.85 2 5V12.03C2 12.29 2.05 12.54 2.15 12.78C2.26 13.02 2.40 13.23 2.58 13.41C2.76 13.59 2.97 13.73 3.21 13.84C3.45 13.94 3.70 14 3.96 14H11C11.14 14 11.27 13.97 11.39 13.92C11.51 13.86 11.61 13.79 11.70 13.71C11.79 13.61 11.86 13.51 11.92 13.39C11.97 13.27 12 13.14 12 13Z" fill="black"/>
<path d="M11.85 4.14C11.75 4.04 11.63 4 11.5 4H7.5C7.36 4 7.24 4.04 7.14 4.14C7.04 4.24 7 4.36 7 4.5C7 4.63 7.04 4.75 7.14 4.85C7.24 4.95 7.36 5 7.5 5H10.29L6.14 9.14C6.04 9.24 6 9.36 6 9.5C6 9.63 6.04 9.75 6.14 9.85C6.24 9.95 6.36 10 6.5 10C6.63 10 6.75 9.95 6.85 9.85L11 5.70V8.5C11 8.63 11.04 8.75 11.14 8.85C11.24 8.95 11.36 9 11.5 9C11.63 9 11.75 8.95 11.85 8.85C11.95 8.75 12 8.63 12 8.5V4.5C12 4.36 11.95 4.24 11.85 4.14Z" fill="#0C59A4"/>
</svg>'

@open_with='<svg fill="none" viewBox="0 0 16 16">
  <g clip-path="url(#clip0)">
    <path fill="@color3" d="M4 1.5H2a.5.5 0 00-.5.5v2a.5.5 0 00.5.5h2a.5.5 0 00.5-.5V2a.5.5 0 00-.5-.5zM4 6.5H2a.5.5 0 00-.5.5v2a.5.5 0 00.5.5h2a.5.5 0 00.5-.5V7a.5.5 0 00-.5-.5zM4 11.5H2a.5.5 0 00-.5.5v2a.5.5 0 00.5.5h2a.5.5 0 00.5-.5v-2a.5.5 0 00-.5-.5z"/>
    <path fill="@image.color1" d="M1 2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 01-1 1H2a1 1 0 01-1-1V2zm3 0H2v2h2V2zM1 7a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 01-1 1H2a1 1 0 01-1-1V7zm3 0H2v2h2V7zm-3 5a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 01-1 1H2a1 1 0 01-1-1v-2zm3 0H2v2h2v-2zM6.5 2a.5.5 0 000 1h8a.5.5 0 000-1h-8zM6.5 7a.5.5 0 000 1h.43c.28-.37.61-.70.98-1H6.5z"/>
    <path fill="@image.color2" d="M7 11.5a4.5 4.5 0 119 0 4.5 4.5 0 01-9 0z"/>
    <path fill="@if(theme.islight,'#fff','#000')" d="M13 12.5a.5.5 0 101 0v-3a.5.5 0 00-.5-.5h-3a.5.5 0 100 1h1.73l-2.61 2.61a.54.54 0 10.76.76L13 10.76V12.5z"/>
  </g>
  @clipPath
</svg>'

@run_with_powershell, open_windows_powershell, open_powershell_window_here,windows_powershell,windows_powershell_admin='<svg fill="none" viewBox="0 0 16 16">
  <path fill="@color3" d="M2.17 3.62l-1.23 8a2.5 2.5 0 002.47 2.88H6.75V8.36c0-1.72 1.86-2.81 3.36-1.95l4.25 2.43.68-4.45a2.5 2.5 0 00-2.47-2.88H4.64a2.5 2.5 0 00-2.47 2.12z"/>
  <path fill="@image.color1" d="M1.43 11.69L2.46 5h11.98l-.55 **********.71-4.64A3 3 0 0012.58 1H4.64A3 3 0 001.68 3.54l-1.23 8A3 3 0 003.41 15h3.36a2.32 2.32 0 01-.02-.36V14H3.41a2 2 0 01-1.97-2.30zM4.64 2h7.94a2 2 0 012.00 2H2.62l.04-.30A2 2 0 014.64 2z"/>
  <path fill="@image.color2" d="M9.49 7.49A1 1 0 008 8.36v6.27a1 1 0 001.49.86l5.49-3.13a1 1 0 000-1.73L9.49 7.49z"/>
</svg>'

@properties='<svg fill="none" viewBox="0 0 16 16">
  <g clip-path="url(#clip0)">
    <path fill="@color3" d="M12 6l-2-2L13.00.99A4.45 4.45 0 0011 .5 4.5 4.5 0 006.5 5c.***********.20 1.28l-5.58 5.58a2.12 2.12 0 103.00 3.00L9.71 9.29c.*********** 1.28.20A4.5 4.5 0 0015.5 5a4.44 4.44 0 00-.49-2.00L12 6z"/>
    <path fill="@image.color1" d="M7.46 1.46A5 5 0 0111 0h.00a4.95 4.95 0 ***********.5 0 01.12.79L10.70 4 12 5.29l2.65-2.65a.5.5 0 01.79.12A4.95 4.95 0 0116 4.99V5a5 5 0 01-5 5h-.00a4.95 4.95 0 01-1.13-.14l-5.37 5.37a2.62 2.62 0 01-4.28-.85 2.62 2.62 0 01.57-2.86l5.37-5.38A4.95 4.95 0 016 5.00V5a5 5 0 011.46-3.53zM10.99 1A4 4 0 007 4.99c.***********.18 1.13a.5.5 0 01-.12.50l-5.58 5.58a1.62 1.62 0 00-.35 1.77 1.62 1.62 0 002.65.52l5.58-5.58a.5.5 0 01.50-.12c.*********** 1.13.18A4 4 0 0015 5.00a3.94 3.94 0 00-.17-1.12l-2.47 2.47a.5.5 0 01-.70 0l-2-2a.5.5 0 010-.70l2.47-2.47A3.95 3.95 0 0010.99 1z"/>
  </g>
  @clipPath
</svg>'

@restore='<svg fill="none" viewBox="0 0 16 16">
  <g clip-path="url(#clip0)">
    <path fill="@image.color2" d="M3.85.14a.5.5 0 010 .70L1.70 3H8.5A6.5 6.5 0 112 9.5a.5.5 0 011 0A5.5 5.5 0 108.5 4H1.70l2.14 2.14a.5.5 0 11-.70.70l-3-3a.5.5 0 010-.70l3-3a.5.5 0 01.70 0z"/>
  </g>
  @clipPath
</svg>'

@undo, undo_copy,undo_delete, undo_rename, undo_new, undo_move='<svg fill="none" viewBox="0 0 16 16">
  <g clip-path="url(#clip0)">
    <path fill="@image.color2" d="M2.5 0a.5.5 0 01.5.5V5h4.5a.5.5 0 010 1h-5a.5.5 0 01-.5-.5v-5a.5.5 0 01.5-.5z"/>
    <path fill="@image.color2" d="M8.51.22A5 5 0 0113.2 8.84l-8.37 7.04a.5.5 0 11-.64-.76l8.37-7.04.00-.00A4 4 0 007.44 1.92L2.94 5.67a.5.5 0 01-.64-.76L6.8 1.15A5.00 5.00 0 018.51.22z"/>
  </g>
  @clipPath
</svg>'

@redo, redo_delete, redo_rename, redo_new, redo_move='<svg fill="none" viewBox="0 0 16 16">
  <g clip-path="url(#clip0)">
    <path fill="@image.color2" d="M13.5 0a.5.5 0 01.5.5v5a.5.5 0 01-.5.5h-5a.5.5 0 010-1H13V.5a.5.5 0 01.5-.5z"/>
    <path fill="@image.color2" d="M7.18 1.18a4.00 4.00 0 011.37.74l4.49 3.74a.5.5 0 00.64-.76L9.2 1.15A5 5 0 002.8 8.84h.00l8.37 7.04a.5.5 0 10.64-.76l-8.38-7.04-.00-.00A4 4 0 017.18 1.18z"/>
  </g>
  @clipPath
</svg>'

@refresh='<svg fill="none" viewBox="0 0 16 16">
  <path fill="@image.color1" d="M14.5 0a.5.5 0 01.5.5v4a.5.5 0 01-.5.5h-4a.5.5 0 010-1H14V.5a.5.5 0 01.5-.5z"/>
  <path fill="@image.color1" d="M8 1a7 7 0 107 7 .5.5 0 011 0 8 8 0 11-1.36-******** 0 01-.82.56A6.99 6.99 0 008 1z"/>
</svg>'

@rename='<svg fill="none" viewBox="0 0 16 16">
  <path fill="@color3" d="M3 2.5h6v11H3A2.5 2.5 0 01.5 11V5A2.5 2.5 0 013 2.5zM13 2.5h-1v11h1a2.5 2.5 0 002.5-2.5V5A2.5 2.5 0 0013 2.5z"/>
  <path stroke="@image.color1" d="M9 2.5H3A2.5 2.5 0 00.5 5v6A2.5 2.5 0 003 13.5h6m3-11h1A2.5 2.5 0 0115.5 5v6a2.5 2.5 0 01-2.5 2.5h-1"/>
  <path stroke="@image.color2" stroke-linecap="round" d="M8.5.5h2m2 0h-2m-2 15h2m2 0h-2m0-15v15"/>
  <path fill="@image.color2" fill-rule="evenodd" d="M5.5 4a.5.5 0 01.45.29l2.75 6a.5.5 0 11-.91.41L7.12 9.25H3.87l-.66 1.45a.5.5 0 11-.91-.41l2.75-6A.50.50 0 015.5 4zm0 1.7L4.33 8.25H6.67L5.5 5.7z" clip-rule="evenodd"/>
</svg>'

@rotate_left='<svg fill="none" viewBox="0 0 16 16">
  <path fill="@color3" d="M1.5 14.5v-7l13 7h-13z"/>
  <path fill="@image.color1" d="M1.24 7.07A.5.5 0 001 7.5v7a.5.5 0 00.5.5h13a.5.5 0 00.23-.94l-13-7a.5.5 0 00-.49.01zM12.51 14H2V8.33L12.51 14z"/>
  <path fill="@image.color2" d="M6.85 1.14a.5.5 0 00-.70 0l-2 2a.5.5 0 000 .70l2 2a.5.5 0 10.70-.70L5.70 4H10.5c1.00 0 1.9.30 **********.47.97 1.08.97 1.72 0 .63-.34 1.25-.97 1.72C12.4 8.69 11.50 9 10.5 9a.5.5 0 000 1c1.20 0 2.31-.36 3.12-.97C14.44 8.40 15 7.52 15 6.5s-.55-1.90-1.37-2.52C12.81 3.36 11.70 3 10.5 3H5.70l1.14-1.14a.5.5 0 000-.70z"/>
</svg>'

@rotate_right='<svg fill="none" viewBox="0 0 16 16">
  <path fill="@color3" d="M14.5 14.5v-7l-13 7h13z"/>
  <path fill="@image.color1" d="M14.75 7.07A.5.5 0 0115 7.5v7a.5.5 0 01-.5.5h-13a.5.5 0 01-.23-.94l13-7a.5.5 0 01.49.01zM3.48 14H14V8.33L3.48 14z"/>
  <path fill="@image.color2" d="M9.14 1.14a.5.5 0 01.70 0l2 2a.5.5 0 010 .70l-2 2a.5.5 0 11-.70-.70L10.29 4H5.5c-1.00 0-1.9.30-2.52.77C2.34 5.25 2 5.86 2 6.5c0 .63.34 1.25.97 1.72C3.6 8.69 4.49 9 5.5 9a.5.5 0 010 1c-1.20 0-2.31-.36-3.12-.97C1.55 8.40 1 7.52 1 6.5s.55-1.90 1.37-2.52C3.19 3.36 4.29 3 5.5 3h4.79L9.14 1.85a.5.5 0 010-.70z"/>
</svg>'

@set_as_desktop_wallpaper, set_as_desktop_background='<svg fill="none" viewBox="0 0 16 16">
  <path fill="@color3" d="M.5 9.37A3.48 3.48 0 002.50 10h4a3.5 3.5 0 003.5-3.5v-4H13A2.5 2.5 0 0115.5 5v6a2.5 2.5 0 01-2.5 2.5H3A2.5 2.5 0 01.5 11V9.37z"/>
  <path fill="@image.color1" d="M0 8.94V11a3 3 0 003 3h10a3 3 0 003-3V5a3 3 0 00-3-3H9.96c.02.16.03.33.03.5V3H13a2 2 0 012 2v5H1v-.33a3.51 3.51 0 01-1-.71zM3 13a2 2 0 01-2-2h10.5a.5.5 0 100 1 .5.5 0 000-1h2a.5.5 0 100 1 .5.5 0 000-1H15a2 2 0 01-2 2H3z"/>
  <path fill="@image.color2" d="M2.50 0a2.5 2.5 0 00-2.5 2.5v4c0 .51.15.98.41 1.37l3.02-3.02a1.5 1.5 0 012.12 0l3.02 3.02c.26-.39.41-.87.41-1.37v-4a2.5 2.5 0 00-2.5-2.5h-4zm3.75 3.5a.75.75 0 110-********* 0 010 1.5z"/>
  <path fill="@image.color2" d="M2.50 9c-.51 0-.98-.15-1.37-.41L4.14 5.56a.5.5 0 01.70 0l3.02 3.02C7.48 8.84 7.01 9 6.50 9h-4z"/>
</svg>'

@next_desktop_background='<svg fill="none" viewBox="0 0 16 16">
  <path fill="@color3" d="M2.5 10v3A2.5 2.5 0 005 15.5h8a2.5 2.5 0 002.5-2.5V5A2.5 2.5 0 0013 2.5h-3V9a1 1 0 01-1 1H2.5z"/>
  <path fill="@image.color1" d="M2 10v3a3 3 0 003 3h8a3 3 0 003-3V5a3 3 0 00-3-3h-3v1h3a2 2 0 012 2v8c0 .37-.10.71-.27 1.01L10.23 9.53a1.75 1.75 0 00-.28-.23A1 1 0 019 10H7.29l-4.01 4.01A1.99 1.99 0 013 13v-3H2zm1.98 4.72l4.48-4.48a.75.75 0 011.06 0l4.48 4.48A1.99 1.99 0 0113 15H5c-.37 0-.71-.10-1.01-.27z"/>
  <path fill="@image.color1" d="M10 6.5a1.5 1.5 0 103 0 1.5 1.5 0 00-3 0zm1.5-.5a.5.5 0 110 1 .5.5 0 010-1z"/>
  <g>
    <path fill="@image.color2" d="M9 .5a.5.5 0 00-1 0v8a.5.5 0 001 0v-8zM5.98 4.10L.80.11A.5.5 0 000 .51v7.97a.5.5 0 00.80.39l5.18-3.98a.5.5 0 000-.79z"/>
  </g>
</svg>'

@desktop, show_desktop, show_desktop_icons,show_the_desktop='<svg fill="none" viewBox="0 0 16 16">
  <g clip-path="url(#clip0)">
    <path fill="@color3" d="M.53 6h1.59a4.00 4.00 0 007.74 0H11.5v-.00a1.50 1.50 0 001.44-1.89 6.60 6.60 0 00-.67-1.59H13A2.5 2.5 0 0115.5 5v6a2.5 2.5 0 01-2.5 2.5H3A2.5 2.5 0 01.5 11V6h.03z"/>
    <path fill="@image.color1" d="M1 6H.53A1.5 1.5 0 010 5.91V11a3 3 0 003 3h10a3 3 0 003-3V5a3 3 0 00-3-3h-1.05c.***********.59 1H13a2 2 0 012 2v5H1V6zm2 7a2 2 0 01-2-2h10.5a.5.5 0 100 1 .5.5 0 000-1h2a.5.5 0 100 1 .5.5 0 000-1H15a2 2 0 01-2 2H3z"/>
    <path fill="@image.color2" d="M.98 4.63C1.54 2.56 3.56 1 6.00 1c2.43 0 4.45 1.56 5.01 3.62a.5.5 0 00.96-.26C11.29 1.83 8.85 0 6.00 0 3.14 0 .70 1.83.01 4.36a.5.5 0 10.96.26z"/>
    <path fill="@image.color2" d="M3 5a3 3 0 116 0 3 3 0 01-6 0z"/>
  </g>
  @clipPath
</svg>'

@restore_previous_versions='<svg fill="none" viewBox="0 0 16 16">
	<path fill="@image.color1" d="M16 8C16 8.73 15.9 9.44 15.7 10.1C15.7 10.1 15.5 10.8 15.2 11.4C15.2 11.4 14.9 12 14.6 12.6C14.6 12.6 14.1 13.2 13.7 13.6C13.7 13.6 13.2 14.1 12.6 14.5C12.6 14.5 12 14.9 11.5 15.2C11.5 15.2 10.8 15.5 10.1 15.7C10.1 15.7 9.45 15.9 8.74 16C7.27 16 6.57 15.9 5.88 15.7C5.88 15.7 5.2 15.5 4.56 15.2C4.56 15.2 3.97 14.9 3.38 14.5C3.38 14.5 2.84 14.1 2.35 13.6C2.35 13.6 1.87 13.2 1.45 12.6C1.45 12.6 1.1 12 0.753 11.4C0.753 11.4 0.482 10.8 0.289 10.1C0.289 10.1 0.096 9.44 0 8.73C0 7.87 0.049 7.75 0.148 7.65C0.148 7.65 0.247 7.55 0.365 7.5C0.635 7.5 0.753 7.55 0.852 7.65C0.852 7.65 0.951 7.75 1 7.87C1 8.85 1.15 9.68 1.45 10.5C1.45 10.5 1.76 11.3 2.19 12C3.07 13 3.44 13.3 3.84 13.6C3.84 13.6 4.24 13.9 4.66 14.2C4.66 14.2 5.11 14.4 5.56 14.6C5.56 14.6 6.02 14.7 6.51 14.8C6.51 14.8 6.99 14.9 7.48 15C8.63 15 9.25 14.9 9.85 14.7C9.85 14.7 10.5 14.6 11 14.3C11 14.3 11.5 14 12.1 13.7C12.1 13.7 12.5 13.4 12.9 12.9C12.9 12.9 13.4 12.5 13.7 12.1C13.7 12.1 14 11.5 14.3 11C14.3 11 14.6 10.5 14.8 9.86C14.8 9.86 14.9 9.27 15 8.65C15 7.35 14.9 6.73 14.8 6.13C14.8 6.13 14.6 5.53 14.3 4.98C14.3 4.98 14 4.46 13.7 3.95C13.7 3.95 13.4 3.47 12.9 3.05C12.9 3.05 12.5 2.62 12.1 2.25C12.1 2.25 11.5 1.95 11 1.65C11 1.65 10.5 1.42 9.86 1.25C9.86 1.25 9.27 1.08 8.65 1C7.43 1 6.87 1.07 6.32 1.2C6.32 1.2 5.77 1.34 5.25 1.54C5.25 1.54 4.76 1.8 4.26 2.06C4.26 2.06 3.81 2.37 3.38 2.74C3.38 2.74 2.96 3.11 2.59 3.53L5.5 4C5.63 4 5.75 4.05 5.85 4.15C5.85 4.15 5.95 4.25 6 4.37C6 4.63 5.95 4.75 5.85 4.85C5.85 4.85 5.75 4.95 5.63 5L1.5 5C1.4 5 1.31 4.98 1.24 4.93C1.24 4.93 1.17 4.88 1.12 4.83C1.12 4.83 1.09 4.76 1.05 4.69C1.05 4.69 1.03 4.61 1.02 4.52C1.02 4.52 1.01 4.44 1 4.35C1 3.63 1 3 1 2.38C1 2.38 1 1.75 1 1.12C1 0.365 1.05 0.247 1.15 0.148C1.15 0.148 1.25 0.049 1.36 0C1.64 0 1.75 0.049 1.85 0.148C1.85 0.148 1.95 0.247 2 0.365L2 2.7C2.39 2.28 2.81 1.89 3.26 1.55C3.26 1.55 3.71 1.22 4.19 0.932C4.19 0.932 4.7 0.703 5.21 0.474C5.21 0.474 5.75 0.299 6.3 0.18C6.3 0.18 6.86 0.06 7.43 0C8.73 0 9.44 0.094 10.1 0.281C10.1 0.281 10.8 0.469 11.4 0.737C11.4 0.737 12 1.09 12.6 1.44C12.6 1.44 13.2 1.85 13.7 2.34C13.7 2.34 14.1 2.82 14.6 3.36C14.6 3.36 14.9 3.95 15.2 4.55C15.2 4.55 15.5 5.19 15.7 5.87C15.7 5.87 15.9 6.55 16 7.26Z"/>
	<path fill="@image.color2" d="M8 7.297L10.86 10.14C10.95 10.24 11 10.36 11 10.5C11 10.64 10.95 10.75 10.85 10.85C10.85 10.85 10.75 10.95 10.64 11C10.36 11 10.25 10.95 10.16 10.84L7.156 7.844C7.052 7.75 7 7.635 7 7.5L7 3.5C7 3.365 7.05 3.247 7.148 3.148C7.148 3.148 7.247 3.05 7.365 3C7.635 3 7.753 3.05 7.852 3.148C7.852 3.148 7.951 3.247 8 3.365Z"/>
</svg>'

@create_shortcut, create_shortcuts_here='
<svg fill="none" viewBox="0 0 16 16">
    <path fill="@image.color1" d="M4.5 12C3.9 12 3.3 12 2.8 12C2.8 12 2.2 11 1.7 11C1.7 11 1.3 11 0.92 10C0.92 10 0.6 9.8 0.36 9.2C0.36 9.2 0.12 8.7 0 8.1C0 6.9 0.12 6.3 0.36 5.7C0.36 5.7 0.6 5.2 0.92 4.7C0.92 4.7 1.3 4.3 1.7 3.9C1.7 3.9 2.2 3.6 2.8 3.4C2.8 3.4 3.3 3.1 3.9 3L6.5 3C6.6 3 6.8 3.1 6.9 3.1C6.9 3.1 7 3.2 7 3.4C7 3.6 7 3.8 6.9 3.9C6.9 3.9 6.8 4 6.6 4L4.5 4C4 4 3.6 4.1 3.1 4.3C3.1 4.3 2.7 4.5 2.3 4.7C2.3 4.7 2 5 1.7 5.3C1.7 5.3 1.5 5.7 1.3 6.1C1.3 6.1 1.1 6.5 1 7C1 8 1.1 8.4 1.3 8.9C1.3 8.9 1.5 9.3 1.7 9.7C1.7 9.7 2 10 2.3 10C2.3 10 2.7 11 3.1 11C3.1 11 3.6 11 4 11L6.5 11C6.6 11 6.8 11 6.9 11C6.9 11 7 11 7 11C7 12 7 12 6.9 12C6.9 12 6.8 12 6.6 12ZM9.5 12C9.4 12 9.2 12 9.2 12C9.2 12 9.1 12 9 12C9 11 9.1 11 9.2 11C9.2 11 9.2 11 9.4 11L12 11C12 11 12 11 13 11C13 11 13 11 14 10C14 10 14 10 14 9.7C14 9.7 15 9.3 15 8.9C15 8.9 15 8.4 15 8C15 7 15 6.5 15 6.1C15 6.1 15 5.7 14 5.3C14 5.3 14 5 14 4.7C14 4.7 13 4.5 13 4.3C13 4.3 12 4.1 12 4L9.5 4C9.4 4 9.2 4 9.2 3.9C9.2 3.9 9.1 3.8 9 3.6C9 3.4 9.1 3.2 9.2 3.1C9.2 3.1 9.2 3.1 9.4 3L12 3C12 3 13 3.1 13 3.4C13 3.4 14 3.6 14 3.9C14 3.9 15 4.3 15 4.7C15 4.7 15 5.2 16 5.7C16 5.7 16 6.3 16 6.9C16 8.1 16 8.7 16 9.2C16 9.2 15 9.8 15 10C15 10 15 11 14 11C14 11 14 11 13 12C13 12 13 12 12 12Z"/>
    <path fill="@image.color2" d="M4.5 8C4.3 8 4.2 8 4.1 7.8C4.1 7.8 4 7.7 4 7.6C4 7.4 4 7.3 4.1 7.2C4.1 7.2 4.2 7 4.3 7L12 7C12 7 12 7 12 7.2C12 7.2 12 7.3 12 7.4C12 7.6 12 7.7 12 7.8C12 7.8 12 8 12 8Z"/>
</svg>'

@turn_on_bitlocker, turn_off_bitlocker='<svg fill="none" viewBox="0 0 16 16">
  <g clip-path="url(#clip0)">
    <path fill="@color3" fill-rule="evenodd" d="M.5 9h5.49a1 1 0 001-1V5.5H14A1.5 1.5 0 0115.5 7v2a1.5 1.5 0 01-1.5 1.5H2A1.5 1.5 0 01.5 9z" clip-rule="evenodd"/>
    <path fill="@image.color1" fill-rule="evenodd" d="M0 9a2 2 0 002 2h12a2 2 0 002-2V7a2 2 0 00-2-2H6.99v1H14a1 1 0 011 1v2a1 1 0 01-1 1H2a1 1 0 01-1-1H0z" clip-rule="evenodd"/>
    <path fill="@image.color1" d="M13 7.75a.75.75 0 11-1.5 0 .75.75 0 011.5 0z"/>
    <path fill="@image.color2" fill-rule="evenodd" d="M2.99 1a1 1 0 00-1 1v1.5h-1V2a2 2 0 114 0v1.5h-1V2a1 1 0 00-1-1z" clip-rule="evenodd"/>
    <path fill="@image.color2" fill-rule="evenodd" d="M-.00 7.5v-4a.49.49 0 01.5-.5h5a.49.49 0 01.5.5v4a.49.49 0 01-.5.5h-5a.49.49 0 01-.5-.5zm4-2a1 1 0 11-2 0 1 1 0 012 0z" clip-rule="evenodd"/>
  </g>
  @clipPath
</svg>'

@show_file_extensions='<svg fill="none" viewBox="0 0 16 16">
  <path fill="@color3" d="M2.5 14V2A1.5 1.5 0 014 .5h3.87a1.5 1.5 0 011.06.44l4.12 4.12a1.5 1.5 0 01.43 1.06V14a1.5 1.5 0 01-1.5 1.5H4A1.5 1.5 0 012.5 14z"/>
  <path fill="@image.color1" d="M4 1a1 1 0 00-1 1v12a1 1 0 001 1h8a1 1 0 001-1V6.12a1 1 0 00-.29-.70L8.58 1.29A1 1 0 007.87 1H4zM2 2a2 2 0 012-2h3.87a2 2 0 011.41.58l4.12 4.12A2 2 0 0114 6.12V14a2 2 0 01-2 2H4a2 2 0 01-2-2V2z"/>
  <path fill="@image.color1" d="M8 4V.5h1V4a1 1 0 001 1h3.5v1H10a2 2 0 01-2-2z"/>
  <path fill="@image.color2" d="M4 6.5a.5.5 0 01.5-.5h2a.5.5 0 010 1H6v6h.5a.5.5 0 010 1h-2a.5.5 0 010-1H5V7h-.5a.5.5 0 01-.5-.5z"/>
</svg>'

@show_hidden_files='<svg fill="none" viewBox="0 0 16 16">
  <path fill="@color3" d="M11 9.5a3 3 0 11-6 0 3 3 0 016 0z"/>
  <path fill="@image.color2" d="M8 7a2.5 2.5 0 100 5 2.5 2.5 0 000-5zM4.5 9.5a3.5 3.5 0 117 0 3.5 3.5 0 01-7 0z"/>
  <path fill="@image.color1" d="M8 4C4.75 4 2 6.37 1.22 9.62a.5.5 0 01-.97-.23C1.12 5.74 4.24 3 8 3c3.75 0 6.87 2.73 7.75 6.38a.5.5 0 01-.97.23C13.99 6.36 11.23 4 8 4z"/>
</svg>'

@compressed='<svg fill="none" viewBox="0 0 16 16">
  <path fill="@color3" d="M5.5 1.5h-3a2 2 0 00-2 2v9a2 2 0 002 2h11a2 2 0 002-2v-7a2 2 0 00-2-2H8.75a1.5 1.5 0 01-1.2-.6l-.45-.6a2 2 0 00-1.6-.8z"/>
  <path fill="@image.color1" d="M2.5 1A2.5 2.5 0 000 3.5v9A2.5 2.5 0 002.5 15h11a2.5 2.5 0 002.5-2.5v-7A2.5 2.5 0 0013.5 3H8.25L7.5 2a2.5 2.5 0 00-2-1h-3zM1 3.5A1.5 1.5 0 012.5 2h3a1.5 1.5 0 011.2.6l.58.77-.55.37A1.5 1.5 0 015.89 4H1v-.5zM1 5h4.89a2.5 2.5 0 001.38-.42l.87-.58H13.5A1.5 1.5 0 0115 5.5v7a1.5 1.5 0 01-1.5 1.5h-11A1.5 1.5 0 011 12.5V5z"/>
  <path fill="@image.color2" d="M10 4a1 1 0 011-1h1a1 1 0 011 1v2a1 1 0 01-1 1v3h.5a.5.5 0 010 1H12v3.5a.5.5 0 01-1 0V13h-.5a.5.5 0 010-1h.5V9h-.5a.5.5 0 010-1h.5V7a1 1 0 01-1-1V4zm2 0h-1v2h1V4z"/>
</svg>'

@more_options='<svg fill="none" viewBox="0 0 16 16">
  <path fill="@color3" d="M5 8.5H1.5V12A2.5 2.5 0 004 14.5h3.5V11A2.5 2.5 0 005 8.5z"/>
  <path fill="@image.color1" d="M2 4a2 2 0 012-2h2.5a.5.5 0 000-1H4a3 3 0 00-3 3v8a3 3 0 003 3h8a3 3 0 003-3V9.5a.5.5 0 00-1 0V12a2 2 0 01-2 2H8v-3a3 3 0 00-3-3H2V4zm0 5h3a2 2 0 012 2v3H4a2 2 0 01-2-2V9z"/>
  <path fill="@image.color2" d="M9.5 1a.5.5 0 000 1h3.79L9.14 6.14a.5.5 0 10.70.70L14 2.70V6.5a.5.5 0 001 0v-5a.5.5 0 00-.5-.5h-5z"/>
</svg>'

@burn_disc_image='<svg fill="none" viewBox="0 0 16 16">
  <path fill="@color3" d="M8 15.5a7.5 7.5 0 100-15 7.5 7.5 0 000 15zm0-6a1.5 1.5 0 110-3 1.5 1.5 0 010 3z"/>
  <path fill="@image.color1" d="M8 1a7 7 0 100 14A7 7 0 008 1zM0 8a8 8 0 1116 0A8 8 0 010 8zm8-1a1 1 0 100 2 1 1 0 000-2zM6 8a2 2 0 114 0 2 2 0 01-4 0z"/>
  <path fill="@image.color2" d="M9.5 3.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0z"/>
  <path fill="@image.color2" d="M8 4a4 4 0 104 4 .5.5 0 011 0 5 5 0 11-5-5 .5.5 0 010 1z"/>
</svg>'

@cleanup='<svg fill="none" viewBox="0 0 16 16">
  <g clip-path="url(#clip0)">
    <path fill="@image.color1" d="M15.85.14a.5.5 0 010 .70l-5.5 5.5a.5.5 0 01-.70-.70l5.5-5.5a.5.5 0 01.70 0z"/>
    <path fill="@color3" d="M9.06 12.06l.02-.04A3.75 3.75 0 103.98 6.91l-.04.02 5.12 5.12z"/>
    <path fill="@image.color1" d="M3.60 6.56a4.25 4.25 0 115.83 ******** 0 01-.72.02L3.58 7.29a.5.5 0 01.02-.72zm.98.31l4.52 4.52A3.25 3.25 0 104.59 6.88z"/>
    <path fill="@color3" d="M9.06 12.06L3.93 6.93.5 9 7 15.5l2.06-3.43z"/>
    <path fill="@image.color2" d="M3.68 6.50a.5.5 0 01.61.07l5.12 5.12a.5.5 0 01.07.61L7.43 15.75a.5.5 0 01-.78.09l-6.5-6.5a.5.5 0 01.09-.78L3.68 6.51zm-2.37 2.59l5.59 5.59 1.53-2.55L3.86 7.56 1.30 9.1z"/>
  </g>
  @clipPath
</svg>'

@move_to, move_here,move_to_folder, extract_all, extract_to='<svg fill="none" viewBox="0 0 16 16">
  <path fill="@if(theme.islight,'#fff','none')" d="M2.5 1.5h3a2 2 0 011.6.8L8 3.5h4.5a2 2 0 012 2 .87.87 0 01.86.99l-.09.66A5.75 5.75 0 006.59 14.5H2.5a2 2 0 01-2-2v-9a2 2 0 012-2z"/>
  <path fill="@image.color1" d="M2.5 1A2.5 2.5 0 000 3.5v9A2.5 2.5 0 002.5 15h4.43a5.75 5.75 0 01-.61-1H2.5c-.25 0-.49-.06-.70-.17l1.44-7.02A1 1 0 014.22 6h5.59a5.75 5.75 0 011.68-.25c.58 0 1.15.08 1.68.25h.59a1 1 0 01.97.75c.***********.96.83l.04-.31a1.99 1.99 0 00-.76-1.86A2.5 2.5 0 0012.5 3H8.25L7.5 2a2.5 2.5 0 00-2-1h-3zm10 3a1.5 1.5 0 011.41 1.00A2.04 2.04 0 0013.77 5H4.22a2 2 0 00-1.96 1.59l-1.25 6.08c-.00-.06-.01-.12-.01-.18v-9A1.5 1.5 0 012.5 2h3a1.5 1.5 0 011.2.6l.9 1.2.15.2h4.75z"/>
  <path fill="@image.color2" d="M11.5 7a4.5 4.5 0 110 9 4.5 4.5 0 010-9z"/>
  <path fill="@if(theme.islight,'#fff','#000')" d="M11.14 13.85a.5.5 0 010-.70l1.64-1.64-1.64-1.64a.5.5 0 01.70-.70l2 2a.5.5 0 010 .70l-2 2a.5.5 0 01-.70 0z"/>
  <path fill="@if(theme.islight,'#fff','#000')" d="M9 11.5a.5.5 0 01.5-.5h4a.5.5 0 010 1h-4a.5.5 0 01-.5-.5z"/>
</svg>'

@copy_to,copy_to_folder,copy_here='<svg fill="none" viewBox="0 0 16 16">
	<path fill="@image.color2" d="M0 4.5C0 3.9 0.12 3.3 0.36 2.7C0.36 2.7 0.6 2.2 0.92 1.7C0.92 1.7 1.3 1.3 1.7 0.91C1.7 0.91 2.2 0.59 2.8 0.35C2.8 0.35 3.3 0.12 3.9 0C4.9 0 5.3 0.05 5.7 0.16C5.7 0.16 6.1 0.26 6.4 0.41C6.4 0.41 6.8 0.61 7.1 0.81C7.1 0.81 7.4 1 7.7 1.3C7.7 1.3 8 1.6 8.2 1.9C8.2 1.9 8.4 2.2 8.6 2.6C8.6 2.6 8.7 2.9 8.8 3.3C8.8 3.3 9 3.7 9 4.1C9 5.1 8.9 5.7 8.7 6.2C8.7 6.2 8.4 6.8 8.1 7.3C8.1 7.3 7.7 7.7 7.3 8.1C7.3 8.1 6.8 8.4 6.3 8.6C6.3 8.6 5.7 8.9 5.1 9C3.9 9 3.3 8.9 2.7 8.7C2.7 8.7 2.2 8.4 1.7 8.1C1.7 8.1 1.3 7.7 0.91 7.3C0.91 7.3 0.59 6.8 0.35 6.3C0.35 6.3 0.12 5.7 0 5.1Z"/>
	<path fill="@image.color2" d="M8.4 12C8.2 12 7.9 12 7.6 12C7.6 12 7.4 12 7.1 12C7.1 12 6.9 11 6.7 11C6.7 11 6.5 11 6.4 11C6.4 11 6.2 11 6.1 10C6.4 9.9 6.7 9.8 7 9.7C7 9.9 7.1 10 7.2 10C7.2 10 7.3 10 7.4 10C7.4 10 7.5 11 7.6 11C7.6 11 7.8 11 8 11C8 11 8.1 11 8.3 11L12 11C13 11 13 11 13 11C13 11 13 11 13 11C13 11 14 11 14 10C14 10 14 10 14 10C14 10 14 9.9 14 9.7L14 3.5C14 3.3 14 3.1 14 2.9C14 2.9 14 2.7 14 2.6C14 2.6 14 2.4 13 2.3C13 2.3 13 2.2 13 2.1C13 2.1 13 2 13 2L9.7 2C9.6 1.8 9.5 1.7 9.4 1.5C9.4 1.5 9.3 1.3 9.2 1.2L13 1C13 1 13 1.1 13 1.2C13 1.2 14 1.3 14 1.5C14 1.5 14 1.7 14 1.9C14 1.9 15 2.2 15 2.5C15 2.5 15 2.8 15 3.1L15 9.6C15 9.9 15 10 15 10C15 10 15 11 14 11C14 11 14 11 14 11C14 11 14 12 13 12C13 12 13 12 13 12Z"/>
	<path fill="@if(theme.islight,'#fff','#000')" d="M4.5 7.1C4.7 7.1 4.8 7 4.9 6.9L6.9 4.9C7 4.8 7.1 4.7 7.1 4.5C7.1 4.3 7 4.2 6.9 4.1L4.9 2.1C4.8 2 4.7 1.9 4.5 1.9C4.3 1.9 4.2 2 4.1 2.1C4.1 2.1 4 2.2 3.9 2.3C3.9 2.7 4 2.8 4.1 2.9L5.2 4L2.5 4C2.4 4 2.2 4 2.1 4.2C2.1 4.2 2 4.2 2 4.4C2 4.6 2 4.8 2.1 4.9C2.1 4.9 2.2 5 2.4 5L5.2 5L4.1 6.1C4 6.2 3.9 6.3 3.9 6.5C3.9 6.7 4 6.8 4.1 6.9C4.1 6.9 4.2 7 4.3 7.1Z"/>
	<path fill="@image.color1" d="M3.5 15C3.1 15 2.8 15 2.5 15C2.5 15 2.2 15 1.9 14C1.9 14 1.7 14 1.5 14C1.5 14 1.3 14 1.2 13C1.2 13 1.1 13 1 13L1 9.1C1.2 9.2 1.3 9.3 1.5 9.4C1.5 9.4 1.7 9.5 1.8 9.6L2 12C2 13 2 13 2.1 13C2.1 13 2.2 13 2.3 13C2.3 13 2.5 14 2.6 14C2.6 14 2.8 14 2.9 14C2.9 14 3.1 14 3.3 14L7.5 14C7.7 14 7.8 14 8 14C8 14 8.1 14 8.2 14C8.2 14 8.4 14 8.5 14C8.5 14 8.6 14 8.7 13C8.7 13 8.8 13 8.9 13L10 13C9.9 13 9.8 14 9.6 14C9.6 14 9.5 14 9.3 14C9.3 14 9.1 14 8.9 15C8.9 15 8.6 15 8.3 15C8.3 15 8.1 15 7.8 15Z"/>
</svg>'

@pc,this_pc='<svg fill="none" viewBox="0 0 16 16">
	<path fill="@image.color1" d="M2.5 15C2.3 15 2.1 15 1.9 15C1.9 15 1.8 15 1.6 15C1.6 15 1.4 15 1.3 14C1.3 14 1.2 14 1.1 14C1.1 14 1 14 1 14L1 2.5C1 2.3 1 2.1 1.1 1.9C1.1 1.9 1.2 1.7 1.3 1.6C1.3 1.6 1.4 1.4 1.6 1.3C1.6 1.3 1.8 1.2 1.9 1.1C1.9 1.1 2.1 1 2.3 1L7.5 1C7.7 1 7.9 1 8.1 1.1C8.1 1.1 8.3 1.2 8.4 1.3C8.4 1.3 8.6 1.4 8.7 1.6C8.7 1.6 8.8 1.7 8.9 1.9C8.9 1.9 9 2.1 9 2.3L9 4L8 4L8 2.5C8 2.4 8 2.2 7.9 2.1C7.9 2.1 7.8 2 7.6 2L2.5 2C2.4 2 2.2 2 2.1 2.1C2.1 2.1 2 2.2 2 2.4L2 14C2 14 2 14 2.1 14C2.1 14 2.2 14 2.4 14L7 14L7 15Z"/>
	<path fill="@image.color1" d="M8.5 15C8.4 15 8.2 15 8.2 15C8.2 15 8.1 15 8 15C8 14 8 14 8.1 14C8.1 14 8.1 14 8.2 14C8.2 14 8.2 14 8.3 14C8.3 14 8.4 14 8.5 14C8.5 14 8.6 14 8.7 14L9 14L9 13L7.5 13C7.3 13 7.1 13 6.9 13C6.9 13 6.8 13 6.6 13C6.6 13 6.5 13 6.3 12C6.3 12 6.2 12 6.1 12C6.1 12 6 12 6 12L6 6.5C6 6.3 6 6.1 6.1 5.9C6.1 5.9 6.2 5.7 6.3 5.6C6.3 5.6 6.5 5.4 6.6 5.3C6.6 5.3 6.8 5.2 6.9 5.1C6.9 5.1 7.1 5 7.3 5L14 5C15 5 15 5 15 5.1C15 5.1 15 5.2 15 5.3C15 5.3 16 5.4 16 5.6C16 5.6 16 5.7 16 5.9C16 5.9 16 6.1 16 6.3L16 12C16 12 16 12 16 12C16 12 16 12 16 12C16 12 16 13 15 13C15 13 15 13 15 13C15 13 15 13 15 13L13 13L13 14L13 14C13 14 13 14 14 14C14 14 14 14 14 14C14 14 14 14 14 14C14 14 14 14 14 14C14 14 14 14 14 14C14 15 14 15 14 15C14 15 14 15 14 15ZM14 12C15 12 15 12 15 12C15 12 15 12 15 12L15 6.5C15 6.4 15 6.2 15 6.2C15 6.2 15 6 15 6L7.5 6C7.4 6 7.2 6 7.2 6.2C7.2 6.2 7 6.2 7 6.4L7 12C7 12 7 12 7.2 12C7.2 12 7.2 12 7.4 12Z"/>
	<path fill="@image.color2" d="M3.5 4C3.4 4 3.2 4 3.1 3.9C3.1 3.9 3.1 3.8 3 3.6C3 3.4 3.1 3.2 3.1 3.1C3.1 3.1 3.2 3.1 3.4 3L6.5 3C6.6 3 6.8 3.1 6.9 3.1C6.9 3.1 7 3.2 7 3.4C7 3.6 7 3.8 6.9 3.9C6.9 3.9 6.8 4 6.6 4ZM3.5 6C3.4 6 3.2 6 3.1 5.9C3.1 5.9 3.1 5.8 3 5.6C3 5.4 3.1 5.2 3.1 5.2C3.1 5.2 3.2 5 3.4 5L5 5L5 6Z"/>
	<path fill="@image.color2" d="M10 13L10 14L12 14L12 13Z"/>
</svg>'

@command_prompt,command_prompt_admin,open_command_prompt,open_command_window_here='<svg fill="none" viewBox="0 0 16 16">
	@svg_window_template
	<path fill="@image.color2" d="M13 11.5C13 11.64 12.95 11.75 12.85 11.85C12.85 11.85 12.75 11.95 12.64 12C12.42 12 12.33 11.97 12.25 11.92C12.25 11.92 12.17 11.87 12.1 11.8L10.06 7.719C10.02 7.646 10 7.573 10 7.5C10 7.365 10.05 7.247 10.15 7.148C10.15 7.148 10.25 7.05 10.36 7C10.59 7 10.68 7.023 10.77 7.07C10.77 7.07 10.85 7.117 10.91 7.182L12.95 11.27C12.98 11.34 13 11.42 13 11.5ZM3.938 9.344C3.938 9.531 3.948 9.727 3.969 9.93C3.969 9.93 3.99 10.13 4.037 10.32C4.037 10.32 4.109 10.49 4.182 10.66C4.182 10.66 4.292 10.8 4.438 10.91C4.438 10.91 4.583 11.01 4.781 11.06C5.177 11.06 5.294 11.04 5.383 11.01C5.383 11.01 5.471 10.97 5.547 10.92C5.547 10.92 5.609 10.87 5.672 10.81C5.672 10.81 5.727 10.74 5.773 10.67C5.773 10.67 5.82 10.6 5.865 10.53C5.865 10.53 5.906 10.48 5.948 10.42C5.948 10.42 5.997 10.37 6.055 10.34C6.055 10.34 6.112 10.3 6.188 10.28C6.417 10.28 6.529 10.33 6.617 10.41C6.617 10.41 6.706 10.5 6.75 10.61C6.75 10.81 6.734 10.88 6.703 10.94C6.557 11.29 6.333 11.56 6.031 11.73C6.031 11.73 5.729 11.91 5.391 12C4.599 12 4.232 11.89 3.914 11.68C3.914 11.68 3.596 11.47 3.365 11.17C3.115 10.52 3.052 10.27 3.031 10.02C3.031 10.02 3.01 9.766 3 9.505C3 9.068 3.021 8.898 3.062 8.727C3.062 8.727 3.104 8.555 3.151 8.391C3.349 7.849 3.565 7.547 3.852 7.328C3.852 7.328 4.138 7.109 4.49 7C5.365 7 5.747 7.102 6.055 7.305C6.055 7.305 6.362 7.508 6.583 7.833C6.74 8.323 6.75 8.375 6.75 8.438C6.75 8.562 6.706 8.672 6.617 8.766C6.617 8.766 6.529 8.859 6.417 8.906C6.146 8.906 6.044 8.87 5.977 8.797C5.977 8.797 5.909 8.724 5.852 8.638C5.852 8.638 5.805 8.539 5.758 8.44C5.758 8.44 5.708 8.338 5.656 8.234C5.656 8.234 5.604 8.13 5.526 8.052C5.349 7.969 5.273 7.951 5.195 7.945C5.195 7.945 5.117 7.94 5.042 7.938L4.875 7.938C4.698 7.938 4.55 7.982 4.43 8.07C4.43 8.07 4.31 8.159 4.213 8.273C4.213 8.273 4.141 8.414 4.068 8.555C4.068 8.555 4.016 8.708 3.984 8.875C3.984 8.875 3.953 9.042 3.938 9.198ZM9 8.5C9 8.635 8.95 8.753 8.852 8.852C8.852 8.852 8.753 8.95 8.635 9C8.365 9 8.247 8.95 8.148 8.852C8.148 8.852 8.05 8.753 8 8.635C8 8.365 8.05 8.247 8.148 8.148C8.148 8.148 8.247 8.05 8.365 8C8.635 8 8.753 8.05 8.852 8.148C8.852 8.148 8.95 8.247 9 8.365ZM9 10.5C9 10.64 8.95 10.75 8.852 10.85C8.852 10.85 8.753 10.95 8.635 11C8.365 11 8.247 10.95 8.148 10.85C8.148 10.85 8.05 10.75 8 10.64C8 10.36 8.05 10.25 8.148 10.15C8.148 10.15 8.247 10.05 8.365 10C8.635 10 8.753 10.05 8.852 10.15C8.852 10.15 8.95 10.25 9 10.36Z"/>
</svg>'

@manage='<svg fill="none" viewBox="0 0 16 16">
	<path fill="@image.color1" d="M6 14L6 14C6 14 6 15 6 15C6 15 6 15 6 15L3.9 15C3.6 15 3.2 15 2.8 15C2.8 15 2.5 15 2.1 14C2.1 14 1.9 14 1.6 14C1.6 14 1.4 14 1.2 13C1.2 13 1.1 13 1 12L1 3.9C1 3.5 1.1 3.2 1.2 2.8C1.2 2.8 1.4 2.5 1.6 2.1C1.6 2.1 1.9 1.9 2.1 1.6C2.1 1.6 2.5 1.4 2.8 1.2C2.8 1.2 3.2 1.1 3.6 1L12 1C12 1 13 1.1 13 1.2C13 1.2 14 1.4 14 1.6C14 1.6 14 1.9 14 2.1C14 2.1 15 2.5 15 2.8C15 2.8 15 3.2 15 3.5L15 8.1L15 8C15 7.7 15 7.5 15 7.2C15 7.2 14 6.9 14 6.7L14 5L2 5L2 12C2 12 2 13 2.2 13C2.2 13 2.3 13 2.4 13C2.4 13 2.6 13 2.8 14C2.8 14 3 14 3.2 14C3.2 14 3.5 14 3.7 14ZM4 2C3.7 2 3.4 2 3.2 2.2C3.2 2.2 3 2.3 2.8 2.4C2.8 2.4 2.6 2.6 2.4 2.8C2.4 2.8 2.3 3 2.2 3.2C2.2 3.2 2 3.5 2 3.7L14 4L14 4C14 3.7 14 3.5 14 3.2C14 3.2 14 3 14 2.8C14 2.8 13 2.6 13 2.4C13 2.4 13 2.3 13 2.2C13 2.2 13 2 12 2Z"/>
	<path fill="@image.color2" d="M16 10L16 12L14 12L14 12C14 12 14 12 14 11C14 11 14 11 14 11C14 11 14 11 14 11C14 11 14 11 14 11C14 11 14 11 14 11C13 11 13 11 13 11C13 11 13 11 13 11C13 11 13 11 13 11C13 11 13 11 13 11C13 11 13 12 13 12L13 12L10 12L10 12C10 12 10 12 10 11C10 11 10 11 10 11C10 11 9.9 11 9.9 11C9.9 11 9.8 11 9.8 11C9.8 11 9.7 11 9.6 11C9.4 11 9.3 11 9.2 11C9.2 11 9.2 11 9.1 11C9.1 11 9.1 11 9.1 11C9.1 11 9 11 9 11C9 11 9 12 9 12L9 12L7 12L7 10C7 10 7 10 7.1 9.9C7.1 9.9 7.2 9.7 7.3 9.6C7.3 9.6 7.4 9.4 7.6 9.3C7.6 9.3 7.7 9.2 7.9 9.1C7.9 9.1 8.1 9 8.3 9L9 9L9 8.5C9 8.3 9 8.1 9.1 7.9C9.1 7.9 9.2 7.7 9.3 7.6C9.3 7.6 9.4 7.4 9.6 7.3C9.6 7.3 9.7 7.2 9.9 7.1C9.9 7.1 10 7 10 7L12 7C13 7 13 7 13 7.1C13 7.1 13 7.2 13 7.3C13 7.3 14 7.4 14 7.6C14 7.6 14 7.7 14 7.9C14 7.9 14 8.1 14 8.3L14 9L14 9C15 9 15 9 15 9.1C15 9.1 15 9.2 15 9.3C15 9.3 16 9.4 16 9.6C16 9.6 16 9.7 16 9.9C16 9.9 16 10 16 10ZM13 9L13 8.5C13 8.4 13 8.2 13 8.2C13 8.2 13 8.1 13 8L10 8C10 8 10 8.1 10 8.2C10 8.2 10 8.2 10 8.4L10 9ZM14 13L16 13L16 14C16 15 16 15 16 15C16 15 16 15 16 15C16 15 16 16 15 16C15 16 15 16 15 16C15 16 15 16 15 16L8.5 16C8.3 16 8.1 16 7.9 16C7.9 16 7.7 16 7.6 16C7.6 16 7.4 16 7.3 15C7.3 15 7.2 15 7.1 15C7.1 15 7 15 7 15L7 13L9 13L9 13C9 13 9 13 9 14C9 14 9 14 9.1 14C9.1 14 9.1 14 9.1 14C9.1 14 9.2 14 9.2 14C9.2 14 9.3 14 9.4 14C9.6 14 9.7 14 9.8 14C9.8 14 9.8 14 9.9 14C9.9 14 9.9 14 10 14C10 14 10 14 10 14C10 14 10 13 10 13L10 13L13 13L13 13C13 13 13 13 13 14C13 14 13 14 13 14C13 14 13 14 13 14C13 14 13 14 13 14C13 14 13 14 13 14C14 14 14 14 14 14C14 14 14 14 14 14C14 14 14 14 14 14C14 14 14 14 14 14C14 14 14 13 14 13Z"/>
</svg>'

@edit='<svg fill="none" viewBox="0 0 16 16">
	<path fill="@image.color1" d="M15.95 2.56C15.95 2.94 15.88 3.3 15.74 3.66C15.74 3.66 15.6 4.01 15.4 4.32L5.14 14.56C5.08 14.63 5.01 14.68 4.93 14.72C4.93 14.72 4.85 14.76 4.77 14.8L0.81 15.8C0.76 15.81 0.72 15.81 0.69 15.81C0.55 15.81 0.43 15.76 0.34 15.66C0.34 15.66 0.24 15.57 0.19 15.45C0.19 15.28 0.19 15.24 0.2 15.19L1.17 11.33C1.2 11.23 1.24 11.15 1.28 11.07C1.28 11.07 1.32 10.99 1.38 10.92L11.53 0.77C11.77 0.52 12.05 0.33 12.38 0.2C12.38 0.2 12.71 0.07 13.05 0C13.74 0 14.08 0.07 14.39 0.2C14.39 0.2 14.7 0.33 14.98 0.51C14.98 0.51 15.21 0.74 15.45 0.98C15.45 0.98 15.63 1.25 15.76 1.56C15.76 1.56 15.89 1.88 15.95 2.21ZM14.95 2.59C14.95 2.38 14.91 2.17 14.84 1.97C14.84 1.97 14.76 1.77 14.65 1.6C14.65 1.6 14.52 1.45 14.38 1.31C14.38 1.31 14.22 1.2 14.02 1.12C14.02 1.12 13.83 1.04 13.62 1C13.19 1 13.02 1.03 12.87 1.08C12.87 1.08 12.72 1.13 12.58 1.2C12.58 1.2 12.45 1.3 12.32 1.39C12.32 1.39 12.19 1.5 12.08 1.62C12.08 1.62 11.96 1.74 11.84 1.86L14 4.28C14.14 4.16 14.26 4.03 14.38 3.91C14.38 3.91 14.49 3.79 14.59 3.67C14.59 3.67 14.67 3.54 14.76 3.41C14.76 3.41 14.82 3.27 14.88 3.12C14.88 3.12 14.93 2.97 14.95 2.79ZM1.38 14.63L4.44 13.86L13.3 4.98L11.02 2.7L2.14 11.56Z"/>
</svg>'

@troubleshoot_compatibility='<svg fill="none" viewBox="0 0 16 16">
	@svg_window_template2
	<path fill="@image.color2" d="M7 11.5C7 10.88 7.12 10.29 7.359 9.742C7.359 9.742 7.599 9.195 7.922 8.719C7.922 8.719 8.328 8.312 8.734 7.906C8.734 7.906 9.211 7.586 9.758 7.352C9.758 7.352 10.3 7.117 10.89 7C11.92 7 12.32 7.052 12.7 7.156C12.7 7.156 13.08 7.26 13.43 7.412C13.43 7.412 13.77 7.609 14.1 7.807C14.1 7.807 14.4 8.044 14.68 8.32C14.68 8.32 14.96 8.596 15.19 8.901C15.19 8.901 15.39 9.234 15.59 9.568C15.59 9.568 15.74 9.925 15.84 10.3C15.84 10.3 15.95 10.68 16 11.08C16 12.11 15.88 12.7 15.65 13.24C15.65 13.24 15.41 13.79 15.09 14.27C15.09 14.27 14.69 14.67 14.28 15.08C14.28 15.08 13.8 15.4 13.26 15.64C13.26 15.64 12.71 15.88 12.12 16C10.88 16 10.29 15.88 9.742 15.65C9.742 15.65 9.195 15.41 8.719 15.09C8.719 15.09 8.312 14.69 7.906 14.28C7.906 14.28 7.586 13.8 7.352 13.26C7.352 13.26 7.117 12.71 7 12.12Z"/>
	<path fill="@color_islight_WB" d="M12.12 9.5C12.12 9.323 12.07 9.175 11.95 9.055C11.95 9.055 11.83 8.935 11.68 8.875C11.33 8.875 11.19 8.935 11.06 9.055C11.06 9.055 10.94 9.175 10.88 9.323C10.88 9.667 10.94 9.812 11.06 9.938C11.06 9.938 11.19 10.06 11.33 10.12C11.68 10.12 11.83 10.06 11.95 9.938C11.95 9.938 12.07 9.812 12.12 9.667ZM12 13.5L12 11.5C12 11.36 11.95 11.25 11.85 11.15C11.85 11.15 11.75 11.05 11.64 11C11.36 11 11.25 11.05 11.15 11.15C11.15 11.15 11.05 11.25 11 11.36L11 13.5C11 13.64 11.05 13.75 11.15 13.85C11.15 13.85 11.25 13.95 11.36 14C11.64 14 11.75 13.95 11.85 13.85C11.85 13.85 11.95 13.75 12 13.64Z"/>
</svg>'

@customize_this_folder='<svg fill="none" viewBox="0 0 16 16">
	<path fill="@image.color1" d="M0 3.5C0 3.4 0.05 3.2 0.15 3.1C0.15 3.1 0.25 3.1 0.36 3L8.1 3C8.1 2.7 8.2 2.4 8.4 2.2C8.4 2.2 8.5 1.9 8.7 1.7C8.7 1.7 8.9 1.6 9.1 1.4C9.1 1.4 9.4 1.2 9.7 1.2C9.7 1.2 9.9 1.1 10 1C11 1 11 1.1 11 1.2C11 1.2 12 1.2 12 1.4C12 1.4 12 1.6 12 1.7C12 1.7 12 1.9 13 2.2C13 2.2 13 2.4 13 2.7L16 3C16 3 16 3.1 16 3.1C16 3.1 16 3.2 16 3.4C16 3.6 16 3.8 16 3.9C16 3.9 16 4 16 4L13 4C13 4.3 13 4.5 13 4.8C13 4.8 12 5 12 5.2C12 5.2 12 5.4 12 5.6C12 5.6 12 5.7 11 5.8C11 5.8 11 6 11 6C10 6 9.9 6 9.7 5.8C9.7 5.8 9.4 5.7 9.1 5.6C9.1 5.6 8.9 5.4 8.7 5.2C8.7 5.2 8.5 5 8.4 4.8C8.4 4.8 8.2 4.5 8.1 4.3L0.5 4C0.36 4 0.25 4 0.15 3.9C0.15 3.9 0.05 3.8 0 3.6ZM16 12C16 13 16 13 16 13C16 13 16 13 16 13L8 13C7.9 13 7.8 14 7.6 14C7.6 14 7.5 14 7.3 14C7.3 14 7.1 14 6.9 15C6.9 15 6.6 15 6.3 15C6.3 15 6.1 15 5.8 15C5.2 15 4.9 15 4.7 15C4.7 15 4.4 15 4.1 15C4.1 15 3.9 14 3.7 14C3.7 14 3.5 14 3.4 14C3.4 14 3.2 14 3.1 13L0.5 13C0.36 13 0.25 13 0.15 13C0.15 13 0.05 13 0 13C0 12 0.05 12 0.15 12C0.15 12 0.25 12 0.36 12L3.1 12C3.1 12 3.2 11 3.4 11C3.4 11 3.5 11 3.7 11C3.7 11 3.9 11 4.1 10C4.1 10 4.4 10 4.7 10C4.7 10 4.9 10 5.2 10C5.8 10 6.1 10 6.3 10C6.3 10 6.6 10 6.9 10C6.9 10 7.1 11 7.3 11C7.3 11 7.5 11 7.6 11C7.6 11 7.8 11 7.9 12L16 12C16 12 16 12 16 12C16 12 16 12 16 12ZM9 3.5C9 3.7 9 3.9 9.1 4.1C9.1 4.1 9.2 4.2 9.3 4.4C9.3 4.4 9.5 4.5 9.6 4.7C9.6 4.7 9.8 4.8 9.9 4.9C9.9 4.9 10 5 10 5C11 5 11 5 11 4.9C11 4.9 11 4.8 11 4.7C11 4.7 12 4.5 12 4.4C12 4.4 12 4.2 12 4.1C12 4.1 12 3.9 12 3.7C12 3.3 12 3.1 12 2.9C12 2.9 12 2.7 12 2.6C12 2.6 12 2.4 11 2.3C11 2.3 11 2.2 11 2.1C11 2.1 11 2 11 2C10 2 10 2 9.9 2.1C9.9 2.1 9.8 2.2 9.6 2.3C9.6 2.3 9.5 2.4 9.3 2.6C9.3 2.6 9.2 2.7 9.1 2.9C9.1 2.9 9 3.1 9 3.3ZM7 12C7 12 7 12 6.9 12C6.9 12 6.8 12 6.7 12C6.7 12 6.6 11 6.4 11C6.4 11 6.3 11 6.1 11C6.1 11 5.9 11 5.7 11C5.3 11 5.1 11 4.9 11C4.9 11 4.8 11 4.6 11C4.6 11 4.5 11 4.3 12C4.3 12 4.2 12 4.1 12C4.1 12 4 12 4 12C4 13 4 13 4.1 13C4.1 13 4.2 13 4.3 13C4.3 13 4.5 14 4.6 14C4.6 14 4.8 14 4.9 14C4.9 14 5.1 14 5.3 14C5.7 14 5.9 14 6.1 14C6.1 14 6.3 14 6.4 14C6.4 14 6.6 14 6.7 13C6.7 13 6.8 13 6.9 13C6.9 13 7 13 7 13Z"/>
</svg>'

@give_access_to='<svg fill="none" viewBox="0 0 20 20">
	<path fill="@image.color1" d="M 3.75 5 L 3.75 4.90 C 3.75 4.22 3.88 3.58 4.15 2.99 C 4.42 2.39 4.79 1.87 5.24 1.43 C 5.70 0.99 6.23 0.64 6.84 0.38 C 7.44 0.12 8.07 0 8.75 0 C 9.44 0 10.08 0.13 10.69 0.39 C 11.30 0.65 11.83 1.00 12.29 1.45 C 12.74 1.91 13.09 2.44 13.35 3.05 C 13.61 3.66 13.75 4.30 13.75 5 C 13.75 5.69 13.61 6.33 13.35 6.94 C 13.09 7.55 12.74 8.08 12.29 8.54 C 11.83 8.99 11.30 9.34 10.69 9.60 C 10.08 9.86 9.44 10 8.75 10 C 8.05 10 7.41 9.86 6.80 9.60 C 6.19 9.34 5.66 8.99 5.20 8.54 C 4.75 8.08 4.40 7.55 4.14 6.94 C 3.88 6.33 3.75 5.69 3.75 5 Z M 12.5 5 L 12.5 4.92 C 12.5 4.41 12.39 3.93 12.19 3.49 C 11.99 3.04 11.72 2.65 11.37 2.32 C 11.03 1.99 10.63 1.73 10.18 1.53 C 9.72 1.34 9.25 1.25 8.75 1.25 C 8.22 1.25 7.74 1.34 7.28 1.54 C 6.82 1.73 6.43 2.00 6.09 2.34 C 5.75 2.68 5.48 3.07 5.29 3.53 C 5.09 3.99 5 4.47 5 5 C 5 5.52 5.09 6.00 5.29 6.46 C 5.48 6.92 5.75 7.31 6.09 7.65 C 6.43 7.99 6.82 8.26 7.28 8.45 C 7.74 8.65 8.22 8.75 8.75 8.75 C 9.27 8.75 9.75 8.65 10.21 8.45 C 10.67 8.26 11.06 7.99 11.40 7.65 C 11.74 7.31 12.01 6.92 12.20 6.46 C 12.40 6.00 12.5 5.52 12.5 5 Z M 0 13.75 C 0 13.40 0.06 13.08 0.19 12.77 C 0.32 12.47 0.50 12.21 0.73 11.98 C 0.96 11.75 1.22 11.57 1.52 11.44 C 1.83 11.31 2.15 11.25 2.5 11.25 L 11.40 11.25 C 11.35 11.45 11.31 11.66 11.28 11.87 C 11.26 12.08 11.25 12.29 11.25 12.5 L 2.5 12.5 C 2.32 12.5 2.15 12.53 2.00 12.59 C 1.85 12.66 1.72 12.75 1.61 12.86 C 1.50 12.97 1.41 13.10 1.34 13.25 C 1.28 13.40 1.25 13.57 1.25 13.75 C 1.25 14.32 1.33 14.84 1.50 15.30 C 1.68 15.76 1.91 16.17 2.21 16.52 C 2.51 16.88 2.86 17.18 3.27 17.44 C 3.67 17.70 4.10 17.91 4.57 18.09 C 5.03 18.26 5.51 18.39 6.01 18.49 C 6.51 18.59 7.01 18.66 7.5 18.70 C 7.5 18.78 7.49 18.86 7.49 18.95 C 7.49 19.03 7.49 19.11 7.49 19.20 C 7.49 19.33 7.49 19.46 7.50 19.59 C 7.52 19.72 7.55 19.84 7.59 19.96 C 6.97 19.92 6.36 19.84 5.74 19.71 C 5.13 19.59 4.54 19.42 3.97 19.19 C 3.41 18.97 2.89 18.70 2.40 18.37 C 1.92 18.04 1.50 17.65 1.14 17.21 C 0.79 16.76 0.51 16.25 0.30 15.68 C 0.10 15.11 0 14.46 0 13.75 Z "/>
	<path fill="@image.color2" d="M 9.37 20 C 9.20 20 9.05 19.93 8.93 19.81 C 8.81 19.69 8.75 19.54 8.75 19.37 L 8.75 17.5 C 8.75 17.33 8.81 17.18 8.93 17.06 L 12.60 13.38 C 12.53 13.05 12.5 12.72 12.5 12.42 C 12.5 11.91 12.60 11.43 12.80 10.99 C 13.00 10.54 13.27 10.15 13.62 9.82 C 13.96 9.49 14.36 9.23 14.81 9.03 C 15.27 8.84 15.74 8.75 16.25 8.75 C 16.77 8.75 17.25 8.84 17.71 9.04 C 18.17 9.23 18.56 9.50 18.90 9.84 C 19.24 10.18 19.51 10.57 19.70 11.03 C 19.90 11.49 20 11.97 20 12.5 C 20 13.00 19.90 13.47 19.71 13.93 C 19.51 14.38 19.25 14.78 18.92 15.12 C 18.59 15.47 18.20 15.74 17.75 15.94 C 17.31 16.14 16.83 16.25 16.32 16.25 C 16.10 16.25 15.87 16.23 15.65 16.20 C 15.43 16.16 15.21 16.11 15 16.03 C 14.99 16.10 15.00 16.18 15.00 16.27 C 15.00 16.37 15.00 16.46 15.00 16.56 C 15.00 16.67 15.00 16.79 14.99 16.90 C 14.97 17.01 14.94 17.11 14.90 17.20 C 14.85 17.29 14.79 17.36 14.70 17.41 C 14.62 17.47 14.51 17.5 14.37 17.5 L 13.75 17.5 C 13.75 17.80 13.74 18.04 13.72 18.20 C 13.70 18.37 13.66 18.49 13.57 18.57 C 13.49 18.66 13.37 18.70 13.20 18.72 C 13.03 18.74 12.79 18.75 12.5 18.75 L 12.5 19.08 C 12.5 19.19 12.49 19.30 12.48 19.40 C 12.46 19.51 12.43 19.61 12.39 19.70 C 12.35 19.79 12.29 19.86 12.21 19.91 C 12.13 19.97 12.01 20 11.87 20 Z M 18.12 11.56 C 18.12 11.30 18.03 11.08 17.85 10.89 C 17.66 10.71 17.44 10.62 17.18 10.62 C 16.92 10.62 16.70 10.71 16.52 10.89 C 16.34 11.08 16.25 11.30 16.25 11.56 C 16.25 11.82 16.34 12.04 16.52 12.22 C 16.70 12.40 16.92 12.5 17.18 12.5 C 17.44 12.5 17.66 12.40 17.85 12.22 C 18.03 12.04 18.12 11.82 18.12 11.56 Z " />
</svg>'

@send_to='<svg fill="none" viewBox="0 0 16 16">
	<path fill="@image.color1" d="M16 7.5C16 7.6 16 7.7 16 7.8C16 7.8 16 7.9 16 7.9L0.72 15C0.66 15 0.58 15 0.5 15C0.35 15 0.23 15 0.14 15C0.14 15 0.05 15 0 15C0 14 0 14 0 14C0 14 0 14 0.01 14L2 7.5L0.02 0.64C0.01 0.62 0 0.6 0 0.58C0 0.58 0 0.56 0 0.53C0 0.35 0.05 0.23 0.14 0.14C0.14 0.14 0.23 0.05 0.35 0C0.58 0 0.66 0.02 0.72 0.05L16 7C16 7.1 16 7.3 16 7.5ZM1.3 14L14 7.5L1.3 1.4L2.9 7L9.5 7C9.6 7 9.8 7 9.8 7.2C9.8 7.2 10 7.2 10 7.4C10 7.6 10 7.8 9.8 7.9C9.8 7.9 9.8 8 9.6 8L2.9 8Z"/>
</svg>'

@include_in_library='<svg fill="none" viewBox="0 0 16 16">
	<path fill="@image.color1" d="M1.5 16C1.3 16 1.1 16 0.93 16C0.93 16 0.75 16 0.59 16C0.59 16 0.45 16 0.3 15C0.3 15 0.2 15 0.12 15C0.12 15 0.04 15 0 15L0 1.5C0 1.3 0.04 1.1 0.12 0.91C0.12 0.91 0.2 0.73 0.3 0.57C0.3 0.57 0.45 0.44 0.59 0.3C0.59 0.3 0.75 0.2 0.93 0.12C0.93 0.12 1.1 0.04 1.3 0L2.5 0C2.7 0 2.9 0.04 3.1 0.12C3.1 0.12 3.3 0.2 3.4 0.3C3.4 0.3 3.6 0.44 3.7 0.57C3.7 0.57 3.8 0.73 3.9 0.91C3.9 0.91 4 1.1 4 1.3L4 14C4 15 4 15 3.9 15C3.9 15 3.8 15 3.7 15C3.7 15 3.6 16 3.4 16C3.4 16 3.3 16 3.1 16C3.1 16 2.9 16 2.7 16ZM6.5 16C6.3 16 6.1 16 5.9 16C5.9 16 5.8 16 5.6 16C5.6 16 5.5 16 5.3 15C5.3 15 5.2 15 5.1 15C5.1 15 5 15 5 15L5 1.5C5 1.3 5 1.1 5.1 0.91C5.1 0.91 5.2 0.73 5.3 0.57C5.3 0.57 5.5 0.44 5.6 0.3C5.6 0.3 5.8 0.2 5.9 0.12C5.9 0.12 6.1 0.04 6.3 0L7.5 0C7.7 0 7.9 0.04 8.1 0.12C8.1 0.12 8.3 0.2 8.4 0.3C8.4 0.3 8.6 0.44 8.7 0.57C8.7 0.57 8.8 0.73 8.9 0.91C8.9 0.91 9 1.1 9 1.3L9 14C9 15 9 15 8.9 15C8.9 15 8.8 15 8.7 15C8.7 15 8.6 16 8.4 16C8.4 16 8.3 16 8.1 16C8.1 16 7.9 16 7.7 16ZM2.5 15C2.6 15 2.8 15 2.9 15C2.9 15 3 15 3 15L3 1.5C3 1.4 3 1.2 2.9 1.2C2.9 1.2 2.8 1.1 2.6 1L1.5 1C1.4 1 1.2 1.1 1.2 1.2C1.2 1.2 1.1 1.2 1 1.4L1 14C1 15 1.1 15 1.2 15C1.2 15 1.2 15 1.4 15ZM7.5 15C7.6 15 7.8 15 7.9 15C7.9 15 8 15 8 15L8 1.5C8 1.4 8 1.2 7.9 1.2C7.9 1.2 7.8 1.1 7.6 1L6.5 1C6.4 1 6.2 1.1 6.2 1.2C6.2 1.2 6 1.2 6 1.4L6 14C6 15 6 15 6.2 15C6.2 15 6.2 15 6.4 15ZM10 4.7C10 4.4 10 4.1 10 3.8C10 3.8 11 3.5 11 3.4C11 3.2 11 3.1 12 3.1C12 3.1 12 3 12 3C12 3 13 3 13 3.1C13 3.1 13 3.1 13 3.2C13 3.2 13 3.3 13 3.4C13 3.4 13 3.6 14 3.7C14 3.7 14 3.8 14 4L16 14C16 14 16 14 16 14C16 15 16 15 16 15C16 15 15 15 15 16C15 16 14 16 14 16C14 16 14 16 14 16C13 16 13 16 13 16C13 16 13 16 13 16C13 16 13 16 12 16C12 16 12 15 12 15C12 15 12 15 12 15L10 5C10 4.9 10 4.8 10 4.7ZM15 14C15 14 15 14 15 14L13 4.4C13 4.3 13 4.2 13 4.1C13 4.1 12 4 12 4C12 4 12 4 12 4.1C12 4.1 12 4.1 12 4.2C11 4.2 11 4.3 11 4.4C11 4.4 11 4.5 11 4.6C11 4.7 11 4.7 11 4.8C11 4.8 11 4.8 11 4.8L13 15C13 15 13 15 13 15C13 15 13 15 13 15C14 15 14 15 14 15C14 15 14 15 14 15C14 15 14 15 14 15C14 15 14 15 14 15C14 15 14 15 15 15C15 15 15 15 15 15C15 15 15 14 15 14Z"/>
</svg>'

@add_a_network_location='<svg fill="none" viewBox="0 0 16 16">
  <path fill="@color3" d="M1.5 2.5a1 1 0 011-1h8a1 1 0 011 1v5a1 1 0 01-1 1h-8a1 1 0 01-1-1v-5z"/>
  <path fill="@image.color1" d="M1 2.5A1.5 1.5 0 012.5 1h8A1.5 1.5 0 0112 2.5v5A1.5 1.5 0 0110.5 9h-8A1.5 1.5 0 011 7.5v-5zM2.5 2a.5.5 0 00-.5.5v5a.5.5 0 00.5.5h8a.5.5 0 00.5-.5v-5a.5.5 0 00-.5-.5h-8zM7.5 10.5a.5.5 0 01.5.5v1.5a.5.5 0 01-1 0V11a.5.5 0 01.5-.5zM0 14.5a.5.5 0 01.5-.5h4a.5.5 0 010 1h-4a.5.5 0 01-.5-.5zM10 14.5a.5.5 0 01.5-.5h4a.5.5 0 010 1h-4a.5.5 0 01-.5-.5z"/>
  <path fill="@color3" d="M4.5 14a.5.5 0 01.5-.5h1.5V13a.5.5 0 01.5-.5h1a.5.5 0 01.5.5v.5H10a.5.5 0 01.5.5v1a.5.5 0 01-.5.5H5a.5.5 0 01-.5-.5v-1z"/>
  <path fill="@image.color2" d="M6 13a1 1 0 011-1h1a1 1 0 011 1h1a1 1 0 011 1v1a1 1 0 01-1 1H5a1 1 0 01-1-1v-1a1 1 0 011-1h1zm2 0H7v.5a.5.5 0 01-.5.5H5v1h5v-1H8.5a.5.5 0 01-.5-.5V13z"/>
  <path fill="@image.color1" d="M3.08 10A1.5 1.5 0 004.5 11h7A2.5 2.5 0 0014 8.5v-4a1.5 1.5 0 00-1-1.41V8.5a1.5 1.5 0 01-1.5 1.5H3.08z"/>
</svg>'

@disconnect_network_drive='<svg fill="none" viewBox="0 0 16 16">
  <g clip-path="url(#clip0)">
    <path fill="@color3" d="M2.5 2.5H2A1.5 1.5 0 00.5 4v2A1.5 1.5 0 002 7.5h5.5l-5-5zM9.62 7.5H13A1.5 1.5 0 0014.5 6V4A1.5 1.5 0 0013 2.5H4.62l5 5zM4.5 12a.5.5 0 01.5-.5h1.5V11a.5.5 0 01.5-.5h1a.5.5 0 01.5.5v.5H10a.5.5 0 01.5.5v1a.5.5 0 01-.5.5H5a.5.5 0 01-.5-.5v-1z"/>
    <g>
      <path fill="@image.color1" d="M0 4a2 2 0 012-2l1 1H2a1 1 0 00-1 1v2a1 1 0 001 1h5l1 1v2a1 1 0 011 1h1a1 1 0 011 1h1l1 1h-2a1 1 0 01-1 1H5a1 1 0 01-1-1H.5a.5.5 0 010-1H4a1 1 0 011-1h1a1 1 0 011-1V8H2a2 2 0 01-2-2V4zm5 9h5v-1H8.5a.5.5 0 01-.5-.5V11H7v.5a.5.5 0 01-.5.5H5v1zM13 7H9.12l1 1H13a2 2 0 002-2V4a2 2 0 00-2-2H4.12l1 1H13a1 1 0 011 1v2a1 1 0 01-1 1z"/>
      <path fill="@image.color1" d="M11.25 5.5a.75.75 0 100-********* 0 000 1.5z"/>
    </g>
    <path fill="@image.color2" d="M.14.14a.5.5 0 01.70 0l15 15a.5.5 0 01-.70.70l-15-15a.5.5 0 010-.70z"/>
  </g>
  @clipPath
</svg>'

@map_network_drive='<svg fill="none" viewBox="0 0 16 16">
  <path fill="@image.color1" d="M7.5 7a.5.5 0 01.5.5v3a.5.5 0 01-1 0v-3a.5.5 0 01.5-.5z"/>
  <path fill="@color3" d="M13 7.5H2A1.5 1.5 0 01.5 6V4A1.5 1.5 0 012 2.5h11A1.5 1.5 0 0114.5 4v2A1.5 1.5 0 0113 7.5z"/>
  <path fill="@image.color1" d="M0 4a2 2 0 012-2h11a2 2 0 012 2v2a2 2 0 01-2 2H2a2 2 0 01-2-2V4zm2-1a1 1 0 00-1 1v2a1 1 0 001 1h11a1 1 0 001-1V4a1 1 0 00-1-1H2z"/>
  <path fill="@image.color1" d="M12 4.75a.75.75 0 11-1.5 0 .75.75 0 011.5 0zM0 12.5a.5.5 0 01.5-.5h4a.5.5 0 010 1h-4a.5.5 0 01-.5-.5zM10 12.5a.5.5 0 01.5-.5h4a.5.5 0 010 1h-4a.5.5 0 01-.5-.5z"/>
  <path fill="@color3" d="M4.5 12a.5.5 0 01.5-.5h1.5V11a.5.5 0 01.5-.5h1a.5.5 0 01.5.5v.5H10a.5.5 0 01.5.5v1a.5.5 0 01-.5.5H5a.5.5 0 01-.5-.5v-1z"/>
  <path fill="@image.color2" d="M6 11a1 1 0 011-1h1a1 1 0 011 1h1a1 1 0 011 1v1a1 1 0 01-1 1H5a1 1 0 01-1-1v-1a1 1 0 011-1h1zm2 0H7v.5a.5.5 0 01-.5.5H5v1h5v-1H8.5a.5.5 0 01-.5-.5V11z"/>
</svg>'

@make_available_offline='<svg fill="none" viewBox="0 0 16 16">
  <path fill="@color3" d="M8 .5C5.72.5 3.88 2.18 3.88 4.26a.48.48 0 01-.48.48h-.02a2.87 2.87 0 100 5.75h2.46a5.75 5.75 0 019.65-3.14 2.87 2.87 0 00-2.86-2.60H12.6a.48.48 0 01-.48-.48C12.11 2.18 10.27.5 8 .5z"/>
  <path fill="@image.color1" d="M8 0C5.49 0 3.39 1.86 3.38 4.25h-.00a3.37 3.37 0 000 6.75h2.39c.03-.34.09-.67.17-1H3.37a2.37 2.37 0 110-4.75h.02c.54 0 .98-.44.98-.98C4.38 2.50 5.96 1 8 1c2.04 0 3.61 1.50 3.61 3.26 0 .54.44.98.98.98h.02c1.03 0 1.91.66 2.24 1.58.42.30.79.66 1.12 1.06a3.37 3.37 0 00-3.36-3.65h-.00C12.60 1.86 10.50 0 8 0z"/>
  <path fill="@image.color2" d="M16 11.5a4.5 4.5 0 11-9 0 4.5 4.5 0 019 0z"/>
  <path fill="@color_islight_WB" d="M9.10 11.10c.22-.22.57-.22.79 0l1.60 1.60 1.60-1.60a.56.56 0 11.79.79l-2 2a.56.56 0 01-.79 0l-2-2a.56.56 0 010-.79z"/>
  <path fill="@color_islight_WB" d="M11.5 9a.5.5 0 01.5.5v4a.5.5 0 01-1 0v-4a.5.5 0 01.5-.5z"/>
</svg>'

@make_available_online='<svg fill="none" viewBox="0 0 16 16">
  <path fill="@color3" d="M3.88 6.26C3.88 4.18 5.72 2.5 8 2.5c2.27 0 4.11 1.68 4.11 3.76 0 .26.21.48.48.48h.02a2.87 2.87 0 010 5.75h-9.25a2.87 2.87 0 010-5.75h.02a.48.48 0 00.48-.48z"/>
  <path fill="@image.color1" d="M3.38 6.25C3.39 3.86 5.49 2 8 2c2.50 0 4.60 1.86 4.61 4.25h.00a3.37 3.37 0 110 6.75h-9.25a3.37 3.37 0 110-6.75h.00zM8 3C5.95 3 4.38 4.50 4.38 6.26c0 .54-.44.98-.98.98h-.02a2.37 2.37 0 100 4.75h9.25a2.37 2.37 0 100-4.75H12.6a.98.98 0 01-.98-.98C11.61 4.50 10.04 3 8 3z"/>
</svg>'

@file_explorer='<svg fill="none" viewBox="0 0 16 16">
	<path fill="@image.color1" d="M14 3C14 3 14 3.1 14 3.2C14 3.2 15 3.3 15 3.5C15 3.5 15 3.7 16 4C16 4 16 4.2 16 4.5C16 4.5 16 4.8 16 5.1L16 13C16 13 16 13 16 13C16 13 16 14 16 14C16 14 15 14 15 14C15 14 15 15 14 15C14 15 14 15 14 15L2.5 15C2.1 15 1.8 15 1.5 15C1.5 15 1.2 15 0.95 14C0.95 14 0.73 14 0.52 14C0.52 14 0.34 14 0.2 13C0.2 13 0.07 13 0 13L0 3.4C0 3.1 0.07 2.8 0.2 2.5C0.2 2.5 0.34 2.2 0.52 1.9C0.52 1.9 0.73 1.7 0.95 1.5C0.95 1.5 1.2 1.3 1.5 1.2C1.5 1.2 1.8 1.1 2.1 1L5.5 1C5.9 1 6.3 1.1 6.6 1.3C6.6 1.3 7 1.4 7.3 1.7L8.2 3ZM1 4L5.9 4C6 4 6.2 4 6.3 4C6.3 4 6.4 3.9 6.5 3.9C6.5 3.9 6.6 3.8 6.7 3.7C6.7 3.7 6.9 3.7 7 3.6C7 3.6 7.1 3.5 7.2 3.5C7.2 3.2 7 3 6.9 2.9C6.9 2.9 6.8 2.7 6.7 2.6C6.7 2.6 6.5 2.4 6.4 2.3C6.4 2.3 6.3 2.2 6.1 2.1C6.1 2.1 5.9 2 5.7 2L2.5 2C2.3 2 2.1 2 1.9 2.1C1.9 2.1 1.8 2.2 1.6 2.3C1.6 2.3 1.4 2.4 1.3 2.6C1.3 2.6 1.2 2.7 1.1 2.9C1.1 2.9 1 3.1 1 3.3ZM15 5.5C15 5.3 15 5.1 15 4.9C15 4.9 15 4.7 15 4.6C15 4.6 15 4.4 14 4.3C14 4.3 14 4.2 14 4.1C14 4.1 14 4 14 4L8.2 4C8 4.1 7.8 4.2 7.6 4.4C7.6 4.4 7.5 4.5 7.3 4.6C7.3 4.6 7.1 4.7 6.9 4.8C6.9 4.8 6.7 4.9 6.5 4.9C6.5 4.9 6.4 5 6.1 5L1 5L1 12C1 13 1 13 1.1 13C1.1 13 1.2 13 1.3 13C1.3 13 1.4 14 1.6 14C1.6 14 1.8 14 1.9 14C1.9 14 2.1 14 2.3 14L3 14L3 10C3 10 3 10 3.1 9.9C3.1 9.9 3.2 9.7 3.3 9.6C3.3 9.6 3.5 9.4 3.6 9.3C3.6 9.3 3.8 9.2 3.9 9.1C3.9 9.1 4.1 9 4.3 9L12 9C12 9 12 9 12 9.1C12 9.1 12 9.2 12 9.3C12 9.3 13 9.4 13 9.6C13 9.6 13 9.7 13 9.9C13 9.9 13 10 13 10L13 14L14 14C14 14 14 14 14 14C14 14 14 14 14 14C14 14 15 14 15 13C15 13 15 13 15 13C15 13 15 13 15 13ZM12 14L12 10C12 10 12 10 12 10C12 10 12 10 12 10L4.5 10C4.4 10 4.2 10 4.2 10C4.2 10 4 10 4 10L4 14Z"/>
	<path fill="@image.color2" d="M10 12C11 12 11 12 11 12C11 12 11 12 11 12C11 13 11 13 11 13C11 13 11 13 11 13L5.5 13C5.4 13 5.2 13 5.2 13C5.2 13 5 13 5 13C5 12 5 12 5.2 12C5.2 12 5.2 12 5.4 12Z"/>
</svg>'

@file_explorer_options='<svg fill="none" viewBox="0 0 16 16">
	<path fill="@image.color1" d="M10.65 15.52C10.6 15.52 10.56 15.52 10.52 15.5C10.45 15.46 10.39 15.41 10.34 15.34C10.08 14.92 9.75 14.59 9.36 14.35C8.97 14.11 8.52 13.99 8.02 13.99C7.77 13.99 7.53 14.02 7.32 14.08C7.11 14.14 6.89 14.23 6.68 14.35C6.56 14.42 6.46 14.49 6.37 14.56C6.28 14.63 6.2 14.7 6.12 14.78C6.05 14.86 5.98 14.94 5.91 15.03C5.85 15.12 5.78 15.22 5.7 15.34C5.63 15.45 5.53 15.51 5.4 15.51C5.36 15.51 5.32 15.5 5.27 15.49C5.11 15.45 4.91 15.38 4.69 15.27C4.47 15.16 4.25 15.04 4.02 14.91C3.79 14.78 3.58 14.64 3.38 14.5C3.17 14.35 3.01 14.22 2.88 14.11C2.8 14.04 2.76 13.95 2.76 13.84C2.76 13.78 2.78 13.72 2.81 13.66C2.93 13.44 3.02 13.22 3.08 13.01C3.14 12.8 3.17 12.57 3.17 12.32C3.17 11.83 3.05 11.38 2.81 10.98C2.57 10.58 2.24 10.25 1.82 10.01C1.41 9.77 .96 9.65 .48 9.65C.44 9.65 .4 9.64 .36 9.63C.32 9.62 .29 9.6 .25 9.57C.19 9.52 .15 9.42 .11 9.28C.08 9.13 .05 8.98 .04 8.81C.02 8.64 .01 8.48 .01 8.32C0 8.16 0 8.04 0 7.95C0 7.73 .01 7.51 .02 7.28C.04 7.05 .07 6.83 .11 6.62C.12 6.57 .13 6.53 .15 6.5C.17 6.47 .2 6.44 .23 6.41C.28 6.37 .32 6.35 .36 6.34C.41 6.33 .46 6.33 .51 6.33C.97 6.33 1.4 6.21 1.8 5.98C2.23 5.74 2.56 5.42 2.79 5.01C3.03 4.6 3.15 4.15 3.15 3.66C3.15 3.41 3.11 3.18 3.05 2.97C2.98 2.76 2.89 2.55 2.77 2.33C2.74 2.27 2.72 2.21 2.72 2.15C2.72 2.04 2.76 1.95 2.85 1.88C3.01 1.73 3.19 1.59 3.38 1.46C3.58 1.32 3.77 1.2 3.98 1.09C4.19 .97 4.4 .87 4.61 .77C4.82 .68 5.03 .6 5.23 .52C5.27 .51 5.31 .5 5.36 .5C5.49 .5 5.59 .56 5.66 .67C5.91 1.09 6.23 1.42 6.64 1.66C7.05 1.91 7.49 2.03 7.98 2.03C8.46 2.03 8.91 1.91 9.32 1.67C9.43 1.6 9.54 1.54 9.62 1.46C9.71 1.39 9.8 1.32 9.87 1.24C9.95 1.16 10.02 1.08 10.09 .99C10.15 .9 10.22 .8 10.3 .69C10.33 .64 10.38 .59 10.43 .56C10.49 .52 10.55 .51 10.61 .51C10.65 .51 10.68 .52 10.73 .53C10.93 .6 11.14 .69 11.35 .79C11.57 .88 11.78 .99 11.98 1.11C12.19 1.22 12.39 1.35 12.59 1.48C12.78 1.62 12.96 1.76 13.12 1.91C13.2 1.98 13.24 2.07 13.24 2.19C13.24 2.23 13.24 2.26 13.23 2.29C13.22 2.31 13.21 2.34 13.19 2.37C13.07 2.59 12.98 2.8 12.92 3.01C12.86 3.22 12.83 3.45 12.83 3.7C12.83 4.19 12.95 4.63 13.19 5.04C13.43 5.45 13.76 5.78 14.18 6.02C14.59 6.25 15.04 6.37 15.52 6.37C15.6 6.37 15.68 6.4 15.75 6.45C15.79 6.48 15.81 6.51 15.83 6.54C15.85 6.58 15.86 6.61 15.88 6.66C15.93 6.89 15.96 7.12 15.98 7.36C15.99 7.61 16 7.84 16 8.08C16 8.3 15.99 8.52 15.98 8.74C15.97 8.97 15.94 9.18 15.89 9.4C15.87 9.48 15.83 9.55 15.77 9.61C15.69 9.67 15.6 9.7 15.49 9.7C15.03 9.7 14.6 9.81 14.2 10.04C13.77 10.28 13.44 10.6 13.21 11.02C12.97 11.43 12.85 11.88 12.85 12.36C12.85 12.6 12.88 12.83 12.95 13.05C13.01 13.28 13.11 13.49 13.23 13.7C13.25 13.73 13.26 13.76 13.27 13.78C13.28 13.8 13.28 13.83 13.28 13.88C13.28 13.97 13.24 14.06 13.15 14.14C12.8 14.44 12.43 14.7 12.02 14.93C11.6 15.16 11.19 15.35 10.77 15.5C10.72 15.52 10.68 15.52 10.65 15.52ZM7.99 11.99C8.54 11.99 9.07 11.89 9.55 11.68C10.04 11.46 10.47 11.18 10.83 10.81C11.19 10.45 11.48 10.02 11.69 9.54C11.9 9.05 12.01 8.53 12.01 7.97C12.01 7.42 11.9 6.9 11.69 6.42C11.47 5.94 11.19 5.52 10.82 5.16C10.46 4.8 10.04 4.51 9.55 4.31C9.06 4.1 8.55 4 8 4C7.45 4 6.94 4.1 6.45 4.31C5.97 4.52 5.55 4.81 5.18 5.17C4.82 5.53 4.54 5.95 4.32 6.44C4.11 6.92 4.01 7.44 4.01 7.98C4.01 8.54 4.11 9.06 4.32 9.54C4.53 10.03 4.81 10.45 5.17 10.82C5.53 11.18 5.95 11.46 6.44 11.68C6.92 11.89 7.44 11.99 7.99 11.99Z"/>
	<path fill="@image.color2" d="M5.34 8L5.34 7.95C5.34 7.58 5.41 7.24 5.55 6.92C5.69 6.6 5.89 6.33 6.13 6.09C6.38 5.86 6.66 5.67 6.98 5.54C7.3 5.4 7.64 5.34 8 5.34C8.37 5.34 8.72 5.4 9.04 5.54C9.37 5.68 9.65 5.87 9.89 6.11C10.13 6.35 10.32 6.63 10.46 6.96C10.6 7.28 10.66 7.63 10.66 8C10.66 8.37 10.6 8.72 10.46 9.04C10.32 9.37 10.13 9.65 9.89 9.89C9.65 10.13 9.37 10.32 9.04 10.46C8.72 10.6 8.37 10.66 8 10.66C7.63 10.66 7.28 10.6 6.96 10.46C6.63 10.32 6.35 10.13 6.11 9.89C5.87 9.65 5.68 9.37 5.54 9.04C5.4 8.72 5.34 8.37 5.34 8Z "/>
</svg>'

@print='<svg fill="none" viewBox="0 0 16 16">
	<path fill="@image.color1" d="M16 5.5L16 10C16 11 16 11 16 11C16 11 16 11 16 11C15 12 15 12 15 12C15 12 15 12 15 12L13 12L13 14C13 14 13 14 13 14C13 14 13 14 13 14C13 14 13 15 12 15C12 15 12 15 12 15C12 15 12 15 12 15L4.5 15C4.3 15 4.1 15 3.9 15C3.9 15 3.8 15 3.6 15C3.6 15 3.5 15 3.3 14C3.3 14 3.2 14 3.1 14C3.1 14 3 14 3 14L3 12L1.5 12C1.3 12 1.1 12 0.93 12C0.93 12 0.75 12 0.58 12C0.3 11 0.2 11 0.12 11C0.12 11 0.04 11 0 11L0 5.5C0 5.2 0.07 4.8 0.2 4.5C0.2 4.5 0.33 4.2 0.51 4C0.51 4 0.73 3.7 0.96 3.5C0.96 3.5 1.2 3.3 1.5 3.2C1.5 3.2 1.8 3.1 2.2 3L3 3L3 2.5C3 2.3 3 2.1 3.1 1.9C3.1 1.9 3.2 1.7 3.3 1.6C3.3 1.6 3.4 1.4 3.6 1.3C3.6 1.3 3.7 1.2 3.9 1.1C3.9 1.1 4.1 1 4.3 1L12 1C12 1 12 1 12 1.1C12 1.1 12 1.2 12 1.3C12 1.3 13 1.4 13 1.6C13 1.6 13 1.7 13 1.9C13 1.9 13 2.1 13 2.3L13 3L14 3C14 3 14 3.1 14 3.2C14 3.2 15 3.3 15 3.5C15 3.5 15 3.7 16 4C16 4 16 4.2 16 4.5C16 4.5 16 4.8 16 5.2ZM4 3L12 3L12 2.5C12 2.4 12 2.2 12 2.1C12 2.1 12 2 12 2L4.5 2C4.4 2 4.2 2 4.2 2.1C4.2 2.1 4 2.2 4 2.4ZM15 5.5C15 5.3 15 5.1 15 4.9C15 4.9 15 4.7 15 4.6C15 4.6 15 4.4 14 4.3C14 4.3 14 4.2 14 4.1C14 4.1 14 4 14 4L2.5 4C2.3 4 2.1 4 1.9 4.1C1.9 4.1 1.7 4.2 1.6 4.3C1.6 4.3 1.4 4.4 1.3 4.6C1.3 4.6 1.2 4.7 1.1 4.9C1.1 4.9 1 5.1 1 5.3L1 10C1 11 1.1 11 1.2 11C1.2 11 1.2 11 1.4 11L3 11L3 9.5C3 9.3 3 9.1 3.1 8.9C3.1 8.9 3.2 8.7 3.3 8.6C3.3 8.6 3.5 8.4 3.6 8.3C3.6 8.3 3.8 8.2 3.9 8.1C3.9 8.1 4.1 8 4.3 8L12 8C12 8 12 8 12 8.1C12 8.1 12 8.2 12 8.3C12 8.3 13 8.4 13 8.6C13 8.6 13 8.7 13 8.9C13 8.9 13 9.1 13 9.3L13 11L14 11C15 11 15 11 15 11C15 11 15 11 15 11ZM12 9.5C12 9.4 12 9.2 12 9.2C12 9.2 12 9.1 12 9L4.5 9C4.4 9 4.2 9.1 4.2 9.2C4.2 9.2 4 9.2 4 9.4L4 14C4 14 4 14 4.2 14C4.2 14 4.2 14 4.4 14L12 14C12 14 12 14 12 14C12 14 12 14 12 14Z"/>
</svg>'

@device_manager='<svg fill="none" viewBox="0 0 16 16">
	<path fill="@image.color1" d="M7 14L7 13L10 13L10 11L7 11L7 10L14 10C14.15 10 14.28 9.974 14.4 9.922C14.4 9.922 14.52 9.87 14.62 9.797C14.62 9.797 14.71 9.703 14.8 9.609C14.8 9.609 14.87 9.503 14.92 9.383C14.92 9.383 14.97 9.263 15 9.135L15 2C15 1.865 14.97 1.734 14.92 1.609C14.92 1.609 14.87 1.484 14.8 1.378C14.8 1.378 14.71 1.289 14.62 1.201C14.62 1.201 14.52 1.13 14.39 1.078C14.39 1.078 14.27 1.026 14.14 1L4 1C3.865 1 3.737 1.026 3.617 1.078C3.617 1.078 3.497 1.13 3.391 1.201C3.391 1.201 3.297 1.289 3.203 1.378C3.203 1.378 3.13 1.482 3.078 1.602C3.078 1.602 3.026 1.721 3 1.854L3 5L2 5L2 1.953C2 1.693 2.055 1.445 2.164 1.211C2.164 1.211 2.273 0.9766 2.417 0.7682C2.417 0.7682 2.594 0.5859 2.771 0.4036C2.771 0.4036 2.979 0.2604 3.219 0.1563C3.219 0.1563 3.458 0.0521 3.708 0L14.05 0C14.31 0 14.55 0.0521 14.79 0.1563C14.79 0.1563 15.02 0.2604 15.23 0.4036C15.23 0.4036 15.41 0.5859 15.6 0.7682C15.6 0.7682 15.74 0.9766 15.84 1.211C15.84 1.211 15.95 1.445 16 1.693L16 9.031C16 9.302 15.95 9.557 15.84 9.797C15.84 9.797 15.73 10.04 15.58 10.24C15.58 10.24 15.4 10.42 15.22 10.6C15.22 10.6 15.01 10.74 14.77 10.84C14.77 10.84 14.53 10.95 14.27 11L11 11L11 13L12.5 13C12.64 13 12.75 13.05 12.85 13.15C12.85 13.15 12.95 13.25 13 13.36C13 13.64 12.95 13.75 12.85 13.85C12.85 13.85 12.75 13.95 12.64 14Z"/>
	<path fill="@image.color2" d="M1.5 16C1.302 16 1.112 15.96 0.9297 15.88C0.9297 15.88 0.7474 15.8 0.5859 15.7C0.5859 15.7 0.4453 15.55 0.3047 15.41C0.3047 15.41 0.1953 15.25 0.1172 15.07C0.1172 15.07 0.0391 14.89 0 14.7L0 7.5C0 7.292 0.0391 7.096 0.1172 6.914C0.1172 6.914 0.1953 6.732 0.3047 6.573C0.3047 6.573 0.4453 6.438 0.5859 6.302C0.5859 6.302 0.7474 6.195 0.9297 6.117C0.9297 6.117 1.112 6.039 1.302 6L4.5 6C4.708 6 4.904 6.039 5.086 6.117C5.086 6.117 5.268 6.195 5.427 6.302C5.427 6.302 5.562 6.438 5.698 6.573C5.698 6.573 5.805 6.732 5.883 6.914C5.883 6.914 5.961 7.096 6 7.292L6 14.5C6 14.7 5.961 14.89 5.883 15.07C5.883 15.07 5.805 15.25 5.698 15.41C5.698 15.41 5.562 15.55 5.427 15.7C5.427 15.7 5.268 15.8 5.086 15.88C5.086 15.88 4.904 15.96 4.708 16ZM4.5 15C4.635 15 4.753 14.95 4.852 14.85C4.852 14.85 4.95 14.75 5 14.64L5 7.5C5 7.365 4.95 7.247 4.852 7.148C4.852 7.148 4.753 7.05 4.635 7L1.5 7C1.365 7 1.247 7.05 1.148 7.148C1.148 7.148 1.05 7.247 1 7.365L1 14.5C1 14.64 1.05 14.75 1.148 14.85C1.148 14.85 1.247 14.95 1.365 15ZM2.5 14C2.365 14 2.247 13.95 2.148 13.85C2.148 13.85 2.05 13.75 2 13.64C2 13.36 2.05 13.25 2.148 13.15C2.148 13.15 2.247 13.05 2.365 13L3.5 13C3.635 13 3.753 13.05 3.852 13.15C3.852 13.15 3.951 13.25 4 13.36C4 13.64 3.951 13.75 3.852 13.85C3.852 13.85 3.753 13.95 3.635 14Z"/>
</svg>'

@disk_management='<svg fill="none" viewBox="0 0 16 16">
	<path fill="@image.color1" d="M0 9C0 8.427 0.0677 7.87 0.2031 7.328C0.2031 7.328 0.3385 6.787 0.5313 6.271C0.5313 6.271 0.7813 5.781 1.031 5.292C1.031 5.292 1.339 4.836 1.703 4.414C1.703 4.414 2.068 3.992 2.484 3.615C3.422 2.948 3.906 2.685 4.406 2.492C4.406 2.492 4.906 2.3 5.438 2.156C6.021 2.062 6.042 2.062 6.062 2.062C6.062 2.062 6.083 2.062 6.104 2.062C6.26 2.062 6.38 2.089 6.484 2.141C6.484 2.141 6.589 2.193 6.68 2.26C6.68 2.26 6.758 2.344 6.836 2.427C6.836 2.427 6.896 2.526 6.938 2.641C6.938 2.641 6.979 2.755 7 2.875L7 9L13 9C13.12 9 13.24 9.021 13.36 9.062C13.36 9.062 13.47 9.104 13.57 9.164C13.57 9.164 13.66 9.242 13.74 9.32C13.74 9.32 13.81 9.412 13.86 9.516C13.86 9.516 13.91 9.62 13.94 9.74C13.94 9.896 13.94 9.917 13.94 9.938C13.94 9.938 13.94 9.958 13.94 9.979C13.84 10.56 13.7 11.09 13.51 11.59C13.51 11.59 13.32 12.09 13.05 12.58C12.39 13.52 12.01 13.93 11.59 14.3C11.59 14.3 11.16 14.66 10.71 14.97C10.71 14.97 10.22 15.22 9.729 15.47C9.729 15.47 9.213 15.66 8.672 15.8C8.672 15.8 8.13 15.93 7.573 16C6.354 16 5.734 15.92 5.141 15.75C5.141 15.75 4.547 15.58 3.99 15.35C3.99 15.35 3.469 15.04 2.948 14.73C2.948 14.73 2.477 14.37 2.055 13.95C2.055 13.95 1.633 13.52 1.268 13.05C1.268 13.05 0.9609 12.53 0.6537 12.01C0.6537 12.01 0.4167 11.45 0.25 10.86C0.25 10.86 0.0833 10.27 0 9.646ZM12.92 10L7 10C6.865 10 6.734 9.974 6.609 9.922C6.609 9.922 6.484 9.87 6.378 9.8C6.378 9.8 6.289 9.711 6.2 9.622C6.2 9.622 6.13 9.516 6.078 9.391C6.078 9.391 6.026 9.266 6 9.135L6 3.078C5.521 3.162 5.065 3.297 4.633 3.484C4.633 3.484 4.2 3.672 3.8 3.904C3.8 3.904 3.43 4.18 3.06 4.456C3.06 4.456 2.724 4.771 2.422 5.125C2.422 5.125 2.12 5.479 1.865 5.862C1.865 5.862 1.656 6.273 1.448 6.685C1.448 6.685 1.286 7.12 1.172 7.578C1.172 7.578 1.057 8.037 1 8.51C1 9.552 1.073 10.08 1.219 10.59C1.219 10.59 1.365 11.1 1.568 11.58C1.568 11.58 1.828 12.02 2.089 12.47C2.089 12.47 2.401 12.87 2.766 13.23C2.766 13.23 3.13 13.6 3.534 13.91C3.534 13.91 3.977 14.17 4.419 14.43C4.419 14.43 4.896 14.64 5.406 14.78C5.406 14.78 5.917 14.93 6.448 15C7.49 15 7.964 14.94 8.422 14.83C8.422 14.83 8.88 14.71 9.315 14.55C9.315 14.55 9.727 14.34 10.14 14.14C10.14 14.14 10.52 13.88 10.88 13.58C10.88 13.58 11.23 13.28 11.54 12.94C11.54 12.94 11.82 12.57 12.1 12.2C12.1 12.2 12.33 11.8 12.52 11.37C12.52 11.37 12.7 10.93 12.84 10.48Z"/>
	<path fill="@image.color2" d="M9 8C8.865 8 8.734 7.974 8.609 7.922C8.609 7.922 8.484 7.87 8.378 7.8C8.378 7.8 8.289 7.711 8.2 7.622C8.2 7.622 8.13 7.516 8.078 7.391C8.078 7.391 8.026 7.266 8 7.135L8 0.9375C8 0.625 8.096 0.3906 8.289 0.2344C8.289 0.2344 8.482 0.0781 8.719 0C9.635 0 10.25 0.0807 10.84 0.2422C10.84 0.2422 11.42 0.4036 11.98 0.6328C11.98 0.6328 12.5 0.9297 13.02 1.227C13.02 1.227 13.5 1.591 13.93 2.023C13.93 2.023 14.36 2.456 14.74 2.943C15.4 4.068 15.64 4.654 15.78 5.242C15.78 5.242 15.93 5.831 16 6.458C16 7.25 15.97 7.367 15.92 7.477C15.92 7.477 15.87 7.586 15.8 7.68C15.8 7.68 15.72 7.758 15.64 7.836C15.64 7.836 15.54 7.896 15.42 7.938C15.42 7.938 15.31 7.979 15.19 8ZM15 7C15 6.167 14.84 5.388 14.53 4.664C14.53 4.664 14.22 3.94 13.79 3.305C13.79 3.305 13.24 2.758 12.7 2.211C12.7 2.211 12.06 1.781 11.34 1.469C11.34 1.469 10.61 1.156 9.833 1L9 7Z"/>
</svg>'

@filter='<svg fill="none" viewBox="0 0 16 16">
	<path fill="@image.color1" d="M0 2.5C0 2.292 0.0391 2.096 0.1172 1.914C0.1172 1.914 0.1953 1.732 0.3021 1.573C0.3021 1.573 0.4375 1.438 0.5729 1.302C0.5729 1.302 0.7318 1.195 0.9141 1.117C0.9141 1.117 1.096 1.039 1.292 1L14.5 1C14.71 1 14.9 1.039 15.09 1.117C15.09 1.117 15.27 1.195 15.43 1.302C15.43 1.302 15.56 1.438 15.7 1.573C15.7 1.573 15.8 1.732 15.88 1.914C15.88 1.914 15.96 2.096 16 2.292C16 2.927 15.84 3.287 15.53 3.578L10.47 8.406C10.4 8.479 10.33 8.557 10.27 8.641C10.27 8.641 10.22 8.724 10.17 8.812C10.04 9.083 10 9.281 10 9.5L10 15.5C10 15.64 9.95 15.75 9.852 15.85C9.852 15.85 9.753 15.95 9.635 16C9.406 16 9.318 15.97 9.234 15.91L6.234 13.91C6.078 13.81 6 13.68 6 13.5L6 9.5C6 9.292 5.964 9.094 5.891 8.906C5.849 8.812 5.797 8.724 5.734 8.641C5.734 8.641 5.672 8.557 5.604 8.479L0.4688 3.578C0.1563 3.287 0 2.927 0 2.5ZM15 2.484C15 2.349 14.95 2.234 14.85 2.141C14.85 2.141 14.75 2.047 14.64 2L1.5 2C1.365 2 1.247 2.047 1.148 2.141C1.148 2.141 1.05 2.234 1 2.349C1 2.641 1.052 2.766 1.156 2.859L6.234 7.688C6.474 7.917 6.662 8.19 6.797 8.508C6.797 8.508 6.932 8.825 7 9.156L7 13.23L9 14.56L9 9.5C9 9.156 9.068 8.825 9.203 8.508C9.203 8.508 9.338 8.19 9.531 7.917L14.84 2.859C14.95 2.766 15 2.641 15 2.484Z"/>
</svg>'

@window ='<svg fill="none" viewBox="0 0 16 16">
	@svg_window_template
</svg>'

@code='<svg fill="none">
	<path fill="@image.color1" d="M70.6,25.4c0.6-0.6,0.5-1.7,0-2.3L67.5,20L53.7,6.6c-0.6-0.6-1.5-0.6-2.2,0l0,0c-0.6,0.6-0.8,1.7,0,2.3 L66,23.1c0.6,0.6,0.6,1.7,0,2.3L51.2,40.1c-0.6,0.6-0.6,1.7,0,2.3l0,0c0.6,0.6,1.7,0.5,2.2,0l13.7-13.6c0,0,0,0,0.1-0.1L70.6,25.4z"/>
	<path fill="@image.color1" d="M7.4,25.4c-0.6-0.6-0.5-1.7,0-2.3l3.1-3.1L24.3,6.6c0.6-0.6,1.5-0.6,2.2,0l0,0c0.6,0.6,0.8,1.7,0,2.3 L12.3,23.1c-0.6,0.6-0.6,1.7,0,2.3l14.5,14.7c0.6,0.6,0.6,1.7,0,2.3l0,0c-0.6,0.6-1.7,0.5-2.2,0L10.6,29c0,0,0,0-0.1-0.1L7.4,25.4z"/> 
	<polygon fill="#FCD116" points="54.6,-4.5 35.1,-4.5 24.6,25.1 37.4,25.2 27.4,54.5 55,15.4 41.6,15.4 "/>
</svg>'

@reddit='<svg fill="none" viewBox="0 0 16 16">
	<path fill="@image.color1" d="M13.7 6.4C14.2 6.4 14.6 6.6 15 7C15.2 7.3 15.3 7.7 15.3 8C15.3 8.2 15.3 8.2 15.3 8.3C15.2 8.9 14.9 9.2 14.4 9.4C14.5 9.7 14.5 9.9 14.5 10.2C14.5 11.1 14.2 11.9 13.6 12.6C13 13.3 12.2 13.9 11.3 14.3C10.2 14.7 9.2 15 8 15C6.8 15 5.7 14.7 4.8 14.3C3.8 13.9 3 13.3 2.3 12.6C1.8 11.9 1.4 11.1 1.4 10.2C1.4 10 1.5 9.8 1.5 9.5C1 9.2 .7 8.9 .6 8.3C.6 8.2 .6 8.2 .6 8C.6 7.7 .7 7.3 1 7C1.3 6.6 1.7 6.4 2.2 6.4C2.2 6.4 2.2 6.4 2.2 6.4C2.7 6.4 3.1 6.6 3.4 6.9C4.6 6.1 6 5.6 7.7 5.6C7.7 5.6 8 4.2 8.6 1.3C8.6 1.2 8.6 1.2 8.7 1.1C94.4 1.1 8.8 1 8.9 1C8.9 1 9 1 9 1C9 1 9.9 1.3 11.9 1.8C12.1 1.5 12.2 1.4 12.4 1.3C12.6 1.1 12.8 1 13 1C13.4 1 13.7 1.2 13.9 1.4C14.2 1.7 14.2 1.9 14.2 2.3C14.2 2.6 14.2 3 13.9 3.2C13.7 3.4 13.4 3.5 13 3.5C12.7 3.5 12.4 3.4 12.2 3.2C11.9 3 11.8 2.6 11.8 2.3C11.8 2.3 11 2.1 9.1 1.7L8.2 5.6C9 5.6 9.8 5.7 10.6 5.9C11.3 6.2 11.9 6.5 12.5 6.9C12.8 6.6 13.2 6.4 13.7 6.4Z"/>
</svg>'

@cortana,show_cortana_button='<svg fill="none" viewBox="0 0 16 16">
  <circle cx="8" cy="8" r="7" stroke="@image.color1" />
  <circle cx="8" cy="8" r="5.5" stroke="@image.color2" />
</svg>'

@nvidia='<svg fill="none" viewBox="0 0 24 24">
	<path fill="@image.color1" d="M8.948 8.798v-1.43a6.7 6.7 0 0 1 .424-.018c3.922-.124 6.493 3.374 6.493 3.374s-2.774 3.851-5.75 3.851c-.398 0-.787-.062-1.158-.185v-4.346c1.528.185 1.837.857 2.747 2.385l2.04-1.714s-1.492-1.952-4-1.952a6.016 6.016 0 0 0-.796.035m0-4.735v2.138l.424-.027c5.45-.185 9.01 4.47 9.01 4.47s-4.08 4.964-8.33 4.964c-.37 0-.733-.035-1.095-.097v1.325c.3.035.61.062.91.062 3.957 0 6.82-2.023 9.593-4.408.459.371 2.34 1.263 2.73 1.652-2.633 2.208-8.772 3.984-12.253 3.984-.335 0-.653-.018-.971-.053v1.864H24V4.063z"/>
	<path fill="@image.color2" d="m0 10.326v1.131c-3.657-.654-4.673-4.46-4.673-4.46s1.758-1.944 4.673-2.262v1.237H8.94c-1.528-.186-2.73 1.245-2.73 1.245s.68 2.412 2.739 3.11M2.456 10.9s2.164-3.197 6.5-3.533V6.201C4.153 6.59 0 10.653 0 10.653s2.35 6.802 8.948 7.42v-1.237c-4.84-.6-6.492-5.936-6.492-5.936z"/>
</svg>'