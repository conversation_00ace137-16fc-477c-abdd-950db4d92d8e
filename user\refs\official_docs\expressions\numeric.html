﻿<h4>Numeric literals</h4>
<br>
<p>There are two types of numbers. Integer and floating point.</p>
<h5>Integer literals</h5>
<p>An integer is a numeric literal(associated with numbers) without any fractional or exponential part. There are two types of integer literals:</p>
<ol>
	<li>Decimal literal (base 10)</li>
	<li>Hexadecimal literal (base 16)</li>
</ol>
<p><strong>1. Decimal-literal(base 10):</strong><br>A non-zero decimal digit followed by zero or more decimal digits(0, 1, 2, 3, 4, 5, 6, 7, 8, 9).</p>
For example:<pre><code>Decimal: 0, -9, 22 etc</code></pre>
<p><strong>2. Hexadecimal-literal(base 16):</strong><br>0x followed by one or more hexadecimal digits(0, 1, 2, 3, 4, 5, 6, 7, 8, 9, a, b, c, d, e, f).</p>
For example:
<pre><code>Hexadecimal: 0x7f, 0x2a, 0x521 etc</code></pre>
<h5>Floating-point iterals</h5>

<p>Floating-point literals specify values that must have a fractional part. These values contain decimal points (.)</p>
For example:
<pre><code>-2.0<br>0.0000234</code></pre>


