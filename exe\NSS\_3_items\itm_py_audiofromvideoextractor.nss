
//
$PY_AUDIOFROMVIDEOEXTRACTOR_DIR = '@user.desktop\my\flow\home\__GOTO__\Scripts\Python\Py_Cli_Utils\py__AudioFromVideoExtractor'
$PY_AUDIOFROMVIDEOEXTRACTOR_EXE = '@PY_AUDIOFROMVIDEOEXTRACTOR_DIR\venv\Scripts\python.exe'
$PY_AUDIOFROMVIDEOEXTRACTOR_APP = '@PY_AUDIOFROMVIDEOEXTRACTOR_DIR\main.py'
//

// Context: Explorer
$PY_AUDIOFROMVIDEOEXTRACTOR_ARGS = '-i "@sel.file" -o "@sel.dir" --prompt'
item(
    title="&AudioFromVideoExtractor"
    keys="py"
    type='File'
    where=str.equals(sel.file.ext,[".m4a",".webm",".wmv",".wav",".f4v",".mov",".mkv",".mp4"])
    //
    image=[E17C,GREY] image-sel=[E17C,PURPLE]
    tip=['"@PY_AUDIOFROMVIDEOEXTRACTOR_APP" @PY_AUDIOFROMVIDEOEXTRACTOR_ARGS',TIP3,0.75]
    //
    admin=keys.rbutton()
    cmd=if(KEYS_EXE_OPEN_EXE,('"@PY_AUDIOFROMVIDEOEXTRACTOR_EXE"'))
    args='"@PY_AUDIOFROMVIDEOEXTRACTOR_APP" @PY_AUDIOFROMVIDEOEXTRACTOR_ARGS'
    //
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@PY_AUDIOFROMVIDEOEXTRACTOR_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@PY_AUDIOFROMVIDEOEXTRACTOR_APP')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@PY_AUDIOFROMVIDEOEXTRACTOR_DIR')),
    }
)

// Context: Titlebar|Taskbar
$PY_AUDIOFROMVIDEOEXTRACTOR_TASKBAR = '--prompt'
item(
    title="&AudioFromVideoExtractor"
    keys="py"
    type='Titlebar|Taskbar'
    //
    image=[E17C,GREY] image-sel=[E17C,PURPLE]
    tip=['"@PY_AUDIOFROMVIDEOEXTRACTOR_APP" @PY_AUDIOFROMVIDEOEXTRACTOR_TASKBAR',TIP3,0.75]
    //
    admin=keys.rbutton()
    args='"@PY_AUDIOFROMVIDEOEXTRACTOR_APP" @PY_AUDIOFROMVIDEOEXTRACTOR_TASKBAR'
    cmd=if(KEYS_EXE_OPEN_EXE,('"@PY_AUDIOFROMVIDEOEXTRACTOR_EXE"'))
    //
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@PY_AUDIOFROMVIDEOEXTRACTOR_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@PY_AUDIOFROMVIDEOEXTRACTOR_APP')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@PY_AUDIOFROMVIDEOEXTRACTOR_DIR')),
    }
)
