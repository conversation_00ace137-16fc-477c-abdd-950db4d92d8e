﻿// Settings
settings
{
	priority=1
	exclude.where = !process.is_explorer
	showdelay = 200
	modify.remove.duplicate=1
	tip.enabled=true
}

// // Dynamic
// static
// {
// 	// item(mode="single" type='file' where=sel.file.len != sel.file.title.len title='Copy "'+@sel.file.title+'"' image=[\uE055, #4d8e4a] cmd=command.copy(sel.file.title))
// 	// item(mode="single" title='Copy "'+@sel.path+'"' tip=sel.path image=[\uE09B, #4d8e4a] cmd=command.copy(sel.path))
// 	item(mode="single" where=@sel.parent.len>3 title='Copy "'+sel.parent+'"' image=[\uE0E7, #4d8e4a] cmd=@command.copy(sel.parent))
// 	separator
// }

// ----------------------------------------------------------------------------
// CLEANUP: FILE-EXPLORER
// ----------------------------------------------------------------------------
menu(type="*" title="Unimportant" image=[\uE1B8, #000000]) {
	item(where=regex.match(this.name, ".*NordVPN.*") menu="Unimportant")
	item(where=regex.match(this.name, ".*Onedrive.*") menu="Unimportant")
	item(where=regex.match(this.name, ".*Dropbox.*") menu="Unimportant")
	item(where=regex.match(this.name, ".*^Add to Favorites.*")  menu="Unimportant")
	item(where=regex.match(this.name, ".*^Open in Terminal.*")  menu="Unimportant")
	item(where=regex.match(this.name, ".*^Run Sandboxed.*")  menu="Unimportant")
	item(where=regex.match(this.name, ".*^Sync or Backup.*")  menu="Unimportant")
	item(where=regex.match(this.name, ".*^Add to VLC Media Player.*")  menu="Unimportant")
	item(where=regex.match(this.name, ".*^Git GUI.*")  menu="Unimportant")
	item(where=regex.match(this.name, ".*^Git Bash.*")  menu="Unimportant")
	item(where=regex.match(this.name, ".*^VENV Utils.*")  menu="Unimportant")
	item(where=regex.match(this.name, ".*^ShareX.*")  menu="Unimportant")
	item(where=regex.match(this.name, ".*^GitKraken.*")  menu="Unimportant")
	item(where=regex.match(this.name, ".*^Microsoft.*")  menu="Unimportant")
	item(where=regex.match(this.name, ".*^Enqueue in Winamp.*") menu="Unimportant")
	item(where=regex.match(this.name, ".*^JetBrains.*") menu="Unimportant")
	item(where=regex.match(this.name, ".*^CFF Explorer.*") menu="Unimportant")
	item(where=regex.match(this.name, ".*^PE Explorer.*") menu="Unimportant")
	item(where=regex.match(this.name, ".*^JustDecompile.*") menu="Unimportant")
	item(where=regex.match(this.name, ".*^Send a copy.*") menu="Unimportant")
	item(where=regex.match(this.name, ".*^Print.*") menu="Unimportant")
}


// Menu: Apps
menu(mode="multiple" type="*" title="Apps" image=\uE00D) {}
// modify(where=regex.match(this.name, ".*Open in Terminal.*")  menu="Apps")
// modify(where=regex.match(this.name, ".*Git Bash.*")  menu="Apps")
// item(title=title.command_prompt tip=tip_run_admin admin=key.shift() image cmd='cmd.exe' args='/K TITLE ^<Prompt^> ..\@sel.dir.name' menu="Apps")
// modify(where=regex.match(this.name, ".*PowerRename.*")  menu="Apps")
// modify(where=regex.match(this.name, ".*Open with Visual Studio.*")  menu="Apps")
// remove(where=regex.match(this.name, ".*CMD.*$")  menu="Apps")
// modify(where=regex.match(this.name, ".*Bulk Rename.*")  menu="Apps")
// modify(where=regex.match(this.name, ".*Add to archive.*")  menu="Apps")
//
// Menu: Unimportant
// menu(mode="multiple" type="*" title="Unimportant" image=[\uE1B8, #000000]) {}
modify(where=regex.match(this.name, ".*NordVPN.*") menu="Unimportant")
modify(where=regex.match(this.name, ".*Onedrive.*") menu="Unimportant")
modify(where=regex.match(this.name, ".*Dropbox.*") menu="Unimportant")
modify(where=regex.match(this.name, ".*^Add to Favorites.*")  menu="Unimportant")
modify(where=regex.match(this.name, ".*^Open in Terminal.*")  menu="Unimportant")
modify(where=regex.match(this.name, ".*^Run Sandboxed.*")  menu="Unimportant")
modify(where=regex.match(this.name, ".*^Sync or Backup.*")  menu="Unimportant")
modify(where=regex.match(this.name, ".*^Add to VLC Media Player.*")  menu="Unimportant")
modify(where=regex.match(this.name, ".*^Git GUI.*")  menu="Unimportant")
modify(where=regex.match(this.name, ".*^Git Bash.*")  menu="Unimportant")
modify(where=regex.match(this.name, ".*^VENV Utils.*")  menu="Unimportant")
modify(where=regex.match(this.name, ".*^ShareX.*")  menu="Unimportant")
modify(where=regex.match(this.name, ".*^GitKraken.*")  menu="Unimportant")
modify(where=regex.match(this.name, ".*^Microsoft.*")  menu="Unimportant")
modify(where=regex.match(this.name, ".*^Enqueue in Winamp.*") menu="Unimportant")
modify(where=regex.match(this.name, ".*^JetBrains.*") menu="Unimportant")
modify(where=regex.match(this.name, ".*^CFF Explorer.*") menu="Unimportant")
modify(where=regex.match(this.name, ".*^PE Explorer.*") menu="Unimportant")
modify(where=regex.match(this.name, ".*^JustDecompile.*") menu="Unimportant")
modify(where=regex.match(this.name, ".*^Send a copy.*") menu="Unimportant")
modify(where=regex.match(this.name, ".*^Print.*") menu="Unimportant")
item(mode=mode.multiple where=this.id(id.restore_previous_versions) menu="Unimportant")
item(mode=mode.multiple where=this.id(id.cast_to_device) menu="Unimportant")
item(mode=mode.multiple where=this.id(id.print) menu="Unimportant")


// DEVELOP
menu(mode="multiple" title='&Develop' image=[\uE22B, #ff66e3])
{
	menu(mode="single" title='editors' image=\uE17A)
	{
		item(title='Visual Studio Code' image=[\uE272, #22A7F2] cmd='code' args='"@sel.path"')
		separator
		item(type='file' mode="single" title='Windows notepad' image cmd='@sys.bin\notepad.exe' args='"@sel.path"')
	}
	menu(mode="multiple" title='dotnet' image=\uE143)
	{
		item(title='run' cmd-line='/K dotnet run' image=\uE149)
		item(title='watch' cmd-line='/K dotnet watch')
		item(title='clean' image=\uE0CE cmd-line='/K dotnet clean')
		separator
		item(title='build debug' cmd-line='/K dotnet build')
		item(title='build release' cmd-line='/K dotnet build -c release /p:DebugType=None')
		menu(mode="multiple" sep="both" title='publish' image=\ue11f)
		{
			$publish='dotnet publish -r win-x64 -c release --output publish /*/p:CopyOutputSymbolsToPublishDirectory=false*/'
			item(title='publish sinale file' sep="after" cmd-line='/K @publish --no-self-contained /p:PublishSingleFile=true')
			item(title='framework-dependent deployment' cmd-line='/K @publish')
			item(title='framework-dependent executable' cmd-line='/K @publish --self-contained false')
			item(title='self-contained deployment' cmd-line='/K @publish --self-contained true')
			item(title='single-file' cmd-line='/K @publish /p:PublishSingleFile=true /p:PublishTrimmed=false')
			item(title='single-file-trimmed' cmd-line='/K @publish /p:PublishSingleFile=true /p:PublishTrimmed=true')
		}
		item(title='ef migrations add InitialCreate' cmd-line='/K dotnet ef migrations add InitialCreate')
		item(title='ef database update' cmd-line='/K dotnet ef database update')
		separator
		item(title='help' image=\uE136 cmd-line='/k dotnet -h')
		item(title='version' cmd-line='/k dotnet --info')
	}
}

// GOTO
// menu(type='~taskbar' where=sel.count mode=mode.multiple title=title.go_to sep=sep.both image=[\uE14A, #ff66e3])
menu(type='~taskbar' where=sel.count mode=mode.multiple title=title.go_to image=[\uE14A, #ff66e3])
{
	menu(title='Folder' image=\uE1F4)
	{
		item(title='Windows' image=inherit cmd=sys.dir)
		item(title='System' image=inherit cmd=sys.bin)
		item(title='Program Files' image=inherit cmd=sys.prog)
		item(title='Program Files x86' image=inherit cmd=sys.prog32)
		item(title='ProgramData' image=inherit cmd=sys.programdata)
		item(title='Applications' image=inherit cmd='shell:appsfolder')
		item(title='Users' image=inherit cmd=sys.users)
		separator
		item(title='@user.name@@@sys.name' vis=label)
		item(title='Desktop' image=inherit cmd=user.desktop)
		item(title='Downloads' image=inherit cmd=user.downloads)
		item(title='Pictures' image=inherit cmd=user.pictures)
		item(title='Documents' image=inherit cmd=user.documents)
		item(title='Startmenu' image=inherit cmd=user.startmenu)
		item(title='Profile' image=inherit cmd=user.dir)
		item(title='AppData' image=inherit cmd=user.appdata)
		item(title='Temp' image=inherit cmd=user.temp)
	}
	item(title=title.control_panel image=\uE0F3 cmd='shell:::{5399E694-6CE5-4D6C-8FCE-1D8870FDCBA0}')
	item(title='All Control Panel Items' image=\uE0F3 cmd='shell:::{ED7BA470-8E54-465E-825C-99712043E01C}')
	item(title=title.run image=\uE14B cmd='shell:::{2559a1f3-21d7-11d4-bdaf-00c04f60b9f0}')
	menu(where=sys.ver.major >= 10 title=title.settings sep=sep.before image=\uE0F3)
	{
		// https://docs.microsoft.com/en-us/windows/uwp/launch-resume/launch-settings-app
		item(title='system' image=inherit cmd='ms-settings:')
		item(title='about' image=inherit cmd='ms-settings:about')
		item(title='your-info' image=inherit cmd='ms-settings:yourinfo')
		item(title='system-info' image=inherit cmd-line='/K systeminfo')
		item(title='search' cmd='search-ms:' image=inherit)
		item(title='usb' image=inherit cmd='ms-settings:usb')
		item(title='windows-update' image=inherit cmd='ms-settings:windowsupdate')
		item(title='windows-defender' image=inherit cmd='ms-settings:windowsdefender')
		menu(title='apps' image=inherit)
		{
			item(title='apps-features' image=inherit cmd='ms-settings:appsfeatures')
			item(title='default-apps' image=inherit cmd='ms-settings:defaultapps')
			item(title='optional-features' image=inherit cmd='ms-settings:optionalfeatures')
			item(title='startup' image=inherit cmd='ms-settings:startupapps')
		}
		menu(title='personalization' image=inherit)
		{
			item(title='personalization' image=inherit cmd='ms-settings:personalization')
			item(title='lockscreen' image=inherit cmd='ms-settings:lockscreen')
			item(title='background' image=inherit cmd='ms-settings:personalization-background')
			item(title='colors' image=inherit cmd='ms-settings:colors')
			item(title='themes' image=inherit cmd='ms-settings:themes')
			item(title='start' image=inherit cmd='ms-settings:personalization-start')
			item(title='taskbar' image=inherit cmd='ms-settings:taskbar')
		}
		menu(title='network' image=inherit)
		{
			item(title='status' image=inherit cmd='ms-settings:network-status')
			item(title='ethernet' image=inherit cmd='ms-settings:network-ethernet')
			item(title='connections' image=inherit cmd='shell:::{7007ACC7-3202-11D1-AAD2-00805FC1270E}')
		}
	}
}

// TERMINAL
menu(type='~taskbar' title=title.terminal image=[\uE26E, #ff66e3])
{
    $tip_run_admin=["Press SHIFT key to run as administrator", tip.warning, 1.0]
    $has_admin=key.shift() or key.rbutton()
    item(title=title.command_prompt tip=tip_run_admin admin=has_admin image cmd='cmd.exe'args='/K TITLE ^< @sel.path.name ^/ ^> &ver& PUSHD "@sel.dir"')
    item(title=title.windows_powershell admin=has_admin tip=tip_run_admin image cmd='powershell.exe'args='-noexit -command Set-Location -Path "@sel.dir\."')
    item(where=package.exists("WindowsTerminal") title=title.Windows_Terminal tip=tip_run_admin admin=has_admin image='@package.path("WindowsTerminal")\WindowsTerminal.exe'cmd='wt.exe'arg='-d "@sel.path\."')
}



// TASKBAR
// Taskbar: Shell
menu(type="taskbar" vis=key.shift() or key.lbutton() pos=0 title=app.name image=\uE249)
{
    item(title="config" image=\uE10A cmd='"@app.cfg"')
    item(title="manager" image=\uE0F3 admin cmd='"@app.exe"')
    item(title="directory" image=\uE0E8 cmd='"@app.dir"')
    item(title="version\t"+@app.ver vis=label col=1)
    item(title="docs" image=\uE1C4 cmd='https://nilesoft.org/docs')
    item(title="donate" image=\uE1A7 cmd='https://nilesoft.org/donate')
}
// Taskbar: Menus
// menu(type='taskbar' separator="both" image=icon.settings expanded=true)
menu(type='taskbar' image=icon.settings expanded=true)
{
    menu(title="Apps" image=\uE254)
    {
        item(title='Regedit' image cmd='regedit.exe')
        sep
        item(title='Paint' image=\uE116 cmd='mspaint')
        item(title='Calculator' image=\ue1e7 cmd='calc.exe')
        item(title='Chrome' image cmd='@sys.prog/Google/Chrome/Application/chrome.exe')
    }
    separator
    menu(title="Utils" type='taskbar' image=\uE28A) {
            item(title='Audio Device' image=[\uE28A, #f76e6e] pos="bottom" cmd='mmsys.cpl')
    }
    menu(title="Windows" image=\uE1FB) {
        item(title=title.cascade_windows image=icon.cascade_windows cmd=command.cascade_windows)
        item(title=title.Show_windows_stacked cmd=command.Show_windows_stacked)
        item(title=title.Show_windows_side_by_side cmd=command.Show_windows_side_by_side)
        sep
        item(title=title.minimize_all_windows cmd=command.minimize_all_windows)
        item(title=title.restore_all_windows cmd=command.restore_all_windows)
    }
    item(title="Show Desktop" pos=-1 image=[\uE16B, #22A7F2] cmd=command.toggle_desktop)
    sep
    item(title="Taskbar Settings" sep=top image=[\uE069, #f1a374] cmd='ms-settings:taskbar')
    // item(title="Task Manager" image=[\uE0A4, #f1a374] cmd='taskmgr.exe')
    item(title="Task Manager" image=[\uE0A4, #f76e6e] cmd='taskmgr.exe')
    sep
    item(vis=@key.shift() title=title.exit_explorer image=[\uE04E, #ff66e3] separator="both" pos="bottom" cmd=command.restart_explorer)
}

// ----------------------------------------------------------------------------
// IMPORT: Initialize images/icons
// ----------------------------------------------------------------------------
import 'imports/shell_themes/theme_blue.nss'
import 'imports/images.nss'


// import 'imports/shell_menus_external/shell-icons.nss'

// ----------------------------------------------------------------------------
// IMPORT: Custom
// ----------------------------------------------------------------------------
// import 'imports/shell_menus/menu_develop.nss'
// import 'imports/shell_menus/menu_taskbar.nss'
// import 'imports/shell_menus/menu_terminal.nss'
// import 'imports/shell_menus/menu_goto.nss'





// import 'imports/static.nss'
// import 'imports/unsorted/theme-manager.nss'
// import 'imports/custom/custom_menu_terminal.nss'
// import 'imports/terminal.nss'








// ----------------------------------------------------------------------------
// GLOBAL
// ----------------------------------------------------------------------------
// menu(type='*' where=(sel.count or wnd.is_taskbar or wnd.is_edit) title="Default" sep='top' image=[\uE26E, #ae00ff]  pos="top") {}
// {
//     $tip_run_admin=["Press SHIFT key to run as administrator", tip.warning, 1.0]
//     $has_admin=key.shift() or key.rbutton()

//     item(
//         title=title.command_prompt
//         tip=tip_run_admin
//         admin=has_admin



// menu(type='*' title="Default" separator="both" image=icon.settings expanded=true)

// modify(where=regex.match(this.name, ".*") menu="Default")


// menu(type='taskbar' title="Relocated" sep='top' image=[\uE26E, #ae00ff]  pos="top") {}
// menu(type='taskbar' title="Relocated" sep='top' image=[\uE1FE, #ae00ff]  pos="top") {}
// menu(type='taskbar' title="Relocated" sep='top' image=image.mdl(\uF259, 12, #ffffff)  pos="top") {}
// menu(type='taskbar' title="Relocated" sep='top' image=[\uE1B8, #000000]  pos="top") {}
// modify(type='taskbar' where=regex.match(this.name, "(.*)") menu="Relocated")

// // ----------------------------------------------------------------------------
// // GLOBAL
// // ----------------------------------------------------------------------------
// menu(type='*' where=(sel.count or wnd.is_taskbar or wnd.is_edit) title="Terminal" sep='top' image=[\uE26E, #ae00ff]  pos="top") {}
// menu(type='*' where=window.is_taskbar||sel.count mode=mode.multiple title="title.go_to" sep=sep.both image=\uE14A) {}



// // ----------------------------------------------------------------------------
// // TASKBAR
// // ----------------------------------------------------------------------------
// menu(type='*' where=(sel.count or wnd.is_taskbar or wnd.is_edit) title="Terminal" sep='top' image=[\uE26E, #ae00ff]  pos="top")
// {
//     $tip_run_admin=["Press SHIFT key to run as administrator", tip.warning, 1.0]
//     $has_admin=key.shift() or key.rbutton()

//     item(
//         title=title.command_prompt
//         tip=tip_run_admin
//         admin=has_admin
//         image
//         cmd='cmd.exe'
//         args='/K TITLE ^< @sel.path.name ^/ ^> &ver& PUSHD "@sel.dir"'
//     )

//     item(
//         title=title.windows_powershell
//         admin=has_admin
//         tip=tip_run_admin
//         image
//         cmd='powershell.exe'
//         args='-noexit -command Set-Location -Path "@sel.dir\."'
//     )

//     item(
//         where=package.exists("WindowsTerminal")
//         title=title.Windows_Terminal
//         tip=tip_run_admin
//         admin=has_admin
//         image='@package.path("WindowsTerminal")\WindowsTerminal.exe'
//         cmd='wt.exe'
//         arg='-d "@sel.path\."'
//     )
// }

// // Menu: Unimportant
// menu(type="taskbar" mode="multiple" type="*" separator="both" title="Unimportant" pos="middle" image=\uE00D) {}
// modify(where=regex.match(this.name, "(.*cas.*)") menu="Unimportant")
// // modify(where=regex.match(this.name, "(.*Open in Terminal.*)")  menu="Unimportant")




// menu(title = 'sub menu' image = #0000ff)
// {
// 	item(title = 'test sub-item')
// }


// // ----------------------------------------------------------------------------
// // GLOBAL: Init
// menu(separator="both" title="Init" pos="top" image=\uE00D) {}
// // item(title=title.cascade_windows image=icon.cascade_windows cmd=command.cascade_windows menu="Init")

// // // item(type="File" mode="multi_single" title=sel.path cmd=msg('@user.name \n @sel.path'))
// // item(type="file|directory|taskbar" mode="multi_single" title=sel.path cmd=msg('@user.name \n @sel.path'))
// // "file|directory|drive|usb|dvd|fixed|vhd|removable|remote|back|desktop|namespace|computer|recyclebin|taskbar"

// // Menu: Unimportant


// // ----------------------------------------------------------------------------
// // TASKBAR: Init
// // menu(type="taskbar" mode="multiple" type="*" separator="both" title="Init" pos="top" image=\uE00D) {}
// // ----------------------------------------------------------------------------
// // TASKBAR: Cleanup
// menu(separator="both" title="Init" pos="top" image=\uE00D) {}

// // modify(where=regex.match(this.name, "(.*Sublime.*)") image='imports\Sublime Material Red.ico')


// modify(where=regex.match(this.name, "(.*cas.*)") menu="Relocated")
// remove(where=regex.match(this.name, "^(.*send a.*)$"))
// // modify(where=regex.match(this.name, "(.*Open in Terminal.*)")  menu="Unimportant")
// // modify(where=regex.match(this.name, "(.*Run Sandboxed.*)")  menu="Unimportant")
// // modify(where=regex.match(this.name, "(.*Sync or Backup.*)")  menu="Unimportant")


// // TASKBAR: NilesoftShell
// menu(type="taskbar" vis=key.shift() or key.lbutton() pos=0 title=app.name image=\uE249)
// {
//     item(title="config" image=\uE10A cmd='"@app.cfg"')
//     item(title="manager" image=\uE0F3 admin cmd='"@app.exe"')
//     item(title="directory" image=\uE0E8 cmd='"@app.dir"')
//     item(title="version\t"+@app.ver vis=label col=1)
//     item(title="docs" image=\uE1C4 cmd='https://nilesoft.org/docs')
//     item(title="donate" image=\uE1A7 cmd='https://nilesoft.org/donate')
// }
// // TASKBAR: Custom (M_ai_ne)
// menu(type='taskbar' separator="both" image=icon.settings expanded=true)
// {
// 	// menu(mode="multiple" type="*" separator="both" title="Init" pos="top" image=\uE00D) {}
//     // item(title=title.cascade_windows image=icon.cascade_windows cmd=command.cascade_windows menu="Init")
// 	//
//     menu(title="Windows" type='taskbar' image=\uE1FB) {}


//     item(title=title.cascade_windows image=icon.cascade_windows cmd=command.cascade_windows)

//     menu(title="Apps" image=\uE254)
//     {
//         item(title='Paint' image=\uE116 cmd='mspaint')
//         item(title='Edge' image cmd='@sys.prog32\Microsoft\Edge\Application\msedge.exe')
//         item(title='Calculator' image=\ue1e7 cmd='calc.exe')
//         item(title=str.res('regedit.exe,-16') image cmd='regedit.exe')
//     }

//     menu(title="Windows" image=\uE1FB) {
//         sep
//         item(title=title.cascade_windows image=icon.cascade_windows cmd=command.cascade_windows)
//         item(title=title.Show_windows_stacked cmd=command.Show_windows_stacked)
//         item(title=title.Show_windows_side_by_side cmd=command.Show_windows_side_by_side)
//         sep
//         item(title=title.minimize_all_windows cmd=command.minimize_all_windows)
//         item(title=title.restore_all_windows cmd=command.restore_all_windows)
//     }
//     menu(title="Utils" type='taskbar' image=\uE28A) {
// 		// menu(where=@(this.count == 0) type='taskbar' separator="both" image=icon.settings expanded=true)
// 			item(title='Audio Device' image=[\uE28A, #f76e6e] pos="bottom" cmd='mmsys.cpl')
//         // item(title=title.cascade_windows cmd=command.cascade_windows)
//         // item(title=title.Show_windows_stacked cmd=command.Show_windows_stacked)
//         // item(title=title.Show_windows_side_by_side cmd=command.Show_windows_side_by_side)
//         // sep
//         // item(title=title.minimize_all_windows cmd=command.minimize_all_windows)
//         // item(title=title.restore_all_windows cmd=command.restore_all_windows)
//     }
//     item(title="Show Desktop" pos=-1 image=[\uE16B, #22A7F2] cmd=command.toggle_desktop)
//     sep
//     item(title="Taskbar Settings" sep=top image=[\uE069, #22A7F2] cmd='ms-settings:taskbar')
//     sep
//     item(title="Task Manager" image=[\uE0A4, #22A7F2] cmd='taskmgr.exe')
//     sep
//     sep
//     item(vis=@key.shift() title=title.exit_explorer cmd=command.restart_explorer)
// }


// // modify(where=regex.match(this.name, "(.*Sublime.*)") image='imports\Sublime Material Red.ico')
// modify(where=regex.match(this.name, "(.*Sublime.*)") image=image.svgf('imports\sublime.svg'))

