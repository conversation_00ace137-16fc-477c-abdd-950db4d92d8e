
// :: update icons


// ---
modify(type='*' image=[E1D2,LOWKEY] image-sel=[E1D2,WHITE] where=this.id==id.new)
modify(type='*' image=[E094,LOWKEY] image-sel=[E094,WHITE] where=this.id==id.refresh)
// ---
modify(type='*' image=[E0A2,LOWKEY] image-sel=[E0A2,WHITE] where=this.id==id.sort_by)
modify(type='*' image=[E09A,LOWKEY] image-sel=[E09A,WHITE] where=this.id==id.group_by)
modify(type='*' image=[E253,LOWKEY] image-sel=[E253,WHITE] where=this.id==id.view)
modify(type='*' image=[E099,LOWKEY] image-sel=[E099,WHITE] where=this.id==id.send_to)
// ---
modify(type='*' image=[E0FE,LOWKEY] image-sel=[E0FE,WHITE] where=this.id==id.run_as_administrator)
modify(type='*' image=[E201,LOWKEY] image-sel=[E201,WHITE] where=this.id==id.open_with)
modify(type='*' image=[E173,LOWKEY] image-sel=[E173,WHITE] where=this.id==id.open)
modify(type='*' image=[E16A,LOWKEY] image-sel=[E16A,WHITE] where=this.id==id.open_in_new_window)
modify(type='*' image=[E160,LOWKEY] image-sel=[E160,WHITE] where=this.id==id.open_in_new_tab)
modify(type='*' image=[E0E8,LOWKEY] image-sel=[E0E8,WHITE] where=this.id==id.open_file_location)
// ---
modify(type='*' image=[E1D2,LOWKEY] image-sel=[E1D2,WHITE] where=this.id==id.create_shortcuts_here)
modify(type='*' image=[E0B2,LOWKEY] image-sel=[E0B2,WHITE] where=this.id==id.copy_here)
modify(type='*' image=[E0CA,LOWKEY] image-sel=[E0CA,WHITE] where=this.id==id.move_here)
// ---
modify(type='*' image=[E0B8,LOWKEY] image-sel=[E0B8,WHITE] where=this.id==id.cut)
modify(type='*' image=[E0B2,LOWKEY] image-sel=[E0B2,WHITE] where=this.id==id.copy)
modify(type='*' image=[E1A6,LOWKEY] image-sel=[E1A6,WHITE] where=this.id==id.create_shortcut)
modify(type='*' image=[E0AF,LOWKEY] image-sel=[E0AF,WHITE] where=this.id==id.paste)
// ---
modify(type='*' image=[E11B,LOWKEY] image-sel=[E11B,WHITE] where=this.id==id.add_to_favorites)
modify(type='*' image=[E17A,LOWKEY] image-sel=[E17A,WHITE] where=this.id==id.edit)
modify(type='*' image=[E0B5,LOWKEY] image-sel=[E0B5,WHITE] where=this.id==id.rename)
modify(type='*' image=[E1D1,LOWKEY] image-sel=[E1D1,WHITE] where=this.id==id.cancel)
// ---
modify(type='*' image=[E0C9,LOWKEY] image-sel=[E0C9,WHITE] where=this.id==id.pin_to_quick_access)
modify(type='*' image=[E0C9,LOWKEY] image-sel=[E0C9,WHITE] where=this.id==id.pin_to_start)
modify(type='*' image=[E0C9,LOWKEY] image-sel=[E0C9,WHITE] where=this.id==id.pin_to_taskbar)
// ---
modify(type='*' image=[E0A0,LOWKEY] image-sel=[E0A0,WHITE] where=this.id==id.customize_this_folder)
modify(type='*' image=[E1DE,LOWKEY] image-sel=[E1DE,WHITE] where=this.id==id.delete)
modify(type='*' image=[E15D,LOWKEY] image-sel=[E15D,WHITE] where=this.id==id.properties)
// ---
modify(type='*' image=[E1D1,RED]    image-sel=[E1D1,WHITE] where=this.id==id.close)
modify(type='*' image=[E1B1,SOFT]   image-sel=[E1B1,WHITE] where=this.id==id.maximize)
modify(type='*' image=[E1D3,SOFT]   image-sel=[E1D3,WHITE] where=this.id==id.minimize)
modify(type='*' image=[E087,SOFT]   image-sel=[E087,WHITE] where=this.id==id.move)
modify(type='*' image=[E1B0,SOFT]   image-sel=[E1B0,WHITE] where=this.id==id.size)

// ---
modify(type='*' image=[E0F8,LOWKEY] image-sel=[E0F8,WHITE] where=regex.match(this.name,".*Toolbars.*"))
modify(type='*' image=[E1B6,LOWKEY] image-sel=[E1B6,WHITE] where=regex.match(this.name,".*Cascade windows.*"))
modify(type='*' image=[E1B6,LOWKEY] image-sel=[E1B6,WHITE] where=regex.match(this.name,".*Show windows stacked.*"))
modify(type='*' image=[E1B6,LOWKEY] image-sel=[E1B6,WHITE] where=regex.match(this.name,".*Show windows side by side.*"))
modify(type='*' image=[E061,LOWKEY] image-sel=[E061,WHITE] where=regex.match(this.name,".*Copy Content to Clipboard.*"))

// ---
modify(type='*' image=exe_everything   where=(str.contains(this.name,"Everything")))
modify(type='*' image=exe_notepad_plus where=(str.contains(this.name,"Notepad\\+\\+")))
modify(type='*' image=exe_sublime      where=(str.contains(this.name,"Sublime")))
modify(type='*' image=exe_winmerge     where=(str.contains(this.name,"WinMerge")))
modify(type='*' image=exe_winmerge     where=(str.equals(this.name,[
                "Compare",
                "Compare As",
                "Select Left",
                "Select Right",
                "Select Middle",
                "Re-select Left"])))