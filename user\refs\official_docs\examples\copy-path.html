﻿<h5>Copy Path example</h5>
<pre><code class="lang-shell">// type can set with '~taskbar' equals all file types except taskbar.
menu(type='file|dir|back|root|namespace' mode="multiple"  title='copy to clipboard' image=#ff00ff)
{
	// Appears only when multiple selections.
	item(vis=@(sel.count > 1) title='Copy path (@sel.count) items selected'
			cmd=command.copy(sel(false, "\n")))

	item(mode="single" title=sel.path 
			cmd=command.copy(sel.path))

	item(mode="single" type='file|dir|back.dir'
			vis=sel.short.len!=sel.path.len title=sel.short
			cmd=command.copy(sel.short))
	separator
	item(mode="single" vis=@(sel.parent.len>3) title=sel.parent 
			cmd=command.copy(sel.parent))
	separator
	item(mode="single" type='file|dir|back.dir' title=sel.file.name
			cmd=command.copy(sel.file.name))
		   
	item(mode="single" type='file' title=sel.file.title
			cmd=command.copy(sel.file.title))

	item(mode="single" type='file' title=sel.file.ext
		   cmd=command.copy(sel.file.ext))
}
</code></pre><div class="has-text-centered my-5 preview">
	<img class="is-256" src="/docs/images/copypath1.png">
	<img class="is-256" src="/docs/images/copypath2.png">
	<img class="is-256" src="/docs/images/copypath3.png">
</div>
