
//
$PY_GITFILTERREPO_DIR = '@user.desktop\my\flow\home\__GOTO__\Scripts\Python\Py_Cli_Utils\py__GitFilterRepo'
$PY_GITFILTERREPO_EXE = '@PY_GITFILTERREPO_DIR\venv\Scripts\python.exe'
$PY_GITFILTERREPO_APP = '@PY_GITFILTERREPO_DIR\main.py'
//

// Context: Explorer
$PY_GITFILTERREPO_EXPLORER = '-i "@sel.dir" --prompt'
item(
    title="&GitFilterRepo"
    keys="py"
    type='File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    //
    image=[E17C,GREY] image-sel=[E17C,PURPLE]
    tip=['"@PY_GITFILTERREPO_APP" @PY_GITFILTERREPO_EXPLORER',TIP3,0.75]
    //
    admin=keys.rbutton()
    cmd=if(KEYS_EXE_OPEN_EXE,('"@PY_GITFILTERREPO_EXE"'))
    args='"@PY_GITFILTERREPO_APP" @PY_GITFILTERREPO_EXPLORER'
    //
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@PY_GITFILTERREPO_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@PY_GITFILTERREPO_APP')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@PY_GITFILTERREPO_DIR')),
    }
)
// Context: Taskbar
$PY_GITFILTERREPO_TASKBAR = '-i "" --prompt'
item(
    title="&GitFilterRepo"
    keys="py"
    type='Taskbar'
    //
    image=[E17C,GREY]
    image-sel=[E17C,PURPLE]
    //
    admin=keys.rbutton()
    cmd=if(KEYS_EXE_OPEN_EXE,('"@PY_GITFILTERREPO_EXE"'))
    args='"@PY_GITFILTERREPO_APP" @PY_GITFILTERREPO_TASKBAR'
    tip=['"@PY_GITFILTERREPO_APP" @PY_GITFILTERREPO_TASKBAR',TIP3,0.75]
    //
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@PY_GITFILTERREPO_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@PY_GITFILTERREPO_APP')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@PY_GITFILTERREPO_DIR')),
    }
)
