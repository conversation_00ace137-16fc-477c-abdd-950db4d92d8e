
//
$PY_YOUTUBEPLAYLISTEXPORTER_DIR = '@user.desktop\my\flow\home\__GOTO__\Scripts\Python\Py_Cli_Utils\py__YoutubePlaylistExporter'
$PY_YOUTUBEPLAYLISTEXPORTER_EXE = '@PY_YOUTUBEPLAYLISTEXPORTER_DIR\venv\Scripts\python.exe'
$PY_YOUTUBEPLAYLISTEXPORTER_APP = '@PY_YOUTUBEPLAYLISTEXPORTER_DIR\main.py'
//

// Context: Explorer
// $PY_YOUTUBEPLAYLISTEXPORTER_EXPLORER = '-op "@sel.dir" --prompt'
$PY_YOUTUBEPLAYLISTEXPORTER_EXPLORER = '-op --prompt'
item(
    title="&YoutubePlaylistExporter"
    keys="py"
    type='File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    //
    image=[E17C,GREY] image-sel=[E17C,PURPLE]
    tip=['"@PY_YOUTUBEPLAYLISTEXPORTER_APP" @PY_YOUTUBEPLAYLISTEXPORTER_EXPLORER',TIP3,0.75]
    //
    admin=keys.rbutton()
    cmd=if(KEYS_EXE_OPEN_EXE,('"@PY_YOUTUBEPLAYLISTEXPORTER_EXE"'))
    args='"@PY_YOUTUBEPLAYLISTEXPORTER_APP" @PY_YOUTUBEPLAYLISTEXPORTER_EXPLORER'
    //
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@PY_YOUTUBEPLAYLISTEXPORTER_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@PY_YOUTUBEPLAYLISTEXPORTER_APP')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@PY_YOUTUBEPLAYLISTEXPORTER_DIR')),
    }
)
// Context: Taskbar
$PY_YOUTUBEPLAYLISTEXPORTER_TASKBAR = '-op "@user.desktop" --prompt'
item(
    title="&YoutubePlaylistExporter"
    keys="py"
    type='Taskbar'
    //
    image=[E17C,GREY]
    image-sel=[E17C,PURPLE]
    //
    admin=keys.rbutton()
    cmd=if(KEYS_EXE_OPEN_EXE,('"@PY_YOUTUBEPLAYLISTEXPORTER_EXE"'))
    args='"@PY_YOUTUBEPLAYLISTEXPORTER_APP" @PY_YOUTUBEPLAYLISTEXPORTER_TASKBAR'
    tip=['"@PY_YOUTUBEPLAYLISTEXPORTER_APP" @PY_YOUTUBEPLAYLISTEXPORTER_TASKBAR',TIP3,0.75]
    //
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@PY_YOUTUBEPLAYLISTEXPORTER_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@PY_YOUTUBEPLAYLISTEXPORTER_APP')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@PY_YOUTUBEPLAYLISTEXPORTER_DIR')),
    }
)
