Name                               AppID
----                               -----
...                ...
Windows PowerShell ISE (x86)    {D65231B0-B2F1-4857-A4CE-A8E7C6EA7D27}\WindowsPowerShell\v1.0\PowerShell_ISE.exe
Editor del Registro             {F38BF404-1D43-42F2-9305-67DE0B28FC23}\regedit.exe
Configuración                   windows.immersivecontrolpanel_cw5n1h2txyewy!microsoft.windows.immersivecontrolpanel
NVIDIA Control Panel            NVIDIACorp.NVIDIAControlPanel_56jybvy8sckqj!NVIDIACorp.NVIDIAControlPanel
Seguridad de Windows            Microsoft.SecHealthUI_8wekyb3d8bbwe!SecHealthUI
Películas y TV                  Microsoft.ZuneVideo_8wekyb3d8bbwe!Microsoft.ZuneVideo
...                ...

NVIDIA CP > cmd='shell:AppsFolder\NVIDIACorp.NVIDIAControlPanel_56jybvy8sckqj!NVIDIACorp.NVIDIAControlPanel'
WhatsApp  > cmd='shell:AppsFolder\5319275A.WhatsAppDesktop_cv1g1gvanyjgm!App'







JS
$set_path='@sel.path'
$get_items=path.files(set_path, "*.lnk")
menu(type='dir' where=len(get_items)>0 mode='single' title='Applications' image=\ue097 pos=1)
{
    item(title='Applications in Folder:' vis=static image=inherit separator=after cmd={$count=0
            for(i= 0, i < len(get_items))
            {item(title=path.title(get_items<a style="text-decoration:none;color:rgb(0, 168, 252);cursor:pointer !important" href="" target="_blank" alt="link-to" rel="noreferrer" title="" data-reactroot="">i</a> where=len(get_items)>i cmd=path.lnk(set_path +'\'+ get_items<a style="text-decoration:none;color:rgb(0, 168, 252);cursor:pointer !important" href="" target="_blank" alt="link-to" rel="noreferrer" title="" data-reactroot="">i</a> image=path.lnk(set_path +'\'+ get_items<a style="text-decoration:none;color:rgb(0, 168, 252);cursor:pointer !important" href="" target="_blank" alt="link-to" rel="noreferrer" title="" data-reactroot="">i</a>)
                        }
                            })
                    }

$set_path='@sel.path'
$get_items=path.files(set_path, "*.lnk")
menu(type='dir' where=len(get_items)>0 mode='single' title='Applications' image=\ue097 pos=1)
{
    item(title='Applications in Folder:' vis=static image=inherit separator=after cmd={$count=0
    for(i= 0, i < len(get_items))
        {item(title=path.title(get_items[i]) where=len(get_items)>i cmd=path.lnk(set_path +'\'+ get_items[i]) image=path.lnk(set_path +'\'+ get_items[i]))
        }
    })
}



menu(type='dir' mode='single' title='Apps' image=\ue0a0)
{
        $set_path='@user.desktop\Games'
    $get_items=path.files(set_path, "*.lnk")
    item(title='Application Shortcuts:' vis=static image=inherit separator=after)
    item(title=path.title(get_items[0]) where=len(get_items)>0 cmd=path.lnk(set_path +'\'+ get_items[0]))
    item(title=path.title(get_items[1]) where=len(get_items)>1 cmd=path.lnk(set_path +'\'+ get_items[1]))
    item(title=path.title(get_items[2]) where=len(get_items)>2 cmd=path.lnk(set_path +'\'+ get_items[2]))
    item(title=path.title(get_items[3]) where=len(get_items)>3 cmd=path.lnk(set_path +'\'+ get_items[3]))
    item(title=path.title(get_items[4]) where=len(get_items)>4 cmd=path.lnk(set_path +'\'+ get_items[4]))
    item(title=path.title(get_items[5]) where=len(get_items)>5 cmd=path.lnk(set_path +'\'+ get_items[5]))
    item(title=path.title(get_items[6]) where=len(get_items)>6 cmd=path.lnk(set_path +'\'+ get_items[6]))
    item(title=path.title(get_items[7]) where=len(get_items)>7 cmd=path.lnk(set_path +'\'+ get_items[7]))
    item(title=path.title(get_items[8]) where=len(get_items)>8 cmd=path.lnk(set_path +'\'+ get_items[8]))
    item(title=path.title(get_items[9]) where=len(get_items)>9 cmd=path.lnk(set_path +'\'+ get_items[9]))
    item(title=' ' where=len(get_items)>9 vis=static separator=after col)
}
menu(type='back' title='Apps' image=\ue0a0)
{
    $set_path=user.sendto // or: '@user.desktop\Apps'
    $get_items=path.files(set_path, "*.lnk")
    item(title='Application Shortcuts:' vis=label sep='after')
    item(title=path.title(get_items[0])  where=len(get_items)>0 image=path.lnk(set_path +'\'+get_items[0]) cmd=path.lnk(set_path +'\'+get_items[0]))
    item(title=path.title(get_items[1])  where=len(get_items)>1 image cmd=path.lnk(set_path +'\'+get_items[1]))
    item(title=path.title(get_items[2])  where=len(get_items)>2 )
    item(title=path.title(get_items[3])  where=len(get_items)>3 )
    item(title=path.title(get_items[4])  where=len(get_items)>4 )
    item(title=''                        where=len(get_items)>5 col )
    item(title=path.title(get_items[5])  where=len(get_items)>5 )
    item(title=path.title(get_items[6])  where=len(get_items)>6 )
    item(title=path.title(get_items[7])  where=len(get_items)>7 )
    item(title=path.title(get_items[8])  where=len(get_items)>8 )
    item(title=path.title(get_items[9])  where=len(get_items)>9 )
}

// Settings
settings
{
    priority=1
    exclude.where = !process.is_explorer
    showdelay = 200
    modify.remove.duplicate=1
    tip.enabled=true
}

menu(separator="both" title="Init" pos="top" image=\uE00D) {}
menu(title = 'sub menu' image = #0000ff) { item(title = 'test sub-item') }

    item(title='Subscene' image=[\uE270, #22A7F2] pos="middle" cmd='chrome' args='"https://subscene.com/subtitles/searchbytitle"')
    item(title='qBittorrent' image=[\uE0CA, #22A7F2] pos="middle" cmd='cmd.exe' args='START /MAX CMD /C "C:\Program Files\qBittorrent\qbittorrent.exe"')
    separator
// TASKBAR: Cleanup

modify(where=regex.match(this.name, "(.*cas.*)") menu="Relocated")
remove(where=regex.match(this.name, "^(.*send a.*)$"))

// TASKBAR: (M_ai_ne)
menu(type='taskbar' separator="both" image=icon.settings expanded=true)
{
    menu(title="Windows" type='taskbar' image=\uE1FB) {}
    menu(title="Apps" image=\uE254)
    {
        item(title='Paint' image=\uE116 cmd='mspaint')
        item(title='Edge' image cmd='@sys.prog32\Microsoft\Edge\Application\msedge.exe')
        item(title='Calculator' image=\ue1e7 cmd='calc.exe')
        item(title=str.res('regedit.exe,-16') image cmd='regedit.exe')
    }
    menu(title="Windows" image=\uE1FB) {
        sep
        item(title=title.cascade_windows image=icon.cascade_windows cmd=command.cascade_windows)
        item(title=title.Show_windows_stacked cmd=command.Show_windows_stacked)
        item(title=title.Show_windows_side_by_side cmd=command.Show_windows_side_by_side)
        sep
        item(title=title.minimize_all_windows cmd=command.minimize_all_windows)
        item(title=title.restore_all_windows cmd=command.restore_all_windows)
    }
    menu(title="Utils" type='taskbar' image=\uE28A) {
            item(title='Audio Device' image=[\uE28A, #f76e6e] pos="bottom" cmd='mmsys.cpl')
    }
    item(title="Show Desktop" pos=-1 image=[\uE16B, #22A7F2] cmd=command.toggle_desktop)
    sep
    item(title="Taskbar Settings" sep=top image=[\uE069, #22A7F2] cmd='ms-settings:taskbar')
    sep
    item(title="Task Manager" image=[\uE0A4, #22A7F2] cmd='taskmgr.exe')
    sep
    sep
    item(vis=@key.shift() title=title.exit_explorer cmd=command.restart_explorer)
}


// ----------------------------------------------------------------------------
// CREATE MENUS / ITEMS
// ----------------------------------------------------------------------------
$sublime_logo = '<svg width="800px" height="800px" viewBox="-38 0 332 332" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" preserveAspectRatio="xMidYMid"> <defs> <linearGradient x1="55.1170996%" y1="58.6795405%" x2="63.6801778%" y2="39.5971572%" id="linearGradient-1"> <stop stop-color="#FF9700" offset="0%"> </stop> <stop stop-color="#F48E00" offset="53%"> </stop> <stop stop-color="#D06F00" offset="100%"> </stop> </linearGradient> </defs> <g> <path d="M255.288325,166.794648 C255.288325,162.908052 252.415934,160.666877 248.891046,161.780372 L6.39727934,238.675387 C2.86530029,239.795974 0,243.859878 0,247.73938 L0,326.329461 C0,330.216057 2.86530029,332.464324 6.39727934,331.343737 L248.891046,254.455814 C252.415934,253.335227 255.288325,249.271323 255.288325,245.384729 L255.288325,166.794648 L255.288325,166.794648 Z" fill="url(#linearGradient-1)"> </path> <path d="M5.68434189e-14,164.291056 C5.68434189e-14,168.177652 2.86530029,172.241555 6.39727934,173.362144 L248.926508,250.26425 C252.458487,251.384837 255.323787,249.13657 255.323787,245.257067 L255.323787,166.659893 C255.323787,162.780391 252.458487,158.716487 248.926508,157.595899 L6.39727934,80.693793 C2.86530029,79.5732052 5.68434189e-14,81.8143808 5.68434189e-14,85.7009761 L5.68434189e-14,164.291056 Z" fill="#FF9800"> </path> <path d="M255.288325,5.30235244 C255.288325,1.41575701 252.415934,-0.83251079 248.891046,0.288076943 L6.39727934,77.1759986 C2.86530029,78.2965864 0,82.36049 0,86.2470854 L0,164.837165 C0,168.723761 2.86530029,170.964936 6.39727934,169.851441 L248.891046,92.9564272 C252.415934,91.8358394 255.288325,87.7719358 255.288325,83.8924327 L255.288325,5.30235244 Z" fill="#FF9800"> </path> </g> </svg>'
menu(title='Archive...' type='file|dir|drive|back' image=sublime_logo expanded=1) {

Create menus / items
menu(type='back' mode="none" title="-> Custom Menus" pos="auto" image=icon.new_folder) {
    item(title='New Folder (Current Date)' cmd=io.dir.create(sys.datetime("y.m.d")))
    item(title='New Folder (Current Date)' cmd=io.dir.create(sys.datetime("y.m.d")))
}

menu(title='New File' image=icon.new_file menu=id.new.name pos=1 sep=sep.after)
{
    $dt = sys.datetime("ymdHMSs")
    item(title='TXT' cmd=io.file.create('@(dt).txt', 'Hello World!'))
    item(title='JSON' cmd=io.file.create('@(dt).json', '{}'))
    item(title='HTML' cmd=io.file.create('@(dt).html', "<html>\n\t<head>\n\t</head>\n\t<body>Hello World!\n\t</body>\n</html>"))
}

works
menu(type='file' mode="none" title="Test -> Type: file" image=icon.pin) {
    item(title='Test -> Type: file' cmd=msg('Hello @user.name'))
}
menu(type='dir' mode="none" title="Test -> Type: dir" image=icon.pin) {
    item(title='Test -> Type: dir' cmd=msg('Hello @user.name'))
}
menu(type='~taskbar' mode="none" sep="both" title="Test -> Type: ~taskbar" image=icon.pin) {
    item(title='Test -> Type: ~taskbar' cmd=msg('Hello @user.name'))
}
item(mode="single" type='file' where=sel.file.len != sel.file.title.len title='Copy "'+@sel.file.title+'"' image=[\uE055, #4d8e4a] cmd=command.copy(sel.file.title))
item(mode="single" title='Copy "'+@sel.path+'"' tip=sel.path image=[\uE09B, #4d8e4a] cmd=command.copy(sel.path))
item(mode="single" where=@sel.parent.len>3 title='Copy "'+sel.parent+'"' image=[\uE0E7, #4d8e4a] cmd=@command.copy(sel.parent))
separator

// remove all items that doesn't match with regex
remove(where=!regex.match(
        this.name, "^(refresh|open|edit|new folder|properties|.*with.*)$"
    )
)

modify(type='*' where=window.name=="static" menu='-> DEBUG')


// Windows Explorer - Rarely used modifys
modify(where=this.id(id.copy_as_path) sep=sep.top menu='Windows Explorer')
modify(where=this.id(id.print) sep=sep.top menu='Windows Explorer')
modify(where=this.id(id.rotate_left) sep=sep.top menu='Windows Explorer')
modify(where=this.id(id.rotate_right) menu='Windows Explorer')
modify(where=this.id(id.troubleshoot_compatibility) sep=sep.top menu='Windows Explorer')
modify(where=this.id(id.extract_all) sep=sep.top menu='Windows Explorer')
modify(where=this.id(id.open_in_new_tab) sep=sep.top menu='Windows Explorer')
modify(where=this.id(id.open_in_new_process) menu='Windows Explorer')
modify(where=this.id(id.open_in_new_window) menu='Windows Explorer')
modify(where=this.id(id.pin_to_start) sep=sep.top menu='Windows Explorer')
modify(where=this.id(id.unpin_from_start) sep=sep.top menu='Windows Explorer')
modify(where=this.id(id.pin_to_quick_access) menu='Windows Explorer')
modify(where=this.id(id.unpin_from_quick_access) menu='Windows Explorer')
modify(where=this.id(id.pin_to_taskbar) menu='Windows Explorer')
modify(where=this.id(id.unpin_from_taskbar) menu='Windows Explorer')
modify(find='add to favorites' sep=sep.top menu='Windows Explorer')
modify(where=this.id(id.customize_this_folder) type='dir.back|drive.back' menu='Windows Explorer')
modify(where=this.id(id.give_access_to) sep=sep.top menu='Windows Explorer')
modify(where=this.id(id.include_in_library) menu='Windows Explorer')
modify(where=this.id(id.cast_to_device) menu='Windows Explorer')
modify(where=this.id(id.restore_previous_versions) menu='Windows Explorer')

item(title=title.command_prompt tip=tip_run_admin admin=key.shift() image cmd='cmd.exe' args='/K TITLE ^<Prompt^> ..\@sel.dir.name' menu="Apps")
item(title=title.command_prompt tip=tip_run_admin admin=key.shift() image cmd='cmd.exe' cmd-line='/K TITLE ^<Prompt^> ..\@sel.dir.name' window='hidden' menu="Apps")

// Menu: Windows
menu(mode="multiple" type="*" separator="both" title="Windows Commands" pos="middle" image=\uE00D) {}
modify(where=this.id(id.show_touch_keyboard_button)  menu="Windows Commands")

menu(mode="multiple" title=title.more_options image=icon.more_options) { }

modify(where=regex.match(this.name, ".*Cascade.*")  menu="Windows")
modify(where=regex.match(this.name, ".*Open in Terminal.*")  menu="Apps")
modify(where=regex.match(this.name, ".*Open in Terminal.*")  menu="Apps")
modify(where=regex.match(this.name, ".*Git Bash.*")  menu="Apps")
item(title=title.command_prompt tip=tip_run_admin admin=key.shift() image cmd='cmd.exe' args='/K TITLE ^<Prompt^> ..\@sel.dir.name' menu="Apps")
modify(where=regex.match(this.name, ".*PowerRename.*")  menu="Apps")
modify(where=regex.match(this.name, ".*Open with Visual Studio.*")  menu="Apps")
remove(where=regex.match(this.name, "^.*CMD.*$")  menu="Apps")

item(mode="single" title=sel.path cmd=msg('@user.name \n @sel.path'))
item(mode="none" title=sel.path cmd=msg('@user.name \n @sel.path'))
item(type="File|Directory" mode="multi_single" title=sel.path cmd=msg('@user.name \n @sel.path'))
item(type="File|Directory" mode="multi_single" Admin title=sel.path cmd=msg('@user.name \n @sel.path'))
item(type="File|Directory" mode="multi_single" Admin title=sel.path image cmd='cmd.exe' args=sel.path)
item(type="File|Directory" mode="multi_single" Admin title=sel.path image cmd='cmd.exe /C "@sel.path)"' args='START /MAX CMD' )
item(type="File|Directory" mode="multi_single" title='cmd.exe (Admin)' admin='True' image cmd='cmd.exe' args='/K TITLE Command Prompt &ver& PUSHD "@sel.dir"')
    cmd-line='/k title Mediainfo & "C:\Program Files (x86)\K-Lite Codec Pack\Tools\mediainfo.exe" --Inform="General;%Format%" @sel(true)')

                item(title='Command prompt' image cmd='cmd.exe')
cmd='cmd.exe' args='START /MAX CMD /C "C:\Program Files\qBittorrent\qbittorrent.exe"')

// Menu: Apps
menu(mode="multiple" type="*" separator="both" title="Apps" pos="middle" image=\uE00D) {}
// modify(where=regex.match(this.name, ".*.*")  menu="Apps")
modify(where=regex.match(this.name, ".*Open in Terminal.*")  menu="Apps")
modify(where=regex.match(this.name, ".*Git Bash.*")  menu="Apps")
item(title=title.command_prompt tip=tip_run_admin admin=key.shift() image cmd='cmd.exe' args='/K TITLE ^<Prompt^> ..\@sel.dir.name' menu="Apps")
modify(where=regex.match(this.name, ".*PowerRename.*")  menu="Apps")
modify(where=regex.match(this.name, ".*Open with Visual Studio.*")  menu="Apps")
remove(where=regex.match(this.name, "^.*CMD.*$")  menu="Apps")

// modify(where=regex.match(this.name, "(.*Sublime.*)") image='imports\Sublime Material Red.ico')
modify(where=regex.match(this.name, "(.*Sublime.*)") image=image.svgf('imports\sublime.svg'))

// Menu: Unimportant
menu(mode="multiple" type="*" separator="both" title="Unimportant" pos="middle" image=\uE00D) {}
modify(where=regex.match(this.name, "(.*Open in Terminal.*)")  menu="Unimportant")
modify(where=regex.match(this.name, "(.*Run Sandboxed.*)")  menu="Unimportant")
modify(where=regex.match(this.name, "(.*Sync or Backup.*)")  menu="Unimportant")
modify(where=regex.match(this.name, "(.*Add to VLC Media Player.*)")  menu="Unimportant")



menu(mode="multiple" type="*" separator="both" title="Applications" pos="middle") {}

modify(find="Run Sandboxed" menu="Applications")
modify(find="Everything" menu="Applications")
modify(find="Git Bash" menu="Applications")
modify(find="Git GUI" menu="Applications")
modify(find="Sync Or Backup This Folder" menu="Applications")
modify(find="Visual Studio" menu="Applications")

modify(where=regex.match(this.name, "(.*Dropbox.*)")  menu="Applications")
modify(where=regex.match(this.name, "(.*Send a copy.*)")  menu="Applications")

menu(mode="multiple" type="*" separator="both" title="Applications" pos="middle") {
 modify(where=regex.match(this.name, "(.*Send a copy.*)") menu="Applications")
}

// Create menus / items
menu(type='a copy' mode="none" title="-> Custom Menus" pos="auto" image=icon.new_folder) {
    item(title='New Folder (Current Date)' cmd=io.dir.create(sys.datetime("y.m.d")))
}

item(title='test regex.match' cmd=msg(regex.match(sel.path, '(.*)?\.exe$')))
$matches = regex.matches('this subject has a submarine as a subsequence','\b(sub)([^ ]*)')
item(title='test regex.matches' cmd=msg(str.join('found matches: @length(matches)', "", matches,"\n")))

menu(title='New Folder' image=icon.new_folder menu=id.new.name pos=0 sep=sep.before)
{
    item(title='New Folder (Current Date)' cmd=io.dir.create(sys.datetime("y.m.d")))
    item(title='DateTime' cmd=io.dir.create(sys.datetime("ymdHMSs")))
    item(title='Guid' cmd=io.dir.create(str.guid))
}
menu(title='New File' image=icon.new_file menu=id.new.name pos=1 sep=sep.after)
{
    $dt = sys.datetime("ymdHMSs")
    item(title='TXT' cmd=io.file.create('@(dt).txt', 'Hello World!'))
    item(title='JSON' cmd=io.file.create('@(dt).json', '{}'))
    item(title='HTML' cmd=io.file.create('@(dt).html', "<html>\n\t<head>\n\t</head>\n\t<body>Hello World!\n\t</body>\n</html>"))
}

menu(mode="multiple" title="Pin/Unpin" image=icon.pin)
{
}

menu(mode="multiple" title=title.more_options image=icon.more_options)
{
}

// ----------------------------------------------------------------------------
// IMPORT
// ----------------------------------------------------------------------------
// import 'imports/static.nss'
import 'imports/images.nss'
import 'imports/custom/custom_menu_terminal.nss'
// import 'imports/terminal.nss'
import 'imports/custom/custom_theme.nss'
import 'imports/unsorted/icon-shell_fr1_english.nss'
import 'imports/icon_viewer.nss'
import 'imports/taskbar.nss'
import 'imports/goto.nss'
import 'imports/custom/custom_menu_taskbar.nss'
import 'imports/custom/svg_icon_copilot.nss'
import 'imports/custom/custom_taskbar.nss'
import 'imports/custom/custom_mediacenter.nss'
import 'imports/modify.nss'
import 'imports/terminal.nss'
import 'imports/file-manage.nss'
import 'imports/develop.nss'
import 'imports/goto.nss'
import 'imports/taskbar.nss'