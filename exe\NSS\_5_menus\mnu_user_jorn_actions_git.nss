
//
menu(title="App :  &Git" type='Desktop|Dir|Drive|Back.Dir|Back.Drive|File' image=[E22C,WHITE] image-sel=[E22C,HOVER] sep='None')
{
    //
    // item(column vis='Static' image=[E00A,WHITE])
    import '@app.dir/NSS/_4_groups/grp_jorn_actions_git.nss'
    separator()

    //
    import '@app.dir/NSS/_3_items/itm_app_gitrestoremtime.nss'
    separator()

    //
    import '@app.dir/NSS/_3_items/itm_py_gitsizeanalyzer.nss'
    separator()

    //
    import '@app.dir/NSS/_3_items/itm_py_gitfilterrepo.nss'
    separator()
}




