
// $CMD_GIT_ADD_ALL = '/C (CD /D "@sel.dir") && (git add *)'
// $CMD_GIT_ADD_ALL_V = '/K (CD /D "@sel.dir") && (git add *) && PAUSE'
// $CMD_GIT_ADD_SELECTION = '/C (CD /D "@sel.dir") && (git add @BATCH_SEL_AS_STRING) && (@BATCH_EXIT_WITH_MSG)'
// $CMD_GIT_ADD_SELECTION_V = '/K (CD /D "@sel.dir") && (git add @BATCH_SEL_AS_STRING) && (@BATCH_EXIT_WITH_MSG) && PAUSE'

//
$PY_PROJECTGENERATOR_DIR = '@user.desktop\my\flow\home\__GOTO__\Scripts\Python\Py_Cli_Utils\py__ProjectGenerator'
$PY_PROJECTGENERATOR_EXE = '@PY_PROJECTGENERATOR_DIR\venv\Scripts\python.exe'
$PY_PROJECTGENERATOR_APP = '@PY_PROJECTGENERATOR_DIR\src\main.py'
//

// Context: Explorer
$PY_PROJECTGENERATOR_EXPLORER = '-pp "@sel.dir" --prompt'
item(
    title="&ProjectGenerator"
    keys="py"
    type='File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    //
    image=[E17C,GREY] image-sel=[E17C,PURPLE]
    tip=['"@PY_PROJECTGENERATOR_APP" @PY_PROJECTGENERATOR_EXPLORER',TIP3,0.75]
    //
    admin=keys.rbutton()
    cmd=if(KEYS_EXE_OPEN_EXE,('"@PY_PROJECTGENERATOR_EXE"'))
    args='"@PY_PROJECTGENERATOR_APP" @PY_PROJECTGENERATOR_EXPLORER'
    //
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@PY_PROJECTGENERATOR_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@PY_PROJECTGENERATOR_APP')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@PY_PROJECTGENERATOR_DIR')),
    }
)
// Context: Taskbar
$PY_PROJECTGENERATOR_TASKBAR = '-pp "@user.desktop" --prompt'
item(
    title="&ProjectGenerator"
    keys="py"
    type='Taskbar'
    //
    image=[E17C,GREY]
    image-sel=[E17C,PURPLE]
    //
    admin=keys.rbutton()
    cmd=if(KEYS_EXE_OPEN_EXE,('"@PY_PROJECTGENERATOR_EXE"'))
    args='"@PY_PROJECTGENERATOR_APP" @PY_PROJECTGENERATOR_TASKBAR'
    tip=['"@PY_PROJECTGENERATOR_APP" @PY_PROJECTGENERATOR_TASKBAR',TIP3,0.75]
    //
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@PY_PROJECTGENERATOR_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@PY_PROJECTGENERATOR_APP')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@PY_PROJECTGENERATOR_DIR')),
    }
)
