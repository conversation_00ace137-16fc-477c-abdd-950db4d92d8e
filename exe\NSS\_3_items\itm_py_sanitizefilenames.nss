
//
$PY_SANITIZEFILENAMES_DIR = '@user.desktop\my\flow\home\__GOTO__\Scripts\Python\Py_Cli_Utils\py__SanitizeFilenames'
$PY_SANITIZEFILENAMES_EXE = '@PY_SANITIZEFILENAMES_DIR\venv\Scripts\python.exe'
$PY_SANITIZEFILENAMES_APP = '@PY_SANITIZEFILENAMES_DIR\main.py'
//
$BATCH_SEL_AS_STRING = for(i=0, i< sel.count, '"@sel[i]" ')

// Context: Explorer
$PY_SANITIZEFILENAMES_EXPLORER = '-i @BATCH_SEL_AS_STRING --prompt'
item(
    title="&SanitizeFilenames"
    keys="py"
    type='File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    //
    image=[E17C,GREY] image-sel=[E17C,PURPLE]
    tip=['"@PY_SANITIZEFILENAMES_APP" @PY_SANITIZEFILENAMES_EXPLORER',TIP3,0.75]
    //
    admin=keys.rbutton()
    cmd=if(KEYS_EXE_OPEN_EXE,('"@PY_SANITIZEFILENAMES_EXE"'))
    args='"@PY_SANITIZEFILENAMES_APP" @PY_SANITIZEFILENAMES_EXPLORER'
    //
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@PY_SANITIZEFILENAMES_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@PY_SANITIZEFILENAMES_APP')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@PY_SANITIZEFILENAMES_DIR')),
    }
)
// Context: Taskbar
$PY_SANITIZEFILENAMES_TASKBAR = '--prompt'
item(
    title="&SanitizeFilenames"
    keys="py"
    type='Taskbar'
    //
    image=[E17C,GREY] image-sel=[E17C,PURPLE]
    tip=['"@PY_SANITIZEFILENAMES_APP" @PY_SANITIZEFILENAMES_TASKBAR',TIP3,0.75]
    //
    admin=keys.rbutton()
    args='"@PY_SANITIZEFILENAMES_APP" @PY_SANITIZEFILENAMES_TASKBAR'
    cmd=if(KEYS_EXE_OPEN_EXE,('"@PY_SANITIZEFILENAMES_EXE"'))
    //
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@PY_SANITIZEFILENAMES_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@PY_SANITIZEFILENAMES_APP')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@PY_SANITIZEFILENAMES_DIR')),
    }
)
