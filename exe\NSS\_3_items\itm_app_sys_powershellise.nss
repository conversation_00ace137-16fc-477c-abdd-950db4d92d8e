
//
$APP_SYS_POWERSHELLISE_DIR = '@sys.dir\System32\WindowsPowerShell\v1.0'
$APP_SYS_POWERSHELLISE_EXE = '@APP_SYS_POWERSHELLISE_DIR\powershell_ise.exe'
$APP_SYS_POWERSHELLISE_TIP = '@APP_SYS_POWERSHELLISE_EXE'

// // Context: Explorer
item(
    title  = ":  &PowerShell ISE"
    keys   = "exe"
    type   = 'File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    args   = '/C (CD /D "@sel.dir") & (start @APP_SYS_POWERSHELLISE_EXE)'
    //
    image  = APP_SYS_POWERSHELLISE_EXE
    tip    = [APP_SYS_POWERSHELLISE_TIP,TIP3,0.5]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('cmd.exe'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_SYS_POWERSHELLISE_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_SYS_POWERSHELLISE_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_SYS_POWERSHELLISE_DIR')),
    }
)
// Context: Taskbar
item(
    title  = ":  &PowerShell ISE"
    keys   = "exe"
    type   = 'Taskbar'
    args   = '/C (CD /D "@user.desktop") & (start @APP_SYS_POWERSHELLISE_EXE)'
    //
    image  = APP_SYS_POWERSHELLISE_EXE
    tip    = [APP_SYS_POWERSHELLISE_TIP,TIP3,0.5]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('cmd.exe'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_SYS_POWERSHELLISE_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_SYS_POWERSHELLISE_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_SYS_POWERSHELLISE_DIR')),
    }
)
