
// Strings
$TIME_NOW = '@sys.datetime("H.M")'

// Static: Paths and commands
$APP_COMFYUI_DIR = '@user.desktop\my\flow\home\__GOTO__\Apps\app_comfyui'
$APP_COMFYUI_CMD = '/K (CD /D "@APP_COMFYUI_DIR") & (TITLE ^[ComfyUI:@TIME_NOW^]) && (ECHO -^| ComfyUI: @TIME_NOW)'
$APP_COMFYUI_EXE = '@APP_COMFYUI_CMD && "@APP_COMFYUI_DIR\exe\python_embeded\python.exe"'


// APP_COMFYUI_GPU_TASKBAR
item(
    title=":  &ComfyUI: GPU"
    keys="py"
    type='Taskbar'
    //
    image=[E17C,GREY]
    image-sel=[E17C,PURPLE]
    //
    admin=keys.rbutton()
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_COMFYUI_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_COMFYUI_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_COMFYUI_DIR')),
        cmd-line=if(KEYS_EXE_OPEN_EXE,('@APP_COMFYUI_EXE "@APP_COMFYUI_DIR\exe\ComfyUI\main.py" --windows-standalone-build')),
    }
)
// APP_COMFYUI_CPU_TASKBAR
item(
    title=":  &ComfyUI: CPU"
    keys="py"
    type='Taskbar'
    //
    image=[E17C,GREY]
    image-sel=[E17C,PURPLE]
    //
    admin=keys.rbutton()
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_COMFYUI_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_COMFYUI_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_COMFYUI_DIR')),
        cmd-line=if(KEYS_EXE_OPEN_EXE,('@APP_COMFYUI_EXE "@APP_COMFYUI_DIR\exe\ComfyUI\main.py" --cpu --windows-standalone-build')),
    }
)
