/* 	
	Glifos SHELL. Glifos integrados en el propio programa SHELL. Código directo: image=\uE090. 
	https://nilesoft.org/gallery/glyphs
 	La variable $default_icon_size=XX, es válida únicamente a efectos del tamaño de visualización de los glifos en la opción "Glifos SHELL".
	Al seleccionar un glifo desde el panel de visualización se copia en el portapapeles el identificador del código > \uE090.
	Autor: drac / 08.09.2023	
*/

/*
	SHELL glyphs. Glyphs embedded in the SHELL program itself. Direct code: image=\uE090.
	https://nilesoft.org/gallery/glyphs
	The variable $default_icon_size=XX, is valid only for the glyph display size in the "SHELL glyphs" option.
	When selecting a glyph from the display panel, the code identifier > \uE090 is copied to the clipboard.
	Author: drac / September 08, 2023	
*/

$default_icon_size=20

menu(type='taskbar' title='Glifos SHELL' image=\uE249			    tip=['Embedded Glyphs Gallery'])
{
	menu(title='Complementarios' 	   	 image=\uE062				tip=['SHELL complementary glyphs'])
	{
	item(title=' '													tip=['SHELL complementary glyphs']) 		
	item(image=icon.glyph(\uE001,default_icon_size)					tip=['E001']								cmd=command.copy('\uE001')) 
	item(image=icon.glyph(\uE002,default_icon_size)					tip=['E002']								cmd=command.copy('\uE002')) 
	item(image=icon.glyph(\uE003,default_icon_size)					tip=['E003']								cmd=command.copy('\uE003')) 
	item(image=icon.glyph(\uE004,default_icon_size)					tip=['E004']								cmd=command.copy('\uE004')) 
	item(image=icon.glyph(\uE005,default_icon_size)					tip=['E005']								cmd=command.copy('\uE005')) 
	item(image=icon.glyph(\uE006,default_icon_size)					tip=['E006']								cmd=command.copy('\uE006')) 
	item(image=icon.glyph(\uE007,default_icon_size)					tip=['E007']								cmd=command.copy('\uE007')) 
	item(image=icon.glyph(\uE008,default_icon_size)					tip=['E008']								cmd=command.copy('\uE008')) 
	item(image=icon.glyph(\uE009,default_icon_size)					tip=['E009']								cmd=command.copy('\uE009')) 
	item(image=icon.glyph(\uE00A,default_icon_size)					tip=['E00A']								cmd=command.copy('\uE00A')) 
	item(image=icon.glyph(\uE00B,default_icon_size)					tip=['E00B']								cmd=command.copy('\uE00B')) 
	item(image=icon.glyph(\uE00C,default_icon_size)					tip=['E00C']								cmd=command.copy('\uE00C')) 
	item(image=icon.glyph(\uE00D,default_icon_size)					tip=['E00D']								cmd=command.copy('\uE00D')) 
	item(image=icon.glyph(\uE00E,default_icon_size)					tip=['E00E']								cmd=command.copy('\uE00E')) 
	item(image=icon.glyph(\uE00F,default_icon_size)					tip=['E00F']								cmd=command.copy('\uE00F'))
	
	item(image=icon.glyph(\uE010,default_icon_size)					tip=['E010']								cmd=command.copy('\uE010') col) 
	item(image=icon.glyph(\uE011,default_icon_size)					tip=['E011']								cmd=command.copy('\uE011')) 
	item(image=icon.glyph(\uE012,default_icon_size)					tip=['E012']								cmd=command.copy('\uE012')) 
	item(image=icon.glyph(\uE013,default_icon_size)					tip=['E013']								cmd=command.copy('\uE013')) 
	item(image=icon.glyph(\uE014,default_icon_size)					tip=['E014']								cmd=command.copy('\uE014')) 
	item(image=icon.glyph(\uE015,default_icon_size)					tip=['E015']								cmd=command.copy('\uE015')) 
	item(image=icon.glyph(\uE016,default_icon_size)					tip=['E016']								cmd=command.copy('\uE016')) 
	item(image=icon.glyph(\uE017,default_icon_size)					tip=['E017']								cmd=command.copy('\uE017')) 
	item(image=icon.glyph(\uE018,default_icon_size)					tip=['E018']								cmd=command.copy('\uE018')) 
	item(image=icon.glyph(\uE019,default_icon_size)					tip=['E019']								cmd=command.copy('\uE019')) 
	item(image=icon.glyph(\uE01A,default_icon_size)					tip=['E01A']								cmd=command.copy('\uE01A')) 
	item(image=icon.glyph(\uE01B,default_icon_size)					tip=['E01B']								cmd=command.copy('\uE01B')) 
	item(image=icon.glyph(\uE01C,default_icon_size)					tip=['E01C']								cmd=command.copy('\uE01C')) 
	item(image=icon.glyph(\uE01D,default_icon_size)					tip=['E01D']								cmd=command.copy('\uE01D')) 
	item(image=icon.glyph(\uE01E,default_icon_size)					tip=['E01E']								cmd=command.copy('\uE01E')) 
	item(image=icon.glyph(\uE01F,default_icon_size)					tip=['E01F']								cmd=command.copy('\uE01F'))
	
	item(image=icon.glyph(\uE020,default_icon_size)					tip=['E020']								cmd=command.copy('\uE020') col) 
	item(image=icon.glyph(\uE021,default_icon_size)					tip=['E021']								cmd=command.copy('\uE021')) 
	item(image=icon.glyph(\uE022,default_icon_size)					tip=['E022']								cmd=command.copy('\uE022')) 
	item(image=icon.glyph(\uE023,default_icon_size)					tip=['E023']								cmd=command.copy('\uE023')) 
	item(image=icon.glyph(\uE024,default_icon_size)					tip=['E024']								cmd=command.copy('\uE024')) 
	item(image=icon.glyph(\uE025,default_icon_size)					tip=['E025']								cmd=command.copy('\uE025')) 
	item(image=icon.glyph(\uE026,default_icon_size)					tip=['E026']								cmd=command.copy('\uE026')) 
	item(image=icon.glyph(\uE027,default_icon_size)					tip=['E027']								cmd=command.copy('\uE027')) 
	item(image=icon.glyph(\uE028,default_icon_size)					tip=['E028']								cmd=command.copy('\uE028')) 
	item(image=icon.glyph(\uE029,default_icon_size)					tip=['E029']								cmd=command.copy('\uE029')) 
	item(image=icon.glyph(\uE02A,default_icon_size)					tip=['E02A']								cmd=command.copy('\uE02A')) 
	item(image=icon.glyph(\uE02B,default_icon_size)					tip=['E02B']								cmd=command.copy('\uE02B')) 
	item(image=icon.glyph(\uE02C,default_icon_size)					tip=['E02C']								cmd=command.copy('\uE02C')) 
	item(image=icon.glyph(\uE02D,default_icon_size)					tip=['E02D']								cmd=command.copy('\uE02D')) 
	item(image=icon.glyph(\uE02E,default_icon_size)					tip=['E02E']								cmd=command.copy('\uE02E')) 
	item(image=icon.glyph(\uE02F,default_icon_size)					tip=['E02F']								cmd=command.copy('\uE02F'))
	
	item(image=icon.glyph(\uE030,default_icon_size)					tip=['E030']								cmd=command.copy('\uE030') col) 
	item(image=icon.glyph(\uE031,default_icon_size)					tip=['E031']								cmd=command.copy('\uE031')) 
	item(image=icon.glyph(\uE032,default_icon_size)					tip=['E032']								cmd=command.copy('\uE032')) 
	item(image=icon.glyph(\uE033,default_icon_size)					tip=['E033']								cmd=command.copy('\uE033')) 
	item(image=icon.glyph(\uE034,default_icon_size)					tip=['E034']								cmd=command.copy('\uE034')) 
	item(image=icon.glyph(\uE035,default_icon_size)					tip=['E035']								cmd=command.copy('\uE035')) 
	item(image=icon.glyph(\uE036,default_icon_size)					tip=['E036']								cmd=command.copy('\uE036')) 
	item(image=icon.glyph(\uE037,default_icon_size)					tip=['E037']								cmd=command.copy('\uE037')) 
	item(image=icon.glyph(\uE038,default_icon_size)					tip=['E038']								cmd=command.copy('\uE038')) 
	item(image=icon.glyph(\uE039,default_icon_size)					tip=['E039']								cmd=command.copy('\uE039')) 
	item(image=icon.glyph(\uE03A,default_icon_size)					tip=['E03A']								cmd=command.copy('\uE03A')) 
	item(image=icon.glyph(\uE03B,default_icon_size)					tip=['E03B']								cmd=command.copy('\uE03B')) 
	item(image=icon.glyph(\uE03C,default_icon_size)					tip=['E03C']								cmd=command.copy('\uE03C')) 
	item(image=icon.glyph(\uE03D,default_icon_size)					tip=['E03D']								cmd=command.copy('\uE03D')) 
	item(image=icon.glyph(\uE03E,default_icon_size)					tip=['E03E']								cmd=command.copy('\uE03E')) 
	item(image=icon.glyph(\uE03F,default_icon_size)					tip=['E03F']								cmd=command.copy('\uE03F'))
	
	item(image=icon.glyph(\uE040,default_icon_size)					tip=['E040']								cmd=command.copy('\uE040') col) 
	item(image=icon.glyph(\uE041,default_icon_size)					tip=['E041']								cmd=command.copy('\uE041')) 
	item(image=icon.glyph(\uE042,default_icon_size)					tip=['E042']								cmd=command.copy('\uE042')) 
	item(image=icon.glyph(\uE043,default_icon_size)					tip=['E043']								cmd=command.copy('\uE043')) 
	item(image=icon.glyph(\uE044,default_icon_size)					tip=['E044']								cmd=command.copy('\uE044')) 
	item(image=icon.glyph(\uE045,default_icon_size)					tip=['E045']								cmd=command.copy('\uE045')) 
	item(image=icon.glyph(\uE046,default_icon_size)					tip=['E046']								cmd=command.copy('\uE046')) 
	item(image=icon.glyph(\uE047,default_icon_size)					tip=['E047']								cmd=command.copy('\uE047')) 
	item(image=icon.glyph(\uE048,default_icon_size)					tip=['E048']								cmd=command.copy('\uE048')) 
	item(image=icon.glyph(\uE049,default_icon_size)					tip=['E049']								cmd=command.copy('\uE049')) 
	item(image=icon.glyph(\uE04A,default_icon_size)					tip=['E04A']								cmd=command.copy('\uE04A')) 
	item(image=icon.glyph(\uE04B,default_icon_size)					tip=['E04B']								cmd=command.copy('\uE04B')) 
	item(image=icon.glyph(\uE04C,default_icon_size)					tip=['E04C']								cmd=command.copy('\uE04C')) 
	item(image=icon.glyph(\uE04D,default_icon_size)					tip=['E04D']								cmd=command.copy('\uE04D')) 
	item(image=icon.glyph(\uE04E,default_icon_size)					tip=['E04E']								cmd=command.copy('\uE04E')) 
	item(image=icon.glyph(\uE04F,default_icon_size)					tip=['E04F']								cmd=command.copy('\uE04F'))
	
	item(image=icon.glyph(\uE050,default_icon_size)					tip=['E050']								cmd=command.copy('\uE050') col) 
	item(image=icon.glyph(\uE051,default_icon_size)					tip=['E051']								cmd=command.copy('\uE051')) 
	item(image=icon.glyph(\uE052,default_icon_size)					tip=['E052']								cmd=command.copy('\uE052')) 
	item(image=icon.glyph(\uE053,default_icon_size)					tip=['E053']								cmd=command.copy('\uE053')) 
	item(image=icon.glyph(\uE054,default_icon_size)					tip=['E054']								cmd=command.copy('\uE054')) 
	item(image=icon.glyph(\uE055,default_icon_size)					tip=['E055']								cmd=command.copy('\uE055')) 
	item(image=icon.glyph(\uE056,default_icon_size)					tip=['E056']								cmd=command.copy('\uE056')) 
	item(image=icon.glyph(\uE057,default_icon_size)					tip=['E057']								cmd=command.copy('\uE057')) 
	item(image=icon.glyph(\uE058,default_icon_size)					tip=['E058']								cmd=command.copy('\uE058')) 
	item(image=icon.glyph(\uE059,default_icon_size)					tip=['E059']								cmd=command.copy('\uE059')) 
	item(image=icon.glyph(\uE05A,default_icon_size)					tip=['E05A']								cmd=command.copy('\uE05A')) 
	item(image=icon.glyph(\uE05B,default_icon_size)					tip=['E05B']								cmd=command.copy('\uE05B')) 
	item(image=icon.glyph(\uE05C,default_icon_size)					tip=['E05C']								cmd=command.copy('\uE05C')) 
	item(image=icon.glyph(\uE05D,default_icon_size)					tip=['E05D']								cmd=command.copy('\uE05D')) 
	item(image=icon.glyph(\uE05E,default_icon_size)					tip=['E05E']								cmd=command.copy('\uE05E')) 
	item(image=icon.glyph(\uE05F,default_icon_size)					tip=['E05F']								cmd=command.copy('\uE05F'))
	
	item(image=icon.glyph(\uE060,default_icon_size)					tip=['E060']								cmd=command.copy('\uE060') col) 
	item(image=icon.glyph(\uE061,default_icon_size)					tip=['E061']								cmd=command.copy('\uE061')) 
	item(image=icon.glyph(\uE062,default_icon_size)					tip=['E062']								cmd=command.copy('\uE062')) 
	item(image=icon.glyph(\uE063,default_icon_size)					tip=['E063']								cmd=command.copy('\uE063')) 
	item(image=icon.glyph(\uE064,default_icon_size)					tip=['E064']								cmd=command.copy('\uE064')) 
	item(image=icon.glyph(\uE065,default_icon_size)					tip=['E065']								cmd=command.copy('\uE065')) 
	item(image=icon.glyph(\uE066,default_icon_size)					tip=['E066']								cmd=command.copy('\uE066')) 
	item(image=icon.glyph(\uE067,default_icon_size)					tip=['E067']								cmd=command.copy('\uE067')) 
	item(image=icon.glyph(\uE068,default_icon_size)					tip=['E068']								cmd=command.copy('\uE068')) 
	item(image=icon.glyph(\uE069,default_icon_size)					tip=['E069']								cmd=command.copy('\uE069')) 
	item(image=icon.glyph(\uE06A,default_icon_size)					tip=['E06A']								cmd=command.copy('\uE06A')) 
	item(image=icon.glyph(\uE06B,default_icon_size)					tip=['E06B']								cmd=command.copy('\uE06B')) 
	item(image=icon.glyph(\uE06C,default_icon_size)					tip=['E06C']								cmd=command.copy('\uE06C')) 
	item(image=icon.glyph(\uE06D,default_icon_size)					tip=['E06D']								cmd=command.copy('\uE06D')) 
	item(image=icon.glyph(\uE06E,default_icon_size)					tip=['E06E']								cmd=command.copy('\uE06E')) 
	item(image=icon.glyph(\uE06F,default_icon_size)					tip=['E06F']								cmd=command.copy('\uE06F'))
	
	item(image=icon.glyph(\uE070,default_icon_size)					tip=['E070']								cmd=command.copy('\uE070') col) 
	item(image=icon.glyph(\uE071,default_icon_size)					tip=['E071']								cmd=command.copy('\uE071')) 
	item(image=icon.glyph(\uE072,default_icon_size)					tip=['E072']								cmd=command.copy('\uE072')) 
	item(image=icon.glyph(\uE073,default_icon_size)					tip=['E073']								cmd=command.copy('\uE073')) 
	item(image=icon.glyph(\uE074,default_icon_size)					tip=['E074']								cmd=command.copy('\uE074')) 
	item(image=icon.glyph(\uE075,default_icon_size)					tip=['E075']								cmd=command.copy('\uE075')) 
	item(image=icon.glyph(\uE076,default_icon_size)					tip=['E076']								cmd=command.copy('\uE076')) 
	item(image=icon.glyph(\uE077,default_icon_size)					tip=['E077']								cmd=command.copy('\uE077')) 
	item(image=icon.glyph(\uE078,default_icon_size)					tip=['E078']								cmd=command.copy('\uE078')) 
	item(image=icon.glyph(\uE079,default_icon_size)					tip=['E079']								cmd=command.copy('\uE079')) 
	item(image=icon.glyph(\uE07A,default_icon_size)					tip=['E07A']								cmd=command.copy('\uE07A')) 
	item(image=icon.glyph(\uE07B,default_icon_size)					tip=['E07B']								cmd=command.copy('\uE07B')) 
	item(image=icon.glyph(\uE07C,default_icon_size)					tip=['E07C']								cmd=command.copy('\uE07C')) 
	item(image=icon.glyph(\uE07D,default_icon_size)					tip=['E07D']								cmd=command.copy('\uE07D')) 
	item(image=icon.glyph(\uE07E,default_icon_size)					tip=['E07E']								cmd=command.copy('\uE07E')) 
	item(image=icon.glyph(\uE07F,default_icon_size)					tip=['E07F']								cmd=command.copy('\uE07F'))
	
	item(image=icon.glyph(\uE080,default_icon_size)					tip=['E080']								cmd=command.copy('\uE080') col) 
	item(image=icon.glyph(\uE081,default_icon_size)					tip=['E081']								cmd=command.copy('\uE081')) 
	item(image=icon.glyph(\uE082,default_icon_size)					tip=['E082']								cmd=command.copy('\uE082')) 
	item(image=icon.glyph(\uE083,default_icon_size)					tip=['E083']								cmd=command.copy('\uE083')) 
	item(image=icon.glyph(\uE084,default_icon_size)					tip=['E084']								cmd=command.copy('\uE084')) 
	item(image=icon.glyph(\uE085,default_icon_size)					tip=['E085']								cmd=command.copy('\uE085')) 
	item(image=icon.glyph(\uE086,default_icon_size)					tip=['E086']								cmd=command.copy('\uE086')) 
	item(image=icon.glyph(\uE087,default_icon_size)					tip=['E087']								cmd=command.copy('\uE087')) 
	item(image=icon.glyph(\uE088,default_icon_size)					tip=['E088']								cmd=command.copy('\uE088')) 
	item(image=icon.glyph(\uE089,default_icon_size)					tip=['E089']								cmd=command.copy('\uE089')) 
	item(image=icon.glyph(\uE08A,default_icon_size)					tip=['E08A']								cmd=command.copy('\uE08A')) 
	item(image=icon.glyph(\uE08B,default_icon_size)					tip=['E08B']								cmd=command.copy('\uE08B')) 
	item(image=icon.glyph(\uE08C,default_icon_size)					tip=['E08C']								cmd=command.copy('\uE08C')) 
	item(image=icon.glyph(\uE08D,default_icon_size)					tip=['E08D']								cmd=command.copy('\uE08D')) 
	item(image=icon.glyph(\uE08E,default_icon_size)					tip=['E08E']								cmd=command.copy('\uE08E')) 
	item(image=icon.glyph(\uE08F,default_icon_size)					tip=['E08F']								cmd=command.copy('\uE08F'))
	}
	menu(title='Glifos' 		   		 image=\uE097				tip=['SHELL glyphs gallery'])
	{
	item(image=icon.glyph(\uE090,default_icon_size)					tip=['E090']								cmd=command.copy('\uE090')) 
	item(image=icon.glyph(\uE091,default_icon_size)					tip=['E091']								cmd=command.copy('\uE091')) 
	item(image=icon.glyph(\uE092,default_icon_size)					tip=['E092']								cmd=command.copy('\uE092')) 
	item(image=icon.glyph(\uE093,default_icon_size)					tip=['E093']								cmd=command.copy('\uE093')) 
	item(image=icon.glyph(\uE094,default_icon_size)					tip=['E094']								cmd=command.copy('\uE094')) 
	item(image=icon.glyph(\uE095,default_icon_size)					tip=['E095']								cmd=command.copy('\uE095')) 
	item(image=icon.glyph(\uE096,default_icon_size)					tip=['E096']								cmd=command.copy('\uE096')) 
	item(image=icon.glyph(\uE097,default_icon_size)					tip=['E097']								cmd=command.copy('\uE097')) 
	item(image=icon.glyph(\uE098,default_icon_size)					tip=['E098']								cmd=command.copy('\uE098')) 
	item(image=icon.glyph(\uE099,default_icon_size)					tip=['E099']								cmd=command.copy('\uE099')) 
	item(image=icon.glyph(\uE09A,default_icon_size)					tip=['E09A']								cmd=command.copy('\uE09A')) 
	item(image=icon.glyph(\uE09B,default_icon_size)					tip=['E09B']								cmd=command.copy('\uE09B')) 
	item(image=icon.glyph(\uE09C,default_icon_size)					tip=['E09C']								cmd=command.copy('\uE09C')) 
	item(image=icon.glyph(\uE09D,default_icon_size)					tip=['E09D']								cmd=command.copy('\uE09D')) 
	item(image=icon.glyph(\uE09E,default_icon_size)					tip=['E09E']								cmd=command.copy('\uE09E')) 
	item(image=icon.glyph(\uE09F,default_icon_size)					tip=['E09F']								cmd=command.copy('\uE09F')) 
		
	item(image=icon.glyph(\uE100,default_icon_size)					tip=['E100']								cmd=command.copy('\uE100') col) 
	item(image=icon.glyph(\uE101,default_icon_size)					tip=['E101']								cmd=command.copy('\uE101')) 
	item(image=icon.glyph(\uE102,default_icon_size)					tip=['E102']								cmd=command.copy('\uE102')) 
	item(image=icon.glyph(\uE103,default_icon_size)					tip=['E103']								cmd=command.copy('\uE103')) 
	item(image=icon.glyph(\uE104,default_icon_size)					tip=['E104']								cmd=command.copy('\uE104')) 
	item(image=icon.glyph(\uE105,default_icon_size)					tip=['E105']								cmd=command.copy('\uE105')) 
	item(image=icon.glyph(\uE106,default_icon_size)					tip=['E106']								cmd=command.copy('\uE106')) 
	item(image=icon.glyph(\uE107,default_icon_size)					tip=['E107']								cmd=command.copy('\uE107')) 
	item(image=icon.glyph(\uE108,default_icon_size)					tip=['E108']								cmd=command.copy('\uE108')) 
	item(image=icon.glyph(\uE109,default_icon_size)					tip=['E109']								cmd=command.copy('\uE109')) 
	item(image=icon.glyph(\uE10A,default_icon_size)					tip=['E10A']								cmd=command.copy('\uE10A')) 
	item(image=icon.glyph(\uE10B,default_icon_size)					tip=['E10B']								cmd=command.copy('\uE10B')) 
	item(image=icon.glyph(\uE10C,default_icon_size)					tip=['E10C']								cmd=command.copy('\uE10C')) 
	item(image=icon.glyph(\uE10D,default_icon_size)					tip=['E10D']								cmd=command.copy('\uE10D')) 
	item(image=icon.glyph(\uE10E,default_icon_size)					tip=['E10E']								cmd=command.copy('\uE10E')) 
	item(image=icon.glyph(\uE10F,default_icon_size)					tip=['E10F']								cmd=command.copy('\uE10F'))
			
	item(image=icon.glyph(\uE110,default_icon_size)					tip=['E110']								cmd=command.copy('\uE110')	col) 
	item(image=icon.glyph(\uE111,default_icon_size)					tip=['E111']								cmd=command.copy('\uE111')) 
	item(image=icon.glyph(\uE112,default_icon_size)					tip=['E112']								cmd=command.copy('\uE112')) 
	item(image=icon.glyph(\uE113,default_icon_size)					tip=['E113']								cmd=command.copy('\uE113'))
	item(image=icon.glyph(\uE114,default_icon_size)					tip=['E114']								cmd=command.copy('\uE114'))
	item(image=icon.glyph(\uE115,default_icon_size)					tip=['E115']								cmd=command.copy('\uE115')) 
	item(image=icon.glyph(\uE116,default_icon_size)					tip=['E116']								cmd=command.copy('\uE116')) 
	item(image=icon.glyph(\uE117,default_icon_size)					tip=['E117']								cmd=command.copy('\uE117')) 
	item(image=icon.glyph(\uE118,default_icon_size)					tip=['E118']								cmd=command.copy('\uE118')) 
	item(image=icon.glyph(\uE119,default_icon_size)					tip=['E119']								cmd=command.copy('\uE119')) 
	item(image=icon.glyph(\uE11A,default_icon_size)					tip=['E11A']								cmd=command.copy('\uE11A')) 
	item(image=icon.glyph(\uE11B,default_icon_size)					tip=['E11B']								cmd=command.copy('\uE11B')) 
	item(image=icon.glyph(\uE11C,default_icon_size)					tip=['E11C']								cmd=command.copy('\uE11C')) 
	item(image=icon.glyph(\uE11D,default_icon_size)					tip=['E11D']								cmd=command.copy('\uE11D')) 
	item(image=icon.glyph(\uE11E,default_icon_size)					tip=['E11E']								cmd=command.copy('\uE11E')) 
	item(image=icon.glyph(\uE11F,default_icon_size)					tip=['E11F']								cmd=command.copy('\uE11F'))

	item(image=icon.glyph(\uE120,default_icon_size)					tip=['E120']								cmd=command.copy('\uE120')	col) 
	item(image=icon.glyph(\uE121,default_icon_size)					tip=['E121']								cmd=command.copy('\uE121')) 
	item(image=icon.glyph(\uE122,default_icon_size)					tip=['E122']								cmd=command.copy('\uE122')) 
	item(image=icon.glyph(\uE123,default_icon_size)					tip=['E123']								cmd=command.copy('\uE123'))
	item(image=icon.glyph(\uE124,default_icon_size)					tip=['E124']								cmd=command.copy('\uE124')) 
	item(image=icon.glyph(\uE125,default_icon_size)					tip=['E125']								cmd=command.copy('\uE125')) 
	item(image=icon.glyph(\uE126,default_icon_size)					tip=['E126']								cmd=command.copy('\uE126')) 
	item(image=icon.glyph(\uE127,default_icon_size)					tip=['E127']								cmd=command.copy('\uE127')) 
	item(image=icon.glyph(\uE128,default_icon_size)					tip=['E128']								cmd=command.copy('\uE128')) 
	item(image=icon.glyph(\uE129,default_icon_size)					tip=['E129']								cmd=command.copy('\uE129')) 
	item(image=icon.glyph(\uE12A,default_icon_size)					tip=['E12A']								cmd=command.copy('\uE12A')) 
	item(image=icon.glyph(\uE12B,default_icon_size)					tip=['E12B']								cmd=command.copy('\uE12B')) 
	item(image=icon.glyph(\uE12C,default_icon_size)					tip=['E12C']								cmd=command.copy('\uE12C')) 
	item(image=icon.glyph(\uE12D,default_icon_size)					tip=['E12D']								cmd=command.copy('\uE12D')) 
	item(image=icon.glyph(\uE12E,default_icon_size)					tip=['E12E']								cmd=command.copy('\uE12E')) 
	item(image=icon.glyph(\uE12F,default_icon_size)					tip=['E12F']								cmd=command.copy('\uE12F'))
	
	item(image=icon.glyph(\uE130,default_icon_size)					tip=['E130']								cmd=command.copy('\uE130')	col) 
	item(image=icon.glyph(\uE131,default_icon_size)					tip=['E131']								cmd=command.copy('\uE131')) 
	item(image=icon.glyph(\uE132,default_icon_size)					tip=['E132']								cmd=command.copy('\uE132')) 
	item(image=icon.glyph(\uE133,default_icon_size)					tip=['E133']								cmd=command.copy('\uE133'))
	item(image=icon.glyph(\uE134,default_icon_size)					tip=['E134']								cmd=command.copy('\uE134')) 
	item(image=icon.glyph(\uE135,default_icon_size)					tip=['E135']								cmd=command.copy('\uE135')) 
	item(image=icon.glyph(\uE136,default_icon_size)					tip=['E136']								cmd=command.copy('\uE136')) 
	item(image=icon.glyph(\uE137,default_icon_size)					tip=['E137']								cmd=command.copy('\uE137')) 
	item(image=icon.glyph(\uE138,default_icon_size)					tip=['E138']								cmd=command.copy('\uE138')) 
	item(image=icon.glyph(\uE139,default_icon_size)					tip=['E139']								cmd=command.copy('\uE139')) 
	item(image=icon.glyph(\uE13A,default_icon_size)					tip=['E13A']								cmd=command.copy('\uE13A')) 
	item(image=icon.glyph(\uE13B,default_icon_size)					tip=['E13B']								cmd=command.copy('\uE13B')) 
	item(image=icon.glyph(\uE13C,default_icon_size)					tip=['E13C']								cmd=command.copy('\uE13C')) 
	item(image=icon.glyph(\uE13D,default_icon_size)					tip=['E13D']								cmd=command.copy('\uE13D')) 
	item(image=icon.glyph(\uE13E,default_icon_size)					tip=['E13E']								cmd=command.copy('\uE13E')) 
	item(image=icon.glyph(\uE13F,default_icon_size)					tip=['E13F']								cmd=command.copy('\uE13F'))
		
	item(image=icon.glyph(\uE140,default_icon_size)					tip=['E140']								cmd=command.copy('\uE140')	col) 
	item(image=icon.glyph(\uE141,default_icon_size)					tip=['E141']								cmd=command.copy('\uE141')) 
	item(image=icon.glyph(\uE142,default_icon_size)					tip=['E142']								cmd=command.copy('\uE142')) 
	item(image=icon.glyph(\uE143,default_icon_size)					tip=['E143']								cmd=command.copy('\uE143'))
	item(image=icon.glyph(\uE144,default_icon_size)					tip=['E144']								cmd=command.copy('\uE144')) 
	item(image=icon.glyph(\uE145,default_icon_size)					tip=['E145']								cmd=command.copy('\uE145')) 
	item(image=icon.glyph(\uE146,default_icon_size)					tip=['E146']								cmd=command.copy('\uE146')) 
	item(image=icon.glyph(\uE147,default_icon_size)					tip=['E147']								cmd=command.copy('\uE147')) 
	item(image=icon.glyph(\uE148,default_icon_size)					tip=['E148']								cmd=command.copy('\uE148')) 
	item(image=icon.glyph(\uE149,default_icon_size)					tip=['E149']								cmd=command.copy('\uE149')) 
	item(image=icon.glyph(\uE14A,default_icon_size)					tip=['E14A']								cmd=command.copy('\uE14A')) 
	item(image=icon.glyph(\uE14B,default_icon_size)					tip=['E14B']								cmd=command.copy('\uE14B')) 
	item(image=icon.glyph(\uE14C,default_icon_size)					tip=['E14C']								cmd=command.copy('\uE14C')) 
	item(image=icon.glyph(\uE14D,default_icon_size)					tip=['E14D']								cmd=command.copy('\uE14D')) 
	item(image=icon.glyph(\uE14E,default_icon_size)					tip=['E14E']								cmd=command.copy('\uE14E')) 
	item(image=icon.glyph(\uE14F,default_icon_size)					tip=['E14F']								cmd=command.copy('\uE14F'))
		
	item(image=icon.glyph(\uE150,default_icon_size)					tip=['E150']								cmd=command.copy('\uE150')	col) 
	item(image=icon.glyph(\uE151,default_icon_size)					tip=['E151']								cmd=command.copy('\uE151')) 
	item(image=icon.glyph(\uE152,default_icon_size)					tip=['E152']								cmd=command.copy('\uE152')) 
	item(image=icon.glyph(\uE153,default_icon_size)					tip=['E153']								cmd=command.copy('\uE153')) 
	item(image=icon.glyph(\uE154,default_icon_size)					tip=['E154']								cmd=command.copy('\uE154')) 
	item(image=icon.glyph(\uE155,default_icon_size)					tip=['E155']								cmd=command.copy('\uE155')) 
	item(image=icon.glyph(\uE156,default_icon_size)					tip=['E156']								cmd=command.copy('\uE156')) 
	item(image=icon.glyph(\uE157,default_icon_size)					tip=['E157']								cmd=command.copy('\uE157')) 
	item(image=icon.glyph(\uE158,default_icon_size)					tip=['E158']								cmd=command.copy('\uE158')) 
	item(image=icon.glyph(\uE159,default_icon_size)					tip=['E159']								cmd=command.copy('\uE159')) 
	item(image=icon.glyph(\uE15A,default_icon_size)					tip=['E15A']								cmd=command.copy('\uE15A')) 
	item(image=icon.glyph(\uE15B,default_icon_size)					tip=['E15B']								cmd=command.copy('\uE15B')) 
	item(image=icon.glyph(\uE15C,default_icon_size)					tip=['E15C']								cmd=command.copy('\uE15C')) 
	item(image=icon.glyph(\uE15D,default_icon_size)					tip=['E15D']								cmd=command.copy('\uE15D')) 
	item(image=icon.glyph(\uE15E,default_icon_size)					tip=['E15E']								cmd=command.copy('\uE15E')) 
	item(image=icon.glyph(\uE15F,default_icon_size)					tip=['E15F']								cmd=command.copy('\uE15F'))
			
	item(image=icon.glyph(\uE160,default_icon_size)					tip=['E160']								cmd=command.copy('\uE160')	col) 
	item(image=icon.glyph(\uE161,default_icon_size)					tip=['E161']								cmd=command.copy('\uE161')) 
	item(image=icon.glyph(\uE162,default_icon_size)					tip=['E162']								cmd=command.copy('\uE162')) 
	item(image=icon.glyph(\uE163,default_icon_size)					tip=['E163']								cmd=command.copy('\uE163')) 
	item(image=icon.glyph(\uE164,default_icon_size)					tip=['E164']								cmd=command.copy('\uE164')) 
	item(image=icon.glyph(\uE165,default_icon_size)					tip=['E165']								cmd=command.copy('\uE165')) 
	item(image=icon.glyph(\uE166,default_icon_size)					tip=['E166']								cmd=command.copy('\uE166')) 
	item(image=icon.glyph(\uE167,default_icon_size)					tip=['E167']								cmd=command.copy('\uE167')) 
	item(image=icon.glyph(\uE168,default_icon_size)					tip=['E168']								cmd=command.copy('\uE168')) 
	item(image=icon.glyph(\uE169,default_icon_size)					tip=['E169']								cmd=command.copy('\uE169')) 
	item(image=icon.glyph(\uE16A,default_icon_size)					tip=['E16A']								cmd=command.copy('\uE16A')) 
	item(image=icon.glyph(\uE16B,default_icon_size)					tip=['E16B']								cmd=command.copy('\uE16B')) 
	item(image=icon.glyph(\uE16C,default_icon_size)					tip=['E16C']								cmd=command.copy('\uE16C')) 
	item(image=icon.glyph(\uE16D,default_icon_size)					tip=['E16D']								cmd=command.copy('\uE16D')) 
	item(image=icon.glyph(\uE16E,default_icon_size)					tip=['E16E']								cmd=command.copy('\uE16E')) 
	item(image=icon.glyph(\uE16F,default_icon_size)					tip=['E16F']								cmd=command.copy('\uE16F'))
			
	item(image=icon.glyph(\uE170,default_icon_size)					tip=['E170']								cmd=command.copy('\uE170')	col) 
	item(image=icon.glyph(\uE171,default_icon_size)					tip=['E171']								cmd=command.copy('\uE171')) 
	item(image=icon.glyph(\uE172,default_icon_size)					tip=['E172']								cmd=command.copy('\uE172')) 
	item(image=icon.glyph(\uE173,default_icon_size)					tip=['E173']								cmd=command.copy('\uE173')) 
	item(image=icon.glyph(\uE174,default_icon_size)					tip=['E174']								cmd=command.copy('\uE174')) 
	item(image=icon.glyph(\uE175,default_icon_size)					tip=['E175']								cmd=command.copy('\uE175')) 
	item(image=icon.glyph(\uE176,default_icon_size)					tip=['E176']								cmd=command.copy('\uE176')) 
	item(image=icon.glyph(\uE177,default_icon_size)					tip=['E177']								cmd=command.copy('\uE177')) 
	item(image=icon.glyph(\uE178,default_icon_size)					tip=['E178']								cmd=command.copy('\uE178')) 
	item(image=icon.glyph(\uE179,default_icon_size)					tip=['E179']								cmd=command.copy('\uE179')) 
	item(image=icon.glyph(\uE17A,default_icon_size)					tip=['E17A']								cmd=command.copy('\uE17A')) 
	item(image=icon.glyph(\uE17B,default_icon_size)					tip=['E17B']								cmd=command.copy('\uE17B')) 
	item(image=icon.glyph(\uE17C,default_icon_size)					tip=['E17C']								cmd=command.copy('\uE17C')) 
	item(image=icon.glyph(\uE17D,default_icon_size)					tip=['E17D']								cmd=command.copy('\uE17D')) 
	item(image=icon.glyph(\uE17E,default_icon_size)					tip=['E17E']								cmd=command.copy('\uE17E')) 
	item(image=icon.glyph(\uE17F,default_icon_size)					tip=['E17F']								cmd=command.copy('\uE17F'))
			
	item(image=icon.glyph(\uE180,default_icon_size)					tip=['E180']								cmd=command.copy('\uE180')	col) 
	item(image=icon.glyph(\uE181,default_icon_size)					tip=['E181']								cmd=command.copy('\uE181')) 
	item(image=icon.glyph(\uE182,default_icon_size)					tip=['E182']								cmd=command.copy('\uE182')) 
	item(image=icon.glyph(\uE183,default_icon_size)					tip=['E183']								cmd=command.copy('\uE183')) 
	item(image=icon.glyph(\uE184,default_icon_size)					tip=['E184']								cmd=command.copy('\uE184')) 
	item(image=icon.glyph(\uE185,default_icon_size)					tip=['E185']								cmd=command.copy('\uE185')) 
	item(image=icon.glyph(\uE186,default_icon_size)					tip=['E186']								cmd=command.copy('\uE186')) 
	item(image=icon.glyph(\uE187,default_icon_size)					tip=['E187']								cmd=command.copy('\uE187')) 
	item(image=icon.glyph(\uE188,default_icon_size)					tip=['E188']								cmd=command.copy('\uE188')) 
	item(image=icon.glyph(\uE189,default_icon_size)					tip=['E189']								cmd=command.copy('\uE189')) 
	item(image=icon.glyph(\uE18A,default_icon_size)					tip=['E18A']								cmd=command.copy('\uE18A')) 
	item(image=icon.glyph(\uE18B,default_icon_size)					tip=['E18B']								cmd=command.copy('\uE18B')) 
	item(image=icon.glyph(\uE18C,default_icon_size)					tip=['E18C']								cmd=command.copy('\uE18C')) 
	item(image=icon.glyph(\uE18D,default_icon_size)					tip=['E18D']								cmd=command.copy('\uE18D')) 
	item(image=icon.glyph(\uE18E,default_icon_size)					tip=['E18E']								cmd=command.copy('\uE18E')) 
	item(image=icon.glyph(\uE18F,default_icon_size)					tip=['E18F']								cmd=command.copy('\uE18F'))
	
	item(image=icon.glyph(\uE190,default_icon_size)					tip=['E190']								cmd=command.copy('\uE190')	col) 
	item(image=icon.glyph(\uE191,default_icon_size)					tip=['E191']								cmd=command.copy('\uE191')) 
	item(image=icon.glyph(\uE192,default_icon_size)					tip=['E192']								cmd=command.copy('\uE192')) 
	item(image=icon.glyph(\uE193,default_icon_size)					tip=['E193']								cmd=command.copy('\uE193')) 
	item(image=icon.glyph(\uE194,default_icon_size)					tip=['E194']								cmd=command.copy('\uE194')) 
	item(image=icon.glyph(\uE195,default_icon_size)					tip=['E195']								cmd=command.copy('\uE195')) 
	item(image=icon.glyph(\uE196,default_icon_size)					tip=['E196']								cmd=command.copy('\uE196')) 
	item(image=icon.glyph(\uE197,default_icon_size)					tip=['E197']								cmd=command.copy('\uE197')) 
	item(image=icon.glyph(\uE198,default_icon_size)					tip=['E198']								cmd=command.copy('\uE198')) 
	item(image=icon.glyph(\uE199,default_icon_size)					tip=['E199']								cmd=command.copy('\uE199')) 
	item(image=icon.glyph(\uE19A,default_icon_size)					tip=['E19A']								cmd=command.copy('\uE19A')) 
	item(image=icon.glyph(\uE19B,default_icon_size)					tip=['E19B']								cmd=command.copy('\uE19B')) 
	item(image=icon.glyph(\uE19C,default_icon_size)					tip=['E19C']								cmd=command.copy('\uE19C')) 
	item(image=icon.glyph(\uE19D,default_icon_size)					tip=['E19D']								cmd=command.copy('\uE19D')) 
	item(image=icon.glyph(\uE19E,default_icon_size)					tip=['E19E']								cmd=command.copy('\uE19E')) 
	item(image=icon.glyph(\uE19F,default_icon_size)					tip=['E19F']								cmd=command.copy('\uE19F'))
			
	item(image=icon.glyph(\uE1A0,default_icon_size)					tip=['E1A0']								cmd=command.copy('\uE1A0')	col) 
	item(image=icon.glyph(\uE1A1,default_icon_size)					tip=['E1A1']								cmd=command.copy('\uE1A1')) 
	item(image=icon.glyph(\uE1A2,default_icon_size)					tip=['E1A2']								cmd=command.copy('\uE1A2')) 
	item(image=icon.glyph(\uE1A3,default_icon_size)					tip=['E1A3']								cmd=command.copy('\uE1A3')) 
	item(image=icon.glyph(\uE1A4,default_icon_size)					tip=['E1A4']								cmd=command.copy('\uE1A4')) 
	item(image=icon.glyph(\uE1A5,default_icon_size)					tip=['E1A5']								cmd=command.copy('\uE1A5')) 
	item(image=icon.glyph(\uE1A6,default_icon_size)					tip=['E1A6']								cmd=command.copy('\uE1A6')) 
	item(image=icon.glyph(\uE1A7,default_icon_size)					tip=['E1A7']								cmd=command.copy('\uE1A7')) 
	item(image=icon.glyph(\uE1A8,default_icon_size)					tip=['E1A8']								cmd=command.copy('\uE1A8')) 
	item(image=icon.glyph(\uE1A9,default_icon_size)					tip=['E1A9']								cmd=command.copy('\uE1A9')) 
	item(image=icon.glyph(\uE1AA,default_icon_size)					tip=['E1AA']								cmd=command.copy('\uE1AA')) 
	item(image=icon.glyph(\uE1AB,default_icon_size)					tip=['E1AB']								cmd=command.copy('\uE1AB')) 
	item(image=icon.glyph(\uE1AC,default_icon_size)					tip=['E1AC']								cmd=command.copy('\uE1AC')) 
	item(image=icon.glyph(\uE1AD,default_icon_size)					tip=['E1AD']								cmd=command.copy('\uE1AD')) 
	item(image=icon.glyph(\uE1AE,default_icon_size)					tip=['E1AE']								cmd=command.copy('\uE1AE')) 
	item(image=icon.glyph(\uE1AF,default_icon_size)					tip=['E1AF']								cmd=command.copy('\uE1AF'))
			
	item(image=icon.glyph(\uE1B0,default_icon_size)					tip=['E1B0']								cmd=command.copy('\uE1B0')	col) 
	item(image=icon.glyph(\uE1B1,default_icon_size)					tip=['E1B1']								cmd=command.copy('\uE1B1')) 
	item(image=icon.glyph(\uE1B2,default_icon_size)					tip=['E1B2']								cmd=command.copy('\uE1B2')) 
	item(image=icon.glyph(\uE1B3,default_icon_size)					tip=['E1B3']								cmd=command.copy('\uE1B3')) 
	item(image=icon.glyph(\uE1B4,default_icon_size)					tip=['E1B4']								cmd=command.copy('\uE1B4')) 
	item(image=icon.glyph(\uE1B5,default_icon_size)					tip=['E1B5']								cmd=command.copy('\uE1B5')) 
	item(image=icon.glyph(\uE1B6,default_icon_size)					tip=['E1B6']								cmd=command.copy('\uE1B6')) 
	item(image=icon.glyph(\uE1B7,default_icon_size)					tip=['E1B7']								cmd=command.copy('\uE1B7')) 
	item(image=icon.glyph(\uE1B8,default_icon_size)					tip=['E1B8']								cmd=command.copy('\uE1B8')) 
	item(image=icon.glyph(\uE1B9,default_icon_size)					tip=['E1B9']								cmd=command.copy('\uE1B9')) 
	item(image=icon.glyph(\uE1BA,default_icon_size)					tip=['E1BA']								cmd=command.copy('\uE1BA')) 
	item(image=icon.glyph(\uE1BB,default_icon_size)					tip=['E1BB']								cmd=command.copy('\uE1BB')) 
	item(image=icon.glyph(\uE1BC,default_icon_size)					tip=['E1BC']								cmd=command.copy('\uE1BC')) 
	item(image=icon.glyph(\uE1BD,default_icon_size)					tip=['E1BD']								cmd=command.copy('\uE1BD')) 
	item(image=icon.glyph(\uE1BE,default_icon_size)					tip=['E1BE']								cmd=command.copy('\uE1BE')) 
	item(image=icon.glyph(\uE1BF,default_icon_size)					tip=['E1BF']								cmd=command.copy('\uE1BF'))
			
	item(image=icon.glyph(\uE1C0,default_icon_size)					tip=['E1C0']								cmd=command.copy('\uE1C0')	col) 
	item(image=icon.glyph(\uE1C1,default_icon_size)					tip=['E1C1']								cmd=command.copy('\uE1C1')) 
	item(image=icon.glyph(\uE1C2,default_icon_size)					tip=['E1C2']								cmd=command.copy('\uE1C2')) 
	item(image=icon.glyph(\uE1C3,default_icon_size)					tip=['E1C3']								cmd=command.copy('\uE1C3')) 
	item(image=icon.glyph(\uE1C4,default_icon_size)					tip=['E1C4']								cmd=command.copy('\uE1C4')) 
	item(image=icon.glyph(\uE1C5,default_icon_size)					tip=['E1C5']								cmd=command.copy('\uE1C5')) 
	item(image=icon.glyph(\uE1C6,default_icon_size)					tip=['E1C6']								cmd=command.copy('\uE1C6')) 
	item(image=icon.glyph(\uE1C7,default_icon_size)					tip=['E1C7']								cmd=command.copy('\uE1C7')) 
	item(image=icon.glyph(\uE1C8,default_icon_size)					tip=['E1C8']								cmd=command.copy('\uE1C8')) 
	item(image=icon.glyph(\uE1C9,default_icon_size)					tip=['E1C9']								cmd=command.copy('\uE1C9')) 
	item(image=icon.glyph(\uE1CA,default_icon_size)					tip=['E1CA']								cmd=command.copy('\uE1CA')) 
	item(image=icon.glyph(\uE1CB,default_icon_size)					tip=['E1CB']								cmd=command.copy('\uE1CB')) 
	item(image=icon.glyph(\uE1CC,default_icon_size)					tip=['E1CC']								cmd=command.copy('\uE1CC')) 
	item(image=icon.glyph(\uE1CD,default_icon_size)					tip=['E1CD']								cmd=command.copy('\uE1CD')) 
	item(image=icon.glyph(\uE1CE,default_icon_size)					tip=['E1CE']								cmd=command.copy('\uE1CE')) 
	item(image=icon.glyph(\uE1CF,default_icon_size)					tip=['E1CF']								cmd=command.copy('\uE1CF'))
			
	item(image=icon.glyph(\uE1D0,default_icon_size)					tip=['E1D0']								cmd=command.copy('\uE1D0')	col) 
	item(image=icon.glyph(\uE1D1,default_icon_size)					tip=['E1D1']								cmd=command.copy('\uE1D1')) 
	item(image=icon.glyph(\uE1D2,default_icon_size)					tip=['E1D2']								cmd=command.copy('\uE1D2')) 
	item(image=icon.glyph(\uE1D3,default_icon_size)					tip=['E1D3']								cmd=command.copy('\uE1D3')) 
	item(image=icon.glyph(\uE1D4,default_icon_size)					tip=['E1D4']								cmd=command.copy('\uE1D4')) 
	item(image=icon.glyph(\uE1D5,default_icon_size)					tip=['E1D5']								cmd=command.copy('\uE1D5')) 
	item(image=icon.glyph(\uE1D6,default_icon_size)					tip=['E1D6']								cmd=command.copy('\uE1D6')) 
	item(image=icon.glyph(\uE1D7,default_icon_size)					tip=['E1D7']								cmd=command.copy('\uE1D7')) 
	item(image=icon.glyph(\uE1D8,default_icon_size)					tip=['E1D8']								cmd=command.copy('\uE1D8')) 
	item(image=icon.glyph(\uE1D9,default_icon_size)					tip=['E1D9']								cmd=command.copy('\uE1D9')) 
	item(image=icon.glyph(\uE1DA,default_icon_size)					tip=['E1DA']								cmd=command.copy('\uE1DA')) 
	item(image=icon.glyph(\uE1DB,default_icon_size)					tip=['E1DB']								cmd=command.copy('\uE1DB')) 
	item(image=icon.glyph(\uE1DC,default_icon_size)					tip=['E1DC']								cmd=command.copy('\uE1DC')) 
	item(image=icon.glyph(\uE1DD,default_icon_size)					tip=['E1DD']								cmd=command.copy('\uE1DD')) 
	item(image=icon.glyph(\uE1DE,default_icon_size)					tip=['E1DE']								cmd=command.copy('\uE1DE')) 
	item(image=icon.glyph(\uE1DF,default_icon_size)					tip=['E1DF']								cmd=command.copy('\uE1DF'))
	
	item(image=icon.glyph(\uE1E0,default_icon_size)					tip=['E1E0']								cmd=command.copy('\uE1E0')	col) 
	item(image=icon.glyph(\uE1E1,default_icon_size)					tip=['E1E1']								cmd=command.copy('\uE1E1')) 
	item(image=icon.glyph(\uE1E2,default_icon_size)					tip=['E1E2']								cmd=command.copy('\uE1E2')) 
	item(image=icon.glyph(\uE1E3,default_icon_size)					tip=['E1E3']								cmd=command.copy('\uE1E3')) 
	item(image=icon.glyph(\uE1E4,default_icon_size)					tip=['E1E4']								cmd=command.copy('\uE1E4')) 
	item(image=icon.glyph(\uE1E5,default_icon_size)					tip=['E1E5']								cmd=command.copy('\uE1E5')) 
	item(image=icon.glyph(\uE1E6,default_icon_size)					tip=['E1E6']								cmd=command.copy('\uE1E6')) 
	item(image=icon.glyph(\uE1E7,default_icon_size)					tip=['E1E7']								cmd=command.copy('\uE1E7')) 
	item(image=icon.glyph(\uE1E8,default_icon_size)					tip=['E1E8']								cmd=command.copy('\uE1E8')) 
	item(image=icon.glyph(\uE1E9,default_icon_size)					tip=['E1E9']								cmd=command.copy('\uE1E9')) 
	item(image=icon.glyph(\uE1EA,default_icon_size)					tip=['E1EA']								cmd=command.copy('\uE1EA')) 
	item(image=icon.glyph(\uE1EB,default_icon_size)					tip=['E1EB']								cmd=command.copy('\uE1EB')) 
	item(image=icon.glyph(\uE1EC,default_icon_size)					tip=['E1EC']								cmd=command.copy('\uE1EC')) 
	item(image=icon.glyph(\uE1ED,default_icon_size)					tip=['E1ED']								cmd=command.copy('\uE1ED')) 
	item(image=icon.glyph(\uE1EE,default_icon_size)					tip=['E1EE']								cmd=command.copy('\uE1EE')) 
	item(image=icon.glyph(\uE1EF,default_icon_size)					tip=['E1EF']								cmd=command.copy('\uE1EF'))
	
	item(image=icon.glyph(\uE1F0,default_icon_size)					tip=['E1F0']								cmd=command.copy('\uE1F0')	col) 
	item(image=icon.glyph(\uE1F1,default_icon_size)					tip=['E1F1']								cmd=command.copy('\uE1F1')) 
	item(image=icon.glyph(\uE1F2,default_icon_size)					tip=['E1F2']								cmd=command.copy('\uE1F2')) 
	item(image=icon.glyph(\uE1F3,default_icon_size)					tip=['E1F3']								cmd=command.copy('\uE1F3')) 
	item(image=icon.glyph(\uE1F4,default_icon_size)					tip=['E1F4']								cmd=command.copy('\uE1F4')) 
	item(image=icon.glyph(\uE1F5,default_icon_size)					tip=['E1F5']								cmd=command.copy('\uE1F5')) 
	item(image=icon.glyph(\uE1F6,default_icon_size)					tip=['E1F6']								cmd=command.copy('\uE1F6')) 
	item(image=icon.glyph(\uE1F7,default_icon_size)					tip=['E1F7']								cmd=command.copy('\uE1F7')) 
	item(image=icon.glyph(\uE1F8,default_icon_size)					tip=['E1F8']								cmd=command.copy('\uE1F8')) 
	item(image=icon.glyph(\uE1F9,default_icon_size)					tip=['E1F9']								cmd=command.copy('\uE1F9')) 
	item(image=icon.glyph(\uE1FA,default_icon_size)					tip=['E1FA']								cmd=command.copy('\uE1FA')) 
	item(image=icon.glyph(\uE1FB,default_icon_size)					tip=['E1FB']								cmd=command.copy('\uE1FB')) 
	item(image=icon.glyph(\uE1FC,default_icon_size)					tip=['E1FC']								cmd=command.copy('\uE1FC')) 
	item(image=icon.glyph(\uE1FD,default_icon_size)					tip=['E1FD']								cmd=command.copy('\uE1FD')) 
	item(image=icon.glyph(\uE1FE,default_icon_size)					tip=['E1FE']								cmd=command.copy('\uE1FE')) 
	item(image=icon.glyph(\uE1FF,default_icon_size)					tip=['E1FF']								cmd=command.copy('\uE1FF'))
			
	item(image=icon.glyph(\uE200,default_icon_size)					tip=['E200']								cmd=command.copy('\uE200') col) 
	item(image=icon.glyph(\uE201,default_icon_size)					tip=['E201']								cmd=command.copy('\uE201')) 
	item(image=icon.glyph(\uE202,default_icon_size)					tip=['E202']								cmd=command.copy('\uE202')) 
	item(image=icon.glyph(\uE203,default_icon_size)					tip=['E203']								cmd=command.copy('\uE203')) 
	item(image=icon.glyph(\uE204,default_icon_size)					tip=['E204']								cmd=command.copy('\uE204')) 
	item(image=icon.glyph(\uE205,default_icon_size)					tip=['E205']								cmd=command.copy('\uE205')) 
	item(image=icon.glyph(\uE206,default_icon_size)					tip=['E206']								cmd=command.copy('\uE206')) 
	item(image=icon.glyph(\uE207,default_icon_size)					tip=['E207']								cmd=command.copy('\uE207')) 
	item(image=icon.glyph(\uE208,default_icon_size)					tip=['E208']								cmd=command.copy('\uE208')) 
	item(image=icon.glyph(\uE209,default_icon_size)					tip=['E209']								cmd=command.copy('\uE209')) 
	item(image=icon.glyph(\uE20A,default_icon_size)					tip=['E20A']								cmd=command.copy('\uE20A')) 
	item(image=icon.glyph(\uE20B,default_icon_size)					tip=['E20B']								cmd=command.copy('\uE20B')) 
	item(image=icon.glyph(\uE20C,default_icon_size)					tip=['E20C']								cmd=command.copy('\uE20C')) 
	item(image=icon.glyph(\uE20D,default_icon_size)					tip=['E20D']								cmd=command.copy('\uE20D')) 
	item(image=icon.glyph(\uE20E,default_icon_size)					tip=['E20E']								cmd=command.copy('\uE20E')) 
	item(image=icon.glyph(\uE20F,default_icon_size)					tip=['E20F']								cmd=command.copy('\uE20F'))
			
	item(image=icon.glyph(\uE210,default_icon_size)					tip=['E210']								cmd=command.copy('\uE210')	col) 
	item(image=icon.glyph(\uE211,default_icon_size)					tip=['E211']								cmd=command.copy('\uE211')) 
	item(image=icon.glyph(\uE212,default_icon_size)					tip=['E212']								cmd=command.copy('\uE212')) 
	item(image=icon.glyph(\uE213,default_icon_size)					tip=['E213']								cmd=command.copy('\uE213'))
	item(image=icon.glyph(\uE214,default_icon_size)					tip=['E214']								cmd=command.copy('\uE214'))
	item(image=icon.glyph(\uE215,default_icon_size)					tip=['E215']								cmd=command.copy('\uE215')) 
	item(image=icon.glyph(\uE216,default_icon_size)					tip=['E216']								cmd=command.copy('\uE216')) 
	item(image=icon.glyph(\uE217,default_icon_size)					tip=['E217']								cmd=command.copy('\uE217')) 
	item(image=icon.glyph(\uE218,default_icon_size)					tip=['E218']								cmd=command.copy('\uE218')) 
	item(image=icon.glyph(\uE219,default_icon_size)					tip=['E219']								cmd=command.copy('\uE219')) 
	item(image=icon.glyph(\uE21A,default_icon_size)					tip=['E21A']								cmd=command.copy('\uE21A')) 
	item(image=icon.glyph(\uE21B,default_icon_size)					tip=['E21B']								cmd=command.copy('\uE21B')) 
	item(image=icon.glyph(\uE21C,default_icon_size)					tip=['E21C']								cmd=command.copy('\uE21C')) 
	item(image=icon.glyph(\uE21D,default_icon_size)					tip=['E21D']								cmd=command.copy('\uE21D')) 
	item(image=icon.glyph(\uE21E,default_icon_size)					tip=['E21E']								cmd=command.copy('\uE21E')) 
	item(image=icon.glyph(\uE21F,default_icon_size)					tip=['E21F']								cmd=command.copy('\uE21F'))

	item(image=icon.glyph(\uE220,default_icon_size)					tip=['E220']								cmd=command.copy('\uE220')	col) 
	item(image=icon.glyph(\uE221,default_icon_size)					tip=['E221']								cmd=command.copy('\uE221')) 
	item(image=icon.glyph(\uE222,default_icon_size)					tip=['E222']								cmd=command.copy('\uE222')) 
	item(image=icon.glyph(\uE223,default_icon_size)					tip=['E223']								cmd=command.copy('\uE223'))
	item(image=icon.glyph(\uE224,default_icon_size)					tip=['E224']								cmd=command.copy('\uE224')) 
	item(image=icon.glyph(\uE225,default_icon_size)					tip=['E225']								cmd=command.copy('\uE225')) 
	item(image=icon.glyph(\uE226,default_icon_size)					tip=['E226']								cmd=command.copy('\uE226')) 
	item(image=icon.glyph(\uE227,default_icon_size)					tip=['E227']								cmd=command.copy('\uE227')) 
	item(image=icon.glyph(\uE228,default_icon_size)					tip=['E228']								cmd=command.copy('\uE228')) 
	item(image=icon.glyph(\uE229,default_icon_size)					tip=['E229']								cmd=command.copy('\uE229')) 
	item(image=icon.glyph(\uE22A,default_icon_size)					tip=['E22A']								cmd=command.copy('\uE22A')) 
	item(image=icon.glyph(\uE22B,default_icon_size)					tip=['E22B']								cmd=command.copy('\uE22B')) 
	item(image=icon.glyph(\uE22C,default_icon_size)					tip=['E22C']								cmd=command.copy('\uE22C')) 
	item(image=icon.glyph(\uE22D,default_icon_size)					tip=['E22D']								cmd=command.copy('\uE22D')) 
	item(image=icon.glyph(\uE22E,default_icon_size)					tip=['E22E']								cmd=command.copy('\uE22E')) 
	item(image=icon.glyph(\uE22F,default_icon_size)					tip=['E22F']								cmd=command.copy('\uE22F'))
	
	item(image=icon.glyph(\uE230,default_icon_size)					tip=['E230']								cmd=command.copy('\uE230')	col) 
	item(image=icon.glyph(\uE231,default_icon_size)					tip=['E231']								cmd=command.copy('\uE231')) 
	item(image=icon.glyph(\uE232,default_icon_size)					tip=['E232']								cmd=command.copy('\uE232')) 
	item(image=icon.glyph(\uE233,default_icon_size)					tip=['E233']								cmd=command.copy('\uE233'))
	item(image=icon.glyph(\uE234,default_icon_size)					tip=['E234']								cmd=command.copy('\uE234')) 
	item(image=icon.glyph(\uE235,default_icon_size)					tip=['E235']								cmd=command.copy('\uE235')) 
	item(image=icon.glyph(\uE236,default_icon_size)					tip=['E236']								cmd=command.copy('\uE236')) 
	item(image=icon.glyph(\uE237,default_icon_size)					tip=['E237']								cmd=command.copy('\uE237')) 
	item(image=icon.glyph(\uE238,default_icon_size)					tip=['E238']								cmd=command.copy('\uE238')) 
	item(image=icon.glyph(\uE239,default_icon_size)					tip=['E239']								cmd=command.copy('\uE239')) 
	item(image=icon.glyph(\uE23A,default_icon_size)					tip=['E23A']								cmd=command.copy('\uE23A')) 
	item(image=icon.glyph(\uE23B,default_icon_size)					tip=['E23B']								cmd=command.copy('\uE23B')) 
	item(image=icon.glyph(\uE23C,default_icon_size)					tip=['E23C']								cmd=command.copy('\uE23C')) 
	item(image=icon.glyph(\uE23D,default_icon_size)					tip=['E23D']								cmd=command.copy('\uE23D')) 
	item(image=icon.glyph(\uE23E,default_icon_size)					tip=['E23E']								cmd=command.copy('\uE23E')) 
	item(image=icon.glyph(\uE23F,default_icon_size)					tip=['E23F']								cmd=command.copy('\uE23F'))
		
	item(image=icon.glyph(\uE240,default_icon_size)					tip=['E240']								cmd=command.copy('\uE240')	col) 
	item(image=icon.glyph(\uE241,default_icon_size)					tip=['E241']								cmd=command.copy('\uE241')) 
	item(image=icon.glyph(\uE242,default_icon_size)					tip=['E242']								cmd=command.copy('\uE242')) 
	item(image=icon.glyph(\uE243,default_icon_size)					tip=['E243']								cmd=command.copy('\uE243'))
	item(image=icon.glyph(\uE244,default_icon_size)					tip=['E244']								cmd=command.copy('\uE244')) 
	item(image=icon.glyph(\uE245,default_icon_size)					tip=['E245']								cmd=command.copy('\uE245')) 
	item(image=icon.glyph(\uE246,default_icon_size)					tip=['E246']								cmd=command.copy('\uE246')) 
	item(image=icon.glyph(\uE247,default_icon_size)					tip=['E247']								cmd=command.copy('\uE247')) 
	item(image=icon.glyph(\uE248,default_icon_size)					tip=['E248']								cmd=command.copy('\uE248')) 
	item(image=icon.glyph(\uE249,default_icon_size)					tip=['E249']								cmd=command.copy('\uE249')) 
	item(image=icon.glyph(\uE24A,default_icon_size)					tip=['E24A']								cmd=command.copy('\uE24A')) 
	item(image=icon.glyph(\uE24B,default_icon_size)					tip=['E24B']								cmd=command.copy('\uE24B')) 
	item(image=icon.glyph(\uE24C,default_icon_size)					tip=['E24C']								cmd=command.copy('\uE24C')) 
	item(image=icon.glyph(\uE24D,default_icon_size)					tip=['E24D']								cmd=command.copy('\uE24D')) 
	item(image=icon.glyph(\uE24E,default_icon_size)					tip=['E24E']								cmd=command.copy('\uE24E')) 
	item(image=icon.glyph(\uE24F,default_icon_size)					tip=['E24F']								cmd=command.copy('\uE24F'))
		
	item(image=icon.glyph(\uE250,default_icon_size)					tip=['E250']								cmd=command.copy('\uE250')	col) 
	item(image=icon.glyph(\uE251,default_icon_size)					tip=['E251']								cmd=command.copy('\uE251')) 
	item(image=icon.glyph(\uE252,default_icon_size)					tip=['E252']								cmd=command.copy('\uE252')) 
	item(image=icon.glyph(\uE253,default_icon_size)					tip=['E253']								cmd=command.copy('\uE253')) 
	item(image=icon.glyph(\uE254,default_icon_size)					tip=['E254']								cmd=command.copy('\uE254')) 
	item(image=icon.glyph(\uE255,default_icon_size)					tip=['E255']								cmd=command.copy('\uE255')) 
	item(image=icon.glyph(\uE256,default_icon_size)					tip=['E256']								cmd=command.copy('\uE256')) 
	item(image=icon.glyph(\uE257,default_icon_size)					tip=['E257']								cmd=command.copy('\uE257')) 
	item(image=icon.glyph(\uE258,default_icon_size)					tip=['E258']								cmd=command.copy('\uE258')) 
	item(image=icon.glyph(\uE259,default_icon_size)					tip=['E259']								cmd=command.copy('\uE259')) 
	item(image=icon.glyph(\uE25A,default_icon_size)					tip=['E25A']								cmd=command.copy('\uE25A')) 
	item(image=icon.glyph(\uE25B,default_icon_size)					tip=['E25B']								cmd=command.copy('\uE25B')) 
	item(image=icon.glyph(\uE25C,default_icon_size)					tip=['E25C']								cmd=command.copy('\uE25C')) 
	item(image=icon.glyph(\uE25D,default_icon_size)					tip=['E25D']								cmd=command.copy('\uE25D')) 
	item(image=icon.glyph(\uE25E,default_icon_size)					tip=['E25E']								cmd=command.copy('\uE25E')) 
	item(image=icon.glyph(\uE25F,default_icon_size)					tip=['E25F']								cmd=command.copy('\uE25F'))
			
	item(image=icon.glyph(\uE260,default_icon_size)					tip=['E260']								cmd=command.copy('\uE260')	col) 
	item(image=icon.glyph(\uE261,default_icon_size)					tip=['E261']								cmd=command.copy('\uE261')) 
	item(image=icon.glyph(\uE262,default_icon_size)					tip=['E262']								cmd=command.copy('\uE262')) 
	item(image=icon.glyph(\uE263,default_icon_size)					tip=['E263']								cmd=command.copy('\uE263')) 
	item(image=icon.glyph(\uE264,default_icon_size)					tip=['E264']								cmd=command.copy('\uE264')) 
	item(image=icon.glyph(\uE265,default_icon_size)					tip=['E265']								cmd=command.copy('\uE265')) 
	item(image=icon.glyph(\uE266,default_icon_size)					tip=['E266']								cmd=command.copy('\uE266')) 
	item(image=icon.glyph(\uE267,default_icon_size)					tip=['E267']								cmd=command.copy('\uE267')) 
	item(image=icon.glyph(\uE268,default_icon_size)					tip=['E268']								cmd=command.copy('\uE268')) 
	item(image=icon.glyph(\uE269,default_icon_size)					tip=['E269']								cmd=command.copy('\uE269')) 
	item(image=icon.glyph(\uE26A,default_icon_size)					tip=['E26A']								cmd=command.copy('\uE26A')) 
	item(image=icon.glyph(\uE26B,default_icon_size)					tip=['E26B']								cmd=command.copy('\uE26B')) 
	item(image=icon.glyph(\uE26C,default_icon_size)					tip=['E26C']								cmd=command.copy('\uE26C')) 
	item(image=icon.glyph(\uE26D,default_icon_size)					tip=['E26D']								cmd=command.copy('\uE26D')) 
	item(image=icon.glyph(\uE26E,default_icon_size)					tip=['E26E']								cmd=command.copy('\uE26E')) 
	item(image=icon.glyph(\uE26F,default_icon_size)					tip=['E26F']								cmd=command.copy('\uE26F'))
			
	item(image=icon.glyph(\uE270,default_icon_size)					tip=['E270']								cmd=command.copy('\uE270')	col) 
	item(image=icon.glyph(\uE271,default_icon_size)					tip=['E271']								cmd=command.copy('\uE271')) 
	item(image=icon.glyph(\uE272,default_icon_size)					tip=['E272']								cmd=command.copy('\uE272')) 
	item(image=icon.glyph(\uE273,default_icon_size)					tip=['E273']								cmd=command.copy('\uE273')) 
	item(image=icon.glyph(\uE274,default_icon_size)					tip=['E274']								cmd=command.copy('\uE274')) 
	item(image=icon.glyph(\uE275,default_icon_size)					tip=['E275']								cmd=command.copy('\uE275')) 
	item(image=icon.glyph(\uE276,default_icon_size)					tip=['E276']								cmd=command.copy('\uE276')) 
	item(image=icon.glyph(\uE277,default_icon_size)					tip=['E277']								cmd=command.copy('\uE277')) 
	item(image=icon.glyph(\uE278,default_icon_size)					tip=['E278']								cmd=command.copy('\uE278')) 
	item(image=icon.glyph(\uE279,default_icon_size)					tip=['E279']								cmd=command.copy('\uE279')) 
	item(image=icon.glyph(\uE27A,default_icon_size)					tip=['E27A']								cmd=command.copy('\uE27A')) 
	item(image=icon.glyph(\uE27B,default_icon_size)					tip=['E27B']								cmd=command.copy('\uE27B')) 
	item(image=icon.glyph(\uE27C,default_icon_size)					tip=['E27C']								cmd=command.copy('\uE27C')) 
	item(image=icon.glyph(\uE27D,default_icon_size)					tip=['E27D']								cmd=command.copy('\uE27D')) 
	item(image=icon.glyph(\uE27E,default_icon_size)					tip=['E27E']								cmd=command.copy('\uE27E')) 
	item(image=icon.glyph(\uE27F,default_icon_size)					tip=['E27F']								cmd=command.copy('\uE27F'))
			
	item(image=icon.glyph(\uE280,default_icon_size)					tip=['E280']								cmd=command.copy('\uE280')	col) 
	item(image=icon.glyph(\uE281,default_icon_size)					tip=['E281']								cmd=command.copy('\uE281')) 
	item(image=icon.glyph(\uE282,default_icon_size)					tip=['E282']								cmd=command.copy('\uE282')) 
	item(image=icon.glyph(\uE283,default_icon_size)					tip=['E283']								cmd=command.copy('\uE283')) 
	item(image=icon.glyph(\uE284,default_icon_size)					tip=['E284']								cmd=command.copy('\uE284')) 
	item(image=icon.glyph(\uE285,default_icon_size)					tip=['E285']								cmd=command.copy('\uE285')) 
	item(image=icon.glyph(\uE286,default_icon_size)					tip=['E286']								cmd=command.copy('\uE286')) 
	item(image=icon.glyph(\uE287,default_icon_size)					tip=['E287']								cmd=command.copy('\uE287')) 
	item(image=icon.glyph(\uE288,default_icon_size)					tip=['E288']								cmd=command.copy('\uE288')) 
	item(image=icon.glyph(\uE289,default_icon_size)					tip=['E289']								cmd=command.copy('\uE289')) 
	item(image=icon.glyph(\uE28A,default_icon_size)					tip=['E28A']								cmd=command.copy('\uE28A')) 
	item(image=icon.glyph(\uE28B,default_icon_size)					tip=['E28B']								cmd=command.copy('\uE28B')) 
	item(image=icon.glyph(\uE28C,default_icon_size)					tip=['E28C']								cmd=command.copy('\uE28C')) 
	item(image=icon.glyph(\uE28D,default_icon_size)					tip=['E28D']								cmd=command.copy('\uE28D')) 
	item(image=icon.glyph(\uE28E,default_icon_size)					tip=['E28E']								cmd=command.copy('\uE28E')) 
	item(image=icon.glyph(\uE28F,default_icon_size)					tip=['E28F']								cmd=command.copy('\uE28F'))
	
	item(image=icon.glyph(\uE290,default_icon_size)					tip=['E290']								cmd=command.copy('\uE290')	col) 
	item(image=icon.glyph(\uE291,default_icon_size)					tip=['E291']								cmd=command.copy('\uE291')) 
	item(image=icon.glyph(\uE292,default_icon_size)					tip=['E292']								cmd=command.copy('\uE292')) 
	item(image=icon.glyph(\uE293,default_icon_size)					tip=['E293']								cmd=command.copy('\uE293')) 
	item(image=icon.glyph(\uE294,default_icon_size)					tip=['E294']								cmd=command.copy('\uE294')) 
	item(image=icon.glyph(\uE295,default_icon_size)					tip=['E295']								cmd=command.copy('\uE295')) 
	item(image=icon.glyph(\uE296,default_icon_size)					tip=['E296']								cmd=command.copy('\uE296')) 
	item(image=icon.glyph(\uE297,default_icon_size)					tip=['E297']								cmd=command.copy('\uE297')) 
	item(image=icon.glyph(\uE298,default_icon_size)					tip=['E298']								cmd=command.copy('\uE298')) 
	item(image=icon.glyph(\uE299,default_icon_size)					tip=['E299']								cmd=command.copy('\uE299')) 
	item(image=icon.glyph(\uE29A,default_icon_size)					tip=['E29A']								cmd=command.copy('\uE29A')) 
	item(image=icon.glyph(\uE29B,default_icon_size)					tip=['E29B (Wifi filled). Not working']		cmd=command.copy('\uE29B')) 
	item(image=icon.glyph(\uE29C,default_icon_size)					tip=['E29C (Wifi regular). Not working']	cmd=command.copy('\uE29C')) 
	}
}	