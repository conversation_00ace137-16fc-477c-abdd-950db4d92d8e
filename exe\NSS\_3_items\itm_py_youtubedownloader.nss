
//
$PY_YOUTUBEDOWNLOADER_DIR = '@user.desktop\my\flow\home\__GOTO__\Scripts\Python\Py_Cli_Utils\py__YoutubeDownloader'
$PY_YOUTUBEDOWNLOADER_EXE = '@PY_YOUTUBEDOWNLOADER_DIR\venv\Scripts\python.exe'
$PY_YOUTUBEDOWNLOADER_APP = '@PY_YOUTUBEDOWNLOADER_DIR\main.py'
//

// Context: Explorer
$PY_YOUTUBEDOWNLOADER_EXPLORER = '-op "@sel.dir" --prompt'
item(
    title="&YoutubeDownloader"
    keys="py"
    type='File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    //
    image=[E17C,GREY] image-sel=[E17C,PURPLE]
    tip=['"@PY_YOUTUBEDOWNLOADER_APP" @PY_YOUTUBEDOWNLOADER_EXPLORER',TIP3,0.75]
    //
    admin=keys.rbutton()
    cmd=if(KEYS_EXE_OPEN_EXE,('"@PY_YOUTUBEDOWNLOADER_EXE"'))
    args='"@PY_YOUTUBEDOWNLOADER_APP" @PY_YOUTUBEDOWNLOADER_EXPLORER'
    //
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@PY_YOUTUBEDOWNLOADER_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@PY_YOUTUBEDOWNLOADER_APP')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@PY_YOUTUBEDOWNLOADER_DIR')),
    }
)
// Context: Taskbar
$PY_YOUTUBEDOWNLOADER_TASKBAR = '-op "@user.desktop" --prompt'
item(
    title="&YoutubeDownloader"
    keys="py"
    type='Taskbar'
    //
    image=[E17C,GREY]
    image-sel=[E17C,PURPLE]
    //
    admin=keys.rbutton()
    cmd=if(KEYS_EXE_OPEN_EXE,('"@PY_YOUTUBEDOWNLOADER_EXE"'))
    args='"@PY_YOUTUBEDOWNLOADER_APP" @PY_YOUTUBEDOWNLOADER_TASKBAR'
    tip=['"@PY_YOUTUBEDOWNLOADER_APP" @PY_YOUTUBEDOWNLOADER_TASKBAR',TIP3,0.75]
    //
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@PY_YOUTUBEDOWNLOADER_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@PY_YOUTUBEDOWNLOADER_APP')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@PY_YOUTUBEDOWNLOADER_DIR')),
    }
)
