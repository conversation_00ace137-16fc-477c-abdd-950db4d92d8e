//
item(title='Audio Device' image=[\uE28A, #22A7F2] cmd='mmsys.cpl')
item(title='Subscene' image=[\uE270, #22A7F2] cmd='chrome' args='"https://subscene.com/subtitles/searchbytitle"')
item(title='qBittorrent' image=[\uE0CA, #22A7F2] cmd='cmd.exe' args='START /MAX CMD /C "C:\Program Files\qBittorrent\qbittorrent.exe"')
separator

// menu(where=@(this.count == 0 && isw11) type='taskbar' image=icon.settings expanded=true)
// {
// 	item(title='Audio Output' image=\uE0EC cmd='mmsys.cpl')
// }
// 	// item(title='Audio Output' image=[\uE259, #22A7F2] cmd='mmsys.cpl')
// 	item(title='Audio Output' image=[\uE16F, #22A7F2] cmd='mmsys.cpl')
// 	// item(title='Audio Output' image=[\uE155, #22A7F2] cmd='mmsys.cpl')
// 	// item(title='qBittorrent' image cmd='C:\Program Files\qBittorrent\qbittorrent.exe')
// 	// item(title='qBittorrent' image=[\uE15E, #22A7F2] cmd='C:\Program Files\qBittorrent\qbittorrent.exe')
// 	// item(title='qBittorrent' image=[\uE15E, #22A7F2] cmd='powershell.exe' args='-noexit -command Set-Location -Path "@sel.dir\."')
// 	separator
// }
// 	// item(title=title.windows_powershell admin=@key.shift() tip=tip_run_admin image cmd='powershell.exe' args='-noexit -command Set-Location -Path "@sel.dir\."')
// cmd='powershell.exe' args='"(New-Object -ComObject WScript.Shell).AppActivate((get-process qbittorrent).MainWindowTitle)"')


// powershell -Command "(New-Object -ComObject WScript.Shell).AppActivate((get-process qbittorrent).MainWindowTitle)"


// (New-Object -ComObject WScript.Shell).AppActivate((get-process qbittorrent).MainWindowTitle)