<!-- ============================================================================= -->
<!-- INTRODUCTION -->
<!-- ============================================================================= -->
Title: Introduction - Shell

URL Source: https://nilesoft.org/docs

Markdown Content:
#### Introduction

##### What is Shell?

**Shell** is a extensions of Windows File Explorer that can be used to create high-performance context menu items. And gives user a high level of control over context menu of Windows File Explorer.

The Right Click Menu or the Context Menu is the menu, which appears when you right-click on the desktop, file, folder or taskbar in Windows. This menu gives you added functionality by offering you actions you can take with the item.

**Shell** is all you need to customize or add new items with several functions to Windows File Explorer Context menu and Much More.(cascade menus, advanced menus, multi-level menus, command menus, separator).

![Image 1: a black screen with a number of items on it](https://nilesoft.org/docs/images/dark/goto.png)

##### Why Use Shell

*   Is portable, fun and easy to learn!
*   Configuration in plain text.
*   Quick loading.
*   Minimal resource usage.
*   No limitations.
*   Embedded expressions syntax.
*   Built-in functions and predefined variables.
*   Multiple sources of images (embedded icons, image files, svg, glyphs, and colors).
*   Dynamic search and filter.
*   Support Taskbar context menu.
*   Full management of the context menu.

* * *

<!-- ============================================================================= -->
<!-- INSTALLATION -->
<!-- ============================================================================= -->
Title: Installation - Shell

URL Source: https://nilesoft.org/docs/installation

Markdown Content:
#### Installation

Shell can be installed in the following ways:

*   [Installer](#installer)
*   [Portable](#portable)
*   [Windows Package Manager](#winget)
*   [Scoop](#scoop)
*   [Chocolatey](#chocolatey)

Please also note the following reference sections:

*   [Command-Line Help](#cli)
*   [Keys to enable/disable Shell functionalities](#keys)

##### Installer version

##### Install

[Download](https://nilesoft.org/download) the installation file (Installer/Portable), run `setup.exe`, follow the installation steps, and agree to restart Windows File Explorer.

The program will be installed to `C:\Program Files\Nilesoft Shell\`, unless you have chosen a different installation path during the installation steps.

* * *

##### Uninstall

Inside the Shell program folder (`C:\Program Files\Nilesoft Shell\` by default), run the `unst000.exe` or `unstall.exe` file, then follow the steps, and then agree to restart Windows File Explorer.

##### Portable version

##### Install

[Download](https://nilesoft.org/download) the installation file (Installer/Portable), run `setup.exe`, click Next, chose the folder you want the program to be extracted to, **Check the option Portable Mode**, and click Extract.

Open an **elevated command prompt** (or use [gsudo](https://github.com/gerardog/gsudo)), change to the directory you have extracted the program to, and run

Administrator: Command Prompt

shell -register -restart

For further details see the chapter [Command-Line Help](#cli) below.

* * *

##### Uninstall

Open an **elevated command prompt** (or use [gsudo](https://github.com/gerardog/gsudo)), change to the directory you have extracted the program to, and run

Administrator: Command Prompt

shell -unregister -restart

_Tip:_ Close all instances of Windows File Explorer before uninstalling to avoid needing a reboot after it (Shell) has been uninstalled.

For further details see the chapter [Command-Line Help](#cli) below.

##### Windows Package Manager

Windows Package Manager is available from Windows 10 version 1809.

##### Install

Open a command prompt, and run

COMMAND PROMPT

winget install Nilesoft.Shell

The program will be installed to `C:\Program Files\Nilesoft Shell\`.

* * *

##### Uninstall

Open a command prompt, and run

COMMAND PROMPT

winget uninstall Nilesoft.Shell

##### Scoop

Scoop can be installed following the instructions at [scoop.sh](https://scoop.sh/).

##### Install

Open a command prompt, and run

COMMAND PROMPT

scoop install nilesoft-shell

The program will be installed to `%USERPROFILE%\scoop\apps\nilesoft-shell\current\`.

_Tip:_ If you get the error `Couldn't find manifest for 'nilesoft-shell'`, please run first

COMMAND PROMPT

scoop bucket add extras

Open an **elevated command prompt** (or use [gsudo](https://github.com/gerardog/gsudo)), and run

Administrator: Command Prompt

shell -register -restart

For further details see the chapter [Command-Line Help](#cli) below.

* * *

##### Uninstall

Open an **elevated command prompt** (or use [gsudo](https://github.com/gerardog/gsudo)), and run

Administrator: Command Prompt

shell -unregister -restart

Then run

Administrator: Command Prompt

scoop uninstall nilesoft-shell

##### Chocolatey

Chocolatey can be installed following the instructions at [chocolatey.org](https://chocolatey.org/install#individual).

##### Install

_Notice:_ Until the package has been moderated, you need to add the specific version string to the installer, adding e.g.

COMMAND PROMPT

choco install nilesoft.shell --version=1.8.1

Please find the latest published version at [Chocolatey's Version History](https://community.chocolatey.org/packages/nilesoft.shell#versionhistory) and use the latest version number (replacing `1.8.1`).

Open an **elevated command prompt** (or use [gsudo](https://github.com/gerardog/gsudo)), and run

COMMAND PROMPT

choco install nilesoft.shell

The program will be installed to `C:\Program Files\Nilesoft Shell\`.

* * *

##### Uninstall

Open an **elevated command prompt** (or use [gsudo](https://github.com/gerardog/gsudo)), and run

COMMAND PROMPT

choco uninstall nilesoft.shell

##### Command-Line Help

`shell -[options]`

##### Options

<table><tbody><tr><td>-register (-r)</td><td>Registering.</td></tr><tr><td>-unregister (-u)</td><td>Unregistering.</td></tr><tr><td>-restart (-re)</td><td>Restart Windows Explorer.</td></tr><tr><td>-treat</td><td>Disable Windows 11 context menu.</td></tr><tr><td>-silent (-s)</td><td>Prevents displaying messages.</td></tr><tr><td>-?</td><td>Display this help message.</td></tr></tbody></table>

* * *

##### Use the following keys to enable/disable

These keys are used when you press right-click or press Shift+F10 keys

CTRL To enable and reload the configuration file `shell.nss`
WIN To make priority to the modern context menu for Windows 11
CTRL+WIN To disable Shell and Enable Classic Context Menu
RIGHT-CLICK+LEFT-CLICK To reload the configuration file `shell.nss`

* * *

<!-- ============================================================================= -->
<!-- GET STARTED -->
<!-- ============================================================================= -->
Title: Get Started - Shell

URL Source: https://nilesoft.org/docs/get-started

Markdown Content:
#### Get Started

This tutorial will teach you the basics of **Shell**.
It is not necessary to have any prior experience.

To start using **Shell**, you need:
A text editor, like Notepad, to write **Shell** code.

##### Quickstart

Let's create our first menu item.

Open the configuration file "shell.nss" and Write the following code and save.

item(title='Hello, World!' cmd=msg('Hello @user.name'))

Don't worry if you don't understand the code above - we will discuss it in detail in later chapters.

The result will look something for this when you press the right-click in an empty place on the desktop:

![Image 1: the display settings menu in adobe adobe adobe adobe adobe](https://nilesoft.org/docs/images/helloworld.png)

**Congratulations** You have now added the first time a menu item to the context menu

* * *

<!-- ============================================================================= -->
<!-- CONFIGURATION -->
<!-- ============================================================================= -->
Title: Configuration - Shell

URL Source: https://nilesoft.org/docs/configuration

Markdown Content:
#### Syntax Rules for Configuration Files

This chapter describes the syntax rules for the configuration files.

The main configuration file [`shell.nss`](#shell.nss) is located in the installation directory of **Shell**, depending on your [installation method](https://nilesoft.org/docs/installation).

##### General rules

*   Syntax is case-insensitive.
*   Spaces around the equal (`=`) sign are optional and are ignored.
*   The properties of [modify-items](https://nilesoft.org/docs/configuration/modify-items) and [new-items](https://nilesoft.org/docs/configuration/new-items) items are separated by blank spaces or on a [separate line](#breaking-long-lines) and must be placed in parentheses `( )`.
*   Other configuration files can be imported using the [import tag](#import).

_Tip:_ When there is an error, it is recorded in the log file (`shell.log`, which is also located in your [installation directory](https://nilesoft.org/docs/installation).).

##### shell.nss structure

The global section `shell{}` may have the following subsections:

*   Section [settings{}](https://nilesoft.org/docs/configuration/settings). Optional.
*   Section [modify-items](https://nilesoft.org/docs/configuration/modify-items) with instructions on how to **change existing menuitems**. Optional.
*   modify-items are only of 2 type: modify and remove.
*   Section [modify-items](https://nilesoft.org/docs/configuration/new-items) with definitions for **new [menuitems](https://nilesoft.org/docs/configuration/new-items#menuitem)**. Optional.
*   Dynamic [menuitems](https://nilesoft.org/docs/configuration/new-items#menuitem) may have one of three types: [menu](https://nilesoft.org/docs/configuration/new-items#menu), [item](https://nilesoft.org/docs/configuration/new-items#item), or [separator (sep)](https://nilesoft.org/docs/configuration/new-items#separator).

##### Example

// variable declaration
$variable-name = variable-value

//image declaration
@image-id = image-value


`settings {key-name = key-value key-name = [key-value, key-value, ...] ... } theme {key-name = key-value ... } // modify items modify ( property-name = property-value ... ) remove ( property-name = property-value ... ) // new items item ( property-name = property-value ... ) separator [( property-name = property-value ... )] menu ( property-name = property-value ... ) { $variable-name = variable-value item ( property-name = property-value ... ) ... }`

#### Breaking Long Lines For best readability, users often like to avoid lines longer than 80 characters. single quotes also allow break up a line. item(title='Command prompt' cmd='cmd.exe') #### Import tag To better organise the configuration file, parts of the configuration can be saved in separate files. These are then imported using the import tag. With this method, it is also possible to import the same file as a sort of "module" into different parts of the configuration. A convenient way to include the same sub-menu in different locations.. ##### Syntax The general syntax is as follows: import %path% Where * `%section%` is the name of a section. Optional. If given, it must be one of * settings * themes * modify-items * new-itemsThe section name is written literally, without any quotes (or the percent signs). * `%path%` is a [string](https://nilesoft.org/docs/expressions/string) literal, that returns the path to the config file that shall be imported. This can be a relative path to the location of the file where the import tag is used, or it can be an absolute path. Expressions are supported when using [single quotes](https://nilesoft.org/docs/expressions/string#single-quotes).

There are effectively two different ways this tag is applied, depending on whether the optional `%section%` is given:

*   Import an entire section
*   Import as a partial:

##### Import an entire section

// import an entire section
import %path%

In this case, the content of the file found at `%path%` will be imported into **a newly created** `section{}`. The result would then look like so:

// import an entire section
section {
/* content of the imported file goes here! Do not include
*
* section {
* }
*
* in your imported file!
*/
}

This syntax may be used only in the following places:

*   root section shell{}: `shell import %path%`
*   the global sections
*   settings{}: `import %path%`
*   sub-sections of the settings{} section:
*   theme.background{}: `background import %path%`
*   theme.item{}: `item import %path%`
*   theme.border{}: `border import %path%`
*   ...
*   settings.tip{}: `tip import %path%`
*   settings.exclude{}: `exclude import %path%`
*   settings.modify{}: `static import %path%`
*   settings.new{}: `dynamic import %path%`

##### Import as a partial

section {
// some code might go here. Optional.

// import of a partial section
import %path%

// some more content might go here. Optional.
}

In this case, the content of the file found at `%path%` will be imported into the **already existing** `section{}`. The result would then look like so:

section {
// some code might go here. Optional.

// import of a partial section
/* content of the imported file goes here! Do not include
*
* section {
* }
*
* in your imported file!
*/

// some more content might go here. Optional.
}

This syntax may be used nearly anywhere:

*   in any section
*   in the body of [menu tags](https://nilesoft.org/docs/configuration/new-items#menu)

* * *
<!-- ============================================================================= -->
<!-- SETTINGS -->
<!-- ============================================================================= -->
Title: Settings - Shell

URL Source: https://nilesoft.org/docs/configuration/settings

Markdown Content:
#### Settings

Settings are containers for storing default values.
```JS
settings
{
    // show menu delay value from 0 to 4000
    showdelay = 200
    
    // Prevent interaction with these windows or processes
    exclude
    {
        where = boolean value
        window = window name
        process = process name
    }
    
    tip = true
    // or
    tip
    {
        enabled = true
        
        // normal = [background, text]
        normal = [default, default]
        
        // normal = [background, text]
        primary = [#000, #fff]
        
        // info = [background, text]
        info = [#88f, #fff]
        
        // success = [background, text]
        success = [#8f8, #fff]
        
        // warning = [background, text]
        warning = [#ff8, #fff]
        
        // danger = [background, text]
        danger = [#f88, #fff]
        
        // max width value from 200 to 2000
        width = 400
        
        // opacity value from 0 to 100
        opacity = 100
        
        // radius size value from 0 to 3
        radius = 1
        
        time = 1.5
        
        padding = [8, 4]
    }
    
    // Disable/Enable modify items processing
    modify
    {
        enabled = boolean value
        image = [0 = disable, 1 = enable, 2 = auto reimage]
        
        // Allow/disallow modification of title
        title = boolean value
        
        // Allow/disallow modification of visibility
        visibility = boolean value
        
        // Allow/disallow modification of parent
        parent = boolean value
        
        // Allow/disallow modification of position
        position = boolean value
        
        // Allow/disallow to add separator
        separator = boolean value
        
        // auto set image and group
        auto = boolean value
    }
    
    // Disable/Enable new items processing
    new
    {
        enabled = boolean value
        // disable/enable image
        image = boolean value
    }
}
```
* * *

<!-- ============================================================================= -->
<!-- THEMES -->
<!-- ============================================================================= -->
Title: Themes - Shell

URL Source: https://nilesoft.org/docs/configuration/themes

Markdown Content:
#### Themes

Theme section to customize the layout and colors of the context menu.

```JS
theme
{
    // theme.name = auto, classic, white, black, or modern
    name = "modern"
    
    // view = auto, compact, small, medium, large, wide
    view = view.compact
    
    dark = true or false
    
    background
    {
        color = color value
        opacity = value from 0 to 100
        
        // effect value 0 = disable, 1 = transparent, 2 = blur, 3 = acrylic
        effect = auto
        
        // for acrylic
        effect = [3, tint color, opacity]
        
        gradient
        {
            enabled = boolean value
            
            // linear = [x1, x2, y1, y2]
            linear = [0, 100, 0, 0]
            
            // or radial = [cx, cy, r, fx, fy]
            radial =[ 100, 100, 150, 100, 100]
            
            // stop = [offset, stop-color]
            stop = [0.5, color.accent, 20]
            
            // or add more stop
            stop = [
                [offset, stop-color],
                [offset, stop-color],
                [offset, stop-color]
            ]
        }
    }
    
    item
    {
        opacity = value from 0 to 100
        radius = value from 0 to 3
        
        // prefix value [auto, 0 = dont display,  1 = display, 2 = ignore]
        prefix = 1
        
        text
        {
            normal = color
            normal.disabled = color
            select = color
            select.disabled = color
        }
        
        back
        {
            normal = color
            normal.disabled = color
            select = color
            select.disabled = color
        }
        
        border
        {
            normal = color
            normal.disabled = color
            select = color
            select.disabled = color
        }
        
        padding
        {
            left = value
            top = value
            right = value
            bottom = value
        }
        
        margin
        {
            left = value
            top = value
            right = value
            bottom = value
        }
    }
    
    border
    {
        enabled = boolean value
        size = value from 0 to 10
        color =  = value
        opacity = value
        radius = value
        
        padding
        {
            left = value
            top = value
            right = value
            bottom = value
        }
    }
    
    shadow
    {
        enabled = boolean value
        size = value from 0 to 30
        color = value
        opacity = value from 0 to 100
        offset = value from 0 to 30
    }
    
    font
    {
        size = value start from 6
        name = "tahoma"
        weight = value from 1 to 9
        italic = 0
    }
    
    separator
    {
        size = value form 0 to 40
        color = value
        opacity = value
        
        margin
        {
            left = value
            top = value
            right = value
            bottom = value
        }
    }
    
    symbol
    {
        normal = color
        normal.disabled = color
        select = color
        select.disabled = color
        
        // or
        chevron
        {
            normal = color
            normal.disabled = color
            select = color
            select.disabled = color
        }
        
        checkmark
        {
            normal = color
            normal.disabled = color
            select = color
            select.disabled = color
        }
        
        bullet
        {
            normal = color
            normal.disabled = color
            select = color
            select.disabled = color
        }
    }
    
    image
    {
        enabled = boolean value
        
        color = [color1, color2, color3]
        gap = value
        glyph = "font name" // font name for default glyph
        scale = boolean value
        align = value [0 = Display only the check mark, 1 = Display only the image, 2 = Display the image and check mark together]
    }
    
    layout
    {
        // Right-to-left layout display for Middle Eastern languages
        rtl = boolean value
        
        // Align submenus
        popup = value from -20 to 20
    }
}
```

### Padding and Margin value syntax

Use the padding shorthand property with four values:

`padding = [1, 2, 3, 4]`

left = 1
right = 2
top = 3
bottom = 4

Use the padding shorthand property with two values:

`padding = [4, 2]`

left = 4
right = 4
top = 2
bottom = 2

Use the padding shorthand property with one value:

`padding = 4`

left = 4
right = 4
top = 4
bottom = 4

* * *

<!-- ============================================================================= -->
<!-- MODIFY ITEMS -->
<!-- ============================================================================= -->
Title: Modify items - Shell

URL Source: https://nilesoft.org/docs/configuration/modify-items

Markdown Content:
#### Modify Items

The optional `modify` section contains entries to **modify existing context menu items**, added by the system or by a third party.

##### Sub-items

The section can have the following entry types, all of which are **optional**:

*   One or more [`item` entries](#item). These contain the instructions on which and how to change existing [menuitems](https://nilesoft.org/docs/configuration/new-items#menuitem).
*   One or more [`` `imports` ``](https://nilesoft.org/docs/configuration#import). The content of the given file will be placed in the position of the import.

##### Example

In the following example, two instructions are defined:

modify(find = 'copy' image = #00ff00)
modify(find = 'paste' image = #0000ff)
remove(find = '"format"')

#### Item Entries

`item` entries contain the instructions on how to identify existing [menuitems](https://nilesoft.org/docs/configuration/new-items#menuitem) (also referred to as [Target](#target)), and when and what changes should be applied to them.

This is done by matching an existing [menuitem](https://nilesoft.org/docs/configuration/new-items#menuitem)'s [`title`](https://nilesoft.org/docs/configuration/properties#title) property against the modify item's mandatory [`find`](https://nilesoft.org/docs/configuration/properties#find) property. If a match is found, the other properties of the modify `item` are applied to the appropriate [menuitem](https://nilesoft.org/docs/configuration/new-items#menuitem), such as changing their properties (e.g. [`title`](https://nilesoft.org/docs/configuration/properties#title), [`icon`](https://nilesoft.org/docs/configuration/properties#image), [`visibility`](https://nilesoft.org/docs/configuration/properties#visibility)), or moving them to another location.

##### Syntax

modify( find = value [property = value [...] ]) ##### Properties `item` entries can define three different sets of properties: [Validation Properties](https://nilesoft.org/docs/configuration/properties#_validation-properties)

Determine if a given `item` entry should be processed when a context menu is displayed. Optional.

[Filter Properties:](https://nilesoft.org/docs/configuration/properties#_filter-properties)

Determine if a given menuitem is a valid [target](#target) for the [process instructions](#process-instructions)

*   [`find`](https://nilesoft.org/docs/configuration/properties#find) **(mandatory)**
Pattern used to identify [targets](#target) by matching against their [`title`](https://nilesoft.org/docs/configuration/properties#title) property.

[Process Instructions](#process-instructions)

Define what to do with the target. Optional. However, if there are no [below](#process-instructions>processinstruction</a>specified,theentryisofnopracticaluse.Forfurtherdetailsrefertoitsseparatesection<ahref=).

For a complete overview and further details regarding applicable properties, please refer to the [properties page](https://nilesoft.org/docs/configuration/properties).

#### Item Target

Item targets are [menuitems](https://nilesoft.org/docs/configuration/new-items#menuitem) of an existing context menu. Their properties or location can be changed by applying the [process instructions](#process-instructions) defined in a [modify `item`](#item").

##### Process instructions

Instructions that should be applied to the [Target](#target). Basically they consist of properties from the two property classes [menuitem properties](https://nilesoft.org/docs/configuration/properties#_menuitem-properties) and [command properties](https://nilesoft.org/docs/configuration/properties#_command-properties).

Once a `item` is validated and a target identified, then these values are applied to the targeted [menuitem](https://nilesoft.org/docs/configuration/new-items#menuitem).

* * *

<!-- ============================================================================= -->
<!-- NEW ITEMS -->
<!-- ============================================================================= -->
Title: New items - Shell

URL Source: https://nilesoft.org/docs/configuration/new-items

Markdown Content:
#### New Items

Add **new items** to the context menu.

##### Sub-items

The section can have the following entry types, all of which are **optional**:

*   Menuitems, i.e. one or more of the following:
*   One or more [`item` entries](#item). These appear as top-level items in a context menu.
*   One or more [`menu` entries](#menu). These appear as top-level sub-menus in a context menu.
*   One or more [`separator` entries](#separator). These create a horizontal line between the given entries.
*   One or more [`` `imports` ``](https://nilesoft.org/docs/configuration#import). The content of the given file will be placed in the position of the import.

##### Example

In the following example, one top-level [`item`](#item) is created, that is separated with a [horizontal line](#separator) from an adjacent sub-[menu](#menu), which in turn has one sub-[item](#item) on its own:

item(title = 'Hello, World!')
separator
menu(title = 'sub menu' image = #0000ff)
{
item(title = 'test sub-item')
}

menuitem is an umbrella term for those entry types, that may appear in a menu. These simply include the following:

*   [`item`](#item)
*   [`menu`](#menu)
*   [`separator`](#separator)

#### Items

items create a single menu entry.

##### Properties

Either a [`title`](https://nilesoft.org/docs/configuration/properties#title) or a [`image`](https://nilesoft.org/docs/configuration/properties#image) property is mandatory (set to a non-null value). For further details, please refer to the [properties page](https://nilesoft.org/docs/configuration/properties).

##### Syntax

item( title = value [property = value [...] ]) menu entries create a new **sub-menu**. They have properties and sub-entries. Either a [`title`](https://nilesoft.org/docs/configuration/properties#title) or a [`image`](https://nilesoft.org/docs/configuration/properties#image) property is mandatory (set to a non-null value). For further details, please refer to the [properties page](https://nilesoft.org/docs/configuration/properties).

menus can have the following entry types, all of which are **optional**:

*   Menuitems, i.e. one or more of the following:
*   One or more [`item` entries](#item).
*   One or more sub-[`menu` entries](#menu).
*   One or more [`separator` entries](#separator). These create a horizontal line between the given entries.
*   One or more [`` `imports` ``](https://nilesoft.org/docs/configuration#import). The content of the given file will be placed in the position of the import.

menu( title = value [property = value [...] ]) { [ item() [...] ] [ menu(){} [...] ] [ separator [...] ] [ import 'path/to/import.nss' [...] ] } #### Separators separators create a horizontal line. ##### Properties separators do not have any mandatory properties. Please refer to the [properties page](https://nilesoft.org/docs/configuration/properties) for further details.

##### Syntax

separator
separator( property = value [property = value [...] ])

* * *

<!-- ============================================================================= -->
<!-- PROPERTIES -->
<!-- ============================================================================= -->
Title: Properties - Shell

URL Source: https://nilesoft.org/docs/configuration/properties

Markdown Content:
#### Properties

Shell supports the following properties classes:

*   [Validation Properties](#_validation-properties)
*   [Filter Properties](#_filter-properties)
*   [Menuitem Properties](#_menuitem-properties)
*   [Command Properties](#_command-properties)

Please also see the full index of available properties [below](#_index).

##### Index

*   *   [Admin](#admin)
*   [arg](#arguments)
*   [args](#arguments)
*   [Arguments](#arguments)
*   *   [Checked](#checked)
*   [cmd](#command)
*   [col](#column)
*   [Column](#column)
*   [Command](#command)
*   *   [Default](#default)
*   [dir](#directory)
*   [Directory](#directory)
*   *   [Expanded](#expanded)
*   *   [Find](#find)
*   *   [Icon](#image)
*   [Image](#image)
*   [Invoke](#invoke)
*   *   [Keys](#keys)
*   *   [Mode](#mode)
*   [Menu](#parent)
*   *   [Parent](#parent)
*   [pos](#position)
*   [Position](#position)
*   *   [sep](#separator)
*   [Separator](#separator)
*   *   [Tip](#tip)
*   [Title](#title)
*   [Type](#type)
*   *   [Verb](#verb)
*   [vis](#visibility)
*   [Visibility](#visibility)
*   *   [Wait](#wait)
*   [Where](#where)
*   [Window](#window)

#### Syntax

##### Entry types

In the following tables, the Types column shows to which entry types the property applies to.

The following abbreviations are used (if set in bold, then the property is mandatory for the given type):

mi

[modify item](https://nilesoft.org/docs/configuration/modify-items#item), i.e. the item entry itself. Is basically required to evaluate if the process instructions are applied to any given target.

mt

[modify target](https://nilesoft.org/docs/configuration/modify-items#target), i.e. the menuitem of the existing menu to which the process instructions are applied

nm

[new menu type](https://nilesoft.org/docs/configuration/new-items#menu)

ni

[new item type](https://nilesoft.org/docs/configuration/new-items#item)

ns

[new separator type](https://nilesoft.org/docs/configuration/new-items#separator).

##### Validation Properties

Determine if a given [Modify items](https://nilesoft.org/docs/configuration/modify-items) or [New items](https://nilesoft.org/docs/configuration/new-items) entry should be processed when a context menu is displayed.

*   [Mode](#mode)
*   [Type](#type)
*   [Where](#where)

###### Syntax

| Property | Types[(\*)](#_entry-types) | Summary |
| --- | --- | --- |
| Where | mi, nm, ni, ns | Process given menuitem if `true` is returned. Allows the **evaluation of arbitrary [expressions](https://nilesoft.org/docs/expressions)**, e.g. [`if()`](https://nilesoft.org/docs/functions#if).
Default = true |
| Mode | mi, nm, ni, ns | Display menuitem by **type of selection**. The value has one of the following parameters (of type [string](https://nilesoft.org/docs/configuration/modify-items)):
<table><tbody><tr><td>none</td><td>Display menuitem when there is no selection.</td></tr><tr><td>single</td><td>Display menuitem when there is a single object selected.</td></tr><tr><td>multi_unique</td><td>Display menuitem when multiple objects of the same <a href="#type">type</a> are selected.</td></tr><tr><td>multi_single</td><td>Display menuitem when multiple files with a single file extension are selected.</td></tr><tr><td>multiple</td><td>Display any type of selection, unless there is none.</td></tr></tbody></table>

Default = single |
| Type | mi, nm, ni, ns | Specifies the **types of objects** for which the menuitem will be displayed.
Possible values are shown below. Separate multiple types with the pipe character (`|`), in which case the menuitem is displayed if any of the given types is matched.
To exclude a given type, prefix its value with the tilde character (`~`).

Expressions are not supported with this property.

<table><tbody><tr id="type-asterisks"><td>*</td><td>Display menuitem when any type is selected.</td></tr><tr id="type-file"><td>File</td><td>Display menuitem when files are selected.</td></tr><tr id="type-directory"><td>Directory(Dir)</td><td>Display menuitem when directories are selected.</td></tr><tr id="type-drive"><td>Drive</td><td>Display menuitem when drives are selected.</td></tr><tr id="type-usb"><td>USB</td><td>Display menuitem when USB flash-drives are selected.</td></tr><tr id="type-dvd"><td>DVD</td><td>Display menuitem when DVD-ROM drives are selected.</td></tr><tr id="type-fixed"><td>Fixed</td><td>Display menuitem when fixed drives are selected. Such drives have a fixed media; for example, a hard disk drive or flash drive.</td></tr><tr id="type-vhd"><td>VHD</td><td>Display menuitem when Virtual Hard Disks are selected.</td></tr><tr id="type-removable"><td>Removable</td><td>Display menuitem when the selected drives have removable media; for example, a floppy drive, thumb drive, or flash card reader.</td></tr><tr id="type-remote"><td>Remote</td><td>Display menuitem when the selected remote (network) drives are selected.</td></tr><tr id="type-back"><td>Back</td><td>Display menuitem when the background of all types are selected (<code>back</code>). Or specify one of the following more granular types for the background:<ul><li><code>back.directory</code></li><li><code>back.drive</code>, including<ul><li><code>back.fixed</code></li><li><code>back.usb</code></li><li><code>back.dvd</code></li><li><code>back.vhd</code></li><li><code>back.Removable</code></li></ul></li><li><code>back.namespace</code>, including<ul><li><code>back.computer</code></li><li><code>back.recyclebin</code></li></ul></li></ul></td></tr><tr id="type-desktop"><td>Desktop</td><td>Display menuitem when the Desktop is selected.</td></tr><tr id="type-namespace"><td>Namespace</td><td>Display menuitem when Namespaces are selected. Can be virtual objects such as My Network Places and Recycle Bin.</td></tr><tr id="type-computer"><td>Computer</td><td>Display menuitem when My Computer is selected.</td></tr><tr id="type-recyclebin"><td>Recyclebin</td><td>Display menuitem when the Recycle bin is selected.</td></tr><tr id="type-taskbar"><td>Taskbar</td><td>Display menuitem when the Taskbar is selected.</td></tr></tbody></table>

Default = Accepts all types, except for the Taskbar. |

##### Filter Properties

For [Modify items](https://nilesoft.org/docs/configuration/modify-items) entries only, filter properties determine if a given menuitem is a valid [target](https://nilesoft.org/docs/configuration/modify-items#target) for the [process instructions](https://nilesoft.org/docs/configuration/modify-items#process-instructions)

*   [Find](#find)

###### Syntax

| Property | Types[(\*)](#_entry-types) | Summary |
| --- | --- | --- |
| Find | nm, ni, ns |
For modify items (required)

Apply the current item's process instructions to any existing menuitem if their [`title`](#title) property matches the pattern of the current item's `find` property.

For dynamic items (optional)

Display the current menuitem if the pattern of its `find` property matches the path name or path extension of the **selected files**.

Default = null, which means any string is "matched".

Syntax

find = '%pattern%'
find = '%pattern%|%pattern%[...]' where **%pattern%** can be one or more matching instructions (see Examples below). The following characters do have special meaning: * `|` **Use to separate patterns.** If any one pattern matches, the property yields true. * `*` **Matches any number of characters.** Is used as a wildcard to match only the beginning or the end of the entire string (or word, if used in combination with the exclamation mark `!`). * `!` **Negates the match** of the current pattern, or **limits the wildcard (`*`)** to one word only. * `""` the enclosed string is treated as a **word**. A **word** is a sequence of alphanumerical characters that is confined to the left and to the right by either a space , a non-word character (e.g. `/` or `-`), or the beginning or the end of the entire string, respectively. Examples | Pattern | Matches any string that ... | Would match | Would not match | | --- | --- | --- | --- | | `'foo'` | contains the literal string `foo` anywhere. | `foo`, `foobar`, `afoobar` | `fo`, `f oo`, `bar` | | `'"foo"'` | contains the literal string `foo` as a whole word only. | `foo`, `foo/bar`, `some foo bar` | `foobar`, `foofoo`, `bar` | | `'*foo'` | ends with the literal string `foo`. | `foo`, `barfoo`, `bar/foo` | `foobar`, `fooo`, `foo` | | `'foo*'` | starts with the literal string `foo`. | `foo`, `foobar`, `foo/bar` | `foobar`, `fo`, `yeti` | | `'!foo'` | does not contain the literal string `foo` anywhere. | `fobar`, `fo`, `kung-fu` | `foo`, `foobar`, `barfoo/bar` | | `'!"foo"'` | does not contain the word `foo` | `fobar`, `kung fu bar`, `foobar` | `foo`, `kung foo bar`, `bar/foo/bar` | | `'!*foo'` | does not contain a word ending on `foo` | `foobar`, `fooo-fo` | `foo`, `foo bar`, `bar/foo` | | `'foo*!'` | does not contain a word starting with `foo` | `myFooBar`, `barFoo` | `foo`, `foobar`, `fo-fooo` | | For dynamic items the following syntax allows to match against file extensions: | | Pattern | Matches any file extension ... | Would match | Would not match | | --- | --- | --- | --- | | `'.exe'` | equal to `.exe` | `setup.exe`, `notepad.exe` | `install.bat`, `shell.nss`, `shell.ex_`, file without an extension. | | `'!.exe'` | not equal to `.exe` | `setup.exe.zip`, `video.mp4`, `shell.ex_`, file without an extension. | `setup.exe`, `shell.exe` | | `'.exe|.dll'` | equal to either `.exe` or `.dll` | `shell.exe`, `shell.dll` | `shell.zip`, `shell.nss`, file without an extension. | | This set of properties describe the appearance and location of a given menuitem. For modify-items, this is the target menuitem. For dynamic entries, this is the newly created menuitem. Appearance * [Checked](#checked)
*   [Default](#default)
*   [Image](#image)
*   [Separator](#separator)
*   [Tip](#tip)
*   [Title](#title)
*   [Visibility](#visibility)

Location

*   [Column](#column)
*   [Expanded](#expanded)
*   [Keys](#keys)
*   [Menu](#parent)
*   [Parent](#parent)
*   [Position](#position)

##### Command Properties

This set of properties describe how a command is executed. Only available for dynamic items.

*   [Admin](#admin)
*   [Arguments](#arguments)
*   [Command](#command)
*   [Directory](#directory)
*   [Invoke](#invoke)
*   [Verb](#verb)
*   [Wait](#wait)
*   [Window](#window)

###### Syntax

| Property | Types[(\*)](#_entry-types) | Summary |
| --- | --- | --- |
| Command (cmd) | ni |
The **command** associated with the menuitem. Occurs when the menuitem is clicked or selected using a shortcut key or access key defined for the menuitem.

Default = null |
| Arguments (arg, args) | ni |

The **command line parameters** to pass to the [command](#command") property of a menuitem.

Default = null |
| Invoke | ni | Set **execution type**

<table><tbody><tr id="invoke-single"><td>0, single</td><td>execute the <a href="#command">command</a> only once in total. The list of selected items can be accessed with <a href="https://nilesoft.org/docs/functions/sel#sel"><code>@sel</code></a></td></tr><tr id="invoke-multiple"><td>1, multiple</td><td>execute the <a href="#command"><code>command</code></a> once for every single item in the current selection. The currently processed item can be accessed with e.g <a href="https://nilesoft.org/docs/functions/sel#sel.path.quote"><code>@sel.path.quote</code></a></td></tr></tbody></table>

Default = 0 |
| Window | ni | Controls how the **window** of the executed [command](#command) is to be shown. Can be one of the following parameters:

`Hidden`, `Show`, `Visible`, `Minimized`, `Maximized`

Default = show |
| Directory (dir) | ni |

Specifies the **working directory** to execute the [command](#command) in.

Default = null |
| Admin | ni |

Execute the [command](#command) with **administrative permissions**.

Default = false |
| Verb | ni | Specifies the **default operation** for the selected file. Value type [string](https://nilesoft.org/docs/configuration/modify-items) and can have one of the following parameters:

<table><tbody><tr id="verb-null"><td>null</td><td>Specifies that the operation is the default for the selected file type.</td></tr><tr id="verb-open"><td>Open</td><td>Opens a file or an application.</td></tr><tr id="verb-openas"><td>OpenAs</td><td>Opener dialog when no program is associated to the extension.</td></tr><tr id="verb-runas"><td>RunAs</td><td>In Windows 7 and Vista, opens the UAC dialog andin others, open the Run as... Dialog.</td></tr><tr id="verb-edit"><td>Edit</td><td>Opens the default text editor for the file.</td></tr><tr id="verb-explore"><td>Explore</td><td>Opens the Windows Explorer in the folder specified in Directory.</td></tr><tr id="verb-properties"><td>Properties</td><td>Opens the properties window of the file.</td></tr><tr id="verb-print"><td>Print</td><td>Start printing the file with the default application.</td></tr><tr id="verb-find"><td>Find</td><td>Start a search.</td></tr></tbody></table>

Default = open |
| Wait | ni |

**Wait** for the [command](#command) to complete.

Default = false |

* * *

<!-- ============================================================================= -->
<!-- EXPRESSIONS -->
<!-- ============================================================================= -->
Title: Expressions - Shell

URL Source: https://nilesoft.org/docs/expressions

Markdown Content:
#### Expressions

**Shell** provides a variety of statements and expressions. Most of these will be familiar to developers who have programmed in Java script, C, C++, C#.

Expressions gives you all the power of **Shell**, but is using a simplified syntax that's easier to learn if you're a beginner, and makes you more productive if you're an expert.

To use expressions, you write them by using proper syntax. Syntax is the set of rules by which the words and symbols in an expression are correctly combined. Initially, expressions in **Shell** are a little bit hard to read. But with a good understanding of expression syntax and a little practice, it becomes much easier.

*   Expressions is non-case sensitive
*   Expressions (variables and functions) start with @
*   blocks are enclosed in @( ... )
*   Strings are enclosed with quotation marks or single quotation marks

* * *

<!-- ============================================================================= -->
<!-- COMMENTS -->
<!-- ============================================================================= -->
Title: Comments - Shell

URL Source: https://nilesoft.org/docs/expressions/comments

Markdown Content:
#### Comments

Comments can be used to explain **Shell** code, and to make it more readable. It can also be used to prevent execution **Shell** code. Comments can be singled-lined or multi-lined.

##### Single-line Comments

Single-line comments start with two forward slashes (`//`).
Any text between `//` and the end of the line is ignored (will not be executed).

This example uses a single-line comment before a line of code:

dynamic
{
// This is a comment
item(title='Hello World!')

//item(title='Hello World!')
}

This example uses a single-line comment at the end of a line of code:

dynamic
{
item(title='Hello World!') // This is a comment
}

##### Multi-line Comments

Multi-line comments start with `/*` and ends with `*/`.
Any text between `/*` and `*/` will be ignored.

dynamic
{
item(title='Hello,/* multiple-lines comment inside */ world')

/*
item(title='test item 1')
item(title='test item 2')
*/
}

Single or multi-line comments?
It is up to you which you want to use. Normally, we use `//` for short comments, and `/* */` for longer.

* * *

<!-- ============================================================================= -->
<!-- VARIABLES -->
<!-- ============================================================================= -->
Title: Variables - Shell

URL Source: https://nilesoft.org/docs/expressions/variables

Markdown Content:
#### Variables

Variables are containers for storing data values.

*   Global and local variables are optional.
*   To declare more than one variable, use a space.

The general rules for constructing names for variables (unique identifiers) are:

*   Names can contain letters, digits and underscores (`_`).
*   Names must begin with a letter.
*   Names cannot contain whitespaces or special characters like !, #, %, etc.
*   Reserved words (like keywords, such as null, true, false, etc.) cannot be used as names.
*   Variables can be placed in globle variables, or in the dynamic body section of an menu, or in both.

All variables must be identified with unique names.
These unique names are called identifiers. Identifiers can be short names (like x and y) or more descriptive names (age, sum, totalVolume).

_Note:_ It is recommended to use descriptive names in order to create understandable and maintainable code

##### Example
```JS
$hello_world = 'Hello World!'
$test_add1 = 5 + 6

item(title = hello_world cmd = msg(hello_world))

menu(title = test_add1)
{
    $test_sub1 = 11 - 5
    item(title = test_sub1)
}
```

* * *

<!-- ============================================================================= -->
<!-- STRING LITERAL -->
<!-- ============================================================================= -->
Title: String literal - Shell

URL Source: https://nilesoft.org/docs/expressions/string

Markdown Content:
#### String literal

string is zero or more characters written inside single or double quotes.

You can use quotes inside a string, as long as they don't match the quotes surrounding the string:
```JS
var
{
    var1 = "It's alright"
    var2 = "He is called 'Johnny'"
    var3 = 'He is called "Johnny"'
}
```

##### Single quotes

single quotes allow you to use the syntax of expressions within them.
The `@` sign must be placed before the expressions.

```JS
dynamic
{
    item(title = 'windows dir path: @sys.dir')
}
```

##### Double quotes

double quotes allow you to use the Escape Character inside them only.
The backslash (`\`) escape character turns special characters into characters.
The sequence `\"` inserts a double quote in a string:

```JS
var
{
    var1 = "hello\"world"
    // result: hello"world
}
```
The complete set of escape sequences is as follows:

<table><tbody><tr><td>\'</td><td>Single quote</td></tr><tr><td>\"</td><td>Double quote</td></tr><tr><td>\\</td><td>Backslash</td></tr><tr><td>\0</td><td>Null</td></tr><tr><td>\a</td><td>Alert</td></tr><tr><td>\b</td><td>Backspace</td></tr><tr><td>\f</td><td>Form Feed</td></tr><tr><td>\n</td><td>New Line</td></tr><tr><td>\r</td><td>Carriage Return</td></tr><tr><td>\t</td><td>Horizontal Tab</td></tr><tr><td>\v</td><td>Vertical Tab</td></tr><tr><td>\uxxxx</td><td>Unicode escape sequence (UTF-16) \uHHHH (range: 0000 - FFFF)</td></tr><tr><td>\xnnnn</td><td>Unicode escape sequence for character with hex value nnnn (variable length version of \uxxxx)</td></tr><tr><td>\Uxxxxxxxx</td><td>Unicode escape sequence (UTF-32) \U00HHHHHH (range: 000000 - 10FFFF)</td></tr></tbody></table>

* * *

<!-- ============================================================================= -->
<!-- NUMERIC LITERAL -->
<!-- ============================================================================= -->
Title: Numeric literal - Shell

URL Source: https://nilesoft.org/docs/expressions/numeric

Markdown Content:
#### Numeric literals

There are two types of numbers. Integer and floating point.

##### Integer literals

An integer is a numeric literal(associated with numbers) without any fractional or exponential part. There are two types of integer literals:

1.  Decimal literal (base 10)
2.  Hexadecimal literal (base 16)

**1\. Decimal-literal(base 10):**
A non-zero decimal digit followed by zero or more decimal digits(0, 1, 2, 3, 4, 5, 6, 7, 8, 9).

For example:

Decimal: 0, -9, 22 etc

**2\. Hexadecimal-literal(base 16):**
0x followed by one or more hexadecimal digits(0, 1, 2, 3, 4, 5, 6, 7, 8, 9, a, b, c, d, e, f).

For example:

Hexadecimal: 0x7f, 0x2a, 0x521 etc

##### Floating-point iterals

Floating-point literals specify values that must have a fractional part. These values contain decimal points (.)

For example:

-2.00.0000234

* * *

<!-- ============================================================================= -->
<!-- COLOR LITERAL -->
<!-- ============================================================================= -->
Title: Color literal - Shell

URL Source: https://nilesoft.org/docs/expressions/color

Markdown Content:
#### Color literal

##### Hexadecimal Colors

interprets color constants as hexadecimal if they are preceded by `#` and hexadecimal color is specified with: `#RRGGBB` or `#RRGGBBAA`, where the RR (red), GG (green) and BB (blue)and AA (alpha) hexadecimal integers specify the components of the color. All values must be between `00` and `FF`.
For example, the `#0000FF` value is rendered as blue, because the blue component is set to its highest value (`FF`) and the others are set to `00`.
There are 140 color names are predefined under the [color](https://nilesoft.org/docs/functions/color) scope.

* * *

<!-- ============================================================================= -->
<!-- OPERATORS -->
<!-- ============================================================================= -->
Title: Operators - Shell

URL Source: https://nilesoft.org/docs/expressions/operators

Markdown Content:
#### Operators

An operator is a symbol that tells to perform specific mathematical or logical manipulations. **Shell** is rich in built-in operators and provide the following types of operators

This chapter will examine the arithmetic, relational, logical, bitwise, assignment and other operators one by one.

##### Arithmetic Operators

The five arithmetical operations supported

| Operator | Description |
| --- | --- |
| + | Addition |
| \- | Subtraction |
| \* | Multiplication |
| / | Division |
| % | Modulo |

##### Relational and comparison operators

Two expressions can be compared using relational and equality operators. For example, to know if two values are equal or if one is greater than the other.

The result of such an operation is either true or false (i.e., a Boolean value).

The relational operators are:

| operator | description |
| --- | --- |
| \== | Equal to |
| != | Not equal to |
| < | Less than |
| \> | Greater than |
| <= | Less than or equal to |
| \>= | Greater than or equal to |

##### Logical Operators (!, ||, &&)

The operator `!` is operator for the Boolean operation NOT. It has only one operand, to its right, and inverts it, producing false if its operand is true, and true if its operand is false. Basically, it returns the opposite Boolean value of evaluating its operand. For example:

The logical operators `&&` and `||` are used when evaluating two expressions to obtain a single relational result. The operator `&&` corresponds to the Boolean logical operation AND, which yields true if both its operands are true, and false otherwise. The following panel shows the result of operator `&&` evaluating the expression `a && b`:

| operator | description |
| --- | --- |
| && | Called Logical AND operator. If both the operands are non-zero, then condition becomes true. |
| || | Called Logical OR Operator. If any of the two operands is non-zero, then condition becomes true. |
| ! | Called Logical NOT Operator. Use to reverses the logical state of its operand. If a condition is true, then Logical NOT operator will make false.
!(5 == 5)   // evaluates to false because the expression at its right (5 == 5) is true
!(6 <= 4)   // evaluates to true because (6 <= 4) would be false
!true       // evaluates to false
!false      // evaluates to true

|

##### Conditional ternary operator

The conditional operator evaluates an expression, returning one value if that expression evaluates to true, and a different one if the expression evaluates as false. Its syntax is:

condition ? result1 : result2

If condition is true, the entire expression evaluates to result1, and otherwise to result2.

7==5 ? 4 : 3     // evaluates to 3, since 7 is not equal to 5.
7==5+2 ? 4 : 3   // evaluates to 4, since 7 is equal to 5+2.
5>3 ? a : b      // evaluates to the value of a, since 5 is greater than 3.
a>b ? a : b      // evaluates to whichever is greater, a or b.

##### Bitwise Operators

Bitwise operators modify variables considering the bit patterns that represent the values they store.

| Operator | Description |
| --- | --- |
| & | Bitwise AND |
| | | Bitwise inclusive OR |
| ^ | Bitwise exclusive OR |
| ~ | Unary complement (bit inversion) |
| << | Shift bits left |
| \>> | Shift bits right |

##### Precedence of operators

A single expression may have multiple operators. For example:

x = 5 + 7 % 2

the above expression always assigns 6 to variable x, because the `%` operator has a higher precedence than the `+` operator, and is always evaluated before. Parts of the expressions can be enclosed in parenthesis to override this precedence order, or to make explicitly clear the intended effect. Notice the difference:

x = 5 + (7 % 2)    // x = 6 (same as without parenthesis)
x = (5 + 7) % 2    // x = 0

From greatest to smallest priority, Operators are evaluated in the following order:

| Level | Precedence group | Operator | Description | Grouping |
| --- | --- | --- | --- | --- |
| 1 | Postfix (unary) | `++ --` | postfix increment / decrement | Left-to-right |
| `()` | functional forms |
| `[]` | subscript |
| `.` | member access |
| 2 | Prefix (unary) | `++ --` | prefix increment / decrement | Right-to-left |
| `~ !` | bitwise NOT / logical NOT |
| `+ -` | unary prefix |
| 4 | Arithmetic: scaling | `* / %` | multiply, divide, modulo | Left-to-right |
| 5 | Arithmetic: addition | `+ -` | addition, subtraction | Left-to-right |
| 6 | Bitwise shift | `<< >>` | shift left, shift right | Left-to-right |
| 7 | Relational | `< > <= >=` | comparison operators | Left-to-right |
| 8 | Equality | `== !=` | equality / inequality | Left-to-right |
| 9 | And | `&` | bitwise AND | Left-to-right |
| 10 | Exclusive or | `^` | bitwise XOR | Left-to-right |
| 11 | Inclusive or | `|` | bitwise OR | Left-to-right |
| 12 | Conjunction | `&&` | logical AND | Left-to-right |
| 13 | Disjunction | `||` | logical OR | Left-to-right |
| 15 | Assignment-level expressions | `=` | assignment | Right-to-left |
| `?:` | conditional operator |
| 16 | Sequencing | `,` | comma separator | Left-to-right |

When an expression has two operators with the same precedence level, grouping determines which one is evaluated first: either left-to-right or right-to-left.
Enclosing all sub-statements in parentheses (even those unnecessary because of their precedence) improves code readability.

* * *

<!-- ============================================================================= -->
<!-- IDENTIFIER -->
<!-- ============================================================================= -->
Title: Identifier - Shell

URL Source: https://nilesoft.org/docs/expressions/identifier

Markdown Content:
#### Identifier

The identifier have unique titles described by fully qualified names that indicate a logical hierarchy.
The identifier that has no parameters. Parentheses are added when necessary to separate:

There is a hierarchy of keywords in that some keywords are always followed by others.

sel.path
sel.path().ext

* * *

This page is **open source**. Noticed a typo? Or something unclear?
[Improve this page on GitHub](https://github.com/moudey/shell/blob/main/docs/expressions/identifier.html)

<!-- ============================================================================= -->
<!-- FUNCTIONS -->
<!-- ============================================================================= -->
Title: Functions - Shell

URL Source: https://nilesoft.org/docs/functions

Markdown Content:
#### Functions

The functions and variables have unique titles described by fully qualified names that indicate a logical hierarchy.

**Shell** has a number of functions and variables built into it that are always available. They are listed here in alphabetical order.

##### indexof

Find the position of a menu item.

Syntax

`indexof(expression[, default position])`

##### if

Conditionally executes another statement.

Syntax

`if(condition-expression)`

`if(condition-expression, true-expression)`

`if(condition-expression, true-expression, false-expression)`

##### null

Returns a null value.

Syntax

`null(expression)`

##### length (len)

Returns length of string and array type

Syntax

`length(expression)`

##### quote

Returns text with a double quotation mark.

Syntax

`quote(expression)`

##### char

Returns the value of the numeric parameter to a character.

Syntax

`char(numeric-expression)`

##### var

Returns the value of the passed variable

Syntax

`var(expression)`

##### tohex

Converting the value of the passed numeric expression to hexadecimal string.

Syntax

`tohex(numeric-expression)`

##### equal

Returns `true` if the parameters passed are equal.

Syntax

`equal(expression-1, expression-2)`

##### not

Returns `true` if the parameters passed are not equal.

Syntax

`not(expression-1, expression-2)`

##### greater

Returns `true` if the first parameter is greater than the second parameter.

Syntax

`greater(expression-1, expression-2)`

##### less

Returns `true` if the first parameter is less than the second parameter.

Syntax

`less(expression-1, expression-2)`

##### shl

bitwise left shift.

Syntax

`shl(shift-expression, additive-expression)`

##### shr

bitwise right shift.

Syntax

`shr(shift-expression, additive-expression)`

##### cmd visibility enumerations

The flags that specify how an application is to be displayed when it is opened.

cmd.hidden
cmd.show
cmd.visible
cmd.normal
cmd.maximized
cmd.minimized

##### Selection mode enumerations

Syntax

mode.none
mode.single
mode.multiple
mode.multiunique
mode.multisingle
mode.multi

##### Selection type enumerations

Syntax

type.desktop
type.directory(dir)
type.drive
type.dvd
type.file
type.fixed
type.namespace
type.remote
type.unknown
type.usb
type.vhd

##### Keywords

null
bool
true
false
auto
none
default

* * *

<!-- ============================================================================= -->
<!-- FUNCTIONS: APP -->
<!-- ============================================================================= -->
Title: app - Shell

URL Source: https://nilesoft.org/docs/functions/app

Markdown Content:
##### APP

The namespace of the application contains file paths, version and some details.

##### app.directory

Returns the path of **Shell** directory.

Syntax

app.directory     // usage in section context
'@app.directory'  // usage inside quoted-string literals

##### app.cfg

Returns the path of the [configuration file](https://nilesoft.org/docs/configuration/index.html#shell.nss) `shell.nss` (or `shell.shl` in older installations).

Syntax

app.cfg     // usage in section context
'@app.cfg'  // usage inside quoted-string literals

##### app.dll

Returns the path of `shell.dll`.

Syntax

app.dll     // usage in section context
'@app.dll'  // usage inside quoted-string literals

##### app.exe

Returns the path of `shell.exe`.

Syntax

app.exe     // usage in section context
'@app.exe'  // usage inside quoted-string literals

##### app.name

Returns application name.

Syntax

app.name     // usage in section context
'@app.name'  // usage inside quoted-string literals

##### app.version

Returns **Shell** version.

Syntax

app.version     // usage in section context
'@app.version'  // usage inside quoted-string literals

##### app.is64

Returns the architecture of the application.

Syntax

app.is64     // usage in section context
'@app.is64'  // usage inside quoted-string literals

##### app.about

Returns **Shell** information.

Syntax

app.about     // usage in section context
'@app.about'  // usage inside quoted-string literals

##### app.reload

Reload the [configuration file](https://nilesoft.org/docs/configuration/index.html#shell.nss).

Syntax

app.reload     // usage in section context
'@app.reload'  // usage inside quoted-string literals

##### app.unload

Unload the [configuration file](https://nilesoft.org/docs/configuration/index.html#shell.nss).

Syntax

app.unload     // usage in section context
'@app.unload'  // usage inside quoted-string literals

* * *

<!-- ============================================================================= -->
<!-- FUNCTIONS: APPX -->
<!-- ============================================================================= -->
Title: appx - Shell

URL Source: https://nilesoft.org/docs/functions/appX

Markdown Content:
#### APPX

##### appx or appx.path

Returns the path of Package.

Syntax

`appx(packageName)   appx.path(packageName)`

Parameters

packageName

can be passed in full name or part of name.

Example

appx.path("WindowsTerminal")
// result:
// C:\Program Files\WindowsApps\Microsoft.WindowsTerminal_1.11.3471.0_x64__8wekyb3d8bbwe

##### appx.name

Returns the display name of the Package.

Syntax

`appx.name(packageName)`

Parameters

packageName

can be passed in full name or part of name.

Example

appx.name("WindowsTerminal")
// result:
// Windows Terminal

##### appx.id

Returns the full name of the Package.

Syntax

`appx.id(packageName)`

Parameters

packageName

can be passed in full name or part of name.

Example

appx.id("WindowsTerminal")
// result:
// Microsoft.WindowsTerminal_1.11.3471.0_x64__8wekyb3d8bbwe

##### appx.family

Returns the family name of the Package.

Syntax

`appx.family(packageName)`

Parameters

packageName

can be passed in full name or part of name.

Example

appx.family("WindowsTerminal")
// result:
// Microsoft.WindowsTerminal_8wekyb3d8bbwe

##### appx.version

Returns version of the Package.

Syntax

`appx.version(packageName)`

Parameters

packageName

can be passed in full name or part of name.

Example

appx.version("WindowsTerminal")
// result:
// 1.11.3471.0

##### appx.shell

Return package settings to run.

Syntax

`appx.shell(packageName)`

Parameters

packageName

can be passed in full name or part of name.

Example

appx.shell("WindowsTerminal")
// result:
// shell:appsFolder\Microsoft.WindowsTerminal_8wekyb3d8bbwe!App

* * *

<!-- ============================================================================= -->
<!-- FUNCTIONS: COLOR -->
<!-- ============================================================================= -->
Title: color - Shell

URL Source: https://nilesoft.org/docs/functions/color

Markdown Content:
##### COLOR

Color namespace contains predefined colors
```JS
color.aliceblue
color.antiquewhite
color.aqua
color.aquamarine
color.azure
color.beige
color.bisque
color.black
color.blanchedalmond
color.blue
color.blueviolet
color.brown
color.burlywood
color.cadetblue
color.chartreuse
color.chocolate
color.coral
color.cornflowerblue
color.cornsilk
color.crimson
color.cyan
color.darkblue
color.darkcyan
color.darkgoldenrod
color.darkgray
color.darkgreen
color.darkkhaki
color.darkmagenta
color.darkolivegreen
color.darkorange
color.darkorchid
color.darkred
color.darksalmon
color.darkseagreen
color.darkslateblue
color.darkslategray
color.darkturquoise
color.darkviolet
color.deeppink
color.deepskyblue
color.dimgray
color.dodgerblue
color.firebrick
color.floralwhite
color.forestgreen
color.fuchsia
color.gainsboro
color.ghostwhite
color.gold
color.goldenrod
color.gray
color.green
color.greenyellow
color.honeydew
color.hotpink
color.indianred
color.indigo
color.ivory
color.khaki
color.lavender
color.lavenderblush
color.lawngreen
color.lemonchiffon
color.lightblue
color.lightcoral
color.lightcyan
color.lightgoldenrodyellow
color.lightgray
color.lightgreen
color.lightpink
color.lightsalmon
color.lightseagreen
color.lightskyblue
color.lightslategray
color.lightsteelblue
color.lightyellow
color.lime
color.limegreen
color.linen
color.magenta
color.maroon
color.mediumaquamarine
color.mediumblue
color.mediumorchid
color.mediumpurple
color.mediumseagreen
color.mediumslateblue
color.mediumspringgreen
color.mediumturquoise
color.mediumvioletred
color.midnightblue
color.mintcream
color.mistyrose
color.moccasin
color.navajowhite
color.navy
color.oldlace
color.olive
color.olivedrab
color.orange
color.orangered
color.orchid
color.palegoldenrod
color.palegreen
color.paleturquoise
color.palevioletred
color.papayawhip
color.peachpuff
color.peru
color.pink
color.plum
color.powderblue
color.purple
color.red
color.rosybrown
color.royalblue
color.saddlebrown
color.salmon
color.sandybrown
color.seagreen
color.seashell
color.sienna
color.silver
color.skyblue
color.slateblue
color.slategray
color.snow
color.springgreen
color.steelblue
color.tan
color.teal
color.thistle
color.tomato
color.transparent
color.turquoise
color.violet
color.wheat
color.white
color.whitesmoke
color.yellow
color.yellowgreen
```

##### color.rgb(red, green, blue)

##### color.box

##### color.box(#ff0000)

##### color.random or color.random(min, max)

color.random
color.random(0x808080, 0xf0f0f0)

color.default
color.invert
color.accent
color.accent\_light1
color.accent\_light2
color.accent\_light3
color.accent\_dark1
color.accent\_dark2
color.accent\_dark3
color.opacity
color.lighten
color.darken
color.rgba

* * *

<!-- ============================================================================= -->
<!-- FUNCTIONS: COMMAND -->
<!-- ============================================================================= -->
Title: command - Shell

URL Source: https://nilesoft.org/docs/functions/command

Markdown Content:
##### COMMAND

##### command.copy, command.copy\_to\_clipboard

copy to paramter clipboard.

Syntax

`command.copy('string copy to clipboard')`

##### command.sleep(milliseconds)

Suspends the execution of the current thread until the time-out interval elapses.

Syntax

`command.sleep(1000)`

##### command.random

Suspends the execution of the current thread until the time-out interval elapses.

Syntax

`command.random(min value, max value)`

```JS
command.cascade_windows
command.copy_to_folder
command.customize_this_folder
command.find
command.folder_options
command.invert_selection
command.minimize_all_windows
command.move_to_folder
command.redo
command.refresh
command.restart_explorer
command.restore_all_windows
command.run
command.search
command.select_all
command.select_none
command.show_windows_side_by_side
command.show_windows_stacked
command.switcher
command.toggle_desktop
command.toggleext
command.togglehidden
command.undo
```

* * *

<!-- ============================================================================= -->
<!-- FUNCTIONS: ID -->
<!-- ============================================================================= -->
Title: id - Shell

URL Source: https://nilesoft.org/docs/functions/id

Markdown Content:
##### ID

The namespace of the id contains the identifiers for most of the system context menus items where the item's title or icons can be returned

##### Syntax

`id.copy`
`id.copy.title`
`id.copy.icon` or `icon.copy`

##### List

```JS
id.add_a_network_location
id.adjust_date_time
id.align_icons_to_grid
id.arrange_by
id.auto_arrange_icons
id.autoplay
id.cancel
id.cascade_windows
id.cast_to_device
id.cleanup
id.collapse
id.collapse_all_groups
id.collapse_group
id.command_prompt
id.compressed
id.configure
id.content
id.control_panel
id.copy
id.copy_as_path
id.copy_here
id.copy_path
id.copy_to
id.copy_to_folder
id.cortana
id.create_shortcut
id.create_shortcuts
id.create_shortcuts_here
id.customize_notification_icons
id.customize_this_folder
id.cut
id.delete
id.desktop
id.details
id.device_manager
id.disconnect
id.disconnect_network_drive
id.display_settings
id.edit
id.eject
id.empty_recycle_bin
id.erase_this_disc
id.exit_explorer
id.expand
id.expand_all_groups
id.expand_group
id.expand_to_current_folder
id.extra_large_icons
id.extract_all
id.extract_to
id.file_explorer
id.folder_options
id.format
id.give_access_to
id.group_by
id.include_in_library
id.insert_unicode_control_character
id.install
id.large_icons
id.list
id.lock_all_taskbars
id.lock_the_taskbar
id.make_available_offline
id.make_available_online
id.manage
id.map_as_drive
id.map_network_drive
id.medium_icons
id.merge
id.more_options
id.mount
id.move_here
id.move_to
id.move_to_folder
id.new
id.new_folder
id.new_item
id.news_and_interests
id.next_desktop_background
id.open
id.open_as_portable
id.open_autoplay
id.open_command_prompt
id.open_command_window_here
id.open_file_location
id.open_folder_location
id.open_in_new_process
id.open_in_new_tab
id.open_in_new_window
id.open_new_tab
id.open_new_window
id.open_powershell_window_here
id.open_windows_powershell
id.open_with
id.options
id.paste
id.paste_shortcut
id.personalize
id.pin_current_folder_to_quick_access
id.pin_to_quick_access
id.pin_to_start
id.pin_to_taskbar
id.play
id.play_with_windows_media_player
id.power_options
id.preview
id.print
id.properties
id.reconversion
id.redo
id.refresh
id.remove_from_quick_access
id.remove_properties
id.rename
id.restore
id.restore_default_libraries
id.restore_previous_versions
id.rotate_left
id.rotate_right
id.run
id.run_as_administrator
id.run_as_another_user
id.search
id.select_all
id.send_to
id.set_as_desktop_background
id.set_as_desktop_wallpaper
id.settings
id.share
id.share_with
id.shield
id.show_all_folders
id.show_cortana_button
id.show_desktop_icons
id.show_file_extensions
id.show_hidden_files
id.show_libraries
id.show_network
id.show_pen_button
id.show_people_on_the_taskbar
id.show_task_view_button
id.show_the_desktop
id.show_this_pc
id.show_touch_keyboard_button
id.show_touchpad_button
id.show_windows_side_by_side
id.show_windows_stacked
id.small_icons
id.sort_by
id.store
id.task_manager
id.taskbar_settings
id.tiles
id.troubleshoot_compatibility
id.turn_off_bitlocker
id.turn_on_bitlocker
id.undo
id.unpin_from_quick_access
id.unpin_from_start
id.unpin_from_taskbar
id.view
```

* * *

<!-- ============================================================================= -->
<!-- FUNCTIONS: IMAGE -->
<!-- ============================================================================= -->
Title: image - Shell

URL Source: https://nilesoft.org/docs/functions/image

Markdown Content:
##### IMAGE (ICON)

The image namespace contains functions that return an icon and are only assigned to the `image` property

##### image.glyph

Return font icon with color and size option.

Syntax

`image.glyph(0xE00B)`
`image.glyph(\uE00B)`
`image.glyph("\uE00B")`
`image.glyph("\xE00B")`

`image.glyph(0xE00B, #0000ff)`
`image.glyph(0xE00B, #0000ff, 10)`
`image.glyph(0xE00B, #0000ff, 12, "Segoe MDL2 Assets")`

##### image.res

Returns an icon through a path or through resources.

Syntax

`image.res(path)`
`image.res(path, index)`
`image.res(path, -id)`

`image(path)`
`image(path, index)`
`image(path, -id)`

##### image.svg, image.svgf

Returns an svg image via a string or from a file path.

Syntax

`image.svg('<svg viewBox="0 0 100 100"><path fill="red" d="M0 0 L 100 0 L50 100 Z"/></svg>')`
`image.svgf(path)`

##### image.rect

Returns a colored rectangle icon of an optional size.

Syntax

`image.rect(#0000ff)`
`image.rect(#0000ff, 10)`

##### image.segoe

Returns a glyph from "Segoe Fluent Icons" if present then "Segoe MDL2 Assets".

Syntax

`image.segoe(\uxxxx)`
`image.segoe(\uxxxx, 10)`

##### image.fluent

Returns a glyph from "Segoe Fluent Icons" with optional size.

Syntax

`image.fluent(\uxxxx)`
`image.fluent(\uxxxx, 10)`

##### image.mdl

Returns a glyph from "Segoe MDL2 Assets" with optional size.

Syntax

`image.mdl(\uxxxx)`
`image.mdl(\uxxxx, 10)`

##### image.default

Syntax

`image.default`

##### icon.box

Syntax

`icon.box([["path to file"], [index]])`

* * *

<!-- ============================================================================= -->
<!-- FUNCTIONS: IO -->
<!-- ============================================================================= -->
Title: io - Shell

URL Source: https://nilesoft.org/docs/functions/io

Markdown Content:
##### IO

##### io.attribute enumerations.

Syntax

```JS
io.attribute.archive
io.attribute.compressed
io.attribute.device
io.attribute.directory
io.attribute.encrypted
io.attribute.hidden
io.attribute.invalid
io.attribute.normal
io.attribute.offline
io.attribute.readonly
io.attribute.sparsefile
io.attribute.system
io.attribute.temporary
io.attribute.virtual
```

##### io.attributes

Retrieves file system attributes for a specified path.

Syntax

`io.attributes(path)`

attribute verification
```JS
// check if path is hidden
io.attribute.hidden(path)

// check if path is directory
io.attribute.directory(path)

var { atrr = io.attributes(path) }

// check if attr is hidden
io.attribute.hidden(atrr)

// check if attr is directory
io.attribute.directory(atrr)
```

##### io.copy

Copies an existing file to a new file.

Syntax

`io.copy(pathFrom, pathTo)`
`io.copy(pathFrom, pathTo, options)` options:
1 = skip\_existing, 2 = overwrite\_existing, 4 = update\_existing, 16 = recursive
default = update\_existing | recursive

Example:
`io.copy('c:\old', 'd:\new', 16 | 4)`

##### io.move

Moves an existing file or a directory, including its children..

Syntax

`io.move(oldPath, newPath)`

##### io.rename

Rename a file or directory.

Syntax

`io.rename(oldName, newName)`

##### io.delete

Deletes an existing path.

Syntax

`io.delete(path)`

##### io.directory.create (io.dir.create)

Create new directory.

Syntax

`io.directory.create(path)`

##### io.directory.exists (io.dir.exists)

Check if one or more directories exists.

Syntax

`io.directory.exists(path)`
`io.directory.exists(path1, path2, path3, ...)`

##### io.directory.empty (io.dir.empty)

Check if one or more directory is empty.

Syntax

`io.directory.empty(path)`
`io.directory.empty(path1, path2, path3, ...)`

##### File functions.

##### io.file.size

Retrieves the size of the specified file, in bytes.

Syntax

`io.file.size(path)`

##### io.file.exists

Check if one or more files exists.

Syntax

`io.file.exists(path)`
`io.file.exists(path1, path2, path3, ...)`

##### io.file.read

Read file contents as text with character count option.

Syntax

`io.file.read(path)`
`io.file.read(path, 12)`

##### io.file.create

Create new file with content option.

Syntax

`io.file.create(path)`
`io.file.create(path, "Hello World!")`

##### io.file.write

Writing to a file with new content.

Syntax

`io.file.write(path, "Hello World!")`

##### io.file.append

Appends text to an existing file, or to a new file if the specified file does not exist.

Syntax

`io.file.append(path, "Hello ")`
`io.file.append(path, "World!")`

##### io.datetime

Gets or Sets the time of the file.

Syntax

get date time 
```JS
io.datetime.created(sel.path)
io.datetime.modified(sel.path)
io.datetime.accessed(sel.path)
io.datetime.created(sel.path, 'y/m/d')
io.datetime.modified(sel.path, 'y/m/d')
io.datetime.accessed(sel.path, 'y/m/d')
```

set date time
`io.datetime.created(sel.path,2000,1,1))   io.datetime.modified(sel.path,2000,1,1))   io.datetime.accessed(sel.path,2000,1,1))   `

##### io.meta

Gets meta data by [property key](https://github.com/MicrosoftDocs/win32/blob/docs/desktop-src/properties/core-bumper.md).

Syntax

`io.meta('path\to\file',"System.Size"))`

* * *

<!-- ============================================================================= -->
<!-- FUNCTIONS: KEY -->
<!-- ============================================================================= -->
Title: key - Shell

URL Source: https://nilesoft.org/docs/functions/key

Markdown Content:
##### KEY

Keyboard functions

##### keys enumerations

```JS
key.alt
key.apps
key.back
key.cancel
key.capital
key.capslock
key.control
key.delete
key.down
key.end
key.enter
key.escape
key.execute
key.f1
key.f10
key.f11
key.f12
key.f2
key.f3
key.f4
key.f5
key.f6
key.f7
key.f8
key.f9
key.help
key.home
key.insert
key.lalt
key.lcontrol
key.left
key.lshift
key.lwin
key.next
key.none
key.pagedown
key.pageup
key.pause
key.play
key.print
key.printscreen
key.prior
key.ralt
key.rcontrol
key.return
key.right
key.rshift
key.rwin
key.shift
key.snapshot
key.space
key.tab
key.up
key.win
```

##### key

Syntax

```JS
// check if SHIFT key is pressed
key(key.shift)

// or
key == key.shift

// or
key.shift()

// check if tow keys (SHIFT+CTRL) is pressed
key(key.shift, key.control)

// check if keys (SHIFT+CTRL+X) is pressed
key(key.shift, key.control, 87)
```

##### key.send

Send one or more keys to the current window.

Syntax

```JS
key.send(key.f5)
key.send(key.ctrl,'n')
key.send(key.shift, key.delete)
```

* * *

<!-- ============================================================================= -->
<!-- FUNCTIONS: MSG -->
<!-- ============================================================================= -->
Title: msg - Shell

URL Source: https://nilesoft.org/docs/functions/msg

Markdown Content:
#### MSG

Displays a modal dialog box that contains a system icon, a set of buttons, and a brief application-specific message, such as status or error information. The message box returns an integer value that indicates which button the user clicked.

##### Syntax

```JS
msg(text)
msg(text, title)
msg(text, title, flags)
```

##### Parameters

<table><tbody><tr><td>text</td><td>The message to be displayed.</td></tr><tr><td>title</td><td>The dialog box title. If this parameter is NULL, the default title is <b>Nilesoft Shell</b>.</td></tr><tr><td>flags</td><td>The contents and behavior of the dialog box. This parameter can be a combination of flags from the following groups of flags.</td></tr></tbody></table>

##### Flags

##### To display an icon in the message box, specify one of the following values.

`msg.error`

A stop-sign icon appears in the message box.

`msg.question`

A question-mark icon appears in the message box.

`msg.warning`

An exclamation-point icon appears in the message box.

`msg.info`

An icon consisting of a lowercase letter i in a circle appears in the message box.

##### To indicate the buttons displayed in the message box, specify one of the following values.

`msg.ok`

The message box contains one push button: OK. This is the default.

`msg.okcancel`

The message box contains two push buttons: OK and Cancel.

`msg.yesnocancel`

The message box contains three push buttons: Yes, No, and Cancel.

`msg.yesno`

The message box contains two push buttons: Yes and No.

##### To indicate the default button, specify one of the following values.

`msg.defbutton1`

The first button is the default button.

`msg.defbutton2`

The second button is the default button.

`msg.defbutton3`

The third button is the default button.

##### To indicate the modality of the dialog box, specify one of the following values.

`msg.applmodal`

The user must respond to the message box before continuing work in the current window. However, the user can move to the windows of other threads and work in those windows.

`msg.taskmodal`

Same as `msg.applmodal` except that all the top-level windows belonging to the current thread are disabled.

##### To specify other options, use one or more of the following values.

`msg.right`

The text is right-justified.

`msg.rtlreading`

Displays message and caption text using right-to-left reading order on Hebrew and Arabic systems.

`msg.setforeground`

The message box becomes the foreground window.

`msg.topmost`

##### Return value

If a message box has a Cancel button, the function returns the `msg.idcancel` value if either the ESC key is pressed or the Cancel button is selected. If the message box has no Cancel button, pressing ESC will no effect - unless an `msg.ok` button is present. If an `msg.ok` button is displayed and the user presses ESC, the return value will be `msg.idok`.

If the function fails, the return value is zero.
If the function succeeds, the return value is one of the following values:

msg.idok or 1

The OK button was selected.

msg.idcancel or 2

The Cancel button was selected.

msg.idyes or 6

The Yes button was selected.

msg.idno or 7

The No button was selected.

##### Examples

```JS
msg("Hello, Warld!","NileSoft Shell", msg.info | msg.ok)
msg("Hello, Warld!","NileSoft Shell")
msg("Hello, Warld!")
```

##### msg.beep(type)

Plays a waveform sound. The waveform sound for each sound type is identified by an entry in the registry.

Syntax

`sys.beep`
`sys.beep(msg.error)`
`sys.beep(msg.warning)`

* * *

<!-- ============================================================================= -->
<!-- FUNCTIONS: PATH -->
<!-- ============================================================================= -->
Title: path - Shell

URL Source: https://nilesoft.org/docs/functions/path

Markdown Content:
##### PATH

##### path.combine (path.join)

Combines an array of strings into a path.

Syntax

`path.combine(path1, path2)`
`path.combine("C:", "Windows", "Explorer.exe")`

##### path.currentdirectory (path.curdir)

Retrieves the current directory for the current process.

Syntax

`path.currentdirectory`

##### path.directory.name (path.dir.name)

Return the directory name from the path.

Syntax

`path.directory.name('c:\windows\system32') // returns: system32`

##### path.directory.box (path.dir.box)

Shows the directory selection box and returns the path of the specified directory.

Syntax

`path.directory.box`

##### path.empty

Check if one or more path is empty.

Syntax

`path.empty(path)`
`path.empty(path1, path2, ...)`

##### path.exists

Check if one or more path exists..

Syntax

`path.exists(path)`
`path.exists(path1, path2, ...)`

##### path.full

Returns the absolute path for the specified path string.

Syntax

`path.full(path)`

##### path.short

Retrieves the short path form of the specified path.

Syntax

`path.short(path)`

##### path.name

Return the name from the path.

Syntax

`path.name(path)`

##### path.location (path.parent)

Return the path of the parent

Syntax

`path.location(path)`

##### path.location.name

Return the name from the parent

Syntax

`path.location.name(path)`

##### path.root

Return the drive path

Syntax

`path.root(path)`

##### path.title

Return the title from the path

Syntax

`path.title(path)`

##### path.type

Return the type of path

Syntax

`path.type(path) == type.file`

##### path.file.name

Return the name of path

Syntax

`path.file.name(path)`

##### path.file.title

Return the name of path without extension

Syntax

`path.file.title(path)`

##### path.file.ext

Return the extension of path

Syntax

`path.file.ext(path)`

##### path.file.box

Shows the file selection box and returns the path of the specified file.

Syntax

`path.file.box`
`path.file.box('All|*.*|Text|*.txt')`
`path.file.box('exe|*.exe', 'c:\windows')`
`path.file.box('exe|*.exe', 'c:\windows', 'explorer.exe')`

##### path.files

Returns all files with the ability to filter.

Syntax

path.files(sys.dir, ["*"], flags[2=files | 3=dirs | 5=files+dirs | 8=quots | 16=full path], sep)

// get all files and dirs
path.files(sys.dir)
path.files(sys.dir, "*")

// get all files with .exe
path.files(sys.dir,"*.exe")

// full path + quots
path.files(sys.dir, '*', 8|16)

##### path.isabsolute

Syntax

`path.isabsolute(path)`

##### path.isrelative

Syntax

`path.isrelative(path)`

##### path.isfile

Syntax

`path.isfile(path)`

##### path.isdirectory

Syntax

`path.isdirectory(path)`

##### path.isroot (path.isdrive)

Syntax

`path.isdrive(path)`

##### path.isclsid (path.isnamespace)

Syntax

`path.isclsid(path)`

##### path.isexe

Syntax

`path.isexe(path)`

##### path.removeextension

Remove the extension from the passed parameter.

Syntax

`path.removeextension`

##### path.lnk

Return a path from the shortcut

Syntax

`path.lnk(path)`

##### path.lnk.type

Return type from the shortcut

Syntax

`path.lnk.type(path)`

##### path.lnk.dir

Return a dir path from the shortcut

Syntax

`path.lnk.dir(path)`

##### path.lnk.icon

Return a icon path and index from the shortcut

Syntax

`path.lnk.icon(path)`

##### path.getknownfolder

Retrieves the full path of a known folder identified.

Syntax

`path.getknownfolder('{905e63b6-c1bf-494e-b29c-65b732d3d21a}')`

##### path.separator(path.sep)

Replacing the back slash with a forward slash or defining a spacer.

Syntax

`path.separator('c:\windows\system32')` _return "c:/windows/system32"_
`path.separator('c:\windows\system32', '#')` _return "c:#windows#system32"_

##### path.wsl

convert the path to wsl path

* * *

<!-- ============================================================================= -->
<!-- FUNCTIONS: PROCESS -->
<!-- ============================================================================= -->
Title: process - Shell

URL Source: https://nilesoft.org/docs/functions/process

Markdown Content:
##### PROCESS

##### Syntax

```JS
process.handle
process.name
process.id
process.path
process.is_explorer
process.used
```

* * *

<!-- ============================================================================= -->
<!-- FUNCTIONS: STR -->
<!-- ============================================================================= -->
Title: str - Shell

URL Source: https://nilesoft.org/docs/functions/str

Markdown Content:
##### STR

##### str.get (str.at)

Returns a character from a specific location in the string.

Syntax

`str.get("Hello World!", 7)`

##### str.set

Sets a character with a specific location in the string.

Syntax

`str.set("Hello World!", 6, '-')`

##### str.contains

Returns a value indicating whether a specified substring occurs within this string.

Syntax

`str.contains("Hello World!", 'World')`

##### str.empty (str.null)

Tests whether the string contains characters.

Syntax

`str.empty("")`

##### str.start

Checks whether the string starts with the specified prefix.

Syntax

`str.start("Hello World!", "World!")`

##### str.end

Checks whether the string ends with the specified suffix.

Syntax

`str.end("Hello World!", "World!")`

##### str.equals

Determines whether two String have the same value.

Syntax

`str.equals("Hello World!", "Hello World!")`

##### str.not

Determine if two strings do not have the same value.

Syntax

`str.not("Hello World!", "Hello-World!")`

##### str.length (str.len)

Gets the number of characters in the current string.

Syntax

`str.length("Hello World!")`

##### str.trim

Returns a new string in which all leading and trailing occurrences of a set of specified characters from the current string are removed.

Syntax

Removes all leading and trailing white-space characters from the current string.

`str.trim(" Hello World! ")`

Removes all leading and trailing 'H!' characters from the current string.

`str.trim("Hello World!", "H!")`

##### str.trimstart

Returns a new string in which all leading occurrences of a set of specified characters from the current string are removed.

Syntax

Removes all leading white-space characters from the current string.

`str.trimstart(" Hello World!")`

Removes all leading 'H' characters from the current string.

`str.trimstart("Hello World!", "H")`

##### str.trimend

Returns a new string in which all trailing occurrences of a set of specified characters from the current string are removed.

Syntax

Removes all trailing white-space characters from the current string.

`str.trimend("Hello World! ")`

Removes all trailing '!' characters from the current string.

`str.trimend("Hello World!", "!")`

##### str.find

Searches a string in a forward direction for the first occurrence of a substring that matches a specified sequence of characters.

Syntax

`str.find("Hello World!", "lo")`

##### str.findlast

Searches a string in a backward direction for the first occurrence of a substring that matches a specified sequence of characters.

Syntax

`str.findlast("Hello World!", "Wor")`

##### str.lower

Returns a copy of this string converted to lowercase.

Syntax

`str.lower("Hello World!")`

##### str.upper

Returns a copy of this string converted to uppercase.

Syntax

`str.upper("Hello World!")`

##### str.left

Extracts the left part of a string.

Syntax

`str.left("Hello World!", 5)`

##### str.right

Extracts the right part of a string.

Syntax

`str.right("Hello World!", 5)`

##### str.sub

Copies a substring of at most some number of characters from a string beginning from a specified position.

Syntax

`str.sub("Hello World!", 5)`
`str.sub("Hello World!", 0, 5)`

##### str.remove

Removes an element or a range of elements in a string from a specified position.

Syntax

`str.replace("Hello World!", " ")`
`str.remove("Hello World!", 5)`
`str.remove("Hello World!", 5, 1)`

##### str.replace

Replace elements in a string at a specified position with specific characters or characters copied from other ranges or strings.

Syntax

`str.replace("Hello World!", "World", "User")`
`str.replace("Hello World!", "world", "user", true)`

##### str.padleft

Returns a new string of a specified length in which the beginning of the current string is padded with spaces or with a specified character.

Syntax

`str.padleft("Hello World!", "*")`
`str.padleft("Hello World!", "*", 3)`

##### str.padright

Returns a new string of a specified length in which the end of the current string is padded with spaces or with a specified character.

Syntax

`str.padright("Hello World!", "*")`
`str.padright("Hello World!", "*", 3)`

##### str.padding

Returns a new string of a specified length in which the start and end of the current string is padded with spaces or with a specified character.

Syntax

`str.padding("Hello World!", "*")`
`str.padding("Hello World!", "*", 3)`

##### str.guid

Returns a new guid string

Syntax

`str.guid` return 00000000000000000000000000000000
`str.guid(1)` return 00000000-0000-0000-0000-000000000000
`str.guid(2)` return {00000000-0000-0000-0000-000000000000}
`str.guid(3)` return (00000000-0000-0000-0000-000000000000)

##### str.capitalize

Returns a new capitalize string

Syntax

`str.capitalize('hello world')` return "Hello World"

##### str.res

Returns a string resource from the executable file. _"shell32.dll" is the default file._

Syntax

`str.res(-4640)` return "Runs the selected command with elevation" from "shell32.dll"
`str.res('explorer.dll', -22000)` return "Desktop"

* * *

<!-- ============================================================================= -->
<!-- FUNCTIONS: SYS -->
<!-- ============================================================================= -->
Title: sys - Shell

URL Source: https://nilesoft.org/docs/functions/SYS

Markdown Content:
##### SYS (SYSTEM)

##### sys.name

Returns Windows name.

Syntax

`sys.name`

##### sys.type

Returns system architecture.

Syntax

`sys.type == 64`
`sys.type == 32`

##### sys.is64

Returns if system architecture is x64.

Syntax

`sys.is64`

##### sys.dark

Check if dark mode is enabled.

Syntax

`sys.dark`

##### sys.var

Retrieves the value of an environment variable.

Syntax

`sys.var('WINDIR')`

##### sys.version (sys.ver)

Windows version.

Syntax

```JS
sys.version
sys.version.build
sys.version.major
sys.version.minor
sys.version.name
sys.version.type
```

##### sys.datetime

Date and time format

Syntax

```JS
sys.datetime
sys.datetime.date
sys.datetime.date_day
sys.datetime.date_dayofweek
sys.datetime.date_month
sys.datetime.date_y
sys.datetime.date_year
sys.datetime.date_yy
sys.datetime.short
sys.datetime.time
sys.datetime.time_hour
sys.datetime.time_milliseconds
sys.datetime.time_min
sys.datetime.time_minute
sys.datetime.time_ms
sys.datetime.time_pm
sys.datetime.time_second
```

##### Windows paths

```JS
sys.appdata
sys.bin
sys.bin32
sys.bin64
sys.directory (sys.dir)
sys.path
sys.prog
sys.prog32
sys.programdata
sys.root
sys.temp
sys.templates
sys.users
sys.wow
```

##### sys.is\_primary\_monitor

Returns true if the current monitor is the primary

Syntax

`sys.is_primary_monitor`

* * *

<!-- ============================================================================= -->
<!-- FUNCTIONS: THIS -->
<!-- ============================================================================= -->
Title: this - Shell

URL Source: https://nilesoft.org/docs/functions/this

Markdown Content:
##### THIS

This namespace contains functions that are used with the current item in the context menu.

##### Syntax

```JS
this.type	// Returns the type of the current item [item = 0, menu = 1, separator = 2]
this.checked	// Returns true if the current item is checked
this.pos	// Returns the position of the current item in the context menu
this.disabled	// Returns true if the current item is disabled
this.sys	// Returns true if the current item is a system item
this.title	// Returns the title of the current item
this.id		// Returns the ID of the current item
this.count	// Returns the number of items in the context menu
this.length	// Returns the length of the current item's title
```

* * *
*
<!-- ============================================================================= -->
<!-- FUNCTIONS: USER -->
<!-- ============================================================================= -->
Title: user - Shell

URL Source: https://nilesoft.org/docs/functions/user

Markdown Content:
##### USER

##### user.name

Returns the current username.

Syntax

`user.name`

##### Functions to return the path of user directories

Syntax

```JS
user.home
user.appdata
user.contacts
user.desktop
user.directory (user.dir)
user.documents
user.documentslibrary
user.downloads
user.favorites
user.libraries
user.localappdata
user.music
user.personal
user.pictures
user.profile
user.quicklaunch
user.sendto
user.startmenu
user.temp
user.templates
user.videos
```

* * *

This page is **open source**. Noticed a typo? Or something unclear?
[Improve this page on GitHub](https://github.com/moudey/shell/blob/main/docs/functions/user.html)

<!-- ============================================================================= -->
<!-- FUNCTIONS: WINDOW -->
<!-- ============================================================================= -->
Title: window - Shell

URL Source: https://nilesoft.org/docs/functions/window

Markdown Content:
##### WINDOW, WND

##### Syntax

```JS
window.is_taskbar	// Returns true if the window handle is for the Taskbar window
window.is_desktop	// Returns true if the window handle is for the Desktop window
window.is_explorer	// Returns true if the window handle is for the Explorer window
window.is_tree		// Returns true if the window handle is for the Side window
window.is_edit		// Returns true if the window handle is for the Edit menu
window.is_start		// Returns true if the window handle is for the Win+X menu

window.send(name, msg, wparam, lparam)	// Search for the window by name and send the specified message
window.post(name, msg, wparam, lparam)	// Search for the window by name and send the specified message without waiting

window.send(null, msg, wparam, lparam)	// Send the specified message to current window.
window.post(null, msg, wparam, lparam)	// Send the specified message without waiting to current window.

window.command(command)	// Send WM_COMMAND to current window.
window.command(command, name)	// Search for the window by name and send the command to it.

window.handle
window.name
window.title
window.owner
window.parent
window.parent.handle
window.parent.name
window.is_contextmenuhandler
```

* * *

<!-- ============================================================================= -->
<!-- FUNCTIONS: CLIPBOARD -->
<!-- ============================================================================= -->
Title: clipboard - Shell

URL Source: https://nilesoft.org/docs/functions/clipboard

Markdown Content:
##### CLIPBOARD

Clipboard handling functions.

##### clipboard.get

Returns the value of the stored clipboard.

Syntax

`clipboard.get`

##### clipboard.set

Store a value in the clipboard.

Syntax

`clipboard.set("Hello world!")`

##### clipboard.length

Returns the length of the value stored in the clipboard.

Syntax

`clipboard.length`

##### clipboard.is\_empty

Verifies that there is a value stored in the clipboard.

Syntax

`clipboard.is_empty`

##### clipboard.empty

Empty values stored in the clipboard.

Syntax

`clipboard.empty`

* * *

This page is **open source**. Noticed a typo? Or something unclear?
[Improve this page on GitHub](https://github.com/moudey/shell/blob/main/docs/functions/clipboard.html)

<!-- ============================================================================= -->
<!-- FUNCTIONS: INPUT -->
<!-- ============================================================================= -->
Title: input - Shell

URL Source: https://nilesoft.org/docs/functions/input

Markdown Content:
##### INPUT

The input box allows the user to enter and pass data.

##### input

Show the input box with the window title and call parameter passed.
The function returns a non-zero value if the OK button is pressed. Otherwise, it returns zero.

Syntax

`input("Title", "Prompt")`

##### input.result

Returns the input value resulting from the input box.

Syntax

`input.result`

* * *

This page is **open source**. Noticed a typo? Or something unclear?
[Improve this page on GitHub](https://github.com/moudey/shell/blob/main/docs/functions/input.html)

<!-- ============================================================================= -->
<!-- FUNCTIONS: INI -->
<!-- ============================================================================= -->
Title: ini - Shell

URL Source: https://nilesoft.org/docs/functions/ini

Markdown Content:
##### INI

Functions for handleing with .ini files.

##### ini.get

Returns the value of the key

Syntax

`ini.get('path\to\ini file', "section", "key")`

##### ini.set

Set the key value.

Syntax

`ini.set('path\to\ini file', "section", "key", "value")`

* * *

<!-- ============================================================================= -->
<!-- FUNCTIONS: REGEX -->
<!-- ============================================================================= -->
Title: regex - Shell

URL Source: https://nilesoft.org/docs/functions/regex

Markdown Content:
##### REGEX

regex functions

##### regex.match

Returns true if a match exists, false otherwise.

Syntax

regex.match(str, pattern)

##### regex.matches

Returns an array of strings

Syntax

regex.matches(str, pattern)

##### regex.replace

Syntax

regex.replace(str, pattern, "new str")

* * *
*
<!-- ============================================================================= -->
<!-- EXAMPLES: COPY PATH -->
<!-- ============================================================================= -->
Title: Copy path - Shell

URL Source: https://nilesoft.org/docs/examples/copy-path

Markdown Content:
##### Copy Path example

```JS
// type can set with '~taskbar' equals all file types except taskbar.
menu(type='file|dir|back|root|namespace' mode="multiple"  title='copy to clipboard' image=#ff00ff)
{
    // Appears only when multiple selections.
    item(vis=@(sel.count > 1) title='Copy path (@sel.count) items selected'
        cmd=command.copy(sel(false, "\n")))
    
    item(mode="single" title=sel.path
        cmd=command.copy(sel.path))
    
    item(mode="single" type='file|dir|back.dir'
        vis=sel.short.len!=sel.path.len title=sel.short
        cmd=command.copy(sel.short))
    separator
    item(mode="single" vis=@(sel.parent.len>3) title=sel.parent
        cmd=command.copy(sel.parent))
    separator
    item(mode="single" type='file|dir|back.dir' title=sel.file.name
        cmd=command.copy(sel.file.name))
    
    item(mode="single" type='file' title=sel.file.title
        cmd=command.copy(sel.file.title))
    
    item(mode="single" type='file' title=sel.file.ext
        cmd=command.copy(sel.file.ext))
}
```

![Image 1: a screenshot of the windows 10 copy and paste menu](https://nilesoft.org/docs/images/copypath1.png) ![Image 2: a window showing the windows 10 system settings](https://nilesoft.org/docs/images/copypath2.png) ![Image 3: copy path selected - screenshot](https://nilesoft.org/docs/images/copypath3.png)

* * *

<!-- ============================================================================= -->
<!-- EXAMPLES: FAVORITES -->
<!-- ============================================================================= -->
Title: Favorites - Shell

URL Source: https://nilesoft.org/docs/examples/favorites

Markdown Content:
##### Favorite applications and directories example

```JS
menu(type='desktop|taskbar' title='Favorites' image=#00ff00)
{
    menu(title='Applications' image=#ff0000)
    {
        item(title='Command prompt' image cmd='cmd.exe')
        item(title='PowerShell' image cmd='powershell.exe')
        item(title='Registry editor' image cmd='regedit.exe')
        separator
        item(title='Paint' image cmd='mspaint.exe')
        item(title='Notepad' image cmd='notepad.exe')
    }
    separator
    menu(title='Directories' image=#0000ff)
    {
        item(title='Downloads' cmd=user.downloads)
        item(title='Pictures' cmd=user.pictures)
        item(title='Home' cmd=user.directory)
        separator
        item(title='Windows' cmd=sys.directory)
        item(title='Program files' cmd=sys.prog())
    }
}
```

![Image 1: a screenshot of the settings menu in windows 10](https://nilesoft.org/docs/images/fav1.png) ![Image 2: a screenshot of the application menu in windows vista](https://nilesoft.org/docs/images/fav2.png)

* * *

<!-- ============================================================================= -->
<!-- EXAMPLES: GOTO -->
<!-- ============================================================================= -->
Title: Go to - Shell

URL Source: https://nilesoft.org/docs/examples/goto

Markdown Content:
##### Goto example



```JS
menu(mode="multiple" title='Goto' sep="both" image= \uE14A)
{
    menu(title='Folder' image=\uE1F4)
    {
        item(title='Windows' image=inherit cmd=sys.dir)
        item(title='System' image=inherit cmd=sys.bin)
        item(title='Program Files' image=inherit cmd=sys.prog)
        item(title='Program Files x86' image=inherit cmd=sys.prog32)
        item(title='ProgramData' image=inherit cmd=sys.programdata)
        item(title='Applications' image=inherit cmd='shell:appsfolder')
        item(title='Users' image=inherit cmd=sys.users)
        separator
        item(title='Desktop' image=inherit cmd=user.desktop)
        item(title='Downloads' image=inherit cmd=user.downloads)
        item(title='Pictures' image=inherit cmd=user.pictures)
        item(title='Documents' image=inherit cmd=user.documents)
        item(title='Startmenu' image=inherit cmd=user.startmenu)
        item(title='Profile' image=inherit cmd=user.dir)
        item(title='AppData' image=inherit cmd=user.appdata)
        item(title='Temp' image=inherit cmd=user.temp)
    }
    
    item(title=title.control_panel image=\uE0F3 cmd='shell:::{5399E694-6CE5-4D6C-8FCE-1D8870FDCBA0}')
    item(title='All Control Panel Items' image=\uE0F3 cmd='shell:::{ED7BA470-8E54-465E-825C-99712043E01C}')
    item(title=title.run image=\uE14B cmd='shell:::{2559a1f3-21d7-11d4-bdaf-00c04f60b9f0}')
    
    menu(title=title.settings sep="before" image=id.settings.icon)
    {
        // https://docs.microsoft.com/en-us/windows/uwp/launch-resume/launch-settings-app
        item(title='systeminfo' image=inherit cmd arg='/K systeminfo')
        item(title='search' cmd='search-ms:' image=inherit)
        item(title='settings' image=inherit cmd='ms-settings:')
        item(title='about' image=inherit cmd='ms-settings:about')
        item(title='usb' image=inherit cmd='ms-settings:usb')
        item(title='network-status' image=inherit cmd='ms-settings:network-status')
        item(title='network-ethernet' image=inherit cmd='ms-settings:network-ethernet')
        item(title='personalization-background' image=inherit cmd='ms-settings:personalization-background')
        item(title='personalization-colors' image=inherit cmd='ms-settings:colors')
        item(title='lockscreen' image=\uE0F3 cmd='ms-settings:lockscreen')
        item(title='personalization-start' image=inherit cmd='ms-settings:personalization-start')
        item(title='appsfeatures' image=inherit cmd='ms-settings:appsfeatures')
        item(title='optionalfeatures' image=inherit cmd='ms-settings:optionalfeatures')
        item(title='defaultapps' image=inherit cmd='ms-settings:defaultapps')
        item(title='yourinfo' image=inherit cmd='ms-settings:yourinfo')
        item(title='windowsupdate' image=inherit cmd='ms-settings:windowsupdate')
        item(title='windowsdefender' image=inherit cmd='ms-settings:windowsdefender')
        item(title='network connections' image=inherit cmd='shell:::{7007ACC7-3202-11D1-AAD2-00805FC1270E}')
    }
}
```

![Image 1: a black screen with a number of items on it](https://nilesoft.org/docs/images/goto.png)

* * *