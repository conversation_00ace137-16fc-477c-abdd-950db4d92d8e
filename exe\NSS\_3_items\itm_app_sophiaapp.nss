
//
$APP_SOPHIAAPP_NSS = '@app.dir\NSS\_3_items\user_apps'
$APP_SOPHIAAPP_SRC = '@user.desktop\my\flow\home\__GOTO__\Apps\app_sophiaapp'
//
$APP_USER_SOPHIAAPP_DIR = '@user.desktop\my\flow\home\__GOTO__\Apps\app_sophiaapp\exe'
$APP_USER_SOPHIAAPP_EXE = '@APP_USER_SOPHIAAPP_DIR\SophiApp.exe'
$APP_USER_SOPHIAAPP_TIP = "..."+str.trimstart('@APP_USER_SOPHIAAPP_EXE','@app.dir')

// Context: Taskbar
item(
    title  = ":  &SophiaApp"
    keys   = "exe"
    type   = 'Taskbar'
    args   = ''
    //
    image  = APP_USER_SOPHIAAPP_EXE
    tip    = [APP_USER_SOPHIAAPP_TIP,TIP3,0.8]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_USER_SOPHIAAPP_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_SOPHIAAPP_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_SOPHIAAPP_EXE')),
        cmd=if(KEYS_EXE_GOTO_NSS,('@APP_SOPHIAAPP_NSS')),
        cmd=if(KEYS_EXE_GOTO_SRC,('@APP_SOPHIAAPP_SRC')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_SOPHIAAPP_DIR')),
    }
)
