menu(type='*' where=window.is_taskbar||sel.count mode=mode.multiple title=title.go_to sep=sep.both image=\uE14A)
{
	separator
	item(title='Desktop' image=\uE265 cmd='shell:::{B4BFCC3A-DB2C-424C-B029-7FE99A87C641}')
	separator
	item(title='Recycle Bin' image=\uE0B4 cmd='shell:::{645FF040-5081-101B-9F08-00AA002F954E}')
	separator
	item(title=title.control_panel image=\uE0F1 cmd='shell:::{5399E694-6CE5-4D6C-8FCE-1D8870FDCBA0}')
	separator
	item(title='Network Adapters' image=\uE180 cmd='ncpa.cpl')
	separator
	item(title='Installed Applications' image=\uE254 cmd='shell:appsfolder')
	separator
	item(title=title.run image=\uE14B cmd='shell:::{2559a1f3-21d7-11d4-bdaf-00c04f60b9f0}')
	separator
	menu(title='Folders' image=\uE1F4)
	{
		item(title='Desktop' image=inherit cmd='shell:::{B4BFCC3A-DB2C-424C-B029-7FE99A87C641}')
		separator
		item(title='Program Files' image=inherit cmd=sys.prog)
		item(title='Program Files x86' image=inherit cmd=sys.prog32)
		separator
		item(title='AppData' image=inherit cmd=user.appdata)
		item(title='LocalAppData' image=inherit cmd=user.localappdata)
		item(title='ProgramData' image=inherit cmd=sys.programdata)
		separator
		item(title='Users' image=inherit cmd=sys.users)
		item(title='UserProfile' image=inherit cmd=user.dir)
		separator
		item(title='Downloads' image=inherit cmd=user.downloads)
		item(title='Pictures' image=inherit cmd=user.pictures)
		item(title='Documents' image=inherit cmd=user.documents)
		item(title='Startmenu' image=inherit cmd=user.startmenu)
		item(title='Temp' image=inherit cmd=user.temp)
		separator
		item(title='All Control Panel Items' image=inherit cmd='shell:::{ED7BA470-8E54-465E-825C-99712043E01C}')
	}
	menu(where=sys.ver.major >= 10 title=title.settings sep=sep.before image=\uE0F3)
	{
		// https://docs.microsoft.com/en-us/windows/uwp/launch-resume/launch-settings-app
		item(title='System' image=\uE0F4 cmd='ms-settings:')
		item(title='About' image=\uE0F4 cmd='ms-settings:about')
		item(title='Your Info' image=\uE0F4 cmd='ms-settings:yourinfo')
		item(title='System Info' image=\uE0F4 cmd-line='/K systeminfo')
		item(title='Search' cmd='search-ms:' image=\uE0F4)
		item(title='USB' image=\uE0F4 cmd='ms-settings:usb')
		item(title='Windows Update' image=\uE0F4 cmd='ms-settings:windowsupdate')
		item(title='Windows Defender' image=\uE0F4 cmd='ms-settings:windowsdefender')
		menu(title='Apps' image=\uE0F4)
		{
			item(title='Apps Features' image=\uE0F4 cmd='ms-settings:appsfeatures')
			item(title='Default Apps' image=\uE0F4 cmd='ms-settings:defaultapps')
			item(title='Optional Features' image=\uE0F4 cmd='ms-settings:optionalfeatures')
			item(title='Startup' image=\uE0F4 cmd='ms-settings:startupapps')
		}
		menu(title='Personalization' image=\uE0F4)
		{
			item(title='Personalization' image=\uE0F4 cmd='ms-settings:personalization')
			item(title='Lockscreen' image=\uE0F4 cmd='ms-settings:lockscreen')
			item(title='Background' image=\uE0F4 cmd='ms-settings:personalization-background')
			item(title='Colors' image=\uE0F4 cmd='ms-settings:colors')
			item(title='Themes' image=\uE0F4 cmd='ms-settings:themes')
			item(title='Start' image=\uE0F4 cmd='ms-settings:personalization-start')
			item(title='Taskbar' image=\uE0F4 cmd='ms-settings:taskbar')
		}
		menu(title='Network' image=\uE0F4)
		{
			item(title='Status' image=\uE0F4 cmd='ms-settings:network-status')
			item(title='Ethernet' image=\uE0F4 cmd='ms-settings:network-ethernet')
			item(title='Connections' image=\uE0F4 cmd='shell:::{7007ACC7-3202-11D1-AAD2-00805FC1270E}')
		}
	}
}