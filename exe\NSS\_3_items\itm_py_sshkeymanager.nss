
//
$PY_SSHKEYMANAGER_DIR = '@user.desktop\my\flow\home\__GOTO__\Scripts\Python\Py_Cli_Utils\py__SshKeyManager'
$PY_SSHKEYMANAGER_EXE = '@PY_SSHKEYMANAGER_DIR\ssh_key_manager\.venv\Scripts\python.exe'
$PY_SSHKEYMANAGER_APP = '@PY_SSHKEYMANAGER_DIR\ssh_key_manager\src\main.py'
//

// Context: Explorer
$PY_SSHKEYMANAGER_EXPLORER = ''
// $PY_SSHKEYMANAGER_EXPLORER = '-op "@sel.dir" --prompt'
item(
    title="&SSHKeyManager"
    keys="py"
    type='File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    //
    image=[E17C,GREY] image-sel=[E17C,PURPLE]
    tip=['"@PY_SSHKEYMANAGER_APP" @PY_SSHKEYMANAGER_EXPLORER',TIP3,0.75]
    //
    admin=keys.rbutton()
    cmd=if(KEYS_EXE_OPEN_EXE,('"@PY_SSHKEYMANAGER_EXE"'))
    args='"@PY_SSHKEYMANAGER_APP" @PY_SSHKEYMANAGER_EXPLORER'
    //
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@PY_SSHKEYMANAGER_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@PY_SSHKEYMANAGER_APP')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@PY_SSHKEYMANAGER_DIR')),
    }
)
// Context: Taskbar
$PY_SSHKEYMANAGER_TASKBAR = ''
// $PY_SSHKEYMANAGER_TASKBAR = '-op "@user.desktop" --prompt'
item(
    title="&SSHKeyManager"
    keys="py"
    type='Taskbar'
    //
    image=[E17C,GREY]
    image-sel=[E17C,PURPLE]
    //
    admin=keys.rbutton()
    cmd=if(KEYS_EXE_OPEN_EXE,('"@PY_SSHKEYMANAGER_EXE"'))
    args='"@PY_SSHKEYMANAGER_APP" @PY_SSHKEYMANAGER_TASKBAR'
    tip=['"@PY_SSHKEYMANAGER_APP" @PY_SSHKEYMANAGER_TASKBAR',TIP3,0.75]
    //
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@PY_SSHKEYMANAGER_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@PY_SSHKEYMANAGER_APP')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@PY_SSHKEYMANAGER_DIR')),
    }
)

