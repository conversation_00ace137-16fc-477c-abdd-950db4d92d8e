
//
$PY_DIRECTORYCLEANER_DIR = '@user.desktop\my\flow\home\__GOTO__\Scripts\Python\Py_Cli_Utils\py__DirectoryCleaner'
$PY_DIRECTORYCLEANER_EXE = '@PY_DIRECTORYCLEANER_DIR\venv\Scripts\python.exe'
$PY_DIRECTORYCLEANER_APP = '@PY_DIRECTORYCLEANER_DIR\src\directory_cleaner.py'
//
// $CMD_GIT_ADD_SELECTION_F_V = '/K (CD /D "@sel.dir") && (git add @BATCH_SEL_AS_STRING -f) && (@BATCH_EXIT_WITH_MSG) && PAUSE'

// Context: Explorer
$PY_DIRECTORYCLEANER_EXPLORER = '"@sel.dir" --prompt'
item(
    title="&DirectoryCleaner"
    keys="py"
    type='File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    //
    image=[E17C,GREY] image-sel=[E17C,PURPLE]
    tip=['"@PY_DIRECTORYCLEANER_APP" @PY_DIRECTORYCLEANER_EXPLORER',TIP3,0.75]
    //
    admin=keys.rbutton()
    cmd=if(KEYS_EXE_OPEN_EXE,('"@PY_DIRECTORYCLEANER_EXE"'))
    args='"@PY_DIRECTORYCLEANER_APP" @PY_DIRECTORYCLEANER_EXPLORER'
    //
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@PY_DIRECTORYCLEANER_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@PY_DIRECTORYCLEANER_APP')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@PY_DIRECTORYCLEANER_DIR')),
    }
)
// Context: Taskbar
$PY_DIRECTORYCLEANER_TASKBAR = '"@user.desktop" --prompt'
item(
    title="&DirectoryCleaner"
    keys="py"
    type='Taskbar'
    //
    image=[E17C,GREY]
    image-sel=[E17C,PURPLE]
    //
    admin=keys.rbutton()
    cmd=if(KEYS_EXE_OPEN_EXE,('"@PY_DIRECTORYCLEANER_EXE"'))
    args='"@PY_DIRECTORYCLEANER_APP" @PY_DIRECTORYCLEANER_TASKBAR'
    tip=['"@PY_DIRECTORYCLEANER_APP" @PY_DIRECTORYCLEANER_TASKBAR',TIP3,0.75]
    //
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@PY_DIRECTORYCLEANER_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@PY_DIRECTORYCLEANER_APP')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@PY_DIRECTORYCLEANER_DIR')),
    }
)
