// Settings
settings
{
	priority=1
	exclude.where = !process.is_explorer
	showdelay = 200
	modify.remove.duplicate=1
	tip.enabled=true
}



// ----------------------------------------------------------------------------
// CLEANUP FILE-EXPLORER
// ----------------------------------------------------------------------------
menu(type='~taskbar' title='ORG' pos='top' sep='bottom' image=[\uE1B8, #000000]) {}
modify(
    where=!this.id(
		// -> Basic Operations
		id.delete, id.edit, id.new, id.new_folder, id.new_item, id.open,
		id.open_with, id.properties, id.rename,

		// -> Clipboard Operations
		id.copy_as_path, id.copy_path, id.copy, id.cut, id.paste,
		id.paste_shortcut,

		// -> Movement and Location
		id.copy_here, id.copy_to, id.copy_to_folder, id.move_here, id.move_to,
		id.move_to_folder, id.open_file_location, id.open_folder_location,

		// -> Advanced File Operations
		id.compressed, id.create_shortcut, id.create_shortcuts_here,
		id.extract_all, id.extract_to, id.restore_previous_versions,

		// -> Icon and Display Settings
		id.extra_large_icons, id.large_icons, id.medium_icons, id.small_icons,
		id.list, id.details, id.tiles, id.content,

		// -> Organization and Sorting
		id.arrange_by, id.group_by, id.sort_by,

		// -> Icon Management
		id.align_icons_to_grid, id.auto_arrange_icons,

		// -> Visibility and Customization
		id.customize_notification_icons, id.customize_this_folder,
		id.show_cortana_button, id.show_desktop_icons, id.show_file_extensions,
		id.show_hidden_files, id.show_libraries, id.show_network,
		id.show_people_on_the_taskbar, id.show_task_view_button,
		id.show_touch_keyboard_button, id.show_touchpad_button,

		// -> System Tools
		id.adjust_date_time, id.control_panel, id.device_manager,
		id.display_settings, id.file_explorer, id.folder_options,
		id.power_options, id.settings, id.task_manager, id.taskbar_settings,

		// -> Personalization
		id.desktop, id.options, id.personalize,

		// -> Network Operations
		id.cast_to_device, id.disconnect, id.disconnect_network_drive,
		id.map_as_drive, id.map_network_drive,

		// -> Sharing and Accessibility
		id.give_access_to, id.make_available_offline, id.make_available_online,
		id.share, id.share_with,

		// -> Command Line Tools
		id.command_prompt, id.open_command_prompt, id.open_command_window_here,
		id.open_powershell_window_here, id.open_windows_powershell,

		// -> System Utilities
		id.cleanup, id.refresh, id.run, id.run_as_administrator,
		id.run_as_another_user, id.search, id.troubleshoot_compatibility,

		// -> Security Tools
		id.install, id.manage, id.turn_off_bitlocker, id.turn_on_bitlocker,

		// -> Device Operations
		id.autoplay, id.eject, id.erase_this_disc, id.mount,

		// -> Media Actions
		id.play, id.print,

		// -> Window Arrangement
		id.cascade_windows, id.show_windows_side_by_side,
		id.show_windows_stacked,

		// -> Taskbar Management
		id.lock_all_taskbars, id.lock_the_taskbar,

		// -> Windows Features
		id.cortana, id.news_and_interests, id.send_to, id.store,

		// -> General
		id.add_a_network_location, id.cancel, id.collapse,
		id.collapse_all_groups, id.collapse_group, id.configure,
		id.empty_recycle_bin, id.exit_explorer, id.expand, id.expand_all_groups,
		id.expand_group, id.format, id.include_in_library,
		id.insert_unicode_control_character, id.merge, id.more_options,
		id.next_desktop_background, id.open_as_portable, id.open_autoplay,
		id.open_in_new_process, id.open_in_new_tab, id.open_in_new_window,
		id.open_new_tab, id.open_new_window,
		id.pin_current_folder_to_quick_access, id.pin_to_quick_access,
		id.pin_to_start, id.pin_to_taskbar, id.preview, id.reconversion,
		id.redo, id.remove_from_quick_access, id.remove_properties, id.restore,
		id.restore_default_libraries, id.rotate_left, id.rotate_right,
		id.select_all, id.set_as_desktop_background,
		id.set_as_desktop_wallpaper, id.shield, id.show_pen_button,
		id.show_the_desktop, id.show_this_pc, id.undo,
		id.unpin_from_quick_access, id.unpin_from_start, id.unpin_from_taskbar,
		id.view
    ) menu='ORG'
)



// Run
menu(type='~taskbar' title="Run" pos=indexof('ORG', 1) image=image.fluent("\uE77B", 12, #22A7F2)) {}
modify(where=this.id==id.run_as_administrator image=image.fluent("\uE77B", 12, #22A7F2) menu="Run")
modify(where=this.id==id.open image=image.fluent("\uE8E5", 12, #22A7F2) menu="Run")
modify(where=this.id==id.open_with image=image.fluent("\uE7AC", 12, #22A7F2) menu="Run")
modify(where=this.id==id.open_in_new_tab image=image.fluent("\uE8A0", 12, #22A7F2) menu="Run")
modify(where=this.id==id.open_in_new_window image=image.fluent("\uE838", 12, #22A7F2) menu="Run")

// Get
menu(type='~taskbar' title="Get" pos=indexof('ORG', 2) image=image.fluent("\uE77B", 12, #39c65a)) {}
modify(where=this.id==id.copy_path image=image.fluent(\uE8C8, 12, #39c65a) menu="Get")
modify(where=this.id==id.copy_as_path image=image.fluent(\uE8C8, 12, #39c65a) menu="Get")

// Actions
menu(type='~taskbar' title="Actions" pos=indexof('ORG', 3) sep="after" image=image.fluent("\uE77B", 12, #f1a374)) {}
modify(where=this.id==id.edit image=image.fluent("\uE70F", 12, #f1a374) menu="Actions")
modify(where=this.id==id.delete image=image.fluent(\uE74D, 12, #f1a374) menu="Actions")


// Root
modify(where=this.id==id.copy pos=indexof('Actions', 1) image=image.fluent(\uE8C8, 12, #39c65a))
modify(where=this.id==id.paste pos=indexof('Actions', 2) image=image.fluent(\uE77F, 12, #f1a374))
modify(where=this.id==id.cut pos=indexof('Actions', 3) image=image.fluent(\uE8C6, 12, #f76e6e))



// #f76e6e
// // Run as administrator
// modify(
// 	where=this.id==id.run_as_administrator
// 	pos=indexof('ORG', 1)
// 	image=image.fluent("\uE77B", 12, #f1a374)
// 	title="Run as administrator \t –"
// )
// // Edit
// modify(
// 	where=this.id==id.edit
// 	pos=indexof('ORG', 2)
// 	image=image.fluent("\uE8E5", 12, #f1a374)
// 	title="Edit \t –"
// )
// // Open
// modify(
// 	where=this.id==id.open
// 	pos=indexof('ORG', 3)
// 	image=image.fluent("\uE8E5", 12, #22A7F2)
// 	title="Open \t –"
// )
// modify(
// 	where=this.id==id.open_with
// 	pos=indexof('ORG', 4)
// 	image=image.fluent("\uE7AC", 12, #22A7F2)
// 	title="Open With"
// )
// modify(
// 	where=this.id==id.open_in_new_tab
// 	pos=indexof('ORG', 5)
// 	image=[icon.open_in_new_tab, #22A7F2]
// 	title="Open in new tab \t –"
// )
// modify(
// 	where=this.id==id.open_in_new_window
// 	pos=indexof('ORG', 6)
// 	image=[icon.open_in_new_window, #22A7F2]
// 	title="Open in new window \t –"
// )
// modify(type='file' where=this.id==id.open_with pos=indexof('ORG', 2) image=image.fluent(\uE7AC, 12) title="Open With" separator="none")
// separator pos=indexof('ORG', 3)
// ----------------------------------------------------------------------------
// IMPORT: Initialize images/icons
// ----------------------------------------------------------------------------
import 'imports/shell_themes/theme_blue.nss'
import 'imports/images.nss'

import 'imports/shell_menus/menu_taskbar.nss'
import 'imports/shell_menus_external/shell-icons.nss'
