
/* item: singles/apps/audacity */

//
$APP_AUDACITY_DIR = '@app.dir\PORTAL\APPS\app_audacity\exe'
$APP_AUDACITY_EXE = '@APP_AUDACITY_DIR\Audacity.exe'
$APP_AUDACITY_TIP = "..."+str.trimstart('@APP_AUDACITY_EXE','@app.dir')

// context: file
item(
    title  = ":  &Audacity"
    keys   = "exe"
    type   = 'File'
    args   = '"@sel.file"'
    where  = str.equals(sel.file.ext,[".aac",".flac",".m4a",".mp3",".ogg",".wav",".wma"])
    //
    image  = APP_AUDACITY_EXE
    tip    = [APP_AUDACITY_TIP,TIP3,1.0]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_AUDACITY_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_AUDACITY_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_AUDACITY_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_AUDACITY_DIR')),
    }
)
// Context: Taskbar
item(
    title  = ":  &Audacity"
    keys   = "exe"
    type   = 'Taskbar'
    args   = ''
    //
    image  = APP_AUDACITY_EXE
    tip    = [APP_AUDACITY_TIP,TIP3,1.0]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_AUDACITY_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_AUDACITY_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_AUDACITY_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_AUDACITY_DIR')),
    }
)
