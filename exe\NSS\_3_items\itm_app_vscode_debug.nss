
//
$APP_VSCODEDEBUG_NSS = 'C:\Users\<USER>\Desktop\SCRATCH\app_vscode'
// $APP_VSCODEDEBUG_NSS = '@app.dir\NSS\_3_items\user_apps'
$APP_VSCODEDEBUG_SRC = 'C:\Users\<USER>\Desktop\SCRATCH\app_vscode'
// $APP_VSCODEDEBUG_SRC = '@user.desktop\my\flow\home\__GOTO__\Apps\app_vscode'
//
$APP_USER_VSCODEDEBUG_DIR = 'C:\Users\<USER>\Desktop\SCRATCH\app_vscode\exe'
// $APP_USER_VSCODEDEBUG_DIR = '@user.desktop\my\flow\home\__GOTO__\Apps\app_vscode\exe'
$APP_USER_VSCODEDEBUG_EXE = '@APP_USER_VSCODEDEBUG_DIR\Code.exe'
$APP_USER_VSCODEDEBUG_TIP = "..."+str.trimstart('@APP_USER_VSCODEDEBUG_EXE','@app.dir')

// -> context: file
item(
    title  = ":  &VSCode [TEMP]"
    keys   = "exe"
    type   = 'File'
    args   = '"@sel.file"'
    where  = !str.equals(sel.file.ext,[".7z",".zip",".rar",".apk",".bin",".dll",".dmg",".exe",".sys"])
    //
    image  = APP_USER_VSCODEDEBUG_EXE
    tip    = [APP_USER_VSCODEDEBUG_TIP,TIP3,0.8]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_USER_VSCODEDEBUG_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_VSCODEDEBUG_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_VSCODEDEBUG_EXE')),
        cmd=if(KEYS_EXE_GOTO_NSS,('@APP_VSCODEDEBUG_NSS')),
        cmd=if(KEYS_EXE_GOTO_SRC,('@APP_VSCODEDEBUG_SRC')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_VSCODEDEBUG_DIR')),
    }
)
// context: directory
item(
    title  = ":  &VSCode [TEMP]"
    keys   = "exe"
    type   = 'Desktop|Dir|Drive|Back.Dir|Back.Drive'
    args   = '"@sel.dir"'
    //
    image  = APP_USER_VSCODEDEBUG_EXE
    tip    = [APP_USER_VSCODEDEBUG_TIP,TIP3,0.8]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_USER_VSCODEDEBUG_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_VSCODEDEBUG_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_VSCODEDEBUG_EXE')),
        cmd=if(KEYS_EXE_GOTO_NSS,('@APP_VSCODEDEBUG_NSS')),
        cmd=if(KEYS_EXE_GOTO_SRC,('@APP_VSCODEDEBUG_SRC')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_VSCODEDEBUG_DIR')),
    }
)
// Context: Taskbar
item(
    title  = ":  &VSCode [TEMP]"
    keys   = "exe"
    type   = 'Taskbar'
    args   = ''
    //
    image  = APP_USER_VSCODEDEBUG_EXE
    tip    = [APP_USER_VSCODEDEBUG_TIP,TIP3,0.8]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_USER_VSCODEDEBUG_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_VSCODEDEBUG_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_VSCODEDEBUG_EXE')),
        cmd=if(KEYS_EXE_GOTO_NSS,('@APP_VSCODEDEBUG_NSS')),
        cmd=if(KEYS_EXE_GOTO_SRC,('@APP_VSCODEDEBUG_SRC')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_VSCODEDEBUG_DIR')),
    }
)

