
//
$APP_UTIL_WIN4RO1_DIR = '@user.desktop\my\flow\home\__GOTO__\Scripts\Python\Py_Ai_Utils\py__GptReasoningChains\win4r.o1'
$APP_UTIL_WIN4RO1_RUN = '@APP_UTIL_WIN4RO1_DIR\run.bat'
$APP_UTIL_WIN4RO1_TIP = "..."+str.trimstart('@APP_UTIL_WIN4RO1_RUN','@app.dir')

// Context: Explorer
item(
    title  = ":  &win4r.o1"
    keys   = "py"
    type   = 'File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    // args   = '"@sel.dir"'
    //
    image  = [E17C,GREY] image-sel=[E17C,PURPLE]
    tip    = [APP_UTIL_WIN4RO1_TIP,TIP3,0.8]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_UTIL_WIN4RO1_RUN"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_UTIL_WIN4RO1_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_UTIL_WIN4RO1_RUN')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_UTIL_WIN4RO1_DIR')),
    }
)
// Context: Taskbar
item(
    title  = ":  &win4r.o1"
    keys   = "py"
    type   = 'Taskbar'
    // args   = ''
    //
    image  = [E17C,GREY] image-sel=[E17C,PURPLE]
    tip    = [APP_UTIL_WIN4RO1_TIP,TIP3,0.8]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_UTIL_WIN4RO1_RUN"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_UTIL_WIN4RO1_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_UTIL_WIN4RO1_RUN')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_UTIL_WIN4RO1_DIR')),
    }
)

