
// docs: `https://nilesoft.org/docs/configuration/properties#keys`

/*
    :: key-combinations for directories

    :: usage:
        cmd=if(KEYS_DIR_OPEN,('@app.dir')),
        commands{
            cmd=if(KEYS_DIR_COPY,clipboard.set('@app.dir')),
            cmd=if(KEYS_DIR_GOTO,command.navigate('@app.dir')),
        }
*/
$KEYS_DIR_COPY = (key(key.control, key.lbutton) OR key(key.control, key.rbutton)) // [Ctrl + Left/Right]
$KEYS_DIR_GOTO = ((!key(key.control) AND window.is_explorer) AND !key(key.shift)) // [Explorer, -Ctrl, -Shift]
$KEYS_DIR_OPEN = ((!key(key.control) AND !window.is_explorer) OR key(key.shift))  // [-Ctrl, Not Explorer] or [Shift]
$KEYS_DIR_TIP  = "» &Dir Keyboard Modifiers\n» Key.CTRL\t// Copy Path\n» Key.SHIFT\t// Open Folder\n"

/*
    :: key-combinations for executables

    :: usage:
        admin=KEYS_EXE_ADMIN
        cmd=if(KEYS_EXE_OPEN_EXE,('"@sys.dir\System32\cmd.exe"')),
        commands{
           cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@sys.dir\System32\')),
           cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@sys.dir\System32\cmd.exe')),
           cmd=if(KEYS_EXE_OPEN_DIR,('"@sys.dir\System32\"')),
        }
*/
$KEYS_EXE_ADMIN    = key.rbutton() AND !key(key.control OR key.shift OR key.alt)   // [Right, -Ctrl, -Alt, -Shift]
$KEYS_EXE_COPY_DIR = key(key.control, key.lbutton) AND !key.alt()                  // [Ctrl + Left, -Alt]
$KEYS_EXE_COPY_EXE = key(key.control, key.rbutton) AND !key.alt()                  // [Ctrl + Right, -Alt]
$KEYS_EXE_OPEN_DIR = key(key.shift, key.lbutton) AND !(key.control() OR key.alt()) // [Shift + Left, -Ctrl, -Alt]
$KEYS_EXE_OPEN_EXE = (!key(key.shift) AND !key(key.control) AND !key(key.alt))     // [-Ctrl, -Alt, -Shift]
//
$KEYS_EXE_GOTO_CFG = key(key.alt, key.lbutton) AND !(key.control() OR key.shift()) // [Alt + Left, -Ctrl, -Shift]
$KEYS_EXE_GOTO_NSS = key(key.alt, key.shift, key.lbutton) AND !key.control()       // [Alt + Shift + Left, -Ctrl]
$KEYS_EXE_GOTO_SRC = key(key.alt, key.rbutton) AND !(key.control() OR key.shift()) // [Alt + Right, -Ctrl, -Shift]
//
$KEYS_EXE_TIP      = "» &Exe Keyboard Modifiers\n» Key.CTRL\t// Copy Path\n» Key.SHIFT\t// Open Folder\n» Mouse.RIGHT\t// Admin\n"

/*
    :: key-combinations for urls

    :: usage:
        commands{
            cmd=if('@KEYS_URL_COPY',clipboard.set('@URL_GOOGLE')),
            cmd=if('@KEYS_URL_OPEN',('@URL_GOOGLE')),
        }
*/
$KEYS_URL_COPY = (key(key.control, key.lbutton) OR key(key.control, key.rbutton)) // [Ctrl + Left/Right]
$KEYS_URL_OPEN = (!key(key.control)) // [-Ctrl]
$KEYS_URL_TIP  = "» &Url Keyboard Modifiers\n» Key.CTRL\t// Copy URL\n» Mouse.LEFT\t// Open\n"

/*
    :: key-combinations for conditional menu visibility

    :: usage:
        menu(type='Taskbar' vis=KEYS_MNU_VISIBILITY_0_ALWAYS expanded=true) {
            import '@app.dir/NSS/_3_items/itm_app_sys_cmd.nss'
        }
        menu(type='Taskbar' vis=KEYS_MNU_VISIBILITY_1_CTRL expanded=true) {
            import '@app.dir/NSS/_3_items/itm_user_app_bulkrenameutility.nss'
        }
*/
$KEYS_MNU_VISIBILITY_0_ALWAYS              = true                             // [Always Visible]
$KEYS_MNU_VISIBILITY_0_DEFAULT             = !(key.control() OR key.shift())  // [-Ctrl, -Shift]
//
$KEYS_MNU_VISIBILITY_1_ALT                 = (key.alt())                      // [Alt]
$KEYS_MNU_VISIBILITY_1_CTRL                = (key.control() AND !key.shift()) // [Ctrl, -Shift]
$KEYS_MNU_VISIBILITY_1_SHIFT               = (!key.control() AND key.shift()) // [Shift, -Ctrl]
//
$KEYS_MNU_VISIBILITY_2_CTRL_SHIFT          = (key.control() AND key.shift())  // [Ctrl + Shift]
//
$KEYS_MNU_VISIBILITY_3_NONE_OR_CTRL_SHIFT  = ('@KEYS_MNU_VISIBILITY_0_DEFAULT' OR '@KEYS_MNU_VISIBILITY_2_CTRL_SHIFT')  // [No Modifiers or Ctrl + Shift]
$KEYS_MNU_VISIBILITY_3_SHIFT_OR_CTRL_SHIFT = ('@KEYS_MNU_VISIBILITY_1_SHIFT' OR '@KEYS_MNU_VISIBILITY_2_CTRL_SHIFT') // [Shift or Ctrl + Shift]
