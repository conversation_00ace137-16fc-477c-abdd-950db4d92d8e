

// -> cloud
$dir_dropbox             = '@user.profile\Dropbox'
$dir_gdrive              = '@user.profile\My Drive'
$dir_onedrive            = '@user.profile\Onedrive'
$dir_sync                = '@user.profile\Sync'

// -> user
$dir_prj                 = '@user.desktop\PRJ\GIT\JHP'
$dir_workflow            = '@user.desktop\PRJ\GIT_WORKFLOW'
$dir_py                  = '@app.dir\PORTAL\PY'
$dir_apps                = '@user.desktop\my\flow\home\__GOTO__\Apps'
$dir_jobb                = '@app.dir\PORTAL\JOBB'
$dir_utils               = '@app.dir\PORTAL\UTILS'
$dir_batch               = '@app.dir\PORTAL\BATCH'
$dir_notes               = '@app.dir\PORTAL\NOTES'

// -> third party appdata
$dir_3dsmax_appdata      = '@DIR_SYS_LOCALAPPDATA\Autodesk\3dsMax'
$dir_sublime_appdata     = '@user.appdata\Sublime Text'
$dir_vlc_appdata         = '@user.appdata\vlc'

// -> third party
$dir_3dsmax              = '@sys.prog\Autodesk\3ds Max 2025'
$dir_chrome              = '@sys.prog\Google\Chrome\Application'
$dir_discord             = '@DIR_SYS_LOCALAPPDATA\Discord\app-1.0.9147'
$dir_dremel3dslicer      = '@sys.prog\Dremel DigiLab 3D Slicer'
$dir_illustrator         = '@sys.prog\Adobe\Adobe Illustrator 2024\Support Files\Contents\Windows'
$dir_photoshop           = '@sys.prog\Adobe\Adobe Photoshop 2024'
$dir_sourcetree          = '@DIR_SYS_LOCALAPPDATA\SourceTree'
$dir_vscode              = '@sys.prog\Microsoft VS Code'

// -> third party portable
$dir_audacity            = '@user.desktop\my\flow\home\__GOTO__\Apps\app_audacity\exe'
$dir_blender             = '@user.desktop\my\flow\home\__GOTO__\Apps\app_blender\exe\blender-4.1.1-windows-x64'
$dir_bulkrename          = '@user.desktop\my\flow\home\__GOTO__\Apps\app_bulkrenameutility\exe\64-bit'
$dir_everything          = '@user.desktop\my\flow\home\__GOTO__\Apps\app_everything\exe'
$dir_filezilla           = '@user.desktop\my\flow\home\__GOTO__\Apps\app_filezilla\exe'
$dir_greenshot           = '@user.desktop\my\flow\home\__GOTO__\Apps\app_greenshot\app'
$dir_notepad_plus        = '@user.desktop\my\flow\home\__GOTO__\Apps\app_notepad++\exe'
$dir_python              = '@app.dir\PORTAL\PY\venv\Scripts'
$dir_qbittorrent         = '@user.desktop\my\flow\home\__GOTO__\Apps\app_qbittorrent\exe'
$dir_sublime             = '@user.desktop\my\flow\home\__GOTO__\Apps\exe_sublimetext\exe'
$dir_vlc                 = '@user.desktop\my\flow\home\__GOTO__\Apps\app_vlc\exe'
$dir_vscodium            = '@user.desktop\my\flow\home\__GOTO__\Apps\app_vscodium\exe'
$dir_winaerotweaker      = '@user.desktop\my\flow\home\__GOTO__\Apps\app_winaerotweaker\exe'
$dir_winmerge            = '@user.desktop\my\flow\home\__GOTO__\Apps\app_winmerge\exe'
// -> nirsoft utils
$dir_registrychangesview = '"@user.desktop\my\flow\home\__GOTO__\Apps\grp_nirsoft\app_registrychangesview\exe"'





// -> windows settings shortcuts
$exe_defender     = '"ms-settings:windowsdefender"'
$exe_lang_conf    = '"ms-settings:keyboard"'
$exe_sys_conf     = '"ms-settings:System Configuration"'
$exe_taskbar_conf = '"ms-settings:taskbar"'


// -> system management consoles
$exe_comp_mgmt    = '"@sys.dir\System32\compmgmt.msc"'
$exe_dev_mgmt     = '"@sys.dir\System32\devmgmt.msc"'
$exe_disk_mgmt    = '"@sys.dir\System32\diskmgmt.msc"'
$exe_event_vwr    = '"@sys.dir\System32\eventvwr.msc"'
$exe_perf_mon     = '"@sys.dir\System32\perfmon.msc"'
$exe_print_mgmt   = '"@sys.dir\System32\printmanagement.msc"'
$exe_services     = '"@sys.dir\System32\services.msc"'
$exe_shared_fldrs = '"@sys.dir\System32\fsmgmt.msc"'
$exe_task_schd    = '"@sys.dir\System32\taskschd.msc"'
$exe_user_mgmt    = '"@sys.dir\System32\lusrmgr.msc"'


// -> control panel utilities
$exe_display      = '"@sys.dir\System32\desk.cpl"'
$exe_firewall     = '"@sys.dir\System32\firewall.cpl"'
$exe_language     = '"ms-settings:regionlanguage-languageoptions"'
$exe_net_adapters = '"@sys.dir\System32\ncpa.cpl"'
$exe_power_opts   = '"@sys.dir\System32\powercfg.cpl"'
$exe_programs     = '"@sys.dir\System32\appwiz.cpl"'
$exe_region       = '"@sys.dir\System32\intl.cpl"'
$exe_sound        = '"@sys.dir\System32\mmsys.cpl"'
$exe_sys_props    = '"@sys.dir\System32\sysdm.cpl"'


// -> common windows applications
$exe_calc         = '"@sys.dir\System32\calc.exe"'
$exe_charmap      = '"@sys.dir\System32\charmap.exe"'
$exe_cmd          = '"@sys.dir\System32\cmd.exe"'
$exe_control      = '"@sys.dir\System32\control.exe"'
$exe_ise          = '"@sys.dir\System32\WindowsPowerShell\v1.0\powershell_ise.exe"'
$exe_magnify      = '"@sys.dir\System32\magnify.exe"'
$exe_mspaint      = '"mspaint.exe"'
$exe_notepad      = '"@sys.dir\System32\notepad.exe"'
$exe_osk          = '"@sys.dir\System32\osk.exe"'
$exe_powershell   = '"@sys.dir\System32\WindowsPowerShell\v1.0\powershell.exe"'
$exe_regedit      = '"@sys.dir\regedit.exe"'
$exe_res_mon      = '"@sys.dir\System32\resmon.exe"'
$exe_task_mgr     = '"@sys.dir\System32\taskmgr.exe"'
