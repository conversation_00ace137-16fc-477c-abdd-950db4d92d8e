<!DOCTYPE html> <html lang=en style><!--
 Page saved with SingleFile 
 url: https://nilesoft.org/docs/configuration/properties#arguments 
 saved date: Thu Oct 12 2023 13:13:03 GMT+0200 (Central European Summer Time)
--><meta charset=utf-8><meta name=viewport content="width=device-width, initial-scale=1.0"><meta name=keywords content=Shell,context-menu,right-click,shell-extension,file-explorer><meta name=description content="Shell is a powerful context menu customizer with highly responsive for Windows File Explorer."><meta property=og:type content=website><meta property=og:locale content=en_US><meta property=og:site_name content=Nilesoft><meta property=og:title content="Properties - Shell"><meta property=og:description content="Shell is a powerful context menu customizer with highly responsive for Windows File Explorer."><meta property=og:url content=https://nilesoft.org/docs/configuration/properties><meta property=og:image content=https://nilesoft.org/images/logo-512.png><meta property=og:image:type content=image/png><meta name=twitter:site content=@moudey><meta name=twitter:creator content=@moudey><meta name=twitter:title content="Properties - Shell"><meta name=twitter:description content="Shell is a powerful context menu customizer with highly responsive for Windows File Explorer."><meta name=twitter:card content=summary><meta name=twitter:image content=https://nilesoft.org/images/logo-512.png><title>Properties - Shell</title><link rel="shortcut icon" type=image/x-icon href="data:image/x-icon;base64,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" data-sf-original-href=https://nilesoft.org/images/favicon.ico><style>.sf-hidden{display:none!important}</style><meta http-equiv=content-security-policy content="default-src 'none'; font-src 'self' data:; img-src 'self' data:; style-src 'unsafe-inline'; media-src 'self' data:; script-src 'unsafe-inline' data:; object-src 'self' data:; frame-src 'self' data:;"><style>img[src="data:,"],source[src="data:,"]{display:none!important}</style><body id=mst-home><plasmo-csui><template shadowrootmode=open><style class=sf-hidden>*,:before,:after{box-sizing:border-box;border:0 solid #e5e7eb}:before,:after{--tw-content:""}html{-webkit-text-size-adjust:100%;tab-size:4;font-feature-settings:normal;font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;line-height:1.5}body{line-height:inherit;margin:0}hr{height:0;color:inherit;border-top-width:1px}abbr:where([title]){text-decoration:underline dotted}h1,h2,h3,h4,h5,h6{font-size:inherit;font-weight:inherit}a{color:inherit;text-decoration:inherit}b,strong{font-weight:bolder}code,kbd,samp,pre{font-family:ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,Liberation Mono,Courier New,monospace;font-size:1em}small{font-size:80%}sub,sup{vertical-align:baseline;font-size:75%;line-height:0;position:relative}sub{bottom:-.25em}sup{top:-.5em}table{text-indent:0;border-color:inherit;border-collapse:collapse}button,input,optgroup,select,textarea{font-family:inherit;font-size:100%;font-weight:inherit;line-height:inherit;color:inherit;margin:0;padding:0}button,select{text-transform:none}button,[type=button],[type=reset],[type=submit]{-webkit-appearance:button;background-color:#0000;background-image:none}:-moz-focusring{outline:auto}:-moz-ui-invalid{box-shadow:none}progress{vertical-align:baseline}::-webkit-inner-spin-button,::-webkit-outer-spin-button{height:auto}[type=search]{-webkit-appearance:textfield;outline-offset:-2px}::-webkit-search-decoration{-webkit-appearance:none}::-webkit-file-upload-button{-webkit-appearance:button;font:inherit}summary{display:list-item}blockquote,dl,dd,h1,h2,h3,h4,h5,h6,hr,figure,p,pre{margin:0}fieldset{margin:0;padding:0}legend{padding:0}ol,ul,menu{margin:0;padding:0;list-style:none}textarea{resize:vertical}input::placeholder,textarea::placeholder{opacity:1;color:#9ca3af}button,[role=button]{cursor:pointer}:disabled{cursor:default}img,svg,video,canvas,audio,iframe,embed,object{vertical-align:middle;display:block}img,video{max-width:100%;height:auto}[hidden]{display:none}[type=text],[type=email],[type=url],[type=password],[type=number],[type=date],[type=datetime-local],[type=month],[type=search],[type=tel],[type=time],[type=week],[multiple],textarea,select{appearance:none;--tw-shadow:0 0#0000;background-color:#fff;border-width:1px;border-color:#6b7280;border-radius:0;padding:.5rem .75rem;font-size:1rem;line-height:1.5rem}[type=text]:focus,[type=email]:focus,[type=url]:focus,[type=password]:focus,[type=number]:focus,[type=date]:focus,[type=datetime-local]:focus,[type=month]:focus,[type=search]:focus,[type=tel]:focus,[type=time]:focus,[type=week]:focus,[multiple]:focus,textarea:focus,select:focus{outline-offset:2px;--tw-ring-inset:var(--tw-empty, );--tw-ring-offset-width:0px;--tw-ring-offset-color:#fff;--tw-ring-color:#2563eb;--tw-ring-offset-shadow:var(--tw-ring-inset)0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow:var(--tw-ring-inset)0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow:var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow);border-color:#2563eb;outline:2px solid #0000}input::placeholder,textarea::placeholder{color:#6b7280;opacity:1}::-webkit-datetime-edit-fields-wrapper{padding:0}::-webkit-date-and-time-value{min-height:1.5em}::-webkit-datetime-edit,::-webkit-datetime-edit-year-field,::-webkit-datetime-edit-month-field,::-webkit-datetime-edit-day-field,::-webkit-datetime-edit-hour-field,::-webkit-datetime-edit-minute-field,::-webkit-datetime-edit-second-field,::-webkit-datetime-edit-millisecond-field,::-webkit-datetime-edit-meridiem-field{padding-top:0;padding-bottom:0}select{print-color-adjust:exact;background-image:url(data:image/svg+xml,%3csvg\ xmlns=\'http://www.w3.org/2000/svg\'\ fill=\'none\'\ viewBox=\'0\ 0\ 20\ 20\'%3e%3cpath\ stroke=\'%236b7280\'\ stroke-linecap=\'round\'\ stroke-linejoin=\'round\'\ stroke-width=\'1.5\'\ d=\'M6\ 8l4\ 4\ 4-4\'/%3e%3c/svg%3e);background-position:right .5rem center;background-repeat:no-repeat;background-size:1.5em 1.5em;padding-right:2.5rem}[multiple]{background-image:initial;background-position:initial;background-repeat:unset;background-size:initial;print-color-adjust:unset;padding-right:.75rem}[type=checkbox],[type=radio]{appearance:none;print-color-adjust:exact;vertical-align:middle;user-select:none;height:1rem;width:1rem;color:#2563eb;--tw-shadow:0 0#0000;background-color:#fff;background-origin:border-box;border-width:1px;border-color:#6b7280;flex-shrink:0;padding:0;display:inline-block}[type=checkbox]{border-radius:0}[type=radio]{border-radius:100%}[type=checkbox]:focus,[type=radio]:focus{outline-offset:2px;--tw-ring-inset:var(--tw-empty, );--tw-ring-offset-width:2px;--tw-ring-offset-color:#fff;--tw-ring-color:#2563eb;--tw-ring-offset-shadow:var(--tw-ring-inset)0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow:var(--tw-ring-inset)0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow:var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow);outline:2px solid #0000}[type=checkbox]:checked,[type=radio]:checked{background-color:currentColor;background-position:50%;background-repeat:no-repeat;background-size:100% 100%;border-color:#0000}[type=checkbox]:checked{background-image:url(data:image/svg+xml,%3csvg\ viewBox=\'0\ 0\ 16\ 16\'\ fill=\'white\'\ xmlns=\'http://www.w3.org/2000/svg\'%3e%3cpath\ d=\'M12.207\ 4.793a1\ 1\ 0\ 010\ 1.414l-5\ 5a1\ 1\ 0\ 01-1.414\ 0l-2-2a1\ 1\ 0\ 011.414-1.414L6.5\ 9.086l4.293-4.293a1\ 1\ 0\ 011.414\ 0z\'/%3e%3c/svg%3e)}[type=radio]:checked{background-image:url(data:image/svg+xml,%3csvg\ viewBox=\'0\ 0\ 16\ 16\'\ fill=\'white\'\ xmlns=\'http://www.w3.org/2000/svg\'%3e%3ccircle\ cx=\'8\'\ cy=\'8\'\ r=\'3\'/%3e%3c/svg%3e)}[type=checkbox]:checked:hover,[type=checkbox]:checked:focus,[type=radio]:checked:hover,[type=radio]:checked:focus{background-color:currentColor;border-color:#0000}[type=checkbox]:indeterminate{background-color:currentColor;background-image:url(data:image/svg+xml,%3csvg\ xmlns=\'http://www.w3.org/2000/svg\'\ fill=\'none\'\ viewBox=\'0\ 0\ 16\ 16\'%3e%3cpath\ stroke=\'white\'\ stroke-linecap=\'round\'\ stroke-linejoin=\'round\'\ stroke-width=\'2\'\ d=\'M4\ 8h8\'/%3e%3c/svg%3e);background-position:50%;background-repeat:no-repeat;background-size:100% 100%;border-color:#0000}[type=checkbox]:indeterminate:hover,[type=checkbox]:indeterminate:focus{background-color:currentColor;border-color:#0000}[type=file]{background:unset;border-color:inherit;font-size:unset;line-height:inherit;border-width:0;border-radius:0;padding:0}[type=file]:focus{outline:1px solid ButtonText;outline:1px auto -webkit-focus-ring-color}*,:before,:after,::backdrop{--tw-border-spacing-x:0;--tw-border-spacing-y:0;--tw-translate-x:0;--tw-translate-y:0;--tw-rotate:0;--tw-skew-x:0;--tw-skew-y:0;--tw-scale-x:1;--tw-scale-y:1;--tw-pan-x: ;--tw-pan-y: ;--tw-pinch-zoom: ;--tw-scroll-snap-strictness:proximity;--tw-ordinal: ;--tw-slashed-zero: ;--tw-numeric-figure: ;--tw-numeric-spacing: ;--tw-numeric-fraction: ;--tw-ring-inset: ;--tw-ring-offset-width:0px;--tw-ring-offset-color:#fff;--tw-ring-color:#3b82f680;--tw-ring-offset-shadow:0 0#0000;--tw-ring-shadow:0 0#0000;--tw-shadow:0 0#0000;--tw-shadow-colored:0 0#0000;--tw-blur: ;--tw-brightness: ;--tw-contrast: ;--tw-grayscale: ;--tw-hue-rotate: ;--tw-invert: ;--tw-saturate: ;--tw-sepia: ;--tw-drop-shadow: ;--tw-backdrop-blur: ;--tw-backdrop-brightness: ;--tw-backdrop-contrast: ;--tw-backdrop-grayscale: ;--tw-backdrop-hue-rotate: ;--tw-backdrop-invert: ;--tw-backdrop-opacity: ;--tw-backdrop-saturate: ;--tw-backdrop-sepia: }.container{width:100%}@media (min-width:640px){.container{max-width:640px}}@media (min-width:768px){.container{max-width:768px}}@media (min-width:1024px){.container{max-width:1024px}}@media (min-width:1280px){.container{max-width:1280px}}@media (min-width:1536px){.container{max-width:1536px}}.prose{color:var(--tw-prose-body);max-width:65ch}.prose :where(p):not(:where([class~=not-prose] *)){margin-top:1.25em;margin-bottom:1.25em}.prose :where([class~=lead]):not(:where([class~=not-prose] *)){color:var(--tw-prose-lead);margin-top:1.2em;margin-bottom:1.2em;font-size:1.25em;line-height:1.6}.prose :where(a):not(:where([class~=not-prose] *)){color:var(--tw-prose-links);font-weight:500;text-decoration:underline}.prose :where(strong):not(:where([class~=not-prose] *)){color:var(--tw-prose-bold);font-weight:600}.prose :where(a strong):not(:where([class~=not-prose] *)){color:inherit}.prose :where(blockquote strong):not(:where([class~=not-prose] *)){color:inherit}.prose :where(thead th strong):not(:where([class~=not-prose] *)){color:inherit}.prose :where(ol):not(:where([class~=not-prose] *)){margin-top:1.25em;margin-bottom:1.25em;padding-left:1.625em;list-style-type:decimal}.prose :where(ol[type=A]):not(:where([class~=not-prose] *)){list-style-type:upper-alpha}.prose :where(ol[type=a]):not(:where([class~=not-prose] *)){list-style-type:lower-alpha}.prose :where(ol[type=A s]):not(:where([class~=not-prose] *)){list-style-type:upper-alpha}.prose :where(ol[type=a s]):not(:where([class~=not-prose] *)){list-style-type:lower-alpha}.prose :where(ol[type=I]):not(:where([class~=not-prose] *)){list-style-type:upper-roman}.prose :where(ol[type=i]):not(:where([class~=not-prose] *)){list-style-type:lower-roman}.prose :where(ol[type=I s]):not(:where([class~=not-prose] *)){list-style-type:upper-roman}.prose :where(ol[type=i s]):not(:where([class~=not-prose] *)){list-style-type:lower-roman}.prose :where(ol[type="1"]):not(:where([class~=not-prose] *)){list-style-type:decimal}.prose :where(ul):not(:where([class~=not-prose] *)){margin-top:1.25em;margin-bottom:1.25em;padding-left:1.625em;list-style-type:disc}.prose :where(ol>li):not(:where([class~=not-prose] *))::marker{color:var(--tw-prose-counters);font-weight:400}.prose :where(ul>li):not(:where([class~=not-prose] *))::marker{color:var(--tw-prose-bullets)}.prose :where(hr):not(:where([class~=not-prose] *)){border-color:var(--tw-prose-hr);border-top-width:1px;margin-top:3em;margin-bottom:3em}.prose :where(blockquote):not(:where([class~=not-prose] *)){color:var(--tw-prose-quotes);border-left-width:.25rem;border-left-color:var(--tw-prose-quote-borders);quotes:"“""”""‘""’";margin-top:1.6em;margin-bottom:1.6em;padding-left:1em;font-style:italic;font-weight:500}.prose :where(blockquote p:first-of-type):not(:where([class~=not-prose] *)):before{content:open-quote}.prose :where(blockquote p:last-of-type):not(:where([class~=not-prose] *)):after{content:close-quote}.prose :where(h1):not(:where([class~=not-prose] *)){color:var(--tw-prose-headings);margin-top:0;margin-bottom:.888889em;font-size:2.25em;font-weight:800;line-height:1.11111}.prose :where(h1 strong):not(:where([class~=not-prose] *)){color:inherit;font-weight:900}.prose :where(h2):not(:where([class~=not-prose] *)){color:var(--tw-prose-headings);margin-top:2em;margin-bottom:1em;font-size:1.5em;font-weight:700;line-height:1.33333}.prose :where(h2 strong):not(:where([class~=not-prose] *)){color:inherit;font-weight:800}.prose :where(h3):not(:where([class~=not-prose] *)){color:var(--tw-prose-headings);margin-top:1.6em;margin-bottom:.6em;font-size:1.25em;font-weight:600;line-height:1.6}.prose :where(h3 strong):not(:where([class~=not-prose] *)){color:inherit;font-weight:700}.prose :where(h4):not(:where([class~=not-prose] *)){color:var(--tw-prose-headings);margin-top:1.5em;margin-bottom:.5em;font-weight:600;line-height:1.5}.prose :where(h4 strong):not(:where([class~=not-prose] *)){color:inherit;font-weight:700}.prose :where(img):not(:where([class~=not-prose] *)){margin-top:2em;margin-bottom:2em}.prose :where(figure>*):not(:where([class~=not-prose] *)){margin-top:0;margin-bottom:0}.prose :where(figcaption):not(:where([class~=not-prose] *)){color:var(--tw-prose-captions);margin-top:.857143em;font-size:.875em;line-height:1.42857}.prose :where(code):not(:where([class~=not-prose] *)){color:var(--tw-prose-code);font-size:.875em;font-weight:600}.prose :where(code):not(:where([class~=not-prose] *)):before,.prose :where(code):not(:where([class~=not-prose] *)):after{content:"`"}.prose :where(a code):not(:where([class~=not-prose] *)){color:inherit}.prose :where(h1 code):not(:where([class~=not-prose] *)){color:inherit}.prose :where(h2 code):not(:where([class~=not-prose] *)){color:inherit;font-size:.875em}.prose :where(h3 code):not(:where([class~=not-prose] *)){color:inherit;font-size:.9em}.prose :where(h4 code):not(:where([class~=not-prose] *)){color:inherit}.prose :where(blockquote code):not(:where([class~=not-prose] *)){color:inherit}.prose :where(thead th code):not(:where([class~=not-prose] *)){color:inherit}.prose :where(pre):not(:where([class~=not-prose] *)){color:var(--tw-prose-pre-code);background-color:var(--tw-prose-pre-bg);border-radius:.375rem;margin-top:1.71429em;margin-bottom:1.71429em;padding:.857143em 1.14286em;font-size:.875em;font-weight:400;line-height:1.71429;overflow-x:auto}.prose :where(pre code):not(:where([class~=not-prose] *)){font-weight:inherit;color:inherit;font-size:inherit;font-family:inherit;line-height:inherit;background-color:#0000;border-width:0;border-radius:0;padding:0}.prose :where(pre code):not(:where([class~=not-prose] *)):before,.prose :where(pre code):not(:where([class~=not-prose] *)):after{content:none}.prose :where(table):not(:where([class~=not-prose] *)){width:100%;table-layout:auto;text-align:left;margin-top:2em;margin-bottom:2em;font-size:.875em;line-height:1.71429}.prose :where(thead):not(:where([class~=not-prose] *)){border-bottom-width:1px;border-bottom-color:var(--tw-prose-th-borders)}.prose :where(thead th):not(:where([class~=not-prose] *)){color:var(--tw-prose-headings);vertical-align:bottom;padding-bottom:.571429em;padding-left:.571429em;padding-right:.571429em;font-weight:600}.prose :where(tbody tr):not(:where([class~=not-prose] *)){border-bottom-width:1px;border-bottom-color:var(--tw-prose-td-borders)}.prose :where(tbody tr:last-child):not(:where([class~=not-prose] *)){border-bottom-width:0}.prose :where(tbody td):not(:where([class~=not-prose] *)){vertical-align:baseline}.prose :where(tfoot):not(:where([class~=not-prose] *)){border-top-width:1px;border-top-color:var(--tw-prose-th-borders)}.prose :where(tfoot td):not(:where([class~=not-prose] *)){vertical-align:top}.prose{--tw-prose-body:#374151;--tw-prose-headings:#111827;--tw-prose-lead:#4b5563;--tw-prose-links:#111827;--tw-prose-bold:#111827;--tw-prose-counters:#6b7280;--tw-prose-bullets:#d1d5db;--tw-prose-hr:#e5e7eb;--tw-prose-quotes:#111827;--tw-prose-quote-borders:#e5e7eb;--tw-prose-captions:#6b7280;--tw-prose-code:#111827;--tw-prose-pre-code:#e5e7eb;--tw-prose-pre-bg:#1f2937;--tw-prose-th-borders:#d1d5db;--tw-prose-td-borders:#e5e7eb;--tw-prose-invert-body:#d1d5db;--tw-prose-invert-headings:#fff;--tw-prose-invert-lead:#9ca3af;--tw-prose-invert-links:#fff;--tw-prose-invert-bold:#fff;--tw-prose-invert-counters:#9ca3af;--tw-prose-invert-bullets:#4b5563;--tw-prose-invert-hr:#374151;--tw-prose-invert-quotes:#f3f4f6;--tw-prose-invert-quote-borders:#374151;--tw-prose-invert-captions:#9ca3af;--tw-prose-invert-code:#fff;--tw-prose-invert-pre-code:#d1d5db;--tw-prose-invert-pre-bg:#00000080;--tw-prose-invert-th-borders:#4b5563;--tw-prose-invert-td-borders:#374151;font-size:1rem;line-height:1.75}.prose :where(video):not(:where([class~=not-prose] *)){margin-top:2em;margin-bottom:2em}.prose :where(figure):not(:where([class~=not-prose] *)){margin-top:2em;margin-bottom:2em}.prose :where(li):not(:where([class~=not-prose] *)){margin-top:.5em;margin-bottom:.5em}.prose :where(ol>li):not(:where([class~=not-prose] *)){padding-left:.375em}.prose :where(ul>li):not(:where([class~=not-prose] *)){padding-left:.375em}.prose :where(.prose>ul>li p):not(:where([class~=not-prose] *)){margin-top:.75em;margin-bottom:.75em}.prose :where(.prose>ul>li>:first-child):not(:where([class~=not-prose] *)){margin-top:1.25em}.prose :where(.prose>ul>li>:last-child):not(:where([class~=not-prose] *)){margin-bottom:1.25em}.prose :where(.prose>ol>li>:first-child):not(:where([class~=not-prose] *)){margin-top:1.25em}.prose :where(.prose>ol>li>:last-child):not(:where([class~=not-prose] *)){margin-bottom:1.25em}.prose :where(ul ul,ul ol,ol ul,ol ol):not(:where([class~=not-prose] *)){margin-top:.75em;margin-bottom:.75em}.prose :where(hr+*):not(:where([class~=not-prose] *)){margin-top:0}.prose :where(h2+*):not(:where([class~=not-prose] *)){margin-top:0}.prose :where(h3+*):not(:where([class~=not-prose] *)){margin-top:0}.prose :where(h4+*):not(:where([class~=not-prose] *)){margin-top:0}.prose :where(thead th:first-child):not(:where([class~=not-prose] *)){padding-left:0}.prose :where(thead th:last-child):not(:where([class~=not-prose] *)){padding-right:0}.prose :where(tbody td,tfoot td):not(:where([class~=not-prose] *)){padding:.571429em}.prose :where(tbody td:first-child,tfoot td:first-child):not(:where([class~=not-prose] *)){padding-left:0}.prose :where(tbody td:last-child,tfoot td:last-child):not(:where([class~=not-prose] *)){padding-right:0}.prose :where(.prose>:first-child):not(:where([class~=not-prose] *)){margin-top:0}.prose :where(.prose>:last-child):not(:where([class~=not-prose] *)){margin-bottom:0}.sr-only{width:1px;height:1px;clip:rect(0,0,0,0);white-space:nowrap;border-width:0;margin:-1px;padding:0;position:absolute;overflow:hidden}.pointer-events-none{pointer-events:none}.pointer-events-auto{pointer-events:auto}.fixed{position:fixed}.absolute{position:absolute}.relative{position:relative}.inset-0{inset:0}.inset-y-0{top:0;bottom:0}.top-10{top:2.5rem}.left-10{left:2.5rem}.top-1\/3{top:33.3333%}.right-10{right:2.5rem}.right-2{right:.5rem}.bottom-2{bottom:.5rem}.right-0{right:0}.isolate{isolation:isolate}.z-10{z-index:10}.m-2{margin:.5rem}.mx-auto{margin-left:auto;margin-right:auto}.my-1{margin-top:.25rem;margin-bottom:.25rem}.mx-2{margin-left:.5rem;margin-right:.5rem}.mr-2{margin-right:.5rem}.mt-1{margin-top:.25rem}.ml-3{margin-left:.75rem}.ml-4{margin-left:1rem}.mt-4{margin-top:1rem}.ml-2{margin-left:.5rem}.box-border{box-sizing:border-box}.block{display:block}.inline{display:inline}.\!inline{display:inline!important}.flex{display:flex}.inline-flex{display:inline-flex}.hidden{display:none}.h-5\/6{height:83.3333%}.h-1\/2{height:50%}.h-screen{height:100vh}.h-full{height:100%}.h-5{height:1.25rem}.h-6{height:1.5rem}.h-8{height:2rem}.h-4{height:1rem}.max-h-60{max-height:15rem}.w-11\/12{width:91.6667%}.w-1\/3{width:33.3333%}.w-28{width:7rem}.w-full{width:100%}.w-96{width:24rem}.w-1\/2{width:50%}.w-5{width:1.25rem}.w-6{width:1.5rem}.w-0{width:0}.w-8{width:2rem}.max-w-3xl{max-width:48rem}.max-w-none{max-width:none}.max-w-sm{max-width:24rem}.flex-1{flex:1}.flex-shrink-0{flex-shrink:0}.origin-center{transform-origin:50%}.translate-y-2{--tw-translate-y:.5rem;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.translate-y-0{--tw-translate-y:0px;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.rotate-180{--tw-rotate:180deg;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.transform{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}@keyframes spin{to{transform:rotate(360deg)}}.animate-spin{animation:1s linear infinite spin}.cursor-default{cursor:default}.select-none{user-select:none}.flex-row{flex-direction:row}.flex-col{flex-direction:column}.items-start{align-items:flex-start}.items-end{align-items:flex-end}.items-center{align-items:center}.justify-end{justify-content:flex-end}.justify-center{justify-content:center}.justify-between{justify-content:space-between}.space-y-1>:not([hidden])~:not([hidden]){--tw-space-y-reverse:0;margin-top:calc(.25rem*calc(1 - var(--tw-space-y-reverse)));margin-bottom:calc(.25rem*var(--tw-space-y-reverse))}.space-x-2>:not([hidden])~:not([hidden]){--tw-space-x-reverse:0;margin-right:calc(.5rem*var(--tw-space-x-reverse));margin-left:calc(.5rem*calc(1 - var(--tw-space-x-reverse)))}.space-y-3>:not([hidden])~:not([hidden]){--tw-space-y-reverse:0;margin-top:calc(.75rem*calc(1 - var(--tw-space-y-reverse)));margin-bottom:calc(.75rem*var(--tw-space-y-reverse))}.space-x-1>:not([hidden])~:not([hidden]){--tw-space-x-reverse:0;margin-right:calc(.25rem*var(--tw-space-x-reverse));margin-left:calc(.25rem*calc(1 - var(--tw-space-x-reverse)))}.space-y-4>:not([hidden])~:not([hidden]){--tw-space-y-reverse:0;margin-top:calc(1rem*calc(1 - var(--tw-space-y-reverse)));margin-bottom:calc(1rem*var(--tw-space-y-reverse))}.-space-x-px>:not([hidden])~:not([hidden]){--tw-space-x-reverse:0;margin-right:calc(-1px*var(--tw-space-x-reverse));margin-left:calc(-1px*calc(1 - var(--tw-space-x-reverse)))}.overflow-auto{overflow:auto}.overflow-hidden{overflow:hidden}.truncate{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.rounded-lg{border-radius:.5rem}.rounded{border-radius:.25rem}.rounded-md{border-radius:.375rem}.rounded-xl{border-radius:.75rem}.rounded-full{border-radius:9999px}.border{border-width:1px}.border-0{border-width:0}.border-t{border-top-width:1px}.border-none{border-style:none}.border-transparent{border-color:#0000}.border-gray-300{--tw-border-opacity:1;border-color:rgb(209 213 219/var(--tw-border-opacity))}.bg-slate-200{--tw-bg-opacity:1;background-color:rgb(226 232 240/var(--tw-bg-opacity))}.bg-indigo-600{--tw-bg-opacity:1;background-color:rgb(79 70 229/var(--tw-bg-opacity))}.bg-slate-100{--tw-bg-opacity:1;background-color:rgb(241 245 249/var(--tw-bg-opacity))}.bg-indigo-100{--tw-bg-opacity:1;background-color:rgb(224 231 255/var(--tw-bg-opacity))}.bg-white{--tw-bg-opacity:1;background-color:rgb(255 255 255/var(--tw-bg-opacity))}.bg-indigo-200{--tw-bg-opacity:1;background-color:rgb(199 210 254/var(--tw-bg-opacity))}.stroke-current{stroke:currentColor}.p-2{padding:.5rem}.p-3{padding:.75rem}.p-10{padding:2.5rem}.p-4{padding:1rem}.px-2\.5{padding-left:.625rem;padding-right:.625rem}.py-0{padding-top:0;padding-bottom:0}.px-2{padding-left:.5rem;padding-right:.5rem}.py-2{padding-top:.5rem;padding-bottom:.5rem}.px-4{padding-left:1rem;padding-right:1rem}.py-1{padding-top:.25rem;padding-bottom:.25rem}.py-\[6px\]{padding-top:6px;padding-bottom:6px}.py-1\.5{padding-top:.375rem;padding-bottom:.375rem}.py-6{padding-top:1.5rem;padding-bottom:1.5rem}.pl-3{padding-left:.75rem}.pr-10{padding-right:2.5rem}.pt-4{padding-top:1rem}.pb-2{padding-bottom:.5rem}.pr-9{padding-right:2.25rem}.pr-4{padding-right:1rem}.pt-0\.5{padding-top:.125rem}.pt-0{padding-top:0}.text-left{text-align:left}.text-start{text-align:start}.text-sm{font-size:.875rem;line-height:1.25rem}.text-xs{font-size:.75rem;line-height:1rem}.text-base{font-size:1rem;line-height:1.5rem}.font-medium{font-weight:500}.font-semibold{font-weight:600}.text-gray-700{--tw-text-opacity:1;color:rgb(55 65 81/var(--tw-text-opacity))}.text-white{--tw-text-opacity:1;color:rgb(255 255 255/var(--tw-text-opacity))}.text-purple-900{--tw-text-opacity:1;color:rgb(88 28 135/var(--tw-text-opacity))}.text-purple-500{--tw-text-opacity:1;color:rgb(168 85 247/var(--tw-text-opacity))}.text-gray-500{--tw-text-opacity:1;color:rgb(107 114 128/var(--tw-text-opacity))}.text-gray-400{--tw-text-opacity:1;color:rgb(156 163 175/var(--tw-text-opacity))}.text-gray-900{--tw-text-opacity:1;color:rgb(17 24 39/var(--tw-text-opacity))}.text-indigo-600{--tw-text-opacity:1;color:rgb(79 70 229/var(--tw-text-opacity))}.text-green-400{--tw-text-opacity:1;color:rgb(74 222 128/var(--tw-text-opacity))}.opacity-30{opacity:.3}.opacity-0{opacity:0}.opacity-100{opacity:1}.shadow-sm{--tw-shadow:0 1px 2px 0#0000000d;--tw-shadow-colored:0 1px 2px 0 var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow,0 0#0000),var(--tw-ring-shadow,0 0#0000),var(--tw-shadow)}.shadow-lg{--tw-shadow:0 10px 15px -3px #0000001a,0 4px 6px -4px #0000001a;--tw-shadow-colored:0 10px 15px -3px var(--tw-shadow-color),0 4px 6px -4px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow,0 0#0000),var(--tw-ring-shadow,0 0#0000),var(--tw-shadow)}.ring-1{--tw-ring-offset-shadow:var(--tw-ring-inset)0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow:var(--tw-ring-inset)0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow:var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow,0 0#0000)}.ring-black{--tw-ring-opacity:1;--tw-ring-color:rgb(0 0 0/var(--tw-ring-opacity))}.ring-opacity-5{--tw-ring-opacity:.05}.filter{filter:var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow)}.transition{transition-property:color,background-color,border-color,text-decoration-color,fill,stroke,opacity,box-shadow,transform,filter,backdrop-filter;transition-duration:.15s;transition-timing-function:cubic-bezier(.4,0,.2,1)}.transition-all{transition-property:all;transition-duration:.15s;transition-timing-function:cubic-bezier(.4,0,.2,1)}.duration-300{transition-duration:.3s}.duration-100{transition-duration:.1s}.ease-out{transition-timing-function:cubic-bezier(0,0,.2,1)}.ease-in{transition-timing-function:cubic-bezier(.4,0,1,1)}.hover\:bg-indigo-700:hover{--tw-bg-opacity:1;background-color:rgb(67 56 202/var(--tw-bg-opacity))}.hover\:bg-purple-200:hover{--tw-bg-opacity:1;background-color:rgb(233 213 255/var(--tw-bg-opacity))}.hover\:bg-indigo-500:hover{--tw-bg-opacity:1;background-color:rgb(99 102 241/var(--tw-bg-opacity))}.hover\:text-gray-500:hover{--tw-text-opacity:1;color:rgb(107 114 128/var(--tw-text-opacity))}.hover\:shadow-md:hover{--tw-shadow:0 4px 6px -1px #0000001a,0 2px 4px -2px #0000001a;--tw-shadow-colored:0 4px 6px -1px var(--tw-shadow-color),0 2px 4px -2px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow,0 0#0000),var(--tw-ring-shadow,0 0#0000),var(--tw-shadow)}.focus\:z-10:focus{z-index:10}.focus\:border-indigo-500:focus{--tw-border-opacity:1;border-color:rgb(99 102 241/var(--tw-border-opacity))}.focus\:outline-none:focus{outline-offset:2px;outline:2px solid #0000}.focus\:ring-2:focus{--tw-ring-offset-shadow:var(--tw-ring-inset)0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow:var(--tw-ring-inset)0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow:var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow,0 0#0000)}.focus\:ring-1:focus{--tw-ring-offset-shadow:var(--tw-ring-inset)0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow:var(--tw-ring-inset)0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow:var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow,0 0#0000)}.focus\:ring-indigo-500:focus{--tw-ring-opacity:1;--tw-ring-color:rgb(99 102 241/var(--tw-ring-opacity))}.focus\:ring-offset-2:focus{--tw-ring-offset-width:2px}.focus-visible\:ring:focus-visible{--tw-ring-offset-shadow:var(--tw-ring-inset)0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow:var(--tw-ring-inset)0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow:var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow,0 0#0000)}.focus-visible\:ring-purple-500:focus-visible{--tw-ring-opacity:1;--tw-ring-color:rgb(168 85 247/var(--tw-ring-opacity))}.focus-visible\:ring-opacity-75:focus-visible{--tw-ring-opacity:.75}.active\:scale-105:active{--tw-scale-x:1.05;--tw-scale-y:1.05;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}@media (prefers-color-scheme:dark){.dark\:bg-slate-700{--tw-bg-opacity:1;background-color:rgb(51 65 85/var(--tw-bg-opacity))}.dark\:prose-invert{--tw-prose-body:var(--tw-prose-invert-body);--tw-prose-headings:var(--tw-prose-invert-headings);--tw-prose-lead:var(--tw-prose-invert-lead);--tw-prose-links:var(--tw-prose-invert-links);--tw-prose-bold:var(--tw-prose-invert-bold);--tw-prose-counters:var(--tw-prose-invert-counters);--tw-prose-bullets:var(--tw-prose-invert-bullets);--tw-prose-hr:var(--tw-prose-invert-hr);--tw-prose-quotes:var(--tw-prose-invert-quotes);--tw-prose-quote-borders:var(--tw-prose-invert-quote-borders);--tw-prose-captions:var(--tw-prose-invert-captions);--tw-prose-code:var(--tw-prose-invert-code);--tw-prose-pre-code:var(--tw-prose-invert-pre-code);--tw-prose-pre-bg:var(--tw-prose-invert-pre-bg);--tw-prose-th-borders:var(--tw-prose-invert-th-borders);--tw-prose-td-borders:var(--tw-prose-invert-td-borders)}.dark\:text-white{--tw-text-opacity:1;color:rgb(255 255 255/var(--tw-text-opacity))}}@media (min-width:640px){.sm\:translate-y-0{--tw-translate-y:0px;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.sm\:translate-x-2{--tw-translate-x:.5rem;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.sm\:translate-x-0{--tw-translate-x:0px;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.sm\:items-start{align-items:flex-start}.sm\:items-end{align-items:flex-end}.sm\:p-6{padding:1.5rem}.sm\:text-sm{font-size:.875rem;line-height:1.25rem}}</style><div id=plasmo-shadow-container style=z-index:2147483647;position:relative><div id=plasmo-overlay-0 class=plasmo-csui-container style=display:flex;position:absolute;top:-0.0004882px;left:0px></div></div></template></plasmo-csui>
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 <link rel=canonical href=https://nilesoft.org/docs/configuration/properties>
 <link rel=alternate type=application/rss+xml title="Shell Releases" href=https://nilesoft.org/feed/releases>
 <link rel=alternate type=application/rss+xml title="Shell News" href=https://nilesoft.org/feed/news>
 <link rel=alternate type=application/rss+xml title="Shell Blog" href=https://nilesoft.org/feed/blog>
 <style>@font-face{font-family:"Open Sans";font-style:normal;font-weight:400;font-stretch:100%;src:/* original URL: https://fonts.gstatic.com/s/opensans/v36/memSYaGs126MiZpBA-UvWbX2vVnXBbObj2OVZyOOSr4dVJWUgsjZ0B4gaVI.woff2 */url(data:font/woff2;base64,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)format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+2074,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}</style>
 <style>/*! bulma.io v0.9.3 | MIT License | github.com/jgthms/bulma */.button,.input{-moz-appearance:none;-webkit-appearance:none;align-items:center;border:1px solid transparent;border-radius:4px;box-shadow:none;display:inline-flex;font-size:1rem;height:2.5em;justify-content:flex-start;line-height:1.5;padding-bottom:calc(.5em - 1px);padding-left:calc(.75em - 1px);padding-right:calc(.75em - 1px);padding-top:calc(.5em - 1px);position:relative;vertical-align:top}.button:active,.button:focus,.file-cta:active,.file-cta:focus,.file-name:active,.file-name:focus,.input:active,.input:focus,.is-active.button,.is-active.file-cta,.is-active.file-name,.is-active.input,.is-active.pagination-ellipsis,.is-active.pagination-link,.is-active.pagination-next,.is-active.pagination-previous,.is-active.textarea,.is-focused.button,.is-focused.file-cta,.is-focused.file-name,.is-focused.input,.is-focused.pagination-ellipsis,.is-focused.pagination-link,.is-focused.pagination-next,.is-focused.pagination-previous,.is-focused.textarea,.pagination-ellipsis:active,.pagination-ellipsis:focus,.pagination-link:active,.pagination-link:focus,.pagination-next:active,.pagination-next:focus,.pagination-previous:active,.pagination-previous:focus,.select select.is-active,.select select.is-focused,.select select:active,.select select:focus,.textarea:active,.textarea:focus{outline:0}.button{-webkit-touch-callout:none;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.block:not(:last-child),.box:not(:last-child),.breadcrumb:not(:last-child),.content:not(:last-child),.level:not(:last-child),.message:not(:last-child),.notification:not(:last-child),.pagination:not(:last-child),.progress:not(:last-child),.subtitle:not(:last-child),.table-container:not(:last-child),.table:not(:last-child),.tabs:not(:last-child),.title:not(:last-child){margin-bottom:1.5rem}/*! minireset.css v0.0.6 | MIT License | github.com/jgthms/minireset.css */body,dd,dl,dt,h4,h5,h6,hr,html,li,p,pre,ul{margin:0;padding:0}h4,h5{font-weight:400}ul{list-style:none}button,input{margin:0}html{box-sizing:border-box}*,::after,::before{box-sizing:inherit}table{border-collapse:collapse;border-spacing:0}td:not([align]),th:not([align]){text-align:inherit}html{background-color:#fff;font-size:16px;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;min-width:300px;overflow-x:hidden;overflow-y:scroll;text-rendering:optimizeLegibility;-webkit-text-size-adjust:100%;-moz-text-size-adjust:100%;text-size-adjust:100%}footer,section{display:block}button,input{font-family:BlinkMacSystemFont,-apple-system,"Segoe UI",Roboto,Oxygen,Ubuntu,Cantarell,"Fira Sans","Droid Sans","Helvetica Neue",Helvetica,Arial,sans-serif}code,pre{-moz-osx-font-smoothing:auto;-webkit-font-smoothing:auto;font-family:monospace}body{font-size:1em;font-weight:400;line-height:1.5}a{color:#485fc7;cursor:pointer;text-decoration:none}a:hover{color:#363636}code{background-color:#f5f5f5;color:#da1039;font-size:.875em;font-weight:400;padding:.25em .5em .25em}hr{background-color:#f5f5f5;border:none;display:block;height:2px;margin:1.5rem 0}small{font-size:.875em}span{font-style:inherit;font-weight:inherit}strong{color:#363636;font-weight:700}pre{-webkit-overflow-scrolling:touch;background-color:#f5f5f5;color:#4a4a4a;font-size:.875em}pre code{background-color:transparent;color:currentColor;font-size:1em;padding:0}table td:not([align]),table th:not([align]){text-align:inherit}@-webkit-keyframes spinAround{from{transform:rotate(0)}to{transform:rotate(359deg)}}@keyframes spinAround{from{transform:rotate(0)}to{transform:rotate(359deg)}}.button{border-width:1px;cursor:pointer;justify-content:center;padding-bottom:calc(.5em - 1px);padding-left:1em;padding-right:1em;padding-top:calc(.5em - 1px);text-align:center;white-space:nowrap}.button.is-hovered,.button:hover{border-color:#b5b5b5;color:#363636}.button.is-focused,.button:focus{border-color:#485fc7;color:#363636}.button.is-focused:not(:active),.button:focus:not(:active){box-shadow:0 0 0 .125em rgba(72,95,199,.25)}.button.is-active,.button:active{border-color:#4a4a4a;color:#363636}.button.is-black{background-color:#0a0a0a;border-color:transparent;color:#fff}.button.is-black.is-hovered,.button.is-black:hover{background-color:#040404;border-color:transparent;color:#fff}.button.is-black.is-focused,.button.is-black:focus{border-color:transparent;color:#fff}.button.is-black.is-focused:not(:active),.button.is-black:focus:not(:active){box-shadow:0 0 0 .125em rgba(10,10,10,.25)}.button.is-black.is-active,.button.is-black:active{background-color:#000;border-color:transparent;color:#fff}.button.is-dark{background-color:#363636;border-color:transparent;color:#fff}.button.is-dark.is-hovered,.button.is-dark:hover{background-color:#2f2f2f;border-color:transparent;color:#fff}.button.is-dark.is-focused,.button.is-dark:focus{border-color:transparent;color:#fff}.button.is-dark.is-focused:not(:active),.button.is-dark:focus:not(:active){box-shadow:0 0 0 .125em rgba(54,54,54,.25)}.button.is-dark.is-active,.button.is-dark:active{background-color:#292929;border-color:transparent;color:#fff}.container{flex-grow:1;margin:0 auto;position:relative;width:auto}@media screen and (min-width:1024px){.container{max-width:960px}}@media screen and (min-width:1216px){.container:not(.is-max-desktop){max-width:1152px}}@media screen and (min-width:1408px){.container:not(.is-max-desktop):not(.is-max-widescreen){max-width:1344px}}.content li+li{margin-top:.25em}.content blockquote:not(:last-child),.content dl:not(:last-child),.content ol:not(:last-child),.content p:not(:last-child),.content pre:not(:last-child),.content table:not(:last-child),.content ul:not(:last-child){margin-bottom:1em}.content h4,.content h5,.content h6{color:#363636;font-weight:600;line-height:1.125}.content h4{font-size:1.25em;margin-bottom:.8em}.content h5{font-size:1.125em;margin-bottom:.8888em}.content h6{font-size:1em;margin-bottom:1em}.content ul{list-style:disc outside;margin-left:2em;margin-top:1em}.content ul ul{list-style-type:circle;margin-top:.5em}.content dd{margin-left:2em}.content pre{-webkit-overflow-scrolling:touch;overflow-x:auto;padding:1.25em 1.5em;white-space:pre;word-wrap:normal}.content sup{font-size:75%}.content table{width:100%}.content table td,.content table th{border:1px solid #dbdbdb;border-width:0 0 1px;padding:.5em .75em;vertical-align:top}.content table th:not([align]){text-align:inherit}.content table thead th{border-width:0 0 2px;color:#363636}.content table tbody tr:last-child td,.content table tbody tr:last-child th{border-bottom-width:0}.icon{align-items:center;display:inline-flex;justify-content:center;height:1.5rem;width:1.5rem}.icon-text{align-items:flex-start;color:inherit;flex-wrap:wrap;line-height:1.5rem;vertical-align:top}.icon-text .icon{flex-grow:0;flex-shrink:0}.icon-text .icon:not(:last-child){margin-right:.25em}.icon-text .icon:not(:first-child){margin-left:.25em}div.icon-text{display:flex}.notification{border-radius:4px;position:relative;padding:1.25rem 2.5rem 1.25rem 1.5rem}.notification.is-info{background-color:#3e8ed0;color:#fff}@-webkit-keyframes moveIndeterminate{from{background-position:200%0}to{background-position:-200%0}}@keyframes moveIndeterminate{from{background-position:200%0}to{background-position:-200%0}}.table{background-color:#fff;color:#363636}.table th:not([align]){text-align:inherit}.table thead{background-color:transparent}.table tbody{background-color:transparent}.table tbody tr:last-child td,.table tbody tr:last-child th{border-bottom-width:0}.table-container{-webkit-overflow-scrolling:touch;overflow:auto;overflow-y:hidden;max-width:100%}.input{background-color:#fff;border-color:#dbdbdb;border-radius:4px;color:#363636}.input::-webkit-input-placeholder,.select select::-webkit-input-placeholder,.textarea::-webkit-input-placeholder{color:rgba(54,54,54,.3)}.input:hover,.is-hovered.input,.is-hovered.textarea,.select select.is-hovered,.select select:hover,.textarea:hover{border-color:#b5b5b5}.input:active,.input:focus,.is-active.input,.is-active.textarea,.is-focused.input,.is-focused.textarea,.select select.is-active,.select select.is-focused,.select select:active,.select select:focus,.textarea:active,.textarea:focus{border-color:#485fc7;box-shadow:0 0 0 .125em rgba(72,95,199,.25)}.input{box-shadow:inset 0 .0625em .125em rgba(10,10,10,.05);max-width:100%;width:100%}.field:not(:last-child){margin-bottom:.75rem}.field.has-addons{display:flex;justify-content:flex-start}.field.has-addons .control:not(:last-child){margin-right:-1px}.field.has-addons .control:not(:first-child):not(:last-child) .button,.field.has-addons .control:not(:first-child):not(:last-child) .input,.field.has-addons .control:not(:first-child):not(:last-child) .select select{border-radius:0}.field.has-addons .control:first-child:not(:only-child) .button,.field.has-addons .control:first-child:not(:only-child) .input,.field.has-addons .control:first-child:not(:only-child) .select select{border-bottom-right-radius:0;border-top-right-radius:0}.field.has-addons .control:last-child:not(:only-child) .button,.field.has-addons .control:last-child:not(:only-child) .input,.field.has-addons .control:last-child:not(:only-child) .select select{border-bottom-left-radius:0;border-top-left-radius:0}.field.has-addons .control .button:not([disabled]).is-hovered,.field.has-addons .control .button:not([disabled]):hover,.field.has-addons .control .input:not([disabled]).is-hovered,.field.has-addons .control .input:not([disabled]):hover,.field.has-addons .control .select select:not([disabled]).is-hovered,.field.has-addons .control .select select:not([disabled]):hover{z-index:2}.field.has-addons .control .button:not([disabled]).is-active,.field.has-addons .control .button:not([disabled]).is-focused,.field.has-addons .control .button:not([disabled]):active,.field.has-addons .control .button:not([disabled]):focus,.field.has-addons .control .input:not([disabled]).is-active,.field.has-addons .control .input:not([disabled]).is-focused,.field.has-addons .control .input:not([disabled]):active,.field.has-addons .control .input:not([disabled]):focus,.field.has-addons .control .select select:not([disabled]).is-active,.field.has-addons .control .select select:not([disabled]).is-focused,.field.has-addons .control .select select:not([disabled]):active,.field.has-addons .control .select select:not([disabled]):focus{z-index:3}.field.has-addons .control .button:not([disabled]).is-active:hover,.field.has-addons .control .button:not([disabled]).is-focused:hover,.field.has-addons .control .button:not([disabled]):active:hover,.field.has-addons .control .button:not([disabled]):focus:hover,.field.has-addons .control .input:not([disabled]).is-active:hover,.field.has-addons .control .input:not([disabled]).is-focused:hover,.field.has-addons .control .input:not([disabled]):active:hover,.field.has-addons .control .input:not([disabled]):focus:hover,.field.has-addons .control .select select:not([disabled]).is-active:hover,.field.has-addons .control .select select:not([disabled]).is-focused:hover,.field.has-addons .control .select select:not([disabled]):active:hover,.field.has-addons .control .select select:not([disabled]):focus:hover{z-index:4}.field.has-addons .control.is-expanded{flex-grow:1;flex-shrink:1}.control{box-sizing:border-box;clear:both;font-size:1rem;position:relative;text-align:inherit}.menu{font-size:1rem}.menu-list{line-height:1.25}.menu-list a{border-radius:2px;color:#4a4a4a;display:block}.menu-list a:hover{background-color:#f5f5f5;color:#363636}.menu-list li ul{border-left:1px solid #dbdbdb;margin:.75em;padding-left:.75em}.navbar{background-color:#fff;min-height:3.25rem;position:relative;z-index:30}.navbar>.container{align-items:stretch;display:flex;min-height:3.25rem;width:100%}.navbar-brand{align-items:stretch;display:flex;flex-shrink:0;min-height:3.25rem}.navbar-brand a.navbar-item:focus,.navbar-brand a.navbar-item:hover{background-color:transparent}.navbar-menu{display:none}.navbar-item{color:#4a4a4a;display:block;line-height:1.5;padding:.5rem .75rem;position:relative}a.navbar-item{cursor:pointer}.navbar-link.is-active,.navbar-link:focus,.navbar-link:focus-within,.navbar-link:hover,a.navbar-item.is-active,a.navbar-item:focus,a.navbar-item:focus-within,a.navbar-item:hover{background-color:#fafafa;color:#485fc7}.navbar-item{flex-grow:0;flex-shrink:0}@media screen and (max-width:1023px){.navbar>.container{display:block}.navbar-brand .navbar-item{align-items:center;display:flex}.navbar-menu{background-color:#fff;box-shadow:0 8px 16px rgba(10,10,10,.1);padding:.5rem 0}}@media screen and (min-width:1024px){.navbar,.navbar-end,.navbar-menu{align-items:stretch;display:flex}.navbar{min-height:3.25rem}.navbar-item{align-items:center;display:flex}.navbar-menu{flex-grow:1;flex-shrink:0}.navbar-end{justify-content:flex-end;margin-left:auto}.navbar>.container .navbar-brand{margin-left:-.75rem}.navbar>.container .navbar-menu{margin-right:-.75rem}a.navbar-item.is-active{color:#0a0a0a}.navbar-link.is-active:not(:focus):not(:hover),a.navbar-item.is-active:not(:focus):not(:hover){background-color:transparent}}.column{display:block;flex-basis:0;flex-grow:1;flex-shrink:1;padding:.75rem}.columns.is-mobile>.column.is-narrow{flex:none;width:unset}@media screen and (min-width:769px),print{.column.is-narrow{flex:none;width:unset}.column.is-half{flex:none;width:50%}.column.is-one-quarter{flex:none;width:25%}}@media screen and (min-width:1024px){.column.is-2-desktop{flex:none;width:16.66667%}.column.is-8-desktop{flex:none;width:66.66667%}}.columns{margin-left:-.75rem;margin-right:-.75rem;margin-top:-.75rem}.columns:last-child{margin-bottom:-.75rem}.columns:not(:last-child){margin-bottom:calc(1.5rem - .75rem)}.columns.is-centered{justify-content:center}.columns.is-gapless{margin-left:0;margin-right:0;margin-top:0}.columns.is-gapless>.column{margin:0;padding:0!important}.columns.is-gapless:not(:last-child){margin-bottom:1.5rem}.columns.is-gapless:last-child{margin-bottom:0}.columns.is-mobile{display:flex}.columns.is-vcentered{align-items:center}@media screen and (min-width:769px),print{.columns:not(.is-desktop){display:flex}}.has-text-white{color:#fff!important}.has-text-black{color:#0a0a0a!important}.has-background-black{background-color:#0a0a0a!important}.has-text-danger{color:#f14668!important}.has-text-grey{color:#7a7a7a!important}.has-text-grey-light{color:#b5b5b5!important}.is-pulled-right{float:right!important}.m-0{margin:0!important}.mb-1{margin-bottom:.25rem!important}.mt-2{margin-top:.5rem!important}.mr-2{margin-right:.5rem!important}.mb-2{margin-bottom:.5rem!important}.ml-2{margin-left:.5rem!important}.mr-3{margin-right:.75rem!important}.mx-3{margin-left:.75rem!important;margin-right:.75rem!important}.mr-4{margin-right:1rem!important}.mb-4{margin-bottom:1rem!important}.my-4{margin-top:1rem!important;margin-bottom:1rem!important}.mt-5{margin-top:1.5rem!important}.mb-5{margin-bottom:1.5rem!important}.ml-5{margin-left:1.5rem!important}.my-5{margin-top:1.5rem!important;margin-bottom:1.5rem!important}.mb-6{margin-bottom:3rem!important}.my-6{margin-top:3rem!important;margin-bottom:3rem!important}.p-0{padding:0!important}.p-2{padding:.5rem!important}.px-2{padding-left:.5rem!important;padding-right:.5rem!important}.p-3{padding:.75rem!important}.px-6{padding-left:3rem!important;padding-right:3rem!important}.is-size-4{font-size:1.5rem!important}.is-size-5{font-size:1.25rem!important}.is-size-7{font-size:.75rem!important}.has-text-centered{text-align:center!important}.is-lowercase{text-transform:lowercase!important}.has-text-weight-bold{font-weight:700!important}.is-block{display:block!important}@media screen and (max-width:768px){.is-hidden-mobile{display:none!important}}.footer{padding:3rem 1.5rem 6rem}</style> 
 <style>html,body{font-family:"Open Sans",sans-serif;color:#000000}body{display:flex;min-height:100vh;flex-direction:column}#mst-main{flex:1}#mst-footer{color:black;background-color:white}.menu-list a{padding:0.5em 0.75em}.menu-list a:hover{color:white;background-color:black}.navbar-menu.is-white .navbar-end>a.navbar-item:focus,.navbar-menu.is-white .navbar-end>a.navbar-item:hover,.navbar-menu.is-white .navbar-end>a.navbar-item.is-active{background-color:black;color:white}a.navbar-item.is-active,.menu-list a.is-active{background-color:black;color:white;border-bottom:0;text-decoration:none}.syntax-keyword{color:#0000FF}</style>
 <style>@font-face{font-family:"nilesoft.shell";font-style:normal;font-weight:400;src:/* original URL: https://nilesoft.org/assets/font/shell.woff2 */url(data:font/woff2;base64,d09GMgABAAAAAQ/EAA0AAAACrJwAAQ9rAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAP0ZGVE0cGiYGYACMfBEMCon5UIi2RAuVAAABNgIkA4pGBCAFhHgHqghbrT9yR2Fju0phGOopJsmeA7PC/tXJpkz20psVO91joQMFctNpge7AUSj1L/yy/////9SkIWOZXDG5XqEoTMT9tkHOXgMrXIHhSNjG7NQxaWBNyNgogwwc9V3Pl3UIc560YygDk2D+xYPe2TwzMg96Ul/P3A+qq1dR1bsil2vjvm4VJGbjixScrnniGKjHu8KhRURvmN5LbdFnpxsHHAlCRoKim4eaUvh4NI66AmNkkRpSbaJsSLhNXKy2VYEU9OiD6Yai3jRnrEBQ8FUkyJCDMsxKyAqs2zC0wAWR0RPJHYYjQVZNdJwXtuRzxRfpyVKDFJWshaxDfPfrgx7ljeM3f/nIAxtV8KRXtEXn1817w0/482efF02M9lpOC/59Rf+gh/X/x88HWwWUY3NqJCsnT74Wkc6q2d1ZiWAQ4WwQnBHkB85E2yKctU4EhzNJgI2DnGSR5SyC2cbY55C5zDnhkPCl6LtHF5NT5EJI+Mzc67T/P/dYCwKGQcAgBhDRAoPtTfbmF3LoYhV+rmKsfv+KqvxT68jTVzyQ8Wv8gWt5itPLiK/QiYxkE9ekJ93N3le6FhxF40f0KpLmA9/PX+6BJVycJpBFcJim9m/tAdayQ/BAI6C9T2C7l91EBjbhLPQsgtATDbTh+bX57hqOuKCuuCjoSyIvkuPgjisOjkypEO5jESYWYmIUulkrRSfW3KZuOnWbOcylzsiFzMfP2f9M+3SmfE1yCaYRKJUQTNqtOO3MvcgKJVRW2d3u7rs3c845yf9O2q91Kk4ElZBgWwEKhFypfEG+C2/rnIgzamrTDjjnwIkZdw9SActAZFD1+/F+z557djZhj+xIEgiJ5RPNioR7KoqUjjDAwuUJlfD/+1furcqnlL6sREm/YGYWTht7ONFtb7vngQ+udm8bqAhctjLfRa/rllm+UQG2e2sLaAD5wzLehr1uLNTE+hEzhjDcRPs5pNNqprzRPc9oBLssGZ8AYh8A5P7QXjVSK7UGih2wLLSUmHAZAu1D+0Xzm1/wdfZShyTUgSQkrVNclrQpS1nhUs7dOeAyhzmHOxJwS8olqKw/d5fyuUs39+ZOhtldq2ulcAqngqjUSeXf7vM1k3cLOmfqprDf5xu0jMtunj6n9pIdJ61k2XXx756ITp/5uiBwoNs0MZTAIOld9PZozd5E5oJBECI3VGQLinBaYviMsd79O5lMSorASiCQvRRfzn5bQuHLwtcBqxrVIdhmp1hBiAISgliJARaYmAgW2tiNCdbsbUbN+VbNqdOfonNz0W6vy9/2/iL6Y8Am83wbqMUAwjYQXHb+Y9NY5bwIoqVPt8EN2P6kvsHJCeLz76ta7f+AaIGaREoT6ElZG+2imxRStaG7//3/8d9HIgJBIhAgSCqApC2DpAJIyhp8AKQgkNISICUDIKU8Zx2jJoGkJEOiLIN0gsKMqUnRYWPOFe1NSZvlbsstp2g3pSq19bTbbVFuUYZ4+pC6Zvf/VC1bULpAnZPkqHXOTR1S1/35A1KDPyBvMMPzgkPpDgR1+0hQ9qNAnR4FXgq8GCvn0Lkiod33uFwVIp20jqF27q6oXfWuKpelq8pV5//fVHvbGQyxm4+5/+cu0qHcosOm1LkrHu67D2/mhRlyBqBIBEoGIC1JSDJFUNpdSvvPDAYE3wxAiJQcRDkpOaRE/eiYO1duuhDLUNSuCnorrV00W9StCV3Tlpx5qJAuddu+TXnqyxUSkhgEFmAbd5jpe9y3t3rZUkeAIblNXUIuTCmR8VqsxSIWcQjIr49Pc9k0zwk2vjTulea4VBB0W3QJdqVdsCc/MWf9J4KkuaV5G9e4NhARYwGlDlPfVO7+IHM6EGE4bfds1ymGLy8KCAo/h0Dw/C/b6H1AZhXG4egNhoJxrU2MlkMBsEZcn/ua3vzadd3rbzypKpT9WcNwVmJScHjGywjxAIrYzuUCK5lkHlM0wEMplWAEpwQBvlahG5b7MRlpernr0gZ8VqssSYxUbFPIGyzg4kEfmSkFv69H5Fbm9S/qJIiZz2rd2ABIShir8tj2P3kIm+FweaUciigAhGAExXCCpGiG5XB5fIFQJJZIZXKFUqXWaHV6g9FktlhtdofT5fZ4gSAwBAqDI5AoNAaLwxOIJDKFSqMzmCw2h8vjC4QisUQqkyuUKrVGq9MbjCazxWqzO5ycXVzd3D084QgkCo3B4vAEIolModLoDCaLzVFWUVVT19DU0tbR1dM3MDQyNjE1M7ewtLK2sbWzd3B0cnZxdXP38PTy9kFjsDg8gUgiU6g0OoPJYnO4PL5AKBJLpDK5QqlSa7Q6vcFoMlusNrvD6QIQYUIZF1JpY/WwgRQaWjSVskprtFbrtF4bSrmoASAEIyiGEyRFMyyHy+MLhCKxRCqTK5QqtUar0xuMJrPFarM7nC63xwsEgSFQGByBRKExWByeQCSRKVQancFksTlcHl8gFIklUplcoVSpNVqd3mA0mS1Wm93h5Ozi6ubu4QlHIFFoDBaHJxBJZAqVRmcwWWyOsoqqmrqGppa2jq6evoGhkbGJqZm5haWVtY2tnb2Do5Ozi6ubu4enl7cPGoPF4QlEEplCpdEZTBabw+XxBUKRWCKVyRVKlVqj1ekNRpPZYrXZHU4XgAgTyriQShsrDA1A1XTDtGzH5fZ4fX4AhGAExXCCpGiG5XB5fIFQJJZIZXKFUqXWaHV6g9FktlhtdofT5fZ4gSAwBAqDI5AoNAaLwxOIJDKFSqMzmCw2h8vjC4QicViRyuQKpUqt0er0BqPJbLHa7A4nZxdXN3cPTwAjKIYTJEUzLMcLoiQrqqYbpuNp2Y7r+YEl+tyssuB9SPyHw9jnMYqQbtZfl15FRX6q6PPoHa69wMELHw316L2zWCKA0fWGHopPA3JMStnCyIFPWV6ifQhUEHRpACBDSpKHPBZBTgrIayU7oxhzfLLKtMCxMADAyUsemc/Dw1+qtqb07IWByoqlHWHWa4A80b3Fxhk4BP0h5QtL85xcb36wQxmr4kg2Cm8RKAVYeVUxKJ4EIECjyuymGVF2mSYEK2hjQwz2oAyW2AtCUCmpXE6MJoXMyc0iG6mwzI3B9UP1TIIbj0LYVuXxvEaCJMoQiWAaGGTnuBQcGL4g6jgiTpRFJAcpQRnOnqBQtTbDV84ZXk0ZBRZymgMjByq4LfAUyjB2GuDtQFUMg0VOY/gUSyXgMxWcGRBK6I+I9aDTwyJQAEKaNq2qEJrNWHkVAhh6ZNoLYpCRe1wokhsN4LgptQxgl/sDblxCQSxdB9MYWSDqABqhHoifxwiJcPpVpGkySIO/wW3jBxC9/HJm07bqUNMcIMQzBiEUryjJR0VqpRuaqpXRC1LHaYhNb1zaigVox//vbLgcH4+NudBJe7MhzZUNWvGecyLFyhVCbMCGdDAHEyuiJDkLL+Hd9DXdWLzx56O4jqc62Xj9jT+oOyUlU9ABgS+zMU2keave4tMUkQWacClNt65wLHQuq72iJJog7cf0CTzZRyeHtjUvwbKHd8GqeYaRbrrOREyUPCsl2zSVUh0gh/wohKMm/OV9Vv34elpqMIswosZ/E2XZ+gI6SzoI/3S847Ec72U2z9jH503VwEVal3+oKg6K4o5lpQ5THz169GStZOC+TBrQmKjTPS52yigCCRKkmzLtqfnYS/S8vEJzppmhaao+Pn/rg+sra1vxMawEiNA3rHdtbugIAHnzTP3uNd2MdK+8F9XPY5Oc+Moc5voVGp5iFqIQJPZo7jjqPH49Je2FLWrCmDcQCzyNSNOmrUrdFr9FADBdF5m2FVL2TwR1bUPaQIuJ5YBneSU00/UxFone0z8IAT17a8IQhv0p0IvIb7RTTqku1/7pti4uOQEg9jIoYvkbncAL53hErJX18ZoVDuidKfHTT1Qd4JhDHsoABugAWmPsGlADbt+DL5PAp5XKorhXxSj2461KX6jDRaybA+9K7YNifVr2zEXvdx1yWtvG+cRZnSvLac33hDXrZKlx0AjVAQCzYRZ988yTv3/XdIX2vyn757vsl/e3Wn1398QIQhUnnunub9eu78vnl+hyczhkls9fnijLpl6sqxewnms5u9QDEe0ATrXtiMqCzGptMkZnaanK4rKTpeTZwaPsQ7qWWuO4pmwkYkkOo9pdIsmTlSNRCL4wI57lqXqdPn4Esf4sMwHVozQd+LFMk7cFVCrLcB6czxvz+X73f6kD9qlPPK9dePZXMLiejFxXnByiIICzoIDpFtHpjUmdkZzHke7+SUyJ76ZPEjHn7fWIN0T4Yp4i5JgYcwPGIBjAriN6XDJrLoyBmTGhxz9mRlWX5yuTJKflnvyV6z88XJzha75u3vJVUV76fpEWtTvPy3TPFVFUspIwEwnzCs0saO+t5mLGfgp574DTztxst+CGeWYEAQhPVIHTsgYo/ZqUu4v5kjoUk1aXUohYdZVtVEyrMewLgE5HZIwCcLDTXTU7De10a0PqzqUFUjA22vcH/GKxhGk0ac4oi1nhy0q9ygbV03CAO3kH4mQzdosxvMDO6il/2F+tg2WHAVOWJLneBmZ5sw7LkV47dG4gkOU9/REPx4SL9q5Ajo2It1AO9+vwobgb6089Szk76ZzqE27BL81eN7TMOIjZNKF2EAY75H3JxZy+ubm97bqtPmJ2z7I8EknSnif2oypZM0JHM0Uw86OHB8KfMgj+x4gs7gqR1fekeoai8CGrpMTMV5/rJmZPnBsfb//+Hp2jNIAgErJ9wHE0BRRv2XajnzgXvHneCZ/ZKJYVwaxcgf0fYyvyTg7WvvjSupvWWBlZbS7N1P1+Xq+nNsDYluh5RjtiM44dS8KT0IctLLkc8gcs46QFLhZv4/Eh48u3WAE6l8PfatSJK50HuCmO6fDW6dzRnS08TVI/2YlJUcNdHepKrMwxOK8LrfN8bYfBbrnUNmeEJMuKctAAOTD/S1n67kehsNvEZacQDAJ8CFWvmnbDVreu6xy/EQcCz3OObbjlMKDwN6FKYtKcU7wDdwxHb5pg79s7aYP4paQQcfo+dFs3FE2LyqWTABe/tX8h5mfMz8XQQBlFT3il94SSF74U4nhnNk63/qR7clmpOH82TdIkT3SuJZBcBziefnPR9P33sXpFCyX6EDeDVROX66EXGnDERL5YmroNfPmhCM17GyFWq9d9JZUAtOC5UncHD8LSI5OlEtF9NnePi0nztmbT8Y6TJugIJ885lyZ/bIvz1eXJ6goNDXUWyUz1XCLusUrwyHxv8wjc9axzaVeVkXvgZP4smLWQS+aKqHRjQrD/cWzWRZoX+l4bkcn+q1Kt0mYcVFZV+Sn8NG99Dl7YNGuaoUsRHuTMJYzV18kzu5XbgByByfXQffgj9qvj6uHZhxdKI8TESVJwUxnTdGS6vZJsNMvm+iQ5dIeNeBxYnrkaY/XWRAKgpJlVHJWiSy62qr9kxmSydGo/nc0waSDAFsdftOE+jj6ALpWWXVhCAQVDRnF/ALmsTSAFDFLkpaTwdy8B40F4mELDNfudyZjuD4rAoXpg8HVST1lob8lW4jfc5yifk84WUtMz4aCqvAsJFqQd5wTFHMTVl45cRAFdyK5sH3Ew1ErmGbxErEzKHQaAbmW5cK7+b7FBHF+GIoBdiwkqw8jM5zihcyzXccQYxoJA9FP3chol4ui8Ddx2zEZSj0/zxRDSlkw3Tqgz1Mh81vJTyzkhYlsRPBC49+i0QmwkfJ4WyvAstQRttsZI3TU3koxvy1tCnGmD+gIWKw0ylEyq7ZZJ4hTlAhUSCs023HkktUZLMSTMtvVWbvEc1DptfYrv5Uoxes2h6Y2wOnP35e2LKXOo0Ry9VUaFietz0uZcVrChBBJ/yvB0qtT2HuCV9MjuEBKwvlafAWFddc5IVvtfDcjIiEipcKTDQA74giaH0NuQeGyCRCChQA6Ivy0oA5x/dnwFIgkXalfIw2QPd4F4BZmJ5QhSgWgM36YOWDbaSFyvd+E1watDWvo+ogIAazYC21hLRcGDQLyL9rJONqzkdb+OE4Aguor5QLWd8zHTjRWWBukBpLtVi6mROr5BdbQ7F4I8yBmXjkykR0jNhOlbbUPwU5iJ5rR8A7CxX0gmfB+TbRKeZy5emNUKAMTbp59uebv5ybbrZ29tJXjtnCd4IgTLMw9j8sG52/EU4p9tI3S92WYOsMbXSiDB5K0gCrxfdT2s+bSxEoIFUmqeKWeR6bS4uwsk5CO1Xh2BxZ4ztYiNAi3zS3ik10C2ZAopiOkoYRlhTnzj4aJpLsJdICCjHj06pjLYHAx5ILl/nkR+HegAEsk+PsLuZKjJaC9CQkdgB4k9A42Kb8ADLLmYQBJwCHwBAnGMpFM5lduA7FNkF0s+7CtJrrh+BsIx1ubLmPkddPx4NV7SabtVTuKs7JpExBhigJt4yTcgyHYg7JXPhfxngUthBVIUxwhg+ecZtVlDRtIdSFr8AKWA7NkHTQdEG0QmhJ4jeM3NOnLgnZ8XA/mSmlvrBGvysrymRJSiFuVGrUwgQ9P7+ZENfPayjYkNn3ZnA+0mKwiFEBy9SnrZzYJ61jy2Cyi3LFhbS/f+BHMgCSLlQGvkC8RuxgKP/m9ekzleAQUurIYelqMMJ5Dc2DHosEhLz6lxQjUd8jP2HDkqXZYvYU9Q5/mof6wFaW/zQezLuZH8xTWJ86fyxJBYA/lQr/zUFqIgmxq4aRkgjrILNwC3SjYS7T37Ivog1Ts+iazJQ7PI+Xi4gsIyIIJjlAmvPmI1VOuqqb2BtDR5TR14IY4awcuUogpp0tgEf8XkpS8bT2G9NRm/ZnGjdE+wJEyY3chLOmFwtfCmF1RoZ+kTXNjlK2dXahqNJKnpluSEA/KzBHPJJ/SNexzQgOmyzmDyPxetjAUphIKJ9L+8Pgg8ePrJmCmD1uVCdpUOxbF31HLUd3ghkSP/4gJA6oqKkCmgO5UWCt5SDXza0aIali+Bjg8tEC6oSTmp0fV8cc1aeVlGZVBXVAjBd3G7bDIxUG0EhCMujgrOXMzkGIxvxFuyUPLHvw65/DL1tbSOMQpFmBMye/y/WEQDRgB1Og9rZ+essUoXBWKjHSQCnJyMjAsHWVMVvtJpCxC/+VfBD6kO2azsbB8+91x4XDC7X6TDTe+NZrtR0tFxcLvhJr1TnvNFddah42BrX+rMr9v2LzDEQUXcxrRwQdiwBfn6+Qqe+J0SQ9WYAPx4hs8bkp58Lp7iv8RlHFORlnwT4gB9TCTdBEP+/Uwv5J8ZmKe0G34BIJXoOTdMNYsJf6K8KOWg18s6Wtmky+KWZAphlcWUMmvLUjtcXoHRzjX3hVJ2a0rfts8c7cQXt8Jw6QZLvCqMsI3VZuEVSqoBy6qsBrzW77pC4bUwo/iiCHAVbQLrVJl1ED6u+Dw8ElgLgTPEBZARUqX6BgTaT6zz2bPAFcY0lZt7p4ZM4s2LRiG+c7yRpNFD5XSi5V1Qk68vK4gcI7zngHWGqq9jYhSh0kxoSCItEsyCmEDALpcoIPkk5GTtJuloaYqdcI1HkoJqkv4o1pUeg5rR5W5Ga0Yhmx0fV2KxZnjmQEJ6T8FA4zFdtVA3Gj74LygX58XpQknpkAg6BSGIMUHCuOM1C8/2MKy4o56Iy0mvmV2eqgZ6SjENW4Lp1WEiXb3QHRPo0z+2EApxqHLcQk0VK8klW4sBMD3Z4TszJVYBNZ9I73ptH/E1NsB+MdjHg6fQIahyo5HGthYpLSCBNjXB/JtRQx3pQeAeSH4G5a1DesIOZON/BKhZffoLfKODYR173cYC5lo1BtuHeG4TBh83WYsktuteO4jsiVHJYli1ttPXnEWlCwW+pa+v6dcOQuY0w/nAurUCye6hyFFxtF3uje0Ahlp8hEnQekb6DoMBwkFz9Q9OBtAYO4hoHgDk3o56AYb9E0FW/w78K4ijk7ipTOwdkoEvpDndNDfpo2fVBUjCYwxnZL1uZ0sroVn0Pofn38BmxZ6+Ha8y9Bfw4mo/XrY/dsuVWVLTSOfOwwe6b8tuc9SNHB1jmPgmYUtD+GXeM4OCNnBk+9e+Bdtc8iCDfnzYgsR/5+0qnZPYtdgE/m2exyCG3hHLfqQvhBMyNLoWdFm7+Wobn44T4d3sMwPO5ufZTeHOCAY9o3+WIxdvb/s29fbuxlsqSW7suLq33369p733JY3m5EJlmschf0RS6sVT9IThMfeFA0ikcgysURMCTCYatTTMit/c2GsTG+jj00wSr6a7Ubf5JgIULrBPztx0VEElz8WPzzgjPj5F184+2jZsNT6RTO2dFEsjQxcI0tLXRWGAB6El5oiQXUnyyJsgf0yLD5Ex9xNz7KNCvFNDIy5pPj5l+Hbd7rnX6+X7E8gggycP8RRyGdBudG7Iu1kNGIgdq7Fw10aLxVpY+YQGFFGgClIHf+8j/LyEj0H9NfHvgYrTW6MQ+wo8JGfbcDNH6vR+fHqrelZG9+XwXu7VCMdP3Tm6JvAyP8erIoL5rzQlI+56xmvuEDGoue6uAzjt4G9X61xzeDE2fyDOY9P0R5ORErzM6aL9VCdCYE5dnhPe5tuY7+c7MMTBw6LwGFKTiwwbg8cg3NZb2eIFZBySaX68P8yMuJJbw1Vxk1uorrw3BDZYXgmBf+gi47bUoEil3caeRTgZY4K6klTY+K1FC5q58nZ4eSWbiC8mJnhnIy8xDwD5aAiJfGOXeyM4u5gRDHOhkIBNevDey+hxDRDDCxLmU7eervOU9lBx0CwDNdkgLydoAPB8CSSlalpG04laNLkIdG/ydJFtfAsBftKKc0MtWx1twnxn4OUjMT1xHqltUXUfXNwc7JbPZeDLior1sGnDZVvtpxD0BrDQjglA6eqV7yQq0xWBG2DPMwAH1837eoKhuGJmndwBrag8KUwA7n9Ze+iu+jZBda4pwu35rbuU2/7EnCWQ+cYvNW1pWRnZGyiMsl25giCDioAvrL+eOD9xajttPns2P78UDifVa4ZJFpWDLSVf9pD6KCcoLML+NCoDkUDabymLNk3FWEH4xO+Uf3bkq2E5OtV4JWtYeJIgzAaVDij6Rh8CQWJWM2IYBmd7PvrenM+uRn6tC2+Gv1rrVG2OjOZXjFNuy8d3y7uuX+hRxO2hI3DXNP/2IIgoBgURcqeC2+vg0VO9ZmNjHPa8KlVJ6FcZUre0X+Ynkn7uvwswUk3fTc2XVkq4dnUyGGCp1PMhn8D2GVkc5blfRiQ3JvAKXD6Wv3ZbWsutGyl50Aqh2rmzu4tzKeP8mpUPZaJacwFK2tG15mjgPCYl+Ykt4T1bMK2GoVbl+SwGZVU7C+Vn8TZ7yKFypZpUVj78m57ZkACjbGujRsNwn7zUIaKPNt1Jxjr1nR8gCpCVME+2s7EwTzfBIsSzbg6rYh3N2hFb3Cdny4QgOs09DIdRT9InQrC/q+f/NGE49WgRarDiupNJIGjdCzPxsD5ljP7gl7E88bHG8ko2HfMZl15MHE2VHWM4HUaNMc/Y53p5PT8g3x7ZOAeIlMtjZ7zGqxxY7Jb6XzMZrLkWwSdqs6uzrZfnQsfShReqW1thEh5kmlzY7d4OCbSPbXSXy2Slk+jtotCjOoLsbelNvFLafjQuQ9+3n93hzOc7apCZxco78eF3hmhAZHb4VN40rFc3BLIgwC2GZxP4vRu9tE5OenaNU2tmHTl/PXJ4vaGiVc1sqrGFAkqPFvVNUWs0vOjaarFKpU615fJGNiNajDFuO4wRhswC9lYk3+bV0lSikpQ5X9vg1FrNlvSmGcTIAlQur5EoxLkRtJE+CdzV+v6MgBNO2FbwktF32qTo0UoHagdW60q2VmQ1jf11qysbbInHCy3W9g3zaxm4rpBr4vIOoN29v1RVrObBZHM8b6vckbxJqqE3TGMmJVrJRkwbrvF6dufyXFdQl5SbFL6+FyLt1E6duAcIjcl+gxWtohS1RFfa5vNG5a0QFLb/+SsbRyeAmZT2RKprKa+1tr5EwWfIyijwNhWaSFBGvQDWMMAngBBW5XDBQGFtxPY5DV2KDTVlUSBxo7XB4wtQtsqDffwjlaxeo2q7ToGH9Xal2jClCtSrNjqKWNIc90LxrNJPhxnFLAMu4yH0waEQx/IVUpYPkfAmSRXFgyKXWUBGa65McLb+xSALybLMF3Rlsm0OYOSxm2hlluKiW924B7AfDSwtruMZ0v0Y8+vozvBoUAahH0Eoo0aNe46Q44PrEMyJD3UXJ5LYYVIk2PBdnB4CwT8P0witfsm0OuEOOI/2an3jzOf4uzhSyhrRzjaQeURr8VYBBlPbz7ewncY/Q3s5mGEQ/k43Pja1RY4NpHX9aL2FwkCzxPnaG2a/6oGGY/NTbi7fTVj//FtYGJBwyDNKWXyxc1oQ+0N0kx39dnNys6hUC/Za8e9GryFu9VI6UK7+VGjW+/InU1O9LdoaL00YV9/R4VxZsJX5lgzIR52Khvqcqwm2gj29UIOk+fGHeAtpFVxFDSKpqBQT/oLoKKBb0IWo+fG3UlafY1X3J0OHE64YSdimsHsjbF3xaJpbg5L94to4YLqTg9a7kz5C4UOxcR5EI9tfVdHRRb+emFQTUTCg0CHMe5DfTqYYxgRXXb+4KhNtJNlbOE/buLSmS4QPY9Tg/DBujTlVCtltGlGLm5g+rRmRVSvWAVY+XnVsiR2gqVEQL3Isq7fyj4scO+V6ALuTb662dKbe+FOtOQK7Kx/29P2koj8Ior+ghG81lc1wx2imtv0u3a16hlMe9kX4j8XXD90BIN6L+SBsLPnh/yAMtTIxwvqj1VQ5EsG1Seyxto9hiKfZQkn5SW4/SI2oNWQe8Tg3w+6ivhA2HOwXgRB9WCBDC7NwVS6N0/1NpgL+73pkBTckPCG+1qE2AU9QOPs8D7yCS/8xnmcc32WFL/RD6jsfskzuqb+okUvJrzapkflyua3+Y8xnH9upB7e3Ab078/hYZWOuJV9KeKnGxn1k+RgCyFYXwOtLmGBi+PGvdGt3jHvlhM5MwjCx0ZBWp4vZQQI3JaJBTq690zQSjQjXZO9wjAT+toDw0Sn7JdnFNfCLrBjJlu8uUQ4YrKxb4FuBKQasMj0dlAlpRFo3pWoZUn8t4tbzADKo+rZtpedS7mopYDHsxZ2zPUtUM3jBe/CvEk+61UY9ZEeXgfw9erNmgfprvtvAsZMSfKJAhtW7isXHwl4gNv9F9IPCot7b62gIvk0Gs6VVMLGfpgZpK6n7uY3FJSj/xej0Hkk4/0bDGV8xPf81gCe8n1Rket9DdBGJmVbB25fguCYkSQlKq2WR9c+W1emvoIS89c0/mdzz32mcCLCNbqgrWk51igX/H/iL7aLQR9aplf/NdAi8jtdKm6WkVbii7T9HwADd4Ln/1a22yi7Fo0Ga2zFoNa6GrfgUXBlg4Kv6FBaWWfXTqDaqsPM9xjeWIVn5/tCq4K0E9aeWc7DumK5kFWwP/vJZNrKGhngg24FkUip0SNAV/2qfouyB7kyNU2jGD9S9x67DfusiSQxDMvRxUqr+tXFx8T2SH/1/xzniLv3F/E25NYpFbH8hPYi9KbmRcveJ+G1wpj2N4/42i4zALwBcJtxWGgs7upJNej/tTiYtg6S86Cz99luNqQ1H+YX0XCnhVonjokxvxu2FRGMYlkO0rSGUA7f0KYJM2NpjPtlGgUnT4hdITDD9R2X9rF6/f4KVEBZCsOa4BDS1J8xn8ZkmDzVYQ8aTlqKZ+hqiesh7tee1b860at7ycPv90EQB5BshLHbq7fgy8KbdTbPZScCjIIVcBZFxkkolGfFwYqK5gc0xCeZYgZ2WB2zJiv/nWsVOXgDE6mjdll/B36By7digf1x4AS82NTr8tlT/4jqilwE6eZzHJgmM5VtF0A3IFesIVD8KFqUrqDeXa9OpNplwt9tQtCR8bDIWtlGmGBgOGYm8rTo8Jfv2sN068wdx2u01MLANjkaA0fEc0GE+hXUUnpD1b91bq2HN2+shIcLQwBVoxY/oHOxbXG99HuxRd+bXPPPhfkWQT7DSI6lfaqOXud7iqJZTLEmySWd3IWiKK+E/tDgIEuwIPaQiRMNG6S+YG5pO9OA0b6MWRB+d00oJRLu5zCQIfqLhGxUZKlQ5JUcZlcdvejUWpN7K3CMpYpcFj1/QojAYOeRZlgKnJyGOrfEcgRr/f2wRagVME0clkcsDZBtZG0BBTeILAsfwXJDRUgbFLL2RPlwquPRpSU2loky22tuT5SxXcJOKYYJ+n59Q/yo0Y94Ju+BTCQy/wn5LOULskIstiASZ0EOUj5FWxxJt6rGL0cOsL3TuPc8kBOZHqa6ltn7ZDUzlnOEOTLkpC+keLxpPX+UqohIqGYfyUcoiJyiOq92cPW5m3gfavR2nIsYyp5PtUj2mI9PVMRJ/jA3TL7arsOJfAm6yWbr9McSdH1zx7e2KcL3ZlHKG3FRr7qRclo2N5EsfCgjtQcOzKmKi5/TfNDUcJ84Qt0KLy1k5FDJqI2clnjuovBcRx6ehyI/jROgG5kub4W3gu2lfH2RDH5rbcfWaWCN/JYSKnfBdyOqku9WpQHMFzRhLJgL/iPtBRmHlD9TfDYV6B2ukd5C6raiXVyBZrUb1To8JN2+FgsHs6VM90Wt7rITVGb1dsYKmV7IWZjeXP58zBADGQLaamJ3YfcL4qInqu0WtfslaCqLgzsZAJVmF6IYyAhW6Bjrlk8LcybyYK+p4vXHU9RiXR2wiEZX6aq/P0umXHvVabIaTQOml0UPESySzZK1vIXVLxpR841+ulOJIOY6xjZ6LPOVzvOfcPfTlH8whGob8Y7vU3g4hMJazKyxm1UZ1frbsdFLz6Cl/z1dM8HNjtN6xIus3xu9l3GHO8mUc4hgXeAgysUS2ZSMPvtAaJbZPdUbmF2m8BALcuRSfwPcdfL7Gg2wtojpBkPWwH4nl7fY/eIyX3Fbg9Ly3IINtOdMlnGZwPmzP/nRgkf2OJEYQNj0XeZzAVTYlfjEjKMepyJy8nKAkp3lZpO6Oc1J+f3gxusgD2u9u8Hx3yu/9X+PM23C03ex55u8+iFyL+8zTQxB89wi4u0qk/94Tuha+fU9l94JQjHHnCfibuL+56VHxFwT7RoIe5V/879HnHGzwMFG7Vmi0weczyKgW/64D/3cMf5PWPoeKSZNbD7i+fjdT+PmqJ+3mo9ziYzBv7oVIz6rbMS94ZDzjvdVxiKEudemP5OFSZ3lhZv1C1hYFjLGjftxZyO4FP9g4EaxOgiffjpxHPF2JrZycSMKnnuRJX4eaiFJnMKqmfyUzJV3GpC4j48IKLKJNh2eXojNLVhI70lxGQh+Q5XLNc9S/zAN5rAvscQAyYzI4LE7aqjpKGcWvLL1rOMrQ5hxyEz8L34vKFpUSjkVYhh62tsC9A0ZtOWJQ5dOVJg8iUPDNLTglsqkS6Q+N9E7uaCc1GIa1MhSroXjmwxk66NhFYen1WjYt89fOaL3gM2TuN+UlaDDa86xxM71kiHYBgLU/jr2Ixc6dpmRlPw7o1EEQqggOOSRc4I6kZgDYUUv344tno8J6uM+4EtXKU8rUYJjt9F6MaQ/xIg550W+GUI0jCGMddu69LG5SKbGOCpJS9sBAHuvo2ahX6Qz19bdsxiwzFlTD6V2u+Dmq9T9/xxdgcfHt/O0UFVTNW2vW9PW99daqVe+8A1yLiYJOKr0KoBR5VdYJAL1cds3z95wKyaCokvJHWLq6fzz8Vm7lFIj/xMdlTBXIE13DEjzr2jY5+gBJkS54du+q08QtiolOXWBxVbF+32h3fKKDE9fL/g3t6I53nuP9X7/lEyQUkoy624MD5JVr1xlEV1Vt077R+Q5lOwXWO+Oni8UzGO+kX0yZAb5R0ksStnWFg5QP/4T3G94MDkjTGo9x6VA0v44rQQZEa6wxUvL8Q2SXtTxfg6brpuYAV+j6ocfLOcY9G62HJ+6cAThlEGISkkRCIAzwHPseM/MHnMvZfB/HL4qfYdRaiYMj8jKipIImGSyymvksI5fAk5abYZx5nsNTARJfneq3N+RYBgaoq9S1k9xuP0f/aKNqpeUW2keVU80neHJrKTmj2hkrBUT8RrKJjQ4yZSVR8poPLO6X7iJmaUkkqMWE2d2xxR0BxlAvKciu0VBdpeR6CuxAqHOXmO9f2N8UoJns3aoxKr/8YgWIiP9IfpFhC8GAyG6YTrMNarwOCWKYFmgJtQe/e4xc5v0n7PiGiBZLBnQGOvQpW9Ob3xfuDR/4d2TO2d1yz04XcQXFLBUX6ey5fqxAIOeBN/ItKffmcgsIeyA89FxUxMaxYpfvf4Oj4DKDK1sdxzst+HSdfVQqgU8AVNMkXScCeXeq4fdmOgC971DixOx1SyM35eEne2rB47DlMSj7Ynu1Cj/d339wlqzWi0ZeimxCi7R0N3dRKVMEnZoAdWZ3uAlzL15suVHdOQ4ATrLstw3UUigFEVuyrOKNsTvSKLzf+1LwmFQgH4/yYWDkc6yMMWk68zu6alq6t4iBwVdg1l0bvcbAXXHjF7zdbvPMFTi3MoCo9l7stTw/YYmfUY2SrK+t4bN+GLNeu+lL5cKu8e+8E0wivAS6VfI52VGMFsqIuEYh2fktoVqUetXPPwWkACOqFT7nam3QbkIdNsaQ1u7CHDmBUsbOz2Al0gp7SrRYHUizn6q1+g8kfT5tFUilReK9KVVYPUkY5RUVdTomxIoEmSsD4PsWhpSsF6BPFac5Qp2quJ+HCKYu+LAHnHEeAsyf+bACnaGa0UGIW890N7/C1FwqMznfai9+XpW/cz5Wi9RWpk5IrkeTYPkSCuhrcsrAsB3xNyuWwft5lV208h6mDkO2GWmvY7KUc8yAGuqGZB0Z6Dlb0dPYzhM4+EQxRSUXEqdq1okkWrkJ+W9dppp3nJ8gg7V2DIx6an6/l17ELvUY7Pl4EWNp9RwxQ5NjxxMp+cZHSEs00x4WTyeu1hrRdvTdEMy4gX2tag1hVomFCUbF2PTIXsUGMU5fH2IkOj+/+VE0bg0YNNcKWGAm58F2iof2jZgHUC/zcNbZ7nkuJzR8p2FPuTgBCyfFMU9IY6/o0VxAU/vlLLv3j/z24HJvzy69p83uGls/VkhpLuT+jJ2zKNPTal8fpJklI2bsa6cXF1emh/q7BzMznDXvA6aslnxYS69GC2aDyORPRxIPKqnlYyhy7AuzPDWl9PYCJheMmLSvnXr5MjI11LO9Ozmp059HhVyJ4d1Y7R5XWrZNmao6MytJFXUZ+oqpav0zJVfCSWWe0AR6qa+eVcGgE7WanELh2BE1jc7K0TbygFxYe46Cnca+JBNqcbSQj4yVKmYWx6MtJZWKkd6Nc2Z1XMvENZr90oz1/iTT7NcBr0EvfaHUkG+K0WSGU6Fk+o6ocmTVlpdNehlu98qUUZp5AVgjnSy6MbZcSCAVjROZXFrO/wLuXnE2FxkkrW8KraUlQtwzXlED0uYWpPVicqwhrThPnD3g+k99fjFfrXC7aKXZfivw8cO73UcJP3HhsOUrfjuqkt1Z0/hO4eMi+515F+6aYMmgJR9ytn1YbY+yRZq8TzJdv6z4mJxTlJ3jgG876Nj3yShgWYzM9rqaC5qfHa6YreptR4/ycu/efb1wy/dxB7i2kcgA57zGtqZDlfcpTge1CYrYOeKTPfF/ypbMiGNorNeXwWGmIeUqObmfYSOuP0AmQGcDrpNeqk/3GVS4L/aF2kvHviPclVX8gY7brmuGlwPLIxaLCPL+D6LvBmdDyO58LnP+tnHSKrIVNek6MFdv+15RXpgNAC8EGSLuThTdN/IvL8+p9pS+8Hw2g6QLFVrnM1phbFyN6ZDTfHKmhZXMKFE2bqLqYOV3rVmLZeYkQfN7oFpVF8MZ0Am9P2kQlossTCrhjz6cmBB8PcnhNTeoL1snv+pa6bEN6tVwTrDwT6nuYU6pcbVvw5w2hkxIo/AVc7qRqZRpR/Y4+U4G5e/Lil1kyZitsZQOkoOAyi5lLDFdlL1qBwQwLkgmi45OjgO3Ajn9MLKPFO9ObO0V2KQ263fsV42nFPfP2PHO0KRXvKW1H/kaQ4GMACxLfkQIWhTUfiJuyyBrTJS7Q7pHgpSouyUX5cq4HwvEQznje9uNniPBZetFpCiV3iG8rspwkdTM+0IiEwmPJJ904srnY/SnupPLHx2NL7rOtmxlkS22VcWFkXxfoWJ6BvJuTS/e6ezlUcWiAmyEVY17dVtjCNUBSqqIYZHA88aEOY0eLBkh9G9AWfnGn5QPgJsOqbJHf83XxEFH8Ku0VKY6ade4tavgx4a1XZXH17ZfIiHm9GWA+3xZKbp041uHo06VzS4H9mbV/tSl8jOoTownDkmRcNjsDV++o3U+56H5ZsN/jZYaLOHwCAvAxyW6Bl9/cnyrAB/TYGrJOHrNOMegEsN/uNpP5t5rUhuCffcd10XqnmohVFqXvN36hBX8gDJ4/VqE6VL+nC1jTyctrdHIefs0Fd0TiddgPYoXcUPYLQe1ewCwcqUnh8MQimlf+womKUIU2Ys+CtOAaT2ptm0zTntf8kExSyfPlOx8fbb6zauLCqEYlvC4NaPh15ItU98W2TRQbXEg7HemevXlFDt+IYV56QUWLPmMZnc9mMf6tggHspSpL77vW2uoW4VAWSUd58bwhDYM+UemPhvN0+7r8GjCGQRxMDh63EKZiMysobj00HLBnWGd7Al7d/bSlomIsF3QYC8yrxoKWDD3q5nqJYUyUss1fjA+YApcqQj/HcaqB8FVoftdcokVtrRClnU6M2h61F1V2TokojE7xTPZFK7KGivzf/liXbsSSXmqUIlKcbVT4mYmN2ymuMJELWYlN08tq54dM0QVMbroHnKEZ2ACgswiy3w6F7dM7CXzVxsV2IyWILcy8ukrsX6WFVK2fGTFcjX/XKkQD8jYOXAWBf3xtzrmNi2Vd7Y+AsTAI3Dso87nvCOy35rKyqjyIhOP80vjGucJba3eF2a3dgs61WbKVllOLmW5GYg0lLvISOXTpup1i8z6j4L99+MPqWnKHVbxDwYfidrx59z6/FKMtcWrZfLouD+KpW+t7zOdjxV6zkkrJX37V05vSmnpllISiyebpZcVvuPMOxtBvkr+02rhgVC4Y1bdySCixOJT5ao+YnNE/n8+j2+3+7KII9ccb7xw7JlP/PDO2Jh6n59rxVSM/O7/Triv1I6/xpWE5T0SpISIqpaZwBebL5xU38ABx3Ta9k3c5DQBmYdRmHuiTiRD/4tNAFcFP/tqJpm+fKtMfoDYyOb+icYkEk1UyMQ2cWP+6nbzx2DljpAkDHWADVq+LeX6fQ1SfGBDLn5YeG86GZU26+TZZHErrxGYp0SYbUyOeMnfn5zblDAhdUd3b1cDUdHHb91Z7+QEU2zjBJsNhn17pAaYPd+cz/Y0I3KDV64GAqoC1hX2VGgqeVnpOdAuXysLnOENNWHgE6RxtjY+C1qLilnRVjBD6M9rtWg0FEvhhot0gDV3IVQ29x8Inf1dlcoIJuU9sD7QThXNpo6dIsvL8W8+l23NEZerjbfkTj5CeBG9kc29qu7R6pbYuLCkFRSHQw9q9XqF/Tvg3FzEFg7d7HSESiDsrsqvpGrtzYbDjjht/pzSu5dqXYEkEYIE0x8E1HYcoj8WDBIg3kMbVapBja0EXy5Wgdd0gW1dzqK5WZKCtdSDTJsupAUDm44IeGVSpN//Rh0HTi3vWNnHaDBRdrBf3Ndn2yDc7s1AZHgO6djpxGOV6wiCkhCeMByTYqjwk3RxTgW4KP6D/JitA7J7+U1QbI8kfgXg46wO3TbBfUTdnOEif4AczOWwrjIG6MyjURAXaVy/jf5AbjUUadzs4SNqe9wal3yOwFA95nwufzd9r6uqOvtzHJVWJFHTQqL9xkBd9wlbLbboefNFpsnhDRzNfp+oJgjNm2ZeYlxTlMgUyM8D4kqzq4Wq74/Q9xnCYxzmam/7+JKiCGNhcSUQcvg63DIP5VJxj781wd9KxrfmbGfZYjFtmH2T/CNapl/PYQ0VhxJWsjai5zab6FmNTbukONoZHsG7KJ/zNWZUI5En4bdgP02JVVM8lHboTv+Blh58nXLAvtLZueZw+uH4De+oaaEHY18rqSD4MPUgALz6GeKjp9gzGDUfjMReCN39uVl0LhM+BH2VIzgnvMqGCBFimEb8H7efnEvSJjVLUXXG0jjkg2MX2DBtgu908IEMjoWFRNpY4tZKL5LXrMONo5ttsIG0dHZNoHaeJgRxCNQkoxD8GAN5UB+npYVGKdjth2Lwxj0uN2tiSMefxS6+yBH+0oIwATOjyT3+JajG4pgOHVIH4xymELxJxYJBFNlRSnHlyg6dlhCB0sUpdBQxzp4nK7PdFbwSO2mXyA7gFXdbckrPtUSvtjHS9ath29QtBqJgGtJFfMMvfKRcBjOJIIBBLdhsMcCcIFunCvX72URUs+14ZUSzb1Clh90z3iMxXWZdZSK1T4lifWs40OXP5iDad48dmZsccaKTyMcBnumcXat1ZL8EZjKZbo1fC9AQz/wbowosUyyuUYZixQ3tLaQ36joKq/IrwGtl7dbYwFcVNrgNiqRbWZcnwXw/rAlHNHGZ+ymio4gNxtEtm+4qLKmS+GB58O+WDgPyLm+RITW5WCgQZPo3WPP5wbv4nwaey/0connzPlBhPGaCLCEJie4JuzkezTT8oDuFrWWWDp5fsLai9Ywt3I5Fe1tCG4V75uxmfR/Aq6wyl14x9rKgvmh9TBwvhPo8p04egzk4vITCo3bpYI1o97b9K2SCgOTKElQUAbWVFsfTeRPRfBpE8KptfqHwQHDV6khguy0lbLn8Y6ErFsBAP8kV5l8o9eCe4XVbDZnGKtCi2cx6HDFQKfKFGtudk01XLxwoAVtOmmqx4m8VUuQNaQ9tygOjkXdzuWHAYKYvz7mML/hR8GJ2md2Sv4QOzv0XJh/aKTbZx8zpj+EUvOMOzJLrgNtO/4mJu9E+QKoTTAcuiUTEuXm3SB21GiP2Rmp7PLa7E+nA9O9cWALBzb5fHEmEDbqpdmmy8KIRMZxO6pQHSsAGmr/Pl18OvaXk4kMxKaOoYHFAEBKtu4YXJO/T29on9GWTQsdn7MJp+qE34ZDGXhMjy88HSz0Dw2WyILibJkxMC+LQ41Y7zaoWqaYSWI9LbpC4/FipVtLaxbwjnXa1/tmzWJxMxOhkgokn4/9zT9KxBBmPUck4cwE5BggPy6LIZ76dyt1p5NJh2vrnlWlqZodPaBpoPqi+1UUUjMEZApJR1dWr06majTzB6S6EUnZAOXEvPbOr6G+bfAMA9MPskYfqSBVmnwdjJZ30CFUMTcR0iM8SmVCSGzziHlGKyi+JfDVTLDhOKiFGeY2qE/yU9xVzT1jFUAhdf4qUV80FNBwwW+dN5LQyJSOR4+UvVl8qiYx1UFIX8IaKhDiqHSMhEvlqYoBA2xfVAHkzo1XGaJdqHTSAzT2DGsDZxTqQSQ+clo9cUuALbBngvOMb3378sgp3clBRPO4RU67S4nbulBqNLlX8L9MXztYYL81yQ95bXFkxXE9ltY1a40mphP6pOzL1rxSbYwnl5pOVWelqg64n1oOeqRLCf7ICwAMP6mOLxYcTpp2GltDwprKL1Hg8mVPVAT891YYVmoRsOGr1Qnd0ED2Vjc1G78YG0AyKCyoG9vogM5+uHsuFIeP6gdynvefPYgkrPL1SiDZ/Fo9Jj7DU79k0i4IfUjf/9EOuyBFfc30GwNtreaoop8MwlozdfYI7GZ2EAOLemBUHZa51syRbG1Cgn+UVfQknLnJADWynfwGhUOORsYhbBPZfRpdNW/TnjOe2n9AwJ2vWKpmjUa5jsyT0z2hbsRWbKxLMejixppPxenGBbsgRy8RtwiqAknhQNVgFHc7YKLEzO/WlrP58XbUCr0pZ02vN1aEd+QuYgJ+p1/Uh7ZENw0Qfn+/d2OBvdmbdVL7x0OhnC11Cg9j4V/X239RqrarO0bzKa+lbc4SgzoTD6p0iEFibFu0NkhTbry2Lq23pUMyucDmZ0la9BjM9YPF9RcmILyM/Zop17TRhlqYJMcALuixxr7oFfTaVxLRaI+r+XyZy8Unx4JROE+Ftry30rMhllKEGb4Myxg5NYCMHA2fTxbomDk+ZTrpJV0o38gYa95VR2zMYyJ5kAjH4zXolpj0uGMglvISKjgTJ7HKNHgJUUU4jJDFADjJJl+O0twDGp/m6bABNfq5HMO81pgCTpj16KL9YMpwpxRVahewkPLLZTkeAbk4uPUr5HEpEQpNZcqGPCKCz9Db7T2gdQy5AJgZOBUj+d/f2YjeKoPmGywJN56VFEQ9nskEGTlD0IXc00UiLduZrGqC7MRy4vQY0DOA6fXNGSfIEVfuAr8GFEr4osxY0NVy0xG8/f1JPoWuxEHamoDuF71aM56jha7f5spIYvq8lF7tDL3mlvUlFjxvlUE5XloPYzLnF6aSeyrRlZ6Q169MnE6JyjS48R3lsiQthTPgoqo2oZLDKObzFQqP8RkYNSEVSAVEZPyD1xfmyP7wPa+SJLvoPkjKf4jxNXXP2kcRFCddN4uikauBdznh1O+ce+3x6JnTIw8rNz0GcU2g2GUJ5j5I+6e5ujoMnsZ5weFwDZAHlLCZ03LZysJpTbhE06HWMDKABgUF1EFt4Eh4O5A23QVZxkABuQJRJ2tdVyRDQxo081uVPdMevuev5zXSGgkxvl/HRngRdHgr9OMleQ6qBAiCwVLrOFYH/CsIIovz4XuPxd0xidf4tDSdT8vzNWGM9s0a3e59Ykq4w0qirghiamagMJWJSkHBf7QnW0NP40eJzsRejZRqMZyTraIdmL86ZDTfFILzg1sWoHf+sBkHVy4+812uhUsQCyX4eW2EnLZ6EaT3rpdyxfd7meUa7a1FM0sSZxKaR5+PmY9L8UlVAyZlS744DpmvLmMXy173t4K+sBycqb1KnmsGc9W5TOiGOp2KNqn6LSVDIwPHEfSDKoCZhOnlNpracnDXhn5F4yeGk5JpKZEUxvdi4rFKL5O6g48chmd3DpSeL+u4ksJOzyIooF/hJJ9pyzU1O4Tu+B72S/2iPokKAHmyQCmV44/omGUj7Ox7vnEMjZYv86CRQunR3d20yrrzkjXqXIPVkEgcqds+OVj/VBAfsHaxU4DIWx4oPlTYLYfkm6G3BCgYnmzch6+m3vsOy9KIGRvNc+9rBfv2EA+ryX7gydxMs34GK3YbBiRPCH83POMH0KV5iApVJ3UArmPIfqCMl1iG/OHmzGrYrygb3Yv+5WOwNt57dhdjiSBf3jhAbOxnS7HN5dqA/DvpsG1xyFS5ZZrCMcbjfR0R9nzVLBj65oWf05M3zxa9CSRusNnYmd/KsH2496z8bnVCa2hQ3w/NNvAJyuOMdJ1qaw1VeuEcG/01osWUAY2Hy7riTV24ajsWk9kcJD1oXtCQGFrJPfUONuB7IhS/JalGtHk8F8Y3ym9pf2CAh9Gg/KXlk/urhjau62sUyZ0iLjNelvtJf2xiJ7UM59gu2LIcDsNvj8ZuqKmkYNtFcVNIO2Mi8Zl5hsq2L7BL2MSXNa/vn+7lOLm6Z94aVjbxGRXyeW7cBVSwForOaOcNblVzRBKt/vFjFMXFc2XlMOdpSjJ/P5qUppiYyvSUxioNYEmRlNu0Nv3Jy0RKP/m8FmAwop03FwrGXLytTEXMrvtMBqnp1XK/0JcanNrpWX3m/ZN/80lK81U9v3Ncbz798+QI/ue/5wnKs3sOISfUlCiuLc7StIZe3iUO9OlInyKutlhb3ixSzEVHwmyiRVJUf3k61mCuq2rysVh/9b08R3+uL8V+m5S0jIPkW3kfIOQsvKh9RDUTiDLC6ijU9TFqXFaUE1PzwLKK9UlLRIkkfgLosfU9rUpPiwJs8TJXlIyV4UywZRH+llX/AAi6KZBw+Ix2johKJ35TSAYqcJgmShVzsR5gWB5lbkytuNiPQB1QgwI8mKD9UQncjJs3ZpjBO7ePX04Zzp4NJD8Y7TnOZwtNBJzMWOSIY1mohHTWQfXWpvdMU1IC2dEl0o4sZEQeIylie1UyD3Z3ejulWu294H2hmFWnk27eb05aWjbzAgUFgto323uiGU/6S8rhVuAF84iYQ38DEYfyI0tftuwO5gCP67tExlFyIrrvt1Xt0eyvzMDlYfVcZbXEDQi0nddEtjla6OvE1P1IS6/0EIvaS4Q8MmdJogrv9NY+8ZgTIW3ta4YJ3+0rY5U405CxnrNMKIjiBBxBEJCKIWWyBgU+L4Uvjm9TUtUce7bphZhxPyq4NGVM+oejXqYc6VDngq8/POyDGP1oRTZ9OsrksYdRBX1CIP9/WEHVPE4pSenc8tTMsdsquureMh39CLSTuM3lAf669n4LmTuOlxW/Ja5gRVm34G24OMW9V0o5458o+tljDVS7UG4S2JX6FTbs14YWnwm/VaDfBn3AlBPaKSrq0WycogH0muCbSzRwDcdKFM6TCKI0BL1fo+JjEk2p71RQqAf7ITw1HZ7UCuGnLsnqFGLDpKfn1QVlbbf3Vq1VpUPeq/C+8/uYNC0z4R0kHtrwteRvaW2fknfmj39/SOm9fRAcvj6D7zGNr9wh3mLsSvwbPQz3ePNAtXFFq1L0G576jYmd30MoBWJBh53aJMR1YyYYxvqBdScgWKxUXeNKE8O3OB09r3Y5bXZQf4lurZDHWFqEZiKrt+uI2Yy1483nG2cNeOqaMtRGZBFY4eUKrMdadD/szAONSmsFaygHMTFqy8cCnGvxPm9OW6y+hq6XTIsn7jGlAaUVU7sFQeACv0wYeaXtGF+mSIcTa0oX2sQJlQ216GE7bHX5Fu+aH0tdffP+9MBmRtdmMC4KI1bcESLv/jFLEYefuh618YIfkxX3aCOOxjsFWUekLRHc343sFikAa9is4c14uWlq29ad7q9GSdBiAMrarjN99Ie/iLn3d0YYvsAi4ZdvSjrM7kLTSrDRKMyfUasFMEMuhsOs95lMk4Jys8Rzfdwp2CyDGgyEcTT2mfWanYgYkT39Kzum2KZNi2+yeWvAYVvmTsKm2o/Gknr4+lP4rkfk00AYbsz6YJ6ZKqQkiDY0uh3fnz5CKlEnD1OkSkE8JTegDlA7NlzCqndVAv65LwpwhYPUAPUWnDp6rosQVRa0yFkskujuwSub2HsjZqHpr4FoWhdrYU/P7D+dxuASXUkecz1O5Fe2djLmc5qieO1x022w711WG4iPbR3rX2cKlbsmDU6M/B3qTEqLZ0FOI/KotCh+ObGDQLcwv20bqbS4RVzuYQnqSNkru3/Er5cy4QiMI5vAnpsZE3AonhbooO93ggZnrJ/RgTL8/TibM0A3/s41Sw4G2loDoXofmAkCoW7BoWhTKwoW1wdNVv1Jd1a74eH8fac0J2+joRxeIC1oSAVFaCdefzj2RGRCaSUnqe2PZTooWfKPKBySytl1JCPpZ62UGE2LFF/G+2ByIzmtatCKafs+Ia0vlG8KGdktjqkfS+hw7oCzIcEw0EoRqDwQV4ITv688JaLK1gqwFqlYQAk+uDKfDuvxd2eeUz89+dglpGUaGWesp6fIxLsucgZLPSJym5wEDrFcE61Tymi2Ub9QtnJzprrbQpg/q+jNUbrbleQnjrrGoFCC+GlLkr0iKcjKLcdDXovWIAFtO42mepG/ktX/XuCfMQNFYYLxoUpESkEGBEHaqM6V9p8oVw19GxXKaoDwSFelTQSNPFLPlrYTLkdIxgEhBqC/JST0c12ltJHvqWTdGdP4E56/d2dUiooL7utu+G/padNzni1Vxq8cd3rxH1mmPZ5/R7C6Kz95G7Q5+XIHIwjeimIbZTkLZuN5n+UpWowsImsw61M30K2KZYZMantr0/LmsSsPlvHJMNw9/QOTDNETNh7afaNcGnYnlGwnzJNPNw6D1z5h86OJP4BZ8sTxEaTcNb4UsbvU/F/RXFhLr07/Aqb94Yf6evGO5bePtf/T3PLnIleMmwa+0wuRaxHuwEXb3CF2Xeg0LrOt7hWmjf27rmUYFu34GH+lS5BD/Drwcn+9KqBqcifnZVyd6BjEdHcm8sz2Fzi8nHG7GppX5yjrvLeel4GVo2ua6Jjn5MGN2KJw809EuSss2tpd1aONvYVAgmCe5+Z2O8YXZ6CIBkrP9CnB7HangJ54cfewL/Ev+I3EjFhOpB43a9m8c+s6uj7h3Tx7mWieimhplCTdarbxRYarZMLtZHUdYdnLIf59OfXcXHm3SczTyOnbIFAHiVm6J+s3yFUc7kClrTYOwQu494BG+Kjd2yTCQZbHG4POTCkocjhSVIkxJS3dNBUKAIhfcBaGLnG0VozkXs7EXSxO7ktU+eosKSndGmVxIRXpwvKT3kTFD735tXKUy8IPzkWrPgitb37gHuKIPBsN+LBQKcQz4Yv8tQH319UObZBnjLAf4DInYB6d0uoNhzL9zGaDLS99ed5HxLnSZWVgF6GB3s7/3er3ozviZqujrjgKZiSbXTKSSdT1wwjcuZ/q3DyqLc1PFyvZDxVd70P6S4exMP6+Iltxv4BJExdUvv4SONeW+UACeJOPi601N9ljrcmFOEm/FckdEV95Ho5opQ3ZPfpuLP6M9K7tebinFMec8Z+nLBz6VsA1Q2VVEdpJdoxNl1jbeqQSDBVoBtOYFI/YqxDA6LqF6r7lSz2G+lhz0xEiMHdqQqgLF73UNV4npQwte93SrBhBlGDWgTc0y3aMrK6pZdvQirzqbqc0ar+jPqRctygsBk2I1wzH61mi1Z9dhpCYOmCk7NXatkr9I6jPH68xYmixyJF1OeOnWqmhzUmMzm5iG60CuANQKN672NjcGp7aDLWRlR+QlVLnsoEH3SE1UxMWWkgdE2VSJfZ1CjQPdNQuwdCcpwuj2A0GXgEYgWjZSMbUFm8kGkM2jzvta0N7IKikxknbfDNRpAu/A0ebgxklG1UomllkDoVaV/68LTn4kqw6/AT9GOhHv5UJYkxDuezr9wUBUMGI64rykRkLammCXWx8PA3BXdtl3RINCtwI0g4rG7kQxAsE8SoIDsPZvKJp3J0G1U5lOvMK2tgfaoA/+sySJS0iSLsbNPjCQtd6Snr77QhKOPQ0tztm/g4j2YH+JlHw60x4fQBICxnW1KOerguEj9n1VSw4K0Hp/Rg6f3UEKsGZU94+n5y5TYSyuB4toFj3eSpOPkUjQ9JCAYzTBCREPZJ+BzWTqJhQxcwdVJio2VVd4twqwwKX1btlagzpXOzX2586LQrrvcGCGre9vvVyBYk3/blJdkdRcOPSWmWT0E/t+bzDs98Nt2HZmctMvp4rV4uQCwNEKLDRnpi12P7M43dcc8IYtWmaUUxTIM8VZwArcDan1bysME/3h/HXxS0coGxMqb/EH1eycCbm3+NXj1J4JtXfN74OpT3JSOMVHUQqa/o5UqZsiNuP52FKTc5mrMDk3WK36oHSzX2ZqsguvUMbKrCRG7Ukyyl/dVTiLIvDBNxpexhLVBzWwTn0WIhoda0lNFQwIKxuYn2EYQN1i8UmdUkoF7JGOhYgXyfiAyxBsRg9NI9tFf1i6AVFJBnoGm9Ygzlof776rqGx9DoWRDmj80tQLdtOaARXBJJ5dmOy58+3O0V+1PGY2614i4ccpnU4MC6LUahieQ9FcrVmm/tX6Mp5+pwULzisNBTsaWVt4sNnwBTtqTZtNGmhRwohbuUaRv2XkmJfB7hUi+0gr0oNdt/FlrKPtORs0XdwIL+WXPWaGzvfBjPovhK1tvHVj4lzv5p0gZRxBRDBsN8eX4a0iIkQNznbcQmojs6x/d4zYQGPZLR4SXSjpP4yZdbNhNNLcTtBMurbZnr1hMePMPW9LLlqt7C/c8b/IhJsV8k4spCWyJm5UDLmiVoCRvb+kanxzJ4/q82X1aKxUVpZBfsrcEitp1ltg9BDZvPupBrIbh7ZeEX5WzJ82G4kHbJUetU1ZM1YSUVb1BxfMqh9MjCpkeny16pv5VehEhmxyd9Py0vLtYSiTttqgTjzG62S3OBcIVTdUUxv/o5qbep0cglISzjR6P1ySg2j8F8E2BltXe81Wf2NzyEuTX2Bis3PQ2UkdcyNV9233in+8ucAHvyzyNub1v09117Jg88Hpf7a1RAu4Je/Opl/odKclbkQwiAYGtKz7llDM0skcW09XTCbdcsrDWc9+d0hCd9InA8M49Xo7Jzp7G56qR+SOxFbnI70KInl11k5a2PkrlSeAI60/3N0NszXXR+rqCvWpMK9Xjc4a6qfaE+zoAKp2qEvC1DaAdqzQRDDgDYPrJdK2DS4TxBFJvO/srjGrK4P1GkHNLPP4qj/etCEUk/irJBF8GlSbatuwPMSWQd9XC66nkG6qrNb7+8jJnWSOakSTzZ/56poGaxUF5dD2lv87A9/dFOCPa0WxknhWkmMkvnQnH3jZBp9gbK3XChcYzi2CXNp+8+aW7DYMKND6go/gBgFTieAK0K9V77qs1r81YqJnRNFIRPBonUeE3nlKMX3aM2yU24yADpSTlvpU2q9ZV3e50BUGW05s7Hh1GknySxoGTMgpruA3ZK5DLraTmV5UgPuZZtsxTf4v7GLIczlXKl/aF9q7Aq713z/ofdBtNZg3yzzvmTz2z9Uzgw/1hx9o0vF6csrDzBF1/eGAcTZTZTsGWjDYA7DpMogP+N1yjbWRDQUEukQ++AvmFk1AjlA0Dkcw6tcxv26O74nYPX0XeFAxFQQNNN51loHkBo8ZzbfiPhnWAGRr4xS2MQp9x6OZ09KYQOmMvRdNgrtyQ2ckEzPRDZAM/wtCmfE7TNSL1FI+CJIXg1utOjR1lWyg2EgLAoASszkVyjs7tCycd9JFVGWqr5g4IeiqCqhSk6CoL4wmL0NJP5C/iBQxkSlmXvjWl9OWRl/nONDn2ZUsN8VjJoMERePW5ST/pZ2psWLqBaCpqRZzkNmbYwS2uMQKhHHS+l7medvGtZAVxtUuTtbgSljSsh6nZw8FQIK8WkFWIMjT87/BcqI5oMPGr480CLxiPlpfVh3M815q4WLWqKOgoacFrAZIrOO1Bql1NsmirtQncpquf6meqwxlBcy9Sd3nfXlhCYPCVMs4KdzsQmXWKGWHeM+u8EuMtBJcHqkOtpXzR1TWmlgdTcLC0rGjm9KA2gLtfMON3np2b5NyeN3rhxmu/9v/xH8T4sZjRDi0goclPSdr+meY5X9B1Bpu7kpxVKllVmc3T/mrT4CFPSEtygZ2JEyBNEwH1P6zBOXwVTW3PzaLNOoUQilzX1D8dLlXR0peRFARS50y5uv95BjpoFzcjzc+RAlmWRWsymxaRj5YCmz37dUodrchwVvAfGLbYTlhZ9JALj6eXGnkyzhm75+vpZT7+vZam+tCzXHp1+O03+qKeJwz/dfT81LiOY78zlBf2j82WNGnNDtSaj//Izu8BkEmT4gkj8jPM7Awpy6qBvfvigsPFJFzNdcAF4xDQx2uX6u3Yfb4u4HcGHySePhWVvrQf7YDqVDJroCNtcnXFNwieNj3c4E/C4iE30576Oz4cfjTexbTrqWboGnyfg9DW2UyfWA312keIL5WyGaw+73j+qjql/mZzkZN/O17JXhiyd20QbQvUCDKPEp7IPBmnML8TFH+JaxjbBrQYOc6aFrWqyf02zSMAhGhQfbLGtRjpvIBV8uwzUbX/64eC3777uxWNX0WTf8+JlbTj4nswmTJDAAqcu+Go1EG7z7J3HEdfSxReJHj85hMeLV6cf3Nb7/duLT2WgSEwwO1pFRFlkyWIN4OM8vb1NtPvVC2NxqxuLFnmbOnt9H8bXtTYK0WRGQ75uBHVcSeFrxfUTr0KDmO5VgAa6ZSzQXoy7IRXrqFFg6aXMIryQv+COaaJpdKOoEsDpI93VBFePFVdXSmTrrjH1jUD4l8lavksqUGkCEN2AE7iTo9eEYBznZjb/TKKZnMNcH8uEDComW+LQiSoAoSlo0gOAg/JCbh+4ZtfDe4/gPLMpGdkCBqIEGAkxe2kQAjZT4XichkIvHmlr9KyHo5J//I7qr8hLp1OkbFE0Y9zozf2UKnXB/8lahvOSF6DDAevK9B+hl3wX2o8XQwazifrrLEAuGjoaSWA7wwO/r8hFx//yqhA6Rj8T0ZAIjGh0/MsRzAGg8wLrFTMwMmP2dSzWUir9A2aCw1+Of7xDsA8DMVO/j9Y72C3Kq6NY8iipV3xAe2Syn3xzE+90aGigIEb2zlUv+sU0H1e3XqFDlgue1pYmMIP5PWxdaTBXPSKQnhaKQ5vEjZrAxUAiXhd4BwNkjaTJdTabfyE+7HPA+dpZEIC8qyYNpeE9w+P758BVYLGD9kzH3I2AwQkN/ck7/L3sL5DMfIbcE7K1LHt8JxfUGYy1BB4uZKmqdgLwDc00lQSeUOzhA5ApTBc+AQ+2SNOlve/gc6pGiEW1uJfzafPbtGTlbT4UOG5z0wdwQecS8SeOzyofMo6GyAXkg8avmVYS+Ss7dbLz9S3MiCWSVHWDATZtZzjURoHD/YojKOBbCAEjICGHj0T1uvvd0Q1didjzC+CqkcQrwy0YqFy3TkimrcP5dAMM4EY6QUo7bqPoZUKc9iKr6T2XC/qLrZQt/inhLeZOapU8ov0IujBu0ozke0gByJ7I1F9ZVdylYx5ddfKlk6GGkgKraJXhoK/Bj+WKTYg7ixHZKOEm8EXuDkdR7az4fcS+NaLhSqnjxXzTEKi5xMw3Xz5UAwbZKPAnJ1/Rp47cqaDzVY+OHltgqYG9V9KigJ5i0n922g6VcR4bbiBK14sKBVVgv0P/FLmyJ26VVEVBA4ecbSx66nVkV7nSgSHWPnVyvw0SaRJekt8v+fvmPACO35NY/g/P0E7j05+yijxkTooLgPGEtUp/roDrSr6k9KFFrUmO92pRvqh+klVDbfiU9ttHY4nVqE52sifCrAYDPRTqvsMLTmAgGnWmuRMqnDF4D5+beaP8CTeQcbD9fYFZ5lz49J0lfBuwnruB/JBBVMsj+kGzqkWgBC6SVTg/MM65a83YhL/Yao7JvfIplGNVkKB+iveDIiR9kIDNjWFSMf/FcU/spym9QAI+fmDIfxJ+IgwX8SlY/4yIhHaAmV9Rvl/Df52mc51iav0dsxGmmOj36+PApFXXE6Wfvsgm1HbeybyxpodEGkYqyLhY+11Ch9LB5L00oTv16yfx6+t/hY3jn9cqpXBLArMjCj2rnBZvdI+Ny8mlduVlx2/Z/eaAxbz4JujeWida38pzVAhLkVVwWGYZh5rJLMCj+bcnMMr2sNbsAH3wn7mL0MmWE4chs6/GJ+mfpMJshKQNlRmU64SK7anlwysagjpDPSMnjzHSHqtDu2FxLqjQ40pr/kfBrk+7XOLjy3liGnJlUN33Q/FHpI6cA5LOi9SMmIkU3OpUI6qL003tnpM2I5JL9bnCkO9vQDiqQ5GwW7+3f19UXd+3uH+qnynb+tcwAJ9t98VCluoKAjaWY2uQjJULHvu3py6tWJ7k6JjtFM5VSralRW9/R/TSZfVMeRm83zZ5tmqPKPQnIlzvvbRXC/5vC3tRlhrfJb8s+Vi+osJpA8oNHedp1/BCm+FuX13eGO3hK+n7x5ssQwQ9wgnn3CRoTDycvH4B50afYhnW9nZakN3n9ig+/gIXxYBGbLTRRE4agnjK6MxndC6ZgN4ypxLq8Uu9uukzmdTbRznECd0xD+66eH0px+Zymv0PmyjWKNCce6q2FaFT7m2jjUtHAL49mFCOo4iHlXLFsEtOBI6WGLifRYw7pWBk3JBqTkNq3c89jNvhrPAT9Sn5HGwNMzmvHb+QbUFHXWaFvEt2VkSXW7Or56p/8j97knlVZbJQ51YBS8BlNouusCgZubPlBE5gAW2v9HM+kmzaNaCZN0Rg8268GkvbUB+gWheVGgtsbAihXVE6AKl/sZzYY6x9gxPAbugmXtjcYH2643TeNtIafyo477mXLUmG6JW3DW6LRklDs1nmATrLGjtRho5A5H/l47Rdse8nBI0n8x+DhtaevxcNiSyTF2512f61Jc88oSa8Xz5ftEKJxLKAERs6qDUanvwX/RVDVca/S3qiQ1N3HRJ23vp7cM9lKCgVquNr5AXNXfO4gQs2BjTRS/sODEY2m9r4h9Gs4Mmn07Ovi6o/xinfwrBoBnhBttqyktw4z9dEMub/3Idte9u5i3Qc1OFWzCNIy/dJo1LS1W6njg3x6navFoi85RrdpWJNGBPniuj8GRDozjZcYGIT9ZjJR9DVD5OoH7XPv5tl58sF5mtIW5Hn/C2vnoMXqOQupktly7rceOnFzKMlF4n1IU5mDssFE0vooDj1AVcEaRsbYDOjY/nPIVcf8+gP87UGxMf3jyPsGNEIIbh+w0x580YxMeXX8rKUnBu+uGpk3eWt/EBesJ4BV1yi1aTD7+aiseym52s2A2u7Aa2PPu27JeNhtzJ3c4cbYgf6H86ceE/EWp1Ebiv77LXa1SCtfsFaDWoqcli0sSSsvnUcYkvQ/QhhmANxwkWtIsn0N1t7BFtXQLiQv3HmPgIw8/ts9JruCzrs9idkNlmikmmWD6rDh9Kw67CXpC2VMv9PgtyOXP+u1MzFCcv62Dztt/L8B6BVImub4R1nmXKGwott0THdyMUfwC5lPxyxNPoaSRrA2Pt/tZgkfic+OxYlKjxdcxeh2gmC3N+rOWyLr7Y5GCjtHku72M+Wezn4QuJ2or6sqJdUD8Ho70QAh/4B1wnqj9/W2owWnyweEDHH93zZwHhJky1NJVqB3b58+X64Dit+gOoCMtlKr5vP/bnCSN9O9HXDZxs4mvjnT+JsOsiT2FLKmp/B79063cgeC2huaBLRA9aEqftxeQHovZ2P4m7nH1Tgo0AWF/ufMt81d7ByM1UITal92f7zMW5+d9BjGjindShd3jvv/tFnu79a9/bALqsJRClpikETUYg6ZpcTF6lAn0uivZeMyUCwRQeE1mzKNllcth3Cfe8fIpoaN/NZ2t/bb53d3SZwdDpv6gwJL+9eUqMV65iF5X37vTk/Qgcnn72Dld1ASWD4vhH+iQJkOKwThUuLC7+5h2alM7fnCqUJK0CbRDqXaKlEYSXkiIkwIb5GyAVpRqNJ2W8qruimg7IJTDOKcr7ryjg6Jl2L9b/3br+9ezBkj/ZfO3I6knXVW0NOs3ftVPjWXz3T2YCUly1BKgZ7W62ana9o2DUw80AZSbBecKxUxqMySDNrgFEj+VWO4Y3z0nKmJRPddmA0UYlInDTbNOTfXXCUdXhfSLsj9BAxi6QIQrVxknIDtKlScrK5JRkvQwe0axzgDu3pcwGLsdp/GelfUcWxCuIemrO9ARxbzL6yQj+Hp/+f9gQQtClmTj6O/431/Ihu7cC1wzQH8B20OHV7kR2xiVCTJw1BPEllJG0RfXwhpu4rPBx75O8ybRtVAcdmzNx/8UH4LvQGudzph4fn0gEGUD/jZQ+AZAuFU6NzDk9+0+cPeghWFOiqYY6MbATkgIGV0ZEcNMLE4wWIHENCufRmaFO2DE2gogwBNtxk/QVlOfU1QtWV9qvflMOjY34WYGvjE+qxHc+00kF+9ojIPSr5tG1Fxd2CfynbSAzv8GxlQkHeUKV82KclSZbwIaRqvKmK2eRYqjBAas2BQ4BUrI2Qu+OVfY4ERqjqkGYNV60vrIlBkMKV0qNg8ynTsEMdiICPwkXYXnWaDrJDpXKMdb5L4W/n9ytd6oGYUoQhg3SFNZpv9masaYijnyomKabkK6+zmoQXtoLK5FieG5iVoF9wxnYy/NuuAQaHdjGTZn+xg6amR+4JU2lH6kWSTwSn9zEnHZi+mEj2iie5Eegl02FgrZ3e6h2u7aWazttLuF3MGHvF3ZYZf2vB12ZWeJrJfSlHNuB1EFiQ1X8+Q685+vwyR/NdvR0En38vHQS0c1NNKr+ZMbpv+jhgv6f2bnUE5zwQbrWKeatbcZbadi8e05Q0Jx06TqcVTsIy5a/LnEBZaynkG8Yow9XuOOCIrD4zWO1VicTCSp/RRLH1j7SbAe803lgzjXoR+qOK4mRe8tdZMXmH3G94EMtnw0PTtmUeWmdop7XB8edxwMV1kUR2GU63r3CmkCpezeI1xGTbhsUoWKCCj5ADxBBqQEVRNZk1bF/iSlohhVZOLrDFeMtMkGHh0Pg9YU6HbS1o/yNix8zFa+a9pZJtyKwc44/utZEJI0mMl2XcQVbYfUkjy1bIutpVGnswYECHt3tJcZOqidxa6Lb6/Zo8x1Cjr/D653ZgJHVuI87RtiKinPutsKTIf5CvhHagPZPFKRJFLcI28GIO8H40Tpbshzb000YXsrx/lZ85NxCu181ldRflrlfgmXqszQ+oK6GzfR7X8fEVivQiGr3iCjI5OuPTHqDQNzcVgp+FD7Fm7DTrkrvbACt5PncjS2cfitIFpa0GBQNxk3a5GCDBYAS8aWIJc67tiyNOjgoxXkrsXxJhsyHCyL8Ys5GiYwxD9TBznlcCRz9bC8Q+Z4benbh6roHMcVwCWyqFa3x2UO4PlObMuur1bw1L1056EFpS99zz2jk+gyMFDQAwHT+rOUgij6CvzaMZnht1R1Ogt7d5KFjbg5q09Irsqf2qHdU4uksOWBtiUS4o9ztx9qm9lTr80Ustmjq5DaBoxslY8g4kTGUajL//hJMSveaNzJseixQUFbpNhxAjObWF+0JNOkg71cEBHXH4ROcujj7S835E9IWG1CMk1FrhB1DQINX1R6qElByN2jvJ8VyYR/M5+cqi9Zq4K3Suy26ze5APkdepZvwdU+yYQ3h2DZysh0PNu9Q4jYQvJ7DW0hmzXKGs8iNp7wJOc4kE4sFa7J2eM4P9IBNbON6eN+ZxZnKr6lRV3wXCbHzJjSm8JrCgw5vVWCb3jWWeMgB3MUbVbyYvJEUCx9fj/lDqR4pmgF1vw1lkQa1rVbBGU4bn6F7rYu/6TvM+o1ygmfDXnKQZBQFgKGTu2UOa+OYqTxoMxlakCZz3zw7/YotOjouBXqw4ksGtIKHA47I8ShilvlG4prbvOfM+LGnK9BMDTFe4G8FQmAN0Ey29GgM7bhtWvbgp9hPbT9D0VJBzCaEBXuvMURO7kXVXQrt5/A6dTg8YpTJeB3AOJkrEU6KbEY51/KnOVuIgWeqC9HBd7o2zxaVDoqnsXBEU/tpudkr1VMjh8WX8ERyVMHmRGV3zSPPvt3/lGaO6j5+ZsWMRMoKd9d4xA5vOUKoS6syPvhcNW7KZyGSuhvroZo5R88k1/e/zwHbJ5HzjDlKrlwyHw7sodkSKaPxkLTSOM+BjiOIyP1mfs7nl9nEJztUFIVUMG2FD1wTjI3Py0l0dCtoeL6ItplfsI5jJy9Zk9taKmGeHYziYszSblsEpfUZh8OnS9f2nVMfbsnK5YPX9CkclHZ4s5xMKtXneIaloXEUBupjarNAKMwZyrT09OPy4+JIVViERLEeoA0uQIsntx2RoPN8Or5w4VJ5FRkY87cyWNZjs8TFZfXDlzc8sSx85bpH6pbkaJzUWYPz0dJZf4u3eq/rfJlpohm1Xa0y2VhwGSd5WEbPZFJuxo20LmvC2Tom/a6h/MHg9V8zhvthWhMEKVVhlCC+J2F9FmZKQ9hLii4z8R003eeTHb/wFA27QrF+lEelBmYnI+PQ//+x6slckMe3grGGY3wlQvRTGHxA1G9A0IAsVJB724YiokSnLUOXVpBtQnnMe9aGf+7MwiYmd/WoNquYRZPa1i6UfD0t+jxZxLFFOc9L8zd+R8aXzqu3Jbq5UEaN5qYyk3RKK8rEVxfumIcAhk+kgYUIqzN2sgp27xNV+sAEjzQrOVXULOb888kpavTCSqsac5PEnRHq/G51U6ZVnnHRyh5+w/OlVnC9fhj5bD7SF2ZP1ZpHxPX6D8/p/6DvNHkar/yrPvlb/OU85qSdGjW+fyO0kc0PThWUM/fHjfGfC6tICyYovtGe6UX08MmPE9AvNa81bMSISdvldYL8+LdDSaJ6dtibNRUubdLMAPMp4Veta2HVJtFOV2J9Um9z6IAXHpYu8XZjc+6LhfWFoYG8Yie+iL5O+vfX4CaUJutYZrxvd4TyW6dViJKIpXXvbBUG4kJjCzHBi8jl8qzTQa6otp3cUfCilZUum7pQD8DZp69DO5dxiVHu9csX3ETGeo6eny/oKWVNJfC/anuWqm+OcFqKYkrE3AJ2K5MrDMo3pVf4WQNElcoZnJFKDlfZUjLIv+/C9d+RdQMmGKmAD+jv/Ru+kEsX7tsJXtola3yhOlcbwVaWzfFtIpvyRg3pD7T25UDpU60eRA4+DjZjcLl1fJ+FartmSik17Oxjj0TViLcEJADPuIEZMF1nICseYyDFWwI/MfOhSG3rjW8IYNx0FlTa3YP6UQvwaRfRSz6oqtiw8IPCrD+g6/k0+aAhb2GF2hucAYIYTFYumW2B4Sa/jWV4ihJDAjuG3Wdb3gRc5HsNIYaPsYalWVyiYpA5nd2hbQinuiEEPWhwbm1NeWbZ+7cUc7fvPvs0aNpZr91sAQMhkfsktql7JVtNMlK0tdSCmJa7Wcrb/bJEyC5HBgK4nuK3H1Stb5PVlAg61uxwOI0rFCbg3KRxmatNNiOfWwNvK/eG1NsWygMMEZdMGwNToUQO4mkqaMscJ18fBYKh4H5xk01C5xIJhqg6BojXXgjRmgbrRPES6PhMDicLbLWNIEouUbb22Aj77Au0N1gdiosXHRpDTAia7JLA8F/HUkMhPOD5s1STSi+VnL3egXeglH34m6OmZ2VD0fINarWw4BHKkWtTmerSEmLWJnrCoeDQSHOFyh0tbIYNBpwX7EgX6fYFfjtibfRLpEe6lULghfgMKcTXmKAZt8i0hm1BjICDkwoiPryYwEORKSSnu8H5pewdIo8slgHQLqHYpfaKWC70uUIZ2f6Z0azZ7Lny184IAEu73RdGehPNqmIaocjskf1BnySa0we3iowXdffdSWsvz8cVrDT0uLmMO6gnhWT/vI7XA3hcNUnofmPvyoet18Ix3//Tfabo6Ysrxkj6LiKkIAjEIylh/2CwIkcIEl+C9dWxEuif3qc8VHadCC+sPeIOPddXC36lyBIHmFaJJ2YcFhRX18iP9+jlExZbMv4U7OOmumzUeXBrssPkvujCVqkvqXYHpAkP7ee8GPC8qsKbNOb38N//OYMAo96vgxmLjhklW71HpdJw9YntfouwqYo/BFR+CglbmQIn+2XNYLfk00yXlMs64exEPFFMq+uHd5zE1qAhw41B4SpMN8jvB4E/PAfP/hYPreWvk1l/fotEj75ajSpRABquBCMevRb1fi8DvzYqpgJ+bgT2vrlLejn6wfSClJRnIbiQLglW52Wq0dsXK/MRIvl3uz8qi+PQoNDlQk/7SopxLeB3rOQInCrNHnh0nOYYMqyJ9Oa10mOVEgzjfBAU2itHlLRhdEFlEDVzb1IC61D3PnLTrdAowrPAxUrhfj9OB+m3PuxA7aKtshXUKCM21OiGjqJxwZ/mmd6dhdbc0qewFhPoKnTZJOrMIY9XVZOu7S6uLoopdAjlcPyP7oGelKuYatdLFaG9LuMGFsxRxwyR8C4rwHCchP+Czd+KuK6iRBdpzyTXwrqQGQzLW1qocHsPNcQ8RqY+qQMr8tMkNLGTjsvTQFp74bf4fI/BfJhTs3uH0BD/aJSlOWaDdhEdIoOlZMvUeCAavXKyM6V4tQ4dYLN5qsGQWO9k0s+wrgKIzolzDwXfc9raeFxRt2rzoEoBiot/IP0A9PMMWXjpcvHnWUbA+qGKJXmgv5/2ek7B8Kg/ecWXJMzes/dXHUcsQ1Bcb61l0Hd3dR5K4nNX624AL0jUh8Hqg6h5Sg0GL24Ur4JTjB6MVy+PhI4CY471x7ICMZB6lljv3VJyfOAlJBgYTzlWUfPgQHAQlFCFHa0Rb98jetfFn9YuA4wGL6xyUpU2kCwjjufdl2VXtYC5UKZJcUcxCFc938o3gvHt9V0GdrD1V7nnhOX68byE8LuAsZxvHlqcKld8r6832SoHW9ogB3bI6/5a7uxHrjtkGVemFodlif2FoflqdV5YeLiCQAaUNnAzQPIFNsG5jT2A4xQOHhCvzPs9xsTaGD4u0MQAieYQWZj5N/hPftt2TK3QKMWFMggLQLpuFwQMNspkKZ6dhYoIpV9VVPzAcyJOqt7xfvKB5Eb84DkBJ7X6c1im03cnO6XvmKHV5GOyZhBIFFDV9M1AsJA1b9ygVYwM2HsHgryEZCo16JCJCGyKbsj2CIOqWIdMYQpg/cKNAWaOri4FTFNIYkkpEqlGxDc759g+v3K89RwJPJGTqz1ZReHU4KmwxMU2ChRkZh7RXWhCF8UiSW377Gwpd6XRGoaUB+t6YoCmezeojkjF44K423zg45WzjmWlft980V5jNTGMxXHzg8zy+F+XDNnV1npd/SjyrjU8uDTNWdM43MXRhenXv4fvX00aMLQb615YCdyesnvT0lPbm92+jPmQmHwSQe5PYaFKV3EyKLAfobkAtee+0DoicGR7fj0z0ckcKk0ueEib9CQ/UVj/Zpfeh2C4DpmgEl670WpzdEXSg+WecghIQBL8khS0mKaxbLYrrKCvXI4gGRgnJoylJQ05GDkqLxwOJw/UXRzP1IeqxyyimEThN8Ks8Jn2jSGtLjFzj7IpXJlQvCwZtGbV3nF/1uuyqkzOQR8//ZiQ/HQyQiCM/+a2PCbyIUMJjDZEwiO5+bxqzuY+39EodfDvk93CEIQnFkoxVv7Nv5GFPXkly9oXFvSafHbVtw1h3WDn7u1w1D0OQeEp7nXTbJ9Z+rY2gWAhsyrr881Zo9nN4IfE/CwHrTGPadNvi4wOHFDCn/Um4t45t1adL5/77ZvWe9lv/5Wt98VevC2te7ZbYrxQA2UDzLdlJX4wdZokLpoIqlK3qzyUZ1GMx11TfpF2kbjIu2mPwjp6UwOdU9mfGJqtTjTFtmQnIodTdZaYbp9+aBZjXxp8q709++EF2ev1Pdk1NRkeMJW+oNiReWi/4Gvfh0jQ8cXshGiS/MO4ZAYU+C8lECK1JT3D1lmWgZsK8sy6VTxcroYYDr/m1RFlmcOkecgaXoaxBBE3w4ig1Zc2hOY0B4eMmGMxBMml4uSBwa54PGsY1wxKfJZERBNUozkExw2DWIJom8GkUHQJQsYC9orBBQ4PX1n3Qftc86dGXS/HsH9/5i7TBxGJRmhrDRuch/731ki/2XREog61+ZdZeatVixhglJWeSThx84BYwtftQ75SPz/h/9BAuYDjyKOdzkKGxAx2SGpJYSeYqi8tHJuS0aIdIP/GMKN2+YjZwcEtYz5u1fyrCFJeYFdtR2ojq7aQSudJiQZVWRzYAkFwYz8dIOG30fxs7HKY7xtLu45DYiN6wKD/8Luv+kcrtVvrEA+VvK1t6T09QVi3V1rbgWGj/7+h+zLtaH41QEe7Wu+h74rdvezUCvD/ZidDW1vxnXAvb90CuLzH8uCkavTYXP7Wk7tlYwKDMPUEXgNGfXuiIuKCyMGI2g+pgCQOAfxiE44MZbD0hcdOmAm69LWwr3nMRYyqaCYmAUHj6zbubhUn+HZSTpVP1dm7N540TOAav3sQB5kclL6J4LZQxjBKHfkPmvZ0kvw+u3A8P3vwPepdsicFpf9lAtk32Rvl4zbVVj64L4kmMpg0wLZWufW+5KyG9le6xW82BqzUDx7lFk3iMt+9nFkU/gUQFwO6dK0kzMeWP50qmpLVVyobgpplM6HD7lYtDkcbSJl9jBCKQ+gylQ7h0PUVgyG8M5CnlwYZKcyFwjEKJLv3GT3eNyYwOf2XTA5SSwqKc6afCITROKQIPyYGoR+OMnilYjEJGAcF/ImaM5zwn1LJQVi+dO8+P7p3dfmqrF//X0Q6f/p9oCFtzra2yYDyZr8CXBad+KE+Zu7d5zaEYdVks8YS1cXcdqpBUywmFUV6cOxVfBfKM69DGZx6mKckFIFyeCjMdED2Q7/akIFiMSrCqMczkF5R50ghDsEeIIjJmeQp13q9IcNwhA1PfQTPO1GinkCGmczflU/Vf+Hs7LICEL3gJq69GaNdH9ru5VeIJ+b2qpJWt/fS7Fzz63amLCMYtuyIB+5wgf/42PABqbMidiY22f/dlaMQ+AUkvvknqaQOttF0cO0vqeG13Vq6G0pB5Zy2HzN741gzrMRCUR5I4Md+DFPu4KmgQ/CUOsizYX9iPwK3jZlryHAfPI4dREL4silTn84e9gmSfhO5aPBqfqLTNl4o8D/8H0VUAKDoE31/ZeIMFZR0XpaaDV/k128Ot3xMKzH094coe2J2Getjzp9RD+8oTAKJXCrIZRrq9MtQsEgWKtCFsrZPbVyyGZ4k7WbXQbp1kjMLmzRfAQjyJl/y75xt7Zwq7JsOjfvglk5gB3eKXH4JGZnpdIogO3dTKJQyykUdhcGqgf+MEBl19fDwHEaiSW5NOTXCfX0kmlgZ1XQ/V6Xulg28kRCZdYazsiOFmxsgHXaNVoOXlWJKomEVQNskGBMbQYhcVQVh5yipO5Qc+NVwYFWYJs1aQtT0V8Y+8UhjcePVvISGs5WHbMsN4Ttn62Jd2iZGBxQZqJV8Wh2Ye3RRSgezTamf/8n7JMrRuLFseqyzLtQu+RSu+t57aoOBKy8HzkGHTvWI5QElgTw73GfqTuGQFXXjvrpp72f9Zy5ZhaMZeVilo/SG/DvyiPZh6E2VjmRKCZ46fjRCZDEh/UWscuZ/jm1WtZHWgNhpEKKz0pbAuu+/AA8GsFDILgIBIJHgCsrGr6BFP9QROUr+EC//YKvYj+ZEeV2pmEnF/8aCNMp4RFz6pyc18pthcxSCm6lOZZ9X2mIh0Dw3MpoxhAQEpB4w8D/6FiG/gluSUjcoeQnfl07Y45NAykBzyQ6pKV4xNrF0fo+P7xd/g0jM2+0D43hd2ERMbfVxsK6bxDxPmilHBeLINdW7uj+YUMmZWEfauVh0w220VDSxVroUKwtn+UyIqB2iN1VWYiQWt//h9qjlEbm5p/v2b4KYh/FScQlIPN8CDF75VjqrJTWs9TAoQqVIME+OU5B7bfnJCZ2SMBER1DgDuSldb8xz51JrFkwLa9ctGPQDfXjFDfES1EIfwsabUOgnq3vwC1DSJrost5Vugexruq+DC3mSHjQolX77OUwd+Hho/oJwfwSmyAKk0pASOhFqQOF8uh8J/bQyLwLCyS8NofBamrhGCF6Ut9rDv9JTbM2DP9UrLghnFdeCbt2RQJgOOX3t3JILN6MRpvwBsVfHx78AgEpw8OjUi4vSXOo9w/5srk/1Ouq0nOy2LamhSwvzCfhwe681AnyPZzxFxUDqm4qgTp/L46c7XXl+6S9uxa62lGT3YuohGBBmSNWgo0hImSMmqMdbzLK750EpjDw8wC/7fOfpPc2/+WoP4N6JsMXzOcI0frewyqHdpkziyNs5Sw2kXxwGYRWwF4I+a6TlqpZTcPJTlqCCRihX+wthnhin7GfBGjoD8OWY3BcQPcy24cpjiU2v+624f7fEXjkHg+959nOi2l7RUpCiZc8kfjDoZwVxYzLBrG7Z+HXLuXE9nETrepQoGH+/1aX8cQ87DM3h1ji4uc+3VuawXO849NNtB2gjhi8r7uUvMVxFDtIUl6UkXlELO+bSdh44p9amvHPCvf4zE9o+W2f9I4K3eo7Ih/rt3w86FrIL8SsEubhKK1amqMq9AcVE89lBI0SxK7ULL2+mLRp1nfXPZIJPkmfva7/HTLcoGjNdXE+RRUXAwe1lhjwP9fFVRPZykmEyg/ul+1Tkm6k3JdVUoyMACuIfQxBR51UN686+Nf4kcXZXTTqhKR7TQvl/bOTgzl7GcqaBDTfUihPReRdsmB4S/Lg68ahSi+/BKyep4y/9VDCjIwwui6cgpOAJQwMPuZ9tEwDcMPWLd55xp+PuY/IijzoliKZ4LL8+YiFVehmPto/cfGUvEYoM97Tk+nK6Vt7dKEq2s7TsIRThkQVrvYfMFaBkJ+qGUoxfswSe6rc8meUyw4O6w4iKBX9fFLgvRD73eQRfve8YuwtGXlDblHf3J61mI+qJhmp9d8dL/eX7ck8eFA+F0sQyr3ZOtv3F5S4aEqwK3v6vBJoK1/BkM/8z6cgrnMCjMUZEjzSJsQ3bpeyq/8h582AO4u208D0PSDFUvTkKRnHKXXcfeZimekjD+uukysRpZx+1+H8oevY/Hr+gg5akgH3+uuF1wfv6cRvK6UxM48cUUCX+WYC5eyK4OslSQ9ynXO5QqV/5wQY46N9L58tf9UxZgYoCshMANN85ILinFy3MalH+31E4EEdcwHCN277mO+x/ShMVLy4e5UZpvv8ZQS/OvxGLOTwEflybEqmb1h1x49XdLCcMsed5y6qQOgEsvIFOpUgH46SCPJVunwBEwUh8yQ5pdgOoaYkOzMWCoUC/g/eXFACZiH06qeBAZWYUouxvcCdLnv+ngeBN8oxOK2MD+78L2x0fCmYERKan6dvuGzZeK7lYwizKaR8IyuLim2+Orauk7TbZz0kZAEvjM+VWJ2iVmiAxdle4RUwASOd+B7cRJe87IChqtoV9HC5/X6mpxvH1fmkCVpx6htay+Fwz6mzWlVAOx/S+vroD9xyhwInMPyJoqOLyPMSzyUhBLNRhCHJ3UGgsqVH/lxS26z4SVj/gElVVEU7HAFWO78fHLgSVhJ1BXIY3sKqrWUlW9xjAu2UZQ+Arx1jVwPzTwSNkGXbHYBxh4DUv6SB3iyxWiV9Nrywa5olWqBxtfrr7S09NukEhI1wWsJGMryaU/AyPvNSzehSWQ63BetVaqQsdM0usLdTs9Yjskl2jJUiD35ajBx90iPK7UEcuhNY/z0ouGoqwqt7ewT65QLmkAQJWQD7HJgEsG4zqh2N7shn5xWEfIitoW1T7QAK697nPb9C8hubYMOseDB40HcYG+m3ZlNAbtQ/0mptB6i3Lq7TTvoQXcm4r/JWyA9M3AP8Ho7FiZXY40AdOuouF+s3Ocy3MQYbp8QIX6XLSHHHwhCev9nSb9Dq38XalnkEawH3HHhOp8+ROK2x7T4j6aJmu7M59sq+RDq2baGQB42B8oGYqbEFynd0uq6IP2sJsuuxKOF5zqxoDPy6OlrBRwxVxSaMocGaDao7TSH2MFwIiUzZgJRlnHu4XwH4+rGvrKzjeSjT7H0XjJBIWro19YqoXgkpJBMoPpk0ZatKb+600A2idF44zmOS2n/ZeTdWmYF1FvbMuTMA7IX2XBNShAfKBTC4MGBCnaTETyATrXgERMb83wQS0oPdeLiI/ja5HRP/zwVKECVJLPiT4pmF/5GuI+k25DeSpScXDBmQrW023IEgbz1o9A4MxmITSFP3GEbfhEJGWt+WvAlGZPpEUxvm4tUAFu8UV0njxtxOxfagcM3W4pJsnpFjRyHpo8owOC3bTCuBdiRPjZ5jctZJzr0N06DYpTgd4S8gIKgPxH7WbrI7nvZq/6tdXNQNuhvP+D7vdqttWgWQyQHy5nlhp3A8+hSWH7qBAkKPLshzVyozgRtoPnBlPKvfZkgaAN2UKOPYn4C2wVRYO7vvyIB58JGT3yQugCIC2X6lWHRsVhUOAzlv7enkoMBeeuspiXuj6gEnc/XQeDgc38wKaLPYPYe0SN7etCuUfCWphbmK1Nd6B6qxakfie7v4vReQMDjFeadEdHlaGQln/VS2MtjDVSuGxUpcQQ/M4ScXY1SJkilExD5OvmWUeigwX0FqjcHnFY5fF28PU4ndvZZXrcNdGvkXJglIFCn4qOj03NcIk5Eezlyz9DULX/L8OfP5Q0Jc4cvrq7b1fOipJeu7vvd26p3jd5a2btN8i9J4CDhjzfWbWfYKU/w3mpU95UecMfPnHCXaoyNZpltehFNmHNbnBqj5ih+P4Nv5JMbYoiQcTmhssoe9UZERMFdBXx0JAXyzR5Q3nShRQAQSrHAr7irh/0BsUOr13WU2dKsidurOTWUq/XU/DYayY0L7Xqb6eluudzyA7tzcmPyfgPbbtEmk4Hfwzu2uEf7lonbr/9WQ9p84HJgV43Xi/1XKAS//v7mp7yMhdNNSheL4+mwj4H/53v2tCiG7juy5viM+u2ty2oLOZSleNCSukaGivPDksY6V78nBFbPP3b4pdYTaewUT9f7O/XODUz2BkmRjch3aR5UwvAdvrkFNaM/55eqte8WPNB1Fny5grgb+Tz4uDeG1BeFqBfhbNw47VpQlLwbFQxX+XoxYucI1Gl4BDILkjKnD8YmL7Q1ggw44tQCE1kDMQtpVLxSq1eBbCMDtCa02+zMr70kY7sgYPgnw2NqhakGBPvHYUPIvJRKwQdgo7B+MiBPVzBri1mM/qYMcFT8+kNiFqV6lj8stzWsiYoWWwMFM9Im2mzfRcs1vbWEOsSmu/f9AXF1NkFlYmL+ebZoV7cY+qJg+Jy2n4SNQzq6QuooOuLcrM9IcnbcpypxsdtXhAvz+NFhmRVo4DaVe5HNm/huiot1Pxkm51H4j1dcH09Jg4WZHGSR//h+EDZP5eukL8jeyTbKi3PibCRnBG6Td0RFI+xD92F2k/DoyDbFXxgmwJD9FwiYum9aSdyZtnd3UzF5LT7N80dS4xkqtOerf1MQWpaZlH2tsErGB7YVEHccJ35yWmkIaJZTRgja0mtPS7+Ae7m4PhKGBryvns5b6VVZy7bE/XB8HADhjmnZDVFCHxXqb2WwEwZnM2tEgQpnArdG4BbIVAgETIpd8yeeSLonI6M1aajWOy0D5Yv0C4koKUcd5+107EzKGnA4AL1L9RcX2SxpJTB1yzMLHp4UhitHudSVxiL+xFokkPxzGZ/ggRJFQyxM0MQHK6ul+aHRyQLjuoaASB/IDfv8nkvyvVaLkuNsimWMR8D/gkRg5OlLuczdc5Wu+JAgZhPhNVHdaGAZOfbrocibxyetTusyMhujhljSOrMj2s4TNV6ny0USWuuY8fe3k891Yi+kn6oi+OGh2EF+Es8AR/iU5Rf7ZCN+iO++gJggF/GBsDOqcP0cif/MCpKKhuiJRD9P0EG6cAkKRLcC7h4YOfF/RynwODSCdEKlBnsNKxPMGEulOH7mwhrFI4GZS0cQXFSI++FSsPKJsW5ICvmT5Pgq/QNIOFuLNiMXjYxm0F+ZZ9CYkFoGU5oWrNAK3ZLb1pW/CgLVKKgHxvF1oMK9g+Q/yzTz/QbbuoM1Wt2YeIp1AU5+06QNsTmGrXI562w+NEOf8SC4ory01xAKDyq/JF+nYZeUPdP9PoexIOylGWZIFPJstk9kzUhpT1noZ4NgN6UPTZrLb0xq793WlxbTO0hLGTG5B6GqMzDEvck+9YVjoXCUrq10LpRQcvJRUDMLOC8dsFLvbHxX9yT7YELmxvYDjNLxVm6VgdjruxLQ5Lia9X2B126q5+H5roLPNDkcld5ZRhRrv9XDAbsmCZRqZmmoDgEZnZMiKAYTZ0CYmyLAemHcKwGGmtJT7ZWvXdKWBhY0HFvkp/sGShKcAuFGwILKNM39lR6lbPJk/v3bmCsQEUWxUr+DIid3/05enAHiuyC0aXlSblOW5vJCUdHAbAFo1L5CpCAzSL1dXdcvy8mTdqtuu1l2iuFum4wN5dane7XCn1zRHMAxDXoSISwbejK5P9gj1xnwjs/sn8D9GRifkGw6WSxd62HQV5PVK1Poeqdst7V1m1WvB3oJ6w1xZfqG43+eq0C222ZJXr+qK7g9KTuwN6o5qUFpzhY0qgOQYlvmiPzF2yNTMniKZYwDQXk3O05tzRftg7D4i3n/1aqWM27v/RiUVK99ZvtQR5Ro0rY8guil8dHEgkRYBrzca9dOYNOVJYLZJbcu0zhmMxnZf91MyDhVTq8UVHrb4dr/xijUiJELfuo11xtDYax+ccFErKeDelP3OwATpbBCJHMi6xtllvFnt8kBW1vhnRUXs7J94/YuDO/6l2P/hgAsOQWwAM6AKJAoii+sk0Ok2K8Au6ZTkH51d4j0OT6CJACQVnKCxXNHDHufcurW2KrNxnSxiZCkwq6TsXVM719fXZ4ZhT15tbL56MzEORhOWNrXIeG/clZb+DFQyEXfWm+gUEgs9inn5R1zX+uMIWngYnfTlAb7Nu22msYWmkEbr1u9+Cg190cWY7aM0mxbFPvjD0XXs+Gd6WBjt1fE1fsi7g6SobLpCEvtthE10ShrxJYZy1ZJNAcdZTrsKdwTZBNl5+4qJT4m/E1XvFmmTZ8CRNRWVXIo/m6EGWnZTUza0PBoIw0wDbznIvtzuBmhtRwTBLwPwsxb4ukDAIw6EjC9+JAD1oFOWt/PcACq4Xy5IEWCA6k7xNYJ1NuWWuZb3NeB4GOgdDjLWoDLgIi/R7wyLuJXt7i9nwom/yc/LV8EGA6M6DXWY92Qn6d3QlY9C2ulQDytWFhQomYtZTF+271cvT8Ut7TLf0DEOayc1AQa+pyqL+Zr01K13vRJcEhsRfentOSIdcPTdkVk53o1AZ4FUhLzOCr8djPSI0LDvV54Y1gNZ+XeNKMum7Q0LTBhoxiktLq1l/EHV2GLWxqRPZoXX8Ull0D9ZYOHh3NBb8wUgRu29tSbVcWUUpGeR3mubgf1R5aBMPhPBr1WMu//94ohVbCLfNZKlZgdNabH3tfD3wfsBwSNZ4bzEWHXPRV/vn329weJ9M06IJfc7Umqi1jvjt3cwA+dViVTB76qtk2HIcyauLdngf8dAnTHxV1Z/rIAV0wB2x3XhL98qy/HK4TGWsNNBDAROP8TW8NHe/36YQe3wK4wPbV4LZUHVu3TPN3BOgXRgf3MAoA1k/gNQbfeLmv7A1CjwE1QrZgcAt0/PJHkK9JITKIVsk2pS7ZfOjqMoReZutqgmFqMEUayc+SvXVB5m5zdAEBpVxteoJomqeEwbf3L+9hG1B6yUibjELftlsy40DB6LvqXcWSJW41QBrX2Pt058be8l6nAGmb3f92Huxa6240HfhvBbKaP4Vt3lsuHpypPe6bhrUU7fpKheLg82lxc2/SbdhiCVkAtDm5eci/c42wz2xRFUId7hpzXyvMgrewWWMvG6SktACtheKL3wgUj0uE5aR0PpHtSJRHWPmdK0IyMty75udjp4rsabTX2II3c/Ax6C6KeLq3A4HOzlucXJtb11Pf46JWPuI/rp/PHx/NP0pz8W2D0bnTyfncOqZXRgMN33HM/nTo7bV6XW0BtptR1IpL/hwQoUtKJY3FSjQiwMtR44/Y7uOS+H2y//Et+V06eaU5DfoPGwFGDuPpo2+efGBxChHvryGfH7s85g5lWf7IXP4+fnYl+CFZjpE7s7c7uRab+PVqjq48xR5RdoGlgeML4r/1KknwqxsAzXYwkOZQFzotr/BbsuO3o+N/8ytga4r/iE6B53vrw9mhHfUttPKgf9b6ZcgG8/+eQR2oA/ldyxqxMNXeJ1/lLiHRg/PckXXx0z8Ea5xkEOkXK8NrNEKK7z6pnhGNbyUkER+jdrABM/kYqFXeMsWZaEFtFCFgYPegHxxD9dQqTM5lzrkdWZvjE4OH0Dfd5ojQcX5kmtFhutkQ3J8uhGc05DjJxSySTjEdxPwlQS54Gwkb7arNytIPhkeYOTMWgwacsYpsBiOKIo6W5hC+jC4TRki8NmP1DvHPbuIV/YYlXnlFUtYO/lQmHgfekDEQdNwYMc4/2sR2sZ7VKHQ9qmOOC8Bjuc7RI5wEFN49QuUkcxt9zxaCpuE0wIC9U5dKHhEwYrdfDaMEdFpWDtToDbNfYqa3/drrPF33zzGkZIWDzZ/NMfoMDm+oT7zIXT/L027Gwq3VAh/PH3XZhSJ8XPrqmiWkheSNKA3ToPZRul08c/3eoVVPVS0ESsf1RAg/fiAkm+fSSyH3nJShOV4X/rySUVUsYauyjNmFEzEYUiNScPeqjYaBW+YEYwq3PdHkbyumRGx95kRtXeJKB/teRlPUgxX1ee+hv5pXxUmmKjoslzX8k/HOec1XxXSVFpuiUul6gL8jKmZeRmhEYJdkC+G9IPG9qAR/BB42NkcxzzbLlemO5sBVFyoQeScJ81u0iPayZ1q/BzZznx16K5FfD7d95bu7RWvndwuZxQu3/wKMS0P/n22L8T4rTIWzUgAQ9blofiXE+Fy51UFDTIQO00D7xJlmg2/O2oho/USPbCHsor5GIi1b4J04o76guBcgqPP+GVXJlbr4WEhpe+JJIv3nzRTfhAdXYG6EBgBIEvB5sBJCNjfCiQjM8HzWDB2q4F16TO9c/7/CZf6stv3+urosufBqdVhzbIgPts3e9znCvhWWSRp1onjWjfq/PD59Qw1a+txGWguoC9TWmk30PkJU80oOP7k7QIZewt10oHt8BN/v8p1By536r/f8//A5DCGprX4T7nW2ZKUhUqud8rtp8EsRdz1CSjGq6YdH+S8nH8ApSSfJO3bDGpYYsLB52e5zJElTKnbby8hp/EUG7OGiEaEmfiHh3mWTUewrc8y5KWuJFIuXgzjwWQwFWJJ8kn9YBIgW1wwAKN++w6FzwGgslziZSxJM/mg2BrCbLFZKSjTISfk33fk1+8TphbGi+S/DSjue+jVibdA9vWVLlck6in1zkb0vW//dGOng76ufuc6b0C5YX0rtgUY2RVJR5hG6LREUInS5UeacrJtFQIWAEjnfiJ+uanXWtfYNHUccwIOHASrRZFcslqj0+9zsyvLQZfsBAEbUgkzogTbuACeE9ezmGmguSQQ5r5fJEiu6fZ20P+Ywtg4hfAty8WJ1aJ97EeT+BYrPf+Xx/fChfEPIUZdWJIVLCzArYreUfM0fuQgCOGDPx9/i/40CyihAkk3M219AsS69K8eWAIaw0PJrJiVkjMgx1cmuku22brOg832hCpT4aMMw6Xv81aJRlOXV5kdW0E0Ae5hfIeUxT8/msPe3vQgBqFBCfDYfhfWqzOIX+mlozLGnSsqkpc6itOUmZaTKsJHgTTDdHzZe86ZipLPi9W6mDh9buUlmld7FhRRdfTtCa4YpVdZtuJw627H6gyasAm28oQabhLoyng4WUFAoWuPj4WjZYJCnSKz+LNMei4+MVuKEgUI+WAbzOJYMJr12zOk1v+iaWDX3+ihPo2IdKoEQrrOv9TVZP34rE3MNHN98n+nmrk9AmMoZ7qEhZGMYPZb7+JRuCunA0/xSRW4qzRxA8oB17pbFcYohLJm/XDDuAhR/AE/SABKZM0Quoe68n64Tomz4DVtjDkDvkYYp+P2xGITaAmPZ3/WeOG7aK7m0Y+nFGTFeFM8M9CCXcESAj+QAZv3rwREW1me5kyZt71JRtLcQrWPdt9V9qQJHSRlSGuzVBdP5x0CwPHvdz5SzF+Nqb0Z/tPUQgrz0X6JBFketHODENe05jHx3P89EWH+sxkXbraFzjpzMYxOgAAeda/wTArCHT62+ICVcRFan0jocHGbjqHa/SbKpCP/6PbW5KlR+/UueZ2YJixTDKUiZyGzWfqmNtsOEs7ACwJ0jp+y1OUjxbidt9buM2OBwneDS44p+Vd6ZdCzEA9c0yacbA5cgT4MS35jL10oyRZGSrlKMpYKiCRUUtb/hYN5K//x8YW1KSUnbapyTgki5OLsWyzm8+eCI7gaUjNpSyXOwR+Zp+xX9T+09KuKsZ95bmFNYwA+NA6v/G72tCJGR/IR5Mw5km5D86zrkuHjB9f+cMuj9iO7OhB/FZIKInwwWcVcl8o/IZs6Vv161Wpi2WnBJOCf39mGMwsj7HMKBT5mxFKPTmAnUZVZgTgi8wF2+XM7MIIH2xH8KfzR77v97DSoz/zCDwHbUCHr7rHhVCxkacwksxkerRsi+RmfxJWSXimC6/vSmBKUp1K2KYk9RlGejOqyQBgfgtocXDimSoJSJ/UGMRfBmuB9wAhJ4vEY9fDHH9xkoTGW9Mwl+NMoOSLNdBhScWSgXZJpoKVMOjpUkjXPv1tck+yT1qGR4wtjtH3ufrW8xYjs260S8K378LBIUtHw3rQbin79zWjpnL7YibsI60cDCaTcCwbl2OrmPlq15INctwtfXMOFA8+xIbW9BnAiqqVOxUUHjmKejp0KhEu3bsTzqzgM2p9v9meO1NYs4CHXb0imZw4uIPuhvpxiwCR12eYmQ1Mv2qOXQIObZyxXp+sR+wdvT9EAbaxIcVYXtX7V4MxvqFso3x5jUxVngAe1ucTiFiOHToDAibS8Uv1SbUdc4or6jm4BBvKZPtE+u4Zp0YEmnrJpuBMg+zvChYOlVVagPC2We9Cvjv2Uvol1oBw/ZNhLnHDEQvx+xmsscMpIOSszc4fIft2LXRAjzcPJSKlxf+5mz5+PDyqBqZkcMXfXWrJjZsX+s3sSj2ofQ9vdHuCm0KkzPv9rTzynUwm++fk1gnUyqpEE2hf+KWwY6tvIPtyRn9afhkPnpNV7DSolYo4rKpEUaFcWpDJVs+bGi3HSHDNe/CqDKJ3P2dSb4mNlXgzq6XfG8Sx19VzV5HsA+TybzCw7QH+bu2SgujVhqulpuCh+Pb5VtfhSdOgIa7eR6gv64/Li1pgedT8USc5omIGJHNDXD2YyDbG70gZMkcVU14sdsxh3UtwQ6BVLnTbNJiJVildGBD7qywEc20XrDgLCY1GCYNSn8PA+JpcRQyd0/EwHgCMJawzzBBpYj7pbLOskFTXkFxrf5RBQakhJ245Nzh9SXqh380Gvgtw7TXbXUeT2uzs2ujUTxxX7PA+a6TeFNwhHdoSkT4hHduaJd1kA6TA4BdeYrDA7eAxpMRpb+GRQ4YsvWXcEpCygz1Ib29HYsHc4LzQDBjUFCWzfpn/yRJ0DO8mbvig4Mbj8Uyvkk5bwdFghQGiRXAkeFfNBtBUCetYPkOiNtyIVXieRuUWSCDgJY9LlugDisAV2nCU9yQeDwR2odvODDjBr424FfCtWw5hlZBFhWdWoNNZFTIgCLufRGDd6Qw8aNWR0AWxgeht6c8lSfdAgKCflJWLm2y2ZtEMc/M8QSYm2leiyIhIhp2g5VFd2aV1mifripkxnPUOfFC40K8a3Gxk6vHf8dERC4IumcPYxv4nPehDL3cjDAGn60KTRlXQKgTBJUlR8OlaHvrMZuQvvTuTbw+RmhAE9xOSNQFvoIqtYVPoHnJI1FqLg5an8Y1ZLngfyGQBInd3RCWYULv+44ZcIYMB4dXzc0GVETwSZn9WAqYgqjQaZ+kCCSMAbCrMaLdf3g4O5m23GW37/lWwfOunRdpE0uKW5ub6+uKAPAEC4d2zKWgNvrJeYIvDYPhtcxUD/APbB3pnepUrMogMA5clCXS69w9485Mko9zivHxNgZust1swLgyFNxdPc5WfWIOWx0tOlcqtj/aLXi+3IcovSnwvz2m2VngTXIlVlBlRItgAkp2sybf7BEh85eIwo+vSxfZxK9A3pAlUKvrqy7t8THBNSazU+xM/cFkoyffvOmMT2/5CxLtTXx+NWw8cuYXiIZu78war9Vao7BJlCxssBuA8RLmnHirOsU/e+obfS6A5gzWwOQ2ChQSdtRqGBIOcF6vxiPPDVOrwkh4WpyG/gL2tK0yjFjTxV7iTLg2wdpsBVDfgUEf8juhOpvAAgYoktirIdcLK7WPxdqnB4YKRlghlEbzZYcg0ZbppUr5mEB1kngbrK7xjP9kiBMxHH5GhjR3V438REQFqm5DLjN2Vhw5pledii58xDlgqSyWGw9dkYxSSZqetVShXtRI5Ha0qFYLgjCrtaafDGd613gyEcCJ/NtoZ0ooh0XNYiuI3H3wggUZVyyc01/WoPkND0CSq7aenm0VcEIx9tBV7079HxLldOTSVB3Da/++MoH9wk/22hBAWQjfU+rrwa+oyIL1RPx0vdSsZS/5X02ueHbs8LWkZbW94Se7VAwPXIkr0i7X7VbXFBTqwT8fYqJhAHauC18appZoLUqbcpcxTuWZp8mah3S5qlqfFtky1tuAMeOmZU32aaq6AriQoaGFcFXXcLUHbXtpXVFFp89f/UGw7lndErO/y2cxKipaQo8LDmGanEghXWzRCvJyJSUVMVmFdy8hWZtYYiplpHs02il4+vl1FSdScKQzv1Nmb/PXPN54xcNO/cFSO+H/yLzOrknSSBH6Fr2ZOQ3SQGJ/qp2mm+6JEOKpuyEd7TaqA6I5fiFR8z2J1ipbhKXzUHi1FRCaZ8TVqgUm+Tz2MfijWT4j+GfWuezSkXFLkyBM4d/ZfwaLluzGQEkSW4gaVLlrLvCNqPcIBYZxKcVl98oTb9sUIxrJA7w2TpWxXIUbVgUpIk6voPqyW5sV0yTkx/vDIDOaQVkakMC2y2mgrVcyJbA2MC5K2BDZGlKdnWqMqUxIiS3So4SdtZH58RYp/odgppL7HUdDfwtFx3Aw/HU0e1jPcqUK1Uwn1EjUtlvPlAwycfVw+qBri0EJTyBYBbpKKJtJnYGo0EZX+vG02+mlp75q0eS2TU5wUK7voz9oewHFNbGPGhBO2HavIpQF9UZ4HiENgUrW3GIAWUgW4wVZmnFJkRddt/c49MPE0wYypw2j0aU/tiYahM1XKepOGjxWad8KvFz92Svay4OsJNvkRKj2vSIp7m+yOx/gGYVSGwmizKsnOEmIXgj8txFcxQ31beYLUFWOj3lXgEHtjMxQya2iU5qwQtcLRGdf+ciIxFCvFAhC0GdMuw51ZuwQHHgwYSLGufzme9+BEQk5uqlpuPuWWIWjZk6DKA5Enoh2oQ8QIkaclHk2hphIfZMlPpvfFnyyUR/HTgb4I8I7useFRiLWJkjBXOi8BPqTFnZiAvmy2WlH4lG2cpGLZJ32V960ntvpyx/EzQbxm/7bQ2jblbRqnDM2Jwj2yYGQUwIOCfh8KCUosPuFAALxFmKZ7bLW/wBzo6ZfQpCoeMwC48IyJviXpcJ1uNMPlujcjG2xIz4q0eIXU1JYZMoiENK1hI+QDKniJ9eH43Rjv+eXwRwij7LJRvpqwK7CqqvgJCQiCq9jlYTOu1og5N7AemGtuQXPwJCB3+7UQIAMg1Cva4XQZIaHw0BuABP8j2a7NBYBQ1BuEZp5Q6SbMfWLVSfJjehgC0j48EwmE+VoyWRav5MmS5N5RDi6rB0VxebT0amub4+WHNm2gZd/sJ7vdZuuEw4Bn9+y33bO2sMvJ6zM0OGzCHuiTOtlnB4cd/GLSEb4qxWnvsNtxqXbrQQl4kt2eCP/slxuKr1csYM5XJqUb53r7dW6tr0f67p6f2Lmr2HO9FHNdqciH/HHa8z0Jxf4+X2zvIXjXnnQPCxu9O6c58dnnuICuATdXlfKnA8dqAhl3d+169SoT79V72j34/dqzhzCHPt+sH4Lvhd1xoEE4pGqELenlrHsci7K8sdd5wxebKUh6IuRwpygTO0qgM4YFYVFipUYAv4cA8Kqq+TVWvTnR/DRWaijbBUX7mg1nQAfZm3bXwaUc4XjG/qp+a7VYihqHuzpikNNmDE6PRKEJV7BgXulc3zUGM5FmejmOAZuEQanHH8fzgcPUYztr0eFw9cjIcG8YTH3vNQogotXZKAQaoZzN13Sg4XAc/5d5eYlkOEL9tK7HFQKHryBcPTqqjpQF6OYAut83sAMbGvCNWblPI58SMXw4ggmcmsDA2H7mcRHyhyEcY/J6+QtTENdd4OqRaHRzpfn50rk6HYLgjDptEKher60iK+n8/RInNCTuO2IET/mdBoKbkPU7y4rDlhXeYttyiYJfdF3qRZes9egcBVkRqiRzouVxvRCsBCqp6yniIMMirKjBseASSLUIKnWOj/LtRgzs/fHiNQH8vXWuU3xnLUtnqcmqUBWZGfWyV0FdZIC8Stt2F7GR4cuZQkqWqxYv/153L39ieaWPprAj8X9/qpaYQdZQ975gllFU66raTUrj42PKVnZXSISodb0/r7tWRKihf9Jca4inmYoGrg7F5ZzpcnL9c3Jby7T/c1eJnw1l5GMyhp4p5YsTTlsp8G/UiN/jycT308C+HqshGxia4GkcLeh7C3uBYkqTSGsvGSunWyjUIvmY8KYDMv6Enip+zQAynh6qIhvovn8xIPDI93zOmdKubpaiOcKOsvivMRBPl3yPYujIOrqGHEqPF/h0leXFpysEhLBgFhxOsWSvE3JEk5pweKDspJ4icdVvOm/xIc650UPXcUYv76EUEKyse6yOrWiJsCIshPYa1ulyV8ncCpmgQKM6O2qFgMcVCFQaU7IUjgA/RCiud1ggUZZ8jsYgcIIyNWjZO/tl72wJkFY7bHb7M6fdZu2A6z0f/Eb8cJ0Q5eq2lJ3Ar/+5fRjj835TKdpMWP/Tw/wAbQ1lXMm9PVwiguKn9lXtlmE3frdU5b97+33oJw2/48b5ZclEYcw/oYL78otAjsVMBizgn00lR5feEZBYf+Y709f6Zlt9Hx/qtyz7Wf0PwjBgClrSXnYlglnXvZpubRXlbXeu2+MsiSjP04urRBWtCLiKM5/eXFLxxPYTvifg9oV8Jw1cEPzGtwMxMemdsTQ58mR7vXsKCVic748Vg+rkDZ+J7OY8Z/CqHEU4HEctClRk5Pj8S4LBwW3B1tT8YM1rX/+3zuCeZjaB1LuxGQGXw3hfuzMl9uASeG3ZRxFWS4Wt4dZiu8haA4dLyCUiu073xSRuPI8mFdVJV4R7TgqFqg2SqrCeHQt4Y4O/Z5bQzmDhHJUAFyLOZhtGMy28uPEeTz4UIfDwgH6TzkiX7xTKIQF55c8CURi5slXscIhblUbVomOAM9fchR7PWX1cuqP72iYe+H7yMUaHEyrwf3l0WSyefvFYVRDK4VEenAC8yLO3GihRS7r/np/Pk9ehXNHbOa2F+YuDxw2cMOJ1V0UTqVfMCoLBKOwc646xwAA2JUR2C8b0NcQjx8K+IeINOP6BBTfK1PqOl0BU5JT1mk53K8xL0uHMtUc5jY36bkFeFRzBw+lWOqQQ4gizRswQr/oInZfcys8Rpen65vPT96ayv720RfCJiGQZ8rOpLRFGxsVLASTQsuu8rTHLNOSnFQXQ3x/NzBz31YoGSGAIGiZmRgO+KdrKw6Xq3AkADllbuu/s2UrcOK4EApu+f+eWCa+JcjysAxbBcVPxe6Pjpvk/SDHpmsm1TjhBlRcPCsryjTkIqnWP7BbOIUHAcXy6bnx6SlwanbvQ2Z32+TY+7movubZECZlma4CyrHBsjcBKGoSjsPdMr7PX1xoSBXePqMJWXmN+EvIdSoVDIPwXcxqkUv5CGxNOo79hDsMiXf4YI8JhzL/mDoFSwXcIX5Cp6vG9re/8CI6FPv3SPp/nVpZzIrdPOcftnqPs46qIqn4uMxe4Lx+DuP0qrDbkOlSbwEtsz/ga71SKGmvYGY+t/iygvoW1Kr5ozNdvKS3e5hY24wDTbY6MRBCcMVLr9QRfFM5ND2SVKbPDOye6SrU+P3fcZ9r4EDxH4cH+Oe+VQFbhOKz6Ufg2dzdZo7351yCJv6lu1A5d1DYTux1BcoffItj3wBJc0PqVoQzMaO9q62fN9SJWUvXnrNZ6ztEUF3LHTVeFvuiVXJv8A8kncLNhuyw2mRl8u1pCUdB/7fHh7fDyT9nNjZzfExm8l5hgbx57IdGjKebuYM4/KOGvnipU47m5Y76TJgcQOYc3MIdy8fL5svMD+tjHuHqLM+KHrXcngJEY5E7zmFspQaq0IZ4UHho9pdXq5EjpBrsUmK9uFRDDYrMTYCGaDhV1W3qla8nslDK1XNy3CbkAAAJHvINCDXVwdrs12waen1tvcvD1ex2ES8LztCo3XzK7baUmCu5Pl/f7fx8sxh8QnahlyrHcnDW+E2YfIbNBe+/WWM3Kq5rn9BLx1ft8NqjggylTGH3s0fZLi2I8VXT6mUAYmuhMbouhOm/80x2xPJDq2Jso0lISF8dk6eLElM91JQWB5wNinC1wVR93jtLtVjZy+1VMxGekwBhq4t4h/DbM5X27CuX63Jwx36msftyVu5rVtBwjRjFy0s7ceIzT0Oq3IcAZYfWxJGeJQ2Vt4jIo4qPjvyu2im26TU5jz2ZGVaVbzaK3KvZIWUSx3lAUGffmMt1tFz6u0kan75/+oo0jnt5PGSOug+qCn8dS5IMhipIXq9pt7HTjkENlZcqQD53dJm4+oREMRpPUH4N+r1dvUzvjCz5K+dYurjTknwJtTn0xpGrqkmQ2vYzMaoq5u0/4UwW75Xhe7tpLuW3D8FQubr4Mt1PmFCzJiBu23s0DMUMVovnsJ7CkBdNw7zd62ZD4ORSQNoLNF46013iyau/huHMONzDJZWfUoil1oV8k17f7Hk8Bq4fjCAXM9HU+MzQHQXAIW6o2ABNan1fFPMKt7uEvqqLtzOJe9U7xbDT/i0vTJrbnxnbM5pn2NB9Nlr0JviaS5pLpM6OFM+YrdnhX/EgnZ8K7NacoM/KbOFJFQDVWWnNJJ0JqimpqNyzRnuBy9iuwdW70RFx7uXJ1oirks5IQvRUnT6mN0vPTqObw9Y1v672CY07hb4oJGz3+CfwZ+Y/HRtjCkM2+imv25XN/sCe8XiBgQkOli1fgj6JIW2VudKe/QCoLcxOcSyYt0HrdnnOm5AcRtClkTSjsL2Uq9IjZ/slXZU/5JCaHTFDwwFMgkF1KgEZQiEcthJeFtyLcHu2yD+YwL2DMj641OkuV6gNZpIzgkbDUrvRVgR4JeC7xLBC26zM9OITxMArlzNETegtnY600Jl5FGsVO8UdM9k+ivITRxKylnkF4X6weBNRX2Rqtu1pyVmmVlUepdGTAbipook8HW9O7ak3DOkYjgsGdaOBGPnuGFdc9G/dV7O+/kEeMxCI85g6PEPtIix4PZa2YD7UDGh/XPeju5giNctQlqAQcrMDbK5q1fys8fGbgVYC08QJOclLAQ3hOF/LZa3iMC4fCzXsmZUQsuLwhJzwHcvu9317M6/jR3z7pRXR3jqOQj6fwiQPRtJmDylCnHtsjsJ0lzkZMNelOz19KmE70v0azZ0TlRL93IVwyXS7vM0Ja/1Zy6I/XNEj6+z2LyCxs58GNfP4mjO6885zuTbjT/PxQi0FCuRpy7+c5OJbOohhpyGKmCvg0EbcI0hnH5VuXlxWPHH4BO181zaBMc0EzsUXO5T6CmijEef4LPFnEaL06T2rnTXooN70Nvz+yDdZ+NcxjS0nxFo9QjF3TsZGQug0/Hl4KSmsAyTGjGIQSSfDr4TsA56EMKZlS4M1JxRXVmMVE8vmXsBFc2TF2z5UjDMk76sYFYrCSOqKWhT9LhDDXKC4fHifjDPhGL803rpsCsAgmuGC38sLzPb6wVJYXUV/KJEgPbU6ZgnGVhoauKM/+usADk7cZM69sf8sjoxs/NsbIdPzej9tbm+lc5K4Lkjgr7xcAVt3zytKPWvy53L3fy/r2ujUcDo/vjTfEb9FIXIQ2zyx/60yqFgi0n8T6RdeOfdrl7Te9u/avubfrwa7xqYpwUhM8WPY2RiGrjJDZzqZ1gNSsgo1G3vZJrzO2kg+4pmC1AQ5T/pZeA5Lmu5kYONpIIycmbU7yV2dsG9jsyM0EHduUP2qtwDXUKsKbdB+MiwRfFXOSh7xvne/8bKJs4mGPa+YHapXLwqoz86DAdjNLtMN/sxPfl5/THTEmnRcHbzjn1DYz+zFRveeSTz11M4ve+pHPGe3t+Ga37/NvbMzxnYulP9nnjHxwnj8wsG3NP04ecLNi2Er3ykrT/GmvfCPyuYmMmxcYahoTsxzTge4l9soaK/Lt9DcajyZ7K709ztX9/vI9NnfRe/aCQE6XMVbp8kxvSP7xhqYrRWFCIEBJ+AYtg3ZTZs04cTWKiBb9e+Ow8LOqKWnY6/b5to+CpbojvVLIlvDfg3frT4T6ZKR8xIzPBdmhv6Zj7Y4cl8oNTfYDed3vgwmJ7CtKyDO3zMWtwaMbHVXqcDjm9fOCASlkwG8Y2onqdN3rP1Is6axW+2hCvf/fIrb8/x7U6jywD7+vC/Ix8iGrnpMCqwGo/Me5mUHCXgdd4hjJID85PM+72vppc+OKTzKs9qh/c7O/6MYChtNwQxX6bO9sMDV5MS3TMCQZJkRjEAdgNZRaDSBkrP0hMLR0M5gWod45OZENC/vdX043rujQrszKN3C7xN8aJ/v6axIX99IEwWasi5XC5LAt2wVpumsbw+8kjsthJe4/FE07enoyufAxLb3CwC4syGeNGiTsC8LaToE97iKnegJTmAI/O6U2GRyImdRgFlyJs5/BXQh4LI3iJGMQtc1frZEowa/jW/PIiZ4rUPjzHILzt77ERLKyqOgQpZyP8JWF3vxbyU/A/hh3OP34pwsO0h6ud0KBdtl81EWz3cJY+heIAiQGxZzSSuwK6OA0iagd6uPHa554QIaXekotvC8SPa6V0i8t/pJyN/fYKL3yjDXH1PuQfi5vZKTgLP1JWY70Stx68+ChkgvsLF98TsCu4DYXhMqT2DA+c3TPdnJ2RFrWxrIjQaeMAClgW5H0wkfgFtUV1UVj9L9BrJn3jO4xljntfVS3qO+ZqkYhLjglQ49g6mk78F4BxD9gCLlUSGB1NGrmeCkutJ32Dn++9TCSvtxV1CaqTckOr9e+z5gv59/ky8gOsfnZ2LEheWtjjU64wB3lZ860cmBMn2QcUxKhVEVYQ4YQbuQSrk9Td/qvDsnGTmP35uOeuKrTK4O34cpNK044JKdWNLQXEQddUL/vNQvZr+LaWcWpGsBjt556ma7dBHodekf9DuX0eTpt4YTabLkWLOLmwyBDxVIV05b+fTj2LfGJNAFTGbFay59MgHmmRD709g2cw4YngAZo2R3aqbSxXsfCSxVEbn0RDYnApciXF8rofLBYJAIXt3Kff9DpA43dKs/5VxX9vAvKzisSNe9/3yC3p7SChILBmtji5VUon/A6G7sxUpF9D+uO1GE13l+91qoat6tcM6jk0L7kyWHxzyBSkP7579j9sn1GdO8ud/TXZinOvcTNjLaovzCXsg6o/4v0IxuUJnQoypwSlLzq1Kqmj38JdZHGXO+Vwr9KzfM5tFKlEAOHwWFwBAkOh8G+IkHqRrSliIUrteAwuBcMWAbEP0XCGCg0jnK9WH+7nwRrhQK2Ce9zya31NQm/cq0u1OPcn8perUmuudWE7ChYEBwOA/nPPIk6mpI7T6a2R/Er//LsVBotEucdIv3mElKLpKVpGZrGJDSG2PA4yivpFlRhUYgavT8S9S7ZuwSDRAYz65f7k4i/MskZwsM8BMnHh7Vg5uRFNbW035qRWwCT+DAYrN4fG6xvvXs8Kt035tyKHBuNxCfbr68+RiX7ec2YwuJfGFCQhvu16+u1j7ykKAbkgvvr649qpQzyyw/uqTSgXzuqGvCSubrG2vcd7sezORJYwrHWNLW3BUhMZS6/0om8J9p4WlWl08MZlMUsQ+nu1W2Iqp7qVGn3LpLFwJaqIMOPfhe9rBMXWmZZixM+rt+B2ZteHC7C1UQ2J85R2acOmMPgoEqiciXWRbbTK37WZwX12v/0SrKSxyNqSjKXuwwLPZOcfyLLsnhZdcvrSnoibHZYL6ezJb6+/gjPgXT8lkZ7NBpo/p60C3oxDxYb9z0C6p16DGq8rx9BrqlutK6wL3ZnJVIPxurOeMCqc/+GO8JPx9YwPy3Y2ZgusHWMVRL+LCO8p/UEzPus+6F2asO2Db5KX/lgudkw1pT0GdS1XVBr1Fvu7/kwL4iCZruqZPs9M4KhMMXsID0jSs2106wkg/YTD4SBZuXao9QZ/1EXGz50eFamDnibT+8frAuEzq0sZGDDsCXJTvGyVksn5aJ8w14lnYm91LJ4eO/VpDxKtuw5Th45qKJxQdkCbL9Cge33/CKrSIUI2dl1y9/TyR8+4NY63jddWZm8U/hNvqBkyrXrRRu9OrIpsUmXa40TiRAEhxDxhLfNX9fqiepIahLaaeMRtYVpy3XNFUIQb65bVlvUFx5EkyfOiepk9hxDeJbL9CsUeFfL08k9C+uqZYcAD2RBXpBiCYj974LamsIMCMtAlnPSRoV+4RTZxgZIkZPSMF0I6FNVmpSGwpL8jzQuTgQW5Vv8P002lxeesUqx4lj1xW/9a5XabGDzlLaLIR9vY4jgcTPNO5/8Al5c2R3wo4y4Eo05zoNJI/m8Y/BYBqWcgoXXNn+1TqQFv4qrgo7WdA8B+6HCJLrH9zVyCHa/Uure89/uqKllDvBJFAwij3T7n68IGBuFGPrWP+4r28+sNn3Bs6+BD+T5Rxjks8of/lRc5k9O8i9rFTLb+mBwAI/izwARMsjKAq61Ajf9UvMBiiJf6Ppl7vXDh2+Wf1EtrP2iJjiEfrcvC+rccsw1343yr2rm+qq6JuRzw/VrE1i/3O5axmp392rGDbv8VgSBI0QMBlwgfbWnZzXdiKYzOR1SrEBWd0DbxHCBakROx3hazsV76ttcTqp0LoLE+7vvJFt6hSdq/G269Dwn03/4+shvi4ylyfz9H2Fh4h8DB9W7Lm/z1bcMcnC4eFu0UhCFJyJB+ahTXV3aaKBlcGG0XWEYQWmDR31Gckxrus+fDwvDuypJjWnaNgISFsghb97mU0cPHelENx6YxdaH+aSnjzpZRUXB9miDYo8HalTDaEQQ3RMg+tKlMDhjRkgtjTAYIkoT0qSNr9N7T50soxc1Sjl8KJK+5B0DX7vNaQmP8FBTGoF+WsTAMoA0AVucrWL76JEbfLJMP93TCsuUCafqnTY62pOyBPWmgPYo5E47bTMT39wcP2NzJi0t7mz8tCubjj9jE2KUbgWSu518K+UwxdLz76+vPazVLGspfVArKvd6WD+bPzWVfXST4ZFCEJz7fMFjsXQjbrhoszrU9EwqMmiylo1yr259reaRl7QcZDXUEyWGOFWcWgGQPC+jR3ZyWHCV/qXxdB3z3Ttm3emyp7YXMidGHeqTa2SP/M6+yl0Bur+y8kiooFnVgSA4hIi3tGrUWWHB43PrtYxAJ2lqJAqDKqFY4SzSODBr+fyeCTszWiZ8hz/SyJncgpIU1sjMNDFM5RNqi8qIikJqO3aPHcHaM9O05aQ40l7YmBleIC6S5MVqedcl/Qh+KWCGX//hggSSKNDGct0IIBMPpNcIlc3nK/NPRuUC5G5m8rt0m0NzC05GuHxzUcA5/kmyNG7F6hWX/vfT6PDAYvNnWWPNrRzq9CYJA0Atl7K7MFCmdVdFpZG8CW1ZoWD5tXPjqnP5KCEj1BKYQaLaqACU9XXT55s01BmLLjixx4XzYafMkwEagx+kiUReZsZloQC4hnHmLvMB7PM0GXJoa/nVxt9n2s+nPLIt95LtuEXFSoKBJP/Xo63vmmgrVtdnLf033bJBuBh62jR17iOv89Wjy9Ta+tZuhK//x2ETUIqVxSUUnKctV9gB8pHTbKfbSQBf9Tm/6Pn58HKG42/wa4EBmD8ELlH//axYyhzQ2cstSLqWJurpXc24YV/Qds2e4BD67K7nc/OooW7++V9freAP2YBb2yb+luH48XLpn86f+ha4lbYl6gV1Qqmap9tXELBGoKarQtWCmMTYwPuCqMS4OJQ9p4NmKsjoJOP0GlCtZGJD1JjzEA6/r3/amTBKGfmgc4QTGI8wAwsF6AI1XemjoyrC1gBNA6JGqa48OO/WEaGmLm8PYDxEPJoxJQXEjj9GqN2fwgeEddYRpC6IKIh1xycBFUxP4bkStIqkER1FJEGGe3sZXvQLR+UIghtakcKv3ScQik2QNimiKhIjEqLiTz1aqBVGS1PofljsS6ErLraqtnh+HhBNLkhVwpGuExk/eTwHJKGHKHHUhCC4PFmZ5OvqqUH1oluk1HPpsnfMrP/RDefcNsS31DMDa8jXBKmll5aK82V+F+mg1kcG+5F2z4ePT1H1+PofZi6WqTsiDmgdRhzRQvFLO281aojXmLanTxi5q/7FIzX2FIUscwrywxdXBW4kcFid6RX5ztZvcg9AOCqCCmGKuXyBnhsUKDubl7+AEpPJaTDqVFZWFf1h60NJI+JRSZHayU0SHlCmyyGmj8POsfywQ1kn2JGRwQcR3oxORkYEUJ0RnKo3HFFZDMCs6eIm/z8nxcIZw76zNFV+dd6D+f9jo+qXkwLB5GugYUEPXmmUhv0Javgoqu5oRL92270Y3ux0uYotRMiuq0MO9H8I7PamJgy76Ors63HBFsQItFbQiIVBNbGW6jXZ+Uanhw2W8yJTpK+wXaEA0VLXBba1xfm00dHlLUujwXmC9laDQKwd0O2RCtu0nWwIZW7m8zczQ/H19OVpiDE/FH6wqppjti16I3bJYkOEUBjRb3GEDebzrTadR4GZzuPxXYP8MKqTP4zfeSWc8grfAT++Z3aOF2tFWEU8htjA4vNRFv9s1gi9zphhkr8Yz79UDtCGmg6S248gsIlEw4SPF+6seAVPekNLKMqtbTvKncqiaCMbv+CpScBDqvRIbgY069r+iywjioatpUbqe4thgLiUtBiQm+xyuRgAiocdv7mIcQ0jIXeRgvwV7z0Z7PX5TmEfGsytYWJI1AMfPTy6fvzHO6Wc8QKj4k0qChlNFgTiVZRYhpzb6B9n4vh8XGycnj6O03l4nOvCb2oSYJWjMOsOzs8vsJCSA5cNLlf4nRfo6QkkpI0InUQZS6GvIBgETPu6LNrawlhf5x7GChwdbI0kAK+95ZIHuH1gVICdtpu2KXT/3LuAB0eQU67AsH++fXGpK0iQsExzHYuFILhWay6tfvsnzMx/+EHAu7le9ONJnmzJOuK5S9As5tvETm4W1xS+m8sN30XDCiJU9HVKzEb8t2fh9nP55KevnUg/ZXc7Nzfp57STbnMi6T6BFLsUfrMXW2XzUOnvGQ+W/HbrG34cE0MIPE8/oaeVNDFJoLNXAF6sGxlmjQkNc2C/XwIySrMmYCFIjwPCB/GKLae7X4QwPF2TzSTS4+hDPzz1IUn+s8L6Fvt5+zFvF0c9HFnNIKdLSqJo1ow82y732/CuOkmH+ps1G+wR6+w+e4fB4FhzJ4ootLLvkAIpTIPh2bNxgzOn93nCzSQNPsmLMAnDbsfrSGai4V1y1yw8qKZng3O8gHODUCL2mhLHqaNogsx4qUaPgm4GFx4NdiqoBnS13GhFW8ce8q999rZzdyhhUpko9332vHMR/+YgFhFCf8rYbkP54rBb9Xhtj/FH3zFUFidy5g1sGMpLeBoVqIKSRnyFiUrN5Q7ddNB0L9dV7JahD3vzabmAYIfyXS5yCKjilPF6n60WaByaT5hIePgk8d4CgoUwn/NsMA0GGaIVCSjBrTjns3DAtSs8uVresxWRNs4ewQZKLTSiiLD967g7JeoT7DW4EyXIuvOGB/Z1sJmW6FA/zKAEhtICOyxZ5YNSbfAJio2CogacMlx2pJaFr7Q4bPGp2X6dRcgcuBf21cMfmn80BMH5IYm3vstuCQqDRbTx8U6JV/crQxG/PB/4yFVbFpBVuXIcbRAbOHr4YfkyWa73sBQL/7uLdvl/c8/1cx8xriOq+wzSF/bpOttOmndk0omJ/Eu8tz5VXVfixZ/DBe8lV8f3BnLmIlFYQvBX50kxXLGkkMM51tOAYQGvUi8MbOSWqhsaWGoqo7z7Cw+5FkllJG7kExPD2ZoLwwJjUPNX52Af0eR7MNCyqxvw+LjwIp2uMCw+Rw7GWpzwnXEAuYePDy8EjKEO5Salt7WpVEUxNg5Y0pRCammgZkteaQ4dI15RsqmJW3PZ9/mg5pFW7JxTVzY5ykZsKn8VGKTeiniQnHfXCRl+MT//gRogXjqPEmXRJeUzYmM1CJxKwtIcXeKZqLyLE2w5bnEhsXKIeQEXuDWokPncy2pfA1kwIANryBlcFz8twUWFQf+tAjB+tQ54GBP7UNLPNQpaPSNhEcWC/Y6sJW63ihlYIu86x+Vl46Ee/WXJPAMzG2TmB2k2ZqrvbkpjIn7gjorOQj54om0bo82hXCa6cG28auk0+02Q/bF5Zr5a1+iJ6JiLaEtHlH29Ws4wjVFoTpJxay8tFGPWcFyF/70LWoxvTrx6Mg9vHDILXaVbViASJxv6RnCDa+VjIs4fzcwvYktUQYCw592BRlxfs0M6pxeswKwE/BISQjvb94suriJ04glVwkw1/ZK0PPQAmPgbjnTMwVL9CK7fKVfsoiYozAmAHxrE6JSD6jC+yuJLojBNqISuBQ3oZgJFQ8FYgjpYQwY9vALqw4Bh1197Mi4wbQE8hmKnoDNINYxe7hy2lXy12cVsdrXJ82jdUKRePFaBwSIvD03cMXCjgvrtz83F9yZLFbVzfpodiO8306Ivm6yvK4U0Rrc+9w73Nb/7k7nY9qy/Jn46cbxlz0z0SUpK1WnKdEzrnsQx22lBijNmS1BDUfZS1pChwyHwg0OIuFCuB7VHMiezwp/wJUem+ju1yAMBhxHu71JkLF/C/E0VaZT+hhXWAn8TddzkBDRrEDkGzdo142YTDXIgBumsDK+p6UZeyFhGYsf7sVjKgcgeYenBkEXToqJDphu4VaWRaykHeX3F3+orB1Ny5/yUo4M+x77+tvpTTPSRf3O2zKsUHVD9XOC1+Vu85whkru3Kw6I3gk8EvcafD0e2q3KS6sLhRfzsUfym94n7WIbaAEQXyK4LUmBMA8YnxIVQ7Sut+gA2HneZcwHAEE53XEXalfogQxb029TiPri8VH+af7mvzjOEWeTY0yuQZwRYQynwaptkCr7YvYcWhRt4POkpIAGnfvDNhU078wATqmHK12kCZUrYiffWU9o9QMsRUj0oQgKhEZusT4l/jOOu8ybGHarkjfH3hagE35PBGA9F4BskKIKBRvFl3nrzcKhTXvqX3hl4IRZJnr/9TwhAyAHlBDISi8DXZ3xJz0vL3dq8ztu2zOz11iezka9D+US8jvDfpWtNzVvkP2B0MIq2sLWCg0ab/PfkpS5lTmApNA9qi2sfT0x8QEd4c3qFLbMuSh5XSXPK9Kamu36++fHV9MTKnaEFy50+j4lBENw2koq2cRy140JAvYWPTJSeu8Qgi2L3sXG76gqCiGREH8NH4PCYq3l7Af+sbmLoVqllryLGPCBMHVmRjyCfIrWlIXLIzQ1BcPGM2k0FK5DyofVE2BAIRUwPbakFJRhgeqq7/4629DdOp62Qzh68FtOvzMnJCIJLmhJw0GnrbcA8WUOOFTVJI35lely82AewTG2IqyZsDhiDhljjM33EvysiBtwYCFCAukoEWvIc4M/PJ9JsZavIidFnnSlKbStzP2PT8MOmSBmpJozP2JLMOpkX7974rsVc8HEkIWOdl1ZUcYSR2cXKiBTKo2uNtqr0ORGdAXEJ7QHNERXpJntkjXz2ZsaY7lQFzSEVsj5oFeNvBx+DCqWJjr7r7ngDyej1AHEjJg1F8u3sUu3KzcqPAn2vNQewemZAS8U8LE5vkUDNdYWLsC0t09LpMufWYMF3q7SuCIlxgt7SRVCwKntnjfVveuigfCELO5FTkMgNMRlbeW+zosqclUVCB1+hEDiF+UKBQ6H0rEI3QJVAhFKnQtEB2UM5DI2k5EyjVAdg++vZUf6nKf1f48QOgVzJd4kARJBzrrJcnzQ/Lm4eTQ9QrmyR+31hE9eiVGJ1yH20lVhCBrmUxENG4o8A4loN6YWyGqoUy2jBaD4TC9I7Y8tqOmrZQRgSrF/4Ym7uRXxdHYLDxavTpgAse327ZZaV5Aoj1V6NSERQqffJ7QzvGh6BrsaCz9cE1i23v4qb1ly3BwoC4p7kG6X7gqxiYvIHyFA7kz8GAA1O0nUqCgLLuw/NDIg73aAw52bS7So1nIT9zMA6WyN6c2eAcvwnjohzE5qYWvmkHoUpb4fce1W4NzFZRzkj4TUgEpmsgHU0Hp/M5Z1hnGnnA11WnRtZ9iGHGhDHTH3bg2Eiv6qjS+qaxr+6pAg5htDU5Dq2SSebpJDPntOUkREPH2yXYVAP0slNfjpz4tS+nxBDkamLG7Mz7CI8Ty6Qd9DUyn1KDow5YsrN+fusjcmFQET//rxOYzZ6fYsPpsyejs0FCNkmDg/Zh6Y8XUTJ2sOskyhiLDHEGMyOZQcn0q3K0ZzMVXSrbTUje4589TSj04JnCXKudc5oTh+rvdXICPO6jiIJ2SFFajIsxKkIW22QBZC14bgczhACOfDP4arXSIDAf4eq/gWkgp5kpqGK8kerfvaI9eudfvL97WSQgS5UHaoLpz++U0MYfCPaCAdNjopM3/8At0aQ7vXiSKSsO7fLka8uZyNnTp8RSwawz5iE7y5K+7fhkYGGMMnsQrsfMvvCl8D11u7KYqB5a4DUB3YynxEzg3IDzP9HuNhXdry8qiUurlLBz5XECyorz58ndR25z1ZzBmUSezGBG4PG0z+aO6sWQfcK9PnU8DO6L+02N9086OSID8NoieTCZTCoM6/E8e0kdv5hk1VoD/lG8U9xAlU21Cya/BXT10lBdlolHopOcuDtp1ZNS/egGeEZ0KjCc0VeDipo6Ao2tPXCS72M0X1bx33Nop8OoDvL74NrPgAJqIJ5mnC8pZef63jyfr3cqlNmWVDNGiPabAiCy8diOkVtKUxPWKqmS3UUxXb4JyYyMDATSLhKoCrdQACBS6WKX5HYDPu1ApORzn+ag0rnF293wUa+I1toAl1mM+nJE43WZDAI8IJFqLB9jNmOk0sPoiYnSdrNdogsOR53fJwVHZOQPfmeOI7lm/+x4E/GXAQE3EMw4SAJOR08H7Lhi9GxNBGpWxJyeHLcjnKbza/MzP6wN6p+U1jZspzlHqFlZlIuecxr9L0CPP72RWuY6sjC/twSbX18V6C64g8U6o9Eo8Hm25kRaepInZQ7ZN6THTzm+fCNd7dX5+N3XZ4zNhwIEBWUdM1W44DH/1b+nIeskhBTOTIoCFn+OV69RO+f7FH3fwDmCfdSMG/OVU55kLHiHz/ffxJHZ3Ei6+KESKpmok9dHVbfJFAL1FsvddhbBYhEmSx4vI/wgDqrxY+vxXraA8L0MYWIC15shL6krSMa3/Uk9SXY9JJmAIHsiE+yztgExbLgfGlLrAyOTnqLuk9LH+6Uhh8LLte/zaQYOa4pC5xa6e3qVN070i2nXmRaBS5Er0rk5eaicdWn033qr/oWY2uEZXqHdbtxdGXSEf7EqKqo7dHtyvU1wlhBVJcja0gUpidFV04pC44H5jzxRBclb8aGI0QROzPKR5JJs0RKj1yOgNOz9Hg6wjxoYuWqUZiBajdSTowbdL4ko5AdzVvqEijVgjyidxAuGBfkHOl6RDV84WLuiKZ64ShkBb94h5YL8UM0yacZXQM3AzYNqhlqubA7QeBBoUYelLELa0GuWpHH50yo44IPqk/5WbLyKKulIeBosgJH9KPM4B1gnDegbMS+urraUyKb57BJGAMm8E8RIQkhavCYnSsbjy/I+i2obOCDOTYWQXAIES9TYnRJy2y/saLweDYT8Thbf6W3oKbGk7FSP5sBIm4xQ8QEtl+nXLM+HT/d1DQTf8o6bdF/eRX2lBR14hAWgK5KSbVX5P23KNX6bPxUlZn226QB1G7oQ4D+7EHGA3GPp6Yfx1UDDHAc4P4qgHnzY9/U41jpBRbnFQyYEMwq4XmL8D7fjR9VIvC/VNT+ufcBnFTC9Ks0hjklk/GVthOd9ecDcwOEmODQMvraRKirPU7XtDLyoN0kL136TexiY/tM2jm7zMZzdgSHEPHVKBQsMyWtDW1Nql4LK2rw811tRixyj6uMGwplR4yStN+cxRnSMvV6VpTMITzulBmTOtwTbrSH3ZlXvjR03MK95FXW9Fr0V9DfsUks/a3ANz6/nfqdrB8GUZF6NHGgVTAZtm5eWfy58c6QfRXW2aIMxRHpfD+638V2zbWalALiK9Q0RBnbarc3i9TqVpHVLmpVzZym5VOVdMOWX5iLDpPNgY0lOYawUdYUu4887FFcFa3YZD0KyaBT/G5gQj0MA1VpTZy9LkxFl9DVwZA+nb1xH0Dsmx1XWH11cN1a3orprpDW0tQxxqJ0ia9vqK+E4N8eZGRkZG/EyGv+kH2RJR03UERpOusWew/o5vT9FhpS+WtGQT0r7DFMIWqz2VtEajWC4BC6pzWvnyVrbB2kM0HOiI/vcAjPfblHuTvoZ/6DLSY1Jd2Q/X85XkdVlhtTAJEsXc3h3eVQ0zlHTpC22NTGS5fzrLHGWJ5tVtfG5AFc6eybANSa5vj4DQ8OGu8zAQgs3icdidpXbFg89TUIOKOmlkOZp6QDAYJQU2aj014e6AhVZ+TrdUB3VUXgyhj5fY6UZY4G5SArvpcEgsWMMROk+jrldcaKgGjXn2ojRSFioSREGCIRikNA2sAJ5ER2kLd9CKcoQyJnH451FYNgaHEyWUmJ+PsIOnl2e9bsk2YIpVY2ao1RFA5wxJeIiP9FhVKv0tEJN7LKS4svfhqVPJKbb2W3SVPVVHRhSx8yqEB4DE9yUmPS84rO/JlU+pbKmn++E/8V2cm/kV4TXrXWNtsLXbqwGonXcRfDtD+4QG0BBUEW4pQ84cfUxLAKar1fmpuB4kA7nnGViC06mwx0j67fKCF2P3rqgRMTSUOq5LRieCLlRCHPl0ip/zcQ0m0LPOingBMkNjLt+mqicCA0WlFawWajWSFL2UBrnu7ZDjx4WUro5Cs4g5nvma9mhdKzCn8EVMqLbqHnVMpdPBHArNVNSiDx2o6iJWIpOQfv5NCwmmnbIin5HtpNpCaDTbrqW0QKl8m7jvko8NbUcul0rgRetxLkAF2/4RGDjLlupFGFr1coMCpyOe3PouBXNkcGPs8UuNx6iL7Qxy1iwVec3+sFBIcQMXI0r7KHDtsre8efWMbVuoBXcmnnVj1qVFCnFKvuTl+9UlhoqUo/5/G6yD7pHTv0RnXTFAgxb6MHf3z1eaF+NTjl/YoLkiYJnM4UcFr4aroO587YcGxDfqpd/7vnGodH6PBuCvCgIZcr8Fr4KyEICKVySvw0UUMnphQIeBg4Ak61U+uRO+BTQcKQmBEn+7DJ9CCJuf2OwSfA5NrJjx1L9QTLMVRmS84cgrua/IGXMCR85RwTCWc2+VNkhaT0pi1bpGCM+JC9Z9k0u6AkIDV3taVG6nPDdw8Zn/zi4Ldq5NNjFSvv7waMVj+CfXa248pOQBxwIwF49khAuGnlpjTz49WD9wA4r/WPAScpxMgPm5agTgFuZAM+jvddohnXVK+9ylxuMDeruJmmzfShcWjBa4boPlOOVutM82K7qpei1jZ6V5knLmwJ+6C78EwUl5oiJuDJfjHZHmaB5GB0f1XNwah5q4KST9q2xk/avGrBZShzU+DS4+J/eKSmAMjarpZBvW+r+ornKWsd+ur7WxYkfEFeMpPi/L5n/4j1XURCR6qrdewGsrp6D8yB9PHzYagtGUVr3I/E6PubwiS8QolYwqbM9TJgJgpCntgV9Sr0dAH8W044MA7Q8WqfXXqRPejuGSvHj90DvYL4kgTo8PLyeVwqkEQlFQWZ/0qZ3H0RH5OSyNfSxwuLJrE6Sc5KtQe5iW+gDwNMXp9/+n7f36vzPI1fNFgBVEJHOxuMx09TIVD2G0jq3RefzvMxL2Ht7WoZabv/qnrw3UQMQuuejdyjkbuH1FCFIJ3KRAlz2LtatwMaLPmx9bLy45SGMAoxUPNHUsBM3m41KsX6rE/A9IDXZblpl8pmsgLNhhkEHTEL7tyVwQjXiOudZBG1DLn/gotYfnwXTbLdL6JxbGdCJ+2qn5vqU9k7Ht4vE+P3eQIA20bDQf+c5uxBo41BGWacjR07dCNmYiLm/oz9Jc166qDmpSvYettB7KUtbHB97MWBQM7s4ywa51Jn1c3+QT0FcDB0EVnx6lFMfhNTPV0cgdR2F5lHEd2lRo9A7Yrhs9MyQUw2lW76ClT7DW2Lad5BDj8iNtwu93aPki7NtqjnODi0ze5on5/SrlaDaYj5OTJNCzc/t5whsP6btQhkG41SAOZrm1jXSOZhF/y7FZBkq0Z97Da0R5dkTy7oJW/YwGdD7/RixCAznaHTvLwq5QGIi/4Th4CUa3Cc8AsWzzjZJ1paxx5Yfv5IoqrjIEBYo3NHGm1QiJEh3LA03AorPGlEzXSRufNCvGKiVjoWWDhKOjY4EW0OSBwiBINfc18Qk+YwjnJ4tJ3ctOsaooE+myGrTf0ikQ0cOLHJsLyxEpisPgMgy01ld0QM7I49G/0FDB7BMFZgt/1dJrV9h1TPcB0H0tyQA/CyrLmZlTTmNOTtpeciLfUN1mIu42HD07mpLCnP9tHyLEg5hJ97H/xgJ4f57wqSQEBeWcXrkNpz8+6UlHcxAKq81f5NZ88FbkHTt8GsLIlGwtGITaZvgrLbLoR294ZcLsjK+c7PT/RSb4jMGbJURWVlWy/xgcul3FRQuMEt3L6esU7SicRbsdTDFmKt5Havc6hrvWtuq2JjaIhi4pi51VdIwh5P3xzMrZq/DvZ/Vsyms0G54zFl2b4D2thxzE+4lAuG0eHBWQE47jEvmmys9lAfVDfAVyjtm7+Cmd/cjwQRamp+uC38bQkKTUyj/PVonxuvlN3g58oFX5XVwx/Mqtxkt+jAshJNSgsoxLzOCFNFKeJKlWSK/do9ROutKGgx+4wOt/mxGqaCgsvEB+X4mdF2hDfPVkNVU8nMNHVWil2cF+n2hatVwMOcyIgMkSpBwZbYn4UHv3DN3FYeGVl6RI5ipwREYT8zGCvF/yI0NIpmIgZl+JMDQ7qO4HIYgjkBVDUVt/1yiJrdOxchosQBZTAJqdZ5FhUOzi3zhumZ0HkhkwN/626Y/Nv9SKAlT3e1B9v2HovboWRVnzc9GjZY5ZTjai8KXo2x9xTL+Zjc7oFo7XFdXdYNk7Oe3G3A/UEVN8/b1rpRUvW1GfOb2sJbzAHpaIgyOwTHYJu+Ni+cekGJfc5DaNjnTZ3D6ybNTaiJOxM/09w8nXDGJm1XO19T5I4KE9TGnbbVw53zn5rGA7sAmn8JIz/G+TQtypeipS6ZHhcwoRJKmrWKvOiOf1zVf+ckSSHRZ2AtRlAl+/eozQGy9CnMAx709GDPrYePIFr26avlHB14ENPArKHA6J6ekjjSkjuMleaJwAtIiGXJcT0gw3REBbB6AKOZpPxK2bJmIttR5WcxbRJ+HwuJ8+RV7IpkIBhFc8QErL9hje091Mny+etUnbKaidvbapVbdegPLpnXSgD+C50L7jA0dLQvCxgxcuwMDWNTqVYKlXDmDAVHI5w9Q3IL26kUtiv3IFlJq/OJyVDAsrp226Nv2T7tQtFdBxLbj1wLtXKxXG95rH4xdkbnlP1fS/ol7PXz/pyTaR+4nrFZxIhMgQnVtL485M90g/kCELM87i4frrDKWahLMlOwCx97arRAdoMFuo6uFmaoWOqgDJ+zkBwCWu2rrAtUs8le+mjLjjhUhU8lMpbOpu/Sg2euyZdDgirhZzkLVcPtdRPxInRVJZSErio7WjdxAUMSBT/IO+sza1wU8T6lSnZXzRc0Pey3TJQwW5In9uiwb8SSeCzj3hha3g/yA9JPcZfr51Ni1Q8gXgSwagY0ToOYZyTAH4GAEPTOANCM87efBkBUodPUQ4WD7RjB6gnQLAexz7QBP7mecmHrytYW5QAHb8bWSSSXKIPDSlLJIYp638eCll0hgH/iqo0apkCUFFADqtWbC4s2uYVnDdpWlqYukDRtJfyWmEMy9mWRoERSRhVpc+qs4+y2MpDFrr1ioJyZvWB4mSbFZxRhuYkpEZUZmRWRKequmkd9k1m5Di1Ri7+0jEiXJI52doLpYPoR49MZrG7ZPE117naTpQEFbTlLTbaHCvked6rWj7ZlGzQZ+n9peJUoyIXZlzont7DFyliV4KBIzuk2J4+kpIzYm7MX2xMcQJO8GGDMfoe9fyOq/jD0pNtC9RECumFqa4sfVdB18fIWUzPvnTqUsYjgt258aYm4aA8R1C8Qdt3YHqpTX32aK+teWJfqdxen9ePzzH+yMWip/cvFmr0uQoFTUR4qMMAB8KtS/tQqNkAIEki6Zx5wY4EhUQ5fDagEAKBRI0cA6gQrXJ0K4ebtMBBQieMi6FBgndVtiD1By/Q+Ey2i1uSGi7FVb0dI42ENc0JvIyZlSByMvB/i7Ji+HNpkgSzW+jM07z9q0hf5WMVfv3HXsQ6Hj78P3WcW2WdXNOBvd6QnrdRVd/HsX94tsx7HKQ7STdduW21Hm/7wtOzJdZPoNOn1m3S/g6oQDks5VKMlrHKwjOyspNLjB940Wc5gVYgsojMtPtYUHW0qFPiZY+P9L2RiMW2KO/gG69z6BY02kw/9KVAWBsNaafs45UCfjeWJPmG1ja//F2BinzRFOPlnpFgo6aaoknO5aNKMsOCwZhiLWDCuAkBHPKIz8M/ZGYHIHub7RUtOvvlTLM25oiDyQueIH1TGor/CxZ1T+BwYMgTaAfI7MCzKUmBYhH2yc6XAeGdURaa1PL0xoiVQWg6isbjExLMKtHko2neZgT4d3F8YGpi/O1Ad6jxKyVhfdAkMloqGTs/EOFDVCVEVmZkVUQmX7RKjjVmRlYlx/BKVqUBo9dJouqoc8yyWLLA44ectcQs1RkGxFABMLTY1c59oc6IBd0ezgCcccvQ2E1Vxh6wDk+/ARUG0hgz8IocA7gwhf2jTWI8afYe1I5PvDKMwUh9kRj/Olz1tQx3x/aIEPx7HODtuPBz2bOnZVthzOw1CSGswMTgx9/FpCW5DXaIJInNrCL+kQJzwgxO5nwSDSnC8AlSCyn0zs+GjMgYP5MjuLTeOnc/PBc5EB4ToYuKVIZKul7iP7BhUzMs9ZgZXFNlE15M1XC5ZIws1RUWTc2J0ivgBPiXt0GuUC1MZ2p0hzI3MIuvpGnKHrIHhTOSoqByyLiZBGcyG/t9uhpJ1r7cQlRU9uFNWZBTZOi394HuqCh32MsuNMtWDD6JY5eDsEUeiDItWMLPRls0exjmsh09ooRIOWU03kE1RTEFUXUaMHPKoOTZzZLrQiW6U3WqyITiyieDAc+cmZmWxcqK0vyN52UxFdJjyD3g9XUfOYsDo7ykX3zNPR88ik1Viju6wxef1BkAxFCmGIqCeqCGy2ycAvBPLBdqq48ujjJnR5fGAG+ItTvgbizdDwCSHC/nf5DQ2lFeZExq3dJoN3TI1o0m3VsroNO1929DYunB4PH6g22DpNpsstP2OHWN4EXJ0YPLDaFiHKbmxY7qPj25s18JJ7LeC8Cs3idtII92AH7dcmnTFrqAn8DfQrb9mVGqOkXHqlv0uFCM83hlYbw06fSjGxbRW4rLJm1ck0AG87CnruLnh8kP0Ga9q7GKpxkxfZWoMhenMBFZ8JV4kvbZHmUv7QkCCU6wQWaSiSEnZpO1A4ItNvNlyBM1Y6q6i5fRFmO90Pah/nbwlO+m//KWrvQTS3cAgtuh3VUIl4Z6pZXDWkxnerMwMnb45u0C+aQXXZdLE0ZrTVeBPV4Xt8VGXy6HX0XklPX5lRZVsjwUuuBYs3Jx2MqtB6rxA2z0r/t2fOmPP0bngoeCy/dg4N2TsR9e/S7eTPfa7a11EoQXdOp8UrOwT2hST4v2E0grWJY/+eLkFiWT4xuCF8cTaJnZ5FYOe/Qh+fc9H1+t7jtp5xrgdc3nlc8PcbmYTs3tuw8ui6aMMGW/Ojd25IBKHwPG2kQ8qy8HPx/6QNMOJT5KuwgNWnoGXEGqzv2hqErFTAZtzyMU9zUkcggPkhgGYDRqCgaAxsmbc1+G2nr9ITXZbHQDLLA1xRZsUPyfqeZSErjvNkXeBbGOHgsOubqlOQjf5xZ4NHoOBoCFSN/FHCU32Vm5mzScRDjZ7GNJj2bYLAwED3ACyXveA+1uRsjvsg/OqqiNuhkHBFE3FxEH1CLWO/o8QqEBU5NTRNrIG2Q1lRHiZ1JWMfMi3HxQ++XmqAZJeCc9h5OWN/JtLDOC8YAkuyLruxAnd8qEGrNgQBLiXK56LuJTwtyCmqzCCPzouLb4EWmZ1VmNCAiUFbsyDZFz8Vno9s/BOknRY8HebtjDkrigE+56NDnP99ebQIb0JOTkr36h7AgLVfbR/ejlVSxniG2E+4VySypg+u6nkwjnoW4N9in1La74gLowNj1XWWwaOKOhsWZwopmncTyvqz1N0qn9qw8rB6USGqfZFQswHSGIb0zDy2HMmtwSOF7iREEiDsqH7hiOPtN1MO3jgI4ZLn0l/ToofmUkhvVOAGtTEgRdrGKbdZCgGRzSEpC/boqzb0afjVMj2E3QfpSxMSjxzB4K5t8zF4HKgErdIu8kOLkzTkmJMppjKRHPiih0eILxuJ6hRhqzTylCH2jo1oWr1Ahm2Sg0DOBJK+Ea10F0J5+u5JGyMNk+szOSVyOL5pWqjW1jJjgjZitIucmeqSwSNpcTip5ltTzwg9q43ujSu9xJnhsj2G6ZcnJepLOHVeqY13ahMApPgC00HxAh1pvDyuOTIaqOpMuYgUc0EK8SL65dd0EosvnCxE3icCbsPJGDCV1+Kzioe53zksj8taz2+NpQ44FIkuZg6PVKdbbiWlhyxNfse4qLFkZDW+rhjiH6cMevzHOkLH6EKdqtmoNfgGAuaNG3N+1B0d04Rg1I6cRdFKGyZo3lGZxWVmEKDb7UXoFnynGPehbZAkJPMICngCW2V+p6gmyeiHK5JLBKqB8kxd1KcHPeYmx2iLRBMasctW6Ko8eW7g0u4RUJDWnRmDEBMtMmQWiTirdxBAo3PEutDfQTuICpKokDdHehCJ1f7PichPI3v1eWmzege397gNnZZTOFN6SnCz8Rus5jMzPQSYRm3MISn8hn0HhpUUPnwCkPKuCXCzPSYzFiAtdE/w9alb2YlxLSxe4N7fJvQMSNMHs2gouClH5w8wZr2lCpOquzvDtp8p1HTDYT18FWy8xZYnPBxbZa46r2NA6ealfd0zM/hGueF+XP4mQUWJ/yCdrzTcWwV4oQGZCNFG3Pc6VvyRRUbgO1rlQtcgchHaUQaCj5EA/VDR9sB7UHUIhUPOuEHeHV/y1t30sVQGxC14r1tYTXObWYbCBsQa2SV5b/Bn5vfCvCXj6IMiEbK3wVmEHlIg4ombDgKGgqBtNuETzFZCAOwvf1D//9fVcWXtskAnMtXjkX/+p/H8b3dqQPkvj225Hxszc1t6i+i1sVZ1+BJ1R6AeWndLWjfZWBulblNxdccjr6hhMTePY3X7J0s7V6PM6zXcyo/uLh7Q89H8draD/5FtWRybe7sL6u7eePazg+zucM2+lms2xnBvThnPWYMEFnwtQ6MBzDKGYB9vqkrAWhD6QitQzg1r9vn9Oj08b6Hj/UABA8e9B3ApQsi6kuaB5/9Lx8OzShPOwGoE4hV3XF5Llm36kI8oTlOlSl1yAGJKyRQ2p3ncqOHwjMeLBBe1TgDljSs01PKHYo2XKBqPaWUtT4uMZ9LlAOgdo+Ufp5dcXHigCfgo/UWZ8A8mt4qB5kptHIg2c7LhemOzmZNkbKjPwfdV+6a7HTqExDd8JlXh5fxNZzKMv6ojjd87sWDfd7FHHdE3Qa6RkPxi5onKSXEXOfTRdInuIG99AENw0v4XK79c19sKTzJu50uyjIQ/b+nxPT0PEz7fRK9mweo7dxk5Q7XaiPcUsDTJBEFJ2CWNGcGmBQXSGZII9wjIufRHlJYtAVxm2BdwSK3A2oq7YEcGuUeSZWzX2Ln/YzPlrykPiCxEuh0PaR+IbXH9u191QNjtmGwOWGKmiV5RX1Y3rx4ueQB9TNpi5m4EaA61xZ44ohfkO9STAunckaRQ7lpZJ4pO0hY1U9o69QyABjOzvKVXBGccaZ9MEQLbDGd21k5QpCmDQZd3z548jJBRslir7KD7i8cM4QWs7AhpThlxQ6vMrVIJ2Lej8/dEG9/3xz0xNNUbCWVn/fM7ea9ZkaFcT0sJY2Xw18F6zwRj9/6U17IN12dF7j5AMalet26inJdj2CJb54zs951ISQvbPs6+NUKvvjAKhkb9aZpa6rIb9Bf48Bcq9O6ghOoE3q867i0vfKC8rFvzs4xQYj+AnUIrohmNiju96B3wKmGIrNzuUB9JgTNjCbKD2t8YcpYKBAAfnhWaBKTWE3oxGcIMZSF1o9WDPlF048VmFn1uECdBcLiN3mUIrZvl+qZn1ATkFlvQWqXdHN9DsATS0wtyILWsxSUVH/HQJkHNDIqeFu2jsVIKtSpiem6I8tLSzKdmqYGNc5NnsSQnicQoiKzd0iqfz6vBmKu7ytdDo1Bmp9IWerrLUjdAjZAWEpKqqcHngxyy2oeOdHSdZ8YsbRNTUt7BCbg17v8TeRpQ3J7xoexWnJg6B2Xt3h1TYgzBADbKmYV/YgiM+uua1PRX7PaO859Tgjf0bV6xWLwtcQNLTlbqLBxhP30HXEWSqVoMV6oUm5yKFWqRGKFsky8tVjkfKYqVa0qlWUPSOkbnuGl2TdMToceFnpGYo4CTg0h1TQRE7q0cfPPPqM/0jzNAOi+73Auf3zMsbrr+4/k38MTWGhUhu3KL3YEe91MExHgv5x5VQTnOtmWM6z77tr/ZVg/gt9/qLBlnvsMvjHVbKmXbabKn1UIqMKVYbKGktoSBKC4ynzHUQFr4/E8l6Ol22R3JNNA6BypXy4EHhRSltc0K0rep/26/PoAqnqce+DdK51oOYj/49082ZiTIbzgkP1Ud1WDGEL4PeL5fsJlnfs8nwve5jn1074ahJCunGGEYCrDGLvHtDH50PM+H3tO9wQ5GCAVC9Lt3Nkmu/C6+ZfOnuBafCwWZNtsRZyo5g9C4hCXcJ2Q3NkxlRYOhXzShnS1Mzy9X58/1tLS3LLl6rzFg2KLoEqbSCAgUDFDgNKizqrp3KFE84V1AfMCXnZ1+Vx4ZIWdTgeuYwn4mrfOX5CZ2G3KQY93naG19o/gE1fMYOz147J+doR687damltagKKCRE5TgGFajievh4tL3oTqrBh4M17SU2BVnhUGanZJfkmifKUNy4UFKxjbiZ7Zd2LSK1lk+Xd3iPTRyt30a5K4mfTtrz4u3Ph1qmXgnnZwi4QRpxDoc9qwstBD+JkE8lNyvxzyLeP8/UHiB0lDOovAKYUI1CfM8BL1AgQp5AwZxnrFw6CPNPmuouqUNqR0N/zUvkfe35WbbdtbSXyi6HSWdN5dIM4kkFND98si3rKOXh0g/wfDpoVZQfYb+nQWZNUqSOP5BRan2MuW58hDGEpLM6a9Y1f2PDVkMeGChOtoiIgoy7vYTGdDRmVlRtAaVJLRc+4MzVlqqOSNw1Gah6g5stXb1t0Fbx7wS6vNAqhT6+cQP/dLqjUB7FbqVJkcE6K2ynNn8vNtx5K1JqZg/AAf8qQLsn59gejCO0gnmWndLIgfEZC/6lOeqdW8IWlcjTyAupKLck1OQMILdGdrCMTXtCRScxVJ/oSrkzCwySfUgonK6w1ADH5UBWRGZ6ifFpxYWqSLpTenTBE2CRVG9poekaE3iYOzRsFtE5c6K9NvO7q+xOGo9b5E2WBp86YORs9QdE6VDacimvU8mwBAJI4XVKOplhs62KFpgXYw17nD1ypl5QF1TVCEtKLEwLB8GVAVmSWlJ9ZKs5XRBVTR4QXiltmN3QJZgJ+1Y3t0NII4DxHh3ibUAeCWkOWHifwEbllEkKYkfPvFLneame8iRUe7SAQnr7nS2Kw5QlYdRqV2h0kB0MgurxEPWsqKzSDbLSjsQZbeZEsDrHx3cKMLESTQjAlEzjYMz5YQU+ojVeGvAgWiMkXmJ8/6FFb3vkYicHhYY2u9RMDfZPWu9XQM9ZVl+7Kvc5lIYM2GpnPAyIsmGdzFwAsqQt2rwRwQiQSE3Tiwof5DnqD1yKz2uDXJcudvNfKQtdU+ROYQOMcQp04BdQPxbtIbuJ8LqBvPnG7QJv6V9Ddo/SQ4ZS56ozjF/VDh+zK8E+2CO4HBkAF3d60Rlqn7YLJ530CcTk8Rguf1XdJbeEoTaMnJubOrpvak0zAESLED10b2xtuRA6NvAXWuCe0uLogLmCslB7yhLgxMGDfJ4d4hB0SHxvIsTwK1YwE+TByMCJIIPzNfaf5pgtjRaEbvsLyjka3k0fcodEAI+e9hFNt/ujFoT+am6meKaWCyqgHY4uucTH7ahv51M9JzR3n5Ds9IgIr9zMp+3f7E6IydlWV7PKIAyvd4lUEes5bTPGZ8b6G7X6o19MkKC2V9BgDDhgMBBzNneOIKiiUDnwm9uyW5iVc0FCikNUH5D8ixcjB4fVitwj3CQJA02Z1/u3kA3DISHMGqWjoh3/3tsqs0hh7SENfsRcn+8jvm81IJ5hPnt8RSS4ksQEsC7FrdYeSj2JQjOZ0vlo/dgPx8VTtutw1tAn0fGFaEADyO5PrYIE5dUt1f7iUaisiNqPEc59hs2aBtEjzpix3eleFUS9F/8ijhoojs7QG0jwyr9Z97Wc+y/zsyJEm57q5H0Em+TvZcqFyvjenb7Rle1O6sYYIp5dYMOMiSAX6YWhlXSSWW4kBvD/gwMksAZXL7bPIVeagTyTfLGlgpGMmhOFgr+Flt9oSe7BaH9l90RWnzmyeYs9p6JUmdm56zCBvtuyojTG3pD2tkfywPTGcUy+XA/1X8dvouSHg8BO44GOArB87ebbmfCbyQZyDmu5K0RTgw7rRtEkwMRp9xugLNUEP+KhOC4DyeKjzjx2rQ9Yt/oF3diLuWbeYuyBR+mqFwTacxsQme+pmNJecuqFbSh7ulF8KtNeEe3EsddwVIAyXq4gMU125KSaWBnBE7/3n/Psg0jUKD2Fcl05unA54QBQoxY5GpqQQGLVrc1sYwbakENidOBbUXHFOemruFw+P/esLmnDEzrTfia/dDk0HBTU+MBR6otTTsEX2H2vRkDBvz5D0jra3d1Ti5PeWYsVfvZNLI0/6hA+4SnVq7oib0wLEGS3/CET1Znqezzd6X7lu1zIwJYnP82u4t3t7EZITmS75YnywlTxDdqR6s37IWFPP3l4ocVzyaKC5xR5cSiQlj22JCWXYUrPcO4wc3aFn6u0Aw2DxpW+8xLq4yL1v55LO06g8gdtbag/QEFpjg3Avh4fWyxKvWvSdro4tE+/hvBzrvCqNbk/qnYGotyxpQB0jBCxCqZ1cuM8OBo0QztC7y6AIucrk0o2OxvM0GY1KkXQPCgDNuAEpMVVvtVv6LrRQbEjFoM2LLL5WYG/qliJsYxQs2IgjlEb0Osnf1WLBwj/ACJa8ZOkKKrEcgInRQCjOv2GQ5WtcAcLHHeOHKuvLZIv2WTX1uRVHOXzkXOqOA0voCznBk+0r2mWBEkP0m9ADkCGitwundcCs3brQ3d9fXdwMLBUWR6YIhISN7sOtyLKWqUFEApeDVr0/TgKB6cIZPZ7N5TPxNm8K/eaZlhrKqrcgPiL7SB7LqKzLB4nPoix8g2kyLIM6fQYpttE6RakoTuMIlUKYbnZGyIVxQVI0elUhh8ihj/cNEWOuyWNd9D7BJcfiVqVQeGnRZLSR1m+VBtg8gC4F2G8iGNQBD2RAFBgrePRi2wGeYgs5/wIdBucY8P/TN+gnvLIjIT+duKkTDcg+rAZCYS5sOi2jEaX6wk7+86/fIDtM4+aFolsrBBC45otnRP+rFkhuWmsemoyw3xHLR6Swyjzx77+yLSHZy/h7RNbN6b2JjvxiCS1S+0jw3p0qYKHaWdQ4TcYMjD7YOz/aRInPkrEqBOV++bfWaO2toMLNYz0J6KxTY4MFjRDoYO9lv9ih27HKh2N9PfEjv9T5beAY5OJmKwh/veD1OB7kjLZwx62NaYkeCsQBzg6J8m8VnWInKOw6gL7a9mkF1h0zNsWyWm3/XvKD7+obadd3HHFmieW4heHHxRIDnlqhpH6Mb7UO+njnNeOfIvD7dLjjKCbSQQFXR3T49pyNZ5Bu9L8faCrYcu9QXK3b4ZZj1vZSeDexX0J50dlUhrpVOj12T0BZ02VbPcpOpTrgZzBuF3URV1UPLPbjJhNbaVPvbYbXrPDhfMCbemfrEGRfQFv708lWP+bzQSQw542CxTJDimPSFuEZKZdK7eie+Q3UE3KUSCClgFgNAlAhSw8oz9WyH0xEuVVLK4hdonkxkgiyWc9Bock/bisfTVpKXJL0C6vo1pir7VUDrqlQC45ArjPVHJ6sJVKTkZTy2avnpEC+icaeh64ab73MJJCT6Dhppl37rmKeWRinnyyJggWikdacMfMEwCQlnNk9pwOFIthqNHeqETrlIw/GWeKePlYBiWVsTfUBNCcM238W1zl/epGjyV6jq57sFgimdUr1D0NAmaNTbO6WhJww1lAs/YkOZjNj3WxrMCpMM5jmUmyFEfc6bOMHlfexT0W/8iAfm6T+qrp7zjAG6kj7jx9SRQhpeDl7YISOW0vIWYQLy35bB4WApgHkxesjbAJaDROCgYbIzk+kE4Mo6PQXA/1C5cdkotC+2t1e6RVqCKY6duzA6JWflQ3PSyJBECATIp7So1kGTMJEIsgLdPTIlF+QSHwSh0EeX40bbEJ2GKS4YY0NcCA5u/uGit66uLv9D/rO6Om/dZ9sGf1g99vWt58Rz30CmdTTtev1vrqRDkrphNiXJKbj34Pz8MDxxLJURcB6XLuIins4J/RDPvvfskFTme/xGtPKThKzZLUaNJebkSfIdn/ysjw9kJCoFSvjes7Nv0IJveCjt68PlSifMpuSJFNw7PD9/EBxIh9+IGlE9qisUkPB2FbDACvx2XRgw2f0r/LXm4nrABuxaFTN+TDxgL/4aj+Xmrl+y41o0gJUwK/Csmf7S3cb+LFMHACY5nWRBk1VZfqZ4XPwqrDNYV7A0dc/9GrKgKKtjER4uBGG4ECCnZk4Qsdp5WqJnYtoziw51pXLATA7/g3Q7/Ox7Jz+cjAo8mPssExkeZV0MgwEnJsZBEmIizGJskoH6ewR4RUKDj94PSBCN3kGl3izJBEvTWn1ZUvQM5R1AnffBh2AkuPwZk7cBOU0iAGQm+LTghGUnoF86+2jQAKaEhf1NWvwkoUKWmCiBAwLGgVHUvlUydonipP0Eem+SOMs/iINy2YL83N4Nrka5rLWovNwV6ja6Imy4PCWznjJkmcMtpSyBWmwr4ZVUh0A6jM4LWgY9xzrXpIaGwwALZqyB1smBaQ2BioHBYHBUwFdNrLPQ1mDV9u67LabzajvN/dazdlgqt1tHGH6PM6zOL+Ma/zZ03/YaCP0KLEMmFNdJb0LpchiwTwQQSlQWKB3MCpCnTSioJVd90PLjPsCjOKacci/j7kI0a+C44E54EZkn4QG8eKGw9r4NOxbgcEq4Wn2QV0aOmDS8YGNT/Htx0h4KAPFTKeNWeXuZCdsuBzwq+HCIVhZZRQoG6o49JcGHgRuW9GC5C4Dev22LZ1ffnMqndIb8f4EICOn0pJ+kJ0LlVrQE+G3gpcj51tVkgNy2sf4M3/yq6umM3VmwHMSANtgXawdeRUvUDeaAahLadBSSAeCaH309kL78dmDZluSyMkRZp1ABCWJuCTAIo3YzNliAQ/L1Qz099WBIGLYJaP8DBwn5hNYEGPEV7di+N3vMnwbn23ufbOzZ2LexsSFnfJL6ttfcKIwdC85LtXuT+qbX1BjjYBuwtyaIXY+OeRuMR2RQKTr/l9qAMaOKWONHW0pA50WJ1m/F/MYj//5rTeOPVDzQWp5qQi2t62NkmSZITOvUZbs1krAReUK9wCaouybw4/EZ0Xup6CmDnG9v+L8qpLErS6IDU5nv+z11wQ0/HJdBpcU0K5mt8+e+TvlVlKW0IMGflbu5L37gMGfdhPJb/OENo0UMIW7ctP3KedgOrEggm268sj3epEkBVlV1ba5d+aoQjH0hBKjSpMe5U/Xtx+wPR9agaO52fNojIKj6gRNv+9Mdtc4I1z2i+1yNY4dklIDDZfojXWcwB6fBq7Xs6sFWvLKHAHCPvmEDlD4AA8FwbQSdFG2njMzLCcjEIXv0l8OvP/DgG4d/YewLLE74uJJe6RsPPHDbqL0cfZ15ICesifPcvj9yXeNl5AHjTDqNIMflxrToXydu3xuJRGgJdNvk0peDgZCBjX8RO6rj/Fl33IONKaby9kxTW0lhYXhKpgaBAC8ocl1jrdz7t3XZBoEatzw9OMt8MHQjyD9ZPOhnTmTFCuVqYSwQ6bFFp3e9WkyGGtPSRKm31PVAlbjujy6JH3FJziXkrv6q/8bfdHr6/N65dbEwInpBUxjN58FfjQWuZwIAOqm3ci9eDGBbFbfuOVrUDqdh/j7JkMGwYb5asLTw/Fpl9Gmd402y6PRD/s8swMllds74BGduba4zId5ZLZOFrUyxllWjfEHj6q10u6zbTKSMUFgYM2W76yGsbOuFBfaVOEGZirMkFMoCdbep3mUolPPDvU22ONDKSj5apIiUA90RlxEbqc7Lmw95yUFNVuPTAUhAXq3uX93oEL0wz5Oh84B69sJloiMrhCrmVBKftI6WfrMIWdrLrGJ87uExjB5RApM1PsKiojXr5vU0hf2T8ZBGBtEygy/MZ6cPzLfFRwz0sWV9s2/vpYX0xhi8OMpt1i5O1ueHl4T0Y5u0S5QIPs3Nu1aGBXlulqL8mVW5/Wt8lWA+LuWyYqGmuh0+IBhTWQaBoqvXIx8//vBw/4tYybO5bDlYVP0wbHbI/NrQDSX8wA6WnTvMA93kz6I5HFnA8yP9gbwabvpCPfLKmaAkNIWlnE//4pgtSfBIgkpr71L83NLSFwaqt45klvTMyN45Np/DJTY0M5UQHSKRlOEmmiz1BYTg5Ls5XuTIXCijRC46BY/TI5ALZM8MI6AzdHu9U3Q+G5dyB/zbDTo9B9kWZMO1UXzOZdlaIyYRoR4CUK+m1EsY43YNiTU/ZLeEhqFUCI72lGe3689TSf8Gx9TxVwMNA306yhh0evVU2eHeNz1KX+W4NDunsWGFa4fbiPL8cKhSG4za7dSea2ox1DHnkksqg9CYbudvvvGmihpDfeaA/rChf3nuA4mjrx4+fSJx9CVwVS6vrmGOek4vAxwrmvYYAf8dcp/xEEm03LGJj38oqPrTPjnlN76IOe029t9/21ynmWsrvg1aWtCzzX6Ydm3Hvt5+6QGANflL7IcyMutqA1rGAca32g3Rrlyj1tsvOZBhd8k2LMoYTM84yaWcsZZJ2PHcpY7Mx2Ow7DMNf+4hzApPobwhy5aKJnSQ1j/PxTXe7PwpQs88cvbtA7e/NZ6qhE5L+7d9PRVsVKcz4rwENx43fwgiIbF5Efp7s7WzvrpvFBns4OIszOaTkjCv19eeeIFlKlt+5zp+8fd/FPPXFUc4/XNniu/dK34QpJpv9oHW4tQFJi/IiWzf2WkxkSDnA1C5+GiyNbUYJXVVJ3V/mGHD/Vl6gtorstnk2RGSyfMBLecDLiTplrX+PupOlSBIqtSUQDFtHbL4AQPj/6zGD6DvgFxEiugH+AE3k06z7jQuo3BAPoDIaBr3afrFdbAfNKSQDPqOvOfsOHYyJymW4k0FpvwG1D3r5BCLl3+51+5bnNaS896wq6aub3Lq5FlQnAus777sm4cMz6sdnzx9LjLMZ/bnXrxsbPLUOczYjzktl67e/vMvQO2Wtf98PVT78q1KL8bFt3fZ5R+oBmtfvQX5loSeoD4YHDbShm4PyYgeDnT9zG7TvCwaBsxsFsVmBoz5KJVhgvpIjZk3BtWxIqfj67qCAoOCApG5u4Yk4TEjzXNFr1U7/9wNCU5qW0ha8bWL6oMJ9PrBfMYbk/XmRk8QkFMHgAoYuJd2+r6pD6W9yj4vBaSzCbUAlxkgqD+9isOHAaHDWTZQ8Ib9cw4ka8NFWUfwU6Lz6BhvEj2zqj4kr+1dRl/wkPud4xUToBn68e6Wu0/Xjiz6ae7nLbVB527WVIj+wrKvXhild3YPJD77SdH7vRyM2Q036R4VPuux2+u0136PE5hX/miXj9yDRAACAhGBe3b54HiCziuQ3IaBo+Bk8sWoa4Ij/yjSq43MDqsDxUcMdAQtYX4OACcNLx6I+H/tAU66ibUPinVVVRazRQKyUnawLSUNNGHPn8wmsCAW538MLrg+LEK+PWoncJl2j9f9ZJNe8+HV41iQIc2CIVkzfowrPc73TwXju2wjN1q5HjWvpkMVlWySkIwZ4/zWTN6dLFu3UY1Xa8kPVqGbPZvR62VCblh73MW2MJRmMVtctC9xOBz6n+hxS+xEkAlmQgKW1FRVl7UURROqgCRaBIwhRZ2QEOlurjaIssFYgEWrTCZnMufuO75tHmNjIcGo1Ew0/FqbbKQ2D2O3ZNDKG9k5URuxNh7UqbxAlfW3lHpXwD6AOa/x+Vw9KklqIirt3rDD9RPvsPII4nuOMkjGOunUK1qFDhmxYR1ltcy6XVR140tGviwMEZWrgqzrYsSJE+auoBnBu6pMnoUJj9VSEa22DZF/RMnvm1C0641QmaKi2Mw3tVWmm9Wupi5CJCgtv8W2vOkFY1R/32+/9rM1FE2KB0J0Mn3A2m9Z163JFP3gymsebV69vLPEopRXjzmO1+/s2VgWHCUhT454G3/cxKdI2lmvkoSaN2Shspl+A76L0u55xqBJJOYhtwwSqGhwCZtPtPhwk3UkD8a4RA2D1XPUU3SrZP6g7WKS26ELUrz994iqCvnvSXtVJBwhW/CnnIQAmCTGIzCrjyjhJcCFPbJDBoFXwmEwQmurBA8c869KIJHscrwlViV3t8lC3Qnl2r5MY6E7H8brqbgvseu2sYXPeeVQMG1GEu4qLjZnGwcd6hju5P3Dh5lCRuGc9OqUDljEAbQ86LLOD5j/GHJ5NvaCs6rUZ8WvsPydSY4Sh8g6B/7bhKNey3nmipvw81P1k/XLFDaYPWkjJrd7neDug4hcUEBR3pxKW193VUSZQ4RyRXA2IxEjmiqX/dHpfk7YZ3nYxC8ODCwWBxYWB4sTrK5YMKAnxetmBCsv2jbUgAkg2di/wJtMKl2kShb7+YC7TMAf7S+XDIEJ97C3WRjgTbArpHBdZ3ChagDZTItuDEfdE7Mp/asIulsmdS3QpqkV4/Hz2jRU7VZrAAULeXZ5k23AumgXoDiscbqF2U0gWCXuvksjLD9LgcJZ+uo+j8KjBzOEbUGbYH4Sv/8YeACYr9kzMfl1pqfvthAhagsQ9+vP9FXj2z17LmzFHA7sCuvqinuQBRJxIK31V5CG/vr8tDY5jTseaP5xTkdO+VuTweXyTr5naPKKJsBlnfr3CZ0iVf6nxPAQRxK4RLxT0RzZXxpfSA7kl+8chpVILrfHn41qZjGNIeRMnX0ZbsMxVZycZKCoQq5Zo1ghRRNga3BD2FuAP0brT95RLQnMkIa4FHrJlhC/kO1qr4TxZ+HViY9q8/XaJLpCMuH737jVISBrAYoRHqqr0G+rAOGiwrGBXV+hr9SHqtBKBjBFDmFKBXlKVZ5AAiDZ4IDAk0JR4DqTLxADg6JLMtiK9tOn6w5GR+PFejLS9Yfco+X9UFXivKdgQMfJdVQAVHCdPIcH4vqzK05GLzLZ8/RabbYkgESbI4utaHSBwzYESQlvGMwv+RjOTwJaduk7Excn8gatsgH22w/511MqShJHuAApMcQBxm5utCovSRriHgo6xLVwiuEIRlZeHiqMVCmuILVWnLj4JY7E4Xr2MOW3MhwiQiwDNHC9BzgWmKT8LijYdnErIKRMq3OU2gCsW1uz/+2suxRWz7EpnTa9L2e/6YMDHrp1vcZRXj/Akeiq6sknaPeDUiKfM3VlfbGkqaurJW0AIhnkXyXVa7M5oB0O8mFyGIeSI1EPDz8zopynLEAnPgw6ZJ+/0sWqJCoaP+0YWk1+jlx6x1zNn+FPUhxtNXArFohdAfJvWtVeVMJj5babEPMEAKBDyglsvuyXD8x5fZg7FasFElQ0MU3FBzgTnpOxo56k3M9VOwXECxVQ2l1rH5einqmr5NET7y8RZzsPGkva29+17/tO8Q3l3Z5UnIPzrrmAswaUP5vJ96N2wGXktnnmtvugcAGyx0nlUIFOQHQcgIrvlyunbaIQ1xoBVpFFcZYiJxyv63eqtPA69o6hcHiAuE4aLibLpdsSBgLwcmyP2r+Chaardj6Ru2FUQ2orAYqF2sukhJm7SkLaNBW1l4k1nV41eMq7kUNlPpEkL4jrvhE26kvoUkK24mvvcLGjbL3i3VDY1k7iMlkIlpn5JYg9OrzTVd8CufK2mV8X2fXtezyI9n41qpWR1xLtWvzliOCFLZcMrnn9u8tFoO2jybm5LeA/eJXl///MqPAK2565qUnwM1f9w/jEr6i++67Rex72Zimz31uMn34ErPKpsfXuUWZU/c9HNI0QEaZD0x4HFomIrLdgWCi4/4GoqNcG2xYDr5fjgxdtVOBjAzU65KHbgnLUua+XktQq12B5r47yjoCO/oaB+5xKeaW31zvlLrGynF+SWLnsi6KyHDQvgHd6e1NtvDKh+ZxMSGZ5Rj0pM6Nkc8iFtiAPsX5c/iIWYYYKXXVeZhJ9F7osDw98Ce9NoNz+82Jhk6znIefP30yEWQH3qNRDmOGxsYV5KRNOrVKPYcZevIBQEAj46py6eJvj9BFyVpKKQSWYbpVqHWadmG8NIkX0zRW5CzJceqJA225duIkNuENg8t118RgWt7XjimXcTIxSEQWBPDU/L+miz3lL+MxZuIDKGUk43ZJHccJ/3SoaVqQdl5B9NZF4DbQuIqkDCN5X4wW7dV64pInUk2uqlKz4PyQA+/YZ6UvE7NcsgIXxPzbpxv7q7LnL/rcwjRQ8YKYfbBQIdFWWEMQEcYISRzUm1vmg1fOVZluN6GymlFPJ3+3/SoL+dc4PhYZaS5sBVr7eN0jJckwpFtYdMvkOkRMJHDD5NCLG6y8yk7V5N+me2j9cbwJIqDtaKhXx+HaC5uvxgVZWga05CWUULZX8l3wRqRDDOrWHC9l78oW83n+1T1XOhnrFz1SDOgVAvfLAiM6vNINlduqA+uglZwQB708UXRhSp1RS5umFajVaepg3pUJWQUE4qNXKqRVJDK94O10rAJhndsgclHlI8kdqJALMj0Q6Lm8hdSApldJKCpJSIa2oKQnd9uk56/mnbUSvMESJUth6d/8+kswuYy/ft47V8cPpWpU2NIy/6POXxUqQD5H0ESaQQik7EB1vPDul36ZUG6TO0CRlu9jplLSrwHyLmuKgDuhr6ZpQbRjAQESFtII6oP7+kBWRQPAjcvYr6CL8f2UgqA6pgwrar96lNuCaZsDtQ9Rh3H/12GHaMHawHldxcNtY1UXx49VSWxC0Wi02iD7cWZ/Q/OuwuWqrLeeNpP78Fu3yK82qxW6vbQNQb7VomnvNuCjFhUAmSoNzEnhAuInrbcUuic2ZZxy0Vf2zocElBl//wUNvyyte77KeEBKxmdWYlTPEGTByFNZ5NRUA9DLmNv6FN2ake3XMRWQkny0B9RHt8O3elyRQIzkzNh4ezyBnEoxa0nlodliVmb4g23LC28gwP5tDD6/CzzlZmjU3bW7jn3hsGi206i+8cVpUqFJvggV+kRkqqPdeGVollpCDHUnJzVh/L3z6F0Kjpi/o+GK/tzsH74jIWJDpHxpWhZ9TeqirJ1j6BhMJO012Ip+HCXx78DOe9caKmLXyfV7aWPOB00puLgxH2hzfrUeWtYSTOddbGP2vYzMvYZcCOV9JZBXZLyH/RUxvdE9Ud8xcCwgmzeSa9Lpex/ZmWiVYuEUfpjXPZQQGmKCPnTZflGoDZF8+nldE2+7RuYxh18X/s7yfL0ieJT79/3pGxtWoxnQh9WqohshgYcXv/JLb6tEQUJWhJQy53pREf5cxJW3I+fDBUGPlPMCQzBMwYdeBE89J4R0HgJq6MrIb8yYqAl35c2ZJHtow2akYDnc71ecUPjRfeQdVMxcwwWL989Ir9+wfZr+JMfE/Q4mEgPkINCrgLSOeDfKugKa1l5Ngy0gswAxj8CpJEFBqgj/DDy3KXixlHKDasHTv/08OttZL0yOtus5a1iplsCWAti/kzqalQxn6VV1Z6of85LD6ibJHjmcKhoYKzjg+ynpQ9ncrQHho/vBw/kUFZEgLp8uPh9Zd7z/y/VTJQXxEiNUqri2oeB/llxU1xo3YU7tFUZrhu/8O/fYrK0PjEVBtFPzGfJvxiKh1wdwb/YdiY20VCGIPqeTUr1PFFmOWppljihW6TAGRDbVUkZD+P6VDuv9mTChRM9Th2hlcH20cl/Nz6SfmjjsbF5hLyyRjt2zZ3IsKFKFw3zwgaDAkcXdd+enRdX2i4Nh7HIDHtGOs/54VMGZ16l77EY5FJtXAECiVg3VTvvYH4GFrCfB94VRzmp4f1eGAZcmGxjcTFxg9RSi07dFcx6isDc/6n7nQE0z2G/BZSQDoIKxUGvBrsW5uiVs1urjA4pwNiH3h8Cw9wsHBR48AoIhy7dQV6SnLaJPhAP+ChaPvRJQSWQRlAElDqBEaqMyrDCCeWZ6SY0/Jkuile8KZzEF8kaL0LO6Grif9kDQKQvE1oepQDf/kGLJO1Wft1IDt724GhlSktDLTDPLAc6j0FIAMPH9GxBA7r8XxtNPp/UqAMKy2t1RPTVa2eR6iDuC6j3C57KLusIBYMzYjaSCBt8CWh8A83/ZAkN0bIJla/nxtz5VEfOPiu9Td/entTgZwpJpfqGk+taw0YYGvd/x8XsKAnREgqYLKWNu1i09sDC2/01xGqKAlZTcO2BeXxS/w8kuYHx+vTSbm7LySJGoF0+3HxQPEb/hpej6qku52Hi171M7PK34BL37AHm5bTmOMdcsJ/R0aU/LGelQrqCOzXfF1pPkRBR+HE7Mv+BNdnO00b9RPqetdoIEThnOjAaJ7ZaoPUsKY40zeoceJ2VEsS0dygM05le1cYS6yLvRT16g+ftTFoJy50TuAUAeEfsAUOKgC80giAahf/vVjR7BG2b47an2TAQWh1utmOcJrgZNUF9kIAoc0rIDfzh0mi3PfK4IRrHlbPIt7NFvDL/+5FwFPNQCSQcb74OL1Bq1UBi/KiIuydltjbodKu6zhAdRSJBHf2p7VNgQlnsvTc1ZNI3fozwnaTiuDF2rrdIRyHVOWm1Fe6WCgRhOrYrJMMVVJwkTwZ2VVRSd+AQB4ADkpoqaSP1F/boVs3AYWVyHezFzO4IZWdNp9mk4bA2Q7yI051lZkZ8vZCpd+up09YSYCExNed3ndTJAX1C3VLMynCZNLWivtufGJwnhnXGVnWUsiYCRW/ZmwyACpQXyQ5gHXEW/ojpnMjC6wF8hgLMWqX5nGvu7vO6pCsVN89WO37VykxijTfY+hBQAXZy8p+76xn9SYpPyph+pLKX/nP6ykW6iULAoCsEoXmSm7S3wBqCpqaNwD81BmIaE0VkQoZRbEOl/p6BS7hsDEug1bSvnb3cKKXc/Wo9smYo6WlGz3WAVYnI3mQjdG+X9hWptoWpuYHdQ425gCxlmnVv4r5lhrzA8T+4kzpMHLLU7jNKQVOLMYa6qtBklLVIcWWJzmNYQrUkjvXm5xIiWL/RAjRXlIcqJE0Ywi5WMdQnN0B63Ht2OhtNPBCHyTyEE3goWq/OAZcfnChscEBvu9CrMDI/SrwgNuvmVQy5d7DJL3zpH3efyWsJdB/euymmFVGPjpd8Ts4aMmc7UAhsjunw4fgoe3h0f98FWYyyoilInL1afpvleNDTrlirBQr0rGwxAVDMrmFWa6MEVLp8hzb9yt0N6TTf0uZbG9bTH1CjUHoHF7vSyltrWlHKFeyt4bI+uqz8u3pUw62Ghnz4Wb7g5u5/NHgydN8obeJx9ZTH7f0Jj0fnHxfVJTqFrcNbiiJ4dOOkziubzQY/bH8Dy5PX3KfgoHCPUejS3fXefyKl4CMOcSdTGloz3lCPViNpQlXEpZJIFQZ+MAQ8RxfwtPwVX7YECOtOgAkgMgW5xe0t9S5gI30OK1difw/RyC4FOMC3Z81mxqUMos9OgYO00tEWZsuu61QrEvgfTRz0BQfGUzQHBP/Vv/QTfbGAChUTKhcIb67u+73jtu9vHN1QG04R+ijfGn0UHk0yCVTPwMqWyCwZxhVBWYQLGoKMCsCgNIBsuTFFPKfpM6FDdcSyXQ1OF8qnb9ieMxLwj6Dq5G4Vf1cOqjI2MwsUTMOnuGG3J/R5WBEgscKkUurzSXp1SEOcpZjgCPMoToRUd/OHP0n3eKeeJH3zususd6kZE4bWXsf0a6BI2k3KX+HzAxh2FYK1lFxn9qNnj6v/97KUpysK/MW0mdr6qI1m+lERlfktBIeHvIazhB0ap4m41dPByMvHkgTR97SDqKvOpUltYYOOB2feX8g0yNI0guAtqZJYb84W0lwn//mciE73dg+FApw3do3b//3aa4UADwV7JKXzf4vUntTa0I2hsY1Ob6A5yl/AL5NW99Jm/X/cGdfy8xWAedWgG1Hb+w8dhAisHb69GbTX6IVmbL64Cgc9U1r/K9T9Haswg4PtcjtDWoTlkLKoAax0T8dsNsWX7HL39HL4PXCRLRbwWOg/qxVCYMVco5d1tRjiOQNv0MvGJuNzj8giXca6VnYZ8Azr6A1rj+ayAtcDHcissG+hEbrjvNaE2+NqtQqk5oWhTTEXBQJ4utE6/BGypsqhQAzsY9llFZwQ6sW1hMPoooOMjD6iCzYkdCVwarMPtLaGHhRFPBnHMsR+MVsnKZoz6nB58TfsXHhUg1zLesztV1++iyv0STvGM4Gdf8sZH0Zjr1AZFFJUAMRAZu3ERGqP0V/+51m5AjtDfsvF9k0NX0Kz07qXKNKMzvDzcd+fHAgV6if1TYlb0M99vtv/e4g5jhGLXjqPwgPEBmUZvfS90ja3n6LvkVDS3judNNOclXZ3+W6dPnzI9o9e8zsLF10lXYDI1TlcKtGadEZYXYMfmaq5/97vAlQtCZs6qoS3af5EfFRmEDvAMIZtKWQG2EBi//q1e8a3tczY+dspNrVi2F+/+r5JqnAn/rBYKLulir9QEuZDZAq3GHSWHxTGFqTZhbRqVgVmvLtVKAD104DWHAZbRQqtUIdEDYDVpwFqNnQxmGxg/rAUDiJ9+q1VGOBT5Bwm/3G7ddKr6mFJuEBEvFQ4qCqdM9N20KTaM2IvlrU23lh6/eBfcYT9TsnY8/51g0JDZUm42rPF5ax2238zlgLDRuO2u5erwgd9x3xvQAYtsCixM+rgUCfs9d533cMx8351nKAVxJtTFZWbG1yZbk2Jqs7ElOiqk8TTcXc+YWb/3GVVBhVDlPzaUqwFk4N4evYYyc/vg8CCDo+ekXi93x/AMSALAMjhAGAGaVhi2aMYQhOmtqwdI50cQtl20NdF6Zk7HaKefp/4lGoyyPJgK46LnPmxjn3RcAXBJh82RGY6LIwdnn0CDMyRE2pHinCuu5v16YCqARaywWHqRRBfL9arr5Chd1MWuEU45Pf7Xun6P2KZI+9Tni1TF6YMsEcKgAmLVKMIq9D/BDKRYf+HNT8uTAJBZI2Id6yo7qYZrZNQH0dUCOmMOJ3sbQeNIYIOKOI73naYR8vRaDJG5MkUANK8wsYBZowECZJ/hVai4AVoZDg/3i/PVFKW4K2AfBbluk+/MdvT0jcsJjasPwEWg75lrpwEDcDdl22B4iL2CfJGpHIXWLwr178hOJhTulmR0XGTEOXSQ1m3WxemWl+g7rF/Ju2DgJ3Gbbl3Kww+L/6j47mv7f4p2R21JvZ64fPLie+Zdjvk034sc+ESE7Xr8eQ+UbP5Yu+Fwy/5gjDpd6jJ9M2Km6tCx+/uHAY7pBAgDkrpsmI0IBUp5mIvQtDzo5pt0yvcTiwn52rpkBcGAn6Ex65MdACCZiN0PVsKzbioQ9zz1bgTEreGTLwKtfBH48bq3qbd//A/do0tyYL61bpCCm3TwygOxX86oEwszIZAqbF5TOQYBsKJmhTKWhswdHAREdMfPG3E56tGVnIfDxqOW/UIOuWVgdg5FB1gIDqzE6kTHLOvO5c3ZucvAAlaQHwLxQOBRidhqcQi99YUOD/OdsQYcCBgxlLA7gwPRaDprx7gTYak8p8ilkI/hnfcAII4MQU4WZ3AsM+xfCHBCltfPH5KyXesMWdABV11UzdAIpQEsQVJ817rRg63W+YgpZkrAjdz2gg+ohjQqc1LnHe5Gh9FaBYKaslxHIBugeU4IVt2NGBo3xCqpJWTFwMjD2KIkCoYynWMqqeqhOfED+24Q/b/jD7VhrzNsW7AM6/zMcnLjc4z1AYDOSjtmHOMAiz/WDAN2TRr2q6d+8Kia9wh9Bh1DUTAIowRat4F+fdGH6rl+j1Xf67kvb3/94vP+VAUz9t4A+3rPdmVTVtxDpgFBVK/ljtfOu+5xBFqn+wG9RoK1+MfuIXevs5WAPO0ekXjAFjpx4tcfjPKQgufJnFwQt9FlJ6juYbQZhUoJ9SwyXcDrRPUAniZKZK1nkE34gEAYqsBRenOd2nqREX8zvsDPaff64T2UxA8jEdNEKScjzQqZ3JiVSl5fMy8m4t7lRJamCGrvHWcd3vQ3DvYeG0Hmv6zn7EF3SoeGWxSATezGPaBL5w5d8u15GUSeWgpxmrf09OOma3gpeS0PU+N7ZSiaF/sAv4GnhjQWc7vUoC6bqLT+TOeg0udNLsV5WK7l+2+8yivd82mMpZ60f8XK/+UbIHlMvu2S8D7dQtH/IGzyBbJhRCgaptVqESFOQbEdMdzsKZZ0v5NuUKsmP5aNDFb0MeghN8/xK7gO2PkTkezt7/LnrKdCQX5yC0IjzD/MGaegOcLMgZbNK/r4YEY7lpRp29H9wSv1VVPj4zJuGYRKHQOyYJT8hiUZf9AHdw4t3GS/ZcCXfk7ghCslWRe6FelAxPkVKjhBZ/13U+kmQxhgjcac1xP/FDCLLUuuZyvDRfN9n2YKc/eSbcUoYUbUeYDgMcdGba96m/O/xi8P94ERaCVX8U4RLZxSCtCADzlUYIjnlXq+UkUg8NGOQjoRHtU+JvtCChBo3SKAPIfmHXQPpYyBF25TBk100SJRIR5IR8oB2mKibgJ3ecVUnpEhkIoRso5jE8KCGtYsnNYXWVsOeH1UixCD0oQMmoFbgqEK61Cic6NazQ8MfFDhUQ2I4+Io6/cMi6jbFQdrkU9UbwEjbhSN89A66EZLQEAXZQfZCC9EB8RLi3vPKNR0b1Rt6xH0acCJr8h3Q7CkBOkIEWJto/Nuq1ktIaHnnwUwRPiPDZkkB1VgUMpYpOGjCS/ib9MGgvTjWuvs5+DPPOje62Uev5fwkdA5kLQpsgstzTSw5XsybIOFmDJWKPQZSkwiWRd7IyJsxF7Qbg0IbiFyGXgXXmiqpvXmvMdl/tmUD+oR0ohIOferSJK8JiUPam3kaJMgOBoAK4NlCNRxhKwk7Xp/YhtIs0VlArQIKCuXBEGOEiXXaRhC7iPgwYDNeJYMTSNNQTLeOghRUprI0oq39YeqhOZ1L9Emnr72I9VPo3c9QKIVCWQkLemt30zdOoXcfnaIfwOpyErSTtG+ep0SW6yvnpZ/+l+9o4HtPkly7Bsl7UHY4Jn8lkKhA9rJ0O2SxtDC6QU2opUUDrwG/YSYeiNMXvi1/TEVX39CJlVGUk2P7McD26xc2Sm/tl9h2rWDfeoJBWjydfvON+kFzIjwKKR0qUPM+zu0VU1aiXgGhhGqwzsV/VJUsIGuWSXnpFHa8Dfjg7OceyGTvYoY3kzs8Iv3tImmfBglvMAcJS2yE8lfOgZY+5hgCED0nwQBtTkYyez2lXvx6jkPG0JyKYWykZXqSTZkLwsBq10IA6+z/MFN8NLw5hkjkOQkBSJ+TUUGRp7DiO3McKv6aU2GWZJmeVOpcEAFsphcCjaVgErwvDSxmOc04WdHGnisksddQqN9uWPy/tyfK//tVng6UsZglrBTzxwz2M5AD05GbdhOkQc5lLPk0t5DJKNKB5mL0FxBKGzLI4Y4NvDST9LvOAgV/wL6x7aOgpaqCSd26u1sT7tLYbVVX/QxaulIDfQYq2Ix1+yEiniIES7+REio1FllIxEbFEfOITWoX/KwfP/aTa2D4Z8Ia1m3YtKXJ1GKxOVweX1sgtG1HR1dP38DQyNjE1MzcwtJZNAaLwxOIJDKFSqMzmCw2h8vjC4QisUQqkyuUKrVGq9MbjCazxWqzO5wuABEmlHEhlTbWhQaBIlVNN0zLdlzPBxBhQhkXUmljHdfzgzCKkzTLi7Kqm7brh3Gal3Xbj/O6nxeEYATFcIKkaIbleEGUZEXVdMO0bMf1/CCM4iTN8qKs6qbt+mGc5mXd9uN0vlxv98cTwAiK4QRJ0QzL8YIoyYqq6YZp2Y7rJcpU743kjyyACBPKuJDK1Fb23gAQYUIZF1KZ2sr+gQYAAAAAACRJkiRJkiIiIiIiIiIiqqqqqqqqqqpmZmZmZmZmZv78CogwoSIzLqTjauvEWAEgwoQyLqQytXVCkiRJkiTJtm3btm1b/AAAAAAAAJIkSZIkSTMzMzMzMzMzSZIkSZIkd3d3d3d3d8/f1b9XASDChDIupDK1dcI555xzzjnnnHMOAAAAAACAJEmSJElSRERERERERERVVVVVVVVV1czMzMzMzMxM9AAAAAAAAJIkSZIkSUmSJEmSJNu2bdu2bfEDAAAAAABIkiRJkiTNzMzMzMzMzCRJkiRJktzd3d3d3d09f1eNVwEgwoQyLqQytZV/bwCIMKGMC6lMbWX/CAAAAAAAQJIkSY4VjnuCFBERERGZVCjX68u/HQAiTCjjQjonVHVWAREmVFTGhcdxvb78ywAQYXICAAAAJEmSJEmSUwWACBPKuPA43wU=)format("woff2")}.i{font-family:"nilesoft.shell"!important;font-weight:400}.i{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:inline-block;font-style:normal;font-variant:normal;text-rendering:auto;line-height:1}.i-lg{font-size:1.33333em;line-height:0.75em;vertical-align:-.0667em}.i-2x{font-size:1.5em}@keyframes i-spin{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}.i-facebook:before{content:""}.i-twitter:before{content:""}.i-reddit:before{content:""}.i-github:before{content:""}.i-youtube:before{content:""}.i-chevron-right:before{content:""}.i-chevron-down:before{content:""}.i-rss:before{content:""}</style>
 
 <style>code[class*="language-"],pre[class*="language-"]{color:black;background:none;text-shadow:0 1px white;font-family:Consolas,Monaco,"Andale Mono","Ubuntu Mono",monospace;font-size:1em;text-align:left;white-space:pre;word-spacing:normal;word-break:normal;word-wrap:normal;line-height:1.5;-moz-tab-size:4;-o-tab-size:4;tab-size:4;-webkit-hyphens:none;-moz-hyphens:none;-ms-hyphens:none;hyphens:none}pre[class*="language-"]::selection,pre[class*="language-"] ::selection,code[class*="language-"]::selection,code[class*="language-"] ::selection{text-shadow:none;background:#b3d4fc}pre[class*="language-"]{padding:1em;margin:.5em 0;overflow:auto}:not(pre)>code[class*="language-"],pre[class*="language-"]{background:#f5f5f5}:not(pre)>code[class*="language-"]{padding:.1em;border-radius:.3em;white-space:normal}.token.punctuation{color:#999}.token.string{color:#da1039}.token.operator{color:#9a6e3a;background:hsla(0,0%,100%,.5)}.token.function{color:#00a;font-weight:bold}</style>
 
 <script type=application/ld+json>{"@context":"https://schema.org","@type":"SoftwareApplication","name":"Shell","softwareVersion":"1.9","downloadUrl":"https://nilesoft.org/download/Shell/1.9/setup.exe","fileSize":"3.2MB","operatingSystem":"Windows 7/8/10/11","releaseNotes":"","screenshot":"https://nilesoft.org/images/screenshots/main.png","applicationCategory":"DesktopEnhancementApplication"}</script>
 <main id=mst-main>
 <nav id=mst-nav class="navbar mt-5" aria-label="main navigation">
 <div class=container>
 <div class=navbar-brand>
 <a class="navbar-item logo-shell" href=https://nilesoft.org/>
 <svg viewBox="0 0 512 512" width=32 height=32><path fill=#000000 d="M200 100L300 0l106 106-100 100zM100 200l100-100 212 212-100 100zM106 406l100-100 106 106-100 100z"></path></svg>
 </a>
 <a role=button class="navbar-burger sf-hidden" aria-label=menu aria-expanded=false data-target=navMenu>
 
 
 
 </a>
 </div>
 <div id=navMenu class="navbar-menu is-white has-text-centered">
 <div class=navbar-end>
 <a class="navbar-item is-lowercase" href=https://nilesoft.org/>Home</a>
 <a class="navbar-item is-lowercase" href=https://nilesoft.org/download>Download</a>
 <a class="navbar-item is-active is-lowercase" href=https://nilesoft.org/docs>Docs</a>
 <a class="navbar-item is-lowercase" href=https://nilesoft.org/gallery/glyphs>Glyphs</a>
 <a class="navbar-item is-lowercase" href=https://nilesoft.org/donate>Donate</a>
 <a class="navbar-item is-lowercase" href=https://nilesoft.org/blog>Blog</a>
 <a class="navbar-item is-lowercase" href=https://nilesoft.org/contact>Contact</a>
 </div>
 </div>
 </div>
 </nav>
 <div class="is-hidden-mobile mb-6"></div>
 <div id=mst-content class="container px-2">
 
<h4 class="is-size-4 mb-5">Documents</h4>
<section id=ad-responsive-horizontal-728-90 class="has-text-centered my-6"><ins class="adsbygoogle sf-hidden" style=display:inline-block;width:90%;height:90px data-ad-client=ca-pub-2559766466824022 data-ad-slot=6484658586 data-ad-format=auto data-full-width-responsive=False></ins>
</section>
<div class=columns>
 <aside id=categories class="column is-one-quarter px-2">
 <div class=menu>
 <div class=container>
 <ul class=menu-list>
 <li>
 <a href=https://nilesoft.org/docs>
 <span class=ml-5>Introduction</span>
 </a>
 </li>
 <li>
 <a href=https://nilesoft.org/docs/installation>
 <span class=ml-5>Installation</span>
 </a>
 </li>
 <li>
 <a href=https://nilesoft.org/docs/get-started>
 <span class=ml-5>Get Started</span>
 </a>
 </li>
 <li>
 <a href=https://nilesoft.org/docs/configuration>
 <div class=icon-text>
 <span class=icon>
 <i class="i i-chevron-down i-lg"></i>
 </span>
 <span class=ml-2>Configuration</span>
 </div>
 </a>
 <ul>
 <li><a href=https://nilesoft.org/docs/configuration/settings>Settings</a></li>
 <li><a href=https://nilesoft.org/docs/configuration/themes>Themes</a></li>
 <li><a href=https://nilesoft.org/docs/configuration/modify-items>Modify items</a></li>
 <li><a href=https://nilesoft.org/docs/configuration/new-items>New items</a></li>
 <li><a href=https://nilesoft.org/docs/configuration/properties class=is-active>Properties</a></li>
 </ul>
 </li>
 <li>
 <a href=https://nilesoft.org/docs/expressions>
 <div class=icon-text>
 <span class=icon>
 <i class="i i-chevron-right i-lg"></i>
 </span>
 <span class=ml-2>Expressions</span>
 </div>
 </a>
 </li>
 <li>
 <a href=https://nilesoft.org/docs/functions>
 <div class=icon-text>
 <span class=icon>
 <i class="i i-chevron-right i-lg"></i>
 </span>
 <span class=ml-2>Functions</span>
 </div>
 </a>
 </li>
 <li>
 <a href=https://nilesoft.org/docs/examples>
 <div class=icon-text>
 <span class=icon>
 <i class="i i-chevron-right i-lg"></i>
 </span>
 <span class=ml-2>Examples</span>
 </div>
 </a>
 </li>
 </ul>
 </div>
 </div>
 <section class="container p-3 has-text-centered my-6"><ins class="adsbygoogle sf-hidden" style=display:block data-ad-client=ca-pub-2559766466824022 data-ad-slot=2410467654 data-ad-format=auto data-full-width-responsive=True></ins>
</section>
 </aside>
 <div id=content class=column>
 <div class=content>
 <h4 id=_top>Properties</h4>
<style class=sf-hidden>dt{font-weight:700}dd{margin-bottom:2em}ul.fold,#_index-list{padding-left:0}ul.fold,#_index-list li{display:inline;list-style:none}ul.fold>li,#_index-list li{display:inline}ul.fold>li:after,#_index-list li li:after{content:", "}ul.fold>li:last-child:after,#_index-list li li:last-child:after{content:""}</style>
Shell supports the following properties classes:
<ul>
 <li><a href=#_validation-properties>Validation Properties</a></li>
 <li><a href=#_filter-properties>Filter Properties</a></li>
 <li><a href=#_menuitem-properties>Menuitem Properties</a></li>
 <li><a href=#_command-properties>Command Properties</a></li>
</ul>
<p>Please also see the full index of available properties <a href=#_index>below</a>.</p>
<h5 id=_index>Index</h5>
<ul id=_index-list class="m-0 p-0 mb-4">
 <li>
 <ul>
 <li><a href=#admin>Admin</a></li>
 <li><a href=#arguments>arg</a></li>
 <li><a href=#arguments>args</a></li>
 <li><a href=#arguments>Arguments</a></li>
 </ul>
 </li>
 <li>
 <ul>
 <li><a href=#checked>Checked</a></li>
 <li><a href=#command>cmd</a></li>
 <li><a href=#column>col</a></li>
 <li><a href=#column>Column</a></li>
 <li><a href=#command>Command</a></li>
 </ul>
 </li>
 <li>
 <ul>
 <li><a href=#default>Default</a></li>
 <li><a href=#directory>dir</a></li>
 <li><a href=#directory>Directory</a></li>
 </ul>
 </li>
 <li>
 <ul>
 <li><a href=#expanded>Expanded</a></li>
 </ul>
 </li>
 <li>
 <ul>
 <li><a href=#find>Find</a></li>
 </ul>
 </li>
 <li>
 <ul>
 <li><a href=#image>Icon</a></li>
 <li><a href=#image>Image</a></li>
 <li><a href=#invoke>Invoke</a></li>
 </ul>
 </li>
 <li>
 <ul>
 <li><a href=#keys>Keys</a></li>
 </ul>
 </li>
 <li>
 <ul>
 <li><a href=#mode>Mode</a></li>
 <li><a href=#parent>Menu</a></li>
 </ul>
 </li>
 <li>
 <ul>
 <li><a href=#parent>Parent</a></li>
 <li><a href=#position>pos</a></li>
 <li><a href=#position>Position</a></li>
 </ul>
 </li>
 <li>
 <ul>
 <li><a href=#separator>sep</a></li>
 <li><a href=#separator>Separator</a></li>
 </ul>
 </li>
 <li>
 <ul>
 <li><a href=#tip>Tip</a></li>
 <li><a href=#title>Title</a></li>
 <li><a href=#type>Type</a></li>
 </ul>
 </li>
 <li>
 <ul>
 <li><a href=#verb>Verb</a></li>
 <li><a href=#visibility>vis</a></li>
 <li><a href=#visibility>Visibility</a></li>
 </ul>
 </li>
 <li>
 <ul>
 <li><a href=#wait>Wait</a></li>
 <li><a href=#where>Where</a></li>
 <li><a href=#window>Window</a></li>
 </ul>
 </li>
</ul>
<h4 id=_syntax>Syntax</h4>
<h5 id=_entry-types>Entry types</h5>
<p>In the following tables, the Types column shows to which entry types the
 property applies to.</p>
<p>The following abbreviations are used (if set in bold, then the property is mandatory for the given type):
</p>
<dl>
 <dt>mi</dt>
 <dd><a href=https://nilesoft.org/docs/configuration/modify-items#item>modify item</a>, i.e. the
 item
 entry itself. Is basically required to evaluate if the process instructions are applied to any given target.
 </dd>
 <dt>mt</dt>
 <dd><a href=https://nilesoft.org/docs/configuration/modify-items#target>modify target</a>, i.e.
 the menuitem of the existing menu to which the process instructions are applied
 </dd>
 <dt>nm</dt>
 <dd><a href=https://nilesoft.org/docs/configuration/new-items#menu>new menu type</a></dd>
 <dt>ni</dt>
 <dd><a href=https://nilesoft.org/docs/configuration/new-items#item>new item type</a></dd>
 <dt>ns</dt>
 <dd><a href=https://nilesoft.org/docs/configuration/new-items#separator>new separator type</a>.
 </dd>
</dl>
<h5 id=_validation-properties>Validation Properties</h5>
<p>Determine if a given <a href=https://nilesoft.org/docs/configuration/modify-items>Modify items</a>
 or <a href=https://nilesoft.org/docs/configuration/new-items>New items</a> entry should be
 processed when a context menu is displayed.</p>
<br>
<ul class=fold>
 <li><a href=#mode>Mode</a></li>
 <li><a href=#type>Type</a></li>
 <li><a href=#where>Where</a></li>
</ul>
<br>
<br>
<div id=_validation-syntax class=table-responsive>
 <h6>Syntax</h6>
 <table class=table>
 <thead>
 <tr>
 <th scope=col>Property</th>
 <th scope=col>Types<sup><a href=#_entry-types>(*)</a></sup></th>
 <th scope=col>Summary</th>
 </tr>
 </thead>
 <tbody>
 <tr id=where>
 <td>Where</td>
 <td>mi, nm, ni, ns</td>
 <td>Process given menuitem if <code>true</code> is returned. Allows the <strong>evaluation of arbitrary
 <a href=https://nilesoft.org/docs/expressions>expressions</a></strong>, e.g. <a href=https://nilesoft.org/docs/functions#if><code>if()</code></a>.<br>
 Default = <span class=syntax-keyword>true</span>
 </td>
 </tr>
 <tr id=mode>
 <td>Mode</td>
 <td>mi, nm, ni, ns</td>
 <td>Display menuitem by <strong>type of selection</strong>. The value has one of the following
 parameters
 (of type <a href=https://nilesoft.org/docs/configuration/modify-items>string</a>):
 <table class=table>
 <tbody><tr>
 <td class=syntax-keyword>none</td>
 <td>Display menuitem when there is no selection.</td>
 </tr>
 <tr>
 <td class=syntax-keyword>single</td>
 <td>Display menuitem when there is a single object selected.</td>
 </tr>
 <tr>
 <td class=syntax-keyword>multi_unique</td>
 <td>Display menuitem when multiple objects of the same <a href=#type>type</a> are selected.
 </td>
 </tr>
 <tr>
 <td class=syntax-keyword>multi_single</td>
 <td>Display menuitem when multiple files with a single file extension are selected.</td>
 </tr>
 <tr>
 <td class=syntax-keyword>multiple</td>
 <td>Display any type of selection, unless there is none.</td>
 </tr>
 </table>
 Default = <span class=syntax-keyword>single</span>
 </td>
 </tr>
 <tr id=type>
 <td>Type</td>
 <td>mi, nm, ni, ns</td>
 <td>Specifies the <strong>types of objects</strong> for which the menuitem will be displayed.<br>
 Possible values are shown below. Separate multiple types with the pipe character (<code>|</code>),
 in
 which case the menuitem is displayed if any of the given types is matched.<br>
 To exclude a given type, prefix its value with the tilde character (<code>~</code>).
 <p class=has-text-danger>Expressions are not supported with this property.</p>
 <table class=table>
 <tbody><tr id=type-asterisks>
 <td class=syntax-keyword>*</td>
 <td>Display menuitem when any type is selected.</td>
 </tr>
 <tr id=type-file>
 <td class=syntax-keyword>File</td>
 <td>Display menuitem when files are selected.</td>
 </tr>
 <tr id=type-directory>
 <td class=syntax-keyword>Directory(Dir)</td>
 <td>Display menuitem when directories are selected.</td>
 </tr>
 <tr id=type-drive>
 <td class=syntax-keyword>Drive</td>
 <td>Display menuitem when drives are selected.</td>
 </tr>
 <tr id=type-usb>
 <td class=syntax-keyword>USB</td>
 <td>Display menuitem when USB flash-drives are selected.</td>
 </tr>
 <tr id=type-dvd>
 <td class=syntax-keyword>DVD</td>
 <td>Display menuitem when DVD-ROM drives are selected.</td>
 </tr>
 <tr id=type-fixed>
 <td class=syntax-keyword>Fixed</td>
 <td>Display menuitem when fixed drives are selected. Such drives have a fixed media; for
 example, a hard disk drive or flash drive.
 </td>
 </tr>
 <tr id=type-vhd>
 <td class=syntax-keyword>VHD</td>
 <td>Display menuitem when Virtual Hard Disks are selected.</td>
 </tr>
 <tr id=type-removable>
 <td class=syntax-keyword>Removable</td>
 <td>Display menuitem when the selected drives have removable media; for example, a floppy drive,
 thumb drive, or flash card
 reader.
 </td>
 </tr>
 <tr id=type-remote>
 <td class=syntax-keyword>Remote</td>
 <td>Display menuitem when the selected remote (network) drives are selected.</td>
 </tr>
 <tr id=type-back>
 <td class=syntax-keyword>Back</td>
 <td>Display menuitem when the background of all types are selected (<code>back</code>). Or
 specify one of
 the following more granular types for the background:
 <ul>
 <li>
 <code>back.directory</code>
 </li>
 <li>
 <code>back.drive</code>, including
 <ul>
 <li><code>back.fixed</code></li>
 <li><code>back.usb</code></li>
 <li><code>back.dvd</code></li>
 <li><code>back.vhd</code></li>
 <li><code>back.Removable</code></li>
 </ul>
 </li>
 <li>
 <code>back.namespace</code>, including
 <ul>
 <li><code>back.computer</code></li>
 <li><code>back.recyclebin</code></li>
 </ul>
 </li>
 </ul>
 </td>
 </tr>
 <tr id=type-desktop>
 <td class=syntax-keyword>Desktop</td>
 <td>Display menuitem when the Desktop is selected.</td>
 </tr>
 <tr id=type-namespace>
 <td class=syntax-keyword>Namespace</td>
 <td>Display menuitem when Namespaces are selected. Can be virtual objects such as My Network Places and Recycle Bin.
 </td>
 </tr>
 <tr id=type-computer>
 <td class=syntax-keyword>Computer</td>
 <td>Display menuitem when My Computer is selected.</td>
 </tr>
 <tr id=type-recyclebin>
 <td class=syntax-keyword>Recyclebin</td>
 <td>Display menuitem when the Recycle bin is selected.
 </td>
 </tr>
 <tr id=type-taskbar>
 <td class=syntax-keyword>Taskbar</td>
 <td>Display menuitem when the Taskbar is selected.
 </td>
 </tr>
 </table>
 Default = <span class=syntax-keyword>Accepts all types, except for the Taskbar.</span>
 </td>
 </tr>
 <tbody>
 </tbody>
 </table>
</div>
<h5 id=_filter-properties>Filter Properties</h5>
<p>For <a href=https://nilesoft.org/docs/configuration/modify-items>Modify items</a> entries only,
 filter properties determine if a given menuitem is a valid <a href=https://nilesoft.org/docs/configuration/modify-items#target>target</a> for the <a href=https://nilesoft.org/docs/configuration/modify-items#process-instructions>process instructions</a></p>
<ul class=fold>
 <li><a href=#find>Find</a></li>
</ul>
<div id=_filter-syntax class=table-responsive>
 <h6>Syntax</h6>
 <table class=table>
 <thead>
 <tr>
 <th scope=col>Property</th>
 <th scope=col>Types<sup><a href=#_entry-types>(*)</a></sup></th>
 <th scope=col>Summary</th>
 </tr>
 </thead>
 <tbody>
 <tr id=find>
 <td>Find</td>
 <td>nm, ni, ns</td>
 <td>
 <dl>
 <dt>For modify items (required)</dt>
 <dd>Apply the current item's process instructions to any existing menuitem if their <a href=#title><code>title</code></a> property matches the
 pattern of the current item's <code>find</code> property.
 </dd>
 <dt>For dynamic items (optional)</dt>
 <dd><p>Display the current menuitem if the pattern of its <code>find</code> property matches the
 path name or path extension
 of the <strong>selected files</strong>.</p>
 <p>Default = <span class=syntax-keyword>null</span>, which means any string is "matched".
 </p>
 </dd>
 <dt>Syntax</dt>
 <dd>
 <pre><code>find = '%pattern%'
find = '%pattern%|%pattern%[...]'</code></pre>
 <p>where <strong>%pattern%</strong> can be one or
 more
 matching instructions (see Examples below). The
 following characters do have special meaning:</p>
 <ul>
 <li><code>|</code> <strong>Use to separate patterns.</strong> If any one pattern
 matches,
 the property yields
 <span class=syntax-keyword>true</span>.
 </li>
 <li><code>*</code> <strong>Matches any number of characters.</strong> Is used as a
 wildcard
 to
 match only the beginning or the end of the entire string (or word, if used in
 combination with the exclamation mark <code>!</code>).
 </li>
 <li><code>!</code> <strong>Negates the match</strong> of the current pattern, or
 <strong>limits
 the wildcard (<code>*</code>)</strong> to one word only.
 </li>
 <li><code>""</code> the enclosed string is treated as a <strong>word</strong>.
 </li>
 </ul>
 <p>A <strong>word</strong> is a sequence of
 alphanumerical characters that is confined to the left and to the right by either a
 space
 <code> </code>, a non-word character (e.g. <code>/</code> or <code>-</code>), or the
 beginning or the end of the entire string, respectively.</p></dd>
 <dt>Examples</dt>
 <dd>
 <div class=table-container>
 <table class=table>
 <thead>
 <tr>
 <th>Pattern</th>
 <th class=is-two-thirds>Matches any string that ...</th>
 <th>Would match</th>
 <th>Would not match</th>
 </tr>
 </thead>
 <tbody>
 <tr>
 <td><code>'foo'</code></td>
 <td>contains the literal string <code>foo</code> anywhere.</td>
 <td><code>foo</code>, <code>foobar</code>, <code>afoobar</code></td>
 <td><code>fo</code>, <code>f oo</code>, <code>bar</code></td>
 </tr>
 <tr>
 <td><code>'"foo"'</code></td>
 <td>contains the literal string <code>foo</code> as a whole word
 only.
 </td>
 <td><code>foo</code>, <code>foo/bar</code>, <code>some foo bar</code></td>
 <td><code>foobar</code>, <code>foofoo</code>, <code>bar</code></td>
 </tr>
 </tbody>
 <tbody>
 <tr>
 <td><code>'*foo'</code></td>
 <td>ends with the literal string <code>foo</code>.</td>
 <td><code>foo</code>, <code>barfoo</code>, <code>bar/foo</code></td>
 <td><code>foobar</code>, <code>fooo</code>, <code>foo </code></td>
 </tr>
 <tr>
 <td><code>'foo*'</code></td>
 <td>starts with the literal string <code>foo</code>.
 </td>
 <td><code>foo</code>, <code>foobar</code>, <code>foo/bar</code></td>
 <td><code> foobar</code>, <code>fo</code>, <code>yeti</code></td>
 </tr>
 </tbody>
 <tbody>
 <tr>
 <td><code>'!foo'</code></td>
 <td>does not contain the literal string <code>foo</code> anywhere.
 </td>
 <td><code>fobar</code>, <code>fo</code>, <code>kung-fu</code></td>
 <td><code>foo</code>, <code>foobar</code>, <code>barfoo/bar</code></td>
 </tr>
 <tr>
 <td><code>'!"foo"'</code></td>
 <td>does not contain the word
 <code>foo</code></td>
 <td><code>fobar</code>, <code>kung fu bar</code>, <code>foobar</code></td>
 <td><code>foo</code>, <code>kung foo bar</code>, <code>bar/foo/bar</code></td>
 </tr>
 <tr>
 <td><code>'!*foo'</code></td>
 <td>does not contain a word ending on
 <code>foo</code></td>
 <td><code>foobar</code>, <code>fooo-fo</code></td>
 <td><code>foo</code>, <code>foo bar</code>, <code>bar/foo</code></td>
 </tr>
 <tr>
 <td><code>'foo*!'</code></td>
 <td>does not contain a word stharting
 wit
 <code>foo</code></td>
 <td><code>myFooBar</code>, <code>barFoo</code></td>
 <td><code>foo</code>, <code>foobar</code>, <code>fo-fooo</code></td>
 </tr>
 </tbody>
 <tbody>
 <tr>
 <td colspan=4>
 <p><br>For dynamic items the following syntax allows to match against file
 extensions:<br><br></p>
 </td>
 </tr>
 </tbody>
 <thead>
 <tr>
 <th>Pattern</th>
 <th>Matches any file extension ...</th>
 <th>Would match</th>
 <th>Would not match</th>
 </tr>
 </thead>
 <tbody>
 <tr>
 <td><code>'.exe'</code></td>
 <td>equal to <code>.exe</code></td>
 <td><code>setup.exe</code>, <code>notepad.exe</code></td>
 <td><code>install.bat</code>, <code>shell.nss</code>, <code>shell.ex_</code>,
 file
 without an extension.
 </td>
 </tr>
 <tr>
 <td><code>'!.exe'</code></td>
 <td>not equal to <code>.exe</code></td>
 <td><code>setup.exe.zip</code>, <code>video.mp4</code>, <code>shell.ex_</code>,
 file
 without an extension.
 </td>
 <td><code>setup.exe</code>, <code>shell.exe</code></td>
 </tr>
 </tbody>
 <tbody>
 <tr>
 <td><code>'.exe|.dll'</code></td>
 <td>equal to either <code>.exe</code> or <code>.dll</code></td>
 <td><code>shell.exe</code>, <code>shell.dll</code>
 </td>
 <td><code>shell.zip</code>, <code>shell.nss</code>, file
 without an extension.
 </td>
 </tr>
 </tbody>
 </table>
 </div>
 </dd>
 </dl>
 </td>
 </tr>
 </tbody>
 </table>
</div>
<h5 id=_menuitem-properties>Menuitem Properties</h5>
<p>This set of properties describe the appearance and location of a given menuitem. For modify-items, this is the target menuitem. For dynamic entries, this is the newly created menuitem.</p>
<dl>
 <dt>Appearance</dt>
 <dd>
 <ul class=fold>
 <li><a href=#checked>Checked</a></li>
 <li><a href=#default>Default</a></li>
 <li><a href=#image>Image</a></li>
 <li><a href=#separator>Separator</a></li>
 <li><a href=#tip>Tip</a></li>
 <li><a href=#title>Title</a></li>
 <li><a href=#visibility>Visibility</a></li>
 </ul>
 </dd>
 <dt>Location</dt>
 <dd>
 <ul class=fold>
 <li><a href=#column>Column</a></li>
 <li><a href=#expanded>Expanded</a></li>
 <li><a href=#keys>Keys</a></li>
 <li><a href=#parent>Menu</a></li>
 <li><a href=#parent>Parent</a></li>
 <li><a href=#position>Position</a></li>
 </ul>
 </dd>
</dl>
<div id=_menuitem-syntax class=table-responsive>
 <h6>Syntax</h6>
 <table class=table>
 <thead>
 <tr>
 <th scope=col>Property</th>
 <th scope=col>Types<sup><a href=#_entry-types>(*)</a></sup></th>
 <th scope=col>Summary</th>
 </tr>
 </thead>
 <tbody>
 <tr id=title>
 <td>Title</td>
 <td>st, <strong>nm</strong>, <strong>ni</strong>
 </td>
 <td><p>Sets the <strong>caption</strong> of the menuitem.</p>
 <dl>
 <dt>For modify-items (optional)</dt>
 <dd><p>Default = <span class=syntax-keyword>null</span>, which means the title of the target
 is
 not changed.</p></dd>
 <dt>For dynamic items (required)</dt>
 <dd><p class=text-danger>It is mandatory for <a href=https://nilesoft.org/docs/configuration/dynamic#menu>menu</a> and <a href=https://nilesoft.org/docs/configuration/dynamic#menu>item</a> entries, unless a <a href=#image%22><code>image</code></a> property is defined.</p>
 </dd>
 </dl>
 </td>
 </tr>
 <tr id=visibility>
 <td>Visibility (vis)</td>
 <td>st, nm, ni, ns</td>
 <td>Sets the <strong>visibility</strong> of a menuitem. Can have one of the following parameters:
 <table class=table>
 <tbody><tr id=visibility-hidden>
 <td class=syntax-keyword>Hidden</td>
 <td>Hide the menuitem.</td>
 </tr>
 <tr id=visibility-normal>
 <td class=syntax-keyword>Normal</td>
 <td>Enable the menuitem.</td>
 </tr>
 <tr id=visibility-disable>
 <td class=syntax-keyword>Disable</td>
 <td>Disable the menuitem.</td>
 </tr>
 <tr id=visibility-static>
 <td class=syntax-keyword>Static</td>
 <td>Display menuitem as label, with or without an <a href=#image%22><code>image</code></a></td>
 </tr>
 <tr id=visibility-label>
 <td class=syntax-keyword>Label</td>
 <td>Display menuitem as label without an image</td>
 </tr>
 </table>
 <div class="notification is-info mt-5">
 <i class=mr-4>Note:</i> The values Static and Label are not available for modify-items.
 </div>
 Default = <span class=syntax-keyword>Normal</span>
 </td>
 </tr>
 <tr id=separator>
 <td>Separator (sep)</td>
 <td>st, nm, ni</td>
 <td>Add a <strong>separator</strong> to the menuitem:
 <table class=table>
 <tbody><tr id=separator-none>
 <td class=syntax-keyword>None</td>
 <td>Not adding a separator with the menuitem.</td>
 </tr>
 <tr id=separator-before>
 <td class=syntax-keyword>Before, Top</td>
 <td>Add a separator before the menuitem.</td>
 </tr>
 <tr id=separator-after>
 <td class=syntax-keyword>After, Bottom</td>
 <td>Add a separator after the menuitem.</td>
 </tr>
 <tr id=separator-both>
 <td class=syntax-keyword>Both</td>
 <td>Add a separator before and after the menuitem.</td>
 </tr>
 </table>
 Default = <span class=syntax-keyword>none</span>
 </td>
 </tr>
 <tr id=position>
 <td>Position (pos)</td>
 <td>st, nm, ni, ns</td>
 <td>The <strong>position</strong> at which a menuitem should be inserted into the <a href=https://nilesoft.org/docs/configuration/dynamic#menu>menu</a>.<br>
 Position can have one of the following parameters:
 <table class=table>
 <tbody><tr id=position-auto>
 <td class=syntax-keyword>Auto</td>
 <td>Insert the menuitem to the current position.</td>
 </tr>
 <tr id=position-middle>
 <td class=syntax-keyword>Middle</td>
 <td>Insert the menuitem to the middle of the <a href=https://nilesoft.org/docs/configuration/dynamic#menu>menu</a>.
 </td>
 </tr>
 <tr id=position-top>
 <td class=syntax-keyword>Top</td>
 <td>Insert the menuitem to the top of the <a href=https://nilesoft.org/docs/configuration/dynamic#menu>menu</a>.
 </td>
 </tr>
 <tr id=position-bottom>
 <td class=syntax-keyword>Bottom</td>
 <td>Insert the menuitem to the bottom of the <a href=https://nilesoft.org/docs/configuration/dynamic#menu>menu</a>.
 </td>
 </tr>
 <tr id=position-integer>
 <td><code>Integer</code></td>
 <td>Insert the menuitem to a specified position.</td>
 </tr>
 </table>
 Default = <span class=syntax-keyword>auto</span>
 </td>
 </tr>
 <tr id=image>
 <td>Image, Icon</td>
 <td>st, nm, ni</td>
 <td>The <strong>icon</strong> that appears in a menuitem. This property can be assigned as image files,
 resource icons, glyph
 or color. With one of the following parameters
 <table class=table>
 <tbody><tr id=image-null>
 <td class=syntax-keyword>null</td>
 <td>Show menuitem without icon.</td>
 </tr>
 <tr id=image-inherit>
 <td class=syntax-keyword>Inherit</td>
 <td>@*Inheriting the image from the parent.*@
 Inherits this property from its parent item.
 </td>
 </tr>
 <tr id=image-cmd>
 <td class=syntax-keyword>Cmd</td>
 <td>Assign image from the command property.</td>
 </tr>
 <tr id=image-glyph>
 <td class=syntax-keyword>Glyph</td>
 <td>Assign image as Glyph.</td>
 </tr>
 <tr id=image-color>
 <td class=syntax-keyword>Color</td>
 <td>Assign image as color.</td>
 </tr>
 <tr id=image-path>
 <td class=syntax-keyword>Path</td>
 <td>Assign image from location path or resource icon.</td>
 </tr>
 </table>
 <div class="notification is-info mt-5">
 <i class=mr-4>Note:</i>The value Cmd is not available for modify-items
 targets.
 </div>
 Default = <span class=syntax-keyword>null</span>
 </td>
 </tr>
 <tr id=parent>
 <td>Parent, Menu</td>
 <td>st, nm, ni, ns</td>
 <td><p><strong>Move current menuitem</strong> to another <a href=https://nilesoft.org/docs/configuration/dynamic#menu>menu</a>.</p>
 Default = <span class=syntax-keyword>null</span></td>
 </tr>
 <tr id=checked>
 <td>Checked</td>
 <td>st, ni</td>
 <td><strong>Type of select option</strong>:
 <table class=table>
 <tbody><tr id=checked-0>
 <td class=syntax-keyword>0</td>
 <td>Not checked</td>
 </tr>
 <tr id=checked-1>
 <td class=syntax-keyword>1</td>
 <td>Display as check mark.</td>
 </tr>
 <tr id=checked-2>
 <td class=syntax-keyword>2</td>
 <td>Display as radio bullet.</td>
 </tr>
 </table>
 Default = <span class=syntax-keyword>0</span>
 </td>
 </tr>
 <tr id=default>
 <td>Default</td>
 <td>st, ni</td>
 <td>
 <p>Specifies that the <a href=https://nilesoft.org/docs/configuration/dynamic#item>item</a> is the default. A <a href=https://nilesoft.org/docs/configuration/dynamic#menu>menu</a> can contain only one default
 menuitem, which is <strong>displayed in bold</strong>.</p>
 Default = <span class=syntax-keyword>false</span>
 </td>
 </tr>
 <tr id=expanded>
 <td>Expanded</td>
 <td>nm</td>
 <td>
 <p>Move all immediate menuitems to the parent <a href=https://nilesoft.org/docs/configuration/dynamic#menu>menu</a>.</p>
 Default = <span class=syntax-keyword>false</span>
 </td>
 </tr>
 <tr id=column>
 <td>Column(col)</td>
 <td>nm, ni</td>
 <td><p>Create a new <strong>column</strong>.</p>
 Default = <span class=syntax-keyword>true</span>
 </tr>
 <tr id=keys>
 <td>Keys</td>
 <td>st, nm, ni</td>
 <td><p>Show <strong>keyboard shortcuts</strong>.</p>
 Default = <span class=syntax-keyword>null</span></td>
 </tr>
 <tr id=tip>
 <td>Tip</td>
 <td>st, nm, ni</td>
 <td>
 <p>Show a <strong>tooltip</strong> for the current <a href=https://nilesoft.org/docs/configuration/dynamic#menu>menu</a>or <a href=https://nilesoft.org/docs/configuration/dynamic#item>item</a>.</p>
 Default = <span class=syntax-keyword>null</span><br>
 <strong>Syntax</strong><br>
 <pre class=language-shell tabindex=0><code class=language-shell>tip <span class="token operator">=</span> <span class="token string">"Lorem Ipsum is simply dummy text."</span>
tip <span class="token operator">=</span> <span class="token punctuation">[</span><span class="token string">"Lorem Ipsum is simply dummy text."</span><span class="token punctuation">,</span> <span class="token function">tip.info</span><span class="token punctuation">]</span>
tip <span class="token operator">=</span> <span class="token punctuation">[</span><span class="token string">"Lorem Ipsum is simply dummy text."</span><span class="token punctuation">,</span> <span class="token function">tip.info</span><span class="token punctuation">,</span> <span class="token function">1.2</span><span class="token punctuation">]</span></code></pre>
 </td>
 </tr>
 </tbody>
 </table>
</div>
<h5 id=_command-properties>Command Properties</h5>
<p>This set of properties describe how a command is executed. Only available for dynamic items.</p>
<ul class=fold>
 <li><a href=#admin>Admin</a></li>
 <li><a href=#arguments>Arguments</a></li>
 <li><a href=#command>Command</a></li>
 <li><a href=#directory>Directory</a></li>
 <li><a href=#invoke>Invoke</a></li>
 <li><a href=#verb>Verb</a></li>
 <li><a href=#wait>Wait</a></li>
 <li><a href=#window>Window</a></li>
</ul>
<div id=_command-syntax class=table-responsive>
 <h6>Syntax</h6>
 <table class=table>
 <thead>
 <tr>
 <th scope=col>Property</th>
 <th scope=col>Types<sup><a href=#_entry-types>(*)</a></sup></th>
 <th scope=col>Summary</th>
 </tr>
 </thead>
 <tbody>
 <tr id=command>
 <td>Command (cmd)</td>
 <td>ni</td>
 <td><p>The <strong>command</strong> associated with the menuitem. Occurs when the menuitem is clicked or
 selected using a shortcut key or access key defined for the menuitem.</p>
 Default = <span class=syntax-keyword>null</span>
 </td>
 </tr>
 <tr id=arguments>
 <td>Arguments (arg, args)</td>
 <td>ni</td>
 <td><p>The <strong>command line parameters</strong> to pass to the <a href=#command%22>command</a> property of a menuitem.</p>
 Default = <span class=syntax-keyword>null</span>
 </td>
 </tr>
 <tr id=invoke>
 <td>Invoke</td>
 <td>ni</td>
 <td>Set <strong>execution type</strong>
 <table class=table>
 <tbody><tr id=invoke-single>
 <td class=syntax-keyword>0, single</td>
 <td>execute the <a href=#command>command</a> only once in total. The list of selected
 items can be accessed with <a href=https://nilesoft.org/docs/functions/sel#sel><code>@sel</code></a>
 </td>
 </tr>
 <tr id=invoke-multiple>
 <td class=syntax-keyword>1, multiple</td>
 <td>execute the <a href=#command><code>command</code></a> once for every single item in the current
 selection. The currently processed item can be accessed with e.g <a href=https://nilesoft.org/docs/functions/sel#sel.path.quote><code>@sel.path.quote</code></a>
 </td>
 </tr>
 </table>
 Default = <span class=syntax-keyword>0</span>
 </td>
 </tr>
 <tr id=window>
 <td>Window</td>
 <td>ni</td>
 <td>Controls how the <strong>window</strong> of the executed <a href=#command>command</a> is to be shown. Can be one of the following
 parameters:
 <p class=syntax-keyword><code>Hidden</code>, <code>Show</code>, <code>Visible</code>,
 <code>Minimized</code>, <code>Maximized</code></p>
 Default = <span class=syntax-keyword>show</span>
 </td>
 </tr>
 <tr id=directory>
 <td>Directory (dir)</td>
 <td>ni</td>
 <td><p>Specifies the <strong>working directory</strong> to
 execute
 the <a href=#command>command</a> in.</p>
 Default = <span class=syntax-keyword>null</span>
 </td>
 </tr>
 <tr id=admin>
 <td>Admin</td>
 <td>ni</td>
 <td><p>Execute the <a href=#command>command</a>
 with <strong>administrative permissions</strong>.</p>
 Default = <span class=syntax-keyword>false</span>
 </td>
 </tr>
 <tr id=verb>
 <td>Verb</td>
 <td>ni</td>
 <td>Specifies the <strong>default operation</strong> for the selected file. Value type <a href=https://nilesoft.org/docs/configuration/modify-items>string</a>
 and can have one of the following parameters:<br>
 <table class=table>
 <tbody><tr id=verb-null>
 <td>null</td>
 <td>Specifies that the operation is the default for the selected file type.</td>
 </tr>
 <tr id=verb-open>
 <td>Open</td>
 <td>Opens a file or an application.</td>
 </tr>
 <tr id=verb-openas>
 <td>OpenAs</td>
 <td>Opener dialog when no program is associated to the extension.</td>
 </tr>
 <tr id=verb-runas>
 <td>RunAs</td>
 <td>In Windows 7 and Vista, opens the UAC dialog andin others, open the Run as... Dialog.</td>
 </tr>
 <tr id=verb-edit>
 <td>Edit</td>
 <td>Opens the default text editor for the file.</td>
 </tr>
 <tr id=verb-explore>
 <td>Explore</td>
 <td>Opens the Windows Explorer in the folder specified in Directory.</td>
 </tr>
 <tr id=verb-properties>
 <td>Properties</td>
 <td>Opens the properties window of the file.</td>
 </tr>
 <tr id=verb-print>
 <td>Print</td>
 <td>Start printing the file with the default application.</td>
 </tr>
 <tr id=verb-find>
 <td>Find</td>
 <td>Start a search.</td>
 </tr>
 </table>
 Default = <span class=syntax-keyword>open</span>
 </td>
 </tr>
 <tr id=wait>
 <td>Wait</td>
 <td>ni</td>
 <td>
 <p><strong>Wait</strong> for the <a href=#command>command</a>
 to complete.</p>
 Default = <span class=syntax-keyword>false</span>
 </td>
 </tr>
 </tbody>
 </table>
</div>
 <hr>
 <div id=improve class=my-5>
 <p>
 This page is <strong class=has-text-grey>open source</strong>.
 Noticed a typo? Or something unclear?
 <br>
 <a href=https://github.com/moudey/shell/blob/main/docs/configuration/properties.html style="border-bottom:1px solid currentColor">
 Improve this page on GitHub
 </a>
 </p>
 </div>
 <section id=ad-responsive-horizontal class="has-text-centered my-6"><ins class="adsbygoogle sf-hidden" style=display:block data-ad-client=ca-pub-2559766466824022 data-ad-slot=9611969124 data-ad-format=auto data-full-width-responsive=True></ins>
</section>
 </div>
 </div>
</div>
 </div>
 </main>
 <footer id=mst-footer class="footer mt-5 is-size-7">
 <div class=container>
 <div id=newsletter class="columns is-centered is-vcentered mb-6">
 <div class="column is-half">
 <h5 class="is-size-5 has-text-black mb-1">
 <strong>Newsletter</strong>
 <small>Features, releases, showcase... stay in the loop!</small>
 </h5>
 <form action=/newsletter/subscribe id=newsletter-form>
 <div class="field has-addons">
 <div class="control is-expanded">
 <input class=input type=email name=email placeholder="email address" required value>
 <div data-lastpass-icon-root=true style=position:relative!important;height:0px!important;width:0px!important;float:left!important><template shadowrootmode=open><svg width=24 height=24 viewBox="0 0 24 24" fill=none xmlns=http://www.w3.org/2000/svg data-lastpass-icon=true style=position:absolute;cursor:pointer;height:22px;max-height:22px;width:22px;max-width:22px;top:9px;left:521.312px;z-index:auto;color:rgb(215,64,58)><rect x=0.680176 y=0.763062 width=22.6392 height=22.4737 rx=4 fill=currentColor></rect><path fill-rule=evenodd clip-rule=evenodd d="M19.7935 7.9516C19.7935 7.64414 20.0427 7.3949 20.3502 7.3949C20.6576 7.3949 20.9069 7.64414 20.9069 7.9516V16.0487C20.9069 16.3562 20.6576 16.6054 20.3502 16.6054C20.0427 16.6054 19.7935 16.3562 19.7935 16.0487V7.9516Z" fill=white></path><path fill-rule=evenodd clip-rule=evenodd d="M4.76288 13.6577C5.68525 13.6577 6.43298 12.9154 6.43298 11.9998C6.43298 11.0842 5.68525 10.3419 4.76288 10.3419C3.8405 10.3419 3.09277 11.0842 3.09277 11.9998C3.09277 12.9154 3.8405 13.6577 4.76288 13.6577Z" fill=white></path><path fill-rule=evenodd clip-rule=evenodd d="M10.3298 13.6577C11.2521 13.6577 11.9999 12.9154 11.9999 11.9998C11.9999 11.0842 11.2521 10.3419 10.3298 10.3419C9.4074 10.3419 8.65967 11.0842 8.65967 11.9998C8.65967 12.9154 9.4074 13.6577 10.3298 13.6577Z" fill=white></path><path fill-rule=evenodd clip-rule=evenodd d="M15.8964 13.6577C16.8188 13.6577 17.5665 12.9154 17.5665 11.9998C17.5665 11.0842 16.8188 10.3419 15.8964 10.3419C14.974 10.3419 14.2263 11.0842 14.2263 11.9998C14.2263 12.9154 14.974 13.6577 15.8964 13.6577Z" fill=white></path></svg></template></div></div>
 <div class=control>
 <button type=submit class="button is-black">
 Subscribe
 </button>
 </div>
 </div>
 </form>
 </div>
</div>
 <div class="columns is-mobile is-gapless is-vcentered">
<div class="column is-narrow mt-2 mr-3">
 <svg xmlns=http://www.w3.org/2000/svg viewBox="0 0 512 512" width=32 height=32><path d="M0 300l100-100 106 106-100 100zM100 200l100-100 106 106-100 100zM206 306l100-100 106 106-100 100zM306 206l100-100 106 106-100 100z"></path></svg>
 </div>
 <div class=column>
 <span class=is-block>© 2023 Nilesoft Ltd.</span>
 <span class=is-block><a href=https://nilesoft.org/legal/privacy-policy>Privacy Policy</a> | <a href=https://nilesoft.org/legal/eula>Terms</a> | <a href=https://nilesoft.org/about>about</a></span>
 </div>
 <div class=column>
 <div class="is-pulled-right has-text-black">
 <span class="icon mr-2">
 <a href=https://facebook.com/moudeygo title=Facebook>
 <i class="i i-facebook i-2x has-text-black"></i>
 </a>
 </span>
 <span class="icon mr-2">
 <a href=https://twitter.com/moudey title=Twitter>
 <i class="i i-twitter i-2x has-text-black"></i>
 </a>
 </span>
 <span class="icon mr-2">
 <a href=https://reddit.com/u/moudeygo title=Reddit>
 <i class="i i-reddit i-2x has-text-black"></i>
 </a>
 </span>
 <span class="icon mr-2">
 <a href=https://youtube.com/@moudey title=Youtube>
 <i class="i i-youtube i-2x has-text-black"></i>
 </a>
 </span>
 <span class="icon mr-2">
 <a href=https://github.com/moudey/Shell title=Github>
 <i class="i i-github i-2x has-text-black"></i>
 </a>
 </span>
 <span class=icon>
 <a href=https://nilesoft.org/feed title="RSS news">
 <i class="i i-rss i-2x has-text-black"></i>
 </a>
 </span>
 </div>
 </div>
 </div>
 </div>
 </footer>
 
 
 
 
 
 <div id=cookieConsent data-category=cookies style=position:fixed;bottom:0;left:0;right:0;z-index:50 role=alert>
 <div class="p-2 has-background-black has-text-white">
 <div class=container>
 <div class=columns>
 <div class="column is-8-desktop">
 <h4 class="mb-2 is-size-5 has-text-weight-bold">Cookie Policy</h4>
 <span class="mb-4 has-text-grey-light mr-4">This website uses cookies to ensure you get the best experience on our website.</span>
 <a aria-label="learn more about cookies" role=button tabindex=0 href=http://cookiesandyou.com/ target=_blank>Learn more</a> 
 <a class=mx-3 href=https://nilesoft.org/legal/privacy-policy role=button>Privacy</a>
 </div>
 <div class="column is-2-desktop">
 <button class="button is-dark px-6 my-4" data-cookie-string=".AspNet.Consent=yes; expires=Fri, 11 Oct 2024 10:54:02 GMT; path=/; secure; samesite=none">Allow</button>
 </div>
 </div>
 </div>
 </div>
</div>
 
 
<div data-lastpass-root style=position:absolute!important;top:0px!important;left:0px!important;height:0px!important;width:0px!important><div data-lastpass-infield=true style=position:absolute!important;top:0px!important;left:0px!important><template shadowrootmode=open><div style=position:absolute;height:100vh;width:100vw;z-index:2147483647;border-radius:4px;top:0px;left:0px;max-height:0px;max-width:280px;min-width:auto;margin-top:10px><div style=left:-1px;display:none;position:absolute;overflow:hidden;bottom:100%;width:20px;height:10px></div></div></template></div></div><script data-template-shadow-root>(()=>{document.currentScript.remove();processNode(document);function processNode(node){node.querySelectorAll("template[shadowrootmode]").forEach(element=>{let shadowRoot = element.parentElement.shadowRoot;if (!shadowRoot) {try {shadowRoot=element.parentElement.attachShadow({mode:element.getAttribute("shadowrootmode")});shadowRoot.innerHTML=element.innerHTML;element.remove()} catch (error) {} if (shadowRoot) {processNode(shadowRoot)}}})}})()</script>