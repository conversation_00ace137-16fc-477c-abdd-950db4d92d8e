/* Global variables */

// Colors
$color3 = @if(theme.islight, image.color3, 'none')
$color_islight_WB = @if(theme.islight, '#fff', '#000')

// Stock paths (for use in different icons)
$clipPath =
'<defs>
	<clipPath id="clip0">
		<path fill="#fff" d="M0 0h16v16H0z"/>
	</clipPath>
</defs>'
$svg_window_template2 = '<path fill="@image.color1" d="M3.453 15C3.12 15 2.805 14.93 2.508 14.8C2.508 14.8 2.211 14.66 1.953 14.48C1.953 14.48 1.734 14.27 1.516 14.05C1.516 14.05 1.339 13.79 1.203 13.49C1.203 13.49 1.068 13.2 1 12.88L1 3.438C1 3.115 1.068 2.805 1.203 2.508C1.203 2.508 1.339 2.211 1.516 1.951C1.516 1.951 1.734 1.727 1.953 1.503C1.953 1.503 2.211 1.326 2.508 1.195C2.508 1.195 2.805 1.065 3.12 1L12.56 1C12.89 1 13.2 1.065 13.49 1.195C13.49 1.195 13.79 1.326 14.05 1.503C14.05 1.503 14.27 1.727 14.5 1.951C14.5 1.951 14.67 2.211 14.8 2.508C14.8 2.508 14.93 2.805 15 3.115L15 6.938C14.84 6.812 14.68 6.7 14.52 6.602C14.52 6.602 14.35 6.503 14.18 6.406L14 5L2 5L2 12.5C2 12.7 2.039 12.89 2.117 13.07C2.117 13.07 2.195 13.25 2.305 13.41C2.305 13.41 2.445 13.55 2.586 13.7C2.586 13.7 2.747 13.8 2.93 13.88C2.93 13.88 3.112 13.96 3.302 14L6.328 14C6.412 14.18 6.505 14.35 6.609 14.52C6.609 14.52 6.714 14.68 6.823 14.84ZM2 4L14 4L14 3.5C14 3.292 13.96 3.096 13.88 2.914C13.88 2.914 13.8 2.732 13.7 2.573C13.7 2.573 13.56 2.438 13.43 2.302C13.43 2.302 13.27 2.195 13.09 2.117C13.09 2.117 12.9 2.039 12.71 2L3.5 2C3.302 2 3.112 2.039 2.93 2.117C2.93 2.117 2.747 2.195 2.586 2.302C2.586 2.302 2.445 2.438 2.305 2.573C2.305 2.573 2.195 2.732 2.117 2.914C2.117 2.914 2.039 3.096 2 3.292Z"/>'
$svg_window_template = '<path fill="@image.color1" d="M3.453 15C3.12 15 2.805 14.93 2.508 14.8C2.508 14.8 2.211 14.66 1.953 14.48C1.953 14.48 1.734 14.27 1.516 14.05C1.516 14.05 1.339 13.79 1.203 13.49C1.203 13.49 1.068 13.2 1 12.88L1 3.438C1 3.115 1.068 2.805 1.203 2.508C1.203 2.508 1.339 2.211 1.516 1.951C1.516 1.951 1.734 1.727 1.953 1.503C1.953 1.503 2.211 1.326 2.508 1.195C2.508 1.195 2.805 1.065 3.12 1L12.56 1C12.89 1 13.2 1.065 13.49 1.195C13.49 1.195 13.79 1.326 14.05 1.503C14.05 1.503 14.27 1.727 14.5 1.951C14.5 1.951 14.67 2.211 14.8 2.508C14.8 2.508 14.93 2.805 15 3.115L15 12.55C15 12.88 14.93 13.2 14.8 13.49C14.8 13.49 14.67 13.79 14.5 14.05C14.5 14.05 14.27 14.27 14.05 14.48C14.05 14.48 13.79 14.66 13.49 14.8C13.49 14.8 13.2 14.93 12.89 15ZM2 4L14 4L14 3.5C14 3.292 13.96 3.096 13.88 2.914C13.88 2.914 13.8 2.732 13.7 2.573C13.7 2.573 13.56 2.438 13.43 2.302C13.43 2.302 13.27 2.195 13.09 2.117C13.09 2.117 12.9 2.039 12.71 2L3.5 2C3.302 2 3.112 2.039 2.93 2.117C2.93 2.117 2.747 2.195 2.586 2.302C2.586 2.302 2.445 2.438 2.305 2.573C2.305 2.573 2.195 2.732 2.117 2.914C2.117 2.914 2.039 3.096 2 3.292ZM12.5 14C12.71 14 12.9 13.96 13.09 13.88C13.09 13.88 13.27 13.8 13.43 13.7C13.43 13.7 13.56 13.55 13.7 13.41C13.7 13.41 13.8 13.25 13.88 13.07C13.88 13.07 13.96 12.89 14 12.7L14 5L2 5L2 12.5C2 12.7 2.039 12.89 2.117 13.07C2.117 13.07 2.195 13.25 2.305 13.41C2.305 13.41 2.445 13.55 2.586 13.7C2.586 13.7 2.747 13.8 2.93 13.88C2.93 13.88 3.112 13.96 3.302 14Z"/>'

/*
	NOTE:
	image.color1 -> main color
	image.color2 -> accent color
	color3 -> fill color (none if dark theme is active)
	color_islight_WB -> theme color (white for light theme, black for dark theme)
*/

// Import icons
import 'icons/copy.nss'
import 'icons/cut.nss'
import 'icons/paste.nss'
import 'icons/paste_shortcut.nss'
import 'icons/copy_path.nss'
import 'icons/settings.nss'
import 'icons/task_manager.nss'
import 'icons/run_as_administrator.nss'
import 'icons/run_as_different_user.nss'
import 'icons/personalize.nss'
import 'icons/display_settings.nss'
import 'icons/pin.nss'
import 'icons/unpin.nss'
import 'icons/add_to_favorites.nss'
import 'icons/remove_from_favorites.nss'
import 'icons/delete.nss'
import 'icons/sort_by.nss'
import 'icons/group_by.nss'
import 'icons/view.nss'
import 'icons/view2.nss'
import 'icons/align_icons_to_grid.nss'
import 'icons/auto_arrange_icons.nss'
import 'icons/x.nss'
import 'icons/expand.nss'
import 'icons/expand.nss'
import 'icons/collapse.nss'
import 'icons/collapse.nss'
import 'icons/format.nss'
import 'icons/eject.nss'
import 'icons/content.nss'
import 'icons/details.nss'
import 'icons/extra_large_icons.nss'
import 'icons/large_icons.nss'
import 'icons/list.nss'
import 'icons/medium_icons.nss'
import 'icons/small_icons.nss'
import 'icons/install.nss'
import 'icons/select_all.nss'
import 'icons/invert_selection.nss'
import 'icons/select_none.nss'
import 'icons/share.nss'
import 'icons/mount.nss'
import 'icons/new.nss'
import 'icons/new_folder.nss'
import 'icons/new_file.nss'
import 'icons/open_folder.nss'
import 'icons/open_new_window.nss'
import 'icons/open_new_tab.nss'
import 'icons/open_spot_light.nss'
import 'icons/open_with.nss'
import 'icons/powershell.nss'
import 'icons/properties.nss'
import 'icons/restore.nss'
import 'icons/undo.nss'
import 'icons/redo.nss'
import 'icons/refresh.nss'
import 'icons/rename.nss'
import 'icons/rotate_left.nss'
import 'icons/rotate_right.nss'
import 'icons/set_as_background.nss'
import 'icons/next_desktop_background.nss'
import 'icons/desktop.nss'
import 'icons/restore_previous_versions.nss'
import 'icons/create_shortcut.nss'
import 'icons/bitlocker.nss'
import 'icons/show_file_extensions.nss'
import 'icons/show_hidden_files.nss'
import 'icons/compressed.nss'
import 'icons/more_options.nss'
import 'icons/burn_disc_image.nss'
import 'icons/cleanup.nss'
import 'icons/move_to.nss'
import 'icons/copy_to.nss'
import 'icons/pc.nss'
import 'icons/command_prompt.nss'
import 'icons/manage.nss'
import 'icons/edit.nss'
import 'icons/troubleshoot_compatibility.nss'
import 'icons/customize_this_folder.nss'
import 'icons/give_access_to.nss'
import 'icons/send_to.nss'
import 'icons/include_in_library.nss'
import 'icons/add_a_network_location.nss'
import 'icons/disconnect_network_drive.nss'
import 'icons/map_network_drive.nss'
import 'icons/make_available_offline.nss'
import 'icons/make_available_online.nss'
import 'icons/file_explorer.nss'
import 'icons/file_explorer_options.nss'
import 'icons/print.nss'
import 'icons/device_manager.nss'
import 'icons/disk_management.nss'
import 'icons/filter.nss'
import 'icons/window.nss'
import 'icons/code.nss'
import 'icons/reddit.nss'
import 'icons/cortana.nss'
import 'icons/nvidia.nss'