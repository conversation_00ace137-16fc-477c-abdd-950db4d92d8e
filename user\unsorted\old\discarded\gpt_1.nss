
@User1:
"""
any idea why this doesn't work when selecting multiple files?
```JS
// COLORS
$clr_blue   = #34b6ff
$clr_green  = #39c65a
$clr_purple = #a457ff

// ITEMS
$cmd_copy_filepath  = ['powershell', '-Command @sel("\\\"",",") | % { [System.IO.Path]::GetFullPath($_) } | Set-Clipboard']
$cmd_copy_location  = ['powershell', '-Command @sel("\\\"",",") | % { [System.IO.Path]::GetDirectoryName($_) } | Set-Clipboard']
$cmd_copy_filename  = ['powershell', '-Command @sel("\\\"",",") | % { [System.IO.Path]::GetFileName($_) } | Set-Clipboard']
$cmd_copy_basename  = ['powershell', '-Command @sel("\\\"",",") | % { [System.IO.Path]::GetFileNameWithoutExtension($_) } | Set-Clipboard']
$cmd_copy_content   = ['powershell', '-Command @sel("\\\"",",") | % { Get-Content $_ -Raw } | Set-Clipboard']
$cmd_copy_contents  = ['powershell', '-Command Set-Clipboard -Path (@sel("\\\"",",") | % { Get-ChildItem $_ -Force | % { $_.FullName } })']

// CREATE
menu(type='~taskbar' title="&Clipboard" mode="none|single|multi_unique|multi_single|multiple" image=["\uE11B", clr_blue]) {
    item(title="Copy Filepath" + "\t 1" cmd=cmd_copy_filepath[0] args=cmd_copy_filepath[1] window=hidden image=["\uE10E", clr_green])
    item(title="Copy Path"     + "\t 2" cmd=cmd_copy_location[0] args=cmd_copy_location[1] window=hidden image=["\uE10E", clr_green])
    separator
    item(title="Copy Filename" + "\t 3" cmd=cmd_copy_filename[0] args=cmd_copy_filename[1] window=hidden image=["\uE10E", clr_blue])
    item(title="Copy Name"     + "\t 4" cmd=cmd_copy_basename[0] args=cmd_copy_basename[1] window=hidden image=["\uE10E", clr_blue])
    separator
    item(title="Copy Content"  + "\t 5"  cmd=cmd_copy_content[0]  args=cmd_copy_content[1]  window=hidden image=["\uE1A3", clr_purple]  type="file" mode="multi_unique")
    item(title="Copy Contents" + "\t 6" cmd=cmd_copy_contents[0] args=cmd_copy_contents[1] window=hidden image=["\uE1A3", clr_purple] type="~file|dir|back.dir|drive|back.drive" mode="multiple")
}
```
"""

@User2:
"""
NS can give information to PS, but PS  cannot send anything to NS back
cmd=cmd_copy_filepath[0] send the information to the clipboard, not to NS

But the logic in your code can be easily achieved using an NS script
"""

@User1:
"""
the PS code works and correctly sends content to the clipboard, however the issue that i have is that the contextmenu itself doesn't appear when having multiple files selected. the variables only serves as temporary placeholders (with the purpose of making the code more readable). the issue are probably related to my lacking understanding on how 'type' and 'mode' works
"""


i don't think the underlying issue isn't with the PowerShell code itself, as it correctly copies content to the clipboard. Instead, the problem appears to be linked to how the context menu behaves when multiple files are selected. This suggests a potential misunderstanding of the 'type' and 'mode' properties in your script. These properties are crucial for determining how the context menu is displayed and function under different conditions, such as when selecting multiple files. A deeper dive into the documentation or examples regarding 'type' and 'mode' usage might provide clarity and help resolve the issue.

the issue seems to be with 'type' and 'mode' in your script, not the powershell code. these control how the context menu shows up, especially with multiple file selections. understanding how they work might fix the problem. looking into their documentation could help.

it looks like the 'type' and 'mode' settings in the script are causing the issue, not the powershell commands. i'm not fully clear on how these settings affect the context menu with multiple files. could you help me understand this better? any insights or pointers towards relevant documentation would be really helpful.

hey @user2, in a bit of a pickle with 'type' and 'mode'. thought i had it figured, but looks like i might've outsmarted myself. 🙃 reckon you could give a quick sanity check? could use a fresh perspective before i go down another rabbit hole. 🕳️🐇


hey, i might be missing a trick with 'type' and 'mode' here. i've gone through the docs but maybe i'm looking at it with potato glasses. 😅 any chance you could shed some light on this? your insights always hit different. 🧠💡 just trying to avoid a brain fart moment with nilesoft shell. thanks in advance! 🙌

might be overthinking 'type' and 'mode' here, or maybe just missing something. a bit stumped. your take on this could clear things up. keen to avoid another wild goose chase. 🤔👀


In grappling with 'type' and 'mode' here, I might've looped myself into confusion. Seems like I'm missing something that's likely staring me in the face.

The PowerShell script is functioning, but the issue seems to be with the context menu's non-response to multiple file selections. 

the PS code is functioning and correctly and sends content to the clipboard, the issue i'm having is that the contextmenu itself doesn't appear when having multiple files selected. i think there's an aspect/interplay of 'type' and 'mode' that i'm not fully grasping 

i've gone through the docs but maybe i'm looking at it with potato glasses

the PS code is functioning and correctly sends content to the clipboard, the issue i'm having is that the contextmenu itself doesn't appear when having multiple files selected. i think there's an aspect/interplay of 'type' and 'mode' that i'm not fully grasping, looking at it with potato glasses 

the PS code is functioning and correctly sends content to the clipboard, the issue i'm having is that the contextmenu itself doesn't appear when having multiple files selected. looking at it with my potato glasses, i think there's an aspect/interplay of 'type' and 'mode' that i'm not fully grasping 


btw, outside of the docs and the snippets-section, are any other resources for a more comprehensive demonstration of actual use-cases? i doubt there's established guidelines or best-practices, but it would be nice to have something that shows how everything is tied together


/advanced use-cases, best-practices or guidelines?

practical use-case resources that can be referenced for a more comprehensive demonstration of best-practices/guidelines for actual use-cases 


btw, outside of the docs, are any other resources that can be referenced for actual use-case examples best-practices/guidelines for actual use-cases 

btw, outside of the docs, are any other resources that demonstrates best-practices/guidelines for actual use-cases 

can be used as reference to understand the interplay between

proper use-case examples


covers that serves as a good reference or references which binds


i doubt there's any established guidelines or best practices, but 

btw, are there any established guidelines or best practices /references that combines


, looking at it with potato glasses 

maybe i'm looking at it with potato glasses, but 

btw, are there any guidelines/best practices/references


maybe i'm looking at it with potato glasses, 
might be missing something straightforward.



The PowerShell script is functional, yet the issue persists with the context menu's absence during multi-file selections. It's likely a nuanced misunderstanding of 'type' and 'mode' parameters. Any insights into their interplay? Might be overlooking something fundamental.













hey, i might be missing a trick with 'type' and 'mode' here. i've gone through the docs but maybe i'm looking at it with potato glasses