
//
$APP_USER_IRFANVIEW_DIR = '@user.desktop\my\flow\home\__GOTO__\Apps\app_irfanview\exe'
$APP_USER_IRFANVIEW_EXE = '@APP_USER_IRFANVIEW_DIR\i_view64.exe'
$APP_USER_IRFANVIEW_TIP = "..."+str.trimstart('@APP_USER_IRFANVIEW_EXE','@app.dir')

// context: directory
item(
    title  = ":  &Irfanview"
    keys   = "exe"
    type   = 'Desktop|Dir|Drive|Back.Dir|Back.Drive'
    args   = '/slideshow="@sel.dir"'
    //
    image  = APP_USER_IRFANVIEW_EXE
    tip    = [APP_USER_IRFANVIEW_TIP,TIP3,0.8]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_USER_IRFANVIEW_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_IRFANVIEW_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_IRFANVIEW_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_IRFANVIEW_DIR')),
    }
)
// Context: Taskbar
item(
    title  = ":  &Irfanview"
    keys   = "exe"
    type   = 'Taskbar'
    args   = ''
    //
    image  = APP_USER_IRFANVIEW_EXE
    tip    = [APP_USER_IRFANVIEW_TIP,TIP3,0.8]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_USER_IRFANVIEW_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_IRFANVIEW_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_IRFANVIEW_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_IRFANVIEW_DIR')),
    }
)
