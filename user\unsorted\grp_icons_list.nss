// common_applications.nss
// \uE18B keyboard
// \uE218 powershell
// menu(title="&Applications" type='Taskbar|Desktop|Back.Dir' image=[\uE11B, clr_purple] image-sel=[\uE11B, clr_sel]) {
// menu(title="&Applications" type='Taskbar|Desktop|Back.Dir' image=[\uE286, clr_purple] image-sel=[\uE286, clr_sel]) {
// menu(title="&Applications" type='Taskbar|Desktop|Back.Dir' image=[\uE274, clr_purple] image-sel=[\uE286, clr_sel]) {
// menu(title="&Applications" type='Taskbar|Desktop|Back.Dir' image=[\uE26B, clr_purple] image-sel=[\uE26B, clr_sel]) {


// CLEANUP
menu(type='Taskbar' expanded=true) {
    menu(title="&Glyphs" type='Taskbar' pos='Top' sep='Both' image=[icon_org, clr_white]) {
        // item(title="A" keys="&A" col vis='Label' sep)
        // item(title=['AlignLeft',"&A"] col vis='Label' sep)
        // item(title="&A" col vis='Label' align=2 sep)


        // item(vis='Label' title='.exe' sep col=1)
        item(vis='Static' title='.exe' image=["\uE16B", clr_white] sep col=1)
        item(image=["\uE16B", clr_soft] tip='E16B' cmd=command.copy("\uE16B") title="chrome.exe")
        item(image=["\uE16B", clr_soft] tip='E16B' cmd=command.copy("\uE16B") title="Photoshop.exe")
        item(image=["\uE16B", clr_soft] tip='E16B' cmd=command.copy("\uE16B") title="Bulk Rename Utility.exe")
        item(image=["\uE16B", clr_soft] tip='E16B' cmd=command.copy("\uE16B") title="Everything64.exe")
        item(image=["\uE16B", clr_soft] tip='E16B' cmd=command.copy("\uE16B") title="Code.exe")
        item(image=["\uE16B", clr_soft] tip='E16B' cmd=command.copy("\uE16B") title="notepad++.exe")
        item(image=["\uE16B", clr_soft] tip='E16B' cmd=command.copy("\uE16B") title="sublime_text.exe")
        item(image=["\uE16B", clr_soft] tip='E16B' cmd=command.copy("\uE16B") title="WinMergeU.exe")


        item(vis='Static' title='.app' image=["\uE16B", clr_white] sep col=1)
        item(image=["\uE16B", clr_soft] tip='E16B' cmd=command.copy("\uE16B") title="calc.exe")
        item(image=["\uE16B", clr_soft] tip='E16B' cmd=command.copy("\uE16B") title="cmd.exe")
        item(image=["\uE16B", clr_soft] tip='E16B' cmd=command.copy("\uE16B") title="mspaint.exe")
        item(image=["\uE16B", clr_soft] tip='E16B' cmd=command.copy("\uE16B") title="notepad.exe")
        item(image=["\uE16B", clr_soft] tip='E16B' cmd=command.copy("\uE16B") title="powershell.exe")
        item(image=["\uE16B", clr_soft] tip='E16B' cmd=command.copy("\uE16B") title="powershell_ise.exe")
        item(image=["\uE16B", clr_soft] tip='E16B' cmd=command.copy("\uE16B") title="regedit.exe")
        item(image=["\uE16B", clr_soft] tip='E16B' cmd=command.copy("\uE16B") title="msedge.exe")

        item(vis='Static' title='.control' image=["\uE16B", clr_white] sep col=1)
        item(image=app_osk tip='E16B' cmd=command.copy("\uE16B") title="osk.exe")
        item(image=app_resmon tip='E16B' cmd=command.copy("\uE16B") title="resmon.exe")
        item(image=app_control tip='E16B' cmd=command.copy("\uE16B") title="control.exe")


        // item(col vis='Label' title='.msc' sep)
        // item(vis='Label' title='01' sep col=1)
        // item(vis='Static' title='.msc' image=\uE0F sep col=1)
        item(vis='Static' title='.msc' image=["\uE0FA", clr_white] sep col=1)
        item(image=["\uE0FA", clr_soft] tip='E0FA' cmd=command.copy("\uE0FA") title="compmgmt.msc")
        item(image=["\uE0FA", clr_soft] tip='E0FA' cmd=command.copy("\uE0FA") title="devmgmt.msc")
        item(image=["\uE0FA", clr_soft] tip='E0FA' cmd=command.copy("\uE0FA") title="diskmgmt.msc")
        item(image=["\uE0FA", clr_soft] tip='E0FA' cmd=command.copy("\uE0FA") title="eventvwr.msc")

        // item(col vis='Label' title='.cpl' sep)
        item(vis='Static' title='.cpl' image=["\uE1E8", clr_white] sep col=1)
        // item(vis='Label' title='02' sep col=1)
        item(image=["\uE1E8", clr_soft, 77] tip='E1E8' cmd=command.copy("\uE1E8") title="mmsys.cpl")
        item(image=["\uE1E8", clr_soft, 77] tip='E1E8' cmd=command.copy("\uE1E8") title="ncpa.cpl")
        item(image=["\uE1E8", clr_soft, 77] tip='E1E8' cmd=command.copy("\uE1E8") title="powercfg.cpl")
        // item(col)




        // item(image=["\uE1C4", clr_white] tip='E1C4' cmd=command.copy("\uE1C4"))
        // item(image=["\uE1DE", clr_white] tip='E1DE' cmd=command.copy("\uE1DE"))
        // item(image=["\uE1E7", clr_white] tip='E1E7' cmd=command.copy("\uE1E7"))
        // item(image=["\uE1FC", clr_white] tip='E1FC' cmd=command.copy("\uE1FC"))
        // item(image=["\uE249", clr_white] tip='E249' cmd=command.copy("\uE249"))
        // // item(vis='Label')
        // // item(vis='Label')
        // // item(vis='Label')
        // // item(vis='Label')

        // item(col vis='Label' sep)
        // item(image=["\uE254", clr_white] tip='E254' cmd=command.copy("\uE254"))
        // item(image=["\uE71D", clr_white] tip='E71D' cmd=command.copy("\uE71D"))
        // item(image=["\uE770", clr_white] tip='E770' cmd=command.copy("\uE770"))
        // item(image=["\uE81C", clr_white] tip='E81C' cmd=command.copy("\uE81C"))
        // item(image=["\uE945", clr_white] tip='E945' cmd=command.copy("\uE945"))

//         //
//         // modify(type='Taskbar' image=clr_subtle where=this.id(id.task_manager))
//         // modify(type='Taskbar' image=clr_subtle where=this.id(id.taskbar_settings))
//         // modify(type='Taskbar' image=clr_subtle where=this.id(id.show_the_desktop))
//         // modify(type='Taskbar' image=clr_subtle where=this.id(id.cascade_windows))
//         // modify(type='Taskbar' image=clr_subtle where=this.id(id.show_windows_stacked))
//         // modify(type='Taskbar' image=clr_subtle where=this.id(id.show_windows_side_by_side))
//         // modify(type='Taskbar' image=clr_subtle where=this.id(id.minimize_all_windows))
//         // modify(type='Taskbar' image=clr_subtle where=this.id(id.restore_all_windows))
//         //
//         item(title='test for statement 2' cmd=msg(for(i=0, i< sel.count, sel[i]+"\n"))

//         // modify(type='Taskbar' where=this.id menu="Glyphs")
    }
}
