
/* menu */


menu(title="&Projects" type='Taskbar|Desktop|Back.Dir' image=[E167,WHITE1] image-sel=[E167,HOVER]  sep='None') {

    //
    $stp_path_1='@user.desktop/User/my\flow\home/__GOTO__/Apps\exe_everything.sublime-project'
    $stp_path_1='@user.desktop/User/my\flow\home/__GOTO__/Apps\app_everything\exe_everything.sublime-project'
    $stp_path_2='@user.desktop/User/my\flow\home/__GOTO__/Apps\exe_nilesoftshell\NilesoftShell_Filtered.sublime-project'
    $stp_path_3='@app.dir\PORTAL\JOBB\JOBB.sublime-project'
    $stp_path_4="C:/Users/<USER>/AppData/Roaming/_Sublime Text/Packages/Jorn_TextCommands/Jorn_TextCommands.sublime-project"
    //
    item(title="Sublime: &Projects"         image=[E17C,GREY]   vis='Static'           sep='Both' col)
    item(title="stp: exe_&everything"         image='@exe_sublime' image-sel='@exe_sublime' cmd='@stp_path_1' tip=path.full('@stp_path_1'))
    item(title="stp: Nilesoft&Shell_Filtered" image='@exe_sublime' image-sel='@exe_sublime' cmd='@stp_path_2' tip=path.full('@stp_path_2'))
    item(title="stp: &JOBB"                   image='@exe_sublime' image-sel='@exe_sublime' cmd='@stp_path_3' tip=path.full('@stp_path_3'))
    item(title="stp: &Jorn_TextCommands"      image='@exe_sublime' image-sel='@exe_sublime' cmd='@stp_path_4' tip=path.full('@stp_path_4'))
    separator()

    //
    $sync_path_1="C:/Users/<USER>/Sync/JORN/_3D/3D Prints"
    $sync_path_2="C:/Users/<USER>/Sync/JORN/UTILS"
    $sync_path_3="C:/Users/<USER>/Sync/JORN/Venners prosjekter"
    $sync_path_4="C:/Users/<USER>/Sync/JORN/Venners prosjekter/Kim - Ringerike Landskap"
    //
    item(title="Sync: &Common"              image=[E0E8,GREY]   vis='Static'           sep='Both' col)
    item(title="Sync: &3D-Prints"          image=[E12A,ORANGE] image-sel=[E12A,HOVER] cmd='@sync_path_1' tip=path.full('@sync_path_1'))
    item(title="Sync: &Utils"              image=[E09C,ORANGE] image-sel=[E09C,HOVER] cmd='@sync_path_2' tip=path.full('@sync_path_2'))
    item(title="Sync: &Venners-Prosjekter" image=[E107,ORANGE] image-sel=[E107,HOVER] cmd='@sync_path_3' tip=path.full('@sync_path_3'))
    item(title="Sync: &Kim"                image=[E107,ORANGE] image-sel=[E107,HOVER] cmd='@sync_path_4' tip=path.full('@sync_path_4'))
    separator()


    //


    //

    // separator()

    // item(title="&Dropbox"      image=[E09C,BLUE]   image-sel=[E09C,HOVER] cmd='@dir_dropbox'                 )
    // item(title="&Onedrive"     image=[E09C,BLUE]   image-sel=[E09C,HOVER] cmd='@dir_onedrive'                )
    // item(title="&Google Drive" image=[E09C,BLUE]   image-sel=[E09C,HOVER] cmd='@dir_gdrive'                  )
    // separator()

    // item(title="&USERPROFILE"  image=[E09F,GREY]   image-sel=[E09F,HOVER] cmd=user.directory                 )
    // item(title="&APPDATA"      image=[E09E,GREY]   image-sel=[E09E,HOVER] cmd=user.appdata                   )
    // item(title="&Recycle Bin"  image=[E0B4,GREY]   image-sel=[E0B4,HOVER] cmd='@DIR_SYS_RECYCLEBIN'             )
    // separator()

    // item(title="&This PC"      image=[E1D9,GREEN]  image-sel=[E1D9,HOVER] cmd='@DIR_SYS_THISPC'                 )
    // item(title="&Downloads"    image=[E0BD,GREEN]  image-sel=[E0BD,HOVER] cmd=user.downloads                 )
    // item(title="&Desktop"      image=[E1A0,GREEN]  image-sel=[E1A0,HOVER] cmd=user.desktop                   )
    // separator()

    // item(title="&Sublime Text" image=[E0E0,PURPLE] image-sel=[E0E0,HOVER] cmd=(user.appdata+'/Sublime Text') )
    // item(title="&Everything"   image=[E0E0,PURPLE] image-sel=[E0E0,HOVER] cmd=(user.appdata+'/Everything')   )
    // separator()

    // item(title="&PRJ/GIT/JHP"  image=[E22C,WHITE]  image-sel=[E22C,HOVER] cmd=(user.desktop+'/PRJ/GIT/JHP')  )
}

// GOTO

    // item(title="&Sync: UTILS" image=[E09C,ORANGE] image-sel=[E09C,HOVER] cmd=if(process.name=="explorer", '@dir_sync/JORN/UTILS', command.navigate('@dir_sync/JORN/UTILS')))
    // item(title="&Meg - 3D Prints" image=[E12A,ORANGE] image-sel=[E12A,HOVER] cmd=if(process.name=="explorer", '@dir_sync/JORN/_3D/3D Prints', command.navigate('@dir_sync/JORN/_3D/3D Prints')))
    // item(title="&Kim - Ringerike Landskap" image=[E254,ORANGE] image-sel=[E254,HOVER] cmd=if(process.name=="explorer", '@dir_sync/JORN/Venners prosjekter/Kim - Ringerike Landskap', command.navigate('@dir_sync/JORN/Venners prosjekter/Kim - Ringerike Landskap')))
    // item(title="&Venners Prosjekter" image=[E254,ORANGE] image-sel=[E254,HOVER] cmd=if(process.name=="explorer", '@dir_sync/JORN/Venners prosjekter', command.navigate('@dir_sync/JORN/Venners prosjekter')))
