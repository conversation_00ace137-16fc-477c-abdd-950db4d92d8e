
//
$APP_USER_PROCEXP_DIR = '@user.desktop\my\flow\home\__GOTO__\Apps\grp_sysinternalssuite\app_procexp\exe'
$APP_USER_PROCEXP_EXE = '@APP_USER_PROCEXP_DIR\procexp64.exe'
$APP_USER_PROCEXP_TIP = "..."+str.trimstart('@APP_USER_PROCEXP_EXE','@app.dir')

// -> Diskmon
item(
    title=":  &Procexp"
    keys="exe"
    type='Taskbar|File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    //
    image=APP_USER_PROCEXP_EXE
    tip=[APP_USER_PROCEXP_TIP,TIP3,0.5]
    //
    admin=keys.rbutton()
    cmd=if(KEYS_EXE_OPEN_EXE,(APP_USER_PROCEXP_EXE))
    args='/AcceptEula'
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_PROCEXP_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_PROCEXP_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_PROCEXP_DIR')),
    }
)
