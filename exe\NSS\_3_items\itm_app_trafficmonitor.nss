
//
$APP_USER_TRAFFICMONITOR_DIR = '@user.desktop\my\flow\home\__GOTO__\Apps\app_trafficmonitor\exe'
$APP_USER_TRAFFICMONITOR_EXE = '@APP_USER_TRAFFICMONITOR_DIR\TrafficMonitor.exe'
$APP_USER_TRAFFICMONITOR_TIP = "..."+str.trimstart('@APP_USER_TRAFFICMONITOR_EXE','@app.dir')

// -> Diskmon
item(
    title=":  &TrafficMonitor"
    keys="exe"
    type='Taskbar|File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    //
    image=APP_USER_TRAFFICMONITOR_EXE
    tip=[APP_USER_TRAFFICMONITOR_TIP,TIP3,0.5]
    //
    admin=keys.rbutton()
    cmd=if(KEYS_EXE_OPEN_EXE,(APP_USER_TRAFFICMONITOR_EXE))
    args='/AcceptEula'
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_TRAFFICMONITOR_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_TRAFFICMONITOR_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_TRAFFICMONITOR_DIR')),
    }
)
