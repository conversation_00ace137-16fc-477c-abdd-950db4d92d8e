
//
$APP_USER_TCPVIEW_DIR = '@user.desktop\my\flow\home\__GOTO__\Apps\grp_sysinternalssuite\app_tcpview\exe'
$APP_USER_TCPVIEW_EXE = '@APP_USER_TCPVIEW_DIR\tcpview64.exe'
$APP_USER_TCPVIEW_TIP = "..."+str.trimstart('@APP_USER_TCPVIEW_EXE','@app.dir')

//
item(
    title=":  &Tcpview"
    keys="exe"
    type='Taskbar|File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    //
    image=APP_USER_TCPVIEW_EXE
    tip=[APP_USER_TCPVIEW_TIP,TIP3,0.5]
    //
    admin=keys.rbutton()
    cmd=if(KEYS_EXE_OPEN_EXE,(APP_USER_TCPVIEW_EXE))
    args='/AcceptEula'
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_TCPVIEW_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_TCPVIEW_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_TCPVIEW_DIR')),
    }
)
