
// =============================================================================
// PRI:2 [15:25] -> 29.05.2024: python cli utils
- need to define a template cli.py
  - decide on library
    - perplexity?

// --- cli.py ---




// =============================================================================
// PRI:3 - [15:20] -> 29.05.2024: integration with py cli
- generate textfile from clipboard (with opt-prompt:cli-options)
- section for git-commands (e.g. `git add *`)
  - or adding/committing/pushing selected file(s) (auto-msg)
    - automatically generate commit-message based on context

- autogenerate-shortcut: option for auto-checkin (to git-repo)


// =============================================================================
// PRI:4 - [11:23] -> 11.06.2024: open multiple files in app
// for(i=0, i< sel.count, '"@sel[i]" ')
menu(where=sel.count>0 mode="multiple" title='Convert' sep="both" image= \uE14A)
{
    menu(title='Audio' image=\uE1F4)
    {
        item(title='Convert (@sel.count) files to OggOpus' image=inherit cmd=
            for(i=0, i< sel.count) {
                if(str.equals(sel[i].file.ext,[".aac",".flac",".m4a",".mp3",".ogg",".wav",".wma"]),
                    $filePath = sel[i].directory
                    $fileName= sel[i].title
                    $newFileName = fileName + '.ogg'
                    $newFilePath = '"@filePath\@newFileName"'

                    msg('ffmpeg -i @sel[i].path.quote -c:a libopus -b:a 510k @newFilePath')
                )
            }
        )
    }
}




