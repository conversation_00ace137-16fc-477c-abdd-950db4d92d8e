
//
$PY_VIDEOSPLITTER_DIR = '@user.desktop\my\flow\home\__GOTO__\Scripts\Python\Py_Cli_Utils\py__VideoSplitter'
$PY_VIDEOSPLITTER_EXE = '@PY_VIDEOSPLITTER_DIR\venv\Scripts\python.exe'
$PY_VIDEOSPLITTER_APP = '@PY_VIDEOSPLITTER_DIR\src\main.py'
//


// Context: Explorer
$PY_VIDEOSPLITTER_ARGS = '"@sel.file" --prompt'
item(
    title="&VideoSplitter"
    keys="py"
    type='File'
    where=str.equals(sel.file.ext,[".m4a",".webm",".wmv",".wav",".f4v",".mov",".mkv",".mp4"])
    //
    image=[E17C,GREY] image-sel=[E17C,PURPLE]
    tip=['"@PY_VIDEOSPLITTER_APP" @PY_VIDEOSPLITTER_ARGS',TIP3,0.75]
    //
    admin=keys.rbutton()
    cmd=if(KEYS_EXE_OPEN_EXE,('"@PY_VIDEOSPLITTER_EXE"'))
    args='"@PY_VIDEOSPLITTER_APP" @PY_VIDEOSPLITTER_ARGS'
    //
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@PY_VIDEOSPLITTER_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@PY_VIDEOSPLITTER_APP')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@PY_VIDEOSPLITTER_DIR')),
    }
)


// Context: Taskbar
$PY_VIDEOSPLITTER_TASKBAR = ' --prompt'
item(
    title="&VideoSplitter"
    keys="py"
    type='Taskbar'
    //
    image=[E17C,GREY]
    image-sel=[E17C,PURPLE]
    //
    admin=keys.rbutton()
    cmd=if(KEYS_EXE_OPEN_EXE,('"@PY_VIDEOSPLITTER_EXE"'))
    args='"@PY_VIDEOSPLITTER_APP" @PY_VIDEOSPLITTER_TASKBAR'
    tip=['"@PY_VIDEOSPLITTER_APP" @PY_VIDEOSPLITTER_TASKBAR',TIP3,0.75]
    //
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@PY_VIDEOSPLITTER_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@PY_VIDEOSPLITTER_APP')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@PY_VIDEOSPLITTER_DIR')),
    }
)
