
//
$APP_CLAUDE_DIR = '@user.localappdata\AnthropicClaude'
$APP_CLAUDE_EXE = '@APP_CLAUDE_DIR\claude.exe'
$APP_CLAUDE_TIP = "..."+str.trimstart('@APP_CLAUDE_EXE','@app.dir')
//
$APP_CLAUDE_DIR_CFG = '@user.appdata\Claude'
$APP_CLAUDE_DIR_NSS = '@app.dir\NSS\_3_items\user_apps'
$APP_CLAUDE_DIR_SRC = '@user.desktop\my\flow\home\__GOTO__\Apps\app_claude'


// Context: File
item(
    title  = ":  &<PERSON>"
    keys   = "exe"
    type   = 'File'
    args   = '"@sel.file"'
    // where  = str.equals(sel.file.ext,[".max"])
    //
    image  = APP_CLAUDE_EXE
    tip    = [APP_CLAUDE_TIP,TIP3,1.0]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_CLAUDE_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_CLAUDE_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_CLAUDE_EXE')),
        cmd=if(KEYS_EXE_GOTO_CFG,('@APP_CLAUDE_DIR_CFG')),
        cmd=if(KEYS_EXE_GOTO_NSS,('@APP_CLAUDE_DIR_NSS')),
        cmd=if(KEYS_EXE_GOTO_SRC,('@APP_CLAUDE_DIR_SRC')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_CLAUDE_DIR')),
    }
)
// Context: Directory
item(
    title  = ":  &Claude"
    keys   = "exe"
    type   = 'Desktop|Dir|Drive|Back.Dir|Back.Drive'
    args   = '--local="@sel.dir"'
    //
    image  = APP_CLAUDE_EXE
    tip    = [APP_CLAUDE_TIP,TIP3,1.0]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_CLAUDE_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_CLAUDE_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_CLAUDE_EXE')),
        cmd=if(KEYS_EXE_GOTO_CFG,('@APP_CLAUDE_DIR_CFG')),
        cmd=if(KEYS_EXE_GOTO_NSS,('@APP_CLAUDE_DIR_NSS')),
        cmd=if(KEYS_EXE_GOTO_SRC,('@APP_CLAUDE_DIR_SRC')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_CLAUDE_DIR')),
    }
)
// Context: Taskbar
item(
    title  = ":  &Claude"
    keys   = "exe"
    type   = 'Taskbar'
    args   = ''
    //
    image  = APP_CLAUDE_EXE
    tip    = [APP_CLAUDE_TIP,TIP3,1.0]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_CLAUDE_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_CLAUDE_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_CLAUDE_EXE')),
        cmd=if(KEYS_EXE_GOTO_CFG,('@APP_CLAUDE_DIR_CFG')),
        cmd=if(KEYS_EXE_GOTO_NSS,('@APP_CLAUDE_DIR_NSS')),
        cmd=if(KEYS_EXE_GOTO_SRC,('@APP_CLAUDE_DIR_SRC')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_CLAUDE_DIR')),
    }
)
