
/* item: singles/apps/filezilla */

//
$APP_FILEZILLA_DIR = '@app.dir\PORTAL\APPS\app_filezilla\exe'
$APP_FILEZILLA_EXE = '@APP_FILEZILLA_DIR\filezilla.exe'
$APP_FILEZILLA_TIP = "..."+str.trimstart('@APP_FILEZILLA_EXE','@app.dir')

// context: directory
item(
    title  = ":  &FileZilla"
    keys   = "exe"
    type   = 'Desktop|Dir|Drive|Back.Dir|Back.Drive'
    args   = '--local="@sel.dir"'
    //
    image  = APP_FILEZILLA_EXE
    tip    = [APP_FILEZILLA_TIP,TIP3,1.0]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_FILEZILLA_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_FILEZILLA_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_FILEZILLA_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_FILEZILLA_DIR')),
    }
)
// Context: Taskbar
item(
    title  = ":  &FileZilla"
    keys   = "exe"
    type   = 'Taskbar'
    args   = ''
    //
    image  = APP_FILEZILLA_EXE
    tip    = [APP_FILEZILLA_TIP,TIP3,1.0]
    //
    admin  = keys.rbutton()
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_FILEZILLA_EXE"'))
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_FILEZILLA_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_FILEZILLA_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_FILEZILLA_DIR')),
    }
)
