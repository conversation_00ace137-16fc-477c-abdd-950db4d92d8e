<!-- ======================================================= -->
<!-- [12:20] -> 30.11.2024:  -->
<!-- "https://chatgpt.com/g/g-plIy0RAhw-art-snob/c/66f86e41-c824-8008-a454-898d5e366795" -->

he structures his code as a simply as possible while maintaining modularity and flexibility. the comments should be expressed in a way that makes it possible to understand their meaning by just casting a quick glance. in order to achieve this it could be a good idea to approach it like you would when expressing code with as elegant syntax as possible. the goal is basically to express this (as a the most easily readable syntax you can imagine):


would you be so kind and help improve the comments describing the key combinations in the following .nss file (def_keys.nss)?
```nss
/*
    :: key-combinations for executables

    :: usage:
        cmd=if(KEYS_EXE_OPEN_EXE,('"@sys.dir\System32\cmd.exe"')),
        commands{
           cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@sys.dir\System32\')),
           cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@sys.dir\System32\cmd.exe')),
           cmd=if(KEYS_EXE_OPEN_DIR,('"@sys.dir\System32\"')),
        }
*/
$KEYS_EXE_ADMIN    = key.rbutton() AND !key(key.alt)
$KEYS_EXE_COPY_DIR = key(key.control, key.lbutton) AND !key(key.alt) // [ctrl & leftclick] -[alt]
$KEYS_EXE_COPY_EXE = key(key.control, key.rbutton) AND !key(key.alt) // [ctrl & rightclick] -[alt]
$KEYS_EXE_GOTO_CFG = key(key.alt, key.lbutton) AND (key.control() AND key.shift()) // [ctrl & shift & alt & leftclick]
$KEYS_EXE_GOTO_NSS = key(key.alt, key.rbutton) AND (key.control() AND key.shift()) // [ctrl & shift & alt & rightclick]
$KEYS_EXE_GOTO_SRC = key(key.alt, key.rbutton) AND !(key.control() AND key.shift()) // [alt & rightclick] -[ctrl & shift]

$KEYS_EXE_OPEN_DIR = key(key.lbutton AND key.shift) AND !key(key.alt) // [shift + leftclick] - [alt]

$KEYS_EXE_OPEN_EXE = key.lbutton() AND (!key(key.shift) AND !key(key.control) AND !key(key.alt)) // left & not(shift | ctrl | alt)


$KEYS_EXE_ADMIN    = // [right - alt]
$KEYS_EXE_COPY_DIR = // [ctrl + left - alt]
$KEYS_EXE_COPY_EXE = // [ctrl + right - alt]
$KEYS_EXE_GOTO_CFG = // [ctrl + shift + alt + left]
$KEYS_EXE_GOTO_NSS = // [alt + right & ctrl + shift]
$KEYS_EXE_GOTO_SRC = // [alt + right - (ctrl & shift)]
$KEYS_EXE_OPEN_DIR = // [shift + left/right] - [alt]
$KEYS_EXE_OPEN_EXE = // [-shift, -ctrl, -alt]

/*
    :: context menu items with keyboard modifiers
*/
```nss
// right-click, no alt
// ctrl + left-click, no alt
// ctrl + right-click, no alt
// alt + left-click, with ctrl + shift
// alt + right-click, with ctrl + shift
// alt + right-click, no ctrl + shift
// shift + left/right-click, no alt
// no shift, no ctrl, no alt
// core
$APP_CURSOR_DIR = '@user.desktop\User\Nas_Flow_Jorn\__GOTO__\Apps\app_cursor\exe'
$APP_CURSOR_EXE = '@APP_CURSOR_DIR\cursor.exe'
$APP_CURSOR_TIP = "..."+str.trimstart('@APP_CURSOR_EXE','@app.dir')
// user
$APP_CURSOR_DIR_CFG = '@user.desktop\User\Nas_Flow_Jorn\__GOTO__\Apps\app_cursor\data'
$APP_CURSOR_DIR_NSS = '@app.dir\NSS\_3_items\user_apps'
$APP_CURSOR_DIR_SRC = '@user.desktop\User\Nas_Flow_Jorn\__GOTO__\Apps\app_cursor'
$APP_CURSOR_DIR_USR = '@user.desktop\User\Nas_Flow_Jorn\__GOTO__\Apps\app_cursor\user'
// context: file
item(
    title  = ":  &Cursor"
    keys   = "exe"
    type   = 'File'
    args   = '"@sel.file"'
    //
    image  = APP_CURSOR_EXE
    tip    = [APP_CURSOR_TIP,TIP3,1.0]
    //
    admin  = if(KEYS_EXE_ADMIN,('"@APP_CURSOR_EXE"')) // right & not(alt)
    cmd    = if(KEYS_EXE_OPEN_EXE,('"@APP_CURSOR_EXE"')) // not(shift | ctrl | alt)
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_CURSOR_DIR')), // (ctrl + left) & not(alt)
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_CURSOR_EXE')), ctrl + left-click, no alt
        cmd=if(KEYS_EXE_GOTO_CFG,('@APP_CURSOR_DIR_CFG')),
        cmd=if(KEYS_EXE_GOTO_NSS,('@APP_CURSOR_DIR_NSS')),
        cmd=if(KEYS_EXE_GOTO_SRC,('@APP_CURSOR_DIR_SRC')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_CURSOR_DIR')), // // (shift+left || shift+right) & not(alt)
    }
)
// ...

```
$KEYS_EXE_ADMIN    = key.rbutton() AND !key(key.alt)
$KEYS_EXE_COPY_DIR = key(key.control, key.lbutton) AND !key(key.alt) // ctrl + left-click, no alt
$KEYS_EXE_COPY_EXE = key(key.control, key.rbutton) AND !key(key.alt) // ctrl + right-click, no alt
$KEYS_EXE_GOTO_CFG = key(key.alt, key.lbutton) AND (key.control() AND key.shift()) // alt + left-click, with ctrl + shift
$KEYS_EXE_GOTO_NSS = key(key.alt, key.rbutton) AND (key.control() AND key.shift()) // alt + right-click, with ctrl + shift
$KEYS_EXE_GOTO_SRC = key(key.alt, key.rbutton) AND !(key.control() AND key.shift()) // alt + right-click, no ctrl + shift
$KEYS_EXE_OPEN_DIR = (key(key.shift, key.lbutton) OR key(key.shift, key.rbutton)) AND !key(key.alt) // shift + left/right-click, no alt
$KEYS_EXE_OPEN_EXE = (!key(key.shift) AND !key(key.control) AND !key(key.alt)) // no shift, no ctrl, no alt

the comments should be expressed in a way that makes it possible to understand their meaning by just casting a quick glance. in order to achieve this it could be a good idea to approach it like you would when expressing code (with as elegant syntax as possible):

for context, the keys are used as keyboard modifiers in relation to a context manager - here's how it's used (itm_app_cursor.nss):

---

```nss
/*
    :: key-combinations for executables

    :: usage:
        cmd=if(KEYS_EXE_OPEN_EXE,('"@sys.dir\System32\cmd.exe"')),
        commands{
           cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@sys.dir\System32\')),
           cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@sys.dir\System32\cmd.exe')),
           cmd=if(KEYS_EXE_OPEN_DIR,('"@sys.dir\System32\"')),
        }
*/
$KEYS_EXE_ADMIN    = key.rbutton() AND !key(key.alt)
$KEYS_EXE_COPY_DIR = key(key.control, key.lbutton) AND !key(key.alt) // (ctrl + left) & not(alt)
$KEYS_EXE_COPY_EXE = key(key.control, key.rbutton) AND !key(key.alt) // (ctrl + right) & not(alt)
$KEYS_EXE_GOTO_CFG = key(key.alt, key.lbutton) AND (key.control() AND key.shift()) // (alt + left) & (ctrl + shift)
$KEYS_EXE_GOTO_NSS = key(key.alt, key.rbutton) AND (key.control() AND key.shift()) // (alt + right) & (ctrl + shift)
$KEYS_EXE_GOTO_SRC = key(key.alt, key.rbutton) AND !(key.control() AND key.shift()) // (alt + right & not(ctrl+shift)
$KEYS_EXE_OPEN_DIR = (key(key.shift, key.lbutton) OR key(key.shift, key.rbutton)) and !key(key.alt) // (shift+left || shift+right) & not(alt)
$KEYS_EXE_OPEN_EXE = (!key(key.shift) AND !key(key.control) AND !key(key.alt)) // not(shift | ctrl | alt)
```


# context:
```nss
$KEYS_EXE_ADMIN    = key.rbutton() AND !key(key.alt)
$KEYS_EXE_COPY_DIR = key(key.control, key.lbutton) AND !key(key.alt) // (ctrl + left) & not(alt)
$KEYS_EXE_COPY_EXE = key(key.control, key.rbutton) AND !key(key.alt) // (ctrl + right) & not(alt)
$KEYS_EXE_GOTO_CFG = key(key.alt, key.lbutton) AND (key.control() AND key.shift()) // (alt + left) & (ctrl + shift)
$KEYS_EXE_GOTO_NSS = key(key.alt, key.rbutton) AND (key.control() AND key.shift()) // (alt + right) & (ctrl + shift)
$KEYS_EXE_GOTO_SRC = key(key.alt, key.rbutton) AND !(key.control() AND key.shift()) // (alt + right & not(ctrl+shift)
$KEYS_EXE_OPEN_DIR = (key(key.shift, key.lbutton) OR key(key.shift, key.rbutton)) and !key(key.alt) // (shift+left || shift+right) & not(alt)
$KEYS_EXE_OPEN_EXE = (!key(key.shift) AND !key(key.control) AND !key(key.alt)) // not(shift | ctrl | alt)
```

```nss
// (ctrl + left) & not(alt)
// (ctrl + right) & not(alt)
// (alt + left) & (ctrl + shift)
// (alt + right) & (ctrl + shift)
// (alt + right & not(ctrl+shift)
// (shift+left || shift+right) & not(alt)
// not(shift | ctrl | alt)
```

```nss
// Right-click without Alt
// Ctrl + Left-click (no Alt)
// Ctrl + Right-click (no Alt)
// Alt + Left-click & Ctrl+Shift
// Alt + Right-click & Ctrl+Shift
// Alt + Right-click (no Ctrl+Shift)
// Shift + (Left || Right) (no Alt)
// No modifiers (Shift | Ctrl | Alt)
```


```nss
// (right-click, no alt)
// (ctrl + left-click, no alt)
// (ctrl + right-click, no alt)
// (alt + left-click, with ctrl + shift)
// (alt + right-click, with ctrl + shift)
// (alt + right-click, no ctrl + shift)
// (shift + left/right-click, no alt)
// (no shift, no ctrl, no alt)
```

```nss
// right-click, no alt
// ctrl + left-click, no alt
// ctrl + right-click, no alt
// alt + left-click, with ctrl + shift
// alt + right-click, with ctrl + shift
// alt + right-click, no ctrl + shift
// shift + left/right-click, no alt
// no shift, no ctrl, no alt
```

```nss
// [rbutton - alt]
// [ctrl + lbutton - alt]
// [ctrl + rbutton - alt]
// [alt + lbutton & ctrl + shift]
// [alt + rbutton & ctrl + shift]
// [alt + rbutton - (ctrl & shift)]
// [shift + lbutton | rbutton - alt]
// [-shift, -ctrl, -alt]
```
