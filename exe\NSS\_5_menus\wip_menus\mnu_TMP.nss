
//
$BG_COLOR = #0B1127 // background
$FG_COLOR = #FFFFFF // foreground

$YELLOW1 = #FFE565
$YELLOW2 = #FFD400
$YELLOW3 = #6E5D0C

$BLUE1 = #99C7FF
$BLUE2 = #3290FF
$BLUE3 = #145FB7

$GREEN1 = #75EF89
$GREEN2 = #32CC4C
$GREEN3 = #2D6B38

$GREY1 = #C1A3A3
$GREY2 = #7F7F7F
$GREY3 = #4C4C4C

$ORANGE1 = #FFCC99
$ORANGE2 = #FF9932
$ORANGE3 = #8E4F0F

$PINK1 = #FF65FE
$PINK2 = #FF00FE
$PINK3 = #890F89

$PURPLE1 = #C184FF
$PURPLE2 = #7F00FF
$PURPLE3 = #4C0F89

$RED1 = #FF7575
$RED2 = #FF0000
$RED3 = #890F0F

$WHITE1 = #FFFFFF
$WHITE2 = #8B93B1
$WHITE3 = #7C839E
$WHITE4 = #717276
$WHITE5 = #5E5E5E

$COLOR1A = #1A377D
$COLOR1B = #285CF1
$COLOR1C = #3893FF

//
menu(title="&TMP" type='Taskbar|Desktop|Drive' image=[E122,BLUE] image-sel=[E123,HOVER] sep='None') {

    //
    item(title="tooltips" image=[E0E0,BLUE] vis='Static' sep='both' )
    //
    item(title="primary" keys="/" image=[E0E8,GREY] image-sel=[E0E8,BLUE] tip=["tip.primary", tip.primary, 0.1] commands{cmd=if(keys.rbutton(), clipboard.set(app.dir))})
    item(title="info"    keys="/" image=[E0E8,GREY] image-sel=[E0E8,BLUE] tip=["tip.info",    tip.info,    0.1] commands{cmd=if(keys.rbutton(), clipboard.set(app.dir))})
    item(title="success" keys="/" image=[E0E8,GREY] image-sel=[E0E8,BLUE] tip=["tip.success", tip.success, 0.1] commands{cmd=if(keys.rbutton(), clipboard.set(app.dir))})
    item(title="warning" keys="/" image=[E0E8,GREY] image-sel=[E0E8,BLUE] tip=["tip.warning", tip.warning, 0.1] commands{cmd=if(keys.rbutton(), clipboard.set(app.dir))})
    item(title="danger"  keys="/" image=[E0E8,GREY] image-sel=[E0E8,BLUE] tip=["tip.danger",  tip.danger,  0.1] commands{cmd=if(keys.rbutton(), clipboard.set(app.dir))})
    separator()
    //
    item(title="primary" keys="/" image=[E0E8,GREY] image-sel=[E0E8,BLUE] tip=["@TIP1", '@TIP1', 0.1] commands{cmd=if(keys.rbutton(), clipboard.set(app.dir))})
    item(title="info"    keys="/" image=[E0E8,GREY] image-sel=[E0E8,BLUE] tip=["@TIP2", '@TIP2', 0.1] commands{cmd=if(keys.rbutton(), clipboard.set(app.dir))})
    item(title="success" keys="/" image=[E0E8,GREY] image-sel=[E0E8,BLUE] tip=["@TIP3", '@TIP3', 0.1] commands{cmd=if(keys.rbutton(), clipboard.set(app.dir))})
    item(title="warning" keys="/" image=[E0E8,GREY] image-sel=[E0E8,BLUE] tip=["@TIP4", '@TIP4', 0.1] commands{cmd=if(keys.rbutton(), clipboard.set(app.dir))})
    item(title="danger"  keys="/" image=[E0E8,GREY] image-sel=[E0E8,BLUE] tip=["@TIP5", '@TIP5', 0.1] commands{cmd=if(keys.rbutton(), clipboard.set(app.dir))})
    separator()
    //
    item(column vis='Static' image=[E00A,DARK])
    item(title="YELLOW1" image=[E0A4,'@YELLOW1'] image-sel=[E0A4,HOVER] cmd=command.copy('"@YELLOW1"'))
    item(title="YELLOW2" image=[E0A4,'@YELLOW2'] image-sel=[E0A4,HOVER] cmd=command.copy('"@YELLOW2"'))
    item(title="YELLOW3" image=[E0A4,'@YELLOW3'] image-sel=[E0A4,HOVER] cmd=command.copy('"@YELLOW3"'))
    item(title="BLUE1"   image=[E0A4,'@BLUE1']   image-sel=[E0A4,HOVER] cmd=command.copy('"@BLUE1"'))
    item(title="BLUE2"   image=[E0A4,'@BLUE2']   image-sel=[E0A4,HOVER] cmd=command.copy('"@BLUE2"'))
    item(title="BLUE3"   image=[E0A4,'@BLUE3']   image-sel=[E0A4,HOVER] cmd=command.copy('"@BLUE3"'))
    item(title="GREEN1"  image=[E0A4,'@GREEN1']  image-sel=[E0A4,HOVER] cmd=command.copy('"@GREEN1"'))
    item(title="GREEN2"  image=[E0A4,'@GREEN2']  image-sel=[E0A4,HOVER] cmd=command.copy('"@GREEN2"'))
    item(title="GREEN3"  image=[E0A4,'@GREEN3']  image-sel=[E0A4,HOVER] cmd=command.copy('"@GREEN3"'))
    item(title="GREY1"   image=[E0A4,'@GREY1']   image-sel=[E0A4,HOVER] cmd=command.copy('"@GREY1"'))
    item(title="GREY2"   image=[E0A4,'@GREY2']   image-sel=[E0A4,HOVER] cmd=command.copy('"@GREY2"'))
    item(title="GREY3"   image=[E0A4,'@GREY3']   image-sel=[E0A4,HOVER] cmd=command.copy('"@GREY3"'))
    item(title="ORANGE1" image=[E0A4,'@ORANGE1'] image-sel=[E0A4,HOVER] cmd=command.copy('"@ORANGE1"'))
    item(title="ORANGE2" image=[E0A4,'@ORANGE2'] image-sel=[E0A4,HOVER] cmd=command.copy('"@ORANGE2"'))
    item(title="ORANGE3" image=[E0A4,'@ORANGE3'] image-sel=[E0A4,HOVER] cmd=command.copy('"@ORANGE3"'))
    item(title="PINK1"   image=[E0A4,'@PINK1']   image-sel=[E0A4,HOVER] cmd=command.copy('"@PINK1"'))
    item(title="PINK2"   image=[E0A4,'@PINK2']   image-sel=[E0A4,HOVER] cmd=command.copy('"@PINK2"'))
    item(title="PINK3"   image=[E0A4,'@PINK3']   image-sel=[E0A4,HOVER] cmd=command.copy('"@PINK3"'))
    item(title="PURPLE1" image=[E0A4,'@PURPLE1'] image-sel=[E0A4,HOVER] cmd=command.copy('"@PURPLE1"'))
    item(title="PURPLE2" image=[E0A4,'@PURPLE2'] image-sel=[E0A4,HOVER] cmd=command.copy('"@PURPLE2"'))
    item(title="PURPLE3" image=[E0A4,'@PURPLE3'] image-sel=[E0A4,HOVER] cmd=command.copy('"@PURPLE3"'))
    item(title="RED1"    image=[E0A4,'@RED1']    image-sel=[E0A4,HOVER] cmd=command.copy('"@RED1"'))
    item(title="RED2"    image=[E0A4,'@RED2']    image-sel=[E0A4,HOVER] cmd=command.copy('"@RED2"'))
    item(title="RED3"    image=[E0A4,'@RED3']    image-sel=[E0A4,HOVER] cmd=command.copy('"@RED3"'))
    item(title="WHITE1"  image=[E0A4,'@WHITE1']  image-sel=[E0A4,HOVER] cmd=command.copy('"@WHITE1"'))
    item(title="WHITE2"  image=[E0A4,'@WHITE2']  image-sel=[E0A4,HOVER] cmd=command.copy('"@WHITE2"'))
    item(title="WHITE3"  image=[E0A4,'@WHITE3']  image-sel=[E0A4,HOVER] cmd=command.copy('"@WHITE3"'))
    item(title="WHITE4"  image=[E0A4,'@WHITE4']  image-sel=[E0A4,HOVER] cmd=command.copy('"@WHITE4"'))
    item(title="WHITE5"  image=[E0A4,'@WHITE5']  image-sel=[E0A4,HOVER] cmd=command.copy('"@WHITE5"'))

    item(column vis='Static' image=[E00A,DARK])

    item(title="COLOR1A"  image=[E0A4,'@COLOR1A']  image-sel=[E0A4,HOVER] cmd=command.copy('"@COLOR1A"'))
    item(title="COLOR1B"  image=[E0A4,'@COLOR1B']  image-sel=[E0A4,HOVER] cmd=command.copy('"@COLOR1B"'))
    item(title="COLOR1C"  image=[E0A4,'@COLOR1C']  image-sel=[E0A4,HOVER] cmd=command.copy('"@COLOR1C"'))
    separator()

    item(title="BLACK"         image=['@E0A4','@BLACK']         image-sel=['@E0A4',HOVER])

    //
    item(title="BLUE_NEW"    image=['@E0A4',#3893FF]        image-sel=['@E0A4',HOVER] cmd=command.copy('"BLUE2"'))
    item(title="BLUE2"       image=['@E0A4','@BLUE2']       image-sel=['@E0A4',HOVER] cmd=command.copy('"BLUE2"'))
    item(title="BLUE"        image=['@E0A4','@BLUE']        image-sel=['@E0A4',HOVER] cmd=command.copy('"BLUE"'))
    item(title="BLUE_DARK"   image=['@E0A4','@BLUE_DARK']   image-sel=['@E0A4',HOVER] cmd=command.copy('"BLUE_DARK"'))

    //
    item(title="RED"         image=['@E0A4','@RED']         image-sel=['@E0A4',HOVER] cmd=command.copy('"RED"'))
    item(title="PINK_BRIGHT" image=['@E0A4','@PINK_BRIGHT'] image-sel=['@E0A4',HOVER] cmd=command.copy('"PINK_BRIGHT"'))
    item(title="PINK"        image=['@E0A4','@PINK']        image-sel=['@E0A4',HOVER] cmd=command.copy('"PINK"'))
    item(title="PURPLE"      image=['@E0A4','@PURPLE']      image-sel=['@E0A4',HOVER] cmd=command.copy('"PURPLE"'))
    item(title="PURPLE2"     image=['@E0A4','@PURPLE2']     image-sel=['@E0A4',HOVER] cmd=command.copy('"PURPLE2"'))
    item(title="PINK2"       image=['@E0A4','@PINK2']       image-sel=['@E0A4',HOVER] cmd=command.copy('"PINK2"'))

    //
    item(title="RED2"        image=['@E0A4','@RED2']        image-sel=['@E0A4',HOVER] cmd=command.copy('"RED2"'))

    //
    item(column vis='Static' image=[E00A,DARK])
    item(title="WHITE"         image=['@E0A4','@WHITE']         image-sel=['@E0A4',HOVER] cmd=command.copy('"WHITE"'))
    item(title="LOWKEY"        image=['@E0A4','@LOWKEY']        image-sel=['@E0A4',HOVER] cmd=command.copy('"LOWKEY"'))
    item(title="SOFT"          image=['@E0A4','@SOFT']          image-sel=['@E0A4',HOVER] cmd=command.copy('"SOFT"'))
    item(title="GREY"          image=['@E0A4','@GREY']          image-sel=['@E0A4',HOVER] cmd=command.copy('"GREY"'))
    item(title="DARK"          image=['@E0A4','@DARK']          image-sel=['@E0A4',HOVER] cmd=command.copy('"DARK"'))

    //
    item(column vis='Static' image=[E00A,DARK])
    item(title="TEST"          image=['@E0A4',#00ffa5]          image-sel=['@E0A4',HOVER] cmd=command.copy('"00ffa5"'))
    item(title="GREEN"         image=['@E0A4','@GREEN']         image-sel=['@E0A4',HOVER] cmd=command.copy('"GREEN"'))
    item(title="GREEN2"        image=['@E0A4','@GREEN2']        image-sel=['@E0A4',HOVER] cmd=command.copy('"GREEN2"'))
    //
    item(title="YELLOW_BRIGHT" image=['@E0A4','@YELLOW_BRIGHT'] image-sel=['@E0A4',HOVER] cmd=command.copy('"YELLOW_BRIGHT"'))
    item(title="HOVER"         image=['@E0A4','@HOVER']         image-sel=['@E0A4',HOVER] cmd=command.copy('"HOVER"'))
    item(title="YELLOW"        image=['@E0A4','@YELLOW']        image-sel=['@E0A4',HOVER] cmd=command.copy('"YELLOW"'))
    item(title="ORANGE"        image=['@E0A4','@ORANGE']        image-sel=['@E0A4',HOVER] cmd=command.copy('"ORANGE"'))
    item(title="ORANGE2"       image=['@E0A4','@ORANGE2']       image-sel=['@E0A4',HOVER] cmd=command.copy('"ORANGE2"'))

    // //
    //
    item(column vis='Static' image=[E00A,DARK])
    item(title="3893FF" image=['@E0A4',#3893FF] image-sel=['@E0A4',HOVER] cmd=command.copy('@3893FF'))

    // item(title="&this is a test" image=['@E0A4',#FF1C1A]          image-sel=['@E0A4',HOVER])
    // item(title="&this is a test" image=['@E0A4',#34B6FF]          image-sel=['@E0A4',HOVER])
    // item(title="&this is a test" image=['@E0A4',#46466F]          image-sel=['@E0A4',HOVER])
    // item(title="&this is a test" image=['@E0A4',#46466F]          image-sel=['@E0A4',HOVER])
    // item(title="&this is a test" image=['@E0A4',#717482]          image-sel=['@E0A4',HOVER])
    // item(title="&this is a test" image=['@E0A4',#FF00FF]          image-sel=['@E0A4',HOVER])
    // item(title="&this is a test" image=['@E0A4',#8D8199]          image-sel=['@E0A4',HOVER])
    // item(title="&this is a test" image=['@E0A4',#8D8199]          image-sel=['@E0A4',HOVER])
    // item(title="&this is a test" image=['@E0A4',#000000]          image-sel=['@E0A4',HOVER])
    // item(title="&this is a test" image=['@E0A4',#39C65A]          image-sel=['@E0A4',HOVER])
    // item(title="&this is a test" image=['@E0A4',#01FEAA]          image-sel=['@E0A4',HOVER])
    // item(title="&this is a test" image=['@E0A4',#FFFA00]          image-sel=['@E0A4',HOVER])
    // item(title="&this is a test" image=['@E0A4',#FFFA00]          image-sel=['@E0A4',HOVER])
    // item(title="&this is a test" image=['@E0A4',#FFFFFF]          image-sel=['@E0A4',HOVER])
    // item(title="&this is a test" image=['@E0A4',#E0D9EA]          image-sel=['@E0A4',HOVER])
    // item(title="&this is a test" image=['@E0A4',#E0D9EA]          image-sel=['@E0A4',HOVER])
    // item(title="&this is a test" image=['@E0A4',#FF904D]          image-sel=['@E0A4',HOVER])
    // item(title="&this is a test" image=['@E0A4',#A457FF]          image-sel=['@E0A4',HOVER])
    // item(title="&this is a test" image=['@E0A4',#f7a1a1b3]        image-sel=['@E0A4',HOVER])
    // item(title="&this is a test" image=['@E0A4',#FFD420]          image-sel=['@E0A4',HOVER])
    // item(title="&this is a test" image=['@E0A4',#34b6ff80]        image-sel=['@E0A4',HOVER])
    // item(title="&this is a test" image=['@E0A4',#ff00ff60]        image-sel=['@E0A4',HOVER])
    // item(title="&this is a test" image=['@E0A4',#34ff6580]        image-sel=['@E0A4',HOVER])
    // item(title="&this is a test" image=['@E0A4',#ff904d80]        image-sel=['@E0A4',HOVER])
    // item(title="&this is a test" image=['@E0A4',#a457ff80]        image-sel=['@E0A4',HOVER])
    // item(title="&this is a test" image=['@E0A4',#285cf1]          image-sel=['@E0A4',HOVER])
    // item(title="&this is a test" image=['@E0A4',#ff4dae]          image-sel=['@E0A4',HOVER])
    // item(title="&this is a test" image=['@E0A4',#D8FF00]          image-sel=['@E0A4',HOVER])
}

$YELLOW1 = #FFE565
$YELLOW2 = #FFD400
$YELLOW3 = #6E5D0C

$BLUE1 = #99C7FF
$BLUE2 = #3290FF
$BLUE3 = #145FB7

$GREEN1 = #75EF89
$GREEN2 = #32CC4C
$GREEN3 = #2D6B38

$GREY1 = #C1A3A3
$GREY2 = #7F7F7F
$GREY3 = #4C4C4C

$ORANGE1 = #FFCC99
$ORANGE2 = #FF9932
$ORANGE3 = #8E4F0F

$PINK1 = #FF65FE
$PINK2 = #FF00FE
$PINK3 = #890F89

$PURPLE1 = #C184FF
$PURPLE2 = #7F00FF
$PURPLE3 = #4C0F89

$RED1 = #FF7575
$RED2 = #FF0000
$RED3 = #890F0F

$WHITE1 = #FFFFFF
$WHITE2 = #FFFFFF
$WHITE3 = #5E5E5E
//
menu(title="&COLORS" type='Taskbar|Desktop|Drive' image=[E122,BLUE] image-sel=[E123,HOVER] sep='None') {
    //
    item(column vis='Static' image=[E00A,DARK])
    item(title="YELLOW1" image=[E0A4,'@YELLOW1'] image-sel=[E0A4,HOVER] cmd=command.copy('"@YELLOW1"'))
    item(title="YELLOW2" image=[E0A4,'@YELLOW2'] image-sel=[E0A4,HOVER] cmd=command.copy('"@YELLOW2"'))
    item(title="YELLOW3" image=[E0A4,'@YELLOW3'] image-sel=[E0A4,HOVER] cmd=command.copy('"@YELLOW3"'))
    item(title="BLUE1"   image=[E0A4,'@BLUE1']   image-sel=[E0A4,HOVER] cmd=command.copy('"@BLUE1"'))
    item(title="BLUE2"   image=[E0A4,'@BLUE2']   image-sel=[E0A4,HOVER] cmd=command.copy('"@BLUE2"'))
    item(title="BLUE3"   image=[E0A4,'@BLUE3']   image-sel=[E0A4,HOVER] cmd=command.copy('"@BLUE3"'))
    item(title="GREEN1"  image=[E0A4,'@GREEN1']  image-sel=[E0A4,HOVER] cmd=command.copy('"@GREEN1"'))
    item(title="GREEN2"  image=[E0A4,'@GREEN2']  image-sel=[E0A4,HOVER] cmd=command.copy('"@GREEN2"'))
    item(title="GREEN3"  image=[E0A4,'@GREEN3']  image-sel=[E0A4,HOVER] cmd=command.copy('"@GREEN3"'))
    item(title="GREY1"   image=[E0A4,'@GREY1']   image-sel=[E0A4,HOVER] cmd=command.copy('"@GREY1"'))
    item(title="GREY2"   image=[E0A4,'@GREY2']   image-sel=[E0A4,HOVER] cmd=command.copy('"@GREY2"'))
    item(title="GREY3"   image=[E0A4,'@GREY3']   image-sel=[E0A4,HOVER] cmd=command.copy('"@GREY3"'))
    item(title="ORANGE1" image=[E0A4,'@ORANGE1'] image-sel=[E0A4,HOVER] cmd=command.copy('"@ORANGE1"'))
    item(title="ORANGE2" image=[E0A4,'@ORANGE2'] image-sel=[E0A4,HOVER] cmd=command.copy('"@ORANGE2"'))
    item(title="ORANGE3" image=[E0A4,'@ORANGE3'] image-sel=[E0A4,HOVER] cmd=command.copy('"@ORANGE3"'))
    item(title="PINK1"   image=[E0A4,'@PINK1']   image-sel=[E0A4,HOVER] cmd=command.copy('"@PINK1"'))
    item(title="PINK2"   image=[E0A4,'@PINK2']   image-sel=[E0A4,HOVER] cmd=command.copy('"@PINK2"'))
    item(title="PINK3"   image=[E0A4,'@PINK3']   image-sel=[E0A4,HOVER] cmd=command.copy('"@PINK3"'))
    item(title="PURPLE1" image=[E0A4,'@PURPLE1'] image-sel=[E0A4,HOVER] cmd=command.copy('"@PURPLE1"'))
    item(title="PURPLE2" image=[E0A4,'@PURPLE2'] image-sel=[E0A4,HOVER] cmd=command.copy('"@PURPLE2"'))
    item(title="PURPLE3" image=[E0A4,'@PURPLE3'] image-sel=[E0A4,HOVER] cmd=command.copy('"@PURPLE3"'))
    item(title="RED1"    image=[E0A4,'@RED1']    image-sel=[E0A4,HOVER] cmd=command.copy('"@RED1"'))
    item(title="RED2"    image=[E0A4,'@RED2']    image-sel=[E0A4,HOVER] cmd=command.copy('"@RED2"'))
    item(title="RED3"    image=[E0A4,'@RED3']    image-sel=[E0A4,HOVER] cmd=command.copy('"@RED3"'))
    item(title="WHITE1"  image=[E0A4,'@WHITE1']  image-sel=[E0A4,HOVER] cmd=command.copy('"@WHITE1"'))
    item(title="WHITE2"  image=[E0A4,'@WHITE2']  image-sel=[E0A4,HOVER] cmd=command.copy('"@WHITE2"'))
    item(title="WHITE3"  image=[E0A4,'@WHITE3']  image-sel=[E0A4,HOVER] cmd=command.copy('"@WHITE3"'))

    //
    item(column vis='Static' image=[E00A,DARK])
    item(title="TIP1_COLOR" image=['@E0A4','@TIP1_COLOR[0]'] image-sel=['@E0A4',HOVER] cmd=command.copy('@TIP1_COLOR[0]'))
    item(title="TIP2_COLOR" image=['@E0A4','@TIP2_COLOR[0]'] image-sel=['@E0A4',HOVER] cmd=command.copy('@TIP1_COLOR[0]'))
    item(title="TIP3_COLOR" image=['@E0A4','@TIP3_COLOR[0]'] image-sel=['@E0A4',HOVER] cmd=command.copy('@TIP1_COLOR[0]'))
    item(title="TIP4_COLOR" image=['@E0A4','@TIP4_COLOR[0]'] image-sel=['@E0A4',HOVER] cmd=command.copy('@TIP1_COLOR[0]'))
    item(title="TIP5_COLOR" image=['@E0A4','@TIP5_COLOR[0]'] image-sel=['@E0A4',HOVER] cmd=command.copy('@TIP1_COLOR[0]'))
    separator()

    item(title="BG_COLOR" image=['@E0A4','@BG_COLOR'] image-sel=['@E0A4',HOVER] cmd=command.copy('@BG_COLOR'))
    item(title="FG_COLOR" image=['@E0A4','@FG_COLOR'] image-sel=['@E0A4',HOVER] cmd=command.copy('@FG_COLOR'))
    separator()

    item(title="WHITE1" image=['@E0A4','@WHITE1'] image-sel=['@E0A4',HOVER] cmd=command.copy('@WHITE1'))
    item(title="WHITE2" image=['@E0A4','@WHITE2'] image-sel=['@E0A4',HOVER] cmd=command.copy('@WHITE2'))
    item(title="WHITE3" image=['@E0A4','@WHITE3'] image-sel=['@E0A4',HOVER] cmd=command.copy('@WHITE3'))
    item(title="WHITE4" image=['@E0A4','@WHITE4'] image-sel=['@E0A4',HOVER] cmd=command.copy('@WHITE4'))
    separator()

    $WHITE1 = #B4BFE4
    $WHITE2 = #8B93B1
    $WHITE3 = #7C839E
    $WHITE4 = #717276

    $HOVER1 = #013CE5
    $HOVER1 = #285CF1
    $HOVER2 = #FF05FF

    $T_BLUE1 = #99C7FF
    $T_BLUE2 = #75B4FF
    $T_BLUE3 = #3893FF
    $T_BLUE4 = #3290FF
    $T_BLUE5 = #285CF1
    $T_BLUE6 = #145FB7
    $T_BLUE7 = #1A377D

    item(title="T_BLUE1" image=['@E0A4','@T_BLUE1'] image-sel=['@E0A4',HOVER1] cmd=command.copy('@T_BLUE1'))
    item(title="T_BLUE2" image=['@E0A4','@T_BLUE2'] image-sel=['@E0A4',HOVER1] cmd=command.copy('@T_BLUE2'))
    item(title="T_BLUE4" image=['@E0A4','@T_BLUE4'] image-sel=['@E0A4',HOVER1] cmd=command.copy('@T_BLUE4'))
    item(title="T_BLUE5" image=['@E0A4','@T_BLUE5'] image-sel=['@E0A4',HOVER2] cmd=command.copy('@T_BLUE5'))
    item(title="T_BLUE6" image=['@E0A4','@T_BLUE6'] image-sel=['@E0A4',HOVER2] cmd=command.copy('@T_BLUE6'))
    item(title="T_BLUE7" image=['@E0A4','@T_BLUE7'] image-sel=['@E0A4',HOVER2] cmd=command.copy('@T_BLUE7'))

}