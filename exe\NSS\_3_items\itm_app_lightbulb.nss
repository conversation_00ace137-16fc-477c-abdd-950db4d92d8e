
//
$APP_USER_LIGHTBULB_DIR = '@user.desktop\my\flow\home\__GOTO__\Apps\app_lightbulb\exe'
$APP_USER_LIGHTBULB_EXE = '@APP_USER_LIGHTBULB_DIR\LightBulb.exe'
$APP_USER_LIGHTBULB_TIP = "..."+str.trimstart('@APP_USER_LIGHTBULB_EXE','@app.dir')

// -> LightBulb
item(
    title=":  &LightBulb"
    keys="exe"
    type='Taskbar|File|Desktop|Dir|Drive|Back.Dir|Back.Drive'
    //
    image=APP_USER_LIGHTBULB_EXE
    tip=[APP_USER_LIGHTBULB_TIP,TIP3,0.5]
    //
    admin=keys.rbutton()
    cmd=if(KEYS_EXE_OPEN_EXE,(APP_USER_LIGHTBULB_EXE))
    args='/AcceptEula'
    commands{
        cmd=if(KEYS_EXE_COPY_DIR,clipboard.set('@APP_USER_LIGHTBULB_DIR')),
        cmd=if(KEYS_EXE_COPY_EXE,clipboard.set('@APP_USER_LIGHTBULB_EXE')),
        cmd=if(KEYS_EXE_OPEN_DIR,('@APP_USER_LIGHTBULB_DIR')),
    }
)
