﻿<h4>Introduction</h4>
<br>
<h5>What is Shell?</h5>
<p>
	<b>Shell</b> is a extensions of Windows File Explorer that can be used to create high-performance context menu items.
	 And gives user a high level of control over context menu of Windows File Explorer.
</p>
<p>The Right Click Menu or the Context Menu is the menu, which appears when you right-click on the desktop, file, folder or taskbar in Windows. This menu gives you added functionality by offering you actions you can take with the item.</p>
<p><b>Shell</b> is all you need to customize or add new items with several functions to Windows File Explorer Context menu and Much More.(cascade menus, advanced menus, multi-level menus, command menus, separator).</p>
<div class="has-text-centered my-6">
	<img class="preview" src="/docs/images/dark/goto.png">
</div>
<h5>Why Use Shell</h5>
<ul>
	<li>Is portable, fun and easy to learn!</li>
	<li>Configuration in plain text.</li>
	<li>Quick loading.</li>
	<li>Minimal resource usage.</li>
	<li>No limitations.</li>
	<li>Embedded expressions syntax.</li>
	<li>Built-in functions and predefined variables.</li>
	<li>Multiple sources of images (embedded icons, image files, svg, glyphs, and colors).</li>
	<li>Dynamic search and filter.</li>
	<li>Support Taskbar context menu.</li>
	<li>Full management of the context menu.</li>
</ul>